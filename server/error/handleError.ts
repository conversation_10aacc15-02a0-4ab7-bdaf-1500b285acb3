/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import HttpError, { HttpErrors } from './HttpError';
import { GENERIC_ERROR } from './ErrorMessages';
import UserError from './UserError';
import { wwgLogger } from '../service/wwgLogger';
import mongoose from 'mongoose';
import RateLimitError from './RateLimitError';
import { Request, Response } from 'express';
import config from '../config';
import { User as SentryUser, Request as SentryRequest } from '@sentry/node';
import ContextError from './ContextError';

export interface ErrorResponse {
  success: boolean;
  message: string;
  userMessage?: string;
  errors?: HttpErrors;
}

type ErrorWithContext = (Error | HttpError) & {
  user?: SentryUser,
  request?: SentryRequest;
};
export const addContext = (err: ErrorWithContext, req?: Request) => {
  if (!req) {
    return err;
  }

  // Add additional context information for logging
  if (req.user) {
    err.user = { id: req.user.id };
  }

  err.request = {
    url: req.originalUrl ?? req.url,
    method: req.method,
  }

  return err
}

type ErrorType = HttpError | UserError | ContextError | Error | string;
export const sendErrorResponse = (err: ErrorType, res: Response, req?: Request) => {

  const data: ErrorResponse = {
    success: false,
    message: GENERIC_ERROR,
  };

  if (typeof err === 'string') {
    if (!config.isProduction) {
      data.message = err;
    }
    return res.status(500).json(data)
  }

  if (!config.isProduction) {
    wwgLogger.error(err.stack);
    if ('context' in err && err.context) {
      console.log(err.context)
    }
  }

  // Log errors for staff user, handle the admin request as well.
  if (!config.isProduction || req?.user?.isStaff) {
    data.message = err.message;
  }

  if (err instanceof mongoose.Error.VersionError) {
    return res.status(409).json({
      ...data,
      userMessage: "There was a conflict between the supplied data and the existing resource. Please check and try again.",
    });
  }

  if ('errors' in err) {
    data.errors = err.errors;
  }

  if (err instanceof UserError) {
    data.userMessage = err.message;
  }

  res.status('status' in err && err.status ? err.status : 500);

  if (err instanceof RateLimitError) {
    res.set('Retry-After', String(err.retry));
  }

  res.json(data);
};
