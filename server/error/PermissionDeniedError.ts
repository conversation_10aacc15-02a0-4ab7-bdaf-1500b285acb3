/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import HttpError, { HttpErrors } from './HttpError';
import { HttpErrorMessages } from './ErrorMessages';
import { ErrorDetails } from "./ContextError";

export default class PermissionDeniedError extends HttpError {
  constructor(
    msg: string = HttpErrorMessages.PermissionDenied,
    errors?: HttpErrors,
    context?: ErrorDetails
  ) {
    super(msg, 403, errors, context);
    Object.setPrototypeOf(this, PermissionDeniedError.prototype);
  }

  public static withContext(context: ErrorDetails) {
    return new PermissionDeniedError(
      HttpErrorMessages.PermissionDenied,
      undefined,
      context,
    )
  }
}
