/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import UserError from './UserError';
import { ErrorDetails } from "./ContextError";

export type HttpErrors = Record<string, unknown>;
export default class HttpError extends UserError {
  constructor(
    m: string,
    public status: number,
    public errors?: HttpErrors,
    context?: ErrorDetails
  ) {
    super(m, context);
    Object.setPrototypeOf(this, HttpError.prototype);
  }
}
