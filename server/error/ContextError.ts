/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

export interface ErrorDetails extends ErrorOptions {
  /** developer message, when actual message is targeting users **/
  debugMessage?: string

  /**
   * Must be current user
   * it will be merged with request.user properties based on configured sentry options
   **/
  user? : { id?: string, [k: string]: unknown }

  /** tags will be applied as sentry tags **/
  tags?: string[]

  /**
   * HTTP status code: 400, 401, 500 etc.
   * Allow to set the response error if applicable
   * Note: status < 500 will be ignored by Sentry error handler
   * **/
  status?: number

  /**
   * Everything else that provide context
   * Note that any ObjectId property will only be converted to string on
   * root level of the error details, inner ones will remain as ObjectId's
   **/
  [k: string]: unknown
}

export default class ContextError extends Error {

  context?: Omit<ErrorDetails, 'cause'>;

  constructor(m: string, context?: ErrorDetails) {
    super(m, { cause: context?.cause });
    delete context?.cause;
    this.context = context;
    Object.setPrototypeOf(this, ContextError.prototype);
  }
}
