/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import express from 'express';
import { ObjectId } from 'bson';
import {
  INSIGHT_TEMPLATE_DASHBOARD_TYPES,
  InsightDashboard,
  InsightDashboardModel,
  InsightDashboardPlain,
  InsightDashboardType,
} from '../models/insightDashboard';
import { AuthRouter } from '../http/AuthRouter';
import { getDashboardItemManager } from '../service/insight-dashboard/DashboardItemManager';
import {
  canAccessDashboard,
  canManageDashboard,
  canManageSummaryDashboard,
} from '../middleware/insightDashboardMiddlewares';
import { canManageInitiative } from '../middleware/initiativeMiddlewares';
import { populateInitiative } from '../middleware/commonMiddlewares';
import { getDashboardSharingService } from '../service/insight-dashboard/DashboardSharingService';
import { getDashboardService } from '../service/insight-dashboard/DashboardService';
import { getOverviewDashboardService } from '../service/insight-dashboard/summary/OverviewDashboardService';
import { getSummaryDashboardService } from '../service/insight-dashboard/summary/SummaryDashboardService';
import UserError from '../error/UserError';
import {
  createInsightDashboardDtoSchema,
  createInsightDashboardItemDtoSchema,
  updateInsightDashboardDtoSchema,
} from './validation-schemas/insight-dashboards';
import { mustValidate } from '../util/validation';
import {
  getDashboardTypeFromParams,
  isStaticDashboardType,
  toPreloadOptions,
} from '../service/insight-dashboard/utils';
import BadRequestError from '../error/BadRequestError';
import FileUpload from '../http/FileUpload';
import { getIntegrationManager } from '../service/integration/IntegrationManager';
import { getDashboardTemplatesService } from '../service/insight-dashboard/DashboardTemplatesService';
import { DashboardTemplateType } from '../service/insight-dashboard/template';

const router = express.Router({ mergeParams: true }) as AuthRouter;
const dashboardItemManager = getDashboardItemManager();
const dashboardSharingService = getDashboardSharingService();
const dashboardService = getDashboardService();
const overviewDashboardService = getOverviewDashboardService();
const summaryDashboardService = getSummaryDashboardService();
const dashboardTemplatesService = getDashboardTemplatesService();

router
  .route('/')
  .get(populateInitiative, async (req, res, next) => {
    try {
      const initiativeId = new ObjectId(req.params.initiativeId);

      const sharedParentDashboards = await dashboardSharingService.getSharedParentDashboardsById(initiativeId);

      const dashboards = await InsightDashboard.find({
        initiativeId,
        type: { $in: [InsightDashboardType.Custom, ...INSIGHT_TEMPLATE_DASHBOARD_TYPES, null] }, // Maybe we want to reuse "custom" type and add a field 'template' instead?
      }).exec();

      return res.FromModel([...dashboards, ...sharedParentDashboards]);
    } catch (e) {
      next(e);
    }
  })
  .post(canManageInitiative, async (req, res, next) => {
    try {
      const body = mustValidate(req.body, createInsightDashboardDtoSchema);
      const creatorId = req.user._id;
      const initiativeId = new ObjectId(res.locals.initiativeId);
      const { title, filters, items } = body;

      const result = await InsightDashboard.create({ creatorId, initiativeId, title, filters, items });

      return res.FromModel(result);
    } catch (e) {
      next(e);
    }
  });

router.route('/templates').get(canManageInitiative, async (req, res, next) => {
  try {
    const initiativeId = new ObjectId(req.params.initiativeId);
    const result = await dashboardTemplatesService.getTemplates({ initiativeId });
    return res.FromModel(result);
  } catch (e) {
    next(e);
  }
});

router.route('/templates/:templateType/create').post(canManageInitiative, async (req, res, next) => {
  try {
    const initiativeId = new ObjectId(req.params.initiativeId);
    const creatorId = req.user._id;
    const templateType = req.params.templateType as DashboardTemplateType;
    const result = await dashboardTemplatesService.createDashboard({ initiativeId, creatorId, templateType });
    return res.FromModel(result);
  } catch (e) {
    next(e);
  }
});

router
  .route('/integrations')
  .get(populateInitiative, async (req, res, next) => {
    try {
      const id = new ObjectId(req.params.initiativeId);
      const integrationServices = await getIntegrationManager().getActiveServices(id);

      const integrationProviders = integrationServices.map(async integrationService => {
        const provider = integrationService.getInfo();
        return ({
          code: provider.code,
          name: provider.name,
          logo: provider.logo,
          icon: provider.icon,
          questions: await integrationService.getAvailableQuestions(),
        });
      });

      const result = await Promise.all(integrationProviders);

      return res.FromModel(result);
    } catch (e) {
      next(e);
    }
  })

router
  .route('/summary/:dashboard')
  .get(populateInitiative, async (req, res, next) => {
    try {
      if (!req.initiative) {
        throw new UserError(`Initiative was not found`);
      }

      const type = getDashboardTypeFromParams({
        dashboard: req.params.dashboard,
        mainDownloadCode: req.query.mainDownloadCode as string,
      });

      if (!isStaticDashboardType(type)) {
        return next(new BadRequestError(`Dashboard ${type} is not available`));
      }

      const dashboard = await summaryDashboardService.getSummaryDashboard({
        user: req.user,
        initiative: req.initiative,
        type,
        filters: toPreloadOptions(req.query),
      });
      return res.FromModel(dashboard);
    } catch (e) {
      next(e);
    }
  })
  .put(canManageInitiative, canManageSummaryDashboard, async (req, res, next) => {
    summaryDashboardService
      .updateSummaryDashboard(res.locals.dashboard, req.body)
      .then((dashboard) => res.FromModel(dashboard))
      .catch((e) => {
        next(e);
      });
  });

router.route('/summary/overview/item').post(canManageInitiative, async (req, res, next) => {
  try {
    const result = await overviewDashboardService.addItem({
      initiativeId: new ObjectId(req.params.initiativeId),
      creatorId: req.user._id,
      item: req.body,
    });
    return res.FromModel(result);
  } catch (e) {
    next(e);
  }
});

router.route('/summary/overview/item/:id').delete(canManageInitiative, async (req, res, next) => {
  try {
    const result = await overviewDashboardService.removeItem({
      initiativeId: new ObjectId(req.params.initiativeId),
      itemId: new ObjectId(req.params.id),
    });
    return res.FromModel(result);
  } catch (e) {
    next(e);
  }
});

router
  .route('/:dashboardId')
  .get(populateInitiative, canAccessDashboard, async (req, res, next) => {
    try {
      const rawDashboard = res.locals.dashboard.toObject() as InsightDashboardPlain;
      const params = {
        // We attach more data to the dashboard so using plain object is better.
        dashboard: rawDashboard,
        initiative: req.initiative,
        filters: toPreloadOptions(req.query),
      };
      const dashboard = INSIGHT_TEMPLATE_DASHBOARD_TYPES.includes(rawDashboard.type)
        ? await dashboardTemplatesService.populateDashboardData(params)
        : await dashboardItemManager.populateData(params);
      return res.FromModel(dashboard);
    } catch (e) {
      next(e);
    }
  })
  .put(canManageDashboard, async (req, res, next) => {
    try {
      const updates = mustValidate(req.body, updateInsightDashboardDtoSchema);
      const result = await dashboardService.update(res.locals.dashboard as InsightDashboardModel, updates);

      return res.FromModel(result);
    } catch (e) {
      next(e);
    }
  })
  .delete(canManageDashboard, async (_req, res, next) => {
    try {
      const result = await dashboardService.delete(res.locals.dashboard._id);

      return res.FromModel(result);
    } catch (e) {
      next(e);
    }
  });

router.route('/:dashboardId/item').post(canManageDashboard, async (req, res, next) => {
  try {
    const dashboard: InsightDashboardModel = res.locals.dashboard;
    const item = mustValidate(req.body, createInsightDashboardItemDtoSchema);
    const result = await dashboardItemManager.addDashboardItem({ dashboard, item });

    return res.FromModel({
      item: result,
    });
  } catch (e) {
    next(e);
  }
});

router.route('/:dashboardId/share').put(canManageDashboard, async (req, res, next) => {
  try {
    const result = await dashboardSharingService.shareDashboard({
      dashboard: res.locals.dashboard as InsightDashboardModel,
      enabled: Boolean(req.body.enabled),
    });
    return res.FromModel(result);
  } catch (e) {
    next(e);
  }
});

router.route('/:dashboardId/duplicate').post(canManageDashboard, async (req, res, next) => {
  try {
    const result = await dashboardService.duplicate(res.locals.dashboard, req.user._id);
    return res.FromModel(result);
  } catch (e) {
    next(e);
  }
});

router.route('/:dashboardId/upload').post(canManageDashboard, FileUpload.any(), async (req, res, next) => {
  try {
    const result = await dashboardService.uploadFiles(res.locals.dashboard._id, req.user._id, req.files);

    return res.FromModel(result);
  } catch (e) {
    next(e);
  }
});

module.exports = router;
