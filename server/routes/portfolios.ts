/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import InitiativeGroup from '../models/initiativeGroup';
import { clearCache } from '../service/cache';
import ScorecardFactory from '../service/scorecard/ScorecardFactory';
import Initiative, { InitiativePlain, InitiativeTypes, PERMISSION_GROUPS } from '../models/initiative';
import { canManagePortfolio, fromSubmissionInsightsRequest, populatePortfolio } from '../middleware/portfolioMiddlewares';
import { MetricGroupRepository } from "../repository/MetricGroupRepository";
import { getPortfolioMetricGroup } from "../service/portfolio/PortfolioMetricGroup";
import { stringifyArrayCsvFile } from "../service/file/writer/CsvFileWriter";
import { AuthenticatedPortfolioRouter } from '../http/AuthRouter';
import { UserInitiativeRepository } from '../repository/UserInitiativeRepository';
import { populatePortfolioDataShareInitiative } from '../middleware/commonMiddlewares';
import { DownloadScope } from "../service/survey/scope/downloadScope";
import { DateRangeType, SurveyRepository } from '../repository/SurveyRepository';
import { DataPeriods } from '../service/utr/constants';
import { PortfolioRepository } from "../repository/PortfolioRepository";
import { InitiativeBenchmarking, PackUsageQuery, PortfolioService } from '../service/portfolio/PortfolioService';
import { PortfolioPackUsageService } from '../service/portfolio/PortfolioPackUsageService';
import { getPortfolioCompaniesService } from '../service/portfolio/PortfolioCompaniesService';
import { SurveyScope } from "../service/survey/SurveyScope";
import { DataScopeAccess } from "../models/dataShare";
import { getPortfolioSubmissionInsightsService } from '../service/portfolio/PortfolioSubmissionInsightsService';
import { getPortfolioCompaniesDashboardService } from "../service/portfolio/PortfolioCompaniesDashboardService";
import { toPreloadOptions, type StaticDashboardType } from "../service/insight-dashboard/utils";
import { ObjectId } from "bson";
import { z } from "zod";
import { InsightDashboardType } from "../models/insightDashboard";
import { mustValidate } from '../util/validation';


const router = express.Router() as AuthenticatedPortfolioRouter;

const portfolioMetricGroup = getPortfolioMetricGroup()
const portfolioCompaniesService = getPortfolioCompaniesService();
const dashboardService = getPortfolioCompaniesDashboardService();

router.route('/:portfolioId')
  .get(populatePortfolio, async (req, res) => {
    try {
      const portfolio = res.locals.portfolio;
      const permissions = await UserInitiativeRepository.getInitiativeUserPermissions(req.user, String(portfolio._id));
      const scorecardFactory = new ScorecardFactory();
      const scorecardGroup = await scorecardFactory.getByInitiativeGroupId(portfolio.initiativeGroupId);
      res.FromModel({
        ...portfolio,
        userPermissions: permissions,
        scorecard: scorecardGroup
      });
    } catch (e) {
      res.Exception(e);
    }
  })
  .delete(populatePortfolio, canManagePortfolio, async (req, res) => {
    try {
      const portfolio = res.locals.portfolio;
      await Initiative.findByIdAndDelete(portfolio._id);

      const otherLinkedInitiatives = await Initiative.find({ initiativeGroupId: portfolio.initiativeGroupId }).lean().exec();
      if (!otherLinkedInitiatives || otherLinkedInitiatives.length === 0) {
        await InitiativeGroup.findByIdAndDelete(portfolio.initiativeGroupId);
        // No other initiative use the initiativeGroup, so safe to delete
      }

      clearCache();
      res.Success();
    } catch (e) {
      res.Exception(e);
    }
  })

router.route('/:portfolioId/holdings')
  .post(populatePortfolio, canManagePortfolio, async (req, res) => {
    try {
      const initiativeGroup = await PortfolioService.addHoldingToGroup({
        portfolio: res.locals.portfolio,
        holdingId: req.body.holdingId,
        weight: req.body.weight
      });

      clearCache();
      res.FromModel(initiativeGroup);
    } catch (e) {
      res.Exception(e);
    }
  });

router.route('/:portfolioId/holdings/:holdingId')
  .patch(populatePortfolio, canManagePortfolio, async (req, res) => {
    try {
      const initiativeGroup = await InitiativeGroup.findById(res.locals.portfolio.initiativeGroupId).orFail().exec();
      const holding = initiativeGroup.group.find(i => String(i.initiativeId) === req.params.holdingId);
      if (!holding) {
        return res.Exception('Holding does not exist in portfolio');
      }

      if (req.body.weight !== undefined) {
        holding.weight = Number(req.body.weight);
        await initiativeGroup.save();
      }

      clearCache();
      res.Success();
    } catch (e) {
      res.Exception(e);
    }
  })
  .delete(populatePortfolio, canManagePortfolio, async (req, res) => {
    try {
      const initiativeIdToRemove = req.params.holdingId;
      const portfolio = res.locals.portfolio;

      const portfolioGroup = await PortfolioService.removeHoldingFromGroup(portfolio, initiativeIdToRemove);
      // Currently the adding of any PT into another PT's holding is unlimited, which is wrong
      // Until that is fixed, we need to ensure it doesn't accidentally delete someone else's TP

      // const initiative = await Initiative.findById(initiativeIdToRemove).exec();
      // if (initiative?.initiativeGroupId) {
      //   await InitiativeGroup.findByIdAndDelete(initiative.initiativeGroupId).exec();
      //   if (String(initiative.parentId) === String(portfolio._id)) {
      //     // If this initiative is owned by this portfolio (not shared), it will get removed.
      //     // This should almost exclusively be true, unless staff is messing around
      //     await initiative.remove();

      //     const initiativeGroupsToCleanUp = await InitiativeGroup.find({ 'group.initiativeId': initiativeIdToRemove }).exec();
      //     await Promise.all(initiativeGroupsToCleanUp.map(i => {
      //       i.group = i.group.filter(g => String(g.initiativeId) !== initiativeIdToRemove);
      //       return i.save();
      //     }));
      //   }
      // }

      clearCache();
      res.FromModel(portfolioGroup);
    } catch (e) {
      res.Exception(e);
    }
  })

router.route('/:portfolioId/companies{/:option}')
  .get(populatePortfolio, async (req, res, next) => {
    try {
      const maxRecursion = req.params.option === 'recursive' ? 3 : 1;
      const dateRange = req.query.dateRange as DateRangeType;
      const portfolio = res.locals.portfolio;

      if (portfolio.permissionGroup === PERMISSION_GROUPS.PORTFOLIO_TRACKER_EXCHANGE) {
        const companyArray = await portfolioCompaniesService.getCompaniesPtExchange({
          dateRange,
          period: req.query.period as DataPeriods,
          metrics: req.query.metrics as string[],
          portfolio
        });

        return res.FromModel(companyArray);
      }

      const companyArray = await portfolioCompaniesService.getCompaniesRecursive({
        maxRecursion,
        dateRange,
        period: req.query.period as DataPeriods,
        metrics: req.query.metrics as string[],
        portfolio
      });

      return res.FromModel(companyArray);
    } catch (e) { next(e); }
  });
router
  .route('/:portfolioId/companies/benchmarking/latest-surveys')
  .get(populatePortfolio, async (req, res, next) => {
    try {
      const maxRecursion = 3;
      const { dateField, dateRange, surveyPacks } = mustValidate(
        req.query,
        z.object({
          dateField: z.enum(['effectiveDate', 'completedDate']).optional(),
          dateRange: z
            .object({
              startDate: z.coerce.date().optional(),
              endDate: z.coerce.date().optional(),
            })
            .optional(),
          surveyPacks: z.string().array().optional(),
        })
      );
      const portfolio = res.locals.portfolio;
      const initiativeId = String(portfolio._id);
      const companies: Map<string, InitiativeBenchmarking> = new Map();
      const naturalSort = new Intl.Collator(undefined, { numeric: true, sensitivity: 'base' }).compare;


      // We also want to filter utrvs using passed in scope
      const utrMatch = Array.isArray(surveyPacks) && surveyPacks.length > 0 ?
        await DownloadScope.generateMultiScopeMatch({
          scope: SurveyScope.fromCodes(surveyPacks),
          // This endpoint return only question counts, therefore currently it's allowed to have full access,
          // given that we are filtering by at least one pack, we can use Partial access that feels more correct.
          access: DataScopeAccess.Partial
        }) : undefined;

      // Use extracted utrIds for additional matching, to filter utrvs as well
      const utrIds = utrMatch ? await PortfolioRepository.getPackUsageUtrs(utrMatch) : undefined;

      // This is PORTFOLIO_TRACKER_EXCHANGE only endpoint, this should be optimized for that.
      // @TODO [PERFORMANCE] No recursive look ups are needed.
      const recursiveInitiativeGroup = async (id: string, depth: number = 1) => {
        const initiative = await PortfolioService.findInitiativeWithIndustry(id);
        if (!initiative) {
          return;
        }
        if (
          initiative.type === InitiativeTypes.Initiative &&
          portfolio.permissionGroup === PERMISSION_GROUPS.PORTFOLIO_TRACKER_EXCHANGE
        ) {
          const iid = String(initiative._id);
          if (companies.has(iid)) {
            return;
          }

          const benchmarkingSurveys = await SurveyRepository.getBenchmarkingLatestSurveys({
            initiativeId: initiative._id,
            dateField,
            startDate: dateRange?.startDate,
            endDate: dateRange?.endDate,
            surveyPacks,
            utrIds,
          });
          const latestSurvey = benchmarkingSurveys.find(item => item.effectiveDate);
          if (!benchmarkingSurveys.length) {
            return;
          }
          return companies.set(iid, {...initiative, benchmarkingSurveys, latestSurvey});
        }
        if (depth > maxRecursion) {
          return;
        }
        const group = await InitiativeGroup.findById(initiative.initiativeGroupId).lean();
        if (group) {
          for (const g of group.group) {
            await recursiveInitiativeGroup(String(g.initiativeId), depth + 1);
          }
        }
      };

      await recursiveInitiativeGroup(initiativeId);
      const companyArray = Array.from(companies.values()).sort((a, b) => naturalSort(a.name, b.name));

      return res.FromModel(companyArray);
    } catch (e) {
      next(e);
    }
  });

router.route('/:portfolioId/companies/pack-usage')
  .get(populatePortfolio, async (req, res, next) => {
    const maxRecursion = 3;
    try {
      const data = await PortfolioPackUsageService.processRequest(
        res.locals.portfolio,
        // Not really doing a validation on the query here....
        req.query as unknown as PackUsageQuery,
        maxRecursion,
      );
      res.FromModel(data);
    } catch (e) {
      next(e);
    }
  });

router.route('/:portfolioId/benchmarking/submission-insights')
  .get(populatePortfolio, fromSubmissionInsightsRequest, async (req, res, next) => {
    try {
      const maxRecursion = 3;
      const portfolio = res.locals.portfolio;
      const submissionInsightService = getPortfolioSubmissionInsightsService();
      const data = await submissionInsightService.getSubmissionInsightsData({
        portfolio,
        filters: res.locals.filters,
        maxRecursion,
      });
      return res.FromModel(data);
    } catch (error) {
      next(error);
    }
  });

router.route('/:portfolioId/metrics/:metricId/download')
  .get(populatePortfolio, async (req, res) => {
    try {
      const metricGroup = await MetricGroupRepository.mustFindById(req.params.metricId);
      const result = await portfolioMetricGroup.downloadAggregateGroupCsv(metricGroup, req.user);
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${result.filename}"`);
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Pragma', 'no-cache');
      return res.send(stringifyArrayCsvFile(result));
    } catch (e) {
      res.Exception(e);
    }
  });

router.route('/:portfolioId/default-dashboards')
  .get(populatePortfolio, async (req, res) => {
    const defaultDashboards = await dashboardService.getDefaultDashboards(req.params.portfolioId);
    return res.FromModel(defaultDashboards);
  })

router.route('/:portfolioId/initiatives/:initiativeId/dashboards/:dashboardId')
  .get(populatePortfolioDataShareInitiative, populatePortfolio, async (req, res) => {
    const { initiativeId, dashboardId } = req.params;
    const dashboard = await dashboardService.getPortfolioCompanyInsightsDashboard({
      portfolio: res.locals.portfolio,
      dataShareInitiativeId: initiativeId,
      dashboardId,
    });
    return res.FromModel(dashboard);
  })

router.route('/:portfolioId/initiatives/:initiativeId/dashboard/:dashboardType')
  .get(populatePortfolioDataShareInitiative, async (req, res, next) => {
    try {
      const { portfolioId, dashboardType } = req.params;

      const historicalReports = await dashboardService.getSummaryDashboard({
        dashboardType: z.nativeEnum(InsightDashboardType).parse(dashboardType) as StaticDashboardType,
        user: req.user,
        initiative: req.initiative as InitiativePlain,
        portfolioId: new ObjectId(portfolioId),
        filters: toPreloadOptions(req.query)
      });
      res.FromModel(historicalReports);
    } catch (e) {
      next(e)
    }
  });

module.exports = router;
