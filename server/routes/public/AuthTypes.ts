/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { ServiceAccountConnection, UserPlain, UserType } from "../../models/user";
import { PublicApiAccess, UserApiKeyPlain } from "../../models/userApiKey";
import { KeysEnum } from "../../models/commonProperties";
import { ObjectId } from "bson";

export type PublicApiUserProps = Pick<UserPlain,
  | '_id'
  | 'firstName'
  | 'surname'
  | 'active'
>;

export const publicApiUserProps: KeysEnum<PublicApiUserProps, 1> = {
  _id: 1,
  active: 1,
  firstName: 1,
  surname: 1,
};

export interface ServiceAccount {
  user: PublicApiUserProps;
  type: UserType.ServiceAccount
  connection: ServiceAccountConnection;
}

export enum AuthType {
  ApiKey = 'api_key',
}

interface ApiKeyConnection extends PublicApiAccess {
  /** Represent api key used for the user **/
  apiKeyId: ObjectId;
  /**
   * Short token used in logs etc. A bit of duplication with apiKeyId...
   */
  shortToken: string;
}

interface PublicApiUser {
  user: PublicApiUserProps;
  type: AuthType.ApiKey
  connection: ApiKeyConnection;
}

export type PublicAuth = ServiceAccount | PublicApiUser


export const toPublicApiUser = (apiKey: UserApiKeyPlain, user: PublicApiUserProps): PublicApiUser => {
  return {
    user: {
      _id: user._id,
      firstName: user.firstName,
      surname: user.surname,
      active: user.active,
    },
    connection: {
      scopes: apiKey.scopes,
      roles: apiKey.roles,
      initiativeId: apiKey.initiativeId,
      apiKeyId: apiKey._id,
      shortToken: apiKey.shortToken,
    },
    type: AuthType.ApiKey,
  };
}
