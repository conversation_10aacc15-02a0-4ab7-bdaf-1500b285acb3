import { getUserManager } from "../../service/user/UserManager";
import { getUserEventService } from "../../service/event/UserEventService";
import { NextFunction } from "express";
import config from "../../config";
import User, { ServiceAccountConnection, UserModel, UserPlain, UserType } from "../../models/user";
import { createToken, decodeToken } from "../../service/authentication/token";
import { UserErrorMessages } from "../../error/ErrorMessages";
import { USER } from "../../service/event/Events";
import HttpError from "../../error/HttpError";
import UserLockManager from "../../service/user/UserLockManager";
import { Response } from "express-serve-static-core";
import bcrypt from "bcrypt";
import { PublicApiUserProps, publicApiUserProps, ServiceAccount, toPublicApiUser } from "./AuthTypes";
import User<PERSON><PERSON><PERSON><PERSON>, { APIKeyConfig } from "../../models/userApiKey";
import { getTokenComponents } from "prefixed-api-key";
import { PreAuthRequest } from "./PublicAuthRouter";
import { wwgLogger } from "../../service/wwgLogger";
import ContextError from "../../error/ContextError";

const userManager = getUserManager();
const userEventService = getUserEventService();
const { accessTokenExpire } = config.jwt

const generateAuthResponse = (user: UserPlain) => {
  const userId = String(user._id);
  return {
    token: createToken({ userId }, accessTokenExpire),
    userId: userId,
    expiry: accessTokenExpire,
  };
};

const saltRounds = 10;
export const setPassword = async (user: Pick<UserModel, 'passwordHash' | 'save'>, plainPassword: string) => {
  if (!plainPassword || plainPassword.length < 4) {
    return Promise.reject('Password is invalid');
  }
  user.passwordHash = await bcrypt.hash(plainPassword, saltRounds);
  return user;
}

const login = async (user: UserModel, plainPassword: string, isStaff = false) => {

  if (UserLockManager.isLocked(user)) {
    throw new Error(UserErrorMessages.LoginFailedServiceAccount);
  }

  const isSuccess = await bcrypt.compare(plainPassword, user.getPasswordHash());

  if (!isSuccess) {
    await userManager.triggerFailedLoginAttempt(user, isStaff);
    throw new Error(UserErrorMessages.LoginFailedServiceAccount);
  }

  user.lastLogin = new Date();
  user.loginAttempts = 0; // Reset count
  await user.save();

  userEventService.addEvent(user, USER.events.loginSuccess, { isStaff, type: user.type });
  return generateAuthResponse(user);
}

export const authenticateServiceAccount = async (_id: string, plainPassword: string) => {
  const user = await User.findOne({ _id, type: UserType.ServiceAccount }).exec();
  if (!user || !user.getPasswordHash()) {
    throw new Error(UserErrorMessages.LoginFailedServiceAccount);
  }

  return login(user, plainPassword);
};

export const publicAuth = async (req: PreAuthRequest, _res: Response, next: NextFunction) => {

  const authHeader = req.headers.authorization ?? '';
  const match = authHeader.match(/Bearer (.+)/);
  const accessToken = match?.[1];
  if (!accessToken) {
    return next(new HttpError('Invalid authentication header', 401));
  }

  if (accessToken.startsWith(APIKeyConfig.keyPrefix)) {
    const { longTokenHash, shortToken } = getTokenComponents(accessToken)
    try {
      const apiKey = await UserApiKey
        .findOne({ longTokenHash, revokedDate: { $exists: false } })
        .lean()
        .orFail()
        .exec();

      const user = await User.findById<PublicApiUserProps>(apiKey.userId, publicApiUserProps)
        .lean()
        .exec();

      if (!user) {
        return next(new HttpError('Not Authenticated', 401, undefined, { shortToken }));
      }

      req.auth = toPublicApiUser(apiKey, user);

      return next();
    } catch (err) {
      wwgLogger.error(new ContextError('Failed to authenticate with API key', {
        shortToken,
        cause: err
      }));
      return next(new HttpError('Not Authenticated', 401, undefined, { shortToken }));
    }
  }


  try {
    const jwt = await decodeToken(accessToken);

    if (typeof jwt !== "object" || !jwt.userId) {
      return next(new HttpError('Not Authenticated', 401));
    }

    const user = await User.findOne({
      _id: jwt.userId,
      type: UserType.ServiceAccount,
      connection: { $exists: true }
    }).lean().exec();

    if (!user) {
      const { uid, sub } = jwt.claims
      return next(new HttpError('Not Authenticated', 401, {}, {
        debugMessage: 'User found',
        userId: jwt.userId,
        uid,
        sub,
      }));
    }

    req.auth = {
      user: {
        _id: user._id,
        firstName: user.firstName,
        surname: user.surname,
        active: user.active,
      },
      connection: user.connection as ServiceAccountConnection,
      type: UserType.ServiceAccount
    } satisfies ServiceAccount;
    return next();
  } catch (err) {
    next(new HttpError('Not Authenticated', 401, {}, { cause: err }));
  }
};
