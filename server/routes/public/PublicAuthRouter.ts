/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import {
  ErrorRequestHandler,
  IRouter,
  NextFunction,
  ParamsDictionary,
  PathParams,
  Request as BaseRequest,
  Response,
} from 'express-serve-static-core';
import { ParsedQs } from 'qs';
import { PublicAuth } from "./AuthTypes";

type Request<
  P = ParamsDictionary,
  ResBody = any,
  ReqBody = any,
  ReqQuery = ParsedQs,
  Locals extends Record<string, any> = Record<string, any>
> = Omit<BaseRequest<P, ResBody, ReqBody, ReqQuery, Locals>, 'user'> & {
  user: undefined;
  auth?: PublicAuth;
}

export interface ApiRequestHandler<
  Req extends Request = Request,
  Res extends Response = Response,
  N extends NextFunction = NextFunction
> {
  (req: Req, res: Res, next: NextFunction): void;
}

type ApiErrorRequestHandler<
  Req extends Request,
  Res extends Response = Response,
  Next extends NextFunction = NextFunction,
> = (err: any, req: Req, res: Res, next: Next) => void;

export type ApiRequestHandlerParams<
  Req extends Request,
  Res extends Response = Response,
  Next extends NextFunction = NextFunction,
  P = ParamsDictionary,
> =
  | ApiRequestHandler<Req, Res, Next>
  | ApiErrorRequestHandler<Req, Res, Next>
  | Array<ApiRequestHandler<Req, Res, Next> | ErrorRequestHandler<P>>;


interface ApiRouterMatcher<
  T,
  Method extends 'all' | 'get' | 'post' | 'put' | 'delete' | 'patch' | 'options' | 'head' = any,
  Req extends Request = Request,
  Res extends Response = Response,
  Next extends NextFunction = NextFunction,
  P = ParamsDictionary,
> {
  (path: PathParams, ...handlers: Array<ApiRequestHandler<Req, Res, Next>>): T;

  (path: PathParams, ...handlers: Array<ApiRequestHandlerParams<Req, Res, Next, P>>): T;
}

interface ApiRouterHandler<
  T,
  Req extends Request,
  Res extends Response = Response,
  Next extends NextFunction = NextFunction,
> {
  (...handlers: ApiRequestHandler<Req, Res, Next>[]): T;

  (...handlers: ApiRequestHandlerParams<Req, Res, Next>[]): T;

  (...handlers: Array<ApiRequestHandler<Req, Res, Next>>): T;

  (...handlers: Array<ApiRequestHandlerParams<Req, Res, Next>>): T;
}

interface ApiRoute<
  Req extends Request,
  Res extends Response = Response,
  Next extends NextFunction = NextFunction,
> {
  path: string;
  stack: any;
  all: ApiRouterHandler<this, Req, Res, Next>;
  get: ApiRouterHandler<this, Req, Res, Next>;
  post: ApiRouterHandler<this, Req, Res, Next>;
  put: ApiRouterHandler<this, Req, Res, Next>;
  delete: ApiRouterHandler<this, Req, Res, Next>;
  patch: ApiRouterHandler<this, Req, Res, Next>;
  options: ApiRouterHandler<this, Req, Res, Next>;
  head: ApiRouterHandler<this, Req, Res, Next>;

  checkout: ApiRouterHandler<this, Req, Res, Next>;
  copy: ApiRouterHandler<this, Req, Res, Next>;
  lock: ApiRouterHandler<this, Req, Res, Next>;
  merge: ApiRouterHandler<this, Req, Res, Next>;
  mkactivity: ApiRouterHandler<this, Req, Res, Next>;
  mkcol: ApiRouterHandler<this, Req, Res, Next>;
  move: ApiRouterHandler<this, Req, Res, Next>;
  'm-search': ApiRouterHandler<this, Req, Res, Next>;
  notify: ApiRouterHandler<this, Req, Res, Next>;
  purge: ApiRouterHandler<this, Req, Res, Next>;
  report: ApiRouterHandler<this, Req, Res, Next>;
  search: ApiRouterHandler<this, Req, Res, Next>;
  subscribe: ApiRouterHandler<this, Req, Res, Next>;
  trace: ApiRouterHandler<this, Req, Res, Next>;
  unlock: ApiRouterHandler<this, Req, Res, Next>;
  unsubscribe: ApiRouterHandler<this, Req, Res, Next>;
}

interface ApiRouter<Req extends Request, Res extends Response = Response> extends IRouter {
  all: ApiRouterMatcher<this, 'all', Req>;
  get: ApiRouterMatcher<this, 'get', Req>;
  post: ApiRouterMatcher<this, 'post', Req>;
  put: ApiRouterMatcher<this, 'put', Req>;
  delete: ApiRouterMatcher<this, 'delete', Req>;
  patch: ApiRouterMatcher<this, 'patch', Req>;
  options: ApiRouterMatcher<this, 'options', Req>;
  head: ApiRouterMatcher<this, 'head', Req>;

  use: ApiRouterHandler<this, Req> & ApiRouterMatcher<this, any, Req>;

  route(prefix: PathParams): ApiRoute<Req, Res>;
}

interface AuthenticatedRequest<
  P = ParamsDictionary,
  ResBody = any,
  ReqBody = any,
  ReqQuery = ParsedQs,
  Locals extends Record<string, any> = Record<string, any>
> extends Request<P, ResBody, ReqBody, ReqQuery, Locals> {
  auth: PublicAuth;
}

export type PreAuthRequest = AuthenticatedRequest & { auth?: PublicAuth };
export type AuthPublicRouter<P extends ParamsDictionary = ParamsDictionary> = ApiRouter<AuthenticatedRequest<P>>
