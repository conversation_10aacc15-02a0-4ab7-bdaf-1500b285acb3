import express from "express";
import { authenticateServiceAccount } from "../publicAuth";
import HttpError from "../../../error/HttpError";

const router = express.Router();

export interface AuthResponse {
  access_token: string;
  scope?: string;
  expires_in: number;
  token_type?: 'Bearer'
}

router.post('/token', (req, res, next) => {

  const clientId = req.body.client_id;
  const clientSecret = req.body.client_secret;
  if (!clientId || !clientSecret) {
    return res.Exception(new HttpError('client_id and client_secret must be provided', 401));
  }

  authenticateServiceAccount(clientId, clientSecret)
    .then((authResult) => res.json({
      access_token: authResult.token,
      expires_in: authResult.expiry,
      token_type: 'Bearer',
    } satisfies AuthResponse))
    .catch((err: Error) => {
      res.status(401).json({ success: false, message: err.message });
    });
});

export default router;
