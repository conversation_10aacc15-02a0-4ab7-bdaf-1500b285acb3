import express from "express";
import UniversalTrackerValue from "../../../models/universalTrackerValue";
import { PublicUtrv } from "../../../public-api/types";
import FileUpload from "../../../http/FileUpload";
import { ActionMap } from "../../../service/utr/constants";
import { getUniversalTrackerValueManager } from "../../../service/utr/UniversalTrackerValueManager";
import { ScopePermissions } from "../../../public-api/scopePermission";
import PermissionDeniedError from "../../../error/PermissionDeniedError";
import { UtrvPublicRepository } from "../../../public-api/UtrvPublicRepository";
import { AuthPublicRouter } from "../PublicAuthRouter";
import { UserRoles } from "../../../service/user/userPermissions";
import { ScopePermission } from "../../../public-api/scopePermissionModels";

const router = express.Router() as AuthPublicRouter;

const utrvManager = getUniversalTrackerValueManager();

router.route('/:utrvId')
  .get((req, res, next) => {
    UtrvPublicRepository.findById(req.params.utrvId, req.auth)
      .then(utrv => res.FromModel(utrv))
      .catch(e => next(e))
  });

router.route('/:utrvId/:action')
  .patch(FileUpload.any(), async (req, res, next) => {

    try {
      const obj = await UniversalTrackerValue.findById(req.params.utrvId, { initiativeId: 1 }).orFail().exec();
      if (!await ScopePermissions.can(req.auth, ScopePermission.UniversalTrackerValueWrite, obj)) {
        return next(new PermissionDeniedError())
      }

      const utrvId = req.params.utrvId;
      const utrv = await utrvManager.processAction({
        id: utrvId,
        action: String(req.params.action).toLowerCase() as ActionMap,
        user: {
          ...req.auth.user,
          // Hack to add permissions as inner method require that, need to have new method...
          // Given that UniversalTrackerValueWrite, we are claiming we have initiative contributor permissions
          permissions: [{ initiativeId: obj.initiativeId, permissions: [UserRoles.Contributor] }],
        },
        data: req.body,
        files: req.files,
        ip: req.ip,
        na: Boolean(req.body.valueData?.notApplicableType),
        autoVerify: Boolean(req.body.autoVerify),
      })

      const r: PublicUtrv = {
        _id: utrv._id,
        value: utrv.value,
        valueData: utrv.valueData,
        status: utrv.status,
        valueType: utrv.valueType,
        effectiveDate: utrv.effectiveDate,
        initiativeId: utrv.initiativeId,
        universalTrackerId: utrv.universalTrackerId,
        verificationRequired: utrv.verificationRequired,
        evidenceRequired: utrv.evidenceRequired,
      };
      return res.FromModel(r);
    } catch (e) {
      next(e)
    }
  });

export default router;
