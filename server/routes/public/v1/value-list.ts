import express from "express";
import ValueList from "../../../models/valueList";
import { ScopePermissions } from "../../../public-api/scopePermission";
import PermissionDeniedError from "../../../error/PermissionDeniedError";
import { AuthPublicRouter } from "../PublicAuthRouter";
import { ScopePermission } from "../../../public-api/scopePermissionModels";
import { z } from "zod";
import { getObjectIdsSchema } from "../../validation-schemas/common";

const router = express.Router() as AuthPublicRouter;

const searchValueListsSchema = z.object({
  ids: getObjectIdsSchema({ min: 1, max: 100, isOptional: false }),
});

router.route('/lookup')
  .post(async (req, res, next) => {
    if (!ScopePermissions.hasScope(req.auth, ScopePermission.ValueListRead)) {
      return next(new PermissionDeniedError())
    }

    const validation = searchValueListsSchema.safeParse(req.body);
    if (!validation.success) {
      res.status(400).json({
        success: false,
        message: 'Invalid request body',
        errors: validation.error.errors,
      });
      return;
    }

    const { ids } = validation.data;

    const valueLists = await ValueList.find({ _id: { $in: ids } }, { options: 1 }).lean().exec()
    res.FromModel(valueLists)
  });

router.route('/:id')
  .get((req, res, next) => {
    if (!ScopePermissions.hasScope(req.auth, ScopePermission.ValueListRead)) {
      return next(new PermissionDeniedError())
    }

    ValueList.findById(req.params.id, { options: 1 }).lean().orFail().exec()
      .then((vl) => res.FromModel(vl))
      .catch(e => next(e))
  });

export default router;
