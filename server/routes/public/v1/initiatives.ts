/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import express from "express";
import { AuthPublicRouter } from "../PublicAuthRouter";
import { ScopePermissions } from "../../../public-api/scopePermission";
import PermissionDeniedError from "../../../error/PermissionDeniedError";
import { InitiativeRepository } from "../../../repository/InitiativeRepository";
import { InitiativePublicMin, initiativePublicMinProjection } from "../../../models/public/initiativeType";
import { arrayToTree } from "../../../util/tree";
import { SurveyPublicRepository } from "../../../public-api/SurveyPublicRepository";
import { ObjectId } from "bson";
import { ScopePermission } from "../../../public-api/scopePermissionModels";
import { z } from "zod";
import BadRequestError from "../../../error/BadRequestError";

const router = express.Router() as AuthPublicRouter;

router.use((req, res, next) => {
  if (!ScopePermissions.hasScope(req.auth, ScopePermission.InitiativeRead)) {
    return next(new PermissionDeniedError())
  }
  next()
})

router.route('/')
  .get((req, res, next) => {
    InitiativeRepository.getAllChildrenById(
      req.auth.connection.initiativeId,
      undefined,
      initiativePublicMinProjection,
    )
      .then((initiatives) => {
        res.FromModel(initiatives)
      })
      .catch(e => next(e))
  });

router.route('/tree')
  .get((req, res, next) => {
    const initiativeId = req.auth.connection.initiativeId;
    InitiativeRepository.getAllChildrenById<InitiativePublicMin>(
      initiativeId,
      undefined,
      initiativePublicMinProjection,
    )
      .then((initiatives) => {
        const [rootTree] = arrayToTree(initiatives, initiativeId);
        res.FromModel(rootTree)
      })
      .catch(e => next(e))
  });


router.route('/:initiativeId/surveys')
  .get(async (req, res, next) => {
    try {
      const initiativeId = new ObjectId(req.params.initiativeId)
      if (!await ScopePermissions.can(req.auth, ScopePermission.SurveyRead, { initiativeId })) {
        return next(new PermissionDeniedError())
      }

      const changedSince = req.query.changedSince;
      if (changedSince) {
        const { success, data } = z.string().date().safeParse(changedSince);
        if (!success) {
          next(new BadRequestError('Invalid "changedSince" date. Please use (YYYY-MM-DD)'));
          return;
        }
        const surveyList = await SurveyPublicRepository.findSurveysFilter(initiativeId,  new Date(data));
        res.FromModel(surveyList)
        return;
      }

      SurveyPublicRepository.findSurveys(initiativeId)
        .then((surveyList) => res.FromModel(surveyList))
        .catch(e => next(e))

    } catch (e) {
      next(e);
    }
  });

export default router;
