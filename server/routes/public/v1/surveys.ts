import express from "express";
import { ScopePermissions } from "../../../public-api/scopePermission";
import PermissionDeniedError from "../../../error/PermissionDeniedError";
import { SurveyPublicRepository } from "../../../public-api/SurveyPublicRepository";
import { AuthPublicRouter } from "../PublicAuthRouter";
import { ScopePermission } from "../../../public-api/scopePermissionModels";
import { SurveyRepository } from "../../../repository/SurveyRepository";
import { ObjectId } from "bson";
import { publicSurvey } from "../../../public-api/types";
import HttpError from "../../../error/HttpError";
import BadRequestError from "../../../error/BadRequestError";
import { z } from "zod";

const router = express.Router() as AuthPublicRouter;


router.route('/')
  .get(async (req, res, next) => {
    try {
      if (!ScopePermissions.hasScope(req.auth, ScopePermission.SurveyRead)) {
        return next(new PermissionDeniedError())
      }

      const changedSince = req.query.changedSince;
      if (changedSince) {
        const { success, data } = z.string().date().safeParse(changedSince);
        if (!success) {
          next(new BadRequestError('Invalid "changedSince" date. Please use (YYYY-MM-DD)'));
          return;
        }
        const fromDate = new Date(data);
        const surveyList = await SurveyPublicRepository.findSurveysFilter(req.auth.connection.initiativeId, fromDate);
        res.FromModel(surveyList)
        return;
      }

      const surveyList = await SurveyPublicRepository.findSurveys(req.auth.connection.initiativeId);
      res.FromModel(surveyList)
    } catch (e) {
      return next(e)
    }
  });


router.route('/:surveyId')
  .get(async (req, res, next) => {

    try {
      if (!ScopePermissions.hasScope(req.auth, ScopePermission.SurveyRead)) {
        return next(new PermissionDeniedError())
      }

      const surveyPublic = await SurveyRepository.findOne({
        _id: new ObjectId(req.params.surveyId),
        deletedDate: { $exists: false },
      }, { ...publicSurvey, visibleUtrvs: 1, });

      if (!surveyPublic) {
        return next(new HttpError('Survey not found', 404));
      }

      const survey = await SurveyPublicRepository.findSurvey({ survey: surveyPublic });
      if (!await ScopePermissions.can(req.auth, ScopePermission.UniversalTrackerValueRead, survey)) {
        return next(new PermissionDeniedError());
      }
      res.FromModel(survey);
    } catch (e) {
      next(e)
    }
  });

export default router;
