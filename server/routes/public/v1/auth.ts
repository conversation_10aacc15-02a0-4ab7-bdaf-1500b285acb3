import express from "express";
import { authenticateServiceAccount } from "../publicAuth";
import HttpError from "../../../error/HttpError";
const router = express.Router();

router.post('/', (req, res) => {
  if (!req.body.clientId || !req.body.clientSecret) {
    return res.Exception(new HttpError('clientId and clientSecret must be provided', 401));
  }

  authenticateServiceAccount(req.body.clientId, req.body.clientSecret)
    .then((authResult) => res.json({ success: true, ...authResult }))
    .catch((err: Error) => {
      res.status(401).json({ success: false, message: err.message });
    });
});

export default router;
