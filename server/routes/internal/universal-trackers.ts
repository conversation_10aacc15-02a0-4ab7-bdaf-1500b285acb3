/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import UniversalTracker from '../../models/universalTracker';
import UniversalTrackerSearchService from '../../service/utr/UniversalTrackerSearchService';

const router = express.Router();

router.route('/')
  .get((req, res) => {
    UniversalTracker.find().sort({ name: 'asc' }).lean().exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  })
// .post((req, res) => {
//   if (req.body._id) {
//     return res.Invalid('Not allowed to specify _id on create');
//   }
//   const model = new UniversalTracker(req.body);
//   model.save()
//     .then((m) => res.FromModel(m))
//     .catch((e: Error) => res.Exception(e));
// });

router.route('/search')
  .post((req, res) => {
    UniversalTrackerSearchService.searchByRequest(req.body)
      .then((results) => res.FromModel(results))
      .catch((e: Error) => res.Exception(e));
  })

module.exports = router;
