/*
* Copyright (c) 2020. World Wide Generation Ltd
*/

/* Disable no-unsafe-argument instead of casting every "require" to RequestHandler */
/* eslint-disable @typescript-eslint/no-unsafe-argument */

import express from 'express';
import authService, {
  authenticationRequired,
  authWithProvisioning,
  internalAuthRequired,
} from '../service/authService';
import { checkIsStaff, UserActive } from '../middleware/userMiddlewares';
import { canManageInitiative, isRootLevel } from '../middleware/initiativeMiddlewares';
import { canManagePortfolio } from '../middleware/portfolioMiddlewares';
import { requireStaffScopes } from '../middleware/staffRoleMiddlewares';
import { StaffScope } from '../models/staffRole';

const router = express.Router();

const Auth = [authenticationRequired, UserActive];
const Staff = [authenticationRequired, checkIsStaff];
const PortfolioManager = [authenticationRequired, canManagePortfolio];
const InternalAuth = [internalAuthRequired];

router.use(authService.initialize());


// PUBLIC
router.get('/', require('./healthcheck'));
router.get('*splat/not-implemented', (_req, res) => {
  res.json({ success: true, message: 'Not Implemented' });
});
router.use('/inbound/', require('./inbound'));
router.use('/auth/', require('./auth'));
router.use('/admin/auth/', require('./admin/auth'));
router.use('/direct/', require('./direct/onboarding'));
router.use('/o/register/', require('./o/register'));
router.use('/o/un/', require('./o/un'));
router.use('/o/s/', require('./o/subscription'));
router.use('/o/common/', require('./o/common'));
router.use('/o/common/ai', Auth, require('./o/ai-common'));
router.use('/o/scorecard', require('./o/scorecard'));
router.use('/o/carbon-calculators', require('./o/carbon-calculators'));
router.use('/o/insight-dashboards', require('./shared-insight-dashboards'));
router.use('/o/value-list', require('./value-list'));
router.use('/reporting-frameworks', require('./reporting-frameworks'));
router.use('/materiality-assessment', Auth, require('./materiality-assessment'));

// AUTHENTICATED
router.use('/blueprints/', Auth, require('./blueprint'));
router.use('/documents/', Auth, require('./documents'));
router.use('/initiative-groups/', Auth, require('./initiative-groups'));
router.use('/initiative-tree/', Auth, require('./initiative-tree'));
router.use('/initiatives/:initiativeId/workgroups', Auth, require('./initiative-workgroups'));
router.use('/initiatives/:initiativeId/insight-dashboards', Auth, require('./insight-dashboards'));
router.use('/initiatives/:initiativeId/metric-groups/tag', Auth, require('./custom-tags'));
router.use('/initiatives/:initiativeId/external-mapping', Auth, require('./initiatives/external-mapping'));
router.use('/initiatives/:initiativeId/custom-reports', Auth, require('./initiative-custom-reports'));
router.use('/initiatives/:initiativeId/ai', Auth, require('./ai-initiatives'));
router.use('/initiatives/:initiativeId/emissions', Auth, require('./initiatives/emissions'));
router.use('/initiatives/:initiativeId/integrations', Auth, require('./initiatives/integrations'));
router.use('/initiatives/:initiativeId/report-documents', Auth, require('./initiatives/report-documents'));
router.use('/initiatives/:initiativeId/documents', Auth, require('./initiatives/documents'));
router.use('/initiatives/', Auth, require('./initiatives'), require('./organisation-map'));
// Onboarding does not require user to be active
router.use('/onboarding/', authenticationRequired, require('./onboarding'));
router.use('/reference-data/', Auth, require('./reference-data'));
router.use('/portfolios/', Auth, require('./portfolios'));
router.use('/reports/', Auth, require('./reports'));
router.use('/scorecard/', Auth, require('./scorecard'));
router.use('/setup-tasks/', Auth, require('./setup-tasks'));
router.use('/surveys/', Auth, require('./surveys'));
router.use('/universal-trackers/', Auth, require('./universal-trackers'));
router.use('/universal-tracker-groups/', Auth, require('./universal-tracker-groups'));
router.use('/universal-tracker-values/', Auth, require('./universal-tracker-values'));
router.use(
  '/universal-tracker-values/:utrvId/secondary-connections',
  Auth,
  require('./universal-tracker-values-connections')
);
router.use('/organizations', Auth, require('./organization'));
router.use('/keys', Auth, require('./api-keys'));
router.use('/users/current', authWithProvisioning, require('./user'));
router.use('/users/', Auth, require('./users'));
router.use('/assurers/', Auth, require('./assurers'));
router.use('/assurances/', Auth, require('./assurances'));
router.use('/value-list/', Auth, require('./value-list'));
router.use('/data-share/', Auth, require('./data-share'));
router.use('/bookmarks/', Auth, require('./bookmarks'));
router.use('/survey-templates/', Auth, require('./survey-templates'));
router.use('/banking-settings', Auth, require('./banking-settings'));
router.use('/stock-exchange-settings', Auth, require('./stock-exchange-settings'));
router.use('/background-jobs', Auth, require('./background-jobs'));

// MANAGER
router.use('/initiative-universal-trackers', Auth, require('./initiative-universal-trackers'));
router.use('/initiatives/:initiativeId/bulk-import', Auth, canManageInitiative, require('./manager/bulk-import'));
router.use('/initiatives/:initiativeId/users', Auth, canManageInitiative, require('./manager/users'));
router.use('/initiatives/:initiativeId/onboardings', Auth, canManageInitiative, require('./manager/onboardings'));
router.use('/initiatives/:initiativeId/subscription', Auth, canManageInitiative, require('./manager/subscription'));
router.use('/initiatives/:initiativeId/sponsorships', Auth, canManageInitiative, require('./manager/initiative-sponsorships'));
router.use('/initiatives/:initiativeId/stats', Auth, canManageInitiative, require('./manager/initiative-stats'));
router.use('/initiatives/:initiativeId/custom-metrics', Auth, canManageInitiative, require('./manager/custom-metrics'));
router.use('/initiatives/:initiativeId/data-share', Auth, canManageInitiative, require('./manager/initiatives-datashare'));
router.use('/initiatives/:initiativeId/aggregated-survey', Auth, canManageInitiative, require('./manager/initiative-aggregated-survey'));
router.use('/initiatives/:initiativeId/export', Auth, canManageInitiative, require('./manager/initiative-export'));
router.use('/initiatives/:initiativeId', Auth, canManageInitiative, require('./manager/initiatives'));
router.use('/initiatives/:initiativeId/metric-groups', Auth, canManageInitiative, require('./manager/metric-groups'));
router.use('/initiatives/:initiativeId/app-integration', Auth, isRootLevel, canManageInitiative, require('./manager/app-integration'));
router.use('/portfolios/:portfolioId/insight-dashboards/', Auth, PortfolioManager, require('./manager/portfolio-insight-dashboards'));
router.use('/portfolios/:portfolioId', Auth, PortfolioManager, require('./manager/portfolios'));

// ADMIN
router.use('/admin/checks', Staff, require('./admin/checks'));
router.use('/admin/maintenance', Staff, require('./admin/maintenance'));
router.use('/admin/assurance', Staff, require('./admin/assurance'));
router.use('/admin/addons', Staff, require('./admin/addons'));
router.use('/admin/background-jobs', Staff, require('./admin/background-jobs'));
router.use('/admin/csv-survey-data/', Staff, require('./admin/csv-survey-data'));
router.use('/admin/data-share/', Staff, require('./admin/data-share'));
router.use('/admin/value-list/', Staff, require('./admin/value-list'));
router.use('/admin/documents/', Staff, require('./admin/documents'));
router.use('/admin/emails', Staff, require('./admin/emails'));
router.use('/admin/initiatives/', Staff, require('./admin/initiatives'), require('./initiatives'));
router.use('/admin/initiative-tree/', Staff, require('./admin/initiative-tree'));
router.use('/admin/initiative-groups/', Staff, require('./admin/initiative-groups'), require('./initiative-groups'));
router.use('/admin/stats', Staff, require('./admin/stats'));
router.use('/admin/messages/', Staff, require('./admin/messages'));
router.use('/admin/onboarding/', Staff, require('./admin/onboarding'));
router.use('/admin/onboarding-list/', Staff, require('./admin/onboarding-list'));
router.use('/admin/organizations/', Staff, require('./admin/organizations'));
router.use('/admin/profile', Staff, require('./admin/profile'));
router.use('/admin/reference-data/', Staff, require('./admin/reference-data'), require('./reference-data'));
router.use('/admin/reporting-frameworks', Staff, require('./admin/reporting-frameworks'), require('./reporting-frameworks'));
router.use('/admin/reporting', Staff, require('./admin/reporting'));
router.use('/admin/surveys/sources/', Staff, require('./survey-sources'));
router.use('/admin/surveys/', Staff, require('./admin/surveys'));
router.use('/admin/sponsorships/', Staff, require('./admin/sponsorship'));
router.use('/admin/sponsorship-configs/', Staff, require('./admin/sponsorship-config'));
router.use('/admin/subscriptions/', Staff, require('./admin/subscriptions'));
router.use('/admin/ledger-universal-tracker-values/', Staff, require('./admin/ledger-universal-tracker-values'));
router.use('/admin/universal-tracker-groups/', Staff,
  require('./admin/universal-tracker-groups'), require('./universal-tracker-groups'));
router.use('/admin/universal-tracker-schedules/', Staff, require('./admin/universal-tracker-schedules'));
router.use('/admin/universal-tracker-value-assurances/', Staff, require('./admin/universal-tracker-value-assurances'));
router.use('/admin/universal-tracker-values/', Staff,
  require('./admin/universal-tracker-values'), require('./universal-tracker-values'));
router.use('/admin/universal-trackers/', Staff, require('./admin/universal-trackers'));
router.use(
  '/admin/users',
  Staff,
  requireStaffScopes([StaffScope.UserRead]),
  require('./admin/staff-roles'),
  require('./admin/users'),
  require('./users')
);
router.use('/admin/user-preferences', Staff, require('./admin/user-preferences'));
router.use('/admin/user-events', Staff, require('./admin/user-events'));
router.use(
  '/admin/materiality-assessment',
  Staff,
  requireStaffScopes([StaffScope.CompanyRead]),
  require('./materiality-assessment'),
  require('./admin/materiality-assessment')
);

// INTERNAL
router.use('/internal/universal-trackers', Staff, require('./internal/universal-trackers'));
router.use('/internal/releases', InternalAuth, require('./internal/releases'));

module.exports = router;
