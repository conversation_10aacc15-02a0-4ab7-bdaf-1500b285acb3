import express from 'express';
import { AuthRouter } from '../http/AuthRouter';
import { getBackgroundJobService } from '../service/background-process/BackgroundJobService';
import { BackgroundJobModel } from '../models/backgroundJob';
import { canManageBackgroundJob } from '../middleware/backgroundJobMiddlewares';

const router = express.Router() as AuthRouter;
const backgroundJobService = getBackgroundJobService();

router.route('/:id/rerun').post(canManageBackgroundJob, async (req, res) => {
  const backgroundJob = res.locals.job as BackgroundJobModel;
  const updatedJob = await backgroundJobService.resetAndRunJob(backgroundJob, req.user);
  res.FromModel(updatedJob);
});

module.exports = router;
