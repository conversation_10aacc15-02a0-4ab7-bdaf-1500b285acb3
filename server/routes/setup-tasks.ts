/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */


import express from 'express';
import { getSetupTaskService } from "../service/task/SetupTaskService";
import { AuthRouter } from "../http/AuthRouter";

const router = express.Router() as AuthRouter;
const service = getSetupTaskService();


router.route('/')
  .get((req, res, next) => {
    const initiativeId = req.query.initiativeId as string
    service.getUserTasks({
      user: req.user,
      initiativeId: initiativeId,
      domain: req.header('origin'),
    }).then((model) => res.FromModel(model))
      .catch((e: Error) => next(e));
  });

router.route('/:taskId/:action{/:initiativeId}')
  .patch((req, res, next) => {

    const { taskId, action, initiativeId } = req.params;
    service.processAction({ taskId, action, userId: req.user._id, initiativeId })
      .then((model) => res.FromModel(model))
      .catch((e: Error) => next(e));
  });

module.exports = router;
