import express from 'express';
import { AuthRouter } from '../http/AuthRouter';
import { canManageInitiative, isNotRootLevel } from '../middleware/initiativeMiddlewares';
import { ObjectId } from 'bson';
import { UserRepository } from '../repository/UserRepository';
import { getArchivedInitiativeManager } from '../service/initiative/ArchivedInitiativeManager';
import { ArchiveSubsidiaryRequest } from '../models/archivedInitiative';
import UserError from '../error/UserError';
import { wwgLogger } from '../service/wwgLogger';
import { ArchivedInitiativeRepository } from '../repository/ArchivedInitiativeRepository';
import { getAuditLogger } from '../service/audit/AuditLogger';
import { InitiativeAudit } from '../service/audit/events/Initiative';
import { LEVEL } from '../service/event/Events';

const router = express.Router() as AuthRouter;
const auditLogger = getAuditLogger();

router.route('/:initiativeId/map/users')
  .get(canManageInitiative, async (req, res) => {
    try {
      const initiativeId = new ObjectId(req.params.initiativeId);
      const users = await UserRepository.getUsersByInitiativeAndDescendants(initiativeId);
      res.FromModel(users);
    } catch (error) {
      res.Exception(error);
    }
  });

router.route('/:initiativeId/archive')
  .patch(canManageInitiative, isNotRootLevel, async (req, res, next) => {
    try {
      const archivedInitiativeManager = getArchivedInitiativeManager();
      const archivedId = req.params.initiativeId;
      const { reassignedUserIds, removedUserIds, reassignedInitiativeId } = req.body as ArchiveSubsidiaryRequest;

      if (!Array.isArray(reassignedUserIds) || !Array.isArray(removedUserIds)) {
        return next(new UserError('Reassigned user and removed user IDs must be an array'));
      }

      if (reassignedUserIds.length > 0 && !reassignedInitiativeId) {
        return next(new UserError('Users can only be reassigned if reassigned initiative is provided'));
      }

      const input = archivedInitiativeManager.convertInput({ ...req.body, archivedId, user: req.user });

      const archivedIds = await archivedInitiativeManager.archive(input);

      auditLogger
        .fromRequest(req, {
          initiativeId: input.archivedId,
          auditEvent: InitiativeAudit.archivedInitiative,
          severity: LEVEL.WARNING,
          targets: archivedIds.map((id) => auditLogger.initiativeTarget({ _id: id })),
        })
        .catch(wwgLogger.error);
      res.Success(`Successfully archived initiative ${archivedId}`);
    } catch (error) {
      next(error);
    }
  });

router.route('/:initiativeId/map/archived-initiatives')
  .get(canManageInitiative, async (req, res, next) => {
    try {
      const initiativeId = new ObjectId(req.params.initiativeId);
      const archivedInitiatives = await ArchivedInitiativeRepository.getInitiativeTreeById(initiativeId);

      res.FromModel(archivedInitiatives);
    } catch (error) {
      next(error);
    }
  });

router.route('/:initiativeId/reactivate')
  .patch(canManageInitiative, async (req, res, next) => {
    try {
      const archivedInitiativeManager = getArchivedInitiativeManager();
      const reportingParentId = req.params.initiativeId;
      const { archivedInitiativeId } = req.body;

      const input = {
        archivedInitiativeId: new ObjectId(archivedInitiativeId),
        reportingParentId: new ObjectId(reportingParentId),
      };

      const reactivatedIds = await archivedInitiativeManager.reactivate(input);
      auditLogger
        .fromRequest(req, {
          initiativeId: input.reportingParentId,
          auditEvent: InitiativeAudit.reactivatedInitiative,
          severity: LEVEL.INFO,
          targets: reactivatedIds.map((id) => auditLogger.initiativeTarget({ _id: id })),
        })
        .catch(wwgLogger.error);
      res.Success(`Successfully reactivated initiatives: ${reactivatedIds.join(', ')}`);
    } catch (error) {
      next(error);
    }
  });

module.exports = router;
