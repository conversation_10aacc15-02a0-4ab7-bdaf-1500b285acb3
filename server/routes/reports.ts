/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { ObjectId } from 'bson';
import { ReportGenerator } from '../service/scorecard/ReportGenerator';
import Initiative from '../models/initiative';

const router = express.Router();

const paramIdCheck = (params: string[]) => (req: any, res: any, next: any) => {
  try {
    for (const param of params) {
      if (req.params[param]) {
        req.params[param] = new ObjectId(req.params[param]);
      }
    }
  } catch (e) {
    return res.Invalid('Initiative id is not valid');
  }
  next();
};

router.route('/initiative/:initiativeId/scorecard{/:surveyId}')
  .get(paramIdCheck(['initiativeId']), (req: any, res) => {
    Initiative.findById(req.params.initiativeId).orFail().exec().then((initiative) => {
      // Must come from published data
      return ReportGenerator.generate(initiative, req.user, req.params.surveyId)
        .then(({ data, headers }) => ReportGenerator.sendResponse(res, {
          data,
          headers,
          name: initiative.name
        }))
    }).catch(e => res.Exception(e));
  });


module.exports = router;
