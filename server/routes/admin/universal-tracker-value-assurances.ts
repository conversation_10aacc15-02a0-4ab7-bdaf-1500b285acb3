/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import UniversalTrackerValueAssurance from '../../models/universalTrackerValueAssurance';
import { UniversalTrackerValueAssuranceModel } from '../../service/assurance/model/Assurance';
import { buildCRUD } from './baseCrud';

const router = buildCRUD<UniversalTrackerValueAssuranceModel>(
  UniversalTrackerValueAssurance
);

module.exports = router;
