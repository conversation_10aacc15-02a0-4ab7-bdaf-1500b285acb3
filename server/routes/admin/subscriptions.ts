/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import express from 'express';
import { ObjectId } from 'bson';
import { AuthenticatedInitiativeRouter } from '../../http/AuthRouter';
import { getCustomerManager } from "../../service/payment/CustomerManager";
import { getInitiativeRepository } from '../../repository/InitiativeRepository';
import { InitiativeWithCustomer } from '../../models/initiative';
import { wwgLogger } from '../../service/wwgLogger';
import BadRequestError from "../../error/BadRequestError";
import { isProductCode } from "../../service/payment/subscriptionCodes";
import { getStripeCouponSetup } from "../../service/payment/StripeCouponSetup";
import { toBoolean } from "../../http/query";
import { getSponsorshipService } from '../../service/referral/SponsorshipService';
import ContextError from '../../error/ContextError';

const router = express.Router() as AuthenticatedInitiativeRouter;

router.route("/refresh-all")
  .get(async (req, res, next) => {
    try {

      // Beware of Stripe rate-limit (100ops/second)
      // https://stripe.com/docs/rate-limits
      let updatedInitiatives = 0;
      let updatedSubscriptions = 0;
      let remainingInitiatives = 0;

      const customerManager = getCustomerManager();
      const updateInitiative = async (initiative: InitiativeWithCustomer) => {
        // Update the subscriptions in serial to avoid collisions as they update same model
        try {
          const subscriptions = await customerManager.getRemoteSubscriptions(initiative);
          const subscriptionIds = subscriptions.data.map(sub => sub.id);
          for (const subId of subscriptionIds) {
            try {
              await customerManager.updateSubscription(initiative, subId);
              updatedSubscriptions += 1;
            } catch (e) {
              wwgLogger.error(new ContextError('Skipping refresh for subscription', {
                subscriptionId: subId,
                cause: e
              }));
            }
          }
        } catch (e) {
          wwgLogger.error(new ContextError(`Skipping "${initiative.name}" refresh`, {
            initiativeId: initiative._id.toString(),
            cause: e
          }));
        }
        updatedInitiatives += 1;
        remainingInitiatives -= 1;
        wwgLogger.info(`Refreshed "${initiative.name}" subscriptions`, {
          initiativeId: initiative._id.toString(),
          updatedSubscriptions
        });
      }

      const initiatives = await getInitiativeRepository().findWithSubscriptions();
      remainingInitiatives = initiatives.length;

      wwgLogger.info(`Found ${initiatives.length} Initiatives to refresh with subscriptions.`, { userId: req.user._id });

      // Update initiatives in serial to avoid rate-limit
      for (const initiative of initiatives) {
        await updateInitiative(initiative);
        await new Promise(resolve => setTimeout(resolve, 100)); // 100ms wait
      }

      wwgLogger.info('Completed Refresh-all', {
        updatedInitiatives,
        remainingInitiatives,
        updatedSubscriptions
      });

      res.FromModel({
        finished: true,
        updatedInitiatives,
        remainingInitiatives,
        updatedSubscriptions
      });
    } catch (e) {
      next(e);
    }
  })

router.route('/initiatives/:initiativeId/subscriptions-referral-info').get((req, res, next) => {
  const { initiativeId } = req.params;
  getSponsorshipService()
    .getSubscriptionCoupons(new ObjectId(initiativeId))
    .then((results) => {
      res.FromModel(results)
    })
    .catch((e) => next(e));
});

router.route("/coupons")
  .get((req, res, next) => {

    const { durationInMonths, productCode, limit } = req.query
    if (!isProductCode(productCode)) {
      return next(new BadRequestError(`productCode is not valid`))
    }

    if (limit !== undefined && Number.isNaN(limit)) {
      const l = Number(limit);
      if (l < 1 || l > 100 ) {
        return next(new BadRequestError(`Limit must be a valid number between 1 and 100`));
      }
    }

    getStripeCouponSetup().getCoupons({
      durationInMonths: durationInMonths ? Number(durationInMonths) : undefined,
      productCode: productCode,
      limit: limit !== undefined ? Number(limit) : undefined,
      // Staff and 100% discount
      isStaff: true,
      discountPercentage: 100,
    }).then(coupons => res.FromModel(coupons))
        .catch(e => next(e));
  })


router.route("/coupons/setup")
  .post(async (req, res, next) => {
    getStripeCouponSetup()
      .process({ shouldCreateMissing: toBoolean(req.body.shouldCreateMissing) })
      .then(couponDetails => res.FromModel(couponDetails))
      .catch(e => next(e))
  })

router.route("/coupons/:couponId")
  .post(async (req, res, next) => {

    const couponId = req.params.couponId;
    const { description } = req.body;

    if (!description || typeof description !== 'string') {
      next(new BadRequestError(`Please provide a description for promo code`));
    }

    getStripeCouponSetup()
      .createPromotionCode({ couponId, description })
      .then(promo => res.FromModel(promo))
      .catch(e => next(e))
  })

module.exports = router;
