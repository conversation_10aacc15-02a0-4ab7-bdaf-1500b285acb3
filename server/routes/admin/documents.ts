/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { addFileUrl, getStorage } from '../../service/storage/fileStorage';
import Document from '../../models/document';
import multer = require('multer');
import deleteConfirm from '../../middleware/deleteConfirm';
import { getDocumentService } from '../../service/file/DocumentService';

const diskStorage = multer.diskStorage({
  destination: (req, file, cb) => cb(null, '/tmp/'),
  filename: (req, file, cb) => cb(null, file.fieldname + '-' + Date.now())
});

const router = express.Router();
const documents = getStorage();
const upload = multer({ storage: diskStorage });

const handlePost = async (req: any, res: any) => {

  const files = req.files;
  const data = req.body;

  if (!Array.isArray(files)) {
    return res.json({ success: false, message: 'No files uploaded' });
  }

  const file = files.shift();
  try {
    const result = await getDocumentService().handleDocumentUpload(data, file, req.user);
    return res.FromModel(result)
  } catch (e) {
    return res.Exception(e);
  }
};

const handlePatch = async (req: any, res: any) => {
  Document.findByIdAndUpdate(req.params.id, { $set: req.body })
    .then(() => {
      const message = 'Successfully updated document with _id=' + req.params.id;
      res.Success(message);
    }).catch(e => res.Exception(e));
};

const handleGet = async (req: any, res: any) => {
  try {
    let model = await Document.findById(req.params.id).orFail().lean().exec();

    const metadata = model.metadata;
    if (!metadata) {
      throw new Error(`Document ${model._id} is missing metadata`)
    }
    model = addFileUrl(model);
    const path = `${model._id}.${metadata.extension}`;
    const signedUrl = await documents.getDownloadUrl(path, metadata.name);

    model.url = signedUrl.pop();
    res.FromModel(model);
  } catch (e) {
    res.Exception(e);
  }
};

function getDocumentsByOwnerId(req: any, res: any) {
  Document.find({ ownerId: req.params.id }).lean().exec()
    .then((data: Array<any>) => {
      data.map(addFileUrl);
      return res.FromModel(data);
    })
    .catch((e) => res.Exception(e));
}

router.route('/')
  .post(upload.any(), handlePost);

router.route('/:id')
  .get(handleGet)
  .patch(handlePatch)
  .delete(deleteConfirm, (req, res) => {
    Document.findByIdAndDelete(req.params.id).exec()
      .then(() => res.Success('Object Deleted'))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/owner/:id')
  .get(getDocumentsByOwnerId);

module.exports = router;
module.exports.addFileUrl = addFileUrl;
module.exports.handleGet = handleGet;
