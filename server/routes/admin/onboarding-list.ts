/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import OnboardingList, { OnboardingListModel } from '../../models/onboardingList';

const router = express.Router();

router.route('/')
  .get((req, res) => {
    OnboardingList.find().exec()
      .then((data: OnboardingListModel[]) => res.FromModel(data))
      .catch((e: Error) => res.Exception(e));
  })

module.exports = router;
