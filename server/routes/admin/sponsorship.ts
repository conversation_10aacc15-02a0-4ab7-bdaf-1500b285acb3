/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { buildCRUD, createSort } from './baseCrud';
import Sponsorship, { ExtendedSponsorshipModel, SponsorshipModel } from "../../models/sponsorship";
import express from "express";
import { AuthRouter } from "../../http/AuthRouter";
import { RequesterCode } from '../../service/organization/domainConfig';
import { ProductCodes } from '../../models/customer';
import { getSponsorshipMigration } from '../../service/referral/SponsorshipMigration';
import ContextError from "../../error/ContextError";
import { getSponsorshipManager } from "../../service/referral/SponsorshipManager";

const router = express.Router() as AuthRouter;
router.route('/').get((req, res) => {
  const sort = createSort(req.query);
  Sponsorship.find({}, undefined, { sort })
    .populate([
      { path: 'initiative', select: 'name' },
      { path: 'sponsorshipConfig', select: 'title' },
    ])
    .exec()
    .then((model ) => res.FromModel(model))
    .catch((e: Error) => res.Exception(e));
})

// Migrate legacy sponsorships to new sponsorship workflow
router.route('/migrate').post(async (req, res, next) => {
  try {
    const sponsorshipMigration = getSponsorshipMigration();

    const {
      referralCode = 'SGX4SGX',
      sponsorCode = RequesterCode.SGX,
      productCode = ProductCodes.SGXESGenome,
      limit = 50,
      initiativeIds,
    } = req.body;

    const data = await sponsorshipMigration.process({
      referralCode,
      sponsorCode,
      productCode,
      initiativeIds,
      limit,
    });
    return res.FromModel(data);
  } catch (e) {
    next(e);
  }
});

router.route('/:sponsorshipId/renew').post(async (req, res, next) => {
  const sponsorshipId = req.params.sponsorshipId;
  try {
    const sponsorshipManager = getSponsorshipManager();
    const sponsorship = await Sponsorship.findById(sponsorshipId)
      .populate<ExtendedSponsorshipModel>('sponsorshipConfig')
      .orFail()
      .exec();

    if (sponsorshipManager.shouldRenew(sponsorship) && !req.body.forceRenew) {
      return res.FromModel({
        message: 'Sponsorship is not due for renewal. Use { forceRenew: true } to renew anyway',
        sponsorship: sponsorship.toObject()
      });
    }

    const periodEndDate = sponsorship.periodEndDate;

    const updatedModel = await sponsorshipManager.renew(sponsorship)
    res.FromModel({
      message: 'Sponsorship renewed successfully',
      sponsorship: updatedModel,
      periodEndDate: {
        before: periodEndDate,
        after: updatedModel.periodEndDate,
      }
    });
  } catch (e) {
    next(e);
  }
});

module.exports = buildCRUD<SponsorshipModel>(Sponsorship, router);
