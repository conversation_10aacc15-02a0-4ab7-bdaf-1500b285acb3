/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express, { Response } from 'express';
import FileUpload from '../../http/FileUpload';
import { saveProfile } from '../../service/file/profile';
import BadRequestError from '../../error/BadRequestError';

const router = express.Router();

router.route('/type/:type/:id')
  .post(FileUpload.any(), async (req: any, res: Response) => {

    const files = req.files;
    if (!Array.isArray(files)) {
      return res.Exception(new BadRequestError('No files uploaded'));
    }

    const type = req.params.type;
    const id = req.params.id;

    try {
      const successMessage = await saveProfile(id, type, files);
      res.Success(successMessage);
    } catch (e) {
      res.Exception(e);
    }
  });

module.exports = router;
