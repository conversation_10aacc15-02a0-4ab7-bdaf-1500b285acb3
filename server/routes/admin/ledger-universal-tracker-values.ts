/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import LedgerUniversalTrackerValue
  from '../../models/ledgerUniversalTrackerValue';

const router = express.Router();
router.route('/')
  .get((req, res) => {
    LedgerUniversalTrackerValue.find()
      .sort({ lastUpdatedDate: 'asc' }).lean().exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  });

module.exports = router;
