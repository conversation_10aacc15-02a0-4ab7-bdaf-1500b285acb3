/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { buildCRUD } from './baseCrud';
import BackgroundJob, { BackgroundJobModel } from "../../models/backgroundJob";
import { getBackgroundJobService } from "../../service/background-process/BackgroundJobService";
import { BackgroundBaseWorkflow } from '../../service/background-process/BackgroundBaseWorkflow';
import { wwgLogger } from "../../service/wwgLogger";
import { ObjectId } from 'bson';

const router = buildCRUD<BackgroundJobModel>(BackgroundJob);

router.route('/:id/run')
  .post((req, res, next) => {
    getBackgroundJobService().runById(req.params.id, req.user)
      .then(job => res.FromModel(job))
      .catch(next);
  });

router.route('/:id/reset')
  .post(async (req, res) => {
    const job = await BackgroundJob.findOne<BackgroundJobModel>({
      _id: new ObjectId(req.params.id),
    })
      .orFail()
      .exec();
    await BackgroundBaseWorkflow.resetJob(job, req.user);
    wwgLogger.warn(`Job ${job._id} reset by ${req.user.email}`, {
      jobId: job._id,
      userId: req.user._id.toString(),
    });
    return res.FromModel(job);
  });

module.exports = router;
