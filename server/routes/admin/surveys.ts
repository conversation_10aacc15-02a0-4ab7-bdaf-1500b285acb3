/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import Survey, { isAggregatedSurvey, SurveyModelMinData, SurveyType } from '../../models/survey';
import deleteConfirm from '../../middleware/deleteConfirm';
import UserError from '../../error/UserError';
import { createStakeholderGroup, StakeholderGroupManager, } from '../../service/stakeholder/StakeholderGroupManager';
import { getSurveyManager } from '../../service/survey/SurveyManager';
import { bodyParamIdCheck } from '../../middleware/commonMiddlewares';
import { SurveyImporter } from '../../service/survey/SurveyImporter';
import { getSurveyDeleteManager } from '../../service/survey/SurveyDeleteManager';
import { SurveyProcessRepository } from '../../repository/SurveyProcessRepository';
import { AuthRouter } from '../../http/AuthRouter';
import { AdminAuditLogs } from '../../middleware/audit/adminAudit';
import BadRequestError from "../../error/BadRequestError";
import { createSurveyCode } from "../../util/string";
import { Blueprints, DefaultBlueprintCode } from "../../survey/blueprints";
import { DataPeriods, UtrvType } from "../../service/utr/constants";
import Initiative from "../../models/initiative";
import { SurveyScope } from "../../service/survey/SurveyScope";
import MetricGroup from "../../models/metricGroup";
import { ObjectId } from "bson";
import moment from "moment";
import { wwgLogger } from "../../service/wwgLogger";
import { getSgxMappingImporter } from "../../service/importer/SgxImporter";
import FileUpload from "../../http/FileUpload";
import { ContextMiddleware } from '../../middleware/audit/contextMiddleware';
import { checkIsStaff, checkIsSuperAdmin } from '../../middleware/userMiddlewares';
import { SurveyQuery } from '../../service/survey/SurveyQuery';
import { getSurveyCleaner } from '../../service/survey/cleanup/SurveyCleaner';
import { toBoolean } from '../../http/query';
import { getRootInitiativeService } from "../../service/organization/RootInitiativeService";
import { FeatureCode } from "@g17eco/core";
import { getMaterialityAssessmentManager, MaterialityAssessmentManager } from '../../service/materiality-assessment/MaterialityAssessmentManager';
import { InitiativeRepository } from '../../repository/InitiativeRepository';
import { getSurveyAggregator } from '../../service/survey/SurveyAggregator';

const surveyManager = getSurveyManager();
const deleteManager = getSurveyDeleteManager();
const surveyCleaner = getSurveyCleaner();

export interface ImportSurveyData {
  initiativeCode: string
  effectiveDate: string
  evidenceRequired: boolean
  verificationRequired: boolean
  utrvType: string
  scope?: Record<string, string[]>;
  sourceName?: string;
}

const router = express.Router() as AuthRouter;
router.use(AdminAuditLogs({ service: 'survey' }));

router
  .route('/')
  .get((_req, res, next) => {
    Survey.find(
      {},
      {
        name: 1,
        code: 1,
        type: 1,
        created: 1,
        sourceName: 1,
        effectiveDate: 1,
        initiativeId: 1,
        deletedDate: 1,
      }
    )
      .exec()
      .then((model: unknown[]) => res.FromModel(model))
      .catch((e: Error) => next(e));
  })
  .post(async function (req, res, next) {
    try {
      if (req.body._id) {
        return res.Invalid('Not allowed to specify _id on create');
      }
      const result = await SurveyImporter.create(req.body, req.user);
      res.FromModel(result);
    } catch (e) {
      next(e);
    }
  });

  router
  .route('/initiative/:initiativeId')
  .get(async (req, res, next) => {
    const { initiativeId } = req.params;

    const initiatives = await InitiativeRepository.getAllChildrenById(initiativeId, undefined, { _id: 1 });

    Survey.find(
      { initiativeId: { $in: initiatives } },
      {
        name: 1,
        code: 1,
        created: 1,
        type: 1,
        sourceName: 1,
        effectiveDate: 1,
        initiativeId: 1,
        deletedDate: 1,
      }
    )
      .exec()
      .then((model: unknown[]) => res.FromModel(model))
      .catch((e: Error) => next(e));
  })

router.route('/clean/:initiativeId')
  .post(checkIsSuperAdmin, async function (req, res, next) {

    try {
      const duplicates = await surveyCleaner.removeDuplicates({
        initiativeId: new ObjectId(req.params.initiativeId),
        applyToChildren: toBoolean(req.body.applyToChildren),
        allowToDeleteNonEmpty: toBoolean(req.body.allowToDeleteNonEmpty),
        executeDelete: toBoolean(req.body.executeDelete),
      }, req.user);
      res.FromModel(duplicates);
    } catch (e) {
      next(e)
    }
  });

router.route('/import')
  .post(checkIsSuperAdmin, async function (req, res, next) {
    try {
      const { survey }: { survey: ImportSurveyData; options?: unknown } = req.body;
      if (!survey) {
        return next(new BadRequestError('Missing survey configuration'));
      }

      const options = {
        onConflictReplace: false,
        includeUser: false,
        ...req.body.options,
      };

      const initiative = await Initiative.findOne({ code: survey.initiativeCode }).orFail().exec();
      if (survey.scope?.custom) {
        const uniqueIds = Array.from(new Set<string>(survey.scope.custom));
        const ids = uniqueIds.map((id) => new ObjectId(id));
        const groups = await MetricGroup.find({ _id: { $in: ids } }, { _id: 1 })
          .lean()
          .exec();
        if (groups.length !== ids.length) {
          const expected = ids.join(',');
          return next(
            new BadRequestError(`Not all custom metric group found, '${expected}': found ${groups.map((g) => g._id)}`)
          );
        }
      }

      const user = req.user;
      const withUser = options.includeUser ? [user._id] : [];

      // YYYY-MM-DD or actual ISO
      const simpleFormat = 'YYYY-MM-DD';
      const effectiveDate =
        survey.effectiveDate.length === simpleFormat.length
          ? moment.utc(survey.effectiveDate, simpleFormat).endOf('day').toDate()
          : moment.utc(survey.effectiveDate).toDate();

      const rootInitiativeService = getRootInitiativeService();
      const config = await rootInitiativeService.getConfig(initiative, { domain: req.header('origin') });
      const hasVerificationFeature = rootInitiativeService.hasFeature(config, FeatureCode.Verification);

      const type = SurveyType.Default;
      const period = DataPeriods.Yearly;
      const createData: SurveyModelMinData = {
        scope: { ...SurveyScope.createEmpty(), ...survey.scope },
        visibleUtrvs: [],
        code: createSurveyCode(initiative.code),
        name: undefined,
        sourceName: survey.sourceName ?? DefaultBlueprintCode,
        period,
        effectiveDate: effectiveDate,
        utrvType: UtrvType.Actual,
        visibleStakeholders: withUser,
        unitConfig: SurveyImporter.createUnitConfig(initiative),
        initiativeId: initiative._id,
        stakeholders: createStakeholderGroup(withUser, withUser),
        roles: { admin: withUser, viewer: [] },
        evidenceRequired: Boolean(survey.evidenceRequired),
        type,
        verificationRequired: hasVerificationFeature && Boolean(survey.verificationRequired),
      };

      const conflictingSurveys = await SurveyQuery.findBy({
        initiativeId: createData.initiativeId,
        effectiveDate: createData.effectiveDate,
        sourceName: createData.sourceName,
        type,
        period,
      });

      wwgLogger.info('Executing onConflictReplace check', {
        options,
        count: conflictingSurveys.length,
        effectiveDateL: createData.effectiveDate,
      });

      const hasConflicts = conflictingSurveys.length > 0;
      if (hasConflicts && !options.onConflictReplace) {
        return res.FromModel({
          survey: {
            _id: conflictingSurveys.find((s) => !s.deletedDate)?._id,
            initiativeId: initiative._id,
            initiativeName: initiative.name,
            status: 'conflict',
          },
          options,
        });
      }

      if (options.onConflictReplace) {
        if (hasConflicts) {
          wwgLogger.info(`Deleting conflict survey by admin for initiative ${initiative.code}`, {
            conflictIds: conflictingSurveys.map((s) => String(s._id)),
            initiativeId: String(createData.initiativeId),
            effectiveDate: createData.effectiveDate,
            sourceName: createData.sourceName,
            type: createData.type,
          });
          const deleteRequests = conflictingSurveys.map((survey) => {
            return deleteManager.disableSurvey(survey, user);
          });
          await Promise.all(deleteRequests).catch(wwgLogger.error);
        }
      }

      const result = await SurveyImporter.create(createData, user);
      res.FromModel({
        survey: {
          _id: result._id,
          initiativeId: result.initiativeId,
          initiativeName: initiative.name,
          status: 'created',
        },
        options,
      });
    } catch (e) {
      next(e);
    }
  });

router
  .route('/importsgx')
  .post(checkIsStaff, FileUpload.single('file'), ContextMiddleware, async function (req, res, next) {
    try {
      const importer = getSgxMappingImporter();
      const file = req.file;
      if (!file || !file.path) {
        return res.Exception(`Missing required import file`);
      }
      const { imported, errors } = await importer.sgxImport({ filePath: file.path, sheetName: 'Data' }, req.user);
      res.FromModel({ imported, errors });
    } catch (e) {
      next(e);
    }
  });

router
  .route('/:id')
  .get((req, res, next) => {
    Survey.findById(req.params.id)
      .exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => next(e));
  })
  .delete(deleteConfirm, async (req, res, next) => {
    try {
      const survey = await Survey.findById(req.params.id).exec();
      if (!survey) {
        return res.Exception(new UserError(`No survey found with ID ${req.params.id}`));
      }

      await deleteManager.disableSurvey(survey, req.user);
      return res.Success('Deleted successfully');
    } catch (e) {
      next(e);
    }
  })
  .put(bodyParamIdCheck, (req, res, next) => {
    if (!req.body._id || req.params.id !== req.body._id) {
      return res.Invalid('POST url id (' + req.params.id + ') does not match POST body _id (' + req.body._id + ')');
    }

    Survey.findById(req.params.id)
      .orFail()
      .exec()
      .then((survey) => {
        const stakeholderGroupUpdate = StakeholderGroupManager.createGroupUpdate(
          survey.stakeholders ?? createStakeholderGroup(),
          req.body.stakeholders
        );

        // Get rid of the direct update.
        delete req.body.stakeholders;
        survey.set(req.body);

        // Save will be applied in the end to survey
        return surveyManager.updateStakeholderGroup(
          survey,
          stakeholderGroupUpdate,
          req.body.evidenceRequired,
          req.body.noteRequired,
          req.body.verificationRequired,
          req.body.utrvType
        );
      })
      .then((result) => res.FromModel(result))
      .catch((e: Error) => next(e));
  });

router.route('/:id/delete').delete(deleteConfirm, async (req, res, next) => {
  try {
    const id = req.params.id;
    const survey = await Survey.findById(id).exec();
    if (!survey) {
      return res.Exception(new UserError(`Survey with id '${id}' was not found `));
    }

    await deleteManager.deleteSurvey(survey, req.user);
    return res.Success(`Deleted survey ${id} successfully`);
  } catch (e) {
    next(e);
  }
});

router.route('/:id/performance').get((req, res, next) => {
  Survey.findById(req.params.id)
    .orFail()
    .exec()
    .then((survey) => SurveyProcessRepository.loadSurveyUtrvs(survey))
    .then((r) => res.FromModel(r))
    .catch((e) => next(e));
});

router.route('/:id/view').get((req, res, next) => {
  const { exclude, extended, groups } = req.query;
  const excludeData: string[] = typeof exclude === 'string' ? exclude.split(',') : [];

  SurveyImporter.buildSurveyView(req.params.id, {
    extended: Boolean(extended),
    groups: Boolean(groups),
    exclude: excludeData,
  })
    .then((result) => res.FromModel(result))
    .catch((e: Error) => next(e));
});

router.route('/:id/action').put(bodyParamIdCheck, (req, res, next) => {
  Survey.findById(req.params.id)
    .exec()
    .then(async (survey) => {
      if (!survey) {
        throw new Error('Survey not found');
      }
      if (MaterialityAssessmentManager.isMaterialityAssessmentBlueprint(survey.sourceName as Blueprints)) {
        const materialityAssessmentManager = getMaterialityAssessmentManager();
        return materialityAssessmentManager.processAction(survey, req.user, req.body.action);
      }

      if (isAggregatedSurvey(survey.type)) {
        await surveyManager.regenerateBlueprint(survey, req.user);
        return InitiativeRepository.mustFindById(survey.initiativeId, { _everything: -1 })
          .then((initiative) => {
            const surveyAggregator = getSurveyAggregator();
            return surveyAggregator.updateAggregatedSurvey(survey, initiative, req.user)
        });
      }

      return surveyManager.processAction(survey, req.user, req.body.action);
    })
    .then((result) => res.FromModel(result))
    .catch((e: Error) => next(e));
});

module.exports = router;
