import express from 'express';
import { AuthRouter } from '../../http/AuthRouter';
import { getArchivedInitiativeManager } from '../../service/initiative/ArchivedInitiativeManager';
import { getInitiativeTreeService } from '../../service/initiative/InitiativeTreeService';

const router = express.Router() as AuthRouter;
const archivedInitiativeManager = getArchivedInitiativeManager();
const initiativeTreeService = getInitiativeTreeService();

router.route('/companies/:initiativeId').get(async (req, res, next) => {
  const { initiativeId } = req.params;
  const isActive = await archivedInitiativeManager.hasActiveStatus(initiativeId);

  return initiativeTreeService
    .getAllChildren({ initiativeId, isActive })
    .then(async (result) => {
      return res.FromModel(result);
    })
    .catch((e) => next(e));
});

module.exports = router;
