/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { AuthRouter } from '../../http/AuthRouter';
import { AdminAuditLogs } from '../../middleware/audit/adminAudit';
import { StaffRoleToScopesMap, StaffScope } from '../../models/staffRole';
import { requireStaffScopes } from '../../middleware/staffRoleMiddlewares';
import { getStaffRoleManager } from '../../service/user/StaffRoleManager';

const router = express.Router() as AuthRouter;
router.use(AdminAuditLogs({ service: 'user' }));
const staffRoleManager = getStaffRoleManager();

router.route('/staff-roles').get((req, res) => {
  res.FromModel(StaffRoleToScopesMap);
});

router.route('/:id/staff-roles')
  .put(requireStaffScopes([StaffScope.UserWrite]), async (req, res, next) => {
    try {
      const user = staffRoleManager.updateStaffRoles({ userId: req.params.id, data: req.body });
      res.FromModel(user);
    } catch (e) {
      next(e);
    }
  });

module.exports = router;
