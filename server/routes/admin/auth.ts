/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */
import express from 'express';
import { verifyAuthHeader } from '../../service/authService';
import BadRequestError from '../../error/BadRequestError';
import { getUserProvisioning } from '../../service/user/UserProvisioning';

const router = express.Router();

router.post('/provision', (req, res) => {
  verifyAuthHeader(req.headers.authorization).then(async (jwt) => {
    const subject = jwt.claims.sub;
    if (req.body.email !== subject) {
      throw new BadRequestError('User email do not match authenticated email')
    }
    const userProvisioning = getUserProvisioning();
    const newUser = await userProvisioning.provisionExternalUser(jwt)
    res.FromModel(newUser.getSafeUser())

  }).catch((err: Error) => res.Exception(err));
});

module.exports = router;
