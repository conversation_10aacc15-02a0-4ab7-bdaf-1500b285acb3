/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import Organization from '../../models/organization';
import deleteConfirm from '../../middleware/deleteConfirm';
import { mustValidate } from '../../util/validation';
import { createSchema } from '../validation-schemas/assurance-tracker';
import { getOrganizationManager } from '../../service/assurance/OrganizationManager';

const router = express.Router();

router
  .route('/')
  .get(function (req, res, next) {
    Organization.find()
      .sort({ name: 'asc' })
      .exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => next(e));
  })
  .post(async (req, res, next) => {
    try {
      if (req.body._id) {
        return res.Invalid('Not allowed to specify _id on create');
      }
      const createData = mustValidate(req.body, createSchema);
      const result = await getOrganizationManager().create(createData);
      res.FromModel(result);
    } catch (e) {
      next(e);
    }
  });

router.route('/code/:code')
  .get(function (req, res, next) {
    Organization.findOne({ 'code': req.params.code }).exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => next(e));
  });

router.route('/orphan')
  .get(function (req, res, next) {
    Organization.find({ parentId: { $exists : false } })
      .sort({ name: 'asc' }).exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => next(e));
  });

router.route('/:id/children')
  .get(function (req, res, next) {
    Organization.find({ parentId: req.params.id })
      .sort({ name: 'asc' }).exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => next(e));
  });

router.route('/:id')
  .get(function (req, res, next) {
    Organization.findById(req.params.id).exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => next(e));
  })
  .delete(deleteConfirm, function (req, res, next) {
    Organization.findByIdAndDelete(req.params.id).exec()
      .then(() => res.Success('Object Deleted'))
      .catch((e: Error) => next(e));
  })
  .put(function (req, res, next) {
    if (!req.body._id || req.params.id !== req.body._id) {
      return res.Invalid('POST url id (' + req.params.id + ') does not match POST body _id (' + req.body._id + ')');
    }

    Organization.findById(req.params.id).orFail().exec()
      .then((obj) => {
        obj.set(req.body);
        return obj.save();
      })
      .then(() => res.Success('Successfully updated document with _id=' + req.params.id))
      .catch((e: Error) => next(e));
  });

module.exports = router;
