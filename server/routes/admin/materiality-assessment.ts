/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { write } from '@sheet/core';
import express from 'express';
import { AuthRouter } from '../../http/AuthRouter';
import { setXlsxFileHeaders } from '../../http/FileDownload';
import { getMaterialityAssessmentReportService } from '../../service/materiality-assessment/MaterialityAssessmentReportService';
import { FileParserType } from '../../service/survey/transfer/parserTypes';
import { AssessmentResultType } from '../../service/materiality-assessment/types';
import { ObjectId } from 'bson';

const router = express.Router() as AuthRouter;
const materialityAssessmentReportService = getMaterialityAssessmentReportService();

Object.values(AssessmentResultType).forEach((type) => {

router
  .route(
    `/initiatives/:initiativeId/surveys/:surveyId/download/${type}`
  )
  .get(async (req, res, next) => {
    try {
      const { initiativeId, surveyId } = req.params;
      const exportType = FileParserType.Xlsx;
      const { workbook, filename } = await materialityAssessmentReportService.downloadAssessmentResults({
        initiativeId,
        surveyId: new ObjectId(surveyId),
        type,
      });
      setXlsxFileHeaders(res, `${filename}.${exportType}`);

      return res.send(write(workbook, { type: 'buffer', bookType: exportType, cellStyles: true }));
    } catch (e) {
      next(e);
    }
  });
});

module.exports = router;
