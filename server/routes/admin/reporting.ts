/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import express from 'express';
import { getSuccessReportDownload } from '../../service/staff-reporting/SuccessReportDownload';
import { AuthRouter } from '../../http/AuthRouter';
import { TaskType } from '../../models/backgroundJob';
import { mustValidate } from '../../util/validation';
import { getSuccessReportDtoSchema } from '../validation-schemas/success-report';

const successReportDownload = getSuccessReportDownload();
const router = express.Router() as AuthRouter;

router.route('/success-report').get(async (req, res, next) => {
  try {
    const latestJob = await successReportDownload.getLatestReportJob();
    if (!latestJob) {
      res.FromModel({});
      return;
    }

    res.FromModel({
      surveyEngagementCompletedDate: latestJob.tasks.find(
        (task) => task.type === TaskType.GenerateSurveyEngagementReport
      )?.completedDate,
      companyUpgradesCompletedDate: latestJob.tasks.find((task) => task.type === TaskType.GenerateCompanyUpgradesReport)
        ?.completedDate,
    });
  } catch (error) {
    next(error);
  }
});

router.route(`/success-report/download/:type`).get(async (req, res, next) => {
  try {
    const { type } = mustValidate(req.params, getSuccessReportDtoSchema);

    const latestJob = await successReportDownload.getLatestReportJob();

    if (!latestJob) {
      res.FromModel({ downloadUrl: '' });
      return;
    }

    const downloadUrl = await successReportDownload.getDownloadUrl(latestJob, type);

    res.FromModel({ downloadUrl });
  } catch (e) {
    next(e);
  }
});

module.exports = router;
