/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import ValueList, { ValueListModel } from '../../models/valueList';
import deleteConfirm from '../../middleware/deleteConfirm';
import { bodyParamIdCheck } from '../../middleware/commonMiddlewares';
const router = express.Router();

router.route('/')
  .get((req, res) => {
    ValueList.find().exec()
      .then((model: ValueListModel[]) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  })
  .post((req, res) => {
    if (req.body._id) {
      return res.Invalid('Not allowed to specify _id on create');
    }
    const model = new ValueList(req.body);
    model.save()
      .then(() => res.Success())
      .catch((e: Error) => res.Exception(e));
  });

router.route('/codes').post((req, res, next) => {
  if (!req.body?.length) {
    return res.FromModel([]);
  }
  ValueList.find({ code: { $in: req.body } }, '_id code')
    .exec()
    .then((model) => res.FromModel(model))
    .catch((e: Error) => next(e));
});

router.route('/code/:code')
    .get((req, res) => {
        ValueList.findOne({ 'code': req.params.code }).exec()
            .then((model) => res.FromModel(model))
            .catch((e: Error) => res.Exception(e));
    });

router.route('/:id')
    .get((req, res) => {
        ValueList.findById(req.params.id).exec()
            .then((model) => res.FromModel(model))
            .catch((e: Error) => res.Exception(e));
    })
    .delete(deleteConfirm, function (req, res) {
        ValueList.findByIdAndDelete(req.params.id).exec()
            .then(() => res.Success('Object Deleted'))
            .catch((e: Error) => res.Exception(e));
    })
    .put(bodyParamIdCheck, (req, res) => {
        ValueList.findById(req.params.id).orFail().exec()
        .then((obj) => {
            obj.set(req.body);
            return obj.save();
        })
        .then(() => res.Success('Successfully updated document with _id=' + req.params.id))
        .catch((e: Error) => res.Exception(e));
    });


module.exports = router;
