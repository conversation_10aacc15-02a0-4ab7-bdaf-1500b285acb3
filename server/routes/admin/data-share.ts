/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import express from 'express';
import DataShare, { DataShareCreate } from '../../models/dataShare';
import { getRequesterService } from '../../service/share/RequesterService';
import { wwgLogger } from '../../service/wwgLogger';
import deleteConfirm from '../../middleware/deleteConfirm';
import { getDataShareService } from '../../service/share/DataShareService';
const router = express.Router();

const dataShareService = getDataShareService();

router.route('/')
  .post(async (req, res, next) => {

    const data = req.body as DataShareCreate;
    const rs = getRequesterService();

    try {
      // No notification here
      const requester = await rs.getRequesterById(data);
      wwgLogger.info(`Adding data share for requester ${requester._id} ${requester.name}`, data)
      const dataShare = await dataShareService.createRequest(data);
      res.FromModel(dataShare)
    } catch (e) {
      next(e)
    }
  });


router.route('/initiative/:initiativeId')
  .get(async (req, res, next) => {
    try {
      const dataShares = await DataShare.find({
        initiativeId: req.params.initiativeId,
        deletedDate: { $exists: false },
      }).lean().exec();
      res.FromModel(dataShares)
    } catch (e) {
      next(e)
    }
  });



router.route('/:id')
  .delete(deleteConfirm, async (req, res, next) => {
    try {
      // No notification here
      const dataShare = await DataShare.findById(req.params.id).orFail().exec();
      wwgLogger.info(`Deleting data share ${dataShare._id}  for requester ${dataShare.requesterId}`, {
        dataShareId: dataShare._id.toString(),
        requesterId: dataShare.requesterId.toString(),
      })
      dataShare.deletedDate = new Date();
      await dataShare.save();
      res.FromModel(dataShare)
    } catch (e) {
      next(e)
    }
  });

module.exports = router;
