/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import UniversalTracker, { UniversalTrackerPlain } from '../../models/universalTracker';
import UniversalTrackerGroup from '../../models/universalTrackerGroup';
import Initiative from '../../models/initiative';
import deleteConfirm from '../../middleware/deleteConfirm';
import { bodyParamIdCheck } from '../../middleware/commonMiddlewares';
import { getExportExcel } from '../../service/utr/admin/Export';
import { FileParserType } from '../../service/survey/transfer/parserTypes';
import { write } from '@sheet/core';
import { setXlsxFileHeaders } from '../../http/FileDownload';
import { getValueListIds } from '../../service/utr/utrUtil';
import { ValueListRepository } from '../../repository/ValueListRepository';
import { wwgLogger } from '../../service/wwgLogger';
import { isFalsyOrEmpty } from '../../util/array';
import UniversalTrackerValue, { UniversalTrackerValuePlain } from "../../models/universalTrackerValue";
import { KeysEnum } from "../../models/public/projectionUtils";
import { RootInitiativeData } from "../../repository/InitiativeRepository";
import { ObjectId } from "bson";
import { recursiveParentLookup } from "../../repository/utrvAggregations";
import { createWithParents, InitiativeWithCombinedParents } from "../../util/tree";

const router = express.Router();

router.route('/')
  .get((req, res, next) => {
    UniversalTracker.find().sort({ name: 'asc' }).lean().exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => next(e));
  })
  .post((req, res, next) => {
    if (req.body._id) {
      return res.Invalid('Not allowed to specify _id on create');
    }
    const model = new UniversalTracker(req.body);
    model.save()
      .then((m) => res.FromModel(m))
      .catch((e: Error) => next(e));
  });

router.route('/code/:code')
  .get((req, res, next) => {
    UniversalTracker.findOne({ 'code': req.params.code }).exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => next(e));
  });

router.route('/universalTrackerGroupId/:universalTrackerGroupId')
  .get((req, res, next) => {
    UniversalTrackerGroup.findById(req.params.universalTrackerGroupId)
      .lean().orFail().exec()
      .then((universalTrackerGroup) => {
        const idArray = universalTrackerGroup.universalTrackers;
        UniversalTracker.find({ _id: { $in: idArray } })
          .sort({ name: 'asc' }).lean().exec()
          .then((model) => res.FromModel(model))
          .catch((e: Error) => next(e));
      })
      .catch((e: Error) => next(e));
  });

router.route('/download-import-sheet')
  .get(async (_req, res) => {
    setXlsxFileHeaders(res, `ImportSheet-${new Date().toISOString()}.xlsx`)
    const result = await getExportExcel().downloadImportSheet();
    res.send(write(result, { type: 'buffer', bookType: FileParserType.Xlsx }));
  });

router.route('/bulk-update').put(async (req, res, next) => {
  try {
    const utrs: UniversalTrackerPlain[] = req.body.universalTrackers;

    if (isFalsyOrEmpty(utrs)) {
      return res.FromModel([]);
    }

    wwgLogger.info('Bulk update Universal trackers', {
      user: req.user?._id,
      utrs: utrs.map((utr) => utr.code),
    });

    const valueListIds = getValueListIds(utrs);
    const valueLists = await ValueListRepository.findByIds(valueListIds);
    if (valueListIds.length > valueLists.length) {
      return res.Invalid(`Some of value lists: ${valueListIds.join(', ')} do not exist.`);
    }

    const updates = utrs.map(({ _id, ...rest }) => ({
      updateOne: {
        filter: { code: rest.code },
        update: rest,
        upsert: true,
      },
    }));

    const result = await UniversalTracker.bulkWrite(updates);
    res.FromModel(result);
  } catch (e) {
    next(e);
  }
});

router.route('/:id')
  .get((req, res, next) => {
    UniversalTracker.findById(req.params.id).lean().exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => next(e));
  })
  .delete(deleteConfirm, (req, res, next) => {
    UniversalTracker.findByIdAndDelete(req.params.id).lean().exec()
      .then(() => res.Success('Object Deleted'))
      .catch((e: Error) => next(e));
  })
  .put(bodyParamIdCheck, async (req, res, next) => {
    try {
      const obj = await UniversalTracker.findById(req.params.id).orFail().exec();
      const data = req.body;
      if (data.instructions && !data.instructionsEditorState) {
        data.instructionsEditorState = undefined;
      }
      obj.set(data);
      const changes = obj.getChanges();
      const utr = await obj.save();
      res.FromModel({
        utr,
        changes
      });
    } catch (e) {
      next(e);
    }
  });

router.route('/:id/alternatives')
  .patch((req, res, next) => {

    const alternatives = req.body.data?.alternatives;
    if (!alternatives) {
      res.Invalid('Missing alternatives data in request body')
    }

    UniversalTracker.findById(req.params.id).orFail().exec()
      .then((obj) => {
        obj.alternatives = alternatives;
        return obj.save();
      })
      .then((obj) => res.FromModel(obj))
      .catch((e: Error) => next(e));
  });

router.route('/initiativeId/:initiativeId')
  .get((req, res, next) => {
    Initiative.findById(req.params.initiativeId).orFail().exec()
      .then((initiative) => {
        const idArray = initiative.linkedUniversalTrackers.map((utr) => utr.universalTrackerId);
        return UniversalTracker.find({ _id: { $in: idArray } })
          .sort({ name: 'asc' }).lean().exec()
          .then((model) => res.FromModel(model));
      })
      .catch((e: Error) => next(e));
  });

type UtrvInfo = Pick<UniversalTrackerValuePlain,
  | '_id'
  | 'initiativeId'
  | 'effectiveDate'
  | 'period'
  | 'status'
  | 'type'
  | 'lastUpdated'
>;

interface ExpandedUtrvInfo extends UtrvInfo {
  initiativeData: InitiativeWithCombinedParents;
}

router.route('/:id/universal-tracker-values')
  .get(async (req, res, next) => {

    try {
      const utr = await UniversalTracker.findById(req.params.id).orFail().exec();

      const utrvs = await UniversalTrackerValue.find(
        { universalTrackerId: utr._id, deletedDate: { $exists: false } },
        {
          _id: 1,
          initiativeId: 1,
          period: 1,
          status: 1,
          effectiveDate: 1,
          type: 1,
          lastUpdated: 1
        } satisfies KeysEnum<UtrvInfo>)
        .lean<UtrvInfo[]>()
        .exec();

      const ids = Array.from(new Set(utrvs.map((utrv) => utrv.initiativeId.toString())));

      const project = { _id: 1, name: 1, code: 1, tags: 1, parentId: 1 }
      const pipeline = [
        { $match: { _id: { $in: [...ids].map(id => new ObjectId(id)) } } },
        recursiveParentLookup,
        { $project: { ...project, parents: project } }
      ];

      const initiatives = await Initiative.aggregate<RootInitiativeData>(pipeline).exec();
      const initiativeLookupMap = new Map<string, ReturnType<typeof createWithParents> | undefined>();
      for (const initiative of initiatives) {
        const parentMap = new Map((initiative.parents ?? []).map((i) => {
          return [i._id.toString(), { _id: i._id, name: i.name, code: i.code, parentId: i.parentId }];
        }));

        const currentInitiative = createWithParents(initiative, parentMap);
        initiativeLookupMap.set(currentInitiative._id.toHexString(), currentInitiative);
      }

      res.FromModel({
        utr,
        utrvs: utrvs.map((utrv) => {
          const initiativeData = initiativeLookupMap.get(utrv.initiativeId.toString());
          return initiativeData ? { ...utrv, initiativeData } satisfies ExpandedUtrvInfo : utrv;
        }),
      });
    } catch (e) {
      next(e);
    }
  });

module.exports = router;
