/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import Sequelize, { FindOptions } from 'sequelize';
import { ObjectId } from 'bson';
import User, { UserModel } from '../../models/user';
import UserEvent from '../../service/event/UserEvent';
import moment = require('moment');

const router = express.Router();


router.route('/limit/:limit/offset/:offset')
  .get((req, res) => {
    const createdAt = moment().utc().subtract('2', 'months').toDate();
    const options: FindOptions = {
      where : {
        createdAt: {
          [Sequelize.Op.gt]: createdAt
        },
      },
      order: [['createdAt', 'DESC']]
    };
    if (req.params.limit) {
      options.limit = Number(req.params.limit);
    }

    UserEvent.findAll(options)
      .then(async (models: any) => {

        const userIds = models.map((u: UserEvent) => new ObjectId(u.userId));
        const users = await User.find(
          { _id: { $in: userIds } },
          { email: 1, firstName: 1, surname: 1 }
        ).exec();

        return res.FromModel(models.map((e: UserEvent) => {
          const event: any = e.toJSON();
          event.user = users.find((u: UserModel) => e.userId === u._id.toString()) ;
          return event;
        }));
      })
      .catch((e: Error) => res.Exception(e));
  });

module.exports = router;
