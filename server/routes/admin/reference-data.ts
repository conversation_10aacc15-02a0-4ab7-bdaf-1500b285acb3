/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import ReferenceData from '../../models/referenceData';
import deleteConfirm from '../../middleware/deleteConfirm';
const router = express.Router();

router.route('/')
    .get(function (req, res) {
        ReferenceData.find().exec()
            .then((model) => res.FromModel(model))
            .catch((e: Error) => res.Exception(e));
    })
    .post(function (req, res) {
        if (req.body._id) {
            return res.Invalid('Not allowed to specify _id on create');
        }
        const model = new ReferenceData(req.body);
        model.save()
            .then(() => res.Success())
            .catch((e: Error) => res.Exception(e));
    });

router.route('/:id')
    .get(function (req, res) {
        ReferenceData.findById(req.params.id).exec()
            .then((model) => res.FromModel(model))
            .catch((e: Error) => res.Exception(e));
    })
    .delete(deleteConfirm, function (req, res) {
        ReferenceData.findByIdAndDelete(req.params.id).exec()
            .then(() => res.Success('Object Deleted'))
            .catch((e: Error) => res.Exception(e));
    })
    .put(function (req, res) {
        if (!req.body._id || req.params.id !== req.body._id) {
            return res.Invalid('POST url id (' + req.params.id + ') does not match POST body _id (' + req.body._id + ')');
        }

        ReferenceData.findById(req.params.id).orFail().exec()
            .then((obj) => {
                obj.set(req.body);
                return obj.save();
            })
            .then(() => res.Success('Successfully updated document with _id=' + req.params.id))
            .catch((e: Error) => res.Exception(e));
    });

module.exports = router;
