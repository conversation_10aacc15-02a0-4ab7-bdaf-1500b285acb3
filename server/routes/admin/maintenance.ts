/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import express from 'express';
import cache from '../../service/cache';
import { getMigrationService } from '../../service/migration/MigrationService';
import { getMigrationWorkflow } from '../../service/migration/MigrationWorkflow';
import { TaskType } from '../../models/backgroundJob';
import { getBackgroundJobService } from '../../service/background-process/BackgroundJobService';
import { wwgLogger } from '../../service/wwgLogger';

const router = express.Router();

router.route('/cache/flush')
  .get((req, res) => {
    cache.del('*', (err: any, keys: number) => {
      return err ? res.Invalid(err) : res.Success({ err, keys });
    });
  });

  router.route('/migration')
  .get((req, res) => {
    const migrationService = getMigrationService();
    res.FromModel(migrationService.getList());
  });

['dry-run', 'up', 'down'].forEach(action => {

router.route(`/migration/${action}/:migrationCode/:migrationAction`)
  .post(async (req, res, next) => {
    try {
      if (!req.user) {
        return res.NotPermitted();
      }

      const { migrationCode, migrationAction } = req.params;

      const getTaskType = (action: string) => {
        switch (action) {
          case 'up':
            return TaskType.MigrationScriptUp;
          case 'down':
            return TaskType.MigrationScriptDown;
          case 'dry-run':
          default:
            return TaskType.MigrationScriptUpDryRun;
        }
      }

      const workflow = getMigrationWorkflow();
      const job = await workflow.createJob(
        {
          userId: req.user.id,
          migrationCode,
          migrationAction,
        },
        getTaskType(action)
      );

      const bgJobService = getBackgroundJobService();
      bgJobService.runFromJob(job).catch((e) => wwgLogger.error(e));

      return res.FromModel({
        job
      });
    } catch (e) {
      next(e);
    }
  });
});

module.exports = router;
