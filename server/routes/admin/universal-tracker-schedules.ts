/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import UniversalTrackerSchedule from '../../models/universalTrackerSchedule';
import moment = require('moment');
import { UniversalTrackerScheduleRepository } from '../../repository/UniversalTrackerScheduleRepository';
import { AuthRouter } from '../../http/AuthRouter';
const router = express.Router() as AuthRouter;

router.route('/')
  .get((req, res) => {
    UniversalTrackerSchedule.find().exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  })
  .post((req, res) => {
    if (req.body._id) {
      return res.Invalid('Not allowed to specify _id on create');
    }
    // Assign userId
    const user = req.user;
    req.body.userId = user._id;
    const model = new UniversalTrackerSchedule(req.body);
    model.save()
      .then(() => res.Success())
      .catch((e: Error) => res.Exception(e));
  });

router.route('/code/:code')
  .get(function (req, res) {
    UniversalTrackerSchedule.findOne({ 'code': req.params.code }).exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/:id')
  .get((req, res) => {

    const find = !req.query.extended ?
      UniversalTrackerScheduleRepository.findByIdExtended(req.params.id)
      : UniversalTrackerSchedule.findById(req.params.id).lean().exec();

    find
      .then((model: any) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  })
  .put((req, res) => {
    if (!req.body._id || req.params.id !== req.body._id) {
      return res.Invalid('POST url id (' + req.params.id + ') does not match POST body _id (' + req.body._id + ')');
    }

    const user = req.user;
    UniversalTrackerSchedule.findById(req.params.id).orFail().exec()
      .then((obj) => {
        obj.set(req.body);
        if (!obj.userId) {
          obj.userId = user._id;
        }

        if (obj.isModified('cronSchedule')) {
          obj.nextRunDate = moment().endOf('day').toDate();
        }

        return obj.save();
      })
      .then(() => res.Success('Successfully updated document with _id=' + req.params.id))
      .catch((e: Error) => res.Exception(e));
  });

module.exports = router;
