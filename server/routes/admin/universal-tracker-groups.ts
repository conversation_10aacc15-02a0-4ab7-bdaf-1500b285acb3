/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import UniversalTrackerGroup from '../../models/universalTrackerGroup';
import deleteConfirm from '../../middleware/deleteConfirm';
const router = express.Router();

router.route('/')
    .get(function (req, res) {
        UniversalTrackerGroup.find().exec()
            .then((model) => res.FromModel(model))
            .catch((e: Error) => res.Exception(e));
    })
    .post(function (req, res) {
        if (req.body._id) {
            return res.Invalid('Not allowed to specify _id on create');
        }
        const model = new UniversalTrackerGroup(req.body);
        model.save()
            .then(() => res.Success())
            .catch((e: Error) => res.Exception(e));
    });

router.route('/:id')
    .get(function (req, res) {
        UniversalTrackerGroup.findById(req.params.id).exec()
            .then((model) => res.FromModel(model))
            .catch((e: Error) => res.Exception(e));
    })
    .delete(deleteConfirm, function (req, res) {
        UniversalTrackerGroup.findByIdAndDelete(req.params.id).exec()
            .then(() => res.Success('Object Deleted'))
            .catch((e: Error) => res.Exception(e));
    })
    .put(function (req, res) {
        if (!req.body._id || req.params.id !== req.body._id) {
            return res.Invalid('POST url id (' + req.params.id + ') does not match POST body _id (' + req.body._id + ')');
        }

        UniversalTrackerGroup.findById(req.params.id).orFail().exec()
            .then((obj) => {
                obj.set(req.body);
                return obj.save();
            })
            .then(() => res.Success('Successfully updated document with _id=' + req.params.id))
            .catch((e: Error) => res.Exception(e));
    });

module.exports = router;
