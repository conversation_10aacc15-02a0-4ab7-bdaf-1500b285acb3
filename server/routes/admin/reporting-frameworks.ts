/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import deleteConfirm from '../../middleware/deleteConfirm';
import ReportingFramework from '../../models/reportingFramework';
import { bodyParamIdCheck } from '../../middleware/commonMiddlewares';
import { ReportingFrameworkManager } from '../../service/reporting/ReportingFrameworkManager';
const router = express.Router();

router.route('/')
  .get((req, res) => {
    ReportingFramework.find().exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/import')
  .post((req, res) => {
    if (req.body._id) {
      return res.Invalid('Not allowed to specify _id on create');
    }

    ReportingFrameworkManager.create(req.body)
      .then(() => res.Success())
      .catch((e: Error) => res.Exception(e));
  });

router.route('/code/:code')
  .get(function (req, res) {
    ReportingFramework.findOne({ 'code': req.params.code }).exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/:id')
  .get((req, res) => {
    ReportingFramework.findById(req.params.id).exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  })
  .delete(deleteConfirm, (req, res) => {
    ReportingFramework.findByIdAndDelete(req.params.id).exec()
      .then(() => res.Success('Object Deleted'))
      .catch((e: Error) => res.Exception(e));
  })
  .put(bodyParamIdCheck, (req, res) => {
    ReportingFrameworkManager.update(req.params.id, req.body)
      .then((o) => res.FromModel(o))
      .catch((e: Error) => res.Exception(e));
  });

module.exports = router;
