/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import UserToken from '../models/userToken';
import { UserErrorMessages } from '../error/ErrorMessages';
import User from '../models/user';
import BadRequestError from '../error/BadRequestError';
import { getIdpManager } from '../service/authentication/IdpManager';
import { getSubdomain } from '../util/url';

const router = express.Router();

router.get('/sso{/:orgCode}', (req, res, next) => {
  getIdpManager().getIdpConfig({
    orgCode: req.params.orgCode,
    domain: getSubdomain(req.header('origin')),
  }).then((configs) => res.FromModel(configs))
    .catch((err: Error) => next(err));
});

router.route('/activation-token{/:token}')
  .get(async (req, res) => {
    try {
      const token = req.params.token;
      if (!token) {
        return res.Exception(new BadRequestError(UserErrorMessages.ActivationTokenNotValid));
      }
      const userToken = await UserToken.findOne({ token }).lean().exec();
      if (!userToken) {
        return res.Exception(new BadRequestError(UserErrorMessages.ActivationTokenNotValid))
      }

      const user = await User.findById(userToken.userId).lean().exec();
      res.FromModel({
        ...userToken,
        hasPassword: Boolean(user?.passwordHash)
      });
    } catch (e) {
      res.Exception(e);
    }
  });

// This is for migrating users to Okta.
// Eventually we should delete and trigger a force password reset for users that have not completed the migration
router.route('/okta-verify')
  .post((req, res, next) => {

    const getResponse = (status: string) => ({
      commands: [{
        type: "com.okta.action.update",
        value: {
          credential: status
        }
      }]
    });

    const username = req.body.data?.context?.credential?.username;
    const password = req.body.data?.context?.credential?.password;
    if (!username || !password) {
      res.json(getResponse("UNVERIFIED"));
      return;
    }

    res.json(getResponse("VERIFIED"))
  });

module.exports = router;
