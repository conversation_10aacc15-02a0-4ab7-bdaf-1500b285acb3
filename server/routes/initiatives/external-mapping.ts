/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import express from 'express';
import { AuthRouter } from '../../http/AuthRouter';
import UtrExternalMapping from '../../models/utrExternalMapping';
import { getReportingFactory } from '../../service/reporting/ReportingFactory';
import { ExternalDefinitionItem, getMappedName } from '../../service/reporting/external-mapping-conversions';
import { ReportDocumentType } from '../../models/reportDocument';

const router = express.Router({ mergeParams: true }) as AuthRouter;

Object.values(ReportDocumentType).forEach((type) => {
  router.route(`/${type}`).get((req, res, next) => {
    UtrExternalMapping.find({ type })
      .lean()
      .exec()
      .then((documents) => {
        const mappingMap = new Map(documents.map((doc) => [doc.code, doc]));
        const reportDefinition = getReportingFactory().getReportDefinition(type);
        const entries = Object.entries(reportDefinition.getDefinition());

        const allMappings = entries.reduce((acc, [tagName, definition]) => {
          if (definition) {
            const mapping = mappingMap.get(tagName);
            acc.push({
              type,
              mappingCode: tagName,
              name: getMappedName({ factName: tagName }),
              references: definition?.references,
              utrs: mapping?.utrs ?? [],
            });
          }
          return acc;
        }, [] as ExternalDefinitionItem[]);
        res.FromModel({ mappings: allMappings });
      })
      .catch((err) => {
        next(err);
      });
  });
});

module.exports = router;
