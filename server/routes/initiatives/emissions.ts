/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import express from 'express';
import { AuthRouter } from '../../http/AuthRouter';
import { getEmissionPartnerRepository } from "../../service/emission-calculator/EmissionPartnerRepository";
import { checkIsStaff } from "../../middleware/userMiddlewares";

const router = express.Router({ mergeParams: true }) as AuthRouter;

router.route('/:code')
  .get(checkIsStaff, ( req, res, next) => {
    getEmissionPartnerRepository()
      .getPartnerInfo(req.params.code, req.user)
      .then((calculators) => {
        res.FromModel(calculators);
      }).catch(next)
  });


module.exports = router;
