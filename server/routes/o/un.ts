/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import sdgMap from '../../static/un-sdg';
const router = express.Router();

const getSDGMap = () => {
  const map: any = {
    goals: []
  };

  sdgMap.map((goal) => {
    if (!goal.targets) {
      return;
    }

    const goalObj: any = {
      code: goal.code,
      title: goal.title,
      description: goal.description
    };

    goalObj.targets = [];

    goal.targets.map((target) => {
      const targetObj: any = {
        code: target.code,
        title: target.title,
        description: target.description
      };
      goalObj.targets.push(targetObj);
    });

    map.goals.push(goalObj);
  });

  return map;
};

router.route('/sdg')
  .get(function (req, res) {
    return res.FromModel(getSDGMap());
  });

module.exports = router;
