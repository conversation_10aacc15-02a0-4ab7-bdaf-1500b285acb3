/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { simpleCountryMap } from '../../service/location/CountryService';
import { OrganizationRepository } from '../../repository/OrganizationRepository';
import { ICB2019 } from '../../service/reporting/framework/ICB2019';
import { getSGXIssuerService } from '../../service/sgx/SGXIssuerService';
import { getReferralStorage } from "../../service/referral/ReferralStorage";

const router = express.Router();

router.route('/all')
  .get((req, res) => res.FromModel({ countries: simpleCountryMap }));

router.route('/country/list')
  .get((req, res) => res.FromModel(simpleCountryMap));

router.route('/organization/search')
  .get((req, res) => {
    OrganizationRepository.search(req.query.q as string)
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/issuers/:code')
  .get((req, res, next) => {
    if (req.params.code !== 'SGX4SGX') { // Future thinking about multiple exchanges
      getReferralStorage()
        .isReferralCustomCompanyEnabled(req.params.code)
        .then((isEnabled) => {
          return res.FromModel({ issuers: [], customCompanyEnabled: isEnabled });
        }).catch(next)
      return;
    }

    getSGXIssuerService().getSGXIssuersWithInitiativeId()
      .then((issuers) => res.FromModel({
        issuers: issuers
          .map(issuer => ({
            _id: issuer._id,
            name: issuer.issuerName?.name,
            initiativeId: issuer.initiativeId
          }))
      }
      ))
      .catch(next);
  });

router.route('/issuers/:code/:issuerId')
  .get((req, res) => {
    const issuerId = req.params.issuerId
    if (!issuerId || req.params.code !== 'SGX4SGX') { // Future thinking about multiple exchanges
      return res.FromModel([]);
    }
    const sgxIssuerService = getSGXIssuerService();
    sgxIssuerService.getSGXIssuerWithInitiativeId(issuerId)
      .then((issuer) => res.FromModel({
        _id: issuer._id,
        name: issuer.issuerName?.name,
        initiativeId: issuer.initiativeId
      }))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/industry-sectors')
  .get((req, res) => {
    const filteredKeys = Object.keys(ICB2019)
      .sort((a, b) => {
        const aa = ICB2019[a];
        const bb = ICB2019[b];
        if (aa.name > bb.name) {
          return 1;
        }
        if (aa.name < bb.name) {
          return -1;
        }
        return 0;
      });

    const filteredList: any[] = [];
    filteredKeys.forEach(code => filteredList.push({ ...ICB2019[code], code }));
    return res.FromModel(filteredList);
  });

module.exports = router;
