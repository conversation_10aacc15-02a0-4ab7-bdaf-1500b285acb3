/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import express from 'express';
import { getEmissionPartnerRepository } from "../../service/emission-calculator/EmissionPartnerRepository";
import { optionalAuth } from "../../service/authService";

const router = express.Router();

router.route('/')
  .get(optionalAuth, (req, res, next) => {
    getEmissionPartnerRepository()
      .getCalculators(req.user)
      .then((calculators) => {
        res.FromModel(calculators);
      }).catch(next)
  });

module.exports = router;
