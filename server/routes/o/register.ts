/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import axios from 'axios';
import express from 'express';
import { wwgLogger } from '../../service/wwgLogger';
import config from '../../config';
import qs from 'qs';
import { getEmailService } from '../../service/email/EmailService';
import { z } from 'zod';
import { mustValidate } from '../../util/validation';
import { VALID_DOMAIN_REGEX } from '../../util/url';

const router = express.Router();

interface RegisterInterestData {
  oid: string;
  salutation?: string;
  first_name?: string;
  last_name?: string;
  email: string;
  title?: string;
  company?: string;
  message?: string;
  whereDidYouHear?: string;
  SGX_Lead__c?: boolean;
  interestedIn?: string;
  productCode?: string;
  siteSource?: string;
}

const { leadUrl, leadOid, messageField, interestedInField, whereDid<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, siteSourceField } =
  config.crm.salesforce;

const registerInterestDataDto = z.object({
  oid: z.literal(leadOid, { message: 'Invalid oid' }),
  email: z.string().email(),
  salutation: z.string().optional(),
  first_name: z.string().optional(),
  last_name: z.string().optional(),
  title: z.string().optional(),
  company: z.string().optional(),
  message: z.string().optional(),
  whereDidYouHear: z.string().optional(),
  SGX_Lead__c: z.boolean().optional(),
  interestedIn: z.string().optional(),
  productCode: z.string().optional(),
  siteSource: z.string().regex(VALID_DOMAIN_REGEX, 'Invalid domain').optional(),
});

router.route('/interest').post((req, res, next) => {
  const data = mustValidate(req.body, registerInterestDataDto) satisfies RegisterInterestData;
  const user = req.user;

  const { message, interestedIn, whereDidYouHear, siteSource, ...rest } = data;

  const formData = qs.stringify({
    ...rest,
    [messageField]: message ?? '',
    [interestedInField]: interestedIn,
    [whereDidYouHearField]: whereDidYouHear,
    [siteSourceField]: siteSource,
  });

  const emailService = getEmailService();
  const toEmail = config.email.leadsEmail;
  const leadsEmailMessage = emailService
    .getNewMessageInstance()
    .addTo(toEmail, 'G17Eco leads group')
    .setSubject('New website lead').setHtml(`<body>
        <h3>New G17Eco website lead form submission.</h3>
        <table border='1' width='100%'>
          <tr><td>Field</td><td>Response</td></tr>
          <tr><td>OID</td><td>${data.oid ?? '-'}</td></tr>
          <tr><td>Salutation</td><td>${data.salutation ?? '-'}</td></tr>
          <tr><td>First Name</td><td>${data.first_name ?? '-'}</td></tr>
          <tr><td>Last Name</td><td>${data.last_name ?? '-'}</td></tr>
          <tr><td>Email</td><td>${data.email ?? '-'}</td></tr>
          <tr><td>Job Title</td><td>${data.title ?? '-'}</td></tr>
          <tr><td>Company</td><td>${data.company ?? '-'}</td></tr>
          <tr><td>Where did they hear about us?</td><td>${data.whereDidYouHear ?? '-'}</td></tr>
          <tr><td>Message</td><td>${data.message ?? '-'}</td></tr>
          <tr><td>Interested In</td><td>${data.interestedIn ?? '-'}</td></tr>
          <tr><td>Product Code</td><td>${data.productCode ?? '-'}</td></tr>
          <tr><td>Site Source</td><td>${data.siteSource ?? '-'}</td></tr>
          <tr><td>Logged in as:</td><td>${
            user ? `${user._id} - ${user.firstName} ${user.surname} (${user.email})` : '-'
          }</td></tr>
        </table>
        </body>`);

  emailService
    .send(leadsEmailMessage)
    .then(() => {
      wwgLogger.info(`Successfully post data to ${toEmail} email from user ${data.email}`, {
        userId: user?._id.toString(),
        productCode: data.productCode,
      });
    })
    .catch((e: Error) => {
      wwgLogger.error('Failed to process send leads email', {
        userId: user?._id.toString(),
        productCode: data.productCode,
        cause: e,
      });
    });

  axios({
    method: 'post',
    url: leadUrl,
    data: formData,
    headers: {
      'content-type': 'application/x-www-form-urlencoded;charset=utf-8',
    },
  })
    .then((r) => {
      const processedHeader = r.headers['is-processed'];
      if (typeof processedHeader === 'string' && processedHeader.toLowerCase().includes('exception')) {
        wwgLogger.error(`Failed to process send request to salesforce, header: ${processedHeader}`);
        return res.Invalid(`Failed to process the request`);
      }

      wwgLogger.info(`Successfully post data to salesforce for ${data.email}`);

      return res.Success('Success');
    })
    .catch((e) => next(e));
});

module.exports = router;
