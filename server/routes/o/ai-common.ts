/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { ObjectId } from 'bson';
import { getAiService } from '../../service/ai/service';

const router = express.Router();
const aiService = getAiService();

/** @deprecated This endpoint is no longer in use. Replace with `/utrv-assistant/:utrvId` instead. */
router.route('/generate-draft/input-text')
  .post(async (req, res, next) => {
    if (!req.user || !req.body.utrId) {
      return res.NotPermitted();
    }

    try {
      const { utrId } = req.body;
      const response = await aiService.getInputTextDraft(new ObjectId(utrId), req.user._id);
      res.FromModel(response);
    } catch(e) {
      next(e);
    }
  });

module.exports = router;
