/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import InitiativeGroup from '../models/initiativeGroup';
const router = express.Router();

router.route('/:groupId')
  .get(function (req, res) {
    InitiativeGroup.findById(req.params.groupId).exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  });

module.exports = router;
