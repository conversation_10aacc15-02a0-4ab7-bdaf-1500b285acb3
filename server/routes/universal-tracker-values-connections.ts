import express from 'express';
import { ObjectId } from 'bson';
import { AuthRouter } from '../http/AuthRouter';
import { getUtrvConnectionsService } from '../service/utr/connection/UtrvConnectionsService';

const router = express.Router({ mergeParams: true }) as AuthRouter;

const utrvConnectionsService = getUtrvConnectionsService();

router.route('/').get(async (req, res, next) => {
  try {
    const result = await utrvConnectionsService.getUtrvConnections(new ObjectId(req.params.utrvId));

    return res.FromModel(result);
  } catch (e) {
    next(e);
  }
});

module.exports = router;
