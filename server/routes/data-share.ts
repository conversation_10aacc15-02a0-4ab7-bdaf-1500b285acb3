/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import express, { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { AuthenticatedRequest, AuthRouter } from '../http/AuthRouter';
import { getDataShareService } from '../service/share/DataShareService';
import DataShare, {
  DataScopeAccess,
  DataShareCreate,
  DataShareModel,
  DataShareScopeView,
  DataShareUpdate,
  getShareStatus,
  RequesterType
} from '../models/dataShare';
import { InitiativeAudit } from '../service/audit/events/Initiative';
import { wwgLogger } from '../service/wwgLogger';
import { getAuditLogger } from '../service/audit/AuditLogger';
import PermissionDeniedError from '../error/PermissionDeniedError';
import { DataShareMin, getDataShareRepository } from '../repository/DataShareRepository';
import { clearCache } from '../service/cache';
import { DataSharePermissions } from '../service/share/DataSharePermissions';
import Initiative from '../models/initiative';
import Survey, {
  SurveyInitiative,
  surveyInitiativeMinProjection,
  surveyInitiativeProjection,
  SurveyListItem,
  SurveyModelPlain,
  SurveyModelPlainWithInitiative,
  SurveyWithInitiative
} from '../models/survey';
import { getCsvName } from '../service/assurance/csvContext';
import { activeBlueprints, Blueprints } from '../survey/blueprints';
import { initiativeMinFields } from '../repository/projections';
import { getRequesterService } from '../service/share/RequesterService';
import { InitiativeMin } from '../models/public/initiativeType';
import { UserPlain } from '../models/user';
import { Actor } from '../service/audit/AuditModels';
import { ContextMiddleware } from '../middleware/audit/contextMiddleware';
import { createDataScopeAccess, extractCustomIds, validateScope } from '../service/share/dataShareUtil';
import { SurveyRepository } from '../repository/SurveyRepository';
import { ObjectId } from "bson";
import { ActionList } from "../service/utr/constants";
import { VisibilityStatus } from "../service/survey/scope/visibilityStatus";
import { simpleDownloadHandler } from "./handlers/download-handlers";
import { DataShareErrorMessages } from "../error/ErrorMessages";
import MetricGroup from "../models/metricGroup";
import { InitiativeRepository } from "../repository/InitiativeRepository";
import { getBluePrintContribution } from '../service/survey/BlueprintContribution';
import { UserRepository } from '../repository/UserRepository';
import { UserRoles } from '../service/user/userPermissions';
import { getNotificationManager } from '../service/notification/NotificationManager';
import { MessageType } from '../service/notification/types';
import { getReportService } from "../service/survey/ReportData";
import { ReportGenerator } from "../service/scorecard/ReportGenerator";
import { KeysEnum } from "../models/public/projectionUtils";
import { getScorecardManager } from '../service/scorecard/ScorecardManager';
import { mustValidate } from '../util/validation';
import { requesterDtoSchema, extendedRequesterDtoSchema, optionalExtendedRequesterDtoSchema } from './validation-schemas/portfolio-tracker';

const router = express.Router() as AuthRouter;

router.use(ContextMiddleware)

const dataShareService = getDataShareService();
const dataShareRepo = getDataShareRepository();
const requesterService = getRequesterService();
const auditLogger = getAuditLogger();
const bc = getBluePrintContribution();

interface DataShareWithInitiative extends DataShareModel {
  initiative: InitiativeMin;
}

export const hasSdgFullAccess: RequestHandler = (req, res, next) => {
  if (!req.user) {
    return next(new PermissionDeniedError())
  }
  const { requesterType, requesterId, initiativeId } = req.params;
  DataSharePermissions.getDataScopeAccess({
    requesterId,
    requesterType: requesterType as RequesterType,
    initiativeId,
    user: req.user,
  }).then(dataScope => {
    if (dataScope.access !== DataScopeAccess.Full) {
      return next(new PermissionDeniedError('You do not have permissions to SDG data'));
    }
    next();
  }).catch(e => next(e));
};

const getDataShareToManage = async (id: string, user: UserPlain) => {
  const dataShare = await DataShare.findById(id)
    .populate({ path: 'initiative', select: initiativeMinFields })
    .orFail().exec();

  if (!await DataSharePermissions.canManage(user, dataShare)) {
    throw new PermissionDeniedError();
  }
  const shareInitiative = dataShare.initiative;
  if (!shareInitiative) {
    throw new Error(`DataShare initiative not found ${dataShare.initiativeId}`);
  }

  return dataShare as DataShareWithInitiative
}

function getShareTarget(dataShare: DataShareModel): Actor {
  return { id: dataShare._id, type: 'DataShare' };
}

router.route('/')
  .post(async (req, res, next) => {
    try {
      const data = req.body as DataShareCreate;
      if (!await DataSharePermissions.canRequest(req.user, data)) {
        return next(new PermissionDeniedError());
      }
      // Only forward expected types
      const dataShare = await dataShareService.createRequest({
        title: data.title,
        content: data.content,
        initiativeId: data.initiativeId,
        requesterId: data.requesterId,
        requesterType: data.requesterType,
        dataScope: data.dataScope,
      });

      const shareTarget = getShareTarget(dataShare);
      const requester = await requesterService.getRequesterById(dataShare)
      const initiative = await Initiative.findById(dataShare.initiativeId).orFail().lean().exec();

      // Requester (Portfolio) entry both dataShare and source initiative
      auditLogger.fromRequest(req, {
        initiativeId: await requesterService.getRequesterInitiativeId(dataShare),
        auditEvent: InitiativeAudit.dataShareRequest,
        targets: [shareTarget, auditLogger.initiativeTarget(initiative)],
      }).catch(wwgLogger.error);

      // Source Initiative (Company)
      auditLogger.createSystem({
        req: req,
        initiativeId: dataShare.initiativeId,
        actor: {
          id: requester._id,
          type: requesterService.toActorType(requester.type),
          alternateId: requester.code,
          displayName: requester.name,
        },
        auditEvent: InitiativeAudit.dataShareRequest,
        targets: [shareTarget]
      }).catch(wwgLogger.error);

      clearCache();

      res.FromModel({
        _id: dataShare._id,
        initiativeId: dataShare.initiativeId,
        status: getShareStatus(dataShare),
      } as DataShareMin);
    } catch (e) {
      next(e);
    }
  })

router.route('/:id')
  .patch(async (req, res, next) => {
    const data = req.body as DataShareUpdate['dataScope'];
    try {
      // throws PermissionDeniedError
      const dataShare = await getDataShareToManage(req.params.id, req.user);
      await dataShareService.updateRequest(dataShare, data);
      const shareTarget = getShareTarget(dataShare);

      auditLogger.fromRequest(req as AuthenticatedRequest, {
        initiativeId: dataShare.initiativeId,
        auditEvent: InitiativeAudit.dataShareDelete,
        targets: [shareTarget],
      }).catch(wwgLogger.error);

      auditLogger.createSystem({
        req: req,
        initiativeId: await requesterService.getRequesterInitiativeId(dataShare),
        actor: auditLogger.initiativeTarget(dataShare.initiative),
        auditEvent: InitiativeAudit.dataShareUpdate,
        targets: [shareTarget]
      }).catch(wwgLogger.error);

      res.Success();
    } catch (e) {
      next(e);
    }
  })
  .delete(async (req, res, next) => {
    try {
      // throws PermissionDeniedError
      const dataShare = await getDataShareToManage(req.params.id, req.user);
      const share = await dataShareService.deleteRequest(dataShare);
      const shareTarget = getShareTarget(dataShare);

      auditLogger.fromRequest(req as AuthenticatedRequest, {
        initiativeId: dataShare.initiativeId,
        auditEvent: InitiativeAudit.dataShareDelete,
        targets: [shareTarget],
      }).catch(wwgLogger.error);

      auditLogger.createSystem({
        req: req,
        initiativeId: await requesterService.getRequesterInitiativeId(dataShare),
        actor: auditLogger.initiativeTarget(dataShare.initiative),
        auditEvent: InitiativeAudit.dataShareDelete,
        targets: [shareTarget]
      }).catch(wwgLogger.error);

      res.FromModel(share);
    } catch (e) {
      next(e);
    }
  });

router.route('/:id/accept')
  .patch(async (req, res, next) => {
    try {
      // throws PermissionDeniedError
      const dataShare = await getDataShareToManage(req.params.id, req.user);
      const share = await dataShareService.acceptRequestThenDeleteExisting(dataShare, req.body);

      const shareTarget = getShareTarget(dataShare);
      auditLogger.fromRequest(req as AuthenticatedRequest, {
        initiativeId: dataShare.initiativeId,
        auditEvent: InitiativeAudit.dataShareAccept,
        targets: [shareTarget],
      }).catch(wwgLogger.error);

      auditLogger.createSystem({
        req: req,
        initiativeId: await requesterService.getRequesterInitiativeId(dataShare),
        actor: auditLogger.initiativeTarget(dataShare.initiative),
        auditEvent: InitiativeAudit.dataShareAccept,
        targets: [shareTarget]
      }).catch(wwgLogger.error);

      res.FromModel(share);
    } catch (e) {
      next(e);
    }
  });

router.route('/:id/revoke')
  .patch(async (req, res, next) => {
    try {
      // throws PermissionDeniedError
      const dataShare = await getDataShareToManage(req.params.id, req.user);
      const share = await dataShareService.revokeRequest(dataShare);

      const shareTarget = getShareTarget(dataShare);
      auditLogger.fromRequest(req as AuthenticatedRequest, {
        initiativeId: dataShare.initiativeId,
        auditEvent: InitiativeAudit.dataShareRevoke,
        targets: [shareTarget],
      }).catch(wwgLogger.error);

      auditLogger.createSystem({
        req: req,
        initiativeId: await requesterService.getRequesterInitiativeId(dataShare),
        actor: auditLogger.initiativeTarget(dataShare.initiative),
        auditEvent: InitiativeAudit.dataShareRevoke,
        targets: [shareTarget]
      }).catch(wwgLogger.error);

      res.FromModel(share);
    } catch (e) {
      next(e);
    }
  })

router.route('/:id/undo-delete')
  .patch(async (req, res, next) => {
    try {
      const dataShare = await getDataShareToManage(req.params.id, req.user);
      const share = await dataShareService.undoDeleteRequest(dataShare);

      // Treat undo as accepted
      const shareTarget = getShareTarget(dataShare);
      auditLogger.fromRequest(req as AuthenticatedRequest, {
        initiativeId: dataShare.initiativeId,
        auditEvent: InitiativeAudit.dataShareAccept,
        targets: [shareTarget],
      }).catch(wwgLogger.error);

      auditLogger.createSystem({
        req: req,
        initiativeId: await requesterService.getRequesterInitiativeId(dataShare),
        actor: auditLogger.initiativeTarget(dataShare.initiative),
        auditEvent: InitiativeAudit.dataShareAccept,
        targets: [shareTarget]
      }).catch(wwgLogger.error);

      res.FromModel(share);
    } catch (e) {
      next(e);
    }
  })

router.route(`/requester/:requesterType/:requesterId/initiative/:initiativeId`)
  .get(async (req, res, next) => {
    try {
      const { requesterId, initiativeId, requesterType } = mustValidate(req.params, requesterDtoSchema);

      if (!await DataSharePermissions.canAccess(req.user, { requesterId, requesterType })) {
        return next(new PermissionDeniedError());
      }
      const initiative = await Initiative.findById(initiativeId, { name: 1, permissionGroup: 1, appConfigCode: 1 })
        .lean().orFail().exec();

      const dataShares = await dataShareRepo.findActiveDataShare({
        requesterId,
        requesterType,
        initiativeId,
      });

      const combinedDataScopeAccess = createDataScopeAccess({ dataShares });
      if (dataShares.length === 0) {
        return res.FromModel({ initiative, list: [], dataShares, combinedDataScopeAccess });
      }

      const projection: KeysEnum<SurveyListItem, 1> = {
        _id: 1,
        completedDate: 1,
        type: 1,
        scope: 1,
        effectiveDate: 1,
        name: 1,
        period: 1,
      };

      // This should in apply filter based on data scope but that is not implemented yet
      // Not filtering by type either, assume any completedDate survey is OK
      const surveyList = await Survey.find({
        initiativeId: initiative._id,
        completedDate: { $exists: true },
        deletedDate: { $exists: false },
        sourceName: { $in: activeBlueprints },
      }, projection)
        .sort({ effectiveDate: -1 })
        .lean<SurveyListItem[]>()
        .exec();


      const list = surveyList.map((survey: SurveyListItem) => {
        return ({
          _id: survey._id,
          name: getCsvName({ initiative, survey, _id: survey._id }),
          completedDate: survey.completedDate,
          effectiveDate: survey.effectiveDate,
          type: survey.type,
          period: survey.period,
          // Not sharing scope for security reasons
        }) satisfies Omit<SurveyListItem, 'scope'>
      });

      const customIds = extractCustomIds({
        scopes: surveyList.map(s => s.scope ?? {}),
        combinedDataScopeAccess
      });
      const metricGroups = customIds.length === 0 ? undefined :
        await MetricGroup.find({ _id: { $in: customIds } }, {
          _id: 1,
          code: 1,
          groupName: 1,
          universalTrackers: 1
        }).lean().exec();

      res.FromModel({ initiative, list, dataShares, combinedDataScopeAccess, metricGroups });
    } catch (e) {
      next(e);
    }
  });

router.route(`/requester/:requesterType/:requesterId/initiative/:initiativeId/survey/:surveyId`)
  .get(async (req, res, next) => {
    try {
      const { requesterId, initiativeId, requesterType, surveyId } = mustValidate(
        req.params,
        extendedRequesterDtoSchema
      );

      if (!(await DataSharePermissions.canAccess(req.user, { requesterId, requesterType }))) {
        return next(new PermissionDeniedError());
      }

      const dataShareScope = await DataSharePermissions.getDataScopeAccess({
        requesterId,
        requesterType,
        initiativeId,
        user: req.user,
      });

      const surveyData = await SurveyRepository.getDataShareSurveyData(
        dataShareScope,
        surveyId,
        initiativeId,
        req.header('origin')
      );


      const initiative = await Initiative.findById(initiativeId, { name: 1, permissionGroup: 1, appConfigCode: 1 })
        .lean()
        .orFail()
        .exec();

      res.FromModel({ initiative, surveyData });
    } catch (e) {
      next(e);
    }
  });

router
  .route([
    `/requester/:requesterType/:requesterId/initiative/:initiativeId/survey/:surveyId/scorecard`,
    `/requester/:requesterType/:requesterId/initiative/:initiativeId/scorecard`
  ])
  .get(hasSdgFullAccess, async (req, res, next) => {
    try {
      const { initiativeId, surveyId } = mustValidate(req.params, optionalExtendedRequesterDtoSchema);
      const query = {
        user: req.user,
        initiativeId,
        surveyId,
      };
      const data = await getScorecardManager().getScorecard(query);
      res.FromModel(data);
    } catch (error) {
      next(error);
    }
  });

['xlsx', 'csv'].forEach(type => {

router.route(`/requester/:requesterType/:requesterId/initiative/:initiativeId/survey/:surveyId/download/${type}`)
  .post(async (req, res, next) => {
    try {
      const { requesterId, initiativeId, surveyId, requesterType } = mustValidate(
        req.params,
        extendedRequesterDtoSchema
      );

      const { downloadScope } = req.body;

      const dataShareScope = await DataSharePermissions.getDataScopeAccess({
        requesterId,
        requesterType,
        initiativeId,
        user: req.user,
      });
      const { scope, access } = validateScope({ dataScope: dataShareScope, downloadScope });

      const survey = await Survey.findOne({
        _id: new ObjectId(surveyId),
        initiativeId: new ObjectId(initiativeId),
        completedDate: { $exists: true },
        deletedDate: { $exists: false },
        sourceName: { $in: activeBlueprints },
      }).populate<Pick<SurveyWithInitiative, 'initiative'>>('initiative')
        .orFail().lean<SurveyModelPlainWithInitiative>();

      return await simpleDownloadHandler({
        survey,
        downloadScope: {
          access,
          scope,
          statuses: [ActionList.Verified],
          visibilityStatus: VisibilityStatus.ExcludeData,
        },
        type,
        res,
      });
    } catch (e) {
      next(e);
    }
  });
});

router.route(`/requester/:requesterType/:requesterId/initiative/:initiativeId/survey/:surveyId/sdg/csv`)
  .get(hasSdgFullAccess, async (req, res, next) => {
    try {
      const { initiativeId, surveyId } = mustValidate(req.params, extendedRequesterDtoSchema);
      const initiative = await Initiative.findById(initiativeId).orFail().exec();
      const { data, headers } = await ReportGenerator.generate(
        initiative,
        req.user,
        new ObjectId(surveyId)
      );
      ReportGenerator.sendResponse(res, {
        data,
        headers,
        name: initiative.name
      })
    } catch (e) {
      next(e);
    }
  });

router.route(`/requester/:requesterType/:requesterId/initiative/:initiativeId/survey/:surveyId/sdg`)
  .post(hasSdgFullAccess, async (req, res, next) => {
    try {
      const { initiativeId, surveyId } = mustValidate(req.params, extendedRequesterDtoSchema);
      const survey = await Survey.findOne({
        _id: new ObjectId(surveyId),
        initiativeId: new ObjectId(initiativeId),
        completedDate: { $exists: true },
        deletedDate: { $exists: false },
        sourceName: { $in: activeBlueprints },
      }, { ...surveyInitiativeProjection, visibleUtrvs: 1, sourceName: 1, })
        .orFail().lean().exec() as SurveyInitiative & Pick<SurveyModelPlain, 'visibleUtrvs' | 'sourceName'>;

      const surveyInitiative = await Initiative.findById(initiativeId, surveyInitiativeMinProjection).orFail().exec()

      const { visibleUtrvs, ...plainSurvey } = survey; // Exclude visibleUtrvs from response

      const sdgReport = getReportService().getSDGReportData({
        survey: survey,
        downloadScope: {
          statuses: [ActionList.Verified],
          visibilityStatus: VisibilityStatus.ExcludeData,
        },
        project: { notes: 1 },
      });

      res.FromModel({
        sdgReport,
        survey: { ...plainSurvey, initiative: surveyInitiative }
      });
    } catch (e) {
      next(e);
    }
  });

router.route(`/requester/:requesterType/:requesterId/initiative/:initiativeId/survey/:surveyId/report`)
  .post(async (req, res, next) => {
    try {
      const { requesterId, initiativeId, surveyId, requesterType } = mustValidate(req.params, extendedRequesterDtoSchema);

      const { downloadScope } = req.body;

      const dataScope = await DataSharePermissions.getDataScopeAccess({
        requesterId,
        requesterType,
        initiativeId,
        user: req.user,
      });

      const survey = await Survey.findOne({
        _id: new ObjectId(surveyId),
        initiativeId: new ObjectId(initiativeId),
        completedDate: { $exists: true },
        deletedDate: { $exists: false },
        sourceName: { $in: activeBlueprints },
      }, {...surveyInitiativeProjection, visibleUtrvs: 1 })
        .orFail().lean().exec() as SurveyInitiative & Pick<SurveyModelPlain, 'visibleUtrvs'>;

      const surveyInitiative = await Initiative.findById(initiativeId, surveyInitiativeMinProjection).orFail().exec()

      const { scope, access } = validateScope({ dataScope, downloadScope });

      const { visibleUtrvs, ...plainSurvey } = survey; // Exclude visibleUtrvs from response

      const reportData = await SurveyRepository.getByDownloadMultiScope({ visibleUtrvs, initiativeId: new ObjectId(initiativeId) }, {
        access,
        scope,
        statuses: [ActionList.Verified],
        visibilityStatus: VisibilityStatus.ExcludeValuesOnly,
      });

      res.FromModel({
        reportData,
        survey: { ...plainSurvey, initiative: surveyInitiative }
      });
    } catch (e) {
      next(e);
    }
  });

router.route(`/requester/:requesterType/:requesterId/initiative/:initiativeId/survey/:surveyId/report/historical`)
  .post(async (req, res, next) => {
    try {
      const { requesterId, initiativeId, surveyId, requesterType } = mustValidate(req.params, extendedRequesterDtoSchema);

      // Used by DOCX download on PT
      const { downloadScope } = req.body;

      const dataScope = await DataSharePermissions.getDataScopeAccess({
        requesterId,
        requesterType,
        initiativeId,
        user: req.user,
      });

      const survey = await Survey.findOne({
        _id: new ObjectId(surveyId),
        initiativeId: new ObjectId(initiativeId),
        completedDate: { $exists: true },
        deletedDate: { $exists: false },
        sourceName: { $in: activeBlueprints },
      }).populate('initiative', { _id: 1, name: 1 })
        .orFail()
        .lean();

      const { scope, access } = validateScope({ dataScope, downloadScope });

      const data = await SurveyRepository.getHistoricalReportData({
        survey: survey,
        downloadScope: {
          access,
          scope,
          statuses: [ActionList.Verified],
          visibilityStatus: VisibilityStatus.ExcludeValuesOnly,
        },
        // We no longer use history for data-share related reports
        reportProject: { notes: 1 }
      });
      const [firstData] = data;

      const targets = req.body.targets ? await InitiativeRepository.getTargets({
        initiativeId: survey.initiativeId,
        // don't have universalTrackerId, use expanded property
        universalTrackerIds: firstData.reportData.map((r) => r.universalTracker?._id),
        visibility: VisibilityStatus.ExcludeData, // Exclude private targets
      }) : []

      const blueprintContributions = await bc.getContributions(survey.sourceName as Blueprints);

      res.FromModel({ historical: data, targets, survey, blueprintContributions });

    } catch (e) {
      next(e);
    }
  });

router
  .route(`/requester/:requesterType/:requesterId/initiative/:initiativeId/message`)
  .post(async (req, res, next) => {
    try {
      const { requesterId, initiativeId, requesterType } = mustValidate(req.params, requesterDtoSchema);

      if (!(await DataSharePermissions.canRequest(req.user, { requesterId, requesterType }))) {
        return next(new PermissionDeniedError());
      }

      const hasScopeViewsPermission = await DataSharePermissions.hasPermissionScopeViews({
        requesterId,
        initiativeId,
        views: [DataShareScopeView.Messaging],
      });

      if (!hasScopeViewsPermission) {
        return next(new PermissionDeniedError(DataShareErrorMessages.NoDataShare));
      }

      // Send message to owners of the initiative
      const owners = await UserRepository.findInitiativeUsers(initiativeId, UserRoles.Owner);

      const fromUserId = String(req.user._id);
      await getNotificationManager().sendUserGeneratedNotification(fromUserId, {
        ...req.body,
        type: MessageType.User,
        initiativeId,
        userIds: owners.map((o) => String(o._id)),
      });
      res.Success();
    } catch (e) {
      next(e);
    }
  });

module.exports = router;
