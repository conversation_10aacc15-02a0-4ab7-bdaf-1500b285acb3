/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import express, { RequestHandler } from 'express';
import { AuthWithOrgIdRouter } from '../http/AuthRouter';
import { AssuranceRepository } from '../repository/AssuranceRepository';
import PermissionDeniedError from '../error/PermissionDeniedError';
import UniversalTrackerActionRepository from '../repository/UniversalTrackerActionRepository';
import { combinedEvidence } from '../service/utr/utrvHistory';
import documentModel from '../models/document';
import { addDocumentUrl } from '../service/storage/fileStorage';
import FileUpload from '../http/FileUpload';
import { AssurancePermissions } from '../service/assurance/AssurancePortfolioPermissions';
import { getAssuranceManager } from '../service/assurance/AssuranceManager';
import { ContextMiddleware } from '../middleware/audit/contextMiddleware';
import { partialAssuranceDtoSchema } from './validation-schemas/assurers';
import { mustValidate } from '../util/validation';
import { canAssureMetrics, canManageAssurancePortfolio } from '../middleware/assurancePortfolioMiddlewares';
import Organization from '../models/organization';
import { organizationProjectFields } from '../repository/projections';

const router = express.Router() as AuthWithOrgIdRouter;
const assuranceManager = getAssuranceManager();

export const isAssurer: RequestHandler = async (req, res, next) => {
  const organization = await Organization.findOne(
    {
      $or: [{ _id: req.user?.organizationId }, { 'permissions.userId': req.user?._id }],
    },
    organizationProjectFields
  ).exec();

  if (!organization) {
    next(new PermissionDeniedError(`You don't have access to any assurance organization.`));
    return;
  }

  req.organization = organization;
  next();
};

router.use(isAssurer);

router.route('/portfolios')
  .get(async (req, res) => {
    try {
      const assurancePortfolios = await AssuranceRepository.getPortfoliosWithRootInitiative(req.organization._id);
      res.FromModel(assurancePortfolios);
    } catch (error) {
      res.Exception(error)
    }
  });

router.route('/portfolios/:id')
  .get((req, res) => {
    AssuranceRepository.getAssuranceForOrg({
      portfolioId: req.params.id,
      organizationId: req.organization._id,
      domainOrigin: req.header('origin')
    })
      .then((result) => res.FromModel(result))
      .catch((err: Error) => res.Exception(err));
  });

router.route('/portfolios/:id/complete')
  .post(FileUpload.any(), ContextMiddleware, async (req, res) => {
    try {
      const portfolio = await AssurancePermissions.getPortfolioExpanded(req.params.id, req.user);
      const docs = await assuranceManager.uploadDocuments(portfolio, req.user, req.files);
      await assuranceManager.completeQuestions(portfolio, req.body.questions, req.user, docs);
      await assuranceManager.completePortfolio(portfolio, req.user, docs);
      res.FromModel(portfolio);
    }
    catch (err) {
      res.Exception(err);
    }
  });

router.route('/portfolios/:id/assure-questions')
  .post(canAssureMetrics, ContextMiddleware, async (req, res) => {
    try {
      const portfolio = await AssurancePermissions.getPortfolioExpanded(req.params.id, req.user);
      await assuranceManager.completeQuestions(portfolio, req.body.questions, req.user);
      res.FromModel(portfolio);
    }
    catch (err) {
      res.Exception(err);
    }
  });

router.route('/portfolios/:id/partial-assurance/:utrvId')
  .post(canAssureMetrics, ContextMiddleware, async (req, res, next) => {
    try {
      const { partialFields } = mustValidate(req.body, partialAssuranceDtoSchema);
      const portfolio = await AssurancePermissions.getPortfolioExpanded(req.params.id, req.user);
      await assuranceManager.partialAssuranceQuestion({
        assurancePortfolio: portfolio,
        partialFields,
        utrvId: req.params.utrvId,
        user: req.user,
      });
      res.FromModel(portfolio);
    } catch (err) {
      next(err);
    }
  });

router.route('/portfolios/:id/dispute')
  .post(canAssureMetrics, async (req, res) => {
    try {
      const portfolio = await AssurancePermissions.getPortfolioExpanded(req.params.id, req.user);
      await assuranceManager.disputeQuestions(portfolio, req.body, req.user);
      res.FromModel(portfolio);
    }
    catch (err) {
      res.Exception(err);
    }
  });

// Used by Survey QuestionContainer
router.route('/portfolios/:id/utrv/:utrvId/history')
  .get((req, res) => {
    UniversalTrackerActionRepository.getUtrvStakeholdersHistoryAssurer(req.params.utrvId, req.user)
      .then(async (data) => {

        const { latestHistory } = data;
        const { stakeholderHistory, verifierHistory } = latestHistory;
        const evidence = combinedEvidence([stakeholderHistory, verifierHistory]);
        const documents = await documentModel
          .find({ _id: { $in: evidence } })
          .lean().exec();

        // Rebuild back into data structure
        return {
          ...data,
          latestHistory: await addDocumentUrl({ ...latestHistory, documents })
        };
      })
      .then(data => res.FromModel(data))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/portfolios/:id/permissions')
  .get((req, res) => {
    AssurancePermissions.getPortfolio(req.params.id, req.user)
      .then((portfolio) => assuranceManager.getAvailablePermissions(portfolio))
      .then((result) => res.FromModel(result))
      .catch((err: Error) => res.Exception(err));
  })
  .put(canManageAssurancePortfolio, ContextMiddleware, (req, res) => {
    AssurancePermissions.getPortfolio(req.params.id, req.user)
      .then((portfolio) => assuranceManager.updatePermissions(portfolio, req.body, req.user))
      .then(() => res.Success('updated'))
      .catch((err: Error) => res.Exception(err));
  });

module.exports = router;
