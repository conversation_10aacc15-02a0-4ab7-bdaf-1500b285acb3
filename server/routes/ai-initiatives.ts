/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import express from 'express';
import { populateInitiative } from '../middleware/commonMiddlewares';
import { AuthRouter } from '../http/AuthRouter';
import { SurveyPermissions } from '../service/survey/SurveyPermissions';
import { SurveyRepository } from '../repository/SurveyRepository';
import { UtrvPermissions } from '../service/utr/UtrvPermissions';
import BadRequestError from '../error/BadRequestError';
import UniversalTrackerValue, { UniversalTrackerValueExtended } from '../models/universalTrackerValue';
import { getTargetRepository } from '../repository/TargetRepository';
import { getAiService } from '../service/ai/service';
import { mustValidate } from '../util/validation';
import { additionalContextDto, autoAnswerSchema } from './validation-schemas/ai-assistant';
import { AdditionalContext } from '../service/ai/utrv-assistant/types';
import { canManageInitiative } from '../middleware/initiativeMiddlewares';
import { getSurveyAutoAnswerWorkflow } from '../service/ai/survey-auto-answer/SurveyAutoAnswerWorkflow';
import { ObjectId } from 'bson';
import { idSchema } from './validation-schemas/common';
import { z } from 'zod';
import { getAIDocumentLibraryScanWorkflow } from '../service/ai/document-utr-mapping/AIDocumentUtrMappingWorkflow';
import { getAuditLogger } from '../service/audit/AuditLogger';
import { wwgLogger } from '../service/wwgLogger';
import { SurveyAudit } from '../service/audit/events/survey';

const router = express.Router({ mergeParams: true }) as AuthRouter;
const aiService = getAiService();
const auditLogger = getAuditLogger();

router.route('/survey/:surveyId/sdg-summary')
  .post(populateInitiative, async (req, res, next) => {
    if (!req.initiative?._id) {
      return res.NotPermitted();
    }

    try {
      const survey = await SurveyRepository.mustFindById(req.params.surveyId);
      if (!await SurveyPermissions.canAccess(survey, req.user)) {
        return res.NotPermitted();
      }

      const response = await aiService.getSummaryMaterialSDGContributions(req.initiative, survey, req.user._id);
      res.FromModel(response);
    } catch (e) {
      next(e);
    }
  });

router.route('/survey/:surveyId/q-and-a')
  .post(populateInitiative, async (req, res, next) => {
    if (!req.body.question || !req.initiative?._id) {
      return res.Invalid('Invalid question');
    }

    try {
      const survey = await SurveyRepository.mustFindById(req.params.surveyId);
      if (!await SurveyPermissions.canAccess(survey, req.user)) {
        return res.NotPermitted();
      }

      const question = `${req.body.question}`;
      const response = await aiService.getAnswerToQuestion({
        initiative: req.initiative,
        survey,
        question,
        userId: req.user._id,
      });
      res.FromModel(response);
    } catch (e) {
      next(e);
    }
  });

router.route('/generate-draft/further-notes')
  .post(populateInitiative, async (req, res, next) => {
    if (!req.body.utrvId) {
      return res.Exception(new BadRequestError('Missing utrvId'));
    }

    try {
      const { utrvId, draftData } = req.body;
      const utrv = await UniversalTrackerValue.findById(utrvId)
        .populate('universalTracker')
        .orFail()
        .lean<UniversalTrackerValueExtended>()
        .exec();

      if (!await UtrvPermissions.canAccess(utrv, req.user)) {
        return res.NotPermitted();
      }

      const response = await aiService.getFurtherNotesDraft(utrv, draftData, req.user._id);
      res.FromModel(response);
    } catch (e) {
      next(e);
    }
  });

router.route('/utrv-assistant/:utrvId').post(populateInitiative, async (req, res, next) => {
  const initiative = req.initiative;
  if (!initiative) {
    return res.NotPermitted();
  }

  try {
    const utrv = await UniversalTrackerValue.findById(req.params.utrvId)
      .populate('universalTracker')
      .orFail()
      .lean<UniversalTrackerValueExtended>()
      .exec();

    const additionalContext: AdditionalContext | undefined = req.body.inputData
      ? mustValidate(req.body, additionalContextDto)
      : undefined;
    const { content, promptInput } = await aiService.getUtrvAssistantResponse({ initiative, utrv, additionalContext });
    const targetRepository = getTargetRepository();

    const result = {
      previousUtrvs: promptInput.previousUtrvs,
      targetValue: await targetRepository.getClosestTargetValue({
        utrId: utrv.universalTrackerId,
        initiativeId: initiative._id,
      }),
      ...content,
    };

    res.FromModel(result);
  } catch (e) {
    next(e);
  }
});

router
  .route('/survey/:surveyId/auto-answer')
  .get(populateInitiative, canManageInitiative, async (req, res) => {
    const { surveyId } = mustValidate({ surveyId: req.params.surveyId }, z.object({ surveyId: idSchema }));
    const response = await getSurveyAutoAnswerWorkflow().findLatestJobWithKey(
      new ObjectId(req.params.initiativeId),
      surveyId
    );
    res.FromModel(response);
  })
  .post(populateInitiative, canManageInitiative, async (req, res) => {
    const { useDocumentLibrary, isOverwriteMetric } = mustValidate(req.body, autoAnswerSchema);
    const surveyId = new ObjectId(req.params.surveyId);
    const response = await getSurveyAutoAnswerWorkflow().createOrRetry({
      initiativeId: new ObjectId(res.locals.initiativeId as string),
      surveyId,
      userId: req.user._id,
      useDocumentLibrary,
      isOverwriteMetric,
    });

    auditLogger
      .fromRequest(req, {
        initiativeId: res.locals.initiativeId as string,
        auditEvent: SurveyAudit.aiAutoAnswerSurvey,
        targets: [auditLogger.surveyTarget({ _id: surveyId })],
        debugData: {
          useDocumentLibrary,
          isOverwriteMetric,
        },
        message: SurveyAudit.aiAutoAnswerSurvey.description,
      })
      .catch(wwgLogger.error);

    res.FromModel(response);
  });

router
  .route('/document-scan')
  .get(populateInitiative, canManageInitiative, async (req, res) => {
    const response = await getAIDocumentLibraryScanWorkflow().findLatestJob(new ObjectId(req.params.initiativeId));
    res.FromModel(response);
  });
    

module.exports = router;
