/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import express from 'express';
import { getDashboardSharingService } from '../service/insight-dashboard/DashboardSharingService';
import { InsightDashboard } from '../models/insightDashboard';
import { ObjectId } from 'bson';
import { DataPeriods } from '../service/utr/constants';
import { SurveyType } from '../models/survey';

const router = express.Router();
const dashboardSharingService = getDashboardSharingService();

router.route('/:dashboardId/token/:token').get(async (req, res, next) => {
  try {
    const dashboard = await InsightDashboard.findOne({ _id: new ObjectId(req.params.dashboardId) })
      .orFail()
      .lean()
      .exec();
    const result = await dashboardSharingService.getSharedDashboard({
      dashboardId: req.params.dashboardId,
      token: req.params.token,
      filters: {
        period: dashboard.filters.period ?? DataPeriods.Yearly,
        surveyType: dashboard.filters.surveyType ?? SurveyType.Default,
      },
    });

    return res.FromModel(result);
  } catch (e) {
    next(e);
  }
});

module.exports = router;
