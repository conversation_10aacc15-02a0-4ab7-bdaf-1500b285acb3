/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */


import express from "express";
import { AuthRouter } from "../http/AuthRouter";
import { getAccessTokenService } from "../public-api/UserApiKeyService";
import BadRequestError from "../error/BadRequestError";
import { mustValidate } from "../util/validation";
import { z } from "zod";
import { isValidObjectId } from "mongoose";
import { ObjectId } from "bson";
import { InitiativePermissions } from "../service/initiative/InitiativePermissions";
import { getAuditLogger } from "../service/audit/AuditLogger";
import { LEVEL } from "../service/event/Events";
import { wwgLogger } from "../service/wwgLogger";
import { userAudit } from "../service/audit/events/userAudit";
import { ConnectionRole, ScopePermission } from "../public-api/scopePermissionModels";

const router = express.Router() as AuthRouter;

const auditLogger = getAuditLogger();
router.route('/personal')
  .get((req, res, next) => {
    getAccessTokenService()
      .listPersonalTokens(req.user)
      .then((keys) => {
        res.FromModel(keys)
      })
      .catch((err: Error) => next(err));
  })
  .post(async (req, res, next) => {

    try {
      const service = getAccessTokenService();
      const tokens = await service.listPersonalTokens(req.user)

      if (tokens.length >= service.maxApiKeys) {
        return next(new BadRequestError('You have reached the maximum number of api keys', {
          maxTokens: service.maxApiKeys,
          currentTokens: tokens.map(token => token.shortToken),
        }));
      }

      const validTokens = tokens.filter(token => !token.revokedDate);
      if (validTokens.length >= service.maxValidApiKeys) {
        return next(new BadRequestError('You have reached the maximum number of active api keys', {
          maxTokens: service.maxValidApiKeys,
          currentTokens: validTokens.map(token => token.shortToken),
        }));
      }

      const data = mustValidate(
        req.body,
        z.object({
          name: z.string().max(200).optional(),
          initiativeId: z.string().refine(
            (strId) => isValidObjectId(strId),
            () => ({ message: 'initiativeId is not valid ObjectId' })
          ).transform((strId) => new ObjectId(strId)),
          roles: z.nativeEnum(ConnectionRole).array().min(1, 'You must provide at least one role').default([]),
          scopes: z.nativeEnum(ScopePermission).array().default([]),
        }),
      );

      // Ensure initiativeId user have access directly.
      const initiativePerm = req.user.permissions.find(p => p.initiativeId.equals(data.initiativeId));
      const allowedRoles = InitiativePermissions.canManageRoles;
      if (!initiativePerm?.permissions.some(p => allowedRoles.includes(p))) {
        return next(new BadRequestError('Not allowed to create api key for this company', {
          initiativeId: data.initiativeId,
          initiativePerm,
          allowedRoles,
        }));
      }

      const apiKeyData = await service.createPersonalToken(req.user, data);

      auditLogger
        .fromRequest(req, {
          auditEvent: userAudit.apiKeyCreated,
          severity: LEVEL.WARNING,
          targets: [auditLogger.userTarget(req.user), auditLogger.userApiKeyTarget(apiKeyData.apiKey)],
        })
        .catch(wwgLogger.error);

      res.FromModel(apiKeyData);
    } catch (e) {
      next(e);
    }
  });

router.route('/personal/:id/revoke')
  .post((req, res, next) => {
    getAccessTokenService()
      .revoke(req.user, req.params.id)
      .then((apiKey) => {
        auditLogger
          .fromRequest(req, {
            auditEvent: userAudit.apiKeyRevoked,
            severity: LEVEL.WARNING,
            targets: [auditLogger.userTarget(req.user), auditLogger.userApiKeyTarget(apiKey)],
          })
          .catch(wwgLogger.error);

        return res.FromModel({
          _id: apiKey._id,
          initiativeId: apiKey.initiativeId,
          name: apiKey.name,
          shortToken: apiKey.shortToken,
          revokedDate: apiKey.revokedDate,
        });
      })
      .catch((err: Error) => next(err));
  });

module.exports = router;
