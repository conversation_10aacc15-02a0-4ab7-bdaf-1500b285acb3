/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import Onboarding from '../models/onboarding';
import { getOnboardingManager } from '../service/onboarding/OnboardingManager';
import { getSelfOnboardingManager } from '../service/onboarding/SelfOnboardingManager';
import { AuthRouter } from '../http/AuthRouter';
import { ContextMiddleware } from '../middleware/audit/contextMiddleware';
import { wwgLogger } from '../service/wwgLogger';
import { getAuditLogger } from '../service/audit/AuditLogger';
import { SurveyAudit } from '../service/audit/events/survey';
import Initiative from "../models/initiative";
import { InitiativePermissions } from "../service/initiative/InitiativePermissions";
import UserError from "../error/UserError";

const router = express.Router() as AuthRouter;
const onboardingManager = getOnboardingManager();
const selfOnboardingManager = getSelfOnboardingManager();
const auditLogger = getAuditLogger();

router.route('/token/:token/:action')
  .post((req, res) => {
    const { token, action } = req.params;
    Onboarding.findOne({ 'token': token }).exec()
      .then(async (model) => {

        if (!model) {
          return res.Invalid('Token is not valid');
        }
        const result = await onboardingManager.userDecision(model, req.user, action);
        return res.FromModel(result);
      })
      .catch((e: Error) => res.Exception(e));
  });

router.route('/self')
  .post(ContextMiddleware, async (req, res, next) => {
    try {
      const initiative = await Initiative.findById(req.body.initiativeId).orFail().exec();
      // @TODO should this be canManage check?
      if (!await InitiativePermissions.canAccess(req.user, initiative._id)) {
        return next(new UserError(`User does not have access to reporting company`, {
          userId: req.user._id,
          initiativeId: req.body.initiativeId,
        }));
      }

      const survey = await selfOnboardingManager.createNewSurvey(
        initiative,
        req.user,
        { ...req.body, domain: req.header('origin') }
      );
      auditLogger.fromContext({
        initiativeId: survey.initiativeId,
        auditEvent: SurveyAudit.create,
        targets: [auditLogger.surveyTarget(survey)],
      }).catch(wwgLogger.error)
      res.FromModel(survey);
    } catch (e) {
      next(e);
    }
  });

module.exports = router;
