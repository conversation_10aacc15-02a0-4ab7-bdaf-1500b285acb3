/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import express from 'express';
import { getBluePrintContribution } from '../service/survey/BlueprintContribution';
import { Blueprints } from '../survey/blueprints';

const router = express.Router();
const bc = getBluePrintContribution();

router.route('/:code')
  .get((req, res) => {
    bc.getContributions(req.params.code as Blueprints)
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/:code/questions')
  .get((req, res) => {
    bc.getQuestions(req.params.code as Blueprints)
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  });

module.exports = router;
