import express from 'express';
import { getInitiativeUniversalTrackerService } from '../service/initiative/InitiativeUniversalTrackerService';
import { canManageInitiative } from '../middleware/initiativeMiddlewares';
import { InitiativePermissions } from '../service/initiative/InitiativePermissions';
import { AuthRouter } from '../http/AuthRouter';
import { mustValidate } from '../util/validation';
import { inputOverrideSchema, utrvConfigOverrideSchema } from './validation-schemas/initiative-universal-trackers';
import { ObjectId } from 'bson';
import { getMetricOverrideService } from '../service/initiative-universal-tracker/MetricOverrideService';

const router = express.Router() as AuthRouter;
const initiativeUniversalTrackerService = getInitiativeUniversalTrackerService();

router
  .route('/initiative/:initiativeId')
  .get(async (req, res, next) => {
    const initiativeId = req.params.initiativeId;
    if (!(await InitiativePermissions.canAccess(req.user, initiativeId))) {
      return res.NotPermitted();
    }
    try {
      const rootInitiativeUtrs = await initiativeUniversalTrackerService.getRootInitiativeUniversalTrackers(initiativeId);
      res.FromModel(rootInitiativeUtrs);
    } catch (e) {
      next(e);
    }
  })
  .post(canManageInitiative, async (req, res, next) => {
    const initiativeId = req.params.initiativeId;
    try {
    const { decimal, unitConfig, variation, utrIds } = mustValidate(req.body, inputOverrideSchema);
      const result = await initiativeUniversalTrackerService.setOverriddenValueValidation({
        decimal,
        initiativeId: new ObjectId(initiativeId),
        variation,
        utrIds,
        unitConfig,
      });
      res.FromModel(result);
    } catch (e) {
      next(e);
    }
  });

router.route('/initiative/:initiativeId/override/utrv-config').post(canManageInitiative, async (req, res, next) => {
  const initiativeId = req.params.initiativeId;
  const { utrIds, utrvConfig } = mustValidate(req.body, utrvConfigOverrideSchema);
  try {
    const result = await getMetricOverrideService().override({ initiativeId, utrIds, utrvConfig });
    res.FromModel(result);
  } catch (e) {
    next(e);
  }
});

module.exports = router;
