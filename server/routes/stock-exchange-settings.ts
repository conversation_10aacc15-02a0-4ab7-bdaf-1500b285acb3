/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { AuthRouter } from '../http/AuthRouter';
import { getStockExchangeService } from '../service/stock-exchange/StockExchangeService';

const router = express.Router() as AuthRouter;

const stockExchangeService = getStockExchangeService();

router.route('/list').get((req, res) => {
  return res.FromModel(stockExchangeService.getListOfStockExchanges());
});

module.exports = router;
