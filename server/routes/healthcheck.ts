/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import express from 'express';
import mongoose from 'mongoose';
import ContextError from '../error/ContextError';
import BadRequestError from '../error/BadRequestError';
import { wwgLogger } from "../service/wwgLogger";

const router = express.Router();

const startupTime = Date.now();
const CHECK_DELAY = 15000;

router.route('/').get((_req, res, next) => {
  // Check MongoDB
  try {
    const { readyState, host } = mongoose.connection;
    if (readyState === mongoose.ConnectionStates.connected) {
      res.send('Home page!')
      return;
    }

    const context = { host, readyState, startupTime, delay: CHECK_DELAY };
    if (Date.now() > (startupTime + CHECK_DELAY)) {
      next(new ContextError('Database not connected', context));
      return
    }

    const state = mongoose.ConnectionStates[readyState] ?? 'unknown';
    wwgLogger.info(`Still waiting for database to connect: state ${state}`, context);

    next(new BadRequestError('Still waiting for database...'));
  } catch (e) {
    next(e)
  }
});

module.exports = router;
