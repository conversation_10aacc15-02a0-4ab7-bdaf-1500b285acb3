import express from 'express';
import { ObjectId } from 'bson';
import type { AuthRouter } from '../http/AuthRouter';
import { getWorkgroupService } from '../service/workgroup/WorkgroupService';
import { canManageInitiative, isRootLevel } from '../middleware/initiativeMiddlewares';
import type { InitiativeModel } from '../models/initiative';
import { mustValidate } from '../util/validation';
import { z } from 'zod';

const router = express.Router({ mergeParams: true }) as AuthRouter;
const workgroupService = getWorkgroupService();

router
  .route('')
  .get(async (req, res, next) => {
    try {
      const workgroups = await workgroupService.getWorkgroups({ initiativeId: new ObjectId(req.params.initiativeId) });
      return res.FromModel(workgroups);
    } catch (e) {
      return next(e);
    }
  })
  .post(canManageInitiative, isRootLevel, async (req, res, next) => {
    try {
      const initiative = res.locals.initiative as InitiativeModel;

      const workgroup = await workgroupService.createWorkgroup({
        creatorId: req.user._id,
        initiative,
        data: mustValidate(
          req.body,
          z.object({
            name: z.string(),
            description: z.string().optional(),
            icon: z.string(),
            color: z.string(),
          })
        ),
      });

      return res.FromModel(workgroup);
    } catch (e) {
      return next(e);
    }
  });

router
  .route('/:workgroupId')
  .get(async (req, res, next) => {
    try {
      const workgroup = await workgroupService.getWorkgroup({
        workgroupId: new ObjectId(req.params.workgroupId),
        initiativeId: new ObjectId(req.params.initiativeId),
      });
      return res.FromModel(workgroup);
    } catch (e) {
      return next(e);
    }
  })
  .put(canManageInitiative, isRootLevel, async (req, res, next) => {
    try {
      const initiative = res.locals.initiative as InitiativeModel;

      const workgroup = await workgroupService.updateWorkgroup({
        initiativeId: initiative._id,
        workgroupId: new ObjectId(req.params.workgroupId),
        data: mustValidate(
          req.body,
          z.object({
            name: z.string().optional(),
            description: z.string().optional(),
            icon: z.string().optional(),
            color: z.string().optional(),
          })
        ),
      });
      return res.FromModel(workgroup);
    } catch (e) {
      return next(e);
    }
  })
  .delete(canManageInitiative, isRootLevel, async (req, res, next) => {
    try {
      const initiative = res.locals.initiative as InitiativeModel;
      await workgroupService.deleteWorkgroup({
        workgroupId: new ObjectId(req.params.workgroupId),
        initiativeId: initiative._id,
      });
      return res.Success();
    } catch (e) {
      return next(e);
    }
  });

router.route('/:workgroupId/duplicate').post(canManageInitiative, isRootLevel, async (req, res, next) => {
  try {
    const initiative = res.locals.initiative as InitiativeModel;

    const result = await workgroupService.duplicateWorkgroup({
      workgroupId: new ObjectId(req.params.workgroupId),
      initiative,
      creatorId: req.user._id,
    });
    return res.FromModel(result);
  } catch (e) {
    return next(e);
  }
});

router.route('/:workgroupId/users').put(canManageInitiative, isRootLevel, async (req, res, next) => {
  try {
    const initiative = res.locals.initiative as InitiativeModel;
    const { userIds } = mustValidate(req.body, z.object({ userIds: z.array(z.string()) }));
    const workgroup = await workgroupService.addUsersToWorkgroup({
      workgroupId: new ObjectId(req.params.workgroupId),
      initiativeId: initiative._id,
      userIds: userIds.map((id) => new ObjectId(id)),
    });
    return res.FromModel(workgroup);
  } catch (e) {
    return next(e);
  }
});

router.route('/:workgroupId/users/:userId').delete(canManageInitiative, isRootLevel, async (req, res, next) => {
  try {
    const initiative = res.locals.initiative as InitiativeModel;
    const workgroup = await workgroupService.removeUserFromWorkgroup({
      workgroupId: new ObjectId(req.params.workgroupId),
      initiativeId: initiative._id,
      userId: new ObjectId(req.params.userId),
    });
    return res.FromModel(workgroup);
  } catch (e) {
    return next(e);
  }
});

module.exports = router;
