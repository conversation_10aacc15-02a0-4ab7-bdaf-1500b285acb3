import express from 'express';
import { AuthRouter } from '../http/AuthRouter';
import { ObjectId } from 'bson';
import { BookmarkRepository } from '../repository/BookmarkRepository';

const router = express.Router() as AuthRouter;

router.route('/universal-tracker-value/survey/:surveyId').get(async (req, res) => {
  try {
    const userId = req.user._id;
    const surveyId = new ObjectId(req.params.surveyId);
    return res.FromModel(await BookmarkRepository.findUtrvBookmarksBySurvey(userId, surveyId));
  } catch (e) {
    res.Exception(e);
  }
});

router
  .route('/universal-tracker-value')
  .post(async (req, res) => {
    try {
      const userId = req.user._id;
      const utrvId = new ObjectId(req.body.utrvId);
      const surveyId = new ObjectId(req.body.surveyId);
      return res.FromModel(await BookmarkRepository.createUtrvBookmark(userId, utrvId, surveyId));
    } catch (e) {
      res.Exception(e);
    }
  })
  .delete(async (req, res) => {
    try {
      const userId = req.user._id;
      const utrvId = new ObjectId(req.body.utrvId);
      const surveyId = new ObjectId(req.body.surveyId);
      return res.FromModel(await BookmarkRepository.deleteUtrvBookmark(userId, utrvId, surveyId));
    } catch (e) {
      res.Exception(e);
    }
  });

module.exports = router;
