/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import UniversalTrackerActionRepository from '../repository/UniversalTrackerActionRepository';
import { addDocumentUrl } from '../service/storage/fileStorage';
import { UserRepository } from '../repository/UserRepository';
import FileUpload from '../http/FileUpload';
import { saveProfile } from '../service/file/profile';
import { AuthRouter } from '../http/AuthRouter';
import { SurveyRepository } from '../repository/SurveyRepository';
import Survey from '../models/survey';
import { UserAgreement } from '../models/user';
import Organization from '../models/organization';
import { organizationProjectFields } from '../repository/projections';
import { getInitiativeUserService } from '../service/user/InitiativeUserService';
import { InitiativePermissions } from '../service/initiative/InitiativePermissions';

const router = express.Router() as AuthRouter;

router.route('/profile')
  .post(FileUpload.single('profile'), (req, res) => {
    if (!req.file) {
      return res.Invalid('No files uploaded');
    }
    saveProfile(req.user._id, 'user', [req.file])
      .then(msg => res.Success(msg))
      .catch((err: Error) => res.Exception(err));
  });

router.route('/current/actions')
  .get(async (req, res) => {
    try {
      const utrData = await UniversalTrackerActionRepository.getUserPendingActions(req.user._id);
      await addDocumentUrl(utrData);
      return res.FromModel({
        user: req.user.UserPlain(),
        actions: { utrs: utrData, frameworkUtrs: [], surveys: [] }
      });
    } catch (err) {
      res.Exception(err);
    }
  });

// May become deprecated if we support multiple assurance organizations per user
router.route('/current/organization')
  .get(async (req, res, next) => {
    try {
      const filter = req.user.organizationId
        ? { _id: req.user.organizationId }
        : { 'permissions.userId': req.user._id };
      const org = await Organization.findOne(filter, organizationProjectFields).exec();
      res.FromModel(org);
    } catch (error) {
      next(error);
    }
  });

router.route('/current/surveys/available')
  .get(async (req, res, next) => {
    Survey.findOne({
      visibleStakeholders: req.user._id,
      deletedDate: { $exists: false },
    }, { _id: 1 }).exec()
      .then(survey => res.FromModel({ available: Boolean(survey) }))
      .catch((e) => next(e));
  });

router.route('/:userId/agreements/:agreementCode/accept')
  .post(async (req, res) => {
    const { user } = req;
    const { userId, agreementCode } = req.params;
    if (userId !== String(user._id)) {
      return res.Invalid('Bad request');
    }

    if (!Object.values(UserAgreement).includes(agreementCode as UserAgreement)) {
      return res.Invalid('Not a valid agreement');
    }

    if (!user.registrationData) {
      user.registrationData = {};
    }
    if (!user.registrationData.agreements) {
      user.registrationData.agreements = {};
    }
    if (user.registrationData.agreements[agreementCode as UserAgreement]) {
      return res.Success(); // Already agreed, so return to say all good and frontend can continue
    }
    user.registrationData.agreements[agreementCode as UserAgreement] = new Date();
    try {
      await user.save();
      return res.Success();
    } catch (err) {
      res.Exception(err);
    }
  });

router.route('/unread-messages-count')
  .get((req, res) => res.Invalid('Not implemented'));

router.route('/initiative/:initiativeId/search')
  .get((req, res) => UserRepository.searchByInitiativeId(req.query.s as string, req.params.initiativeId)
    .then((data) => UserRepository.anonymize(data))
    .then((data) => res.FromModel(data))
    .catch((e) => res.Exception(e)));

router.route('/organization/:organizationId/search')
  .get((req, res) => UserRepository.searchByOrganizationId(req.query.s as string, req.params.organizationId)
    .then((data) => UserRepository.anonymize(data))
    .then((data) => res.FromModel(data))
    .catch((e) => res.Exception(e)));

router.route('/survey/:surveyId/search')
  .get((req, res) => {
    SurveyRepository.mustFindById(req.params.surveyId)
      .then(async (survey) => {
        const canManage = await InitiativePermissions.canManageInitiative(req.user, survey.initiativeId);
        const data = await getInitiativeUserService().getUsersAndOnboardingsForDelegation(req.query.s as string, survey);
        return canManage ? data : UserRepository.anonymize(data);
      })
      .then((data) => res.FromModel(data))
      .catch((e) => res.Exception(e))
  });

// router.route('/current/scorecard-favourites/:initiativeId')
//   .post(async (req, res) => {
//     const user = req.user;
//     if (!Array.isArray(user.scorecardFavourites)) {
//       user.scorecardFavourites = [];
//     }
//     user.scorecardFavourites = user.scorecardFavourites.filter((id: ObjectId) => String(id) !== req.params.initiativeId);
//     user.scorecardFavourites.push(new ObjectId(req.params.initiativeId));
//     try {
//       await user.save();
//       return res.FromModel('User scorecard favourite add');
//     } catch (err) {
//       res.Exception(err);
//     }
//   })
//   .delete(async (req, res) => {
//     const user = req.user;
//     if (!Array.isArray(user.scorecardFavourites)) {
//       return res.FromModel('Nothing to do');
//     }
//     user.scorecardFavourites = user.scorecardFavourites.filter((id: ObjectId) => String(id) !== req.params.initiativeId);
//     try {
//       await user.save();
//       return res.FromModel('User scorecard favourite add');
//     } catch (err) {
//       res.Exception(err);
//     }
//   });

module.exports = router;
