/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import express from 'express';
import { AuthRouter } from '../http/AuthRouter';
import Initiative from '../models/initiative';
import UserError from '../error/UserError';
import { getCustomReportManager } from '../service/custom-report/CustomReportManager';
import CustomReport, { CustomReportType, MetricTypes, isCustomReportTemplate } from '../models/customReport';
import UniversalTrackerValueService, { CustomReportValue } from '../service/utr/UniversalTrackerValueService';
import { setXlsxFileHeaders } from '../http/FileDownload';
import * as XLSX from '@sheet/core';
import { FileParserType } from '../service/survey/transfer/parserTypes';
import CalculationUniversalTrackerService from '../service/utr/CalculationUniversalTrackerService';
import { UtrValueType } from '../models/public/universalTrackerType';
import { populateInitiative } from '../middleware/commonMiddlewares';
import { getCustomReportDownload } from '../service/custom-report/CustomReportDownload';
import { getInitiativeStatsService } from '../service/initiative/InitiativeStatsService';
import Survey, { BASED_SURVEY_TYPES, SurveyType } from '../models/survey';
import { canManageCustomReports } from '../middleware/initiativeMiddlewares';
import { ObjectId } from 'bson';
import { InitiativeRepository } from '../repository/InitiativeRepository';
import MetricGroup, { MetricGroupType } from '../models/metricGroup';

const router = express.Router({ mergeParams: true }) as AuthRouter;
const customReportManager = getCustomReportManager();
const initiativeStats = getInitiativeStatsService();

router.route('')
  .get(populateInitiative, async (req, res, next) => {
    try {
      const customReports = await customReportManager.getByInitiativeId(req.params.initiativeId);
      return res.FromModel(customReports);
    } catch (e) {
      return next(e);
    }
  })
  .post(canManageCustomReports, async (req, res, next) => {
    try {
      const initiativeId = res.locals.initiativeId; // Parsed by middleware
      const initiative = await Initiative.findById(initiativeId).exec();
      if (!initiative) {
        return next(new UserError('There was an error creating this custom report. Please refresh the page and try again.'));
      }

      const customReport = await customReportManager.create({
        initiativeId: initiativeId,
        name: req.body.name,
        description: req.body.description,
        type: req.body.type,
        survey: req.body.survey,
        config: req.body.config
      });
      return res.FromModel(customReport);
    } catch (e) {
      return next(e);
    }
  })

router.route('/create-from-bubbles')
  .post(canManageCustomReports, async (req, res, next) => {
    try {
      const initiativeId = res.locals.initiativeId; // Parsed by middleware
      const initiative = await Initiative.findById(initiativeId).exec();
      if (!initiative) {
        return next(new UserError('There was an error creating this custom report. Please refresh the page and try again.'));
      }
      const data = {
        initiativeId: initiativeId,
        name: 'Targets and Latest Values',
        description: 'Automatically created from targets',
        type: CustomReportType.Metrics,
      }
      const customReport = await customReportManager.createFromBubbles(data, initiative);

      return res.FromModel(customReport);
    } catch (e) {
      return next(e);
    }
  })

router.route('/metric-groups').get(canManageCustomReports, async (req, res, next) => {
  try {
    const initiativeId = res.locals.initiativeId as string; // Parsed by middleware
    const initiatives = await InitiativeRepository.getFullTree(initiativeId);
    const initiativeIds = initiatives.map((i) => i._id);
    const metricGroups = await MetricGroup.find(
      {
        type: MetricGroupType.Custom,
        $or: [
          { initiativeId: { $in: initiativeIds } },
          {
            'share.initiativeId': { $in: initiativeIds },
          },
        ],
      },
      {
        _id: 1,
        groupName: 1,
        universalTrackers: 1,
      }
    )
      .lean()
      .exec();
    res.FromModel(metricGroups);
  } catch (e) {
    return next(e);
  }
});

router.route('/:reportId')
  .get(populateInitiative, async (req, res, next) => {
    try {
      const initiativeId = req.params.initiativeId;
      const customReport = await customReportManager.getById({
        id: new ObjectId(req.params.reportId),
        initiativeId: new ObjectId(initiativeId),
      });
      return res.FromModel(customReport);
    } catch (e) {
      return next(e);
    }
  })
  .delete(canManageCustomReports, async (req, res, next) => {
    try {
      const initiativeId = res.locals.initiativeId; // Parsed by middleware
      const customReport = await CustomReport.findOne({ _id: req.params.reportId, initiativeId: initiativeId }).orFail().exec();

      await Promise.all(customReport.metrics.map(metric => {
        return customReportManager.removeMetric(customReport, String(metric.utrId), false);
      })
      );

      await CustomReport.findOneAndDelete({ _id: req.params.reportId, initiativeId: initiativeId }).exec();
      return res.Success('Custom report deleted.');
    } catch (e) {
      return next(e);
    }
  })
  .put(canManageCustomReports, async (req, res, next) => {
    try {
      const initiativeId = res.locals.initiativeId; // Parsed by middleware
      const customReport = await CustomReport.findOne({ _id: req.params.reportId, initiativeId }).orFail().exec();
      await customReportManager.update(customReport, req.body);
      return res.FromModel(customReport);
    } catch (e) {
      return next(e);
    }
  })

router.route('/:reportId/values')
  .get(populateInitiative, async (req, res, next) => {
    try {
      const initiativeId = req.params.initiativeId;
      const initiative = await Initiative.findById(initiativeId).exec();
      if (!initiative) {
        return next(new UserError('There was an error downloading this custom report. Please refresh the page and try again.'));
      }
      const customReport = await CustomReport.findOne({ _id: req.params.reportId, initiativeId: initiativeId }).populate('universalTrackers').orFail().exec();
      const utrService = new UniversalTrackerValueService(initiative);
      const values = await utrService.getCustomReportValues(customReport, true);
      res.FromModel(values);
    } catch (e) {
      return next(e);
    }
  });

router.route('/:reportId/download')
  .get(populateInitiative, async (req, res, next) => {
    try {
      const initiativeId = req.params.initiativeId;
      const initiative = await Initiative.findById(initiativeId).exec();
      if (!initiative) {
        return next(new UserError('There was an error downloading this custom report. Please refresh the page and try again.'));
      }
      const customReport = await CustomReport.findOne({ _id: req.params.reportId, initiativeId: initiativeId })
        .populate('universalTrackers')
        .orFail()
        .exec();
      const customDownload = getCustomReportDownload();
      const workbook = await customDownload.getCustomReportDownload(customReport, initiative);

      if (!workbook) {
        return next(new UserError('Could not generate workbook'));
      }

      const fileName = `${customReport.name} ${new Date().toISOString()}.xlsx`;

      setXlsxFileHeaders(res, fileName);
      return res.send(XLSX.write(workbook, { type: 'buffer', bookType: FileParserType.Xlsx, cellStyles: isCustomReportTemplate(customReport) }));
    } catch (e) {
      return next(e);
    }
  })

router.route('/:reportId/clone')
  .post(canManageCustomReports, async (req, res, next) => {
    try {
      const initiativeId = res.locals.initiativeId as string; // Parsed by middleware
      const customReport = await customReportManager.getById({ id: new ObjectId(req.params.reportId), initiativeId: new ObjectId(initiativeId) });

      const data = {
        initiativeId: initiativeId,
        name: `Copy of ${customReport.name}`,
        description: customReport.description,
        metrics: customReport.metrics,
        type: customReport.type,
        survey: customReport.survey,
        config: customReport.config,
      };
      const clonedCustomReport = await customReportManager.create(data);
      return res.FromModel(clonedCustomReport);
    } catch (e) {
      return next(e);
    }
  });

Object.values(MetricTypes).forEach((type) => {

router.route(`/:reportId/metrics/${type}`)
  .post(canManageCustomReports, async (req, res, next) => {
    try {
      const initiativeId = res.locals.initiativeId; // Parsed by middleware
      const customReport = await CustomReport.findOne({ _id: req.params.reportId, initiativeId: initiativeId }).exec();
      if (!customReport) {
        return next(new UserError('Unable to manage custom report'));
      }

      const utrs = await CalculationUniversalTrackerService.createCombinedUtrs(req.body, initiativeId);
      /// Only first UTR is supposed to be displayed, as the other ones are just used for intermediary calculations
      const utr = utrs[0];
      await customReportManager.addMetric(customReport, type, utr);

      return res.FromModel(utr);
    } catch (e) {
      next(e);
    }
  })
})

router.route('/:reportId/metrics/:metricId')
  .delete(canManageCustomReports, async (req, res, next) => {
    try {
      const initiativeId = res.locals.initiativeId; // Parsed by middleware
      const customReport = await CustomReport.findOne({ _id: req.params.reportId, initiativeId: initiativeId }).exec();
      if (!customReport) {
        return next(new UserError('Unable to manage custom report'));
      }

      await customReportManager.removeMetric(customReport, req.params.metricId);
      res.Success();
    } catch (e) {
      next(e);
    }
  });


Object.values(MetricTypes).forEach((type) => {
router.route(`/:reportId/metrics/${type}/preview`)
  .post(canManageCustomReports, async (req, res, next) => {
    try {
      const initiativeId = res.locals.initiativeId; // Parsed by middleware
      const initiative = await Initiative.findById(initiativeId).exec();
      if (!initiative) {
        return next(new UserError('There was an error creating the metric. Please refresh the page and try again.'));
      }

      const customReport = await CustomReport.findOne({ _id: req.params.reportId, initiativeId: initiativeId }).exec();
      if (!customReport) {
        return next(new UserError('Unable to manage custom report'));
      }

      // This is ugly, but simplest implementation - create the UTR, execute aggregation, then delete the UTR...
      const utrs = await CalculationUniversalTrackerService.createCombinedUtrs(req.body, initiativeId);
      const utr = utrs[0];
      await customReportManager.addMetric(customReport, type, utr);

      const utrService: UniversalTrackerValueService = new UniversalTrackerValueService(initiative);
      const values: CustomReportValue[] = await utrService.getCustomReportValues(customReport);

      const preview: CustomReportValue | undefined = values.find(({ utrv }) => String(utrv.universalTrackerId) === String(utr._id));
      await customReportManager.removeMetric(customReport, String(utr._id));

      if (!preview) {
        return res.FromModel({
          type: 'string',
          value: 'There is no data currently reported for this metric',
        });
      }

      const isTable = preview.utrv.valueType && [UtrValueType.Table].includes(preview.utrv.valueType as UtrValueType);
      if (isTable) {
        return res.FromModel({
          type: 'string',
          value: (preview.utrv.valueData?.table ?? []).map(cols => {
            const col = cols.find(c => c.code === 'text');
            return col?.value ?? ''
          })
        });
      }
      const isValueData = preview.utrv.valueType && [UtrValueType.Text, UtrValueType.ValueList, UtrValueType.ValueListMulti].includes(preview.utrv.valueType as UtrValueType);
      if (isValueData) {
        return res.FromModel({
          type: 'string',
          value: preview.utrv.valueData?.data ?? []
        });
      }

      return res.FromModel({
        type: 'number',
        value: preview.utrv.value
      });

    } catch (e) {
      next(e);
    }
  });
})

router.route('/subsidiary-report/subsidiary-list')
  .get(canManageCustomReports, (req, res, next) => {
    return Initiative.findById(res.locals.initiativeId).orFail().exec()
      .then(initiative => initiativeStats.withSurveyCount({ initiative }))
      .then(stats => res.FromModel(stats))
      .catch(e => next(e))
  })

router.route('/subsidiary-report/survey-list')
  .post(canManageCustomReports, (req, res, next) => {
    const initiativeIds: string[] = req.body.initiativeIds ?? [];
    if (!Array.isArray(initiativeIds) || initiativeIds.length === 0) {
      return res.FromModel([]);
    }

    const surveyType: SurveyType | undefined = req.body.surveyType;

    return Survey.find(
      {
        type: { $in: surveyType ? [surveyType] : BASED_SURVEY_TYPES },
        initiativeId: { $in: initiativeIds },
        deletedDate: { $exists: false },
      },
      { effectiveDate: 1, period: 1, type: 1, scope: 1, initiativeId: 1 }
    )
      .sort({ effectiveDate: 1 })
      .exec()
      .then((d) => res.FromModel(d))
      .catch((e) => next(e));
  })
  router.route('/:reportType/download-example').get(async (req, res, next) => {
    try {
      const reportType = req.params.reportType;
      const customDownload = getCustomReportDownload();
      const { fileName, exportType, workBook } = await customDownload.getCustomReportDownloadExample(reportType);
      setXlsxFileHeaders(res, fileName);

      return res.send(
        XLSX.write(workBook, {
          type: 'buffer',
          bookType: exportType,
        })
      );
    } catch (e) {
      next(e);
    }
  });

module.exports = router;
