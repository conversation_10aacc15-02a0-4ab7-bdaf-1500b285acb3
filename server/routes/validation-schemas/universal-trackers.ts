import { DataPeriods } from '../../service/utr/constants';
import { PrivacyFilter, SurveyFilter, TimeFrameType, UtrvFilter } from '../../models/insightDashboard';
import { z } from 'zod';
import { BASED_SURVEY_TYPES } from '../../models/survey';
import { objectIdsSchema } from './common';

export const getUtrsHistoryDtoSchema = z.object({
  utrCodes: z.string().array(),
  filters: z
    .object({
      survey: z.nativeEnum(SurveyFilter),
      utrv: z.nativeEnum(UtrvFilter),
      privacy: z.nativeEnum(PrivacyFilter),
      baselinesTargets: z.object({
        enabled: z.boolean(),
      }),
      initiativeIds: objectIdsSchema,
    })
    .passthrough(),
});

export const getUtrsHistoryQueryDtoSchema = z.object({
  period: z.enum([DataPeriods.Monthly, DataPeriods.Quarterly, DataPeriods.Yearly, 'all']).optional(),
  timeFrameType: z.nativeEnum(TimeFrameType).optional(),
  dateRange: z.object({
    startDate: z.string().optional(),
    endDate: z.string().optional()
  }).optional(),
  surveyType: z.enum([...BASED_SURVEY_TYPES, 'all']).optional(),
});

export const createCustomMetricDtoSchema = z.object({
    valueValidation: z
      .object({
        min: z.number().int(),
        max: z.number().int(),
      })
      .passthrough(),
  })
  .passthrough()
  .refine((data) => Number(data.valueValidation.min) < Number(data.valueValidation.max));
