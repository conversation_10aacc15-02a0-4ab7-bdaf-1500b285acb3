/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { z, ZodType } from 'zod';
import { CreateReportDocument, ReportDocumentType } from '../../models/reportDocument';

type CreateBase = Omit<CreateReportDocument, 'createdBy' | 'initiativeId'>;
export const createReportDocumentSchema = z.object({
  title: z.string(),
  description: z.string().optional(),
  type: z.nativeEnum(ReportDocumentType),
}) satisfies ZodType<CreateBase>;
