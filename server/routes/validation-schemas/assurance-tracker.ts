import { z } from 'zod';
import { OrganizationPermission } from '../../models/assurancePermission';
import { ObjectId } from 'bson';
import { isValidObjectId } from 'mongoose';

export const userOnboardingSchema = z.object({
  emails: z.string().email().array(),
  permissions: z.nativeEnum(OrganizationPermission).array(),
});

export const userPermissionUpdateSchema = z.object({
  permissions: z.nativeEnum(OrganizationPermission).array().nonempty(),
});

export const createSchema = z.object({
  code: z.string(),
  name: z.string(),
  profile: z.string().optional(),
  organizationType: z.string(),
  partnerTypes: z.string().array().optional(),
  address: z
    .object({
      line1: z.string(),
      line2: z.string().optional(),
      city: z.string().optional(),
      postcode: z.string(),
      country: z.string().optional(),
    })
    .optional(),
  permissions: z
    .object({
      userId: z
        .string()
        .refine(
          (strId) => isValidObjectId(strId),
          (strId) => ({ message: `Not a valid id, user: ${strId}` })
        )
        .transform((strId) => new ObjectId(strId)),
      permissions: z.nativeEnum(OrganizationPermission).array(),
    })
    .array()
    .optional(),
});
