import { AutoParseableResponseFormat } from 'openai/lib/parser';
import { infer as zodInfer, z, ZodType } from 'zod';

export type ResponseFormat = AutoParseableResponseFormat<zodInfer<ZodType>>;

// string | number | string[]
const valueTypeDto = z.union([z.string(), z.number(), z.array(z.string())]);
// { [key: string]: string | number | string[] }
const multiInputAnswerDto = z.record(z.string(), valueTypeDto);

export const utrvAssistantResponseDto = z.object({
  // predictedAnswer?: string | number | { [key: string]: string | number | string[] };
  predictedAnswer: z.union([z.string(), z.number(), multiInputAnswerDto]).optional(),
  questionExplanation: z.string(),
  bestPractice: z.array(z.string()),
  keyInfo: z.array(z.string()),
  suggestedEvidence: z.object({
    primaryDocumentation: z.array(z.string()),
    supportingDocumentation: z.array(z.string()),
  }),
  whereToFind: z.object({
    externalSource: z.array(z.string()),
    internalSource: z.array(z.string()),
  }),
});
