import { betaFeatureTags } from '../../models/initiative';
import { z } from 'zod';
import { Actions } from '../../service/action/Actions';

export const getInitiativeAuditLogsSchema = z.object({
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
  limit: z.number().default(100),
});

export const updateInitiativeTagsSchema = z.object({
  tag: z.enum(betaFeatureTags),
  action: z.nativeEnum(Actions),
});
