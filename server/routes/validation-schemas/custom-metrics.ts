import { UtrValueType } from '../../models/public/universalTrackerType';
import { NumberScale, SupportedMeasureUnits } from '../../service/units/unitTypes';
import { z } from 'zod';

export const customMetricUpdateDtoSchema = z
  .object({
    unitType: z.preprocess((value) => {
      if (typeof value === 'string' && value === '') {
        return undefined;
      }

      return value;
    }, z.nativeEnum(SupportedMeasureUnits).optional()),
    valueType: z.nativeEnum(UtrValueType),
    numberScale: z.preprocess((value) => {
      if (typeof value === 'string' && value === '') {
        return undefined;
      }

      return value;
    }, z.nativeEnum(NumberScale).optional()),
    // TODO: add other fields.
  })
  .passthrough();
