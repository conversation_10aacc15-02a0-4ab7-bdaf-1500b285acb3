import { RequesterType } from '../../models/dataShare';
import { z } from 'zod';
import { stringIdSchema } from './common';

export const requesterDtoSchema = z.object({
  requesterId: stringIdSchema('requesterId'),
  initiativeId: stringIdSchema('initiativeId'),
  requesterType: z.nativeEnum(RequesterType),
});

export const extendedRequesterDtoSchema = requesterDtoSchema.extend({
  surveyId: stringIdSchema('surveyId'),
});

export const optionalExtendedRequesterDtoSchema = requesterDtoSchema.extend({
  surveyId: stringIdSchema('surveyId').optional(),
});