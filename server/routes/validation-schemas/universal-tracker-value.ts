import { z } from 'zod';

export const filesMetadataSchema = z.object({
  filesMetadata: z
    .object({
      id: z.coerce.number(),
      saveToLibrary: z.string().transform((saveToLibrary) => (saveToLibrary === 'true' ? true : false)),
      description: z.string(),
    })
    .array()
    .optional(),
});

export const tableDataSchema = z.object({
  tableData: z
    .array(
      z.array(
        z.object({
          code: z.string(),
          value: z.union([z.string(), z.number(), z.array(z.string())]).optional(),
          unit: z.string().optional(),
          numberScale: z.string().optional(),
        })
      )
    )
    .min(1),
});
