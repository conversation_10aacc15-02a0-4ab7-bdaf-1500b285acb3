/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import express from 'express';
import xmlparser from 'express-xml-bodyparser';
import { OutboundMessage } from '../../service/crm/Salesforce/OutboundMessage';
import { Account, Notification } from '../../service/crm/Salesforce/types';
import { wwgLogger } from '../../service/wwgLogger';
import { Analytics, AnalyticsEvents } from '../../analytics';
import config from '../../config';
import StripeClient from '../../service/payment/StripeClient';
import BadRequestError from '../../error/BadRequestError';
import { getEventHandler } from "../../service/payment/EventHandler";
import AwsSesEmailCommand from "../../command/AwsSesEmailCommand";
import AwsSesWorkerFactory from "../../service/email/AwsSesWorkerFactory";
import HttpError from "../../error/HttpError";
import { webhookAuth } from '../../middleware/authentication';
import { createQldbCommand } from '../../service/ledger/QldbCommand';
import { UniversalTrackerRepository } from '../../repository/UniversalTrackerRepository';
import { getOnboardingScheduler } from '../../service/onboarding/OnboardingScheduler';
import { ObNotificationCode } from '../../models/onboarding';
import { getSgxExport } from '../../service/data/sgx/SgxExport';
import { ConfigurationVariableSetup } from '../../survey/compositeUtrConfigs';
import { UniversalTrackerModel } from '../../models/universalTracker';
import { UtrValueType } from '../../models/public/universalTrackerType';
import { MailchimpService } from '../../service/email/MailChimpService';
import { OnboardingMailchimpSync } from '../../service/onboarding/OnboardingMailchimpSync';
import moment from 'moment';
import { getRootInitiativeService } from '../../service/organization/RootInitiativeService';
import { getSgxIssuerManager } from '../../service/data/sgx/issuer/SgxIssuerManager';
import { getAppConfigService } from '../../service/app/AppConfigService';
import { getNotificationManager } from '../../service/notification/NotificationManager';
import { BackdateMailchimpSync } from '../../service/onboarding/BackdateMailchimpSync';
import { getScheduledNotificationService } from '../../service/scheduled-notification/ScheduledNotificationService';
import { getUserDailyNotificationService } from '../../service/scheduled-notification/DailyNotificationService';
import { getSuccessReportWorkflow } from '../../service/staff-reporting/SuccessReportWorkflow';
import { getIntegrationManager } from "../../service/integration/IntegrationManager";
import { mustValidate } from "../../util/validation";
import { z } from "zod";
import { ScheduledType } from '../../models/scheduledNotification';
import { getSponsorshipRenewalService } from '../../service/referral/SponsorshipRenewalService';
import ContextError from '../../error/ContextError';

/**
 * Router here will need to apply JSON/XML middleware manually where needed.
 */
const router = express.Router()

const eventHandler = getEventHandler();

/**
 * XML notification request for customer journey field change trigger
 *
 <?xml version="1.0" encoding="UTF-8"?>
 <soapenv:Envelope
 xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
 xmlns:xsd="http://www.w3.org/2001/XMLSchema"
 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
 <soapenv:Body>
 <notifications
 xmlns="http://soap.sforce.com/2005/09/outbound">
 <OrganizationId>00D3z000001BYGGEA4</OrganizationId>
 <ActionId>04k3z000000GxOzAAK</ActionId>
 <SessionId xsi:nil="true"/>
 <EnterpriseUrl>https://worldwidegeneration.my.salesforce.com/services/Soap/c/52.0/00D3z000001BYGG</EnterpriseUrl>
 <PartnerUrl>https://worldwidegeneration.my.salesforce.com/services/Soap/u/52.0/00D3z000001BYGG</PartnerUrl>
 <Notification>
 <Id>04l3z00001a8PT1AAM</Id>
 <sObject xsi:type="sf:Account"
 xmlns:sf="urn:sobject.enterprise.soap.sforce.com">
 <sf:Id>0013z00002PUj2qAAD</sf:Id>
 <sf:Account_Status__c>Active</sf:Account_Status__c>
 <sf:Customer_Journey__c>Pipeline</sf:Customer_Journey__c>
 <sf:G17Eco_ID__c>5babc4e326140135c24b5ad0</sf:G17Eco_ID__c>
 </sObject>
 </Notification>
 </notifications>
 </soapenv:Body>
 </soapenv:Envelope>
 */

// Account Object Props
// Name
// AccountNumber
// Customer_Journey__c
// Account_Status__c
// Company_Registration__c
// Contract_type__c
// G17Eco_ID__c
// Industry

const handler = xmlparser({
  trim: true,
  explicitArray: false,
  normalizeTags: false,
  ignoreAttrs: true,
});

const om = new OutboundMessage();
const anonymousId = config.analytics.anonymousId;

const qldbCommand = createQldbCommand();
const sesCommand = new AwsSesEmailCommand(
  AwsSesWorkerFactory(),
  config.queue.emailFeedback.queueName,
  10
);

const scheduledNotificationService = getScheduledNotificationService();
const dailyNotificationService = getUserDailyNotificationService();
const sponsorshipRenewalService = getSponsorshipRenewalService();

/** @deprecated Might be removed in if no one uses it and also remove 'express-xml-bodyparser' package */
router.route('/salesforce')
  .all(handler, (req, res) => {
    // Log as error so that we can track it on Sentry if anyone uses this endpoint
    wwgLogger.error(new ContextError('Received salesforce inbound request'));

    const events = om.processNotification(req.body)
    if (events.length === 0) {
      wwgLogger.info(`Received data for no valid events`, req.body)
    }
    // as Notification<Account>[]
    const accountEvents = events
      .filter(n => om.isAccountNotification(n))
      .map((n) => om.toAccountMessage(n as Notification<Account>));

    if (events.length !== accountEvents.length) {
      wwgLogger.info(`Received events: ${events.length}, account events: ${accountEvents.length}`);
    }

    const promises = accountEvents.map(event => Analytics.track({
      anonymousId: anonymousId,
      event: AnalyticsEvents.AccountJourney,
      properties: event,
    }).catch(e => wwgLogger.info(e)))
    Promise.all(promises).catch(e => wwgLogger.error(e))

    let msg = '';
    msg = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">';
    msg += '<soapenv:Body>';
    msg += '<notificationsResponse xmlns="http://soap.sforce.com/2005/09/outbound">';
    msg += '<Ack>true</Ack>';
    msg += '</notificationsResponse>';
    msg += '</soapenv:Body>';
    msg += '</soapenv:Envelope>';

    res.send(msg);
  });

// Webhook handler for asynchronous events.
router.post("/stripe/webhook", async (req, res, next) => {
  const webhookSecret = config.payment.stripe.webhookSecret;
  const signature = req.headers["stripe-signature"];
  if (!signature) {
    return next(new BadRequestError(`Missing signature header`))
  }

  if (!req.rawBody) {
    return next(new Error(`Missing 'rawBody' req property`))
  }

  try {
    // Retrieve the event by verifying the signature using the raw body and secret.
    const event = StripeClient.webhooks.constructEvent(req.rawBody, signature, webhookSecret);

    // Process event internally
    await eventHandler.acknowledge(event);
    res.FromModel({ eventId: event.id });
  } catch (err) {
    next(err);
  }
});

router.post("/mail/process", webhookAuth, async (req, res) => {
  sesCommand.execute().catch(e => wwgLogger.error(e))
  res.Success('started')
});

router.post("/qldb/process", webhookAuth, async (req, res) => {
  qldbCommand.execute().catch(e => wwgLogger.error(e))
  res.Success('started')
});

// Exports SGX data to SGX SFTP
router.post("/export/sgx", webhookAuth, async (req, res, next) => {
  const { initiativeIds } = <{ initiativeIds?: string[] }>req.body;
  getSgxExport().generate({ initiativeIds })
    .then((job) => {
      wwgLogger.info("SGX export hook completed");
      res.FromModel(job);
    }).catch(e => next(e))
});

router.post("/export/survey-engagement", webhookAuth, async (req, res, next) => {
  getSuccessReportWorkflow().create()
    .then((job) => {
      wwgLogger.info("Success reports generation hook completed");
      res.FromModel(job);
    }).catch(e => next(e))
});

// syncSFTPtoRemote: Pull in latest SGX Issuer file and updates remote storage
// syncToLocal: Pulls in SGX Issuer file from our remote storage and sync database.
router.post("/jobs/sgx/issuer", webhookAuth, async (req, res, next) => {
  getSgxIssuerManager().processTask(req.body)
    .then((job) => {
      wwgLogger.info("SGX export hook completed");
      res.FromModel(job);
    }).catch(e => next(e))
});

router.post('/cron/financial-end-notifications', webhookAuth, (req, res, next) => {
  getNotificationManager().financialEndDateNotification().catch(wwgLogger.error);
  res.Success('started');
});

router.post("/onboarding/notifications", webhookAuth, async (req, res) => {
  const scheduler = getOnboardingScheduler();
  const { limit } = req.body;
  scheduler.process({ notificationCode: ObNotificationCode.FirstReminder, limit })
    .then(r => wwgLogger.info(`Completed onboarding notifications ${r.length} for ${ObNotificationCode.FirstReminder}`, r))
    .then(() => scheduler.process({ notificationCode: ObNotificationCode.FinalReminder, limit }))
    .then(r => wwgLogger.info(`Completed onboarding notifications ${r.length} for ${ObNotificationCode.FinalReminder}`, r))
    .catch(e => wwgLogger.error(e))

  res.Success('started')
});

router.post("/onboarding/mailchimp", webhookAuth, async (req, res, next) => {
  try {
    const initiativeServ = getRootInitiativeService()
    const appConfigService = getAppConfigService()
    const chimp = new OnboardingMailchimpSync(
      new MailchimpService({ batchSize: 500 }),
      wwgLogger,
      appConfigService,
      initiativeServ,
      config.appEnv
    )
    // give 5 min extra slack
    const results = await chimp.process({ startDate: moment().subtract(65, 'minute'), endDate: moment() })
    res.FromModel(results);
  } catch (err) {
    next(err);
  }
});


router.post("/backdate/mailchimp", webhookAuth, async (req, res, next) => {
  try {
    const initiativeServ = getRootInitiativeService()
    const appConfigService = getAppConfigService()
    const chimp = new BackdateMailchimpSync(
      new MailchimpService({ batchSize: 500 }),
      wwgLogger,
      appConfigService,
      initiativeServ,
      config.appEnv
    )
    // give 5 min extra slack
    const results = await chimp.process({ batchSize: 100 })
    res.FromModel(results);
  } catch (err) {
    next(err);
  }
});

router.post("/deployment/pre-validation", async (req, res) => {
  const envSecret = config.deployment.authorizationToken;
  const requestSecret = req.headers.authorization;

  if (!requestSecret || requestSecret !== envSecret) {
    return res.Exception(new HttpError('Invalid authentication header', 401));
  }

  const { utrCodes, variables }: { utrCodes?: string[], variables?: Pick<ConfigurationVariableSetup, 'code' | 'valueListCode'>[] } = req.body;
  if (!utrCodes || !variables) {
    return res.Invalid('Invalid request');
  }

  let missingUtrCodes: string[] = [];
  const incorrectVariables: (Pick<ConfigurationVariableSetup, 'code' | 'valueListCode'> & { message: string; })[] = [];

  const utrs: Pick<UniversalTrackerModel, 'code' | 'valueValidation' | 'valueType'>[] = await UniversalTrackerRepository.findByCodes(
    utrCodes,
    { code: 1, valueType: 1, valueValidation: 1 }
  );

  if (utrs.length !== utrCodes.length) {
    const dbCodes = new Set(utrs.map(u => u.code));
    missingUtrCodes = utrCodes.filter(code => !dbCodes.has(code));
  }

  variables
    .forEach(variable => {
      const utr = utrs.find(dbUtr => dbUtr.code === variable.code);
      if (!utr) {
        return incorrectVariables.push({
          ...variable,
          message: 'UTR Code does not exist in the database'
        });
      }
      switch (utr.valueType) {
        case UtrValueType.Number:
        case UtrValueType.Sample:
        case UtrValueType.Percentage:
        case UtrValueType.Date:
        case UtrValueType.Text:
        case UtrValueType.ValueList:
        case UtrValueType.ValueListMulti:
        case UtrValueType.TextValueList:
          if (variable.valueListCode) {
            return incorrectVariables.push({
              ...variable,
              message: `UTR code is type ${utr.valueType} so shouldn't have any valueListCode set`
            });
          }
          return;
        case UtrValueType.NumericValueList:
          // If has a valueListCode or not, should both be valid
          return;
        case UtrValueType.Table: {
          if (!variable.valueListCode) {
            return incorrectVariables.push({
              ...variable,
              message: `UTR code is type ${utr.valueType} so must have a valueListCode set`
            });
          }
          const tableColumnCodes = utr.valueValidation?.table?.columns.map(col => col.code) ?? [];
          if (!tableColumnCodes.includes(variable.valueListCode)) {
            return incorrectVariables.push({
              ...variable,
              message: `ValueListCode does not exist in UTR. Available columns are: ${tableColumnCodes?.join(', ')}`
            });
          }
          return;
        }
      }
    });

  if (missingUtrCodes.length > 0 || incorrectVariables.length > 0) {
    return res.FromModel({
      missingUtrCodes,
      incorrectVariables
    });
  }
  return res.Success();
});

// This CRON run periodically to process scheduled works
// SurveyDeadline run daily
// SummaryEmail run hourly
router.route('/scheduled-notification/process').post(webhookAuth, async (req, res, next) => {
  try {
    const { type = ScheduledType.SurveyDeadline } = req.body;
    wwgLogger.info(`Processing scheduled notification type: ${type}`, { body: req.body });
    const data = await scheduledNotificationService.process(type);
    return res.FromModel(data);
  } catch (e) {
    next(e);
  }
});

// This CRON run daily to trigger sending user notification
router.route('/user-daily-notification/process').post(webhookAuth, async (req, res, next) => {
  try {
    const result = mustValidate(req.body, z.object({
      scheduledDate: z.object({
        startDate: z.coerce.date(),
        endDate: z.coerce.date(),
      }).optional(),
      limit: z.number().min(1).max(100).optional(),
    }));
    wwgLogger.info(`Processing daily user notification`, { body: req.body, options: result });
    const data = await dailyNotificationService.process(result);
    return res.FromModel(data);
  } catch (e) {
    next(e);
  }
});

// This CRON run hourly to run renew subscriptions process
router.route('/subscription-schedule/process').post(webhookAuth, async (req, res, next) => {
  try {
    const data = await sponsorshipRenewalService.process();
    return res.FromModel(data);
  } catch (e) {
    next(e);
  }
});

router.route('/integrations/checks').post(webhookAuth, async (req, res, next) => {
  try {
    const { providerCodes } = mustValidate(req.body, z.object({
      providerCodes: z.string().array().optional()
    }));

    const result = await getIntegrationManager().executeChecks(providerCodes);
    return res.FromModel(result);
  } catch (e) {
    next(e);
  }
});

module.exports = router
