/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { ReportingFrameworkRepository } from '../repository/ReportingFrameworkRepository';
const router = express.Router();

router.route('/map')
  .get((req, res) => {
    ReportingFrameworkRepository.getSDGToFrameworkMap()
      .then(map => res.FromModel(map))
      .catch(e => res.Exception(e));
  });

router.route('/map/adaptor')
  .get(async (req, res) => {
    ReportingFrameworkRepository.getSDGToFrameworkMapFromSurvey()
      .then(map => res.FromModel(map))
      .catch(e => res.Exception(e));
  });

module.exports = router;
