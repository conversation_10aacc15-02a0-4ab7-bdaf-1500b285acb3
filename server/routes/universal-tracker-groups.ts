/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import UniversalTrackerGroup, {
  UniversalTrackerGroupModel,
} from '../models/universalTrackerGroup';
import { InitiativePlain } from '../models/initiative';
import { InitiativeRepository } from '../repository/InitiativeRepository';
const router = express.Router();

router.route('/initiativeId/:initiativeId')
    .get(async function (req, res) {
        try {
            const parentInitiatives = await InitiativeRepository.getAllParentsById(req.params.initiativeId);
            const parentInitiative = parentInitiatives.pop();
            if (!parentInitiative) {
                return res.Invalid('Initiative not found');
            }

            const initiatives = parentInitiative.parents;
            delete ((parentInitiative as InitiativePlain).parents);
            initiatives.push(parentInitiative);

            const initiativeIds = initiatives.map(initiative => initiative._id);
            UniversalTrackerGroup.find({ initiativeId: { $in: Array.from(initiativeIds) } })
                .exec()
                .then((models) => {
                    const recursiveFind = (initiativeId: string): UniversalTrackerGroupModel[] => {
                        const matchingModels: UniversalTrackerGroupModel[] = [];
                        for (const model of models) {
                            if (model.initiativeId.toString() === initiativeId) {
                                matchingModels.push(model);
                            }
                        }
                        if (matchingModels.length > 0) {
                            return matchingModels;
                        }
                        const initiative = initiatives.find((initiativeModel) =>
                            initiativeModel._id.toString() === initiativeId);
                        if (!initiative || !initiative.parentId) {
                            return matchingModels;
                        }

                        return recursiveFind(initiative.parentId.toString());
                    };

                    res.FromModel(recursiveFind(req.params.initiativeId));
                })
                .catch((e: Error) => res.Exception(e));
        } catch (e) {
            res.Exception(e);
        }
    });

module.exports = router;
