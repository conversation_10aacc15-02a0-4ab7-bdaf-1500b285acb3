/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { InitiativeRepository } from '../repository/InitiativeRepository';
import { UserModel } from '../models/user';
import { AuthRouter } from '../http/AuthRouter';
import { getRootInitiativeService } from '../service/organization/RootInitiativeService';
import { getCustomerManager } from '../service/payment/CustomerManager';
import { trace } from "@opentelemetry/api";
import { getAppConfigService } from "../service/app/AppConfigService";
import { getAgreementService } from "../service/organization/AgreementService";

const router = express.Router() as AuthRouter;
const rootService = getRootInitiativeService();

router.route('/')
  .get(async (req, res) => {
    if (!req.user.permissions) {
      return res.Invalid('User has no linked initiatives.');
    }

    try {
      const initiativeMap = await InitiativeRepository.getInitiativeTree(req.user);
      res.FromModel(Array.from(initiativeMap));
    } catch (err) {
      res.Invalid('Error finding initiative');
    }
  });

router.route('/companies')
  .get(async (req, res, next) => {
    const ids = req.user.permissions?.map(p => p.initiativeId) ?? [];
    if (ids.length === 0) {
      return res.FromModel([]);
    }
    const customerManager = getCustomerManager();
    const appConfigService = getAppConfigService();
    const tracer = trace.getTracer('companies-list');

    try {
      const rootInitiatives = await tracer.startActiveSpan('getRootInitiativesForIds', async (span) => {

        const initiatives = await InitiativeRepository.getRootInitiativesForIds(ids);

        const codes = new Set<string>;
        initiatives.forEach(initiative => {
          // Ensure legacy ones have appConfigCode set as well
          initiative.appConfigCode = appConfigService.getAppConfigCode(initiative);
          if (initiative.appConfigCode) {
            codes.add(initiative.appConfigCode)
          }
        })

        const configs = await rootService.getConfigs(Array.from(codes));
        const configMap = new Map(configs.map((c) => [c.code, c]));

        const list = await customerManager.hydrateSubscriptions(initiatives, configMap);
        span.end();
        return list;
      });

      res.FromModel(rootInitiatives);
    } catch (err) {
      next(err)
    }
  });

router.route('/companies/:initiativeId')
  .get(async (req, res, next) => {
    InitiativeRepository.getTreeForRootInitiative(req.params.initiativeId, req.user)
      .then(async (data) => {
        const appConfig = await rootService.getAppConfig(data.root);

        const [calculatedSubscriptions, config, agreements] = await Promise.all([
          getCustomerManager().getCalculatedSubscriptions(data.root, appConfig),
          rootService.getConfig(data.root, { domain: req.header('origin') }),
          getAgreementService().getAgreementConfigs(data.root),
        ]);

        return res.FromModel({
          root: { ...data.root, calculatedSubscriptions },
          tree: data.tree,
          config,
          appConfig,
          companyAgreementsRequired: agreements,
        });
      })
      .catch(e => next(e))
  });

router.route('/portfolios')
  .get(async (req, res) => {
    try {
      const user: UserModel = req.user;
      if (!Array.isArray(user.permissions)) {
        res.FromModel([]);
        return;
      }

      const ids = user.permissions.map(p => p.initiativeId);
      const initiatives = await InitiativeRepository.getInitiativeGroupTree(ids);
      res.FromModel(initiatives);
    } catch (err) {
      res.Invalid('Error finding initiative');
    }
  });

module.exports = router;
