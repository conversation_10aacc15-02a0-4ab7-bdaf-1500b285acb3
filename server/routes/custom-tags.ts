import express from 'express';
import { ObjectId } from 'bson';

import { ContextMiddleware } from '../middleware/audit/contextMiddleware';
import { AuthRouter } from '../http/AuthRouter';
import { populateInitiative } from '../middleware/commonMiddlewares';
import MetricGroup, { MetricGroupType } from '../models/metricGroup';
import { canManageInitiative, isRootLevel } from '../middleware/initiativeMiddlewares';
import { CustomTagManager } from '../service/metric/CustomTagManager';

const router = express.Router({ mergeParams: true }) as AuthRouter;
router.use(ContextMiddleware);

router
  .route('/')
  .get(populateInitiative, (req, res, next) => {
    const initiativeId = req.params.initiativeId;
    CustomTagManager.getInitiativeCustomTags({ initiativeId })
      .then((metrics) => res.FromModel(metrics))
      .catch(next);
  })
  .post(canManageInitiative, isRootLevel, async (req, res, next) => {
    try {
      const metricGroup = await CustomTagManager.createTag({
        initiativeId: res.locals.initiativeId,
        groupName: req.body.groupName,
        userId: req.user._id,
      });
      res.FromModel(metricGroup);
    } catch (e) {
      next(e);
    }
  })
  .patch(canManageInitiative, isRootLevel, async (req, res, next) => {
    const { updatedTagIds, utrIds } = req.body;
    const initiativeId = res.locals.initiativeId;
    CustomTagManager.updateUtrsTags({ initiativeId, updatedTagIds, utrIds })
      .then((results) => res.FromModel(results))
      .catch((e: Error) => next(e));
  })
  .delete(canManageInitiative, isRootLevel, async (req, res, next) => {
    const customTagIds = (req.body.ids as string[]).map((id) => new ObjectId(id));
    const initiativeId = res.locals.initiativeId;

    try {
      await MetricGroup.deleteMany({
        _id: { $in: customTagIds },
        initiativeId: initiativeId,
        type: MetricGroupType.Tag,
      });
      res.Success(true);
    } catch (e) {
      next(e);
    }
  });

module.exports = router;
