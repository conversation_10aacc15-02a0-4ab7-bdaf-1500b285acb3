/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import ValueList from '../models/valueList';
import { getDashboardSharingService } from '../service/insight-dashboard/DashboardSharingService';
import { z } from 'zod';
import { mustValidate } from '../util/validation';
import { getObjectIdsSchema } from './validation-schemas/common';

const router = express.Router();
const dashboardSharingService = getDashboardSharingService();

router.route('/:id')
  .get((req, res) => {
    ValueList.findById(req.params.id).exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  })

router.route('/ids').post(async (req, res) => {  
  const { ids } = mustValidate(
    req.body,
    z.object({
      ids: getObjectIdsSchema(),
    })
  );
  if (ids.length === 0) {
    return res.FromModel([]);
  }
  const models = await ValueList.find({ _id: { $in: ids } })
    .lean()
    .exec();
  res.FromModel(models);
});

router.route('/:id/insight-dashboards/:dashboardId/token/:token').get(async (req, res, next) => {
  try {
    await dashboardSharingService.findSharedDashboard({ dashboardId: req.params.dashboardId, token: req.params.token });
    const valueList = await ValueList.findById(req.params.id).lean().exec();
    res.FromModel(valueList);
  } catch (e) {
    next(e);
  }
});

module.exports = router;
