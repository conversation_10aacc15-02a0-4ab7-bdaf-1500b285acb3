/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { getBlueprintRepository } from '../repository/BlueprintRepository';
const router = express.Router();

const surveyRepo = getBlueprintRepository();

router.route('/')
  .get((req, res) => {
    return surveyRepo.listAll()
      .then((r) => res.FromModel(r))
      .catch(e => res.Exception(e));
  });

router.route('/code/:code/expanded')
  .get((req, res) => {
    return surveyRepo.getExpandedBlueprintByCode(req.params.code)
      .then(r => res.FromModel(r))
      .catch(e => res.Exception(e));
  });

router.route('/code/:code')
  .get((req, res) => {
    return surveyRepo.getBlueprint(req.params.code)
      .then(r => res.FromModel(r))
      .catch(e => res.Exception(e));
  });

module.exports = router;
