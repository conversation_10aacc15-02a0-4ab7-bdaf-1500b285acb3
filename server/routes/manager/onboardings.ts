/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { AuthRouter } from '../../http/AuthRouter';
import { ContextMiddleware } from '../../middleware/audit/contextMiddleware';
import { getOnboardingEmailService } from '../../service/onboarding/OnboardingEmailService';
import { mustValidate } from '../../util/validation';
import { onboardingParamsSchema } from '../validation-schemas/onboardings';

const router = express.Router({ mergeParams: true }) as AuthRouter;

const onboardingEmailService = getOnboardingEmailService();

router.use(ContextMiddleware);

router.route('/:onboardingId/emails')
  .get(async (req, res, next) => {
    try {
      const { onboardingId, initiativeId } = mustValidate(req.params, onboardingParamsSchema);
      const result = await onboardingEmailService.getSentEmails({ onboardingId, initiativeId });
      res.FromModel(result);
    } catch (e) {
      next(e);
    }
  })
  .post(async (req, res, next) => {
    try {
      const { onboardingId, initiativeId } = mustValidate(req.params, onboardingParamsSchema);
      const updateItem = await onboardingEmailService.sendManualReminder({ onboardingId, initiativeId });
      res.Success({ referenceId: updateItem.referenceId });
    } catch (e) {
      next(e);
    }
  });

module.exports = router;
