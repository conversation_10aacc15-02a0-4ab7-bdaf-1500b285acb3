/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { AuthRouter } from '../../http/AuthRouter';
import { ContextMiddleware } from '../../middleware/audit/contextMiddleware';
import { getSponsorshipService } from '../../service/referral/SponsorshipService';
import { ObjectId } from "bson";

const router = express.Router() as AuthRouter;

router.use(ContextMiddleware);

router.route('').get(async (_req, res, next) => {
  try {
    const sponsorshipService = getSponsorshipService();
    const sponsorships = await sponsorshipService.getInitiativeSponsorships(new ObjectId(res.locals.initiativeId));
    return res.FromModel(sponsorships);
  } catch (e) {
    next(e);
  }
});

module.exports = router;
