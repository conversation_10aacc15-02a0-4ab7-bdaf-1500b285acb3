import express from 'express';
import { AuthRouter } from '../../http/AuthRouter';
import { ContextMiddleware } from '../../middleware/audit/contextMiddleware';
import { getInitiativeExportWorkflow } from '../../service/initiative/export/InitiativeExportWorkflow';
import { ObjectId } from 'bson';
import { mustValidate } from '../../util/validation';
import { z } from 'zod';
import { idSchema } from '../validation-schemas/common';

const router = express.Router() as AuthRouter;
router.use(ContextMiddleware);

const exportWorkflow = getInitiativeExportWorkflow();

router
  .route('/full')
  .get(async (req, res, next) => {
    exportWorkflow
      .findLatestJob(new ObjectId(res.locals.initiativeId as string))
      .then((response) => {
        res.FromModel(response);
      })
      .catch((e) => {
        next(e);
      });
  })
  .post(async (req, res, next) => {
    const initiativeId = new ObjectId(res.locals.initiativeId as string);
    exportWorkflow
      .create({ initiativeId, userId: req.user._id })
      .then((response) => {
        res.FromModel(response);
      })
      .catch((e) => {
        next(e);
      });
  });

router.route('/signed-url').post(async (req, res, next) => {
  try {
    const { jobId } = mustValidate(
      req.body,
      z.object({
        jobId: idSchema.transform((strId) => new ObjectId(strId)),
      })
    );
    const url = await exportWorkflow.getBundleSignedUrl(jobId);

    return res.FromModel({ url });
  } catch (e) {
    return next(e);
  }
});

module.exports = router;
