/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { clearCache } from '../../service/cache';
import { getPortfolioOnboardingManager } from '../../service/portfolio/PortfolioOnboardingManager';
import { AuthRouter } from '../../http/AuthRouter';
import { ContextMiddleware } from '../../middleware/audit/contextMiddleware';
import { populatePortfolio } from '../../middleware/portfolioMiddlewares';
import { getPortfolioSponsorshipService } from '../../service/portfolio/PortfolioSponsorshipService';
import { InitiativePlain } from '../../models/initiative';
import { getCustomerManager } from '../../service/payment/CustomerManager';
import { getInitiativeRepository } from '../../repository/InitiativeRepository';
import { wwgLogger } from '../../service/wwgLogger';
import { z } from 'zod';
import { mustValidate } from '../../util/validation';
import ContextError from "../../error/ContextError";
import { getSponsorshipManager } from "../../service/referral/SponsorshipManager";
import Sponsorship, { ExtendedSponsorshipModel } from "../../models/sponsorship";
import { checkIsStaff } from "../../middleware/userMiddlewares";
import UserError from "../../error/UserError";

const router = express.Router({ mergeParams: true }) as AuthRouter;
router.use(populatePortfolio, ContextMiddleware);

const portfolioOnboarding = getPortfolioOnboardingManager();
const subscriptionService = getPortfolioSponsorshipService();

router.route('/onboard')
  .post(async (req, res) => {
    try {
      const portfolio = res.locals.portfolio;
      const company = await portfolioOnboarding.processOnboarding(portfolio, req.body, req.user);
      clearCache();
      res.FromModel(company);
    } catch (e) {
      res.Exception(e);
    }
  });

router.route('/onboard/import')
  .post(async (req, res) => {
    try {
      if (!Array.isArray(req.body.companies)) {
        res.Invalid(`Missing 'companies' array in post body`)
        return;
      }

      const portfolio = res.locals.portfolio;
      const results = await portfolioOnboarding.onboardCompanies(portfolio, req.body, req.user);
      clearCache();
      res.FromModel(results);
    } catch (e) {
      res.Exception(e);
    }
  })

router.route('/sponsored-companies/referrer-codes')
  .get((_req, res, next) => {
    subscriptionService.getReferralCodes(res.locals.portfolio)
      .then(codes => res.FromModel(codes))
      .catch(next)
  })

router.route('/sponsored-companies/referrer-codes/:referrerCode')
  .get(async (req, res, next) => {
    try {
      const requestPromoCode = req.params.referrerCode;
      const promoCodes = await subscriptionService.getReferralCodes(res.locals.portfolio);
      if (!promoCodes.includes(requestPromoCode)) {
        return res.FromModel([]);
      }
      const results = await subscriptionService.getSponsoredCompanies(res.locals.portfolio, requestPromoCode);
      res.FromModel(results);
    } catch (e) {
      next(e);
    }
  })

router.route('/sponsored-companies/referrer-codes/:referrerCode/sponsor/:initiativeId')
  .post(async (req, res, next) => {
    try {
      const { initiativeId, referrerCode } = req.params;
      const results = await subscriptionService.sponsorCompany({
        portfolio: res.locals.portfolio as InitiativePlain,
        initiativeId,
        referralCode: referrerCode,
      });
      res.FromModel(results);
    } catch (e) {
      next(e);
    }
  })
  .delete(async (req, res, next) => {
    try {
      const { initiativeId, referrerCode } = req.params;
      const portfolio = res.locals.portfolio;
      const results = await subscriptionService.unSponsorCompany({
        portfolio,
        initiativeId,
        referralCode: referrerCode,
      });
      res.FromModel(results);
    } catch (e) {
      next(e);
    }
  })

router.route("/sponsored-companies/refresh/:initiativeId")
  .get(async (req, res, next) => {
    try {
      // Beware of Stripe rate-limit (100ops/second)
      // https://stripe.com/docs/rate-limits

      const customerManager = getCustomerManager();
      const initiative = await getInitiativeRepository().mustFindById(req.params.initiativeId);
      const initiativeWithCustomer = await customerManager.getInitiativeWithCustomer(initiative, req.user);
      const customer = initiativeWithCustomer.customer;

      // Note: this only refresh remote, but in case remote have been deleted
      // it will not delete local version, therefore in Stripe test environment
      // it's not a proper full sync, where remote subs are deleted every ~3 months...
      const subscriptions = await customerManager.getRemoteSubscriptions({ _id: initiative._id, customer });
      const subData = subscriptions.data.map((sub) => ({ id: sub.id, status: sub.status }));

      wwgLogger.info(`Refreshing subscriptions ${subscriptions.data.length} for ${initiative.name}:`, {
        initiativeId: initiative._id,
        initiativeCode: initiative.code,
        appConfigCode: initiative.appConfigCode,
        customerId: customer.id,
        subscriptions: subData,
      });

      // Update the subscriptions in serial to avoid collisions as they update same model
      for (const sub of subscriptions.data) {
        try {
          await customerManager.updateSubscription(initiativeWithCustomer, sub.id);
        } catch (e) {
          wwgLogger.error(new ContextError(`Subscription ${sub.id} refresh error`, {
            initiativeId: initiative._id,
            initiativeCode: initiative.code,
            cause: e,
          }));
        }
      }

      res.FromModel({
        finished: true,
        subscriptions: subData,
      });
    } catch (e) {
      next(e);
    }
  })

router.route("/sponsored-companies/:initiativeId/sponsorships/:sponsorshipId/renew")
  .post(checkIsStaff, async (req, res, next) => {
    try {
      const portfolio = res.locals.portfolio as InitiativePlain;
      const sponsorshipId = req.params.sponsorshipId;
      const sponsorshipManager = getSponsorshipManager();
      const sponsorship = await Sponsorship.findById(sponsorshipId)
        .populate<ExtendedSponsorshipModel>('sponsorshipConfig')
        .orFail()
        .exec();

      if (sponsorship.initiativeId.toString() !== req.params.initiativeId) {
        return next(new UserError('Sponsorship does not belong to the company', {
          initiativeId: req.params.initiativeId,
          sponsorshipId: sponsorshipId,
          sponsorshipInitiativeId: sponsorship.initiativeId,
        }));
      }

      if (portfolio.code !== sponsorship.sponsorshipConfig?.sponsorCode) {
        return next(new UserError('Portfolio does not match sponsorship sponsor code', {
          portfolioCode: portfolio.code,
          sponsorshipSponsorCode: sponsorship.sponsorshipConfig?.sponsorCode,
        }));
      }

      if (!sponsorshipManager.shouldRenew(sponsorship) && !req.body.forceRenew) {
        return next(new UserError('Sponsorship is not due for renewal.', {
          status: 400,
          autoRenew: sponsorship.sponsorshipConfig?.autoRenew,
          cancelDate: sponsorship.cancelDate,
          periodEndDate: sponsorship.periodEndDate,
        }));
      }

      const periodEndDate = sponsorship.periodEndDate;
      const updatedModel = await sponsorshipManager.renew(sponsorship)
      res.FromModel({
        message: 'Sponsorship renewed successfully',
        sponsorship: updatedModel,
        periodEndDate: {
          before: periodEndDate,
          after: updatedModel.periodEndDate,
        }
      });
    } catch (e) {
      next(e);
    }
  })

router.route('/sponsored-companies/referrer-codes/:referralCode/config')
  .get(async (req, res, next) => {
    try {
      const results = await subscriptionService.getSponsorshipConfig({
        portfolio: res.locals.portfolio as InitiativePlain,
        referralCode: req.params.referralCode,
      });
      res.FromModel(results);
    } catch (e) {
      next(e);
    }
  })
  .post(async (req, res, next) => {
    try {
      const validateSchema = z.object({
        autoRenew: z.boolean(),
      });
      const { autoRenew } = mustValidate(req.body, validateSchema);

      const results = await subscriptionService.updateSponsorshipConfig({
        portfolio: res.locals.portfolio as InitiativePlain,
        referralCode: req.params.referralCode,
        data: { autoRenew }
      });
      res.FromModel(results);
    } catch (e) {
      next(e);
    }
  })


module.exports = router;
