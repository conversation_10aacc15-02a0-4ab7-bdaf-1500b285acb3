import express from 'express';
import { getCustomMetricManager } from '../../service/custom-metrics/CustomMetricManager';
import { AuthRouter } from '../../http/AuthRouter';
import { ObjectId } from 'bson';
import FileUpload from '../../http/FileUpload';
import UserError from '../../error/UserError';
import { checkIsStaff } from '../../middleware/userMiddlewares';
import { ContextMiddleware } from '../../middleware/audit/contextMiddleware';

const router = express.Router() as AuthRouter;
const customMetricManager = getCustomMetricManager();

router.route('/').get((req, res, next) => {
  if (!res.locals.initiativeId) {
    return res.Invalid();
  }
  const initiativeId = String(res.locals.initiativeId);
  customMetricManager
    .getCustomMetricsByInitiativeId(initiativeId)
    .then((result) => {
      res.FromModel(result);
    })
    .catch(next);
});

router.route('/export').get(checkIsStaff, (req, res, next) => {
  if (!res.locals.initiativeId) {
    return res.Invalid();
  }

  const initiativeId = String(res.locals.initiativeId);

  customMetricManager.createExportByInitiativeId(new ObjectId(initiativeId))
    .then((zip) => {
      // Set the headers
      res.writeHead(200, {
        'Content-Type': 'application/zip',
        'Content-disposition': 'attachment; filename=custom-metrics.g17'
      });
      return zip
        .generateNodeStream({ type: 'nodebuffer', streamFiles: true })
        .pipe(res)
        .on('finish', () => res.end())
        .on('error', next);
      }
    )
    .catch(next);
});

router.route('/import').patch(checkIsStaff, FileUpload.single('file'), (req, res, next) => {
  const file = req.file;
  if (!file || !res.locals.initiativeId) {
    return res.Invalid();
  }

  const initiativeId = res.locals.initiativeId;
  customMetricManager
    .importDumpToInitiativeId(new ObjectId(initiativeId), file)
    .then((result) => {
      res.FromModel(result);
    })
    .catch(e => {
      switch(e.code) {
        case 11000:
          return res.Exception(new UserError('Duplicate custom metric. This tool can only be used to copy metrics from a different environment.'));
      }
      return next(e);
    });
});

router.route('/usage').get(ContextMiddleware, async (req, res, next) => {
  const initiativeId = res.locals.initiativeId; // Parsed by middleware
  try {
    const usage = await customMetricManager.getCustomMetricsUsageByInitiative(initiativeId);
    res.FromModel(usage);
  } catch (e) {
    next(e);
  }
});

module.exports = router;
