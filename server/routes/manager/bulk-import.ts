/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import express from 'express';
import { AuthenticatedInitiativeRouter } from '../../http/AuthRouter';
import { getBulkFileImportWorkflow } from "../../service/survey/workflow/BulkFileImportWorkflow";
import { ObjectId } from "bson";
import PermissionDeniedError from "../../error/PermissionDeniedError";
import { getBackgroundJobService } from "../../service/background-process/BackgroundJobService";
import { wwgLogger } from "../../service/wwgLogger";
import FileUpload from "../../http/FileUpload";
import { getBulkFileImporter } from "../../service/survey/BulkFileImporter";
import { clearCache } from "../../service/cache";
import ContextError from "../../error/ContextError";
import { FileParserType } from '../../service/survey/transfer/parserTypes';
import { write } from '@sheet/core';
import { JobStatus } from '../../models/backgroundJob';
import { setXlsxFileHeaders } from '../../http/FileDownload';

const router = express.Router() as AuthenticatedInitiativeRouter;


router.route('/jobs')
  .get(async (_req, res, next) => {
    try {
      const bulkFileImportWorkflow = getBulkFileImportWorkflow();
      const jobs = await bulkFileImportWorkflow.listInitiativeJobs(res.locals.initiativeId);
      res.FromModel(jobs);
    } catch (e) {
      next(e)
    }
  })
  .post(FileUpload.single('file'), async (req, res, next) => {
    if (!req.file?.path) {
      return next(new ContextError(`Missing required import file`));
    }
    try {
      const bulkFileImportWorkflow = getBulkFileImportWorkflow();
      const result = await bulkFileImportWorkflow.create({
        initiativeId: new ObjectId(res.locals.initiativeId),
        idempotencyKey: req.body.idempotencyKey,
        user: req.user,
        file: req.file
      })
      res.FromModel(result)
    } catch (e) {
      next(e)
    }
  })

router.route('/jobs/:jobId')
  .post(async (req, res, next) => {
    try {
      const bulkFileImportWorkflow = getBulkFileImportWorkflow();
      const job = await bulkFileImportWorkflow.findJob(req.params.jobId);
      if (job.initiativeId?.toString() !== res.locals.initiativeId) {
        return next(new PermissionDeniedError())
      }
      // Update status when job is waiting for start
      job.status = JobStatus.WaitForRun;
      await job.save();

      const bgJobService = getBackgroundJobService();
      bgJobService.runFromJob(job).catch(wwgLogger.error)
      res.FromModel({
        jobId: req.params.jobId,
        jobType: job.type,
        status: job.status,
      });
    } catch (e) {
      next(e)
    }
  })
  .delete(async (req, res, next) => {
    try {
      const bulkFileImportWorkflow = getBulkFileImportWorkflow();
      const job = await bulkFileImportWorkflow.findJob(req.params.jobId);
      if (job.initiativeId?.toString() !== res.locals.initiativeId) {
        return next(new PermissionDeniedError())
      }
      job.status = JobStatus.Deleted;
      job.deletedDate = new Date();
      await job.save();

      res.FromModel(job)
    } catch (e) {
      next(e)
    }
  })

router.route('/validate')
  .post(FileUpload.single('file'), async (req, res, next) => {
    if (!req.file?.path) {
      return next(new ContextError(`Missing required import file`));
    }
    try {
      const importer = getBulkFileImporter()
      const results = await importer.parseFile(res.locals.initiativeId, req.file.path);
      res.FromModel({ errors: results.errors, validRows: results.validRows })
    } catch (e) {
      next(e)
    }
  })

router.route('/file')
  .post(FileUpload.single('file'), async (req, res, next) => {
    if (!req.file) {
      return next(new ContextError('Missing required import file'));
    }
    try {
      const importer = getBulkFileImporter();
      const { surveysLoaded, validRows } = await importer.parseFile(
        res.locals.initiativeId,
        req.file.path,
        req.body.effectiveDate
      );

      const results = await importer.import({
        surveysLoaded: surveysLoaded,
        validRows: validRows,
        user: req.user,
      })
      clearCache();
      res.FromModel(results);
    } catch (e) {
      next(e);
    }
  })

router.route('/export/xlsx').get(async (req, res, next) => {
  try {
    const exportType = FileParserType.Xlsx;
    const importer = getBulkFileImporter();

    const workbook = await importer.exportBulkImportTemplate(res.locals.initiativeId as string);
    const filename = `Bulk import sheet template.${exportType}`;
    setXlsxFileHeaders(res, filename);
    return res.send(write(workbook, { type: 'buffer', bookType: exportType, cellStyles: true }));
  } catch (e) {
    next(e);
  }
});


module.exports = router;
