/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import express, { RequestHandler } from 'express';
import { AuthenticatedInitiativeRouter } from '../../http/AuthRouter';
import UserError from '../../error/UserError';
import BadRequestError from '../../error/BadRequestError';
import { ObjectId } from 'bson';
import PermissionDeniedError from '../../error/PermissionDeniedError';
import { getInitiativeRepository, InitiativeRepository } from '../../repository/InitiativeRepository';
import Initiative, { isOrganization } from "../../models/initiative";
import { getCustomerManager } from "../../service/payment/CustomerManager";
import StripeClient from "../../service/payment/StripeClient";
import { UrlMapper } from '../../service/url/UrlMapper';
import { getProductManager } from '../../service/payment/ProductManager';
import { wwgLogger } from '../../service/wwgLogger';
import { getRootInitiativeService } from '../../service/organization/RootInitiativeService';
import { getUpgradeService } from "../../service/organization/UpgradeService";
import { isFeatureCode } from "../../service/payment/subscriptionCodes";
import { mustValidate } from "../../util/validation";
import { upgradeSubscriptionSchema } from "../validation-schemas/subscription";

const router = express.Router() as AuthenticatedInitiativeRouter;
const customerManager = getCustomerManager();
const productManager = getProductManager();
const rootInitiativeService = getRootInitiativeService();

const getInitiative: RequestHandler = (req, res, next) => {
  const initiativeId = res.locals.initiativeId;
  if (!ObjectId.isValid(initiativeId)) {
    next(new PermissionDeniedError(`InitiativeId is not valid: '${initiativeId}'`))
    return;
  }

  InitiativeRepository.getInitiativeById(initiativeId).then((initiative) => {
    if (!initiative) {
      next(new PermissionDeniedError())
      return;
    }

    if (!isOrganization(initiative)) {
      next(new BadRequestError(`You cannot manage products at this reporting level`))
      return;
    }
    req.initiative = initiative;
    next();
  }).catch((e: Error) => next(e));
};


router.use(getInitiative);


router.route("/upgrade/:featureCode/preview")
  .post(async (req, res, next) => {
    try {
      if (!isFeatureCode(req.params.featureCode)) {
        return next(new BadRequestError(`You cannot upgrade this feature`))
      }
      // Need initiative model for saving
      const initiative = await Initiative.findById(req.initiative._id).orFail().exec();
      if (!customerManager.isInitiativeWithCustomer(initiative)) {
        return next(new BadRequestError(`You cannot manage products at this reporting level`))
      }

      const data = mustValidate(req.body, upgradeSubscriptionSchema)
      const details = await getUpgradeService().preview(initiative, data, req.params.featureCode);
      res.FromModel(details)
    } catch (e) {
      next(e);
    }
  })

router.route("/upgrade/:featureCode")
  .get(async (req, res, next) => {
    try {
      if (!isFeatureCode(req.params.featureCode)) {
        return next(new BadRequestError(`You cannot upgrade this feature`))
      }
      if (!req.initiative.customer) {
        return next(new BadRequestError(`You cannot manage products at this reporting level`))
      }

      const details = await getUpgradeService().getCurrentDetails(req.initiative, req.params.featureCode);
      res.FromModel(details)
    } catch (e) {
      next(e);
    }
  })
  .post(async (req, res, next) => {
    try {
      if (!isFeatureCode(req.params.featureCode)) {
        return next(new BadRequestError(`You cannot upgrade this feature`))
      }
      // Need initiative model for saving
      const initiative = await Initiative.findById(req.initiative._id).orFail().exec();
      if (!customerManager.isInitiativeWithCustomer(initiative)) {
        return next(new BadRequestError(`You cannot manage products at this reporting level`))
      }

      const data = mustValidate(req.body, upgradeSubscriptionSchema)
      const details = await getUpgradeService().upgrade(initiative, data, req.params.featureCode);
      res.FromModel(details)
    } catch (e) {
      next(e);
    }
  })

router.route("/checkout-setup/")
  .post(async (req, res, next) => {
    try {
      let customer = req.initiative.customer
      if (!customer) {
        const initiative = await getInitiativeRepository().mustFindById(req.initiative._id);
        customer = await customerManager.getInitiativeCustomer(initiative, req.user);
      }

      const initiativeReportUrl = UrlMapper.surveyList(req.initiative)
      const session = await StripeClient.checkout.sessions.create({
        mode: "setup",
        payment_method_types: ["card"],
        customer: customer?.id,
        success_url: `${initiativeReportUrl}?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: initiativeReportUrl,
      });
      if (!session.url) {
        return next(new UserError(`Unable to create setup session`))
      }
      res.FromModel({ url: session.url })
    } catch (e) {
      next(e);
    }
  })

// Fetch the Checkout Session to display the JSON result on the success page
router.route("/checkout-session{/:productCode}")
  .post(async (req, res, next) => {

    const { returnUrl, productCodes = [], } = req.body;
    const paramCode = req.params.productCode ? [req.params.productCode] : []; // Fallback to single
    const buyCodes: string[] = productCodes.length > 0 ? productCodes : paramCode;

    const productCode = productManager.convertToSubscriptionCode(buyCodes);

    wwgLogger.info(`Start checkout session for productCode ${productCode}`, {
      buyCodes,
      productCode,
      initiativeId: String(req.initiative._id),
      customerId: req.initiative.customer?.id,
    });

    // Check if sub already active
    const subs = await customerManager.getSubscriptions(req.initiative._id);
    if (customerManager.hasStatusForProduct(productCode, ['active'], subs)) {
      return next(new UserError(`Reporting level already have active subscription for this product`))
    }

    // Create new Checkout Session for the order
    // Other optional params include:
    // [customer] - if you have an existing Stripe Customer ID
    // [customer_email] - lets you prefill the email input in the form
    // For full details see https://stripe.com/docs/api/checkout/sessions/create
    try {
      const initiative = await getInitiativeRepository().mustFindById(req.initiative._id);
      const customer = await customerManager.getInitiativeCustomer(initiative, req.user);

      // From available referrals, extract best non used trial code
      const referralCode = productManager.selectBestTrialCode(productCode, initiative.referrals)

      const domain = req.header('origin')
      const domainConfig = await rootInitiativeService.getDomainConfig({ domain });
      const initiativeReportUrl = returnUrl ? UrlMapper.relativeUrl(returnUrl, domain) : UrlMapper.surveyList(req.initiative, domain)
      const session = await customerManager.createCheckoutSession({
        userId: String(req.user.id),
        initiative,
        customer,
        subscriptions: subs,
        successUrl: `${initiativeReportUrl}?checkout=success&session_id={CHECKOUT_SESSION_ID}`,
        cancelUrl: initiativeReportUrl,
        productCode,
        domainConfig,
        referralCode,
      });

      if (!session.url) {
        return next(new UserError(`Unable to create checkout session`))
      }
      res.FromModel({ url: session.url })
    } catch (e) {
      next(e);
    }
  })
  .get(async (req, res, next) => {
    const { sessionId } = req.query;
    if (typeof sessionId !== 'string') {
      return next(new BadRequestError(`Missing required sessionId query parameter`))
    }
    const session = await StripeClient.checkout.sessions.retrieve(sessionId);
    res.send(session);
  });

router.route('/customer-portal')
  .post(async (req, res, next) => {
    try {
      const customerId = req.initiative.customer?.id
      if (!customerId) {
        return next(new BadRequestError(`Company does not have any subscriptions`))
      }

      const { returnUrl } = req.body;
      const domain = req.header('origin');

      // This is the url to which the customer will be redirected when they are done
      // managing their billing with the portal.
      const url = returnUrl ? UrlMapper.relativeUrl(returnUrl, domain) : UrlMapper.profile(req.initiative, domain);
      const portalSession = await StripeClient.billingPortal.sessions.create({
        customer: customerId,
        return_url: url,
      });

      res.FromModel({ url: portalSession.url });
    } catch (e) {
      next(e)
    }
  });

router.route('/add-payment-details')
  .post(async (req, res, next) => {
    try {
      const customerId = req.initiative.customer?.id
      if (!customerId) {
        return next(new BadRequestError(`Company does not have any subscriptions`))
      }

      const { returnUrl } = req.body;
      const domain = req.header('origin');

      // This is the url to which the customer will be redirected when they are done
      const url = typeof returnUrl === 'string' && returnUrl ?
        UrlMapper.relativeUrl(returnUrl, domain) :
        UrlMapper.profile(req.initiative, domain);

      const portalSession = await StripeClient.checkout.sessions.create({
        customer: customerId,
        payment_method_types: ['card'],
        mode: 'setup',
        success_url: `${url}?checkout=success&session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: url,
      });

      res.FromModel({ url: portalSession.url });
    } catch (e) {
      next(e)
    }
  });

module.exports = router;
