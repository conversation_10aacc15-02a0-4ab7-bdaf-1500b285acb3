import express from 'express';
import { AuthRouter } from '../../http/AuthRouter';
import { getAppIntegrationService } from '../../service/app-integration/AppIntegrationService';
import { InitiativeModel } from '../../models/initiative';

const appIntegrationService = getAppIntegrationService();

const router = express.Router({ mergeParams: true }) as AuthRouter;

router.route('/company-tracker').post(async (req, res) => {
  const initiative = res.locals.initiative as InitiativeModel;
  const domain = req.header('origin');
  const result = await appIntegrationService.addCompanyTracker({
    initiative,
    user: req.user,
    domain,
  });
  res.FromModel(result);
});

router.route('/materiality-tracker').post(async (req, res) => {
  const initiative = res.locals.initiative as InitiativeModel;
  const result = await appIntegrationService.addMaterialityTracker({
    initiative,
    user: req.user,
  });
  res.FromModel(result);
});

module.exports = router;
