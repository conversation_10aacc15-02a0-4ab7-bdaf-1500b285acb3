/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import express from 'express';
import { AuthRouter } from '../../http/AuthRouter';
import { populatePortfolio } from '../../middleware/portfolioMiddlewares';
import { InitiativePlain } from '../../models/initiative';
import {
  InsightDashboard,
  InsightDashboardModel,
  InsightDashboardPlain,
  InsightDashboardType,
} from '../../models/insightDashboard';
import { getDashboardService } from '../../service/insight-dashboard/DashboardService';
import { getUtrvFiltersFromDashboardFilters, toPreloadOptions } from '../../service/insight-dashboard/utils';
import { getPortfolioDashboardService } from '../../service/portfolio/PortfolioDashboardService';
import { mustValidate } from '../../util/validation';
import { createPortfolioInsightDashboardDtoSchema, updatePortfolioInsightDashboardDtoSchema } from '../validation-schemas/insight-dashboards';
import { getUtrsHistoryDtoSchema } from '../validation-schemas/universal-trackers';
import FileUpload from '../../http/FileUpload';
import { getDashboardItemManager } from '../../service/insight-dashboard/DashboardItemManager';
import { refineIdSchema } from '../validation-schemas/common';
import { z } from 'zod';

const router = express.Router({ mergeParams: true }) as AuthRouter;
const portfolioDashboardService = getPortfolioDashboardService();
const dashboardService = getDashboardService();
const dashboardItemManager = getDashboardItemManager();

router
  .route('/')
  .get(async (req, res, next) => {
    try {
      const portfolioId = new ObjectId(req.params.portfolioId);

      const dashboards = await InsightDashboard.find({
        initiativeId: portfolioId,
        type: { $in: [InsightDashboardType.Custom, null] },
      }).exec();

      return res.FromModel(dashboards);
    } catch (e) {
      next(e);
    }
  })
  .post(async (req, res, next) => {
    try {
      const body = mustValidate(req.body, createPortfolioInsightDashboardDtoSchema);
      const creatorId = req.user._id;
      const initiativeId = new ObjectId(req.params.portfolioId);
      const { title, filters, items } = body;

      const result = await InsightDashboard.create({ creatorId, initiativeId, title, filters, items });

      return res.FromModel(result);
    } catch (e) {
      next(e);
    }
  });

router
  .route('/:dashboardId')
  .get(populatePortfolio, async (req, res, next) => {
    try {
      const dashboard = await InsightDashboard.findOne({
        _id: req.params.dashboardId,
        initiativeId: req.params.portfolioId,
      })
        .orFail()
        .lean<InsightDashboardPlain>()
        .exec();

      const portfolio = res.locals.portfolio as InitiativePlain;

      const utrPopulatedDashboardItems = await portfolioDashboardService.populateUtrDataByPortfolioId({
        dashboard,
        portfolio,
        filters: toPreloadOptions(req.query),
      });

      const dashboardWithFiles = await dashboardItemManager.populateFiles(utrPopulatedDashboardItems);

      const dashBoardWithScoreCard = await portfolioDashboardService.populateScorecard(dashboardWithFiles, portfolio);
      return res.FromModel(dashBoardWithScoreCard);
    } catch (e) {
      next(e);
    }
  })
  .put(async (req, res, next) => {
    try {
      const { dashboardId, portfolioId } = mustValidate(req.params, z.object({
        dashboardId: refineIdSchema('dashboardId'),
        portfolioId: refineIdSchema('portfolioId'),
      }));
      const dashboard = await InsightDashboard.findOne({
        _id: dashboardId,
        initiativeId: portfolioId,
      })
        .orFail()
        .exec();
      const updates = mustValidate(req.body, updatePortfolioInsightDashboardDtoSchema);
      await portfolioDashboardService.ensureOneDefaultDashboard({
        displayAsDefault: updates.filters.displayAsDefault,
        portfolioId,
        dashboardId,
      });
      const result = await dashboardService.update(dashboard, updates);

      return res.FromModel(result);
    } catch (e) {
      next(e);
    }
  })
  .delete(async (req, res, next) => {
    try {
      const dashboardId = new ObjectId(req.params.dashboardId);
      const result = await dashboardService.delete(dashboardId);

      return res.FromModel(result);
    } catch (e) {
      next(e);
    }
  });

router.route('/:dashboardId/duplicate').post(async (req, res, next) => {
  try {
    const dashboard: InsightDashboardModel = await InsightDashboard.findOne({
      _id: req.params.dashboardId,
      initiativeId: req.params.portfolioId,
    })
      .orFail()
      .exec();

    const result = await dashboardService.duplicate(dashboard, req.user._id);
    return res.FromModel(result);
  } catch (e) {
    next(e);
  }
});

router.route('/utrs-history').post(populatePortfolio, async (req, res, next) => {
  try {
    const body = mustValidate(req.body, getUtrsHistoryDtoSchema);
    const { utrCodes } = body;

    const portfolioDashboardService = getPortfolioDashboardService();

    const utrsData = await portfolioDashboardService.getUtrsHistoricalData({
      portfolio: res.locals.portfolio as InitiativePlain,
      utrCodes,
      filters: getUtrvFiltersFromDashboardFilters({ filters: body.filters }),
    });

    res.FromModel(utrsData);
  } catch (e) {
    next(e);
  }
});

router.route('/:dashboardId/upload').post(FileUpload.any(), async (req, res, next) => {
  try {
    const result = await dashboardService.uploadFiles(new ObjectId(req.params.dashboardId), req.user._id, req.files);

    return res.FromModel(result);
  } catch (e) {
    next(e);
  }
});

module.exports = router;
