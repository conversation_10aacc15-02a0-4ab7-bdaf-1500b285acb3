import express from 'express';
import { AuthRouter } from '../../http/AuthRouter';
import { getInitiativeDataShareService } from '../../service/initiative/InitiativeDataShareService';
import { ContextMiddleware } from '../../middleware/audit/contextMiddleware';
import UserError from '../../error/UserError';
import { InitiativeDataShare } from '../../models/initiative';

const router = express.Router() as AuthRouter;
router.use(ContextMiddleware);

const initiativeDataShareService = getInitiativeDataShareService();

router.route('/').get(async (req, res, next) => {
  try {
    const sharesWithRequesters = await initiativeDataShareService.getDataSharesWithRequesters(res.locals.initiativeId);
    return res.FromModel(sharesWithRequesters);
  } catch (e) {
    next(e);
  }
});

router
  .route('/template')
  .get(async (req, res, next) => {
    try {
      const dataShareTemplate = await initiativeDataShareService.getDataShare(res.locals.initiativeId);
      return res.FromModel(dataShareTemplate);
    } catch (e) {
      next(e);
    }
  })
  .patch(async (req, res, next) => {
    try {
      const updateDataShare = req.body as InitiativeDataShare;
      if (!updateDataShare) {
        throw new UserError('Update data share information is required');
      }

      const initiative = await initiativeDataShareService.updateDataShareTemplate({
        req,
        initiativeId: res.locals.initiativeId,
        updateDataShare,
      });
      return res.FromModel(initiative);
    } catch (e) {
      next(e);
    }
  });

router
  .route('/combined-scope')
  .get(async (req, res, next) => {
    try {
      const scope = await initiativeDataShareService.getCombinedScope(res.locals.initiativeId);
      return res.FromModel(scope);
    } catch (e) {
      next(e);
    }
  });

module.exports = router;
