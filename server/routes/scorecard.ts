/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import ScorecardFactory from '../service/scorecard/ScorecardFactory';
import { InitiativePlain, InitiativeTags } from '../models/initiative';
import { checkPublicPermissions, populateInitiative } from '../middleware/commonMiddlewares';
import { createComparisonService } from '../service/initiative/ComparisonService';
import { InitiativeRepository } from '../repository/InitiativeRepository';
import cache from '../service/cache';
import { getScorecardManager } from '../service/scorecard/ScorecardManager';
import { AuthenticatedRequest, AuthRouter } from '../http/AuthRouter';
import { ScorecardOptions } from '../service/utr/aggregation/aggregatorDataService';
import { IndustryLevels } from '../types/initiative';

const router = express.Router() as AuthRouter;
const comparisonService = createComparisonService();
const scManager = getScorecardManager();

type ScorecardRequest = AuthenticatedRequest<any, any, any, { tag?: string }>
const getScorecardQueryFromReq = (req: ScorecardRequest) => ({
  user: req.user,
  initiative: req.initiative as InitiativePlain,
  initiativeId: (req.initiative as InitiativePlain)._id,
  surveyId: req.params.surveyId,
  tag: req.query.tag,
});

router.route('/')
  .get(cache.route(), async (req: ScorecardRequest, res) => {
    try {
      const initiatives = await InitiativeRepository.findWithTags([InitiativeTags.Organization, InitiativeTags.VerifiedOrganization], false);
      const options: ScorecardOptions = {
        isCompletedData: true
      }
      if (req.query.tag) {
        options.includeFilterTags = [req.query.tag];
      }
      const scorecardFactory = new ScorecardFactory();
      const scorecards = await Promise.all(initiatives.map((initiative) => {
        return scorecardFactory.getByInitiative(initiative, options);
      }));

      res.FromModel({ scorecards, initiatives });
    } catch (error) {
      res.Exception(error);
    }
  });

router.route('/initiative/:initiativeId/goals/:goalCode')
  .get(checkPublicPermissions, cache.route(), async (req: ScorecardRequest, res) => {
    try {
      if (!req.initiative) {
        return res.Invalid();
      }
      const result = await scManager.getGoalScorecard({
        goalCode: req.params.goalCode,
        initiative: req.initiative,
        user: req.user,
      })
      res.FromModel(result);
    } catch (error) {
      res.Exception(error);
    }
  });


router.route([
  '/initiative/:initiativeId',
  '/initiative/:initiativeId/survey/:surveyId',
]).get(populateInitiative, cache.route(), async (req: ScorecardRequest, res) => {
  try {
    const result = await scManager.getScorecard(getScorecardQueryFromReq(req))
    res.FromModel(result);
  } catch (error) {
    res.Exception(error);
  }
});

router.route([
  '/initiative/:initiativeId/all',
  '/initiative/:initiativeId/survey/:surveyId/all'
]).get(checkPublicPermissions, async (req: ScorecardRequest, res) => {
  try {
    const data = await scManager.getAllScorecards(getScorecardQueryFromReq(req))
    res.FromModel(data);
  } catch (error) {
    res.Exception(error);
  }
});

router.route('/initiative/:initiativeId/comparison')
  .get(checkPublicPermissions, async (req, res) => {
    try {

      const initiative = req.initiative as InitiativePlain;
      const { framework, level1, level2, level3, level4 } = req.query;

      const peerComparison = await comparisonService.getPeerComparison(
        initiative._id,
        framework as string,
        { level1, level2, level3, level4 } as IndustryLevels
      );

      res.FromModel(peerComparison);
    } catch (error) {
      res.Exception(error);
    }
  });

module.exports = router;
