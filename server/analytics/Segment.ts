/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import Analytics from 'analytics-node';
import config from '../config';
import { AnalyticsEvents } from './events';


type Identity =
  | { userId: string | number }
  | { userId?: string | number; anonymousId: string | number }

interface Integrations {
  [integration_name: string]: IntegrationValue;
}

type IntegrationValue = boolean | { [integration_key: string]: any }

type EventMessage = Identity & {
  event: AnalyticsEvents;
  properties?: any;
  timestamp?: Date;
  context?: any;
  integrations?: Integrations;
}

export class Segment {
  constructor(private client: Analytics) {
  }

  public async track(message: EventMessage) {
    return new Promise(((resolve, reject) => {
      this.client.track(message, (err) => {
        err ? resolve(true) : reject(err);
      })
    }))
  }
}

let instance: Segment;
export const getSegment = () => {
  if (!instance) {
    instance = new Segment(
      new Analytics(config.analytics.segment.writeKey),
    );
  }
  return instance;
}
