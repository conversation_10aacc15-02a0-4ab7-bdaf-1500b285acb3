/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import BadRequestError from '../error/BadRequestError';
import BackgroundJob, { BackgroundJobPlain, JobStatus, JobType, TaskType } from '../models/backgroundJob';
import { ObjectId } from 'bson';

export class BackgroundJobRepository {

  public static async mustFindById(id: ObjectId | string) {
    if (!ObjectId.isValid(id)) {
      throw new Error(`Job could not be found`);
    }

    const job = await BackgroundJob.findById(id);
    if (!job) {
      throw new BadRequestError(`Job was not found`);
    }
    return job;
  }

  public getLatestJob({ type, status, taskType }: { type: JobType; status?: JobStatus; taskType?: TaskType }) {
    return BackgroundJob.findOne({
      type,
      ...(status ? { status } : {}),
      ...(taskType ? { 'tasks.type': taskType } : {}),
    })
      .sort({ created: -1 })
      .lean<BackgroundJobPlain>()
      .exec();
  }
}

let instance: BackgroundJobRepository;
export const getBackgroundJobRepository = () => {
  if (!instance) {
    instance = new BackgroundJobRepository();
  }
  return instance;
};
