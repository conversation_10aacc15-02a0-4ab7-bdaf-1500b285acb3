/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { SubscriptionCustomer } from "../models/customer";
import Initiative, { InitiativeTags } from "../models/initiative";
import Survey from "../models/survey";
import UniversalTrackerValue from "../models/universalTrackerValue";
import User from "../models/user";
import { subtractDate } from "../util/date";
import { getStatsRepository, StatsRepository } from "./StatsRepository";

export enum StatsPeriod {
  P24M = '24M',
  P12M = '12M',
  P6M = '6M',
  P3M = '3M',
  P1M = '1M',
  ALL = 'ALL',
}

export enum StatsGranularity {
  Daily = 'daily',
  Weekly = 'weekly',
  Monthly = 'monthly',
}

interface ChartHeader {
  [index: number]: {
    label: string;
    type?: 'date'
  }
}

interface ChartDataRow {
  [index: number]: string | number;
}

export interface ChartData {
  headers: ChartHeader;
  rows: ChartDataRow[];
}

enum DataSources {
  Subscriptions = 'subscriptions_created',
  CompaniesOnboarded = 'companies_onboarded',
  CompaniesByCountry = 'companies_by_country',
  UsersPerCompany = 'users_per_company',
  SurveysPerCompany = 'surveys_per_company',
  Surveys = 'surveys',
  Registrations = 'registration',
  LastLogins = 'last_logins',
  UTRUpdated = 'utr_updated',
}

export const availableDataSources = [
  { label: 'Companies Onboarded', value: DataSources.CompaniesOnboarded },
  { label: 'Companies By Country', value: DataSources.CompaniesByCountry },
  { label: 'Subscriptions Created', value: DataSources.Subscriptions },
  { label: 'Users per Company', value: DataSources.UsersPerCompany },
  { label: 'Surveys per Company', value: DataSources.SurveysPerCompany },
  { label: 'Surveys Created', value: DataSources.Surveys },
  { label: 'Registrations', value: DataSources.Registrations },
  { label: 'Last Logins', value: DataSources.LastLogins },
  { label: 'UTRs Updated', value: DataSources.UTRUpdated },
]

interface GroupedData {
  _id: {
    year?: number;
    month?: number;
    day?: number;
    week?: number;
  },
  count: number;
}

const getFromDate = (period: StatsPeriod) => {
  switch (period) {
    case StatsPeriod.P24M:
      return subtractDate(new Date(), 24, 'months');
    case StatsPeriod.P12M:
      return subtractDate(new Date(), 12, 'months');
    case StatsPeriod.P6M:
      return subtractDate(new Date(), 6, 'months');
    case StatsPeriod.P3M:
      return subtractDate(new Date(), 3, 'months');
    case StatsPeriod.P1M:
      return subtractDate(new Date(), 1, 'months');
    case StatsPeriod.ALL:
      return new Date(0);
  }
}

export class StaffStatsRepository {
  constructor(
    private statsRepository: StatsRepository
  ) {}

  async getReportData(dataSource: string, period: StatsPeriod, granularity: StatsGranularity): Promise<ChartData | undefined> {
    switch(dataSource) {
      case DataSources.CompaniesOnboarded: {
        return this.getCompaniesOnboarded(period, granularity);
      }
      case DataSources.CompaniesByCountry: {
        return this.getCompaniesByCountry(period, granularity);
      }
      case DataSources.Registrations: {
        return this.getUserRegistrations(period, granularity);
      }
      case DataSources.UsersPerCompany: {
        return this.getUsersPerCompany(period, granularity);
      }
      case DataSources.Surveys: {
        return this.getSurveys(period, granularity);
      }
      case DataSources.Subscriptions: {
        return this.getSubscriptions(period, granularity);
      }
      case DataSources.SurveysPerCompany: {
        return this.getSurveysPerCompany(period, granularity);
      }
      case DataSources.LastLogins: {
        return this.getLastLogins(period, granularity);
      }
      case DataSources.UTRUpdated: {
        return this.getUTRsUpdated(period, granularity);
      }
      default: {
        return this.getNoData();
      }
    }
  }

  private getGroupByDate(granularity: StatsGranularity, dateField: 'created' | string): [{ $group: any }, { $sort: any }] {
    switch(granularity) {
      case StatsGranularity.Daily:
        return [
          {
            $group: {
              _id: {
                year: { $year: `$${dateField}` },
                month: { $month: `$${dateField}` },
                day: { $dayOfMonth: `$${dateField}` }
              },
              count: { $sum: 1 }
            }
          },
          {
            $sort: {
              "_id.year": 1,
              "_id.month": 1,
              "_id.day": 1
            }
          }
        ];
      case StatsGranularity.Weekly:
        return [
          {
            $group: {
              _id: {
                year: { $year: `$${dateField}` },
                week: { $week: `$${dateField}` }
              },
              count: { $sum: 1 }
            }
          },
          {
            $sort: {
              "_id.year": 1,
              "_id.week": 1
            }
          }
        ];
      case StatsGranularity.Monthly:
        return [
          {
            $group: {
              _id: {
                year: { $year: `$${dateField}` },
                month: { $month: `$${dateField}` }
              },
              count: { $sum: 1 }
            }
          },
          {
            $sort: {
              "_id.year": 1,
              "_id.month": 1
            }
          }
        ];
    }
  }

  private getDateRangeFilter(period: StatsPeriod) {
    return {
      $gte: getFromDate(period),
      $lte: new Date()
    }
  }

  private convertToDateRows(
    data: GroupedData[],
    granularity: StatsGranularity
  ) {
    return data.map(u => {
      switch(granularity) {
        case StatsGranularity.Daily: {
          const date = new Date(u._id.year as number, u._id.month as number - 1, u._id.day as number);
          return [date.toISOString(), u.count];
        }
        case StatsGranularity.Weekly: {
          const week = new Date(u._id.year as number, 0, 1);
          week.setDate(week.getDate() + (u._id.week as number - 1) * 7);
          return [week.toISOString(), u.count];
        }
        case StatsGranularity.Monthly: {
            const date = new Date(u._id.year as number, u._id.month as number - 1);
            return [date.toISOString(), u.count];
        }
      }
    });
  }

  private async getCompaniesOnboarded(period: StatsPeriod, granularity: StatsGranularity): Promise<ChartData> {
    const docs = await Initiative.aggregate<GroupedData>([
      {
        $match: {
          tags: InitiativeTags.Organization,
          created: this.getDateRangeFilter(period)
        }
      },
      ...this.getGroupByDate(granularity, 'created')
    ]);

    return {
      headers: [
        {
          label: 'Date',
          type: 'date'
        },
        {
          label: 'Companies onboarded',
        }
      ],
      rows: this.convertToDateRows(docs, granularity)
    };
  }

  private async getUserRegistrations(period: StatsPeriod, granularity: StatsGranularity): Promise<ChartData> {
    const docs = await User.aggregate<GroupedData>([
      {
        $match: {
          created: this.getDateRangeFilter(period)
        }
      },
      ...this.getGroupByDate(granularity, 'created')
    ]);

    return {
      headers: [
        {
          label: 'Date',
          type: 'date'
        },
        {
          label: 'Registrations',
        }
      ],
      rows: this.convertToDateRows(docs, granularity)
    };
  }

  private async getUsersPerCompany(period: StatsPeriod, granularity: StatsGranularity): Promise<ChartData> {
    const stats = await this.statsRepository.companyStats({
      fromDate: getFromDate(period).toISOString(),
      toDate: new Date().toISOString(),
      showStaff: false,
      hideArchived: true,
    });

    return {
      headers: [
        {
          label: 'Company',
        },
        {
          label: 'Users',
        }
      ],
      rows: stats.map(stat => {
          return [stat.name, stat.userCount];
      })
    };
  }

  private async getCompaniesByCountry(period: StatsPeriod, granularity: StatsGranularity): Promise<ChartData> {
    const stats = await this.statsRepository.companyStats({
      fromDate: getFromDate(period).toISOString(),
      toDate: new Date().toISOString(),
      showStaff: false,
      hideArchived: true,
    });

    const acc = stats.reduce((acc, stat) => {
      const country = stat.country ?? 'Not set';
      acc[country] = (acc[country] || 0) + stat.userCount;
      return acc;
    }, {} as { [key: string]: number});
    return {
      headers: [
        {
          label: 'Country',
        },
        {
          label: 'Companies',
        }
      ],
      rows: Object.entries(acc)
    };
  }

  private async getSurveysPerCompany(period: StatsPeriod, granularity: StatsGranularity): Promise<ChartData> {
    const stats = await this.statsRepository.companyStats({
      fromDate: getFromDate(period).toISOString(),
      toDate: new Date().toISOString(),
      showStaff: false,
      hideArchived: true,
    });

    return {
      headers: [
        {
          label: 'Company',
        },
        {
          label: 'Surveys',
        }
      ],
      rows: stats.map(stat => {
          return [stat.name, stat.surveyCount];
      })
    };
  }

  private async getLastLogins(period: StatsPeriod, granularity: StatsGranularity): Promise<ChartData> {
    const docs = await User.aggregate<GroupedData>([
      {
        $match: {
          lastLogin: this.getDateRangeFilter(period)
        }
      },
      ...this.getGroupByDate(granularity, 'lastLogin')
    ]);

    return {
      headers: [
        {
          label: 'Date',
          type: 'date'
        },
        {
          label: 'Last login',
        }
      ],
      rows: this.convertToDateRows(docs, granularity)
    };
  }

  private async getUTRsUpdated(period: StatsPeriod, granularity: StatsGranularity): Promise<ChartData> {
    const docs = await UniversalTrackerValue.aggregate<GroupedData>([
      {
        $match: {
          lastUpdated: this.getDateRangeFilter(period),
          deletedDate: { $exists: false }
        }
      },
      ...this.getGroupByDate(granularity, 'lastUpdated')
    ]);

    return {
      headers: [
        {
          label: 'Date',
          type: 'date'
        },
        {
          label: 'Questions updated',
        }
      ],
      rows: this.convertToDateRows(docs, granularity)
    };
  }

  private async getSurveys(period: StatsPeriod, granularity: StatsGranularity): Promise<ChartData> {
    const docs = await Survey.aggregate<GroupedData>([
      {
        $match: {
          created: this.getDateRangeFilter(period),
          deletedDate: { $exists: false }
        }
      },
      ...this.getGroupByDate(granularity, 'created')
    ]);

    return {
      headers: [
        {
          label: 'Date',
          type: 'date'
        },
        {
          label: 'Surveys',
        }
      ],
      rows: this.convertToDateRows(docs, granularity)
    };
  }

  private async getSubscriptions(period: StatsPeriod, granularity: StatsGranularity): Promise<ChartData> {
    const docs = await SubscriptionCustomer.aggregate<GroupedData>([
      {
        $unwind: '$subscriptions'
      },
      {
        $addFields: {
          // Convert timestamp to Date
          'subscriptions.convertedStartDate': {
            $convert: {
              input: { $multiply: [ '$subscriptions.startDate', 1000 ] },
              to: 'date',
              onError: null, // Handle conversion errors by setting the value to null
              onNull: null   // Handle null values by setting the value to null
            }
          }
        }
      },
      {
        $match: {
          'subscriptions.convertedStartDate': this.getDateRangeFilter(period),
        }
      },
      ...this.getGroupByDate(granularity, 'subscriptions.convertedStartDate')
    ]);

    return {
      headers: [
        {
          label: 'Date',
          type: 'date'
        },
        {
          label: 'Subscriptions',
        }
      ],
      rows: this.convertToDateRows(docs, granularity)
    };
  }

  private async getNoData(): Promise<ChartData> {
    return {
      headers: [
        { label: 'No data' },
        { label: 'No data' },
      ],
      rows: [
        ['A', 0],
        ['B', 25],
        ['C', 50],
        ['D', 75],
      ]
    };
  }
}

let instance: StaffStatsRepository;
export const getStaffStatsRepository = () => {
  if (!instance) {
    instance = new StaffStatsRepository(
      getStatsRepository()
    );
  }
  return instance;
}
