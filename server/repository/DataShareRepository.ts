/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import DataShare, { getShareStatus, DataShareModel, DataSharePlain, RequesterType } from '../models/dataShare';
import { FilterQuery, ProjectionType } from 'mongoose';
import { ObjectId } from 'bson';
import { SurveyModelPlain } from '../models/survey';
import { InitiativePlain } from '../models/initiative';
import { InitiativeRepository } from './InitiativeRepository';
import { UserModel } from '../models/user';

export type DataShareMin = Pick<DataSharePlain, '_id' | 'initiativeId' | 'status' | 'dataScope'>;
export type ActiveDataShare = DataShareMin & Pick<DataShareModel, 'dataScope'>
export type RequesterFilter = FilterQuery<DataShareModel>;

export class DataShareRepository {

  public async findRequesterDataShare(filter: RequesterFilter): Promise<DataShareMin[]> {
    return DataShare.find({
      ...filter,
      deletedDate: { $exists: false },
      revokedDated: { $exists: false },
      expiredDate: { $exists: false },
    }).lean().exec()
      .then(data => data.map(share => ({
        _id: share._id,
        initiativeId: share.initiativeId,
        status: getShareStatus(share),
        dataScope: share.dataScope,
      })));
  }

  public async getSurveyDataShares(survey: Pick<SurveyModelPlain, '_id' | 'initiativeId'>): Promise<DataSharePlain[]> {
    return DataShare.find({
      initiativeId: survey.initiativeId,
      ...this.getActiveFilter(),
    }).lean().exec();
  }

  public async findActiveDataShare(filter: RequesterFilter): Promise<ActiveDataShare[]> {
    return DataShare.find({
      ...filter,
      ...this.getActiveFilter(),
    }).lean().exec()
      .then(data => data.map(share => ({
        _id: share._id,
        initiativeId: share.initiativeId,
        dataScope: share.dataScope,
        status: getShareStatus(share),
      } as ActiveDataShare)));
  }

  public async findRawActiveDataShare(filter: RequesterFilter, projection: ProjectionType<DataSharePlain> = {}): Promise<DataShareModel[]> {
    return DataShare.find({
      ...filter,
      ...this.getActiveFilter(),
    }, projection).exec();
  }

  public async findDataShareByInitiativeId(initiativeId: ObjectId) {
    return DataShare.find({ initiativeId, deletedDate: { $exists: false } }).exec();
  }

  public async findActiveDataShareByInitiativeId(requesterId: string, initiativeId: string) {
    return this.findActiveDataShare({ requesterId, initiativeId });
  }

  public async findActiveDataShareByUser(user: UserModel, initiativeId: string) {
    const userInitiatives = await InitiativeRepository.getInitiativeTree(user);
    const userInitiativeIds = userInitiatives.map(i => i._id);
    return this.findActiveDataShare({
      requesterId: {
        $in: userInitiativeIds
      },
      initiativeId
    });
  }

  public async deleteAll(initiative: Pick<InitiativePlain, '_id'>) {
    const dataShares = await DataShare.find({ initiativeId: initiative._id, deletedDate: { $exists: false } });
    if (dataShares.length === 0) {
      return;
    }

    await Promise.all(dataShares.map(dataShare => {
      dataShare.deletedDate = new Date();
      return dataShare.save();
    }));
  }

  public async deleteExcludeOne({
    initiativeId,
    requesterId,
    excludedId,
  }: { excludedId: DataSharePlain['_id'] } & Pick<DataSharePlain, 'initiativeId' | 'requesterId'>) {
    return DataShare.updateMany(
      {
        _id: { $ne: excludedId },
        requesterId,
        initiativeId,
        deletedDate: { $exists: false },
        revokedDated: { $exists: false },
        expiredDate: { $exists: false },
        ...this.excludeRestrictedFilter(),
      },
      { $set: { deletedDate: new Date() } },
    );
  }

  public async find(filter: FilterQuery<DataShareModel>): Promise<DataSharePlain[]> {
    return DataShare.find({
      ...filter,
      deletedDate: { $exists: false }
    }).lean().exec()
  }

  public async hasAtLeastOne(filter: FilterQuery<DataShareModel>): Promise<boolean> {
    return DataShare.countDocuments({
      ...filter,
      deletedDate: { $exists: false }
    }).lean().exec().then((count) => count > 0)
  }

  public async findRaw(filter: FilterQuery<DataShareModel>): Promise<DataShareModel[]> {
    return DataShare.find({
      ...filter,
      deletedDate: { $exists: false }
    });
  }

  public getActiveFilter() {
    return {
      acceptedDate: { $exists: true },
      deletedDate: { $exists: false },
      revokedDated: { $exists: false },
      expiredDate: { $exists: false },
    }
  }

  public getPendingFilter() {
    return {
      acceptedDate: { $exists: false },
      deletedDate: { $exists: false },
      revokedDated: { $exists: false },
      expiredDate: { $exists: false },
    }
  }

  public getRevokedFilter() {
    return {
      deletedDate: { $exists: false },
      revokedDated: { $exists: true },
      expiredDate: { $exists: false },
    }
  }

  public excludeRestrictedFilter() {
    return {
      $or: [
        { restriction: { $exists: false } },
      ]
    }
  }

  public static async getInitiative(
    requesterType: RequesterType,
    requesterId: string,
    initiativeId: string
  ): Promise<InitiativePlain | undefined> {
    return InitiativeRepository.getInitiativeById(initiativeId, true);
  }

}

let instance: DataShareRepository;
export const getDataShareRepository = () => {
  if (!instance) {
    instance = new DataShareRepository();
  }
  return instance;
}
