/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { stakeholdersUsers } from './projections';
import { ObjectId } from 'bson';
import { ActionList } from '../service/utr/constants';
import { AssuranceStatus } from '../service/assurance/model/Assurance';
import { AssurancePortfolioStatus } from '../service/assurance/model/AssurancePortfolio';
import { SourceTypes } from '../models/public/universalTrackerValueType';
import { AssurancePortfolioPermission } from '../models/assurancePermission';

export const stakeholdersAggregation = ($match: any) => [
  {
    $match: $match
  },
  {
    $lookup: {
      from: 'users',
      localField: 'stakeholders.stakeholder',
      foreignField: '_id',
      as: 'stakeholder'
    }
  },
  {
    $lookup: {
      from: 'users',
      localField: 'stakeholders.verifier',
      foreignField: '_id',
      as: 'verifier'
    }
  },
  {
    $lookup: {
      from: 'users',
      localField: 'stakeholders.escalation',
      foreignField: '_id',
      as: 'escalation'
    }
  },
  {
    $project: { ...stakeholdersUsers, compositeData: 1 },
  }
];

export const sourceTypeActionFilter = () => ({
  sourceType: {
    $nin: [SourceTypes.Composite, SourceTypes.Fragment, SourceTypes.SubFragment]
  }
});

export const excludeSoftDeleted = () => ({ deletedDate: { $exists: false } });

const stakeholderByStatusMatch = (userId: ObjectId) => ({
  $or: [
    {
      status: ActionList.Created,
      'stakeholders.stakeholder': userId
    },
    {
      status: ActionList.Updated,
      'stakeholders.verifier': userId
    },
    {
      status: ActionList.Rejected,
      'stakeholders.stakeholder': userId
    }
  ]
});

export const userPendingActionMatch = (userId: ObjectId) => ({
  $and: [
    sourceTypeActionFilter(),
    excludeSoftDeleted(),
    stakeholderByStatusMatch(userId)
  ]
});

export const leafSurveyUtrvs = (): { $or: any[] } => ({
  $or: [
    {
      sourceType: SourceTypes.Fragment,
      'compositeData.fragmentUtrvs': { $eq: [] }
    },
    {
      sourceType: SourceTypes.SubFragment,
    }
  ]
});

export const surveyActionMatchByUserId = (userMatchId?: ObjectId, surveyId?: ObjectId, userMatch?: object): any => {

  const matchPipeline: any = {
    $match: {
      $and: [
        {
          stakeholders: { $exists: true, $ne: null },
          ...excludeSoftDeleted(),
          ...leafSurveyUtrvs(),
        }
      ]
    }
  };

  if (userMatchId) {
    const items = userMatch || {
      $or: [
        { 'stakeholders.stakeholder': userMatchId },
        { 'stakeholders.verifier': userMatchId }
      ]
    };
    matchPipeline.$match.$and.unshift(items);
  }

  if (surveyId) {
    matchPipeline.$match.$and.unshift({ 'compositeData.surveyId': surveyId });
  }
  return matchPipeline;
};

export const assurancePortfolioLookUp = (localField: string, foreignField: string) => ({
  $lookup: {
    from: 'assurance-portfolios',
    let: { localField: `$${localField}` },
    pipeline: [
      {
        $match: {
          $expr: {
            $and: [
              {
                $eq: ['$' + foreignField, '$$localField']
              },
              {
                $ne: ['$status', AssurancePortfolioStatus.Deleted],
              }
            ]
          }
        }
      }
    ],
    as: 'assurance'
  }
});

export const utrvAssuranceLookUp = (
  localField: string,
  foreignField: string,
  asName = 'universalTrackerValueAssurances'
) => ({
  $lookup: {
    from: 'universal-tracker-value-assurances',
    let: { localField: `$${localField}` },
    pipeline: [
      {
        $match: {
          status: { $nin: [AssuranceStatus.Disabled, AssuranceStatus.Removed] },
          $expr: { $eq: ['$' + foreignField, '$$localField'] }
        }
      }
    ],
    as: asName
  }
});

export const groupAssurer = {
  assurers: {
    $filter: {
      input: {
        $map: {
          input: '$permissions',
          as: 'permission',
          in: {
            $mergeObjects: [
              {
                $min: {
                  $filter: {
                    input: '$assurers',
                    cond: { $eq: ['$$this._id', '$$permission.userId'] },
                  },
                },
              },
              {
                isAdmin: {
                  $cond: {
                    if: { $in: [AssurancePortfolioPermission.Admin, '$$permission.permissions'] },
                    then: true,
                    else: false,
                  },
                },
                isAssurer: {
                  $cond: {
                    if: { $in: [AssurancePortfolioPermission.Assurer, '$$permission.permissions'] },
                    then: true,
                    else: false,
                  },
                },
                isRestrictedUser: {
                  $cond: {
                    if: { $in: [AssurancePortfolioPermission.RestrictedUser, '$$permission.permissions'] },
                    then: true,
                    else: false,
                  },
                },
              },
            ],
          },
        },
      },
      cond: { $ifNull: ['$$this._id', false] },
    },
  },
};
