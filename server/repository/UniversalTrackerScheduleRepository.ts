/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import UniversalTrackerSchedule from '../models/universalTrackerSchedule';
import { ObjectId } from 'bson';
import { userStakeholderLookup } from './utrvAggregations';

export class UniversalTrackerScheduleRepository {

  public static async getPendingScheduleActions() {

    const now = new Date();
    return UniversalTrackerSchedule.aggregate([
      {
        $match: {
          enabled: true,
          endDate: { $gte: now },
          startDate: { $lte: now },
          $expr: {
            $or: [
              {
                $lte: ['$nextRunDate', now],
              },
              {
                $eq: ['$nextRunDate', undefined],
              },
            ],
          },
        },
      },
      ...userStakeholderLookup
    ]);
  }

  public static async findByIdExtended(id: ObjectId | string) {
    return UniversalTrackerSchedule.aggregate([
      { $match: { _id: new ObjectId(id) } },
      {
        $lookup: {
          from: 'universal-tracker-values',
          localField: 'history.universalTrackerValueId',
          foreignField: '_id',
          as: 'universalTrackerValues'
        }
      }
    ]).then(result => Array.isArray(result) ? result.pop() : result);
  }

}
