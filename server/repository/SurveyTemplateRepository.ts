import { SurveyTemplateModel, SurveyTemplate } from '../models/surveyTemplate';
import { ObjectId } from 'bson';
import BadRequestError from '../error/BadRequestError';
import { SurveyConfigService } from '../service/initiative/SurveyConfigService';

export class SurveyTemplateRepository {
  public static async getTemplatesByCurrentLevelAndAbove(parentIds: ObjectId[]) {
    return SurveyTemplate.aggregate([
      {
        $match: {
          initiativeId: { $in: parentIds },
          deletedDate: { $exists: false },
        },
      },
      {
        $lookup: {
          from: 'initiatives',
          localField: 'initiativeId',
          foreignField: '_id',
          as: 'initiative',
        },
      },
      {
        $unwind: {
          path: '$initiative',
        },
      },
      {
        $lookup: {
          from: 'survey-template-history',
          localField: '_id',
          foreignField: 'templateId',
          pipeline: [{ $sort: { created: -1 } }, { $limit: 1 }],
          as: 'histories',
        },
      },
      {
        $addFields: {
          lastUsed: { $arrayElemAt: ['$histories.created', 0] },
        },
      },
      {
        $project: {
          histories: 0,
        },
      },
    ])
      .sort({ created: 'desc' })
      .exec();
  }

  public static async mustFindByIdWithRootCurrency(id: string | ObjectId): Promise<SurveyTemplateModel<ObjectId>> {
    if (!ObjectId.isValid(id)) {
      throw new Error(`Template could not be found`);
    }
    const template = await SurveyTemplate.findById(id);
    if (!template) {
      throw new BadRequestError(`Template was not found. Please go back to the previous page and try again.`);
    }

    const surveyConfig = await SurveyConfigService.findByInitiative(template.initiativeId.toString());
    template.unitConfig = { ...template.unitConfig, currency: surveyConfig.unitConfig.currency };

    return template;
  }
}
