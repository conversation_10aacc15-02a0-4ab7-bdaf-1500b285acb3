/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import Initiative, { InitiativeRating } from '../models/initiative';
import config from '../config';

const logoSrcRoot = `${config.assets.logoSrcRoot}/`;

interface RatingAgency {
  code: string;
  title: string;
  logoSrc: string;
  ratingAgencyUrl: string;
  additionalInformation: string;
}

const getCdpRating = ({ code, title }: { code: string, title: string }) => ({
  code,
  title,
  logoSrc: 'cdp.png',
  ratingAgencyUrl: 'https://www.cdp.net/',
  additionalInformation: 'CDP is a not-for-profit charity that runs the global disclosure system for investors, companies, cities, states and regions to manage their environmental impacts.',
});

export const ratingAgencies: RatingAgency[] = [
  {
    code: 'reprisk',
    title: 'RepRisk',
    logoSrc: 'reprisk.png',
    ratingAgencyUrl: 'https://www.reprisk.com/',
    additionalInformation: 'Since 2007, RepRisk has produced the largest, high-quality annotated (human-labeled) dataset that allows us to train our machine learning algorithms to be more accurate and effective in identifying ESG risks'
  },
  {
    code: 'refinitiv',
    title: 'Refinitiv ESG',
    logoSrc: 'refinitiv.png',
    ratingAgencyUrl: 'https://www.refinitiv.com/en/financial-data/company-data/esg-data',
    additionalInformation: 'Refinitiv provides ESG data on over 9,400 companies, both active and inactive, spanning 22 global and regional indices, with insight on 1.8 million Officers & Directors and 769k+ individual Fixed Income securities issued by companies in our universe.'
  },
  {
    code: 'vigeoeiris',
    title: 'Vigeo Eiris',
    logoSrc: 'virgeoeiris.jpg',
    ratingAgencyUrl: 'http://vigeo-eiris.com/',
    additionalInformation: 'ISS ESG’s scientifically based rating concept places a clear, sector-specific focus on the materiality of non-financial information. It is constantly reviewed and developed to cover all relevant environmental, social and governance related topics.'
  },
  {
    code: 'iss',
    title: 'ISS ESG Ratings & Rankings',
    logoSrc: 'iss.png',
    ratingAgencyUrl: 'https://www.issgovernance.com/esg/ratings/',
    additionalInformation: 'ISS ESG’s scientifically based rating concept places a clear, sector-specific focus on the materiality of non-financial information. It is constantly reviewed and developed to cover all relevant environmental, social and governance related topics.'
  },
  {
    code: 'trucost',
    title: 'TruCost',
    logoSrc: 'trueCost.png',
    ratingAgencyUrl: 'https://www.trucost.com',
    additionalInformation: 'Trucost, part of S&P Global, assesses risks relating to climate change, natural resource constraints, and broader environmental, social, and governance factors.'
  },
  {
    code: 'msci',
    title: 'MSCI Inc.',
    logoSrc: 'msci.png',
    ratingAgencyUrl: 'https://www.msci.com/',
    additionalInformation: 'MSCI strives to bring greater transparency to financial markets and enable the investment community to make better decisions for a better world.'
  },
  {
    code: 'sustainalytics',
    title: 'Sustainalytics',
    logoSrc: 'sustainalytics.png',
    ratingAgencyUrl: 'https://www.sustainalytics.com/',
    additionalInformation: 'Sustainalytics is a global leader in ESG and Corporate Governance research and ratings. Over the last 25 years, we have brought together leading ESG research and client servicing professionals to retain that personal touch that has helped us to grow. Today, Sustainalytics supports hundreds of the world’s foremost investors who incorporate ESG and corporate governance insights into their investment processes.'
  },
  {
    code: 'arabesque',
    title: 'Arabesque S-Ray',
    logoSrc: 'arabesque.png',
    ratingAgencyUrl: 'https://www.arabesque.com/',
    additionalInformation: 'Arabesque S-Ray is a global data provider that focuses on advisory and data solutions by combining big data and environmental, social and governance (ESG) metrics to assess the performance and sustainability of companies worldwide.'
  },
  {
    code: 'futurefitBe01',
    title: 'Future-Fit BE01',
    logoSrc: 'futureFit.png',
    ratingAgencyUrl: 'https://futurefitbusiness.org/',
    additionalInformation: 'Break-Even 01 - Energy is from renewable sources'
  },
  {
    code: 'futurefitBe02',
    title: 'Future-Fit BE02',
    logoSrc: 'futureFit.png',
    ratingAgencyUrl: 'https://futurefitbusiness.org/',
    additionalInformation: 'Break-Even 02 - Water use is environmentally responsible and socially equitable'
  },
  {
    code: 'futurefitBe03',
    title: 'Future-Fit BE03',
    logoSrc: 'futureFit.png',
    ratingAgencyUrl: 'https://futurefitbusiness.org/',
    additionalInformation: 'Break-Even 03 - Natural resources are managed to respect the welfare of ecosystems, people and animals'
  },
  {
    code: 'futurefitBe04',
    title: 'Future-Fit BE04',
    logoSrc: 'futureFit.png',
    ratingAgencyUrl: 'https://futurefitbusiness.org/',
    additionalInformation: 'Break-Even 04 - Procurement safeguards the pursuit of future-fitness'
  },
  {
    code: 'futurefitBe05',
    title: 'Future-Fit BE05',
    logoSrc: 'futureFit.png',
    ratingAgencyUrl: 'https://futurefitbusiness.org/',
    additionalInformation: 'Break-Even 05 - Operational emissions do not harm people or the environment'
  },
  {
    code: 'futurefitBe06',
    title: 'Future-Fit BE06',
    logoSrc: 'futureFit.png',
    ratingAgencyUrl: 'https://futurefitbusiness.org/',
    additionalInformation: 'Break-Even 06 - Operations emit no greenhouse gases'
  },
  {
    code: 'futurefitBe07',
    title: 'Future-Fit BE07',
    logoSrc: 'futureFit.png',
    ratingAgencyUrl: 'https://futurefitbusiness.org/',
    additionalInformation: 'Break-Even 07 - Operational waste is eliminated'
  },
  {
    code: 'futurefitBe08',
    title: 'Future-Fit BE08',
    logoSrc: 'futureFit.png',
    ratingAgencyUrl: 'https://futurefitbusiness.org/',
    additionalInformation: 'Break-Even 08 - Operations do not encroach on ecosystems or communities'
  },
  {
    code: 'futurefitBe09',
    title: 'Future-Fit BE09',
    logoSrc: 'futureFit.png',
    ratingAgencyUrl: 'https://futurefitbusiness.org/',
    additionalInformation: 'Break-Even 09 - Community health is safeguarded'
  },
  {
    code: 'futurefitBe10',
    title: 'Future-Fit BE10',
    logoSrc: 'futureFit.png',
    ratingAgencyUrl: 'https://futurefitbusiness.org/',
    additionalInformation: 'Break-Even 10 - Employee health is safeguarded'
  },
  {
    code: 'futurefitBe11',
    title: 'Future-Fit BE11',
    logoSrc: 'futureFit.png',
    ratingAgencyUrl: 'https://futurefitbusiness.org/',
    additionalInformation: 'Break-Even 11 - Employees are paid at least a living wage'
  },
  {
    code: 'futurefitBe12',
    title: 'Future-Fit BE12',
    logoSrc: 'futureFit.png',
    ratingAgencyUrl: 'https://futurefitbusiness.org/',
    additionalInformation: 'Break-Even 12 - Employees are subject to fair employment terms'
  },
  {
    code: 'futurefitBe13',
    title: 'Future-Fit BE13',
    logoSrc: 'futureFit.png',
    ratingAgencyUrl: 'https://futurefitbusiness.org/',
    additionalInformation: 'Break-Even 13 - Employees are not subject to discrimination'
  },
  {
    code: 'futurefitBe14',
    title: 'Future-Fit BE14',
    logoSrc: 'futureFit.png',
    ratingAgencyUrl: 'https://futurefitbusiness.org/',
    additionalInformation: 'Break-Even 14 - Employee concerns are actively solicited, impartially judged and transparently addressed'
  },
  {
    code: 'futurefitBe15',
    title: 'Future-Fit BE15',
    logoSrc: 'futureFit.png',
    ratingAgencyUrl: 'https://futurefitbusiness.org/',
    additionalInformation: 'Break-Even 15 - Product communications are honest, ethical, and promote responsible use'
  },
  {
    code: 'futurefitBe16',
    title: 'Future-Fit BE16',
    logoSrc: 'futureFit.png',
    ratingAgencyUrl: 'https://futurefitbusiness.org/',
    additionalInformation: 'Break-Even 16 - Product concerns are actively solicited, impartially judged and transparently addressed'
  },
  {
    code: 'futurefitBe17',
    title: 'Future-Fit BE17',
    logoSrc: 'futureFit.png',
    ratingAgencyUrl: 'https://futurefitbusiness.org/',
    additionalInformation: 'Break-Even 17 - Products do not harm people or the environment'
  },
  {
    code: 'futurefitBe18',
    title: 'Future-Fit BE18',
    logoSrc: 'futureFit.png',
    ratingAgencyUrl: 'https://futurefitbusiness.org/',
    additionalInformation: 'Break-Even 18 - Products emit no greenhouse gases'
  },
  {
    code: 'futurefitBe19',
    title: 'Future-Fit BE19',
    logoSrc: 'futureFit.png',
    ratingAgencyUrl: 'https://futurefitbusiness.org/',
    additionalInformation: 'Break-Even 19 - Products can be repurposed'
  },
  {
    code: 'futurefitBe20',
    title: 'Future-Fit BE20',
    logoSrc: 'futureFit.png',
    ratingAgencyUrl: 'https://futurefitbusiness.org/',
    additionalInformation: 'Break-Even 20 - Business is conducted ethically'
  },
  {
    code: 'futurefitBe21',
    title: 'Future-Fit BE21',
    logoSrc: 'futureFit.png',
    ratingAgencyUrl: 'https://futurefitbusiness.org/',
    additionalInformation: 'Break-Even 21 - The right tax is paid in the right place at the right time'
  },
  {
    code: 'futurefitBe22',
    title: 'Future-Fit BE22',
    logoSrc: 'futureFit.png',
    ratingAgencyUrl: 'https://futurefitbusiness.org/',
    additionalInformation: 'Break-Even 22 - Lobbying and advocacy safeguard the pursuit of future-fitness'
  },
  {
    code: 'futurefitBe23',
    title: 'Future-Fit BE23',
    logoSrc: 'futureFit.png',
    ratingAgencyUrl: 'https://futurefitbusiness.org/',
    additionalInformation: 'Break-Even 23 - Financial assets safeguard the pursuit of future-fitness'
  },
  getCdpRating({ code: 'cdp-climate-change-2018', title: 'CDP Climate Change 2018'}),
  getCdpRating({ code: 'cdpWater2018', title: 'CDP Water 2018'}),
  getCdpRating({ code: 'cdpForestsCattle2018', title: 'CDP Forests (Cattle) 2018'}),
  getCdpRating({ code: 'cdpForestsPalmOil2018', title: 'CDP Forests (Palm Oil) 2018'}),
  getCdpRating({ code: 'cdpForestsSoy2018', title: 'CDP Forests (Soy) 2018'}),
  getCdpRating({ code: 'cdpForestsTimber2018', title: 'CDP Forests (Timber) 2018'}),

  getCdpRating({ code: 'cdp-climate-change-2019', title: 'CDP Climate Change 2019'}),
  getCdpRating({ code: 'cdpWater2019', title: 'CDP Water 2019'}),
  getCdpRating({ code: 'cdpForestsCattle2019', title: 'CDP Forests (Cattle) 2019'}),
  getCdpRating({ code: 'cdpForestsPalmOil2019', title: 'CDP Forests (Palm Oil) 2019'}),
  getCdpRating({ code: 'cdpForestsSoy2019', title: 'CDP Forests (Soy) 2019'}),
  getCdpRating({ code: 'cdpForestsTimber2019', title: 'CDP Forests (Timber) 2019'}),

  getCdpRating({ code: 'cdp-climate-change-2020', title: 'CDP Climate Change 2020'}),
  getCdpRating({ code: 'cdpWater2020', title: 'CDP Water 2020'}),
  getCdpRating({ code: 'cdpForestsCattle2020', title: 'CDP Forests (Cattle) 2020'}),
  getCdpRating({ code: 'cdpForestsPalmOil2020', title: 'CDP Forests (Palm Oil) 2020'}),
  getCdpRating({ code: 'cdpForestsSoy2020', title: 'CDP Forests (Soy) 2020'}),
  getCdpRating({ code: 'cdpForestsTimber2020', title: 'CDP Forests (Timber) 2020'}),
  {
    code: 'supportTheGoals',
    title: 'Support the Goals',
    logoSrc: 'supportTheGoals.png',
    ratingAgencyUrl: 'https://supportthegoals.org/',
    additionalInformation: 'Discover how businesses are supporting the Global Goals.'
  },
  {
    code: 'snpGlobal',
    title: 'S&P Global',
    logoSrc: 'snpRatings.png',
    ratingAgencyUrl: 'https://www.spglobal.com/ratings/',
    additionalInformation: 'S&P Global provide intelligence that is embedded into the workflow and decision-making of customers around the globe'
  },
  {
    code: 'gri',
    title: 'Global Reporting Initiative (GRI)',
    logoSrc: 'gri.png',
    ratingAgencyUrl: 'https://www.globalreporting.org/',
    additionalInformation: 'The Global Reporting Initiative is an international independent standards organization that helps businesses, governments and other organizations understand and communicate their impacts on issues such as climate change, human rights and corruption.',
  },
  {
    code: 'bcorp',
    title: 'B-Corp Certified',
    logoSrc: 'blabs.png',
    ratingAgencyUrl: 'https://bcorporation.net/',
    additionalInformation: 'B Lab is a nonprofit that serves a global movement of people using business as a force for good. B Lab’s initiatives include B Corp Certification, administration of the B Impact Management programs and software, andadvocacy for governance structures like the benefit corporation.',
  },
  {
    code: 'djsi',
    title: 'DJSI Ranking',
    logoSrc: 'djsi.png',
    ratingAgencyUrl: 'https://www.spglobal.com/esg/csa/indices/',
    additionalInformation: 'The Dow Jones Sustainability Index family tracks the stock performance of the world\'s leading companies in terms of economic, environmental and social criteria.',
  },
  {
    code: 'iso',
    title: 'ISO Certifications',
    logoSrc: 'iso.jpg',
    ratingAgencyUrl: 'https://www.iso.org/certification.html',
    additionalInformation: 'The International Organization for Standardization is an independent, non-governmental organization, the members of which are the standards organizations of the 165 member countries. It is the world&#39;s largest developer of voluntary international standards and it facilitates world trade by providing common standards among nations. More than twenty thousand standards have been set, covering everything from manufactured products and technology to food safety, agriculture, and healthcare.',
  },
];

export interface RatingsRepository {
  list(args: RatingsListQuery): Promise<any>;
  ratingAgencies(): Promise<RatingAgency[]>
}

const getRatingWithAgency = (ratingObj: InitiativeRating) => {
  const { _id, code, rating, link, linkText, date } = ratingObj;
  const agency = ratingAgencies.find(a => a.code === code);
  if (!agency) {
    return;
  }
  return {
    ...agency,
    _id,
    rating,
    link,
    linkText,
    date,
    logoSrc: `${logoSrcRoot}${agency.logoSrc}`
  };
};


class DummyRatingsRepository implements RatingsRepository {
  async list(query: RatingsListQuery): Promise<any> {
    const initiative = await Initiative.findById(query.initiativeId, {
      code: 1,
      ratings: 1
    }).lean();

    return (initiative?.ratings ?? []).map(getRatingWithAgency).filter(Boolean);
  }

  public async ratingAgencies() {
    return ratingAgencies.map(r => r);
  }
}

export class RatingsListQuery {
  initiativeId: string;
  constructor(initiativeId: string = '') {
    this.initiativeId = initiativeId;
  }
}

export function getRatingsRepository(): RatingsRepository {
  return new DummyRatingsRepository();
}
