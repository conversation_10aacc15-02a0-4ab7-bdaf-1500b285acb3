import { ObjectId } from 'bson';
import Onboarding, {
  OrganizationOnboardingModel,
  OnboardingStatus,
  OrganizationOnboardingPlain,
  ObType,
  OnboardingModel,
} from '../models/onboarding';
import { OnboardingRepository, statusCondition } from './OnboardingRepository';
import User, { SafeUser, safeUserFields } from '../models/user';
import { OrganizationModel } from '../models/organization';
import { FilterQuery, ProjectionType, QueryOptions } from 'mongoose';

export class OrganizationOnboardingRepository extends OnboardingRepository {
  public async find(
    match: FilterQuery<OnboardingModel>,
    projection?: ProjectionType<OnboardingModel>,
    options?: QueryOptions
  ) {
    return Onboarding.find(
      { ...match, type: ObType.Organization, organizationId: { $exists: true } },
      projection,
      options
    ).exec() as Promise<OrganizationOnboardingModel[]>;
  }

  public async findExisting(email: string, organizationId: ObjectId) {
    return Onboarding.findOne({
      type: ObType.Organization,
      'user.email': email,
      organizationId,
      status: statusCondition,
    }).exec() as Promise<OrganizationOnboardingModel>;
  }

  public async findExistingByOrganizationId({
    email,
    organizationId,
    statuses = statusCondition['$in'],
  }: {
    email: string;
    organizationId: ObjectId;
    statuses?: OnboardingStatus[];
  }) {
    return Onboarding.find({
      type: ObType.Organization,
      'user.email': email,
      organizationId,
      status: { $in: statuses },
    }).exec() as Promise<OrganizationOnboardingModel[]>;
  }

  public async findExistingRootById(onboardingId: string, organizationId: ObjectId) {
    return Onboarding.findOne({
      type: ObType.Organization,
      _id: onboardingId,
      organizationId,
      status: statusCondition,
    }).exec() as Promise<OrganizationOnboardingModel>;
  }

  public async findOnboardingsSortedDescByCreated(organizationIds: ObjectId[], statuses: OnboardingStatus[]) {
    return Onboarding.find({
      type: ObType.Organization,
      status: { $in: statuses },
      organizationId: { $in: organizationIds },
    })
      .sort({ created: 'desc' })
      .lean<OrganizationOnboardingPlain[]>()
      .exec();
  }

  public async getOrganizationUsers(organization: Pick<OrganizationModel, '_id' | 'permissions'>) {
    const users = await User.find(
      {
        active: true,
        $or: [
          {
            organizationId: organization._id,
          },
          {
            _id: {
              $in: organization.permissions?.map((p) => p.userId),
            },
          },
        ],
      },
      { ...safeUserFields, email: 1 }
    )
      .lean<(SafeUser & { email: string; invitedDate?: Date })[]>()
      .exec();

    const invitations = await this.findOnboardingsSortedDescByCreated([organization._id], [
      ...statusCondition['$in'],
      OnboardingStatus.Complete,
    ]);

    const pendingInvitations = invitations.filter((i) => statusCondition['$in'].includes(i.status));

    users.forEach((user) => {
      user.invitedDate = invitations.find((i) => i.user.email === user.email)?.created;
    });

    return { users, invitations: pendingInvitations }
  }
}


let instance: OrganizationOnboardingRepository;
export const getOrganizationOnboardingRepository = () => {
  if (!instance) {
    instance = new OrganizationOnboardingRepository();
  }
  return instance;
};
