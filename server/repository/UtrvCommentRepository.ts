import { ObjectId } from 'bson';
import UniversalTrackerValueComments from '../models/universalTrackerValueComments';

export class UtrvCommentRepository {
  constructor(private utrvCommentsModel: typeof UniversalTrackerValueComments) {}

  public async countCommentsByUtrvIds(utrvIds: (string | ObjectId)[]) {
    if (!utrvIds.length) {
      return [];
    }

    const aggregate = [
      {
        $match: {
          utrvId: { $in: utrvIds },
        },
      },
      {
        $project: {
          _id: 1,
          utrvId: 1,
          commentCount: {
            $size: '$items',
          },
        },
      },
    ];

    return this.utrvCommentsModel.aggregate(aggregate);
  }
}

let instance: UtrvCommentRepository;
export const getUtrvCommentRepository = () => {
  if (!instance) {
    instance = new UtrvCommentRepository(UniversalTrackerValueComments);
  }

  return instance;
};
