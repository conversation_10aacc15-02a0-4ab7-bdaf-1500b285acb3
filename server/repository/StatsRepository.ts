/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import Survey from '../models/survey';
import type { RequestScope } from '../service/survey/model/DelegationScope';
import HttpError from '../error/HttpError';
import { activeBlueprints, Blueprints } from '../survey/blueprints';
import { getBluePrintContribution } from '../service/survey/BlueprintContribution';
import { getCombinedScopeGroups } from '../service/survey/scope/scopeGroups';
import UniversalTrackerValue from '../models/universalTrackerValue';
import { valueChainCategories } from '../util/valueChain';
import Initiative, { CompanyTrackerEnterpriseIds, CompanyTrackerProIds, CompanyTrackerStarterIds, InitiativePlain, InitiativeTags, InitiativeTypes, PERMISSION_GROUPS } from '../models/initiative';
import { PipelineStage } from 'mongoose';
import { ActionList } from '../service/utr/constants';
import { filterByFramework, filterBySdgCodes, filterByStandard } from "../service/survey/scope/filterScope";
import MetricGroup from "../models/metricGroup";
import { ObjectId } from "bson";
import { getBlueprintRepository } from "./BlueprintRepository";
import UniversalTracker from "../models/universalTracker";
import { extractVisibleUtrCodes } from "../survey/surveyForms";
import { ConvertEmailRules, UserRepository } from './UserRepository';
import { UserPlain } from '../models/user';
import { UserRoles } from '../service/user/userPermissions';
import { getSubscriptionManager, SubscriptionManager } from '../service/organization/SubscriptionManager';
import { excludeSoftDeleted } from './aggregations';

export enum ProductFilter {
  SGXESGenome = 'sgx_esgenome',
  MaterialityTracker = 'materiality_assessment',
  CompanyTracker = 'company_tracker',
  CompanyTrackerPro = 'company_tracker_pro',
  CompanyTrackerEnterprise = 'company_tracker_enterprise',
  CompanyTrackerStarter = 'company_tracker_starter',
  PortfolioTracker = 'portfolio_tracker',
  PortfolioTrackerExchange = 'portfolio_tracker_exchange',
}

export interface MatchParams {
  fromDate?: string,
  toDate?: string
  showStaff?: boolean;
  scopeTag?: string;
  productCodes?: string[];
  referralCode?: string;
  hideArchived?: boolean;
}

export interface ScopeGroupsParams extends MatchParams {
  scopeGroups: RequestScope[];
  sourceName?: Blueprints;
}

interface CompanyStatsMin
  extends Pick<
    InitiativePlain,
    '_id' | 'name' | 'code' | 'type' | 'created' | 'appConfigCode' | 'permissionGroup' | 'tags' | 'country'
  > {
  surveyCount: number;
  userCount: number;
  users: UserPlain[];
  isActive: boolean;
}

export interface CompanyStats extends Omit<CompanyStatsMin, 'users'> {
  owners: Pick<UserPlain, "email">[];
}

const isScopeParams = (params: MatchParams & Partial<Pick<ScopeGroupsParams, 'scopeGroups'>>): params is ScopeGroupsParams => {
  return Array.isArray(params.scopeGroups) && params.scopeGroups.length > 0;
}

export class StatsRepository {

  constructor(private subscriptionManager: SubscriptionManager) {}

  private getMatch(params: MatchParams) {
    return {
      sourceName: {
        $in: activeBlueprints
      },
      deletedDate: { $exists: false },
      ...this.getCreatedFilter(params),
    };
  }

  private getCreatedFilter(params: MatchParams, propName = 'created') {
    const created: { $gte?: Date, $lte?: Date } = {};
    if (params.fromDate) {
      created.$gte = new Date(params.fromDate);
    }
    if (params.toDate) {
      created.$lte = new Date(params.toDate);
    }
    return Object.keys(created).length > 0 ? { [propName]: created } : {};
  }

  private generateInitiativeMatch(params: MatchParams, lookup?: string) {
    const prefix = lookup ? lookup.concat('.') : '';

    const staffFilter = params.showStaff
      ? {
          [`${prefix}tags`]: { $in: [InitiativeTags.StaffOrganization, InitiativeTags.Organization] },
        }
      : {
         [`${prefix}tags`]: { $eq: InitiativeTags.Organization, $ne: InitiativeTags.StaffOrganization },
        };

    const $match = {
      ...this.getFilterProductCodes(params.productCodes, prefix),
      ...(params.referralCode ? { [`${prefix}referrals.code`]: { $eq: params.referralCode } } : {}),
      ...staffFilter,
    };

    return $match;
  }

  private getFilterProductCodes (productCodes: undefined | string[], prefix: string) {
    if (!productCodes) {
      return {};
    }

    const $or = [] as { [k: string]: unknown }[];
    if (!productCodes.length) {
      $or.push({ [`${prefix}permissionGroup`]: { $in: [] } });
    }

    const filterCTlightAndESGenome = this.getFilterCTlightAndESGenome(productCodes, prefix);
    if (filterCTlightAndESGenome) {
      $or.push({ $and: filterCTlightAndESGenome });
    }

    if (productCodes.includes(ProductFilter.CompanyTrackerPro)) {
      $or.push({ [`${prefix}permissionGroup`]: { $in: CompanyTrackerProIds } });
    }

    if (productCodes.includes(ProductFilter.CompanyTrackerEnterprise)) {
      $or.push({ [`${prefix}permissionGroup`]: { $in: CompanyTrackerEnterpriseIds } });
    }
    // filter out materiality tracker
    if (productCodes.includes(ProductFilter.CompanyTrackerStarter)) {
      $or.push({
        $and: [
          { [`${prefix}permissionGroup`]: { $in: CompanyTrackerStarterIds } },
          { [`${prefix}appConfigCode`]: { $ne: ProductFilter.MaterialityTracker } },
        ],
      });
    }

    const filterPortfolioTracker = this.getFilterPortfolioTracker(productCodes, prefix);

    if (filterPortfolioTracker) {
      $or.push({ $and: filterPortfolioTracker });
    }

    if (productCodes.includes(ProductFilter.MaterialityTracker)) {
      $or.push({ [`${prefix}appConfigCode`]: { $eq: ProductFilter.MaterialityTracker } });
    }

    if ($or.length === 0) {
      return undefined;
    }

    return { $or };
  }

  private getFilterCTlightAndESGenome(productCodes: string[], prefix: string) {
    const filterCTLight = productCodes.includes(ProductFilter.CompanyTracker);
    const filterESGenome = productCodes.includes(ProductFilter.SGXESGenome);

    if (!filterCTLight && !filterESGenome) {
      return;
    }
    const $and = [{ [`${prefix}permissionGroup`]: { $in: [PERMISSION_GROUPS.COMPANY_TRACKER_LIGHT] } }] as { [k: string]: unknown }[];

    if (filterCTLight && !filterESGenome) {
      $and.push({ [`${prefix}appConfigCode`]: { $ne: ProductFilter.SGXESGenome } });
    }

    if (!filterCTLight && filterESGenome) {
      $and.push({ [`${prefix}appConfigCode`]: { $eq: ProductFilter.SGXESGenome } });
    }

    return $and;
  }

  private getFilterPortfolioTracker (productCodes: string[], prefix: string) {
    const filterPT = productCodes.includes(ProductFilter.PortfolioTracker);
    const filterPTExchange = productCodes.includes(ProductFilter.PortfolioTrackerExchange);

    if (!filterPT && !filterPTExchange) {
      return;
    }
    const $and = [{ [`${prefix}type`]: { $eq: InitiativeTypes.Group } }] as { [k: string]: unknown }[];

    if (filterPT && !filterPTExchange) {
      $and.push({ [`${prefix}permissionGroup`]: { $ne: PERMISSION_GROUPS.PORTFOLIO_TRACKER_EXCHANGE } });
    }

    if (!filterPT && filterPTExchange) {
      $and.push({ [`${prefix}permissionGroup`]: { $eq: PERMISSION_GROUPS.PORTFOLIO_TRACKER_EXCHANGE } });
    }

    return $and;
  }

  private getCompanyStatsPipeline(params: MatchParams): PipelineStage[] {
    if (params.hideArchived) {
      return [
        {
          $match: {
            ...this.generateInitiativeMatch(params),
            ...this.getCreatedFilter(params)
          }
        },
        {
          $lookup: {
            from: 'surveys',
            localField: '_id',
            foreignField: 'initiativeId',
            pipeline: [{ $match: excludeSoftDeleted() }],
            as: 'surveys'
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: '_id',
            foreignField: 'permissions.initiativeId',
            as: 'users'
          }
        },
        {
          $project: {
            name: 1,
            code: 1,
            type: 1,
            permissionGroup: 1,
            country: 1,
            tags: 1,
            created: 1,
            appConfigCode: 1,
            surveyCount: { $size: '$surveys' },
            userCount: { $size: '$users' },
            users: {
              _id: 1,
              firstName: 1,
              surname: 1,
              email: 1,
              permissions: 1,
              isStaff: 1
            }
          },
        },
        {
          $addFields: {
            isActive: true,
          },
        },
        {
          $sort: {
            name: 1
          }
        }
      ];
    }
    return [
      {
        $addFields: {
          isActive: true,
        },
      },
      {
        $unionWith: {
          coll: "archived-initiatives",
        },
      },
      {
        $addFields: {
          isActive: { $ifNull: ["$isActive", false] },
        },
      },
      {
        $match: {
          ...this.generateInitiativeMatch(params),
          ...this.getCreatedFilter(params)
        }
      },
      {
        $lookup: {
          from: 'surveys',
          localField: '_id',
          foreignField: 'initiativeId',
          pipeline: [{ $match: excludeSoftDeleted() }],
          as: 'surveys'
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: 'permissions.initiativeId',
          as: 'users'
        }
      },
      {
        $project: {
          name: 1,
          code: 1,
          type: 1,
          permissionGroup: 1,
          country: 1,
          tags: 1,
          created: 1,
          appConfigCode: 1,
          surveyCount: { $size: '$surveys' },
          userCount: { $size: '$users' },
          isActive: 1,
          users: {
            _id: 1,
            firstName: 1,
            surname: 1,
            email: 1,
            permissions: 1,
            isStaff: 1
          }
        },
      },
      {
        $sort: {
          name: 1
        }
      }
    ];
  }

  async companyStats(params: MatchParams): Promise<CompanyStats[]> {
    const aggregate: PipelineStage[] = this.getCompanyStatsPipeline(params);
    const initiatives = await Initiative.aggregate<CompanyStatsMin>(aggregate).exec();

    return initiatives.map((initiative) => {
      const { users, ...otherProps } = initiative;
      const owners = users.filter((u) =>
        u.permissions.some(
          (p) => String(p.initiativeId) === String(initiative._id) && p.permissions.includes(UserRoles.Owner)
        )
      );
      const emailRules: ConvertEmailRules = {
        numOfFirstLetters: 4,
        numOfAtLetters: 5,
      }

      return {
        ...otherProps,
        owners: UserRepository.anonymize(owners, emailRules),
        productCode: this.subscriptionManager.fromPermissionGroup(initiative),
      };
    });
  }

  public async surveyStats(params: MatchParams, extended = false) {

    const { scopeTag = 'sgx_metrics' } = params;

    const aggregate: PipelineStage[] =
      [
        {
          $match: this.getMatch(params)
        },
        {
          $lookup: {
            from: 'universal-tracker-values',
            localField: 'visibleUtrvs',
            foreignField: '_id',
            as: 'utrvs'
          }
        },
        {
          $lookup: {
            from: 'initiatives',
            localField: 'initiativeId',
            foreignField: '_id',
            as: 'initiative'
          }
        },
        {
          $lookup: {
            from: 'universal-trackers',
            localField: 'utrvs.universalTrackerId',
            foreignField: '_id',
            pipeline: [
              {
                $match: {
                  $expr: {
                    $or: [
                      {
                        $ifNull: [`$alternatives.${scopeTag}`, false]
                      },
                      {
                        $eq: ['$type', scopeTag],
                      },
                    ],
                  },
                },
              },
            ],
            as: scopeTag,
          },
        },
        {
          $unwind: '$initiative'
        },
        {
          $match: this.generateInitiativeMatch(params, 'initiative'),
        },
      ];

    const project = {
      _id: 1,
      name: 1,
      scopeCount: { $size: "$utrvs" },
      verifiedCount: {
        $size: {
          $filter: {
            input: '$utrvs',
            cond: {
              $eq: ['$$this.status', ActionList.Verified]
            }
          }
        }
      },
      answeredCount: {
        $size: {
          $filter: {
            input: '$utrvs',
            cond: {
              $eq: ['$$this.status', ActionList.Updated]
            }
          }
        }
      },
      [`${scopeTag}_verified`]: {
        $size: {
          $filter: {
            input: '$utrvs',
            cond: {
              $and: [
                { $eq: ['$$this.status', ActionList.Verified] },
                { $in: ['$$this.universalTrackerId', `$${scopeTag}._id`] },
              ]
            }
          }
        }
      },
      rejectedCount: {
        $size: {
          $filter: {
            input: '$utrvs',
            cond: {
              $eq: ['$$this.status', ActionList.Rejected]
            }
          }
        }
      },
      initiativeId: 1,
      effectiveDate: 1,
      completedDate: 1,
      created: 1,
      initiativeName: '$initiative.name',
      ...(extended ? { scope: 1 } : {})
    }

    aggregate.push({
      $project: project,
    });

    aggregate.push({
      $sort: {
        name: 1,
      },
    });

    return Survey.aggregate(aggregate);
  }

  public async questionStats(params: MatchParams, extended = false) {

    const aggregate: PipelineStage[] =
      [
        {
          $match: this.getMatch(params)
        },
        {
          $lookup: {
            from: 'initiatives',
            localField: 'initiativeId',
            foreignField: '_id',
            as: 'initiative'
          }
        },
        {
          $unwind: '$initiative'
        },
        {
          $match: this.generateInitiativeMatch(params, 'initiative'),
        },
        {
          $unwind: '$visibleUtrvs',
        },
        {
          $project: {
            visibleUtrvs: 1
          }
        },
        {
          $lookup: {
            from: 'universal-tracker-values',
            localField: 'visibleUtrvs',
            foreignField: '_id',
            as: 'utrv'
          }
        },
        {
          $unwind: '$utrv',
        },
        {
          $project: {
            _id: '$utrv._id',
            universalTrackerId: '$utrv.universalTrackerId',
            status: '$utrv.status',
          }
        },
        {
          $group: {
            _id: {
              universalTrackerId: '$universalTrackerId',
              status: '$status'
            },
            count: {
              $sum: 1
            }
          }
        },
        {
          $lookup: {
            from: 'universal-trackers',
            localField: '_id.universalTrackerId',
            foreignField: '_id',
            as: 'utr'
          }
        },
        {
          $unwind: '$utr'
        }
      ];

    if (extended) {
      aggregate.push(
        {
          $project: {
            _id: '$_id.universalTrackerId',
            name: '$utr.name',
            status: '$_id.status',
            count: 1,
            tags: '$utr.tags',
            alternatives: '$utr.alternatives'
          }
        }
      );
    } else {
      aggregate.push(
        {
          $project: {
            _id: '$_id.universalTrackerId',
            name: '$utr.name',
            status: '$_id.status',
            count: 1
          }
        }
      );
    }

    aggregate.push(
      {
        $sort: {
          name: 1
        }
      }
    );

    return Survey.aggregate(aggregate);
  }

  public async questionScopeTag(data: ScopeGroupsParams) {

    if (!isScopeParams(data)) {
      throw new HttpError('scopeGroups parameter must be non empty array', 400)
    }

    const bc = getBluePrintContribution();
    const blueprintRepo = getBlueprintRepository();

    const scopeGroups = getCombinedScopeGroups(data.scopeGroups);

    const standards = Array.from(scopeGroups.standards ?? new Set());
    const frameworks = Array.from(scopeGroups.frameworks ?? new Set());
    const sdgs = Array.from(scopeGroups.sdg ?? new Set());
    const custom = Array.from(scopeGroups.custom ?? new Set());
    const metricGroupIds = custom.map(id => new ObjectId(id));

    const blueprintCodes = data.sourceName ? [data.sourceName] : activeBlueprints;
    const blueprints = await Promise.all(blueprintCodes.map(async (blueprintCode) => {
      const blueprint = await blueprintRepo.getBlueprint(blueprintCode);
      if (!blueprint) {
        throw new Error(`Failed to find blueprint by ${blueprintCode}`);
      }

      const contributions = await bc.getContributions(blueprintCode);

      return { blueprint, contributions };
    }))

    const codes = blueprints.reduce((acc, { blueprint }) => {
      extractVisibleUtrCodes(blueprint).forEach(code => acc.add(code))
      return acc;
    }, new Set<string>());

    const groups = metricGroupIds.length > 0 ? await MetricGroup.find({ _id: { $in: metricGroupIds } }, { universalTrackers: 1 }).lean().exec() : [];
    const customIds = new Set(groups.map(g => g.universalTrackers.map(String)).flat());

    const questions = await UniversalTracker.find(
      {
        $or: [
          { _id: { $in: Array.from(customIds).map(id => new ObjectId(id)) } },
          { code: { $in: Array.from(codes) } },
        ]
      },
      { type: 1, typeTags: 1, alternatives: 1, tags: 1, code: 1, _id: 1 },
    ).lean().exec();

    const filteredUtrs = questions.filter(utr => {
      if (filterByStandard(standards, utr.type, utr.typeTags, utr.alternatives)) {
        return true;
      }
      if (filterByFramework(frameworks, utr.tags)) {
        return true;
      }

      if (customIds.has(utr._id.toString())) {
        return true;
      }
      return blueprints.some(({ contributions }) => filterBySdgCodes(contributions, sdgs, utr.code));
    });


    const aggregate: PipelineStage[] = [
      {
        $match: {
          universalTrackerId: { $in: filteredUtrs.map(o => o._id) },
          // Only apply fromDate, as we know for sure each utrv must be created after survey is created
          // as in this case fromDate means from survey created date
          ...(data.fromDate ? { created: { $gte: new Date(data.fromDate) } } : {})
        },
      },
      {
        $group: {
          _id: {
            initiativeId: '$initiativeId',
            surveyId: '$compositeData.surveyId',
          },
          utrvs: { $push: "$status" }
        },
      },
      {
        $lookup: {
          from: 'initiatives',
          localField: '_id.initiativeId',
          foreignField: '_id',
          as: 'initiative'
        }
      },
      {
        $lookup: {
          from: 'surveys',
          localField: '_id.surveyId',
          foreignField: '_id',
          as: 'survey'
        }
      },
      {
        $project: {
          initiativeName: { $arrayElemAt: ['$initiative.name', 0] },
          initiativeCode: { $arrayElemAt: ['$initiative.code', 0] },
          survey: { $arrayElemAt: ['$survey', 0] },
          utrvs: 1,
          tags: { $arrayElemAt: ['$initiative.tags', 0] },
        }
      },
      {
        $match: {
          tags: { $nin: valueChainCategories },
          'survey.sourceName': { $in: activeBlueprints },
          'survey.deletedDate': { $exists: false },
          // Main filter for created, to ensure it match list page filtering logic
          ...this.getCreatedFilter(data, 'survey.created'),
        },
      },
      {
        $project: {
          scope: '$survey.scope',
          count: { $size: '$utrvs' },
          initiativeName: 1,
          initiativeCode: 1,
          verifiedCount: {
            $size: {
              $filter: {
                input: '$utrvs',
                cond: {
                  $eq: ['$$this', ActionList.Verified]
                }
              }
            }
          },
          answeredCount: {
            $size: {
              $filter: {
                input: '$utrvs',
                cond: {
                  $ne: ['$$this', ActionList.Created]
                }
              }
            }
          },
        }
      },
    ];

    const v = await UniversalTrackerValue.aggregate(aggregate);
    return {
      universalTrackerValues: v,
    };
  }
}

let instance: StatsRepository;
export const getStatsRepository = () => {
  if (!instance) {
    instance = new StatsRepository(getSubscriptionManager());
  }
  return instance;
}
