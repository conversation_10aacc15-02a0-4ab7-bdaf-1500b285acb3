/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import User, { UserMin, userMinFields, UserPlain } from '../models/user';
import {
  AssuranceListPortfolioMin,
  AssurancePortfolioExpanded,
  AssurancePortfolioExpandedExtra,
  AssurancePortfolioModel,
  AssurancePortfolioPlain,
  AssurancePortfolioStatus,
} from '../service/assurance/model/AssurancePortfolio';
import {
  assurancePortfolioProjection,
  assuranceSurvey,
  organizationProjectFields,
  utrvAssuranceProjection,
} from './projections';
import AssurancePortfolio from '../models/assurancePortfolio';
import { addDataPropertyUrl, addDocumentUrl } from '../service/storage/fileStorage';
import UniversalTrackerValueAssurance from '../models/universalTrackerValueAssurance';
import {
  AssuranceStatus,
  disabledUtrvAssuranceStatuses,
  UniversalTrackerValueAssuranceModel,
  UtrvAssuranceExtended,
} from '../service/assurance/model/Assurance';
import { groupAssurer, utrvAssuranceLookUp } from './aggregations';
import Organization, { OrganizationPartnerTypes } from '../models/organization';
import { SurveyRepository } from './SurveyRepository';
import { PipelineStage } from 'mongoose';
import { InitiativeRepository, RootInitiativeDataMin } from './InitiativeRepository';
import DocumentModel, { insightDocumentProjection } from '../models/document';
import { InsightDocument } from '../types/document';

interface AssuranceQuery {
  organizationId: ObjectId;
  portfolioId: ObjectId | string;
  domainOrigin?: string;
}

interface UtrvAssuranceWithPortfolio extends Omit<UtrvAssuranceExtended, 'portfolio'> {
  portfolio: Pick<AssurancePortfolioPlain, 'status'>;
}

export class AssuranceRepository {

  public static async findPortfolioById(id: ObjectId | string, lean?: true): Promise<AssurancePortfolioPlain | null>;
  public static async findPortfolioById(id: ObjectId | string, lean?: false): Promise<AssurancePortfolioModel | null>;
  public static async findPortfolioById(id: ObjectId | string, lean: boolean = true): Promise<AssurancePortfolioPlain | AssurancePortfolioModel | null> {
    return AssurancePortfolio.findOne({
      _id: new ObjectId(id),
      status: { $ne: AssurancePortfolioStatus.Deleted },
    }).lean(lean).exec();
  }

  public static async findPortfoliosByOrgId(orgId: ObjectId): Promise<AssuranceListPortfolioMin[]> {
    // This is used by Assurer to get any pending or completed assurances

    const readyForAssurer = [AssurancePortfolioStatus.Pending, AssurancePortfolioStatus.Completed];
    const baseProjectFields = {
      _id: 1,
      description: 1,
      status: 1,
      documents: 1,
      survey: {
        _id: 1,
        name: 1,
        effectiveDate: 1,
        deletedDate: 1,
      },
      universalTrackerValueAssurances: 1,
      universalTrackerValues: {
        _id: 1,
        universalTrackerId: 1,
        initiativeId: 1,
        status: 1,
      },
      initiative: {
        _id: 1,
        name: 1,
        tags: 1,
        appConfigCode: 1,
      },
    }

    return AssurancePortfolio.aggregate([
      {
        $match: {
          organizationId: orgId,
          status: { $in: readyForAssurer },
        },
      },
      {
        $lookup: {
          from: 'surveys',
          localField: 'surveyId',
          foreignField: '_id',
          as: 'survey',
        }
      },
      {
        $match: {
          deletedDate: null
        }
      },
      {
        $unwind: {
          'path': '$survey'
        }
      },
      {
        $lookup: {
          from: 'initiatives',
          localField: 'initiativeId',
          foreignField: '_id',
          as: 'initiative',
        }
      },
      utrvAssuranceLookUp('_id', 'portfolioId'),
      {
        $lookup: {
          from: 'universal-tracker-values',
          localField: 'universalTrackerValueAssurances.utrvId',
          foreignField: '_id',
          as: 'universalTrackerValues',
        }
      },
      {
        $unwind: {
          'path': '$initiative'
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'permissions.userId',
          foreignField: '_id',
          as: 'assurers',
        },
      },
      {
        $project: {
          ...baseProjectFields,
          assurers: userMinFields,
          permissions: 1,
        }
      },
      {
        $project: {
          ...baseProjectFields,
          ...groupAssurer,
        }
      }
    ])
      .exec();
  }

  public static async getPortfoliosWithRootInitiative(orgId: ObjectId) {
    const assurancePortfolios = await this.findPortfoliosByOrgId(orgId);
    const rootInitiativeMap = await this.getRootInitiativeMap(assurancePortfolios);

    return assurancePortfolios.map((assurancePortfolio) => {
      const initiative = assurancePortfolio.initiative;
      const rootInitiative = rootInitiativeMap.get(initiative._id.toString());

      return {
        ...assurancePortfolio,
        initiative: { _id: initiative._id, name: initiative.name },
        rootInitiative: rootInitiative ? { _id: rootInitiative._id, name: rootInitiative.name } : undefined,
      };
    });
  }

  private static async getRootInitiativeMap(assurancePortfolios: AssuranceListPortfolioMin[]) {
    const rootInitiativeMap = new Map<string, RootInitiativeDataMin>();

    const initiativeMap = assurancePortfolios.reduce((acc, { initiative }) => {
      const initiativeId = initiative._id.toString();
      if (acc.get(initiativeId)) {
        return acc;
      }
      acc.set(initiativeId, initiative);
      return acc;
    }, new Map<string, RootInitiativeDataMin>());

    await Promise.all(
      Array.from(initiativeMap.values()).map(async (initiative) => {
        const rootInitiative = await InitiativeRepository.getOrganization(initiative);
        rootInitiativeMap.set(initiative._id.toString(), rootInitiative);
      })
    );

    return rootInitiativeMap;
  }

  public static async findPortfoliosBySurveyId(surveyId: string | ObjectId) {
    return AssurancePortfolio.find({
      surveyId: new ObjectId(surveyId),
      status: { $ne: AssurancePortfolioStatus.Deleted },
    }).exec();
  }

  public static async findUniversalTrackerValueAssuranceExtended(portfolioId: ObjectId, optionalMatch = {}) {
    return await UniversalTrackerValueAssurance.find({
      portfolioId,
      ...optionalMatch
    }).populate('universalTrackerValue').exec() as UtrvAssuranceExtended[];
  }

  public static async findSelectedUniversalTrackerValueAssuranceUsers({
    portfolioId,
    utrvAssuranceIds,
  }: {
    portfolioId: ObjectId;
    utrvAssuranceIds: string[];
  }): Promise<UserMin[]> {
    return UniversalTrackerValueAssurance.aggregate([
      {
        $match: {
          portfolioId: portfolioId,
          utrvId: { $in: utrvAssuranceIds.map((id) => new ObjectId(id)) },
          status: { $ne: AssuranceStatus.Removed },
        },
      },
      {
        $unwind: {
          path: '$permissions',
        },
      },
      {
        $group: {
          _id: '$permissions.userId',
          count: { $sum: 1 },
        },
      },
      {
        $match: {
          count: utrvAssuranceIds.length,
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'assignee',
        },
      },
      {
        $project: {
          _id: 1,
          assignee: { $arrayElemAt: ['$assignee', 0] },
        },
      },
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: ['$$ROOT', '$assignee'],
          },
        },
      },
      {
        $project: {
          ...userMinFields,
        },
      },
    ]).exec();
  }

  /** Find any existing (even removed) utrv assurance **/
  public static async findOneUniversalTrackerValueAssuranceExtended(portfolioId: ObjectId, utrvId: ObjectId) {
    return UniversalTrackerValueAssurance
      .findOne({ utrvId, portfolioId })
      .populate('universalTrackerValue')
      .exec();
  }

  /**
   * Automatically exclude Removed, unless explicitly override status
   */
  public static async findByUtrvId(utrvId: ObjectId, optionalMatch = {}) {
    return UniversalTrackerValueAssurance.find({
      utrvId,
      status: { $nin: disabledUtrvAssuranceStatuses },
      ...optionalMatch,
    }).exec()
  }

  /**
   * Automatically exclude Disabled assuranceUtrvs
   * Ensure is still in active on portfolio
   */
  public static async findActivePortfoliosByUtrvId(utrvId: ObjectId, project?: {}) {
    const utrvAssurances = await UniversalTrackerValueAssurance.find({
      utrvId,
      status: { $nin: disabledUtrvAssuranceStatuses },
    }, { portfolioId: 1 }).lean();
    return AssurancePortfolio.find({
      _id: { $in: utrvAssurances.filter(u => u.portfolioId).map(u => u.portfolioId ?? '') },
      status: {
        $nin: [
          AssurancePortfolioStatus.Deleted,
          AssurancePortfolioStatus.Created,
        ]
      },
    }, project).lean().exec();
  }


  /**
   * Automatically exclude Disabled assuranceUtrvs
   * Ensure is still in active on portfolio
   */
  public static async findActivePortfoliosAssurancesByUtrvId(utrvId: ObjectId): Promise<UtrvAssuranceWithPortfolio[]> {
    return UniversalTrackerValueAssurance.find<UtrvAssuranceWithPortfolio>({
      utrvId,
      status: { $nin: disabledUtrvAssuranceStatuses },
    }).populate([
      {
        path: 'portfolio',
        select: 'status',
        match: {
          status: { $ne: AssurancePortfolioStatus.Deleted }
        }
      },
      {
        path: 'universalTrackerValue',
      },
    ]).exec();
  }

  public static async findByUtrvIdExtended(_id: ObjectId, optionalMatch = {}) {
    return UniversalTrackerValueAssurance.findOne({
      _id,
      status: { $nin: disabledUtrvAssuranceStatuses },
      ...optionalMatch
    }).populate('universalTrackerValue').exec()
  }

  public static async getPortfolioQuestions(assurancePortfolioId: ObjectId) {

    const aggregations: any[] = [
      {
        $match: {
          portfolioId: assurancePortfolioId,
          status: { $nin: disabledUtrvAssuranceStatuses },
        },
      },
      {
        $project: {
          ...utrvAssuranceProjection,
        }
      },
    ];

    return UniversalTrackerValueAssurance.aggregate(aggregations);
  }

  public static async getAssuranceExpandedExtraByInitiativeId(initiativeId: ObjectId, surveyId?: string | ObjectId, userId?: ObjectId, hasPermissions = true) {
    const assuranceMatch: { [K in keyof AssurancePortfolioPlain | string]?: any } = {
      initiativeId,
      status: { $ne: AssurancePortfolioStatus.Deleted },
    };

    if (surveyId) {
      assuranceMatch.surveyId = new ObjectId(surveyId);
    }

    return AssuranceRepository.getAssuranceExpandedExtra(assuranceMatch, userId, hasPermissions);
  }

  public static async getAssuranceForOrg({ organizationId, portfolioId, domainOrigin }: AssuranceQuery) {
    const assuranceMatch: { [K in keyof AssurancePortfolioPlain | string]?: any } = {
      _id: new ObjectId(portfolioId),
      organizationId,
      status: { $in: [AssurancePortfolioStatus.Pending, AssurancePortfolioStatus.Completed] },
    };

    const assurances = await AssuranceRepository.getAssuranceExpandedExtra(assuranceMatch);
    const assurance = assurances.shift();

    if (!assurance || !assurance.survey) {
      return;
    }

    const surveyData = await SurveyRepository.getAssuranceSurveyData(assurance, domainOrigin);
    return {
      ...assurance,
      surveyData
    }
  }

  static getAssuranceExpanded(_id: ObjectId): Promise<AssurancePortfolioExpanded | null> {
    return AssurancePortfolio.findOne({
      _id,
      status: { $ne: AssurancePortfolioStatus.Deleted }
    })
      .populate('survey')
      .populate('initiative')
      .populate('organization')
      .exec();
  }

  public static async getAssuranceExpandedExtraById(
    assurancePortfolioId: ObjectId | string,
    userId?: ObjectId,
    hasPermissions = true
  ): Promise<AssurancePortfolioExpandedExtra | undefined> {
    const assuranceMatch: { [K in keyof AssurancePortfolioPlain | string]?: any } = {
      _id: new ObjectId(assurancePortfolioId),
      status: { $ne: AssurancePortfolioStatus.Deleted },
    };

    const assurances = await AssuranceRepository.getAssuranceExpandedExtra(assuranceMatch, userId, hasPermissions);
    return assurances.shift();
  }

  private static async getAssuranceExpandedExtra(
    assuranceMatch: { [K in keyof AssurancePortfolioPlain | string]?: any },
    userId?: ObjectId,
    hasPermissions = true
  ): Promise<AssurancePortfolioExpandedExtra[]> {
    if (hasPermissions === false) {
      if (!userId) {
        return Promise.resolve([]);
      }
      // Lookup by permissions
      assuranceMatch['permissions'] = { $elemMatch: { userId } };
    }

    const aggregations: PipelineStage[] = [
      {
        $match: assuranceMatch
      },
      {
        $lookup: {
          from: 'document',
          localField: 'history.documents.documentId',
          foreignField: '_id',
          as: 'historyDocuments'
        }
      },
      {
        $lookup: {
          from: 'organizations',
          localField: 'organizationId',
          foreignField: '_id',
          as: 'organization'
        }
      },
      {
        $lookup: {
          from: 'surveys',
          localField: 'surveyId',
          foreignField: '_id',
          as: 'survey'
        }
      },
      {
        $lookup: {
          from: 'initiatives',
          localField: 'initiativeId',
          foreignField: '_id',
          as: 'initiative'
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'permissions.userId',
          foreignField: '_id',
          as: 'assurers',
        },
      },
      utrvAssuranceLookUp('_id', 'portfolioId'),
      {
        $project: {
          ...assurancePortfolioProjection,
          historyDocuments: 1,
          universalTrackerValueAssurances: 1,
          users: userMinFields,
          assurers: userMinFields,
          organization: { $arrayElemAt: ['$organization', 0] },
          survey: { $arrayElemAt: ['$survey', 0] },
          initiative: { $arrayElemAt: ['$initiative', 0] },
        }
      },
      {
        $project: {
          ...assurancePortfolioProjection,
          historyDocuments: 1,
          universalTrackerValueAssurances: 1,
          users: userMinFields,
          organization: organizationProjectFields,
          survey: assuranceSurvey,
          initiative: {
            _id: 1,
            name: 1,
            tags: 1,
          },
          ...groupAssurer,
        },
      },
    ];

    return AssurancePortfolio.aggregate(aggregations).then(async portfolios => {
      for (const p of portfolios) {
        await addDataPropertyUrl(p, 'historyDocuments')
      }
      return portfolios;
    });
  }

  public static async getUsers(portfolio: Pick<AssurancePortfolioPlain, 'organizationId'>, projection?: {}, lean = true) {
    if (!portfolio.organizationId) {
      return [];
    }
    return User.find({
      organizationId: portfolio.organizationId
    }, projection).lean(lean);
  }

  public static async getUsersByArray(portfolios: Pick<AssurancePortfolioPlain, 'organizationId'>[], projection?: {}) {
    return User.find({
      organizationId: {
        $in: portfolios.filter(p => p.organizationId).map(p => p.organizationId)
      }
    }, projection).lean();
  }


  public static async getPermissions(portfolioId: ObjectId) {
    const aggregations: any[] = [
      {
        $match: {
          _id: portfolioId,
          status: { $ne: AssurancePortfolioStatus.Deleted },
        },
      },
      {
        $lookup: {
          from: 'organizations',
          localField: 'organizationId',
          foreignField: '_id',
          as: 'organization',
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'permissions.userId',
          foreignField: '_id',
          as: 'assurers',
        },
      },
      {
        $project: {
          _id: 1,
          initiativeId: 1,
          organizationId: 1,
          permissions: 1,
          assurers: userMinFields,
          organization: { $arrayElemAt: ['$organization', 0] },
        },
      },
      {
        $project: {
          _id: 1,
          initiativeId: 1,
          organizationId: 1,
          ...groupAssurer,
          organization: organizationProjectFields,
        },
      },
    ];
    return AssurancePortfolio.aggregate(aggregations);
  }

  static async getSurveyAssuranceUtrvOrganizations({
    surveyIds,
    utrvIds,
  }: {
    surveyIds: ObjectId[];
    utrvIds: ObjectId[];
  }): Promise<{ _id: ObjectId; names: string[] }[]> {
    return AssurancePortfolio.aggregate([
      {
        $match: { surveyId: { $in: surveyIds } }
      },
      {
        $lookup: {
          from: 'universal-tracker-value-assurances',
          let: { localField: `$_id` },
          pipeline: [
            {
              $match: {
                utrvId: { $in: utrvIds },
                status: AssuranceStatus.Completed,
                $expr: { $eq: ['$portfolioId', '$$localField'] }
              }
            }
          ],
          as: 'universalTrackerValueAssurances'
        }
      },
      {
        $lookup: {
          from: 'organizations',
          localField: 'organizationId',
          foreignField: '_id',
          as: 'organization'
        }
      },
      {
        $project: {
          orgName: { $arrayElemAt: ['$organization.name', 0] },
          universalTrackerValueAssurances: {
            utrvId: 1,
          }
        }
      },
      {
        $unwind: {
          path: '$universalTrackerValueAssurances',
        }
      },
      {
        $group: {
          _id: '$universalTrackerValueAssurances.utrvId',
          names: { $push: '$orgName' }
        }
      }
    ]).exec()
  }

  public static find(match: any, projection?: {}) {
    return AssurancePortfolio.find(match, projection).exec();
  }

  static getOrganization(user: UserPlain) {
    return Organization.findOne(
      { _id: user.organizationId, partnerTypes: OrganizationPartnerTypes.Assurer },
      organizationProjectFields
    ).exec();
  }

  static async getSurveyAssuranceUtrvs(
    surveyIds: ObjectId[],
    additionalMatch?: { [K in keyof UniversalTrackerValueAssuranceModel]?: any }
  ): Promise<Pick<UniversalTrackerValueAssuranceModel, 'utrvId' | 'partialFields'>[]> {
    const assurancePortfolios = await AssurancePortfolio.find({ surveyId: { $in: surveyIds } }, { _id: 1 })
      .lean<Pick<AssurancePortfolioPlain, '_id'>[]>()
      .exec();
    const portfolioIds = assurancePortfolios.map(({ _id }) => _id);

    return UniversalTrackerValueAssurance.find(
      { portfolioId: { $in: portfolioIds }, ...additionalMatch },
      { utrvId: 1, partialFields: 1 }
    );
  }

  static async getAssuranceDocuments(initiativeId: ObjectId | string): Promise<{ documents: InsightDocument[] }> {
    const assurancePortfolios = await AssuranceRepository.find(
      {
        status: { $ne: AssurancePortfolioStatus.Deleted },
        initiativeId,
      },
      { _id: 1, documents: 1 }
    );

    const documentIds: ObjectId[] = assurancePortfolios.reduce((acc, portfolio) => {
      acc.push(...portfolio.documents.map((d) => d.documentId));
      return acc;
    }, [] as ObjectId[]);

    const documents = await DocumentModel
      .find({ _id: { $in: documentIds } }, insightDocumentProjection)
      .sort({ created: -1 })
      .lean<InsightDocument[]>()
      .exec();

    return addDocumentUrl({ documents });
  }
}
