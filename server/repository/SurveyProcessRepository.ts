/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import UniversalTrackerValue, { CompositeData, UniversalTrackerValuePlain } from '../models/universalTrackerValue';
import UniversalTracker from '../models/universalTracker';
import { SurveyModelPlain } from '../models/survey';
import { ObjectId } from 'bson';
import { SupportedMeasureUnits, Unit } from '../service/units/unitTypes';
import { universalTrackerLookup } from './utrvAggregations';
import { EscalationPolicy } from '../models/escalationPolicy';
import { StakeholderGroup } from '../models/stakeholderGroup';
import moment = require('moment');

type KeysEnum<T> = { [P in keyof Required<T>]: 0 | 1 | {} };
export interface MinUtrData {
  _id: ObjectId;
  code: string;
  unitType?: SupportedMeasureUnits;
  numberScale?: string;
  numberScaleInput?: string;
  unit?: Unit;
  unitInput?: Unit;
}
export const minUtrDataProject: KeysEnum<MinUtrData> = {
  _id: 1,
  code: 1,
  numberScale: 1,
  numberScaleInput: 1,
  unit: 1,
  unitInput: 1,
  unitType: 1,
}

// Min fields required for SurveyProcess clone data
export type SurveyProcessCloneData = Pick<UniversalTrackerValuePlain<ObjectId, MinUtrData>,
  '_id' |
  'initiativeId' |
  'status' |
  'universalTrackerId' |
  'stakeholders' |
  'deletedDate' |
  'unit' |
  'numberScale' |
  'universalTracker' |
  'value' |
  'valueData' |
  'valueType' |
  'evidence' |
  'note'>

const utrvCloneProject: KeysEnum<SurveyProcessCloneData>  = {
  _id: 1,
  initiativeId: 1,
  universalTrackerId: 1,
  deletedDate: 1,
  stakeholders: 1,
  unit: 1,
  status: 1,
  numberScale: 1,
  universalTracker: minUtrDataProject,
  // Value Info
  value: 1,
  valueType: 1,
  valueData: 1,
  note: 1,
  evidence: 1,
}

// Min fields required for SurveyProcess
export interface SurveyProcessUtrvPlain<T = ObjectId> extends Pick<UniversalTrackerValuePlain,
  'value' | 'valueData' | 'valueType' | 'sourceType' | 'status' | 'sourceCode' | 'sourceItems' | 'valueAggregation'> {
  _id: T;
  initiativeId: T;
  universalTrackerId: T;
  verificationRequired: boolean;
  evidenceRequired: boolean;
  noteRequired?: boolean;
  stakeholders: StakeholderGroup<T>;
  escalationPolicy?: EscalationPolicy
  compositeData?: CompositeData<T>;
  deletedDate?: Date;
  created?: Date;
  unit?: string;
  numberScale?: string;
  universalTracker?: any;
}

const utrvProject: KeysEnum<SurveyProcessUtrvPlain>  = {
  _id: 1,
  created: 1,
  initiativeId: 1,
  universalTrackerId: 1,
  verificationRequired: 1,
  evidenceRequired: 1,
  noteRequired: 1,
  compositeData: 1,
  deletedDate: 1,
  sourceType: 1,
  status: 1,
  sourceCode: 1,
  stakeholders: 1,
  unit: 1,
  numberScale: 1,
  escalationPolicy: 1,
  universalTracker: minUtrDataProject,
  value: 1,
  valueData: 1,
  valueType: 1,
  sourceItems: 1,
  valueAggregation: 1,
}

export class SurveyProcessRepository {

  /**
   * Utrv's that will be used to update data points in different blueprint
   */
  public static async getReferencedData(survey: SurveyModelPlain, utrCodes: string[]) {
    const { initiativeId, effectiveDate, sourceName } = survey;

    const initiativeIds = [initiativeId];
    const refIds = survey.references ? survey.references.map(r => r.surveyId) : [];

    const utrs = refIds.length > 0 ? [] : await UniversalTracker.find({ code: { $in: utrCodes } }, { _id: 1 })
        .lean()
        .exec();

    const $match = {
      initiativeId: { $in: initiativeIds },
      effectiveDate: {
        $gte: moment(effectiveDate).startOf('month').startOf('day').toDate(),
        $lte: moment(effectiveDate).endOf('month').endOf('day').toDate(),
      },
      'compositeData.blueprint': { $ne: sourceName, $exists: true },
      deletedDate: { $exists: false },
      ...(refIds.length > 0
        ? { 'compositeData.surveyId': { $in: refIds } }
        : { universalTrackerId: { $in: utrs.map(u => u._id) } }),
    };

    return UniversalTrackerValue.aggregate([
      { $match: $match },
      {
        $lookup: {
          from: 'universal-trackers',
          localField: 'universalTrackerId',
          foreignField: '_id',
          as: 'universalTracker'
        }
      },
    ]).option({ hint: 'get_survey_process_referenced_data' });
  }

  public static async loadSurveyUtrvs(survey: SurveyModelPlain): Promise<SurveyProcessUtrvPlain[]> {

    const aggregations = [
      {
        $match: {
          'compositeData.surveyId': survey._id
        }
      },
      universalTrackerLookup,
      {
        $project: utrvProject
      },
    ];

    return UniversalTrackerValue.aggregate(aggregations);
  }

  /**
   * Used by SurveyClone to get data of existing surveys for cloning purpose
   */
  public static async loadSurveyUtrvCloneData(survey: SurveyModelPlain): Promise<SurveyProcessCloneData[]> {
    const aggregations = [
      {
        $match: { _id: { $in: survey.visibleUtrvs } },
      },
      universalTrackerLookup,
      { $project: utrvCloneProject },
    ];

    const surveyUtrvsData = await UniversalTrackerValue.aggregate(aggregations);
    return surveyUtrvsData.map(utrv => {
      utrv.universalTracker = utrv.universalTracker[0]

      // Clear value chain as, we dont include it in cloned data
      if (utrv.valueData?.input?.valueChainPercentage) {
        utrv.valueData.input.valueChainPercentage = undefined;
      }

      return utrv
    });
  }
}
