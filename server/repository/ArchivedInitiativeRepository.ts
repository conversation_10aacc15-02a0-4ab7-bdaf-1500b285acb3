import { ObjectId } from 'bson';
import { InitiativePlain, minimumFields } from '../models/initiative';
import { PipelineStage } from 'mongoose';
import ArchivedInitiative from '../models/archivedInitiative';
import { InitiativeRepository } from './InitiativeRepository';

export class ArchivedInitiativeRepository {
  public static async getInitiativeTreeById(initiativeId: ObjectId) {
    const children = await InitiativeRepository.getAllChildrenById(initiativeId, undefined, { _id: 1 });
    const activeInitiativeIds = children.map((i) => i._id);
    return this.getInitiativeTreeByActiveParentIds(activeInitiativeIds);
  }

  public static async getInitiativeTreeByActiveParentIds(activeInitiativeIds: ObjectId[]) {
    const archivedParents = await this.getArchivedInitiativesByActiveParents(
      activeInitiativeIds,
      { _id: 1 }
    );
    return this.getAllChildrenById(archivedParents.map((i) => i._id));
  }

  public static async getAllChildrenById(
    initiativeId: (string | ObjectId) | (string | ObjectId)[],
    restrictSearch?: { tags: unknown },
    projection: { [key: string]: number } = minimumFields
  ): Promise<InitiativePlain[]> {
    const $graphLookup: PipelineStage.GraphLookup['$graphLookup'] = {
      from: 'archived-initiatives',
      startWith: '$_id',
      connectFromField: '_id',
      connectToField: 'parentId',
      as: 'children',
    };

    if (restrictSearch) {
      $graphLookup.restrictSearchWithMatch = restrictSearch;
    }

    const $match = Array.isArray(initiativeId)
      ? { _id: { $in: initiativeId.map((id) => new ObjectId(id)) } }
      : { _id: new ObjectId(initiativeId) };

    const results: InitiativePlain[] = await ArchivedInitiative.aggregate([
      { $match },
      { $graphLookup: $graphLookup },
      { $project: { ...projection, children: { ...projection } } },
    ]).exec();

    return results.reduce((a, initiative) => {
      const children = initiative.children ?? [];
      delete initiative.children;
      children.unshift(initiative);
      return [...a, ...children];
    }, [] as InitiativePlain[]);
  }

  public static async getArchivedInitiativesByActiveParents(
    parentIds: (string | ObjectId)[],
    projection: { [key: string]: number } = minimumFields
  ) {
    return ArchivedInitiative.find({ parentId: { $in: parentIds } }, projection)
      .lean()
      .exec();
  }
}
