import { ObjectId } from 'bson';
import BadRequestError from '../error/BadRequestError';
import { ExtendedTemplateHistory, SurveyTemplateHistory } from '../models/surveyTemplateHistory';

export class SurveyTemplateHistoryRepository {
  public static async findHistoryWithInitiative(id: string | ObjectId): Promise<ExtendedTemplateHistory> {
    if (!ObjectId.isValid(id)) {
      throw new Error('History could not be found');
    }
    const history = await SurveyTemplateHistory.findById(id).populate<ExtendedTemplateHistory>('results.initiative', 'name').lean().exec();
    if (!history) {
      throw new BadRequestError('History was not found. Please go back to the previous page and try again.');
    }
    return history;
  }
}
