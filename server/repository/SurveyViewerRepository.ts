/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { SurveyModelPlain } from '../models/survey';
import UniversalTrackerValue, {
  CompositeData
} from '../models/universalTrackerValue';
import { initiativeLookup, universalTrackerLookup } from './utrvAggregations';
import { ObjectId } from 'bson';
import {
  UtrValueType,
  ValueValidation,
} from "../models/public/universalTrackerType";
import { ValueData } from "../models/public/universalTrackerValueType";

const utrvViewerData = {
  value: 1,
  status: 1,
  valueData: 1,
  compositeData: 1,
  universalTracker: 1,
  initiative: 1,
  uniqueCode: 1,
  deletedDate: 1,
  lastUpdated: 1,
};

export interface SurveyViewData {
  _id: ObjectId;
  value: any;
  status: string;
  valueData: ValueData;
  compositeData: CompositeData;
  universalTracker: {
    code: string;
    valueLabel: string;
    valueType: UtrValueType;
    valueValidation: ValueValidation;
  };
  lastUpdated: Date;
  initiative: {
    code: 1,
    name: 1,
    tags: string[]
  };
  universalTrackerId: ObjectId;
  uniqueCode: string;
  deletedDate: Date;
}

export class SurveyViewerRepository {

  public static async loadSurveyData(survey: SurveyModelPlain) {
    return UniversalTrackerValue.aggregate([
      {
        $match: {
          $or: [
            {
              'compositeData.surveyId': survey._id,
            },
            {
              'compositeData.secondary.surveyId': survey._id,
            },
            {
              _id: {
                $in: [
                  ...survey.compositeUtrvs,
                  ...survey.fragmentUtrvs,
                  ...survey.disabledUtrvs,
                  ...survey.subFragmentUtrvs
                ]
              }
            },
          ],
        }
      },
      universalTrackerLookup,
      initiativeLookup,
      {
        $project: {
          ...utrvViewerData,
          universalTracker: { $arrayElemAt: ['$universalTracker', 0] },
          initiative: { $arrayElemAt: ['$initiatives', 0] },
        }
      },
      {
        $project: {
          ...utrvViewerData,
          universalTracker: {
            code: 1,
            valueLabel: 1,
            valueType: 1,
            valueValidation: 1,
          },
          initiative: {
            name: 1,
            code: 1,
            tags: 1,
          },
          uniqueCode: {$concat: ['$initiative.code', '#', '$universalTracker.code']}
        }
      }
    ]);
  }
}

