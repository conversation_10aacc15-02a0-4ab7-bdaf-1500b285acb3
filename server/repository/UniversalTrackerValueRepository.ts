/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { ObjectId } from 'bson';
import UniversalTrackerValue, {
  UniversalTrackerValueModel,
  UniversalTrackerValuePlain,
} from '../models/universalTrackerValue';
import User, { userMinFields } from '../models/user';
import { universalTrackerLookup } from './utrvAggregations';
import { universalTrackerValuePlainFields } from './projections';
import { DownloadScopeData } from '../types/download';
import { FilterQuery, QueryOptions } from 'mongoose';
import { Blueprints } from '../survey/blueprints';
import { getBluePrintContribution } from '../service/survey/BlueprintContribution';
import { SurveyPlainExtended } from '../models/survey';
import { applyVisibilityFilter, applyVisibilityProject } from './visibilityFilter';
import { KeysEnum } from '../models/public/projectionUtils';
import { scopeFiltersUtrProjection } from '../service/survey/scope/filterScope';
import { scopeUtrvProjection, ScopeUtrv } from '../util/scope-utrv';
import { SurveyUserRoles } from '../types/roles';
import { createSurveyUsersRoles } from '../util/survey';

export class UniversalTrackerValueRepository {

  public static async getUtrvStakeholderUsersByUtrv(utrv: Pick<UniversalTrackerValueModel, 'stakeholders'>) {
    const { stakeholder = [], verifier = [] } = utrv.stakeholders ?? {};
    return UniversalTrackerValueRepository.getUtrvStakeholderUsers(stakeholder, verifier);
  }

  public static async getUtrvStakeholderUsers(stakeholderIds: ObjectId[], verifierIds: ObjectId[], adminIds?: ObjectId[]) {

    const userIds = [...stakeholderIds, ...verifierIds, ...(adminIds ?? [])];
    if (userIds.length === 0) {
      return [];
    }

    const users = await User.find({ _id: { $in: userIds } }, { ...userMinFields, isStaff: 1, jobTitle: 1 })
      .lean()
      .exec();

    const roleMap = {
      [SurveyUserRoles.Stakeholder]: stakeholderIds.map(String),
      [SurveyUserRoles.Verifier]: verifierIds.map(String),
      ...(adminIds ? { [SurveyUserRoles.Admin]: adminIds.map(String) } : {}),
    }

    return users.map((user) => ({
      ...user,
      roles: createSurveyUsersRoles(user, roleMap),
    }));
  }

  public static async getUtrvsByIdsAndUtrType(survey: SurveyPlainExtended, dlScope: DownloadScopeData, projection: { [key: string]: any } = universalTrackerValuePlainFields) {

    const { statuses, visibilityStatus } = dlScope;

    const $match: Record<string, any> = { _id: { $in: survey.visibleUtrvs } };
    if (statuses && statuses.length > 0) {
      $match.status = { $in: statuses }
    }

    const pipeline = [
      { $match: applyVisibilityFilter($match, visibilityStatus) },
      universalTrackerLookup,
      {
        $project: {
          ...projection,
          universalTracker: { $arrayElemAt: ['$universalTracker', 0] },
        },
      },
      await this.matchTypes(dlScope, survey),
      { $project: applyVisibilityProject(projection, visibilityStatus) },
    ];

    return UniversalTrackerValue.aggregate(pipeline)
  }

  private static async matchTypes(dlScope: DownloadScopeData, survey: SurveyPlainExtended) {
    const { values: types } = dlScope;
    if(types && types.length === 1 && types.includes('sdg')) {

      const blueprint = getBluePrintContribution()
      const contribution = await blueprint.getContributions(survey.sourceName as Blueprints);
      const codes = Object.keys(contribution)
      return {
        $match: {
          'universalTracker.code': { $in: codes }
        }
      }
    }
    return {
      $match: {
        $or: [
          { 'universalTracker.type': { $in: types } },
          ...types?.map(type => ({
            [`universalTracker.alternatives.${type}`]: { $exists: true },
          })) ?? []
        ]
      }
    };
  }

  public static async findById(id: string | ObjectId, projection?: any) {
    return UniversalTrackerValue.findById(id, projection).exec();
  }

  public static async find(
    filter: FilterQuery<UniversalTrackerValueModel>,
    projection?: any | null,
    options?: QueryOptions | null,
  ) {
    return UniversalTrackerValue.find(filter, projection, options).exec();
  }

  public static async mustFindById(id: string | ObjectId, projection?: any) {

    if (!ObjectId.isValid(id)) {
      throw new Error(`UniversalTrackerValue id is not valid`);
    }

    const model = await UniversalTrackerValueRepository.findById(id, projection);
    if (!model) {
      throw new Error(`UniversalTrackerValue was not found with id "${id}"`);
    }

    return model;
  }


  public static async findWithDeleted(match = {}, projection?: any | null): Promise<UniversalTrackerValuePlain[]> {
    return UniversalTrackerValue.find(match, projection).lean().exec() as Promise<UniversalTrackerValuePlain[]>;
  }

  public static async getScopeUtrvs<AdditionalFields extends Partial<Omit<UniversalTrackerValuePlain, 'universalTracker'>> = {}>({
    utrvIds,
    additionalProjection,
  }: {
    utrvIds: ObjectId[];
    additionalProjection?: KeysEnum<AdditionalFields>;
  }) {
    return UniversalTrackerValue
      .find({ _id: { $in: utrvIds } }, { ...scopeUtrvProjection, ...additionalProjection })
      .populate('universalTracker', scopeFiltersUtrProjection)
      .lean<(ScopeUtrv & AdditionalFields)[]>()
      .exec();
  }
}
