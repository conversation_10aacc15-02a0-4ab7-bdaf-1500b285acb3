import { ActionList, UtrvType } from '../service/utr/constants';
import UniversalTrackerValue, { UniversalTrackerValuePlain } from '../models/universalTrackerValue';
import { ObjectId } from 'bson';
export class TargetRepository {
  private utrvType = UtrvType.Target;
  constructor(private model: typeof UniversalTrackerValue) {}

  public async getClosestTargetValue({ utrId, initiativeId }: { utrId: ObjectId; initiativeId: ObjectId }) {
    const match = {
      universalTrackerId: utrId,
      initiativeId,
      type: this.utrvType,
      deletedDate: { $exists: false },
      status: ActionList.Verified,
    };
    const commonPipelines = [{ $limit: 1 }, { $project: { value: 1 } }];

    const [result] = await this.model.aggregate<{ closestTarget: Pick<UniversalTrackerValuePlain, 'value'> }>([
      {
        $match: match,
      },
      {
        $facet: {
          futureTargets: [
            { $match: { effectiveDate: { $gte: new Date() } } },
            { $sort: { effectiveDate: 1 } },
            ...commonPipelines,
          ],
          pastTargets: [
            { $match: { effectiveDate: { $lt: new Date() } } },
            { $sort: { effectiveDate: -1 } },
            ...commonPipelines,
          ],
        },
      },
      {
        $project: {
          closestTarget: {
            $cond: [
              { $gt: [{ $size: '$futureTargets' }, 0] },
              { $arrayElemAt: ['$futureTargets', 0] },
              { $arrayElemAt: ['$pastTargets', 0] },
            ],
          },
        },
      },
    ]);

    return result?.closestTarget?.value;
  }
}

let instance: TargetRepository;
export const getTargetRepository = () => {
  if (!instance) {
    instance = new TargetRepository(UniversalTrackerValue);
  }

  return instance;
};
