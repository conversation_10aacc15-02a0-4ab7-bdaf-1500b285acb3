/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import Onboarding, { OnboardingModel, OnboardingStatus, OnboardingUser } from '../models/onboarding';
import { ObjectId } from 'bson';
import { FilterQuery, ProjectionType, QueryOptions } from 'mongoose';
import { UsersSearchResult } from './UserRepository';

export interface OnboardingSearchResult extends UsersSearchResult {
  status: OnboardingStatus.Pending | OnboardingStatus.NotStarted;
  user: Pick<OnboardingUser, 'permissions'>;
}

export const statusCondition = {
  $in: [OnboardingStatus.Pending, OnboardingStatus.NotStarted],
};

// Onboarding now is narrowed down to initiative onboarding and organization onboarding
// So methods in this repository should only be used when we don't care about the onboarding type
export class OnboardingRepository {
  public async find(
    match: FilterQuery<OnboardingModel>,
    projection?: ProjectionType<OnboardingModel>,
    options?: QueryOptions
  ): Promise<OnboardingModel[]> {
    return Onboarding.find(match, projection, options).exec();
  }

  public async mustFindById(id: string | ObjectId): Promise<OnboardingModel> {
    if (!ObjectId.isValid(id)) {
      throw new Error('onboarding id is not valid');
    }

    const model = await Onboarding.findById(id).exec();
    if (!model) {
      throw new Error('Invalid model ID');
    }
    return model;
  }
}

export const createOnboardingRepository = () => {
  return new OnboardingRepository();
};

let instance: OnboardingRepository;
export const getOnboardingRepository = () => {
  if (!instance) {
    instance = createOnboardingRepository();
  }
  return instance;
};
