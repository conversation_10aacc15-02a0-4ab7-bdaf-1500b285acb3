import { Bookmark, BookmarkTarget, BookmarkModel } from '../models/bookmark';
import { ObjectId } from 'bson';

export class BookmarkRepository {
  public static findUtrvBookmarksBySurvey(userId: ObjectId, surveyId: ObjectId): Promise<BookmarkModel[]> {
    return Bookmark.find({
      userId,
      targetType: BookmarkTarget.UniversalTrackerValue,
      surveyId,
    }).exec();
  }

  public static createUtrvBookmark(userId: ObjectId, utrvId: ObjectId, surveyId: ObjectId) {
    return Bookmark.create({ userId, surveyId, targetId: utrvId, targetType: BookmarkTarget.UniversalTrackerValue });
  }

  public static deleteUtrvBookmark(userId: ObjectId, utrvId: ObjectId, surveyId: ObjectId) {
    return Bookmark.deleteOne({ userId, surveyId, targetId: utrvId, targetType: BookmarkTarget.UniversalTrackerValue });
  }
}
