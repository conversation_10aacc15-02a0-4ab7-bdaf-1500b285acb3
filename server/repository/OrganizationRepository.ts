/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import Organization, { OrganizationModel } from '../models/organization';
import { ObjectId } from 'bson';
import { UserMin, userMinFields } from '../models/user';

interface OrganizationSearchResult {
  _id: ObjectId;
  name: string;
  profile?: string;
}

interface OrganizationWithUsers {
  _id: ObjectId;
  name: string;
  partnerTypes: string[];
  profile?: string;
  users: UserMin[];
}

export class OrganizationRepository {

  public static async findOne(match = {}) {
    return Organization.findOne(match).exec();
  }

  public static async mustFindById(id: string | ObjectId) {
    return Organization.findById(id).orFail().exec();
  }

  public static async search(searchStr: string): Promise<OrganizationSearchResult[]> {
    if (!searchStr) {
      return [];
    }
    const searchRegex = new RegExp('.*' + decodeURIComponent(searchStr) + '.*', 'i');

    return Organization.find({
      organizationType: 'public',
      name: { $regex: searchRegex },
      }, { name: 1, profile: 1 })
      .sort({ surname: 1, firstName: 1 })
      .limit(20)
      .lean()
      .exec();
  }

  public static async getOrganizationByTypes(types: string[], withUser = false): Promise<OrganizationWithUsers[]> {

    if (!Array.isArray(types)) {
      return [];
    }

    const $match: { [K in keyof OrganizationModel]?: any } = {
      organizationType: 'public',
      partnerTypes: { $in: types }
    };

    const minOrgFields = { _id: 1, name: 1, partnerTypes: 1, profile: 1 };

    const aggregations: any[] = [
      { $match: $match },
      { $project: minOrgFields }
    ];

    if (withUser) {
      aggregations.push({
          $lookup: {
            from: 'users',
            let: { organizationId: '$_id' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ["$organizationId", "$$organizationId"] },
                      { $eq: ["$active", true] },
                    ]
                  }
                }
              },
            ],
            as: 'users'
          }
        },
        { $project: { ...minOrgFields, users: { ...userMinFields } } }
      );
    }

    return Organization.aggregate(aggregations);
  }
}
