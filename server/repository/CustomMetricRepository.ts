import { ObjectId } from 'bson';
import UniversalTracker, { UniversalTrackerPlain, UtrType } from '../models/universalTracker';
import { universalTrackerFields } from './projections';
import UniversalTrackerValue, { UniversalTrackerValuePlain } from '../models/universalTrackerValue';
import { ActionList } from '../service/utr/constants';
import { InitiativeRepository } from './InitiativeRepository';
import { AccessType } from '../models/metricGroup';

export type InitiativeCustomMetric = UniversalTrackerPlain & {
  universalTrackerValues: (Pick<UniversalTrackerValuePlain, 'status'>)[];
  accessType: AccessType.Custom | AccessType.Inherited
};

export class CustomMetricRepository {
  public static async getInitiativeCustomMetrics(initiativeId: ObjectId | undefined): Promise<InitiativeCustomMetric[]> {
    if (!initiativeId) {
      return [];
    }

    const currentLevelAndAbove = await InitiativeRepository.getCurrentAndParentInitiativeIds(initiativeId);

    const aggregate = [
      {
        $match: {
          ownerId: { $in: currentLevelAndAbove },
          type: UtrType.CustomKpi,
        },
      },
      {
        $lookup: {
          from: 'universal-tracker-values',
          localField: '_id',
          foreignField: 'universalTrackerId',
          as: 'universalTrackerValues',
        },
      },

      {
        $project: {
          ...universalTrackerFields,
          universalTrackerValues: {
            status: 1,
          },
          ownerId: 1,
          // classify inherited metrics and custom metrics
          accessType: {
            $cond: {
              if: { $eq: ['$ownerId', initiativeId] },
              then: AccessType.Custom,
              else: AccessType.Inherited
            }
          }
        },
      },
    ];

    return UniversalTracker.aggregate(aggregate).exec();
  }

  public static async dumpInitiativeCustomMetrics(
    initiativeId: ObjectId,
    limit = 200  // Arbitrary limit for safety
  ) {
    return UniversalTracker.find({
      ownerId: initiativeId,
      type: UtrType.CustomKpi,
    })
      .limit(limit)
      .lean().exec();
  }

  public static async importInitiativeCustomMetrics(
    universalTrackers: (Omit<UniversalTrackerPlain, '_id'> & { _id?: undefined })[]
  ) {
    universalTrackers.forEach((universalTracker) => {
      delete universalTracker._id; // remove the _id property
    });
    return UniversalTracker.insertMany(universalTrackers);
  }

  public static async findOne({ _id, ownerId }: Pick<UniversalTrackerPlain, '_id' | 'ownerId'>) {
    return UniversalTracker.findOne({
      _id,
      type: UtrType.CustomKpi,
      ownerId,
    })
      .orFail()
      .exec();
  }

  public static async checkIsAnswered(id: ObjectId) {
    const count = await UniversalTrackerValue.find({
      universalTrackerId: id,
      status: { $ne: ActionList.Created },
    }).countDocuments();
    return count > 0;
  }
}
