/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { ObjectId } from "bson";
import documentModel, {
  DocumentOwnerType,
  DocumentPlain
} from "../models/document"
import { AssuranceRepository } from "./AssuranceRepository"
import config from '../config';
import { AssurancePortfolioStatus } from '../service/assurance/model/AssurancePortfolio';

const bundleCacheDate = config.cache.bundleZipDate;


export class DocumentRepository {

  public static async findOwner(ownerId: ObjectId, ownerType: DocumentOwnerType, ownerSubType?: string): Promise<DocumentPlain[]> {
    const conditions: { [key: string]: unknown } = {
      ownerId,
      ownerType,
      created: { $gte: bundleCacheDate },
    };

    if (ownerSubType) {
      conditions.ownerSubType = ownerSubType;
    }

    return documentModel.find(conditions).sort({ created: 1 }).lean().exec();
  }

  public static async findInitiativeDocuments(initiativeId: ObjectId) {

    const portfolios = await AssuranceRepository.find({
      status: { $ne: AssurancePortfolioStatus.Deleted },
      initiativeId
    }, { _id: 1, documents: 1 });

    const ownerIds: ObjectId[] = [];

    portfolios.forEach((d) => {
      d.documents.forEach((doc) => {
        ownerIds.push(doc.documentId);
      })
    });

    return documentModel.aggregate([
      {
        $match: {
          $or: [
            { _id: { $in: ownerIds } },
            { ownerId: initiativeId },
          ]
        }
      },
      { $sort: { created: -1 } },
    ])
  }
}
