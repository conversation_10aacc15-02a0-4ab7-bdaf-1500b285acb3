/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { bLab2019Blueprint } from '../survey/bLab2019';
import { CompositeUtrConfigRepository, getCompositeUtrConfigRepository } from './CompositeUtrConfigRepository';
import { CompositeUtrConfigInterface } from '../survey/compositeUtrConfigs';
import { UTrCustomGroup, UTrGroupConfigInterface } from '../survey/utrGroupConfigs';
import { UnitConfig } from '../service/units/unitTypes';
import { gri2019 } from '../survey/gri2019';
import { gri2020 } from '../survey/gri/gri2020';
import { blueprint2022 } from '../survey/blueprints/blueprint2022';
import { baseline2019 } from '../survey/configs/baseline2019/baseline2019';
import { adapter2020 } from '../survey/configs/adapter2020/adapter2020';
import { adapter2022 } from '../survey/configs/adapter2022/adapter2022';
import { UtrConfig } from '../rules/UtrConfig';
import { Blueprints } from '../survey/blueprints';
import { deepClone } from '../util/object';
import { materiality2024 } from '../survey/blueprints/materiality2024';

export interface BlueprintMap {
  [key: string]: Blueprint;
}

export const allSurveys: BlueprintMap = {
  bLab2019Blueprint,
  baseline2019,
  gri2019,
  gri2020,
  blueprint2022,
  adapter2020,
  adapter2022,
  materiality2024,
};

export interface SurveyForm {
  utrGroupConfig?: UTrGroupConfigInterface;
  compositeConfig?: string;
  config?: CompositeUtrConfigInterface | UTrGroupConfigInterface;
}

export interface Blueprint {
  name: string;
  date?: Date;
  references: string[];
  code: Blueprints;
  logo?: {
    src: string;
    alt: string;
  };
  customGroups?: UTrCustomGroup[];
  unitConfig: UnitConfig;
  forms: SurveyForm[];
  additionalConfigs: SurveyForm[];
}

export interface BlueprintRepositoryInterface {
  listAll(): Object | BlueprintMap;

  getBlueprint(blueprintCode: string): Promise<Blueprint | undefined>;

  getCompositeConfig(surveyCode: string, compositeConfigCode: string): any;

  getExpandedBlueprintByCode(surveyCode: string): Promise<Blueprint | undefined>;

  mustFindExpandedByCode(surveyCode: string): Promise<Blueprint>;

  getSurveyUtrCodes(surveyCodes: string[]): Promise<string[]>;

  getExpandedBlueprint(blueprint: Blueprint): Promise<Blueprint>;
}

export class BlueprintRepository implements BlueprintRepositoryInterface {
  private readonly blueprints: BlueprintMap;
  private compositeUtrRepo: CompositeUtrConfigRepository;

  constructor(blueprintMap: BlueprintMap, compositeUtrRepo: CompositeUtrConfigRepository) {
    this.blueprints = blueprintMap;
    this.compositeUtrRepo = compositeUtrRepo;
  }

  async listAll() {
    return this.blueprints;
  }

  async getBlueprint(blueprintCode: string): Promise<Blueprint | undefined> {
    return this.blueprints[blueprintCode];
  }

  async getExpandedBlueprintByCode(blueprintCode: string): Promise<Blueprint | undefined> {
    const blueprint = await this.getBlueprint(blueprintCode);
    if (!blueprint) {
      return;
    }
    return this.getExpandedBlueprint(blueprint);
  }

  public async mustFindExpandedByCode(blueprintCode: string) {
    const expandedBlueprint = await this.getExpandedBlueprintByCode(blueprintCode);
    if (!expandedBlueprint) {
      throw new Error(`Failed to find expanded blueprint by code: '${blueprintCode}`);
    }
    return expandedBlueprint;
  }

  /** Safe to modify **/
  public async findExpandedCopy(blueprintCode: string) {
    return this.mustFindExpandedByCode(blueprintCode).then((blueprint) => deepClone(blueprint));
  }

  public async getExpandedBlueprint(blueprint: Blueprint) {
    const configs = blueprint.forms.concat(blueprint.additionalConfigs || []);
    for (const form of configs) {
      if (form.compositeConfig) {
        form.config = await this.compositeUtrRepo.getSurveyByCode(form.compositeConfig);
      } else if (form.utrGroupConfig) {
        form.config = form.utrGroupConfig;
      }
    }

    return blueprint;
  }

  public async getSurveyUtrCodes(blueprintCodes: string[]): Promise<string[]> {
    const utrCodes: Set<string> = new Set();

    for (const blueprintCode of blueprintCodes) {
      const blueprint = await this.getBlueprint(blueprintCode);
      if (!blueprint) {
        continue;
      }
      const configs = blueprint.forms.concat(blueprint.additionalConfigs || []);
      for (const form of configs) {
        if (form.compositeConfig) {
          const compConfig = await this.compositeUtrRepo.getSurveyByCode(form.compositeConfig);
          utrCodes.add(compConfig.compositeUtrCode);
          UtrConfig.getFragmentUtrCodes(compConfig).forEach((code) => utrCodes.add(code));
        } else if (form.utrGroupConfig) {
          const compConfig = form.utrGroupConfig;
          compConfig.utrCodes.forEach((code) => utrCodes.add(code));
        }
      }
    }

    return Array.from(utrCodes);
  }

  async getCompositeConfig(blueprintCode: string, compositeConfigCode: string) {
    const blueprint = await this.getBlueprint(blueprintCode);
    if (!blueprint) {
      return;
    }

    return blueprint.forms.find((form) => form.compositeConfig === compositeConfigCode);
  }
}

export const getBlueprintRepository = () => {
  return new BlueprintRepository(allSurveys, getCompositeUtrConfigRepository());
};
