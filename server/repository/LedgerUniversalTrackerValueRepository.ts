/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { ObjectId } from 'bson';
import { FilterQuery, ProjectionType, QueryOptions } from 'mongoose';
import LedgerUniversalTrackerValue, { LedgerUniversalTrackerValueModel } from '../models/ledgerUniversalTrackerValue';

export class LedgerUniversalTrackerValueRepository {

  public static async findOne(
    filter: FilterQuery<LedgerUniversalTrackerValueModel>,
    projection?: ProjectionType<LedgerUniversalTrackerValueModel> | null,
    options?: QueryOptions | null,
  ) {
    return LedgerUniversalTrackerValue.findOne(filter, projection, options).exec();
  }

  public static async mustFindByUtrvId(utrvId: string | ObjectId, projection?: any) {

    if (!ObjectId.isValid(utrvId)) {
      throw new Error(`LedgerUniversalTrackerValue utrvId is not valid`);
    }

    const model = await LedgerUniversalTrackerValueRepository.findOne({ utrvId: utrvId });
    if (!model) {
      throw new Error(`LedgerUniversalTrackerValue was not found with utrvId "${utrvId}"`);
    }

    return model;
  }
}
