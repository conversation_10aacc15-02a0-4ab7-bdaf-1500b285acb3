/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import Initiative, {
  getMateriality, getMaterialityMap,
  hasMaterialityMap,
  InitiativeModel,
  InitiativeModelChildrenGroups,
  InitiativePlain,
  InitiativePlainWithParents,
  InitiativeTags,
  InitiativeTypes,
  isOrganization,
  minimumFields,
  InitiativePlainWithRoot,
  InitiativeWithFinancialEndDate,
  InitiativeWithCustomer,
} from '../models/initiative';
import { ObjectId } from 'bson';
import type { UserModel, UserPlain } from '../models/user';
import { valueChainCategories, ValueChainCategory } from '../util/valueChain';
import UniversalTrackerValue, { UniversalTrackerValuePlain } from '../models/universalTrackerValue';
import Survey from '../models/survey';
import { getIndustryText, getSectorText } from '../service/reporting/FrameworkMapping';
import { UniversalTrackerBlueprintMin, UniversalTrackerPlain } from '../models/universalTracker';
import { recursiveParentLookup } from './utrvAggregations';
import MetricGroup, { AccessType, MetricGroupPlain, MetricGroupType } from '../models/metricGroup';
import { ActionList, UtrvType } from '../service/utr/constants';
import ICBMaterialityData from '../models/icbMaterialityData';
import {
  AggregationStrategy,
  getAggregateSurveyIds,
  SurveyRecursiveResult,
} from '../service/aggregate/initiativeSurveys';
import { UniversalTrackerRepository } from './UniversalTrackerRepository';
import { surveyActionMinimalUtrvProjection, utrForBlueprintProject } from './projections';
import { FilterQuery, PipelineStage, ProjectionType } from 'mongoose';
import { KeysEnum, PartialKeysEnum } from "../models/public/projectionUtils";
import { wwgLogger } from '../service/wwgLogger';
import { InitiativeErrorMessages } from '../error/ErrorMessages';
import PermissionDeniedError from '../error/PermissionDeniedError';
import { UserRolePermissions } from '../models/commonProperties';
import { UserRoles } from '../service/user/userPermissions';
import { applyVisibilityFilter, applyVisibilityProject } from './visibilityFilter';
import { VisibilityStatus } from '../service/survey/scope/visibilityStatus';
import { getLastNumberOfMonths, getFinancialMonths } from '../util/date';
import { UserRepository } from './UserRepository';
import { isPopularEmailDomain } from '../static/popular-email-domains';
import { BankingCodeService } from "../service/banking/BankingCodeService";
import { Subscription } from '../models/customer';
import { AppConfig } from '../service/app/AppConfig';
import { IndustryLevels } from '../types/initiative';

export enum FrameworkTypes {
  GICS = 'gics',
  ICB = 'icb',
  ICB2019 = 'icb2019',
}

export interface SurveyDates {
  effectiveDate: Date;
  initiativeId: ObjectId;
}

const initiativeSuggestionProjection: KeysEnum<Omit<InitiativeSuggestion, 'surveys' | 'countries'>> = {
  _id: 1,
  name: 1,
  profile: 1,
  code: 1,
  permissionGroup: 1,
  appConfigCode: 1,
};

const metricGroupSurveyProjection: KeysEnum<MetricGroupPlain['survey']> = {
  _id: 1,
  effectiveDate: 1,
  assessmentType: 1,
  type: 1,
  completedDate: 1,
  initiativeId: 1,
};

export interface InitiativeSuggestion {
  _id: ObjectId;
  name: string;
  code: string;
  profile?: string;
  surveys?: SurveyDates[];
  countries?: InitiativeSuggestion[];
  permissionGroup?: string;
  appConfigCode?: string;
}

type InitiativePlainProject = {
  [k in keyof InitiativePlain]?: 1;
};
type ConditionCheck = (initiative: InitiativePlain) => boolean;

type ParentLookupInfo = { root: InitiativePlain, parentId: string, ids: Set<string> };

export type RootInitiativeData = Pick<InitiativePlain,
  '_id' | 'name' | 'tags' | 'permissionGroup'
  | 'parentId' | 'parents' | 'type' | 'materialityMap'
  | 'customer' | 'referrals' | 'appConfigCode' | 'created' | 'metadata'>;

export type RootInitiativeDataMin = Pick<RootInitiativeData, '_id' | 'name' | 'tags' | 'appConfigCode'>;

export interface RootInitiativeWithSubscriptionsData extends RootInitiativeData {
  appConfig?: AppConfig;
  calculatedSubscriptions: Subscription[];
}


export const rootInitiativeProject: KeysEnum<RootInitiativeData> = {
  _id: 1,
  name: 1,
  permissionGroup: 1,
  tags: 1,
  created: 1,
  appConfigCode: 1,
  parents: 1,
  materialityMap: 1,
  type: 1,
  parentId: 1,
  customer: 1,
  metadata: 1,
  referrals: 1,
};

type LevelMap = Record<string, Set<string>>;

export interface RootInitiativeWithFirst extends RootInitiativeData {
  firstInitiativeId: ObjectId,
  initiativeIds: string[],
}

interface UniversalTrackerValuesByUniversalTrackerId {
  initiativeId: ObjectId;
  universalTrackerIds: ObjectId[];
  visibility?: VisibilityStatus;
}

interface MetricGroupParams {
  groupIds: ObjectId[];
  initiativeId: string | ObjectId;
  children?: InitiativePlain[];
}


interface ParentSearch {
  initiativeId: ObjectId,
  parentId: ObjectId
  project?: InitiativePlainProject
}

export class InitiativeRepository {

  static supportedFrameworks = [
    FrameworkTypes.GICS,
    FrameworkTypes.ICB,
    FrameworkTypes.ICB2019,
  ];

  public static mustFindById(id: string | ObjectId, projection?: ProjectionType<InitiativePlain>) {
    return Initiative.findById(id, projection).orFail().exec();
  }

  public static async getFullTree(initiativeId: string) {
    const initiativeTree = await InitiativeRepository.getAllParentsById(initiativeId);

    const initiative = initiativeTree.pop();
    const parentIds = initiative?.parents ?? [];

    const childIds = await this.getAllChildrenById(initiativeId);

    return parentIds.concat(childIds);
  }

  public static async getInitiativeTree(user: UserModel, projection: { [key: string]: number } = minimumFields) {
    const initiativeMap = new Map();
    if (!Array.isArray(user.permissions)) {
      return [];
    }

    for (const { initiativeId } of user.permissions) {

      // If we already have it in the map, skip it
      const key = initiativeId.toString();
      if (!initiativeMap.has(key)) {
        const initiatives = await this.getAllChildrenById(key, { tags: { $nin: valueChainCategories } }, projection);

        initiatives.forEach((i: InitiativePlain) => {
          return initiativeMap.set(i._id.toString(), i);
        });
      }
    }

    return Array.from(initiativeMap.values());
  }

  public static async getMainTreeChildren<T = Omit<InitiativePlain, 'children'>>(initiativeId: string | ObjectId) {
    return this.getAllChildrenById<T>(initiativeId, {
      tags: { $nin: valueChainCategories },
    });
  }

  public static async getAllChildrenById<T = Omit<InitiativePlain, 'children'>>(
    initiativeId: (string | ObjectId) | (string | ObjectId)[],
    restrictSearch?: { tags: unknown },
    projection: { [key: string]: number } = minimumFields
  ): Promise<T[]> {

    const $graphLookup: PipelineStage.GraphLookup['$graphLookup'] = {
      from: 'initiatives',
      startWith: '$_id',
      connectFromField: '_id',
      connectToField: 'parentId',
      as: 'children'
    };

    if (restrictSearch) {
      $graphLookup.restrictSearchWithMatch = restrictSearch;
    }

    const $match = Array.isArray(initiativeId) ?
      { _id: { $in: initiativeId.map(id => new ObjectId(id)) } } :
      { _id: new ObjectId(initiativeId) }

    const results = await Initiative.aggregate<T & { children?: T[] }>([
      { $match },
      { $graphLookup: $graphLookup },
      { $project: { ...projection, children: { ...projection } } }
    ]).exec()

    return results.reduce((a, initiative) => {
      const children = initiative.children ?? [];
      delete (initiative.children);
      children.unshift(initiative);
      return [...a, ...children];

    }, [] as T[]);
  }

  public static async hasPortfolioTracker(user: UserRolePermissions, role: UserRoles) {
    if (!user.permissions) {
      return false;
    }
    const ids = user.permissions.reduce((acc, p) => {
      return p.permissions.includes(role) ? [...acc, p.initiativeId] : acc;
    }, [] as ObjectId[]);

    if (ids.length === 0) {
      return false;
    }

    return Initiative.exists({ _id: { $in: ids }, type: InitiativeTypes.Group })
  }

  public static async getInitiativeGroupTree(initiativeIds: (string | ObjectId)[], maxDepth = 3): Promise<InitiativePlain[]> {

    const process = async (
      initiatives: InitiativeModelChildrenGroups[],
      map = new Map<string, InitiativeModelChildrenGroups>(),
      depth = 1): Promise<Map<string, InitiativeModelChildrenGroups>> => {

      const ids = []
      for (const initiative of initiatives) {
        map.set(initiative._id.toString(), initiative);

        if (initiative.initiativeGroup) {
          const groupIds = initiative.initiativeGroup.group
            .map(g => String(g.initiativeId))
            .filter(id => !map.has(id));

          ids.push(...groupIds);
        }

      }

      if (depth >= maxDepth || ids.length === 0) {
        return map;
      }


      const innerInitiatives = await InitiativeRepository.getInitiativesWithGroups(ids);
      return process(innerInitiatives, map, depth++);
    }

    const initiatives = await InitiativeRepository.getInitiativesWithGroups(initiativeIds);

    // Root should only be initiativeGroups
    const groups = initiatives.filter(i => i.initiativeGroupId)

    const tree = await process(groups);
    return Array.from(tree.values());
  }

  public static getInitiativesWithGroups(initiativeId: (string | ObjectId)[]): Promise<InitiativeModelChildrenGroups[]> {
    return Initiative.aggregate([
      {
        $match: {
          _id: { $in: initiativeId.map(id => new ObjectId(id)) },
        },
      },
      {
        $lookup: {
          from: 'initiative-groups',
          localField: 'initiativeGroupId',
          foreignField: '_id',
          as: 'groups',
        },
      },
      {
        $project: {
          ...minimumFields,
          initiativeGroupId: 1,
          initiativeGroup: { $arrayElemAt: ['$groups', 0] },
        },
      },
    ]).exec();
  }

  public static async getAllParentsById(initiativeId: string | ObjectId, project?: InitiativePlainProject): Promise<InitiativePlainWithParents[]> {
    const pipeline: PipelineStage[] = [
      { $match: { _id: new ObjectId(initiativeId) } },
      recursiveParentLookup,
    ];

    if (project) {
      pipeline.push({ $project: { ...project, parents: project } })
    }

    return Initiative.aggregate(pipeline).exec();
  }

  public static async getAllParentsUptoId({ initiativeId, parentId, project }: ParentSearch): Promise<InitiativePlainWithParents[]> {
    const pipeline: PipelineStage[] = [
      { $match: { _id: new ObjectId(initiativeId) } },
      {
        $graphLookup: {
          from: 'initiatives',
          startWith: '$parentId',
          connectFromField: 'parentId',
          connectToField: '_id',
          // Should stop once reached the parent
          restrictSearchWithMatch: { _id: { $ne: parentId } },
          as: 'parents',

        }
      },
    ];

    if (project) {
      pipeline.push({ $project: { ...project, parents: project } })
    }

    return Initiative.aggregate(pipeline).exec();
  }

  public static async getSortedParentsUntilRootOrganization(
    initiative: Pick<InitiativePlain, '_id' | 'parentId' | 'tags'>
  ): Promise<InitiativePlainWithParents['parents']> {
    if (isOrganization(initiative)) {
      return [];
    }

    const [initiativeWithParents] = await InitiativeRepository.getAllParentsById(initiative._id);
    if (!initiativeWithParents || !initiativeWithParents.parents.length) {
      return [];
    }

    const { parents } = initiativeWithParents;

    const sortedParents: InitiativePlain[] = [];
    for (let i = 0; i < parents.length; i++) {
      const parent = parents.find(({ _id }) => (i ? sortedParents[i - 1] : initiative).parentId?.equals(_id));
      if (parent) {
        sortedParents[i] = parent;
      }
      if (isOrganization(parent)) {
        break;
      }
    }

    return sortedParents.reverse();
  }

  public static async getRootInitiativesForIds(initiativeIds: (string | ObjectId)[]): Promise<RootInitiativeWithFirst[]> {

    const project: KeysEnum<RootInitiativeData> = {
      _id: 1,
      name: 1,
      tags: 1,
      type: 1,
      permissionGroup: 1,
      parents: 1,
      parentId: 1,
      customer: 1,
      referrals: 1,
      appConfigCode: 1,
      materialityMap: 1,
      created: 1,
      metadata: 1
    }
    const pipeline = [
      { $match: { _id: { $in: initiativeIds.map(id => new ObjectId(id)) } } },
      recursiveParentLookup,
      { $project: { ...project, parents: project } }
    ];

    const initiatives = await Initiative.aggregate<RootInitiativeData>(pipeline).exec();
    const rootInitiativeMap = InitiativeRepository.createRootMap(initiatives);

    return Array.from(rootInitiativeMap.values());
  }

  public static createRootMap(initiatives: RootInitiativeData[]) {
    const rootMap = new Map<string, LevelMap>()

    return initiatives.reduce((acc, i) => {
      const currentLevelId = String(i._id);

      if (isOrganization(i)) {
        rootMap.set(currentLevelId, { [currentLevelId]: new Set<string>() })
        acc.set(currentLevelId, {
          ...i,
          firstInitiativeId: i._id,
          initiativeIds: [currentLevelId],
          parents: undefined,
        });
        return acc;
      }

      if (!i.parents || !i.parentId) {
        return acc;
      }

      const parentId = String(i.parentId);
      const parentRootData = InitiativeRepository.findParentWithIds(i.parents, parentId, isOrganization);
      if (!parentRootData) {
        return acc;
      }

      const rootId = String(parentRootData.root._id);

      const current = acc.get(rootId);
      if (!current) {
        rootMap.set(rootId, { [currentLevelId]: parentRootData.ids })
        acc.set(rootId, {
          ...parentRootData.root,
          firstInitiativeId: i._id,
          initiativeIds: [currentLevelId],
          parents: undefined,
        })
        return acc;
      }

      const currentMap = rootMap.get(rootId);
      if (!currentMap) {
        wwgLogger.error(`Current Map is not set for id ${rootId}`)
        return acc; // Should be always set by now
      }

      if (current.initiativeIds.some(id => id === rootId || currentMap[currentLevelId])) {
        // directly root access or id is already there, nothing to do
        return acc
      }

      const hasParentMapping = Array.from(parentRootData.ids).some(id => currentMap[id]);
      if (hasParentMapping) {
        return acc; // Skip will be included from parent
      }

      // Check if clean up needed
      Object.entries(currentMap).forEach(([existingId, parentIds]) => {
        if (parentIds.has(currentLevelId)) {
          // Add current level as parent, clean up initiativeId
          current.initiativeIds = current.initiativeIds.filter(id => id !== existingId)
          delete (currentMap[existingId]);
        }
      });

      // Add new entry
      currentMap[currentLevelId] = parentRootData.ids;
      current.initiativeIds.push(currentLevelId)

      acc.set(rootId, current);

      return acc;
    }, new Map<string, RootInitiativeWithFirst>());
  }

  /**
   * Need to keep track of all ids we passed to ensure we can determine if this is unique
   */
  public static findParentWithIds(
    parents: InitiativePlain[],
    parentId: string,
    conditionCheck: ConditionCheck,
    ids = new Set<string>(),
  ): ParentLookupInfo | undefined {
    const directParent = parents.find(parent => parentId === parent._id.toString());
    if (!directParent) {
      return undefined;
    }

    ids.add(parentId)
    const match = conditionCheck(directParent);
    if (match) {
      return { root: directParent, parentId, ids };
    }

    if (directParent.parentId) {
      return this.findParentWithIds(parents, String(directParent.parentId), conditionCheck, ids);
    }

    return undefined;
  }

  public static async getTreeForRootInitiative(orgId: string | ObjectId, user: UserModel): Promise<{ root: RootInitiativeData, tree: InitiativePlain[] }> {

    const initiativeId = String(orgId);
    const ids = user.permissions?.map(p => p.initiativeId) ?? [];
    if (ids.length === 0) {
      throw new PermissionDeniedError(InitiativeErrorMessages.RootAccess);
    }

    const rootInitiatives = await InitiativeRepository.getRootInitiativesForIds(ids);
    const rootOrg = rootInitiatives.find(i => String(i._id) === initiativeId)
    if (!rootOrg) {
      throw new PermissionDeniedError(InitiativeErrorMessages.RootAccess);
    }

    return {
      root: rootOrg,
      tree: await InitiativeRepository.getAllChildrenById(
        rootOrg.initiativeIds,
        { tags: { $nin: valueChainCategories } },
      )
    }
  }

  public static async getInitiativeKpis(initiativeId: string | ObjectId): Promise<UniversalTrackerBlueprintMin[]> {
    const [initiative] = await InitiativeRepository.getAllParentsById(initiativeId);
    if (!initiative) {
      return [];
    }

    const ids = [initiative._id];
    if (initiative.parents) {
      initiative.parents.forEach(i => ids.push(i._id));
    }

    const metricGroups = await MetricGroup.find({
      $or: [
        { initiativeId: { $in: ids } }, // Might not need this
        {
          'share.initiativeId': { $in: ids }
        }
      ]
    })
      .populate('universalTracker', utrForBlueprintProject)
      .lean().exec();

    const groupUtrs = metricGroups.map(g => g.universalTracker ?? []).flat() as UniversalTrackerPlain[];

    const utrs = await UniversalTrackerRepository.find(
      { ownerId: { $in: ids, $nin: groupUtrs.map(u => u._id) } },
      utrForBlueprintProject,
      { lean: true },
    );

    return groupUtrs.concat(utrs);
  }

  public static async getInitiativeKpiGroups(initiativeId: string | ObjectId, type: string = MetricGroupType.Custom): Promise<MetricGroupPlain[]> {
    const [initiative] = await InitiativeRepository.getAllParentsById(initiativeId);
    if (!initiative) {
      return [];
    }

    const ids = [initiative._id];
    const parentIds = new Set<string>()

    if (initiative.parents) {
      initiative.parents.forEach(i => {
        ids.push(i._id);
        parentIds.add(String(i._id))
      });
    }

    const $or: { [key: string]: { $in: ObjectId[] } }[] = [
      { initiativeId: { $in: ids } },
      {
        'share.initiativeId': { $in: ids }
      }
    ];

    // START BankingSettings: Support shared assigned metrics inherited from ABS
    const assignedMetricGroupIds = await BankingCodeService.getPortfolioMappingMetricGroupIds(initiative.code);
    if (assignedMetricGroupIds.length > 0) {
      $or.push({ _id: { $in: assignedMetricGroupIds } });
    }
    // END BankingSetings

    const groups = (await MetricGroup.find({ type, $or }, { share: 0 })
      .populate('initiative', { name: 1 })
      .populate('universalTracker')
      .populate('survey', metricGroupSurveyProjection)
      .sort({ created: 'desc' })
      .lean()
      .exec()) as MetricGroupPlain[];

    const mainId = String(initiative._id);
    return groups.map(g => {
      return ({
        ...g,
        accessType: InitiativeRepository.getMetricGroupType(g, mainId, parentIds),
      });
    });
  }

  public static async getOrganizationKpiGroups(initiativeId: string | ObjectId): Promise<MetricGroupPlain[]> {
    const initiative = await Initiative.findById(initiativeId, { code: 1, type: 1 }).orFail().lean().exec();
    const rootInitiativeWithChildren = await InitiativeRepository.getOrganizationWithChildren(initiativeId);
    const initiativeIds = rootInitiativeWithChildren.map((initiative) => initiative._id);

    const $or: { [key: string]: { $in: ObjectId[] } }[] = [
      { initiativeId: { $in: initiativeIds } },
      {
        'share.initiativeId': { $in: initiativeIds }
      }
    ];

    // START BankingSettings: Support shared assigned metrics inherited from ABS
    const assignedMetricGroupIds = await BankingCodeService.getPortfolioMappingMetricGroupIds(initiative.code);
    if (assignedMetricGroupIds.length > 0) {
      $or.push({ _id: { $in: assignedMetricGroupIds } });
    }
    // END BankingSetings

    const groups = await MetricGroup.find({ type: MetricGroupType.Custom, $or }, { share: 0 })
      .populate('initiative', { name: 1 })
      .populate('universalTracker')
      .sort({ created: 'desc' })
      .lean<MetricGroupPlain[]>()
      .exec();

    const mainId = String(initiative._id);
    return groups.map(g => {
      return ({
        ...g,
        accessType: InitiativeRepository.getMetricGroupType(g, mainId, new Set(initiativeIds.map(id => String(id)))),
      });
    });
  }
  public static async getInitiativeKpiGroupsSingle(portfolio: Pick<InitiativePlain, '_id' | 'code'>, type: string = MetricGroupType.Custom): Promise<MetricGroupPlain[]> {
    const groups = await MetricGroup.find({ initiativeId: portfolio._id, type })
      .populate('initiative', { name: 1 })
      .populate('share.initiative', { name: 1, profile: 1 })
      .populate('universalTracker')
      .sort({ created: 'desc' })
      .lean().exec() as MetricGroupPlain[];

    // Loading extra details for search etc. that allow DBS to select ABS packs etc.
    // without explicitly sharing it, by using portfolio code config mapping
    const mappingMetricGroupIds = await BankingCodeService.getPortfolioMappingMetricGroupIds(portfolio.code);
    const loadedIds = groups.map(g => g._id.toString());
    // Ensure we are not loading already loaded groups, only additional ones
    const assignedMetricGroupIds = mappingMetricGroupIds.filter(id => !loadedIds.includes(id.toString()))

    const mainId = portfolio._id.toString();
    const parentIds = new Set<string>();
    const mappingGroups = assignedMetricGroupIds.length ?
      await MetricGroup.find({ _id: { $in: assignedMetricGroupIds }, type }, { share: 0 })
        .populate('initiative', { name: 1 })
        .populate('universalTracker')
        .sort({ created: 'desc' })
        .lean().exec() as MetricGroupPlain[] : [];

    return groups.concat(mappingGroups).map(g => {
      return ({
        ...g,
        accessType: InitiativeRepository.getMetricGroupType(g, mainId, parentIds),
      });
    });
  }

  public static async getRecursiveInitiativeKpiGroups(params: MetricGroupParams): Promise<MetricGroupPlain[]> {

    const { initiativeId, children, groupIds } = params;

    if (groupIds.length === 0) {
      return [];
    }

    const childIds = (children ?? await InitiativeRepository.getAllChildrenById(initiativeId, {
      tags: { $nin: valueChainCategories },
    })).map(({ _id }) => _id);

    const groups = await MetricGroup.find({
      _id: { $in: groupIds },
      $or: [
        { initiativeId: { $in: childIds } },
        {
          'share.initiativeId': { $in: childIds },
        },
      ],
    })
      .populate('initiative', { name: 1 })
      .populate('share.initiative', { name: 1, profile: 1 })
      .populate('universalTracker')
      .lean()
      .exec() as MetricGroupPlain[];

    const mainId = String(initiativeId);
    const parenIds = new Set(childIds.map((id) => String(id)));

    return groups.map((g) => {
      return {
        ...g,
        // @TODO need to change the logic to get the accessType as we are filtering current and below levels
        accessType: InitiativeRepository.getMetricGroupType(g, mainId, parenIds),
      };
    });
  }

  private static getMetricGroupType(group: MetricGroupPlain, initiativeId: string, parentIds: Set<string>) {
    const id = String(group.initiativeId);
    if (id === initiativeId) {
      return group.type; // custom
    }
    return parentIds.has(id) ? AccessType.Inherited : AccessType.Assigned;
  }

  public static async searchByUserTree(searchStr: string, user: UserModel) {

    const initiatives = await InitiativeRepository.getInitiativeTree(user);
    const suggestions: any[] = [];
    initiatives.map((initiative) => {
      const isVcInitiative = Array.isArray(initiative.tags) && initiative.tags
        .some((t: string) => valueChainCategories.includes(t as ValueChainCategory))

      if (initiative.name && !isVcInitiative) {
        suggestions.push(
          {
            _id: initiative._id,
            name: initiative.name,
            profile: initiative.profile
          });
      }
    });
    return suggestions;
  }

  public static async searchByHoldings(user: UserModel) {
    const match: any = {
      $and: [
        { visible: true },
        { tags: { $eq: InitiativeTags.Organization, $nin: valueChainCategories } },
        { type: InitiativeTypes.Initiative },
        { initiativeGroupId: { $exists: false }}
      ],
    };

    if (!user.isStaff) {
      match.$and.push({ tags: { $nin: InitiativeTags.StaffOrganization } });
    }

    const initiativesByTag = (await Initiative.find(match, initiativeSuggestionProjection)
      .lean<Omit<InitiativeSuggestion, 'surveys' | 'countries'>[]>()
      .exec());
    return initiativesByTag;
  }

  public static async findWithTags(
    tags: InitiativeTags[],
    onlyPublic: Boolean = true,
    excludeInitiativeGroups: Boolean = true): Promise<InitiativePlain[]> {
    const searchQuery: { type?: InitiativeTypes, tags: any, isPublic?: Boolean } = {
      tags: { $all: tags },
    };
    if (onlyPublic) {
      searchQuery.isPublic = true;
    }
    if (excludeInitiativeGroups) {
      searchQuery.type = InitiativeTypes.Initiative;
    }
    return Initiative.find(searchQuery).lean().exec();
  }

  public static async findMainUtrvInitiative(utrv: Pick<UniversalTrackerValuePlain, 'initiativeId' | 'compositeData'>) {
    let initiativeId = utrv.initiativeId;
    if (utrv.compositeData && utrv.compositeData.surveyId) {
      const survey = await Survey.findById(utrv.compositeData.surveyId, { initiativeId: 1 }).orFail().lean().exec();
      initiativeId = survey.initiativeId;
    }
    return Initiative.findById(initiativeId).exec();
  }

  public static findParent(parents: InitiativePlain[], parentId: string, conditionCheck: ConditionCheck): InitiativePlain | undefined {
    const directParent = parents.find(parent => parentId === parent._id.toString());
    if (!directParent) {
      return undefined;
    }

    const match = conditionCheck(directParent);
    if (match) {
      return directParent;
    }

    if (directParent.parentId) {
      return this.findParent(parents, String(directParent.parentId), conditionCheck);
    }

    return undefined;
  }

  public static async getInitiativeById(initiativeId: string | ObjectId, includeParents: boolean = false): Promise<InitiativePlainWithRoot | undefined> {
    const initiativeTree = await InitiativeRepository.getAllParentsById(initiativeId);

    if (!Array.isArray(initiativeTree)) {
      return undefined;
    }

    const initiative = initiativeTree.pop();
    if (!initiative) {
      return undefined;
    }

    if (initiative.linkedUniversalTrackers.length <= 0) {
      // Stop recursing higher than organizational level
      if (!isOrganization(initiative)) {
        const conditionCheck = (p: InitiativePlain) => {
          const hasUTRs = p.linkedUniversalTrackers && p.linkedUniversalTrackers.length > 0;
          return isOrganization(p) || hasUTRs;
        }
        const parentInitiative = initiative.parentId &&
          InitiativeRepository.findParent(initiative.parents, String(initiative.parentId), conditionCheck);
        if (parentInitiative) {
          initiative.linkedUniversalTrackers = parentInitiative.linkedUniversalTrackers;
        }
      }
    }

    const hasValidIndustry = (p: InitiativePlain) => Boolean(p.industry?.icb2019?.level3
      && ICBMaterialityData.getById(p.industry.icb2019.level3));

    if (!hasValidIndustry(initiative)) {
      const parentInitiative = initiative.parentId &&
        InitiativeRepository.findParent(initiative.parents, String(initiative.parentId), hasValidIndustry);
      if (parentInitiative) {
        initiative.industry = parentInitiative.industry;
      }
    }

    if (!hasMaterialityMap(initiative)) {
      const parentInitiative = initiative.parentId &&
        InitiativeRepository.findParent(initiative.parents, String(initiative.parentId), hasMaterialityMap);
      if (parentInitiative) {
        initiative.materialityMap = parentInitiative.materialityMap;
      }
      if (!hasMaterialityMap(initiative) && initiative.industry) {
        initiative.materialityMap = getMaterialityMap(initiative.industry)
      }
    }

    if (!initiative.displaySettings) {
      const displayParent = InitiativeRepository.findParent(
        initiative.parents,
        String(initiative.parentId),
        (parent) => Boolean(parent.displaySettings)
      );
      initiative.displaySettings = displayParent?.displaySettings;
    }

    initiative.materiality = getMateriality(initiative.industry, initiative.materialityMap);
    initiative.sectorText = getSectorText(initiative.industry);
    initiative.industryText = getIndustryText(initiative.industry);

    if (!initiative.root && !isOrganization(initiative)) {
      initiative.root = InitiativeRepository.findParent(initiative.parents, String(initiative.parentId), isOrganization);
    }

    if (!includeParents) {
      delete ((initiative as InitiativePlain).parents);
    }

    return initiative;
  }

  public static async getTargets({ initiativeId, universalTrackerIds, visibility }: UniversalTrackerValuesByUniversalTrackerId) {
    const $match = applyVisibilityFilter({
      initiativeId: initiativeId,
      status: ActionList.Verified,
      deletedDate: { $exists: false },
      universalTrackerId: { $in: universalTrackerIds },
      type: UtrvType.Target,
    }, visibility);

    return UniversalTrackerValue
      .aggregate([
        {
          $match: $match
        },
        {
          $sort: {
            effectiveDate: 1,
          }
        },
        {
          $group: {
            _id: '$universalTrackerId',
            target: { $last: "$$ROOT" }
          }
        },
        {
          $replaceRoot: {
            newRoot: '$target',
          },
        },
        {
          $project: applyVisibilityProject(surveyActionMinimalUtrvProjection, visibility)
        },
      ])
      .exec();
  }

  public async getCompletedSurveyIds(ids: (string | ObjectId)[], metricGroup?: MetricGroupPlain) {

    const surveyLookupAnd: unknown[] = [
      { $in: ['$initiativeId', '$$childrenIds'] },
      { $ifNull: ['$completedDate', false] },
      { $ifNull: ['$deletedDate', true] },
    ];

    if (metricGroup) {
      surveyLookupAnd.push({ $in: [metricGroup._id, { $ifNull: ['$scope.custom', []] }] })
    }

    const aggregate = [
      {
        $match: { _id: { $in: ids.map(id => new ObjectId(id)) } },
      },
      {
        $graphLookup: {
          from: 'initiatives',
          startWith: '$_id',
          connectFromField: '_id',
          connectToField: 'parentId',
          restrictSearchWithMatch: { tags: { $nin: valueChainCategories } },
          as: 'children',
        }
      },
      {
        $project: {
          childrenIds: {
            $reduce: {
              input: "$children",
              initialValue: {
                ids: ["$_id"],
                initiatives: [{ _id: "$_id", parentId: "$parentId" }],
              },
              in: {
                ids: { $concatArrays: ["$$value.ids", ["$$this._id"]] },
                initiatives: {
                  "$concatArrays": [
                    "$$value.initiatives",
                    [{ _id: "$$this._id", parentId: "$$this.parentId" }]
                  ]
                }
              }
            }
          }
        }
      },
      {
        $lookup: {
          from: 'surveys',
          let: { childrenIds: '$childrenIds.ids' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: surveyLookupAnd,
                },
              },
            },
          ],
          as: 'surveys',
        },
      },
      {
        $project: {
          initiatives: "$childrenIds.initiatives",
          surveys: { _id: 1, initiativeId: 1, effectiveDate: 1 },
        }
      }
    ];

    const data: SurveyRecursiveResult[] = await Initiative.aggregate(aggregate).exec();

    return getAggregateSurveyIds(data, AggregationStrategy.Latest)
  }

  public async find(match = {}): Promise<InitiativeModel[]> {
    return Initiative.find(match).exec();
  }

  public async findOne(filter?: FilterQuery<InitiativeModel>, projection?: ProjectionType<InitiativeModel>) {
    return Initiative.findOne(filter, projection).exec();
  }

  public async findById(id: any | string | number) {
    return Initiative.findById(id).exec();
  }

  public async mustFindById(id: string | ObjectId): Promise<InitiativeModel> {

    if (!ObjectId.isValid(id)) {
      throw new Error(`Initiative id is not valid`);
    }

    const initiative = await Initiative.findById(id).exec();
    if (!initiative) {
      throw new Error(`Initiative was not found with id '${id}'`);
    }

    return initiative;
  }

  public static async findByEmailDomain(user: Pick<UserPlain, 'email'>): Promise<Pick<InitiativeModel, 'name'>[]> {
    // Extract email domain
    const [, emailDomain] = user.email.split('@');

    // Find any users with same domain
    if (isPopularEmailDomain(emailDomain)) {
      return [];
    }

    const users = await UserRepository.getUsersByEmailDomain(emailDomain);
    const initiativeIds = users.reduce((acc, user) => (
      [
      ...acc,
      ...user.permissions
        .filter(p => p.permissions.includes(UserRoles.Manager))
        .map(p => p.initiativeId)
      ]
    ), [] as ObjectId[]);

    return Initiative.find({
      _id: { $in: initiativeIds }
    }, { name: 1 }).limit(3).lean().exec();
  }

  public async findWithSubscriptions() {
    return Initiative.find<InitiativeWithCustomer>({
      customer: { $exists: true }
    }).exec();
  }

  public async findPeers(parentId: ObjectId, framework: FrameworkTypes, levels: IndustryLevels): Promise<InitiativePlain[]> {

    if (!InitiativeRepository.supportedFrameworks.includes(framework)) {
      return [];
    }
    const matching = {
      tags: InitiativeTags.Organization,
      [`industry.${framework}.level1`]: levels.level1,
    };

    ['level2', 'level3', 'level4'].forEach(key => {
      if (levels[key]) {
        const queryKey = `industry.${framework}.${key}`;
        matching[queryKey] = levels[key];
      }
    });

    return Initiative.find(matching).limit(1000).lean().exec();
  }

  public async findByParentIdPeers(parentId?: ObjectId): Promise<InitiativePlain[]> {
    return Initiative.find({ parentId: parentId }).limit(1000).lean().exec();
  }

  public async findChildrenWithTags(parentId: ObjectId | string, requiredValueChains: ValueChainCategory[]): Promise<InitiativePlain[]> {
    return Initiative.find({
      parentId: new ObjectId(parentId),
      tags: { $in: requiredValueChains },
    }).lean().exec();
  }

  public static getInitiativesByOktaGroupId(groupIds: string[], project: PartialKeysEnum<InitiativePlain>) {
    return Initiative.find({ oktaGroupIds: { $in: groupIds } }, project).lean().exec();
  }

  public getSGXRecipients(): Promise<InitiativeWithFinancialEndDate[]> {

    // Create a check range from financial end date to future X months
    const dateRangeInMonths = 4;

    const projection: KeysEnum<InitiativeWithFinancialEndDate> = {
      _id: 1,
      name: 1,
      code: 1,
      tags: 1,
      appConfigCode: 1,
      financialEndDate: 1,
      notificationStartDate: 1,
      notificationEndDate: 1,
      users: 1,
    }

    const utcFullYear = new Date().getUTCFullYear();
    const lastNumberOfMonths = getLastNumberOfMonths(dateRangeInMonths);

    const aggregate = [
      {
        $match: {
          // Exclude current month, Example: financialEndDate.month=Jan
          // would only send first notification in Feb and later.
          // Unless we add notification that needs to be sent earlier than
          // 3 months before the deadline
          'financialEndDate.month': { $in: lastNumberOfMonths },
          'metadata.sgx_issuer_name': { $ne: null }
        },
      },
      {
        $project: {
          ...projection,
          notificationStartDate: {
            $toDate: {
              $concat: [
                {
                  $cond: {
                    if: {
                      $lte: [
                        // if range is 4 months, then last [9, 10, 11, 12] should go to previous year,
                        // 8 and bellow stay with current year
                        { $indexOfArray: [getFinancialMonths(), '$financialEndDate.month'] },
                        (12 - dateRangeInMonths)
                      ]
                    },
                    then: utcFullYear.toString(),
                    else: (utcFullYear - 1).toString(),
                  }
                },
                `-`,
                {
                  $toString: {
                    $indexOfArray: [getFinancialMonths(), '$financialEndDate.month'],
                  },
                },
                '-',
                '$financialEndDate.day',
              ],
            },
          },
        },
      },
      {
        $addFields: {
          notificationEndDate: {
            $dateAdd: {
              startDate: '$notificationStartDate',
              unit: "month",
              amount: 4
            }
          },
        },
      },
      {
        $match: {
          $expr: {
            $and: [
              // Must be start < $NOW < end
              { $lt: ['$notificationStartDate', '$$NOW'] },
              { $gt: ['$notificationEndDate', '$$NOW'] },
            ],
          },
        },
      },
      {
        $lookup: {
          from: 'surveys',
          let: {
            startDate: '$notificationStartDate',
            initiativeId: '$_id',
          },
          pipeline: [
            {
              $match: {
                completedDate: { $exists: true },
                $expr: {
                  $and: [{ $eq: ['$initiativeId', '$$initiativeId'] }, { $gte: ['$effectiveDate', '$$startDate'] }],
                },
              },
            },
          ],
          as: 'surveys',
        },
      },
      {
        $match: { surveys: [] },
      },
      {
        $lookup: {
          from: 'users',
          let: {
            initiativeId: '$_id',
            roles: 'manager',
          },
          pipeline: [
            {
              $unwind: '$permissions',
            },
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$permissions.initiativeId', '$$initiativeId'] },
                    { $in: ['$$roles', '$permissions.permissions'] },
                  ],
                },
              },
            },
            {
              $project: { email: 1, firstName: 1, surname: 1 },
            },
          ],
          as: 'users',
        },
      },
      {
        $project: projection,
      },
    ];

    return Initiative.aggregate(aggregate).exec();
  }


  /**
   * Used when we only need to find root org without root service other methods.
   * To avoid circular dependencies with RootInitiativeService
   */
  public static async getOrganization<T extends Pick<RootInitiativeData, '_id' | 'tags' | 'appConfigCode'>>(initiative: T): Promise<RootInitiativeData | T> {
    if (isOrganization(initiative)) {
      return initiative;
    }

    const [root] = await InitiativeRepository.getRootInitiativesForIds([initiative._id]);
    return root ?? initiative;
  }

  public static async getOrganizationWithChildren(initiativeId: string | ObjectId) {
    const [rootInitiative] = await InitiativeRepository.getRootInitiativesForIds([initiativeId]);

    const rootInitiativeWithChildren = await InitiativeRepository.getMainTreeChildren(rootInitiative._id);

    return rootInitiativeWithChildren;
  }

  public static async getCurrentAndParentInitiativeIds(initiativeId: ObjectId) {
    const initiativeTree = await InitiativeRepository.getAllParentsById(initiativeId);
    const parents = initiativeTree.pop()?.parents ?? [];
    return [initiativeId].concat(parents.map((parent) => parent._id));
  }
}

export const createInitiativeRepository = () => new InitiativeRepository();

let instance: InitiativeRepository;
export const getInitiativeRepository = (): InitiativeRepository => {
  if (!instance) {
    instance = new InitiativeRepository();
  }
  return instance;
}
