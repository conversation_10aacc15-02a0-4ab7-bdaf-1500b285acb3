/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import UniversalTrackerValue from '../models/universalTrackerValue';
import { ActionList } from '../service/utr/constants';
import { universalTrackerLookup } from './utrvAggregations';
import { excludeSoftDeleted } from './aggregations';
import { SurveyModelPlain } from '../models/survey';
import { standards } from '@g17eco/core';


export interface StandardsData {
  code: string;
  title: string;
  logoSrc: string;
  ratingAgencyUrl: string;
  additionalInformation: string;
}

const standardData: StandardsData[] = Object.entries(standards).map(([code, data]) => {
  return <StandardsData>{
    code,
    title: data.name ?? data.shortName,
    logoSrc: data.src,
    ratingAgencyUrl: data.link,
    additionalInformation: data.description,
  }
});

interface StandardsValue {
  type: string,
  alternatives?: { [key:string]: any },
  code: string,
}

export interface StandardsRepository {
  findByCodes(codes: string[]): Promise<StandardsData[]>
  findSurveyStandardValues(survey: SurveyModelPlain): Promise<StandardsValue[]>;
}


class StandardsRepositoryImpl implements StandardsRepository {

  public async findByCodes(codes: string[]) {
    return standardData.filter((s) => codes.includes(s.code))
  }

  public async findSurveyStandardValues(survey: SurveyModelPlain) {

    const aggregate = [
      {
        $match: {
          _id: { $in: survey.visibleUtrvs },
          ...excludeSoftDeleted(),
          status: ActionList.Verified,
        }
      },
      universalTrackerLookup,
      {
        $project: {
          _id: 1,
          universalTracker:{ $arrayElemAt: ['$universalTracker', 0] }
        }
      },
      {
        $project: {
          _id: 1,
          type: '$universalTracker.type',
          alternatives: '$universalTracker.alternatives',
          code: '$universalTracker.code',
        }
      }
    ];

    return UniversalTrackerValue.aggregate(aggregate);
  }
}

export function getStandardsRepository(): StandardsRepository {
  return new StandardsRepositoryImpl();
}
