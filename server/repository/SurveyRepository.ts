/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import Survey, {
  ScopeUpdate,
  SourceItemExtended,
  SurveyActionData,
  SurveyActionDataAggregation,
  SurveyModel,
  SurveyModelMinWithUtrvsPlain,
  SurveyModelPlain,
  SurveyModelPlainWithInitiative,
  SurveyType,
  SurveyWithInitiative
} from '../models/survey';
import {
  initiativeMinFields,
  surveyActionData,
  SurveySummary,
  surveySummary,
  surveyWithUtrvsProjection,
  universalTrackerFields,
  valueValidationProjection,
} from './projections';
import { assurancePortfolioLookUp, excludeSoftDeleted, stakeholdersAggregation, } from './aggregations';
import {
  initiativeLookup,
  leafSurveyUtrvLookup,
  universalTrackerLookup,
  userStakeholderLookup,
} from './utrvAggregations';
import { activeBlueprints, adapterBlueprints, Blueprints } from '../survey/blueprints';
import { getCsvName, getPreferredAltName } from '../service/assurance/csvContext';
import { userMinFields, UserPlain } from '../models/user';
import UniversalTrackerValue, { assuredUtrvActionStatuses, UniversalTrackerValuePlain } from '../models/universalTrackerValue';
import { GroupType, UTrCustomGroup } from '../survey/utrGroupConfigs';
import { InitiativeRepository } from './InitiativeRepository';
import { MetricGroupManager } from '../service/metric/MetricGroupManager';
import { ActionList, DataPeriods } from '../service/utr/constants';
import { getIndustryText } from '../service/reporting/FrameworkMapping';
import {
  KeysEnum,
  PartialKeysEnum,
  universalTrackerPublicFields,
  universalTrackerValuePublicFields,
} from "../models/public/projectionUtils";
import { SurveyPublic, UnitConfigMap } from "../models/public/surveyType";
import { FilterQuery, PipelineStage, ProjectionType } from 'mongoose';
import BadRequestError from '../error/BadRequestError';
import { DownloadMultiScope, DownloadScope, DownloadScopeData } from '../service/survey/scope/downloadScope';
import { SurveyCalculator } from '../service/survey/SurveyCalculator';
import { getRootInitiativeService } from '../service/organization/RootInitiativeService';
import UserError from '../error/UserError';
import { Blueprint, getBlueprintRepository } from './BlueprintRepository';
import { getBluePrintContribution } from '../service/survey/BlueprintContribution';
import PermissionDeniedError from '../error/PermissionDeniedError';
import { SurveyPermissions } from '../service/survey/SurveyPermissions';
import { applyVisibilityFilter, applyVisibilityProject } from './visibilityFilter';
import { AssurancePortfolioExpandedExtra } from '../service/assurance/model/AssurancePortfolio';
import { CombinedDataScopeAccess } from '../models/dataShare';
import { StakeholderGroup } from '../models/stakeholderGroup';
import { MetricGroupPlain, MetricGroupType } from '../models/metricGroup';
import { hasChange, projectDate } from '../util/date';
import moment from 'moment';
import { DataSharePermissions } from '../service/share/DataSharePermissions';
import { SurveyQueryHelper } from "../service/survey/SurveyQueryHelper";
import { naturalSort } from '../util/string';
import { InitiativeModel, InitiativePlain } from '../models/initiative';
import { SURVEY } from '../util/terminology';
import { InitiativePermissions } from '../service/initiative/InitiativePermissions';
import { getInitiativeTreeService } from '../service/initiative/InitiativeTreeService';
import { getInitiativeUniversalTrackerService, PipelineStagesOnOverrideType } from '../service/initiative/InitiativeUniversalTrackerService';
import { InitiativeMin } from '../models/public/initiativeType';
import { ExchangeSurvey } from '../service/portfolio/PortfolioService';
import { getUtrSortingConfigMapByScope } from '../survey/utr-sorting';
import { SURVEY_AGGREGATOR_VERSION } from '../service/survey/constants';

interface DefaultStats {
  created: number;
  updated: number;
  verified: number;
  rejected: number;
}

const sourceItemsProjection: KeysEnum<SourceItemExtended> = {
  _id: 1,
  type: 1,
  period: 1,
  effectiveDate: 1,
  initiativeId: 1,
};

export type BulkDelegateSurvey = Pick<SurveyModelPlain, '_id' | 'initiativeId' | 'scope' | 'effectiveDate'>;

export type PortfolioLatestSurvey = Pick<SurveyModelPlain, '_id' | 'initiativeId' | 'effectiveDate' | 'completedDate' | 'period'>;

export const getBaseAggregation = function () {
  return [
    {
      $lookup: {
        from: 'universal-tracker-values',
        localField: 'fragmentUtrvs',
        foreignField: '_id',
        as: 'fragmentUniversalTrackerValues'
      }
    },
    {
      $lookup: {
        from: 'universal-trackers',
        localField: 'fragmentUniversalTrackerValues.universalTrackerId',
        foreignField: '_id',
        as: 'fragmentUniversalTracker'
      }
    },
    {
      $lookup: {
        from: 'initiatives',
        localField: 'initiativeId',
        foreignField: '_id',
        as: 'initiatives'
      }
    },
  ];
};

export interface DateRangeType {
  startDate?: Date | undefined;
  endDate?: Date;
}

export interface BreakdownsQuery extends DateRangeType {
  initiativeIds: ObjectId[];
  isCompleted?: boolean;
  includeStaff?: boolean;
}

export interface PortfolioExchangeQuery extends DateRangeType {
  initiativeIds: ObjectId[];
  period?: DataPeriods;
  metrics?: string[];
  /** Used to generated additional project fields based on requested metrics **/
  customMetricGroups: Pick<MetricGroupPlain, '_id' | 'universalTrackers'>[];
}

export interface BenchmarkingLatestSurveysQuery extends DateRangeType {
  initiativeId: ObjectId;
  surveyPacks?: string[] | undefined;
  utrIds?: ObjectId[];
  dateField?: 'effectiveDate' | 'completedDate';
}

export interface UserBreakdownQuery extends BreakdownsQuery {
  user: Pick<UserPlain, '_id' | 'permissions'>;
  initiatives: InitiativePlain[];
}

type MatchUnknown = Record<string, unknown>;
type QuestionMatchProject = PartialKeysEnum<UniversalTrackerValuePlain>;

export interface BulkDelegateSurveyQuery {
  datePeriods: { period: DataPeriods; effectiveDate: string }[];
  initiativeIds: string[];
}

interface HistoricalReportDataParams {
  survey: Pick<SurveyModelPlain, "_id" | "initiativeId" | "effectiveDate" | "visibleUtrvs" | "unitConfig">;
  downloadScope: DownloadMultiScope;
  reportProject: { history?: 1, notes: 1 };
}

export type ToCheckPermissionSurvey = Pick<
  SurveyModelPlain,
  | 'roles'
  | 'stakeholders'
  | 'visibleStakeholders'
  | 'initiativeId'
  | 'permissions'
  | 'deletedDate'
>;

const toCheckPermissionSurveyProjection: KeysEnum<ToCheckPermissionSurvey> = {
  roles: 1,
  stakeholders: 1,
  visibleStakeholders: 1,
  initiativeId: 1,
  deletedDate: 1,
  permissions: 1,
};

export class SurveyRepository {

  public static async getSurveySummary(sourceNames = activeBlueprints) {

    const aggregation = [
      {
        $match: {
          sourceName: {
            $in: sourceNames
          }
        }
      },
      leafSurveyUtrvLookup(),
      initiativeLookup,
      ...userStakeholderLookup,
      {
        $project: {
          ...surveySummary,
          created: 1,
          initiative: { $arrayElemAt: ['$initiatives', 0] },
          users: userMinFields,
          universalTrackerValues: 1,
        }
      },
      {
        $project: {
          ...surveySummary,
          initiative: initiativeMinFields,
          users: userMinFields,
          onboardingDate: '$created',
          universalTrackerValues: {
            status: 1
          },
        }
      }
    ];

    const defaultStats: DefaultStats = { created: 0, updated: 0, verified: 0, rejected: 0 };
    const reduceFn = (a: DefaultStats, { status }: { status: keyof DefaultStats }) => {
      a[status] += 1;
      return a;
    };

    const result = await Survey.aggregate(aggregation);

    return result.map(({ universalTrackerValues, initiative, ...rest }) => {

      const status = universalTrackerValues.reduce(reduceFn, { ...defaultStats });
      const { stakeholder, verifier } = rest.stakeholders;
      const id = rest._id;
      return ({
        ...rest,
        initiative: initiative?.name,
        surveyId: id,
        totalUTVs: universalTrackerValues.length,
        numStakeholders: stakeholder.length,
        numVerifiers: verifier.length,
        surveyName: getCsvName({ initiative, survey: rest, _id: id }),
        ...status,
      });
    });
  }

  public static async findSurveys(match: object, project: ProjectionType<SurveyModelPlain> = {}, lean = true) {
    return Survey.find(match, project).lean(lean).exec() as Promise<SurveyModelPlain[]>
  }

  public static async findSurveysWithUtrvs(match: object, sort?: PipelineStage.Sort) {
    return Survey.aggregate([
      {
        $match: match,
      },
      {
        $lookup: {
          from: 'universal-tracker-values',
          localField: 'fragmentUtrvs',
          foreignField: '_id',
          as: 'fragmentUniversalTrackerValues',
        },
      },
      {
        $project: surveyWithUtrvsProjection,
      },
      ...(sort ? [sort] : []),
    ]).exec() as Promise<SurveyModelMinWithUtrvsPlain[]>;
  }

  public static async findLatestSurveys(match: object, project: ProjectionType<SurveyModelPlain>, limit: number) {
    return Survey.find(match, project)
      .populate('initiative')
      .sort({ effectiveDate: -1 })
      .limit(limit)
      .lean().exec() as Promise<SurveyModelPlainWithInitiative[]>;
  }

  public static async findInitiativeLatestSurveys(initiativeIds: ObjectId[]) {
    const latestSurveys: Pick<SurveyModel, '_id'>[] = await Survey.aggregate([
      {
        $match: {
          initiativeId: { $in: initiativeIds },
          type: SurveyType.Default,
          deletedDate: { $exists: false },
        }
      },
      {
        $project: {
          _id: 1,
          initiativeId: 1,
          effectiveDate: 1,
        }
      },
      {
        $sort:{ effectiveDate: -1 }
      },
      {
        $group: {
          _id: '$initiativeId',
          'surveyId': { '$first': '$_id' }
        }
      },
      {
        $project: {
          _id: '$surveyId'
        }
      },
    ]);

    return latestSurveys.map(item => item._id);
  }

  public static async findById(id: string | ObjectId, projection?: ProjectionType<SurveyModelPlain> | null) {
    return Survey.findOne({
      _id: new ObjectId(id),
      deletedDate: { $exists: false },
    }, projection).exec()
  }

  public static async findByIds<T = SurveyModelPlain>(
    ids: string[] | ObjectId[],
    projection?: ProjectionType<T> | null,
    initiativeProjection?: ProjectionType<InitiativeModel>
  ) {
    if (!ids.length) {
      return [];
    }

    return Survey.find(
      {
        _id: { $in: ids.map((id) => new ObjectId(id)) },
        deletedDate: { $exists: false },
      },
      projection
    )
      .populate('initiative', initiativeProjection)
      .lean<T[]>()
      .exec();
  }

  public static async findByIdWithInitiative(id: string | ObjectId, projection?: ProjectionType<SurveyModelPlain> | null): Promise<SurveyWithInitiative | null> {
    return Survey.findOne({
      _id: new ObjectId(id),
      deletedDate: { $exists: false },
    }, projection)
      .populate('initiative')
      .exec() as Promise<SurveyWithInitiative | null>;
  }

  public static async findOne(find: FilterQuery<SurveyModel>, projection?: ProjectionType<SurveyModelPlain> | null) {
    return Survey.findOne({ deletedDate: { $exists: false }, ...find }, projection).exec()
  }

  public static async mustFindById(id: string | ObjectId) {
    if (!ObjectId.isValid(id)) {
      throw new UserError('Survey could not be found', { surveyId: id });
    }

    const survey = await SurveyRepository.findById(id);
    if (!survey) {
      // Downgrade the error as this is happening due to staff activity
      throw new BadRequestError(`Survey was not found. Please go back to the previous page and try again.`);
    }

    return survey;
  }

  public static async getStakeholders(utrvId: string) {
    return Survey.aggregate(stakeholdersAggregation({ _id: new ObjectId(utrvId) }));
  }

  public static async getSurveyStatsByInitiativeId(initiativeIds: ObjectId[]) {
    const match = {
      initiativeId: { $in: initiativeIds },
      type: { $nin: [SurveyType.Materiality] },
    };
    return this.getSurveyStats(match, { visibleValues: 1 });
  }

  public static async getSurveysByInitiativeIds({
    initiativeIds,
    project,
  }: {
    initiativeIds: ObjectId[];
    project?: ProjectionType<SurveyModelPlain>;
  }) {
    const match = {
      initiativeId: { $in: initiativeIds },
      type: SurveyType.Default,
      deletedDate: { $exists: false },
    };
    return Survey.find(match, project);
  }

  public static async getSurveyStatsByUserId(userId: ObjectId, initiativeIds?: ObjectId[]) {
    const match: Record<string, unknown> = {
      visibleStakeholders: userId
    };

    if (initiativeIds) {
      match.initiativeId = { $in: initiativeIds }
    }

    const projection = {
      visibleValues: {
        $filter: {
          input: '$visibleValues',
          as: 'item',
          cond: {
            $or: [
              { $in: [userId, '$$item.stakeholders.stakeholder'] },
              { $in: [userId, '$$item.stakeholders.verifier'] }
            ]
          }
        }
      }
    };

    return this.getSurveyStats(match, projection);
  }

  public static async getSurveyStats(match = {}, projection = {}) {

    interface SurveyStats extends SurveySummary {
      initiative: InitiativeMin;
      assurance: Pick<SurveyModelPlain, 'assurance'>;
      visibleValues: Pick<UniversalTrackerValuePlain, 'status' | 'assuranceStatus'>[];
    }

    const aggregation = [
      {
        $match: {
          ...match,
          deletedDate: { $exists: false },
          sourceName: { $nin: adapterBlueprints }
        }
      },
      {
        $lookup: {
          from: 'universal-tracker-values',
          localField: 'visibleUtrvs',
          foreignField: '_id',
          as: 'visibleValues'
        }
      },
      initiativeLookup,
      {
        $project: {
          ...surveySummary,
          initiative: { $arrayElemAt: ['$initiatives', 0] },
          ...projection
        },
      },
      assurancePortfolioLookUp('_id', 'surveyId'),
      {
        $project: {
          ...surveySummary,
          assurance: 1,
          initiative: {
            ...initiativeMinFields,
            industry: 1,
          },
          visibleValues: { status: 1, assuranceStatus: 1 },
        }
      }
    ];

    const result = await Survey.aggregate<SurveyStats>(aggregation);

    return result.map(survey => ({
      _id: survey._id,
      name: survey.name,
      period: survey.period,
      sourceName: survey.sourceName,
      initiativeId: survey.initiativeId,
      effectiveDate: survey.effectiveDate,
      completedDate: survey.completedDate,
      aggregatedDate: survey.aggregatedDate,
      aggregatedVersion: survey.aggregatedVersion,
      aggregatedVersionMismatch: (survey.aggregatedVersion ?? 0) < SURVEY_AGGREGATOR_VERSION,
      stakeholders: survey.stakeholders,
      initiative: {
        ...survey.initiative,
        industryText: getIndustryText(survey.initiative?.industry)
      },
      assurance: survey.assurance,
      status: survey.visibleValues.reduce((a: { [x: string]: number }, c: Pick<UniversalTrackerValuePlain, 'status' | 'assuranceStatus'>) => {
        a[c.status] += 1;
        if (c.assuranceStatus && assuredUtrvActionStatuses.includes(c.assuranceStatus)) {
          a.assured += 1;
        }
        return a;
      }, { created: 0, updated: 0, verified: 0, rejected: 0, assured: 0 }),
      logo: undefined,
      scorecard: undefined,
      type: survey.type,
      visibleUtrvs: survey.visibleUtrvs,
      permissions: survey.permissions,
    }));
  }

  /**
   * Need to as very efficient
   * Only return default type surveys
   */
  public static async getAggregatedStats(query: BreakdownsQuery) {

    const {
      initiativeIds,
      startDate,
      endDate,
      isCompleted,
    } = query

    const surveyProjection = {
      _id: 1,
      name: 1,
      initiativeId: 1,
      effectiveDate: 1,
      completedDate: 1,
      period: 1,
    }

    const $match: Record<string, unknown> = {
      initiativeId: { $in: initiativeIds },
      ...projectDate({ field: 'effectiveDate', startDate, endDate }),
      type: SurveyType.Default,
      deletedDate: { $exists: false },
    };

    if (typeof isCompleted === 'boolean') {
      $match.completedDate = { $exists: isCompleted };
    }

    const aggregation = [
      { $match },
      {
        $lookup: {
          from: 'universal-tracker-values',
          localField: 'visibleUtrvs',
          foreignField: '_id',
          as: 'visibleValues'
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'creatorId',
          foreignField: '_id',
          as: 'creator'
        }
      },
      {
        $project: {
          ...surveyProjection,
          visibleValues: { status: 1, lastUpdated: 1 },
          creator: { $arrayElemAt: ['$creator', 0] }
        }
      },
      {
        $unwind: {
          path: '$visibleValues', preserveNullAndEmptyArrays: true
        }
      },
      {
        '$project': {
          _id: 1,
          name: 1,
          initiativeId: 1,
          period: 1,
          effectiveDate: 1,
          completedDate: 1,
          lastUpdated: '$visibleValues.lastUpdated',
          creator: { _id: 1, firstName: 1, surname: 1 },
          created: { '$cond': { if: { $eq: ['$visibleValues.status', 'created'] }, then: 1, else: 0 } },
          updated: { '$cond': { if: { $eq: ['$visibleValues.status', 'updated'] }, then: 1, else: 0 } },
          rejected: { '$cond': { if: { $eq: ['$visibleValues.status', 'rejected'] }, then: 1, else: 0 } },
          verified: { '$cond': { if: { $eq: ['$visibleValues.status', 'verified'] }, then: 1, else: 0 } },
        }
      },
      {
        '$group': {
          '_id': '$_id',
          'name': { '$first': '$name' },
          'initiativeId': { '$first': '$initiativeId' },
          'period': { '$first': '$period' },
          'effectiveDate': { '$first': '$effectiveDate' },
          'completedDate': { '$first': '$completedDate' },
          'lastUpdated': { '$max': '$lastUpdated' },
          'creator': { '$first': '$creator' },
          'created': { '$sum': '$created' },
          'updated': { '$sum': '$updated' },
          'rejected': { '$sum': '$rejected' },
          'verified': { '$sum': '$verified' },
        }
      },
      {
        $project: {
          ...surveyProjection,
          lastUpdated: 1,
          creator: 1,
          status: {
            created: '$created',
            updated: '$updated',
            rejected: '$rejected',
            verified: '$verified',
          },
        }
      }
    ];

    return Survey.aggregate(aggregation);
  }

  public static async getInitiativePermissionIds(params: Pick<UserBreakdownQuery, 'user' | 'initiativeIds' | 'initiatives'>) {
    const { user, initiativeIds: currentAndChildrenInitiativeIds, initiatives: currentAndChildrenInitiatives } = params;
    const [currentInitiativeId] = currentAndChildrenInitiativeIds;

    const [initiative] = await InitiativeRepository.getAllParentsById(currentInitiativeId, { _id: 1, parentId: 1 });
    const parentIds = initiative.parents.map(i => i._id);
    const verifyContributeRoles = [...InitiativePermissions.canContributeRoles, ...InitiativePermissions.canVerifyRoles];

    const hasRoleForInitiativeId = (id: ObjectId): boolean => {
      const perm = user.permissions.find((p) => p.initiativeId.equals(id));
      if (!perm) {
        return false;
      }
      return perm.permissions.some((role) => verifyContributeRoles.includes(role));
    };

    if (parentIds.find(i => hasRoleForInitiativeId(i._id))) {
      return currentAndChildrenInitiativeIds;
    }

    const existedInitiativePermissionNodes = currentAndChildrenInitiativeIds.filter(i => hasRoleForInitiativeId(i._id));
    if (!existedInitiativePermissionNodes.length) {
      return [];
    }

    const initiativeTreeService = getInitiativeTreeService();
    const initiativePermissionIds = initiativeTreeService.getAllChildrenFromTreeNodes(currentAndChildrenInitiatives, existedInitiativePermissionNodes);

    return initiativePermissionIds.map(i => new ObjectId(i));
  }

  // This aggregates survey data from direct delegated user and inherited user
  public static async getAggregatedUserSurveyStats(query: UserBreakdownQuery) {

    const { initiatives, initiativeIds, startDate, endDate, isCompleted, user } = query;

    const surveyProjection = {
      _id: 1,
      name: 1,
      initiativeId: 1,
      effectiveDate: 1,
      completedDate: 1,
      period: 1,
      type: 1,
    };

    const initiativePermissionIds = await this.getInitiativePermissionIds({ user, initiativeIds, initiatives });

    const $match: Record<string, unknown> = {
      ...projectDate({ field: 'effectiveDate', startDate, endDate }),
      type: SurveyType.Default,
      deletedDate: { $exists: false },
      $or: [{ initiativeId: { $in: initiativePermissionIds } }, { initiativeId: { $in: initiativeIds } }],
    };

    if (typeof isCompleted === 'boolean') {
      $match.completedDate = { $exists: isCompleted };
    }

    const aggregation = [
      { $match },
      {
        $lookup: {
          from: 'universal-tracker-values',
          localField: 'visibleUtrvs',
          foreignField: '_id',
          as: 'visibleValues',
        },
      },
      {
        $project: {
          ...surveyProjection,
          visibleValues: { status: 1, lastUpdated: 1, stakeholders: 1, initiativeId: 1 },
        },
      },
      {
        $unwind: {
          path: '$visibleValues',
        },
      },
      {
        $match: {
          $expr: {
            $or: [
              { $in: [user._id, '$visibleValues.stakeholders.stakeholder'] },
              { $in: [user._id, '$visibleValues.stakeholders.verifier'] },
              { $in: ['$visibleValues.initiativeId', initiativePermissionIds] },
            ],
          },
        },
      },
      {
        $project: {
          ...surveyProjection,
          lastUpdated: '$visibleValues.lastUpdated',
          created: { $cond: { if: { $eq: ['$visibleValues.status', 'created'] }, then: 1, else: 0 } },
          updated: { $cond: { if: { $eq: ['$visibleValues.status', 'updated'] }, then: 1, else: 0 } },
          rejected: { $cond: { if: { $eq: ['$visibleValues.status', 'rejected'] }, then: 1, else: 0 } },
          verified: { $cond: { if: { $eq: ['$visibleValues.status', 'verified'] }, then: 1, else: 0 } },
        },
      },
      {
        $group: {
          _id: '$_id',
          name: { $first: '$name' },
          initiativeId: { $first: '$initiativeId' },
          period: { $first: '$period' },
          type: { $first: '$type' },
          effectiveDate: { $first: '$effectiveDate' },
          completedDate: { $first: '$completedDate' },
          lastUpdated: { $max: '$lastUpdated' },
          created: { $sum: '$created' },
          updated: { $sum: '$updated' },
          rejected: { $sum: '$rejected' },
          verified: { $sum: '$verified' },
        },
      },
      {
        $lookup: {
          from: 'initiatives',
          localField: 'initiativeId',
          foreignField: '_id',
          as: 'initiatives',
        },
      },
      {
        $project: {
          ...surveyProjection,
          lastUpdated: 1,
          initiativeName: { $arrayElemAt: ['$initiatives.name', 0] },
          status: {
            created: '$created',
            updated: '$updated',
            rejected: '$rejected',
            verified: '$verified',
          },
        },
      },
    ];

    return Survey.aggregate(aggregation);
  }

  public static async getAggregatedUserStatsBySurvey({ initiativeIds, surveyIds }: { initiativeIds: ObjectId[], surveyIds: ObjectId[] }) {

    const $match: Record<string, unknown> = {
      _id: { $in: surveyIds },
      initiativeId: { $in: initiativeIds },
      deletedDate: { $exists: false },
    };

    const aggregation = [
      { $match },
      {
        $lookup: {
          from: 'universal-tracker-values',
          localField: 'visibleUtrvs',
          foreignField: '_id',
          as: 'visibleValues'
        }
      },
      {
        $project: {
          visibleValues: {
            status: 1,
            lastUpdated: 1,
            stakeholders: 1,
          }
        }
      },
      {
        $unwind: {
          path: '$visibleValues'
        }
      },
      {
        '$project': {
          userIds: {
            $setUnion: [
              "$visibleValues.stakeholders.stakeholder",
              "$visibleValues.stakeholders.verifier",
            ]
          },
          stakeholders: "$visibleValues.stakeholders",
          created: { '$cond': { if: { $eq: ['$visibleValues.status', 'created'] }, then: 1, else: 0 } },
          updated: { '$cond': { if: { $eq: ['$visibleValues.status', 'updated'] }, then: 1, else: 0 } },
          rejected: { '$cond': { if: { $eq: ['$visibleValues.status', 'rejected'] }, then: 1, else: 0 } },
          verified: { '$cond': { if: { $eq: ['$visibleValues.status', 'verified'] }, then: 1, else: 0 } },
        }
      },
      {
        $match: {
          userIds: { $ne: [] },
        }
      },
      {
        "$unwind": {
          "path": "$userIds"
        }
      },
      {
        '$group': {
          "_id": "$userIds",
          'created': { '$sum': '$created' },
          'updated': { '$sum': '$updated' },
          'rejected': { '$sum': '$rejected' },
          'verified': { '$sum': '$verified' },
          isStakeholder: { $max: { $in: ['$userIds', '$stakeholders.stakeholder'] } },
          isVerifier: { $max: { $in: ['$userIds', '$stakeholders.verifier'] } },
        }
      },
      {
        $project: {
          status: {
            created: '$created',
            updated: '$updated',
            rejected: '$rejected',
            verified: '$verified',
          },
          isStakeholder: '$isStakeholder',
          isVerifier: '$isVerifier',
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'userData'
        }
      },
      {
        $unwind: {
          path: '$userData'
        }
      },
      {
        $project: {
          status: '$status',
          isContributor: '$isStakeholder',
          isVerifier: '$isVerifier',
          firstName: "$userData.firstName",
          surname: "$userData.surname",
        }
      },
    ];

    return Survey.aggregate(aggregation);
  }


  public static async findAllSurveyUtrvs(
    survey: SurveyModelPlain,
    conditions: { [key: string]: any } = { deleted: { $exists: false } },
    project?: {},
  ) {

    const surveyUtrvIds = [
      ...survey.compositeUtrvs,
      ...survey.fragmentUtrvs,
      ...survey.subFragmentUtrvs,
      ...survey.disabledUtrvs,
      ...survey.visibleUtrvs, // Assume all visibleUtrvs are owned by this survey
    ];

    if (surveyUtrvIds.length === 0) {
      return [];
    }

    const match = {
      ...conditions,
      _id: { $in: surveyUtrvIds },
    };

    return UniversalTrackerValue.find(match, project).exec();
  }

  public static async findSurveyCustomGroups(survey: SurveyModelPlain): Promise<UTrCustomGroup[]> {
    const localGroups = survey.blueprint?.customGroups ?? [];

    // Find groups organization level groups
    const metricGroups = await InitiativeRepository.getInitiativeKpiGroups(survey.initiativeId, MetricGroupType.Custom);
    const orgGroups = await MetricGroupManager.getUtrCustomGroups(metricGroups, survey.initiativeId);

    return [...localGroups, ...orgGroups];
  }

  public static async getInitiativeSurveyUtrvActions(surveyId: ObjectId): Promise<SurveyActionDataAggregation[]> {
    const aggregations: any[] = [
      ...getBaseAggregation(),
    ];

    aggregations.unshift({
      $match: {
        '_id': surveyId,
        deletedDate: { $exists: false }
      }
    });

    aggregations.push({
      $lookup: {
        from: 'value-list',
        localField: 'fragmentUniversalTracker.valueValidation.valueList.listId',
        foreignField: '_id',
        as: 'list',
      },
    });

    aggregations.push({
      $lookup: {
        from: 'surveys',
        localField: 'sourceItems.sourceId',
        foreignField: '_id',
        pipeline: [{ $project: sourceItemsProjection }],
        as: 'aggregatedSurveys',
      },
    });

    aggregations.push({
      $project: {
        ...surveyActionData
      },
    });

    return Survey.aggregate(aggregations);
  }

  // public static getSurveyQuestionByAlternativeCode(survey: SurveyModel, code: string | string[], downloadScope?: DownloadScopeData, project?: {}) {
  public static getSurveyQuestionByAlternativeCode(
    survey: SurveyModel, code: string | string[],
    downloadScope?: DownloadScopeData,
    project?: QuestionMatchProject,
  ) {
    const alternatives = Array.isArray(code) ? code : [code];
    const match: MatchUnknown = {
      $or: alternatives.map(code => [
        {
          [`universalTracker.alternatives.${code}`]: { $exists: true, $ne: [] },
        },
        {
          'universalTracker.type': code
        }
      ]).flat()
    };
    return this.getSurveyQuestionByMatch(survey, downloadScope, match, project);
  }

  public static async getByDownloadMultiScope(
    survey: Pick<SurveyModelPlain, 'visibleUtrvs' | 'initiativeId'>,
    downloadScope: DownloadMultiScope,
    project?: QuestionMatchProject
  ) {
    const match = await DownloadScope.generateMultiScopeMatch(downloadScope, 'universalTracker.')
    return SurveyRepository.getSurveyQuestionByMatch(survey, downloadScope, match, project);
  }

  public static async getSurveyQuestionByMatch(
    survey: Pick<SurveyModelPlain, 'visibleUtrvs' | 'initiativeId'>,
    downloadScope?: Pick<DownloadMultiScope, 'statuses' | 'assuranceStatus' | 'visibilityStatus' | 'displayMetricOverrides'>,
    match?: MatchUnknown,
    project?: QuestionMatchProject,
  ) {
    const initiativeUtrService = getInitiativeUniversalTrackerService();
    const pipelineStagesOnOverride = downloadScope?.displayMetricOverrides
      ? await initiativeUtrService.getPipelineStagesOnOverride(survey.initiativeId)
      : undefined;

    const aggregateMatch: MatchUnknown = {
      _id: { $in: survey.visibleUtrvs },
      ...excludeSoftDeleted(),
    }

    // @TODO should really just filter all the time, already caused bugs
    if (!downloadScope?.statuses) {
      aggregateMatch.status = ActionList.Verified;
    } else if (downloadScope.statuses.length > 0) {
      aggregateMatch.status = { $in: downloadScope.statuses };
    }

    if (downloadScope?.assuranceStatus && downloadScope.assuranceStatus.length > 0) {
      aggregateMatch.assuranceStatus = { $in: downloadScope?.assuranceStatus };
    }

    const visibilityStatus = downloadScope?.visibilityStatus;
    return SurveyRepository.aggregateQuestionMatch({
      aggregateMatch: applyVisibilityFilter(aggregateMatch, visibilityStatus),
      project,
      match,
      visibilityStatus,
      pipelineStagesOnOverride,
    });
  }

  private static aggregateQuestionMatch({
    aggregateMatch,
    project,
    match,
    visibilityStatus,
    pipelineStagesOnOverride
  }: {
    aggregateMatch: MatchUnknown;
    project?: QuestionMatchProject;
    match?: MatchUnknown;
    visibilityStatus?: DownloadScopeData['visibilityStatus'];
    pipelineStagesOnOverride?: PipelineStagesOnOverrideType;
  }): Promise<any[]> {

    const baseProject = {
      _id: 1,
      value: 1,
      note: 1,
      effectiveDate: 1,
      valueData: 1,
      status: 1,
      evidence: 1,
      valueType: 1,
      isPrivate: 1,
      assuranceStatus: 1,
    }
    const { isPrivate, ...firstMatch } = aggregateMatch;

    // query utrvs based on aggregateMatch except isPrivate
    const aggregate: PipelineStage[] = [{ $match: firstMatch }];

    // override isPrivate
    if (pipelineStagesOnOverride) {
      const { initiativeUtrLookupAndUnwind, initiativeUtrOverrides } = pipelineStagesOnOverride;

      aggregate.push(...initiativeUtrLookupAndUnwind, {
        $project: {
          ...baseProject,
          ...project,
          universalTrackerId: 1,
          ...initiativeUtrOverrides,
        },
      });
    }
    // filter utrvs based on isPrivate
    if (isPrivate) {
      aggregate.push({
        $match: { isPrivate },
      });
    }

    aggregate.push(
      universalTrackerLookup,
      {
        $project: {
          ...baseProject,
          universalTrackerId: 1, // Keep it for potential third $match
          universalTracker: { $arrayElemAt: ['$universalTracker', 0] },
          ...project,
        },
      },
      {
        $lookup: {
          from: 'value-list',
          localField: 'universalTracker.valueValidation.valueList.listId',
          foreignField: '_id',
          as: 'valueList',
        },
      },
      {
        $lookup: {
          from: 'value-list',
          localField: 'universalTracker.valueValidation.table.columns.listId',
          foreignField: '_id',
          as: 'tableValueList',
        },
      }
    );

    if (match) {
      aggregate.push({ $match: match });
    }

    aggregate.push(
      {
        $project: {
          ...baseProject,
          valueList: 1,
          tableValueList: 1,
          universalTracker: {
            typeTags: 1, // Need to populate subgroups
            tags: 1,
            alternatives: 1,
            ...universalTrackerFields,
            ...valueValidationProjection
          },
          ...applyVisibilityProject(project ?? {}, visibilityStatus)
        }
      }
    );

    return UniversalTrackerValue.aggregate(aggregate).exec();
  }

  public static async getHistoricalReportData({ survey, downloadScope, reportProject }: HistoricalReportDataParams) {
    const reportData = await SurveyRepository.getByDownloadMultiScope(survey, downloadScope, reportProject);
    const preferredTypes = downloadScope.scope.standards ?? []
    reportData.sort(
      (a, b) =>
        naturalSort(
          a.universalTracker.typeCode ? String(a.universalTracker.typeCode) : '',
          b.universalTracker.typeCode ? String(b.universalTracker.typeCode) : ''
        ) || naturalSort(getPreferredAltName(a, preferredTypes), getPreferredAltName(b, preferredTypes))
    );
    const data = [{
      reportData: reportData,
      effectiveDate: survey.effectiveDate,
        unitConfig: survey.unitConfig,
    }];

    // get previous surveys
    const previousSurveys = await SurveyRepository.findLatestSurveys(
      {
        _id: { $ne: survey._id },
        initiativeId: survey.initiativeId,
        effectiveDate: { $lt: survey.effectiveDate },
        deletedDate: { $exists: false },
        type: SurveyType.Default,
      },
      { effectiveDate: 1, visibleUtrvs: 1, unitConfig: 1, initiativeId: 1 },
      2
    );

    for (const previousSurvey of previousSurveys) {
      data.push({
        effectiveDate: previousSurvey.effectiveDate,
        unitConfig: previousSurvey.unitConfig,
        reportData: await SurveyRepository.getByDownloadMultiScope(previousSurvey, downloadScope, reportProject)
      });
    }

    return data;
  }


  /**
   * Create public version of survey data with visible utrvs data
   * @param survey
   */
  public static async getPublicSurveyQuestions(survey: SurveyModel): Promise<SurveyPublic> {
    const aggregate: any[] = [
      {
        $match: {
          _id: { $in: survey.visibleUtrvs },
          ...excludeSoftDeleted(),
        }
      },
      universalTrackerLookup,
      {
        $project: {
          ...universalTrackerValuePublicFields,
          universalTracker: { $arrayElemAt: ['$universalTracker', 0] }
        }
      },
      {
        $lookup: {
          from: 'value-list',
          localField: 'universalTracker.valueValidation.valueList.listId',
          foreignField: '_id',
          as: 'list'
        }
      },
      {
        $project: {
          ...universalTrackerValuePublicFields,
          universalTracker: universalTrackerPublicFields
        }
      },

    ];

    const universalTrackerValues = await UniversalTrackerValue.aggregate(aggregate);
    await survey.populate('initiative');

    return {
      code: survey.code,
      effectiveDate: survey.effectiveDate,
      initiativeId: String(survey.initiativeId),
      name: survey.initiative?.name ?? survey.name,
      roles: survey.roles,
      stakeholders: survey.stakeholders,
      unitConfig: survey.unitConfig as unknown as UnitConfigMap,
      _id: String(survey._id),
      universalTrackerValues
    }
  }

  public static async getSurveyData(surveyId: string, user: UserPlain, domainOrigin?: string): Promise<SurveyActionData | undefined> {

    const survey = await Survey.findById(surveyId, toCheckPermissionSurveyProjection)
      .lean<ToCheckPermissionSurvey>()
      .exec();

    if (!survey || survey.deletedDate) {
      return undefined;
    }

    const canAccess = await SurveyPermissions.canAccess(survey, user);
    if (!canAccess) {
      throw new PermissionDeniedError();
    }

    const [surveyData] = await SurveyRepository.getInitiativeSurveyUtrvActions(new ObjectId(surveyId));
    if (!surveyData) {
      throw new UserError(`${SURVEY.CAPITALIZED_SINGULAR} not found ${surveyId}`);
    }

    if (surveyData.fragmentUniversalTrackerValues &&
      !await SurveyPermissions.canAccessAllData(survey, user)
    ) {
      const userId = String(user._id);
      const hasUserId = (id: ObjectId) => String(id) === userId;
      surveyData.fragmentUniversalTrackerValues = surveyData.fragmentUniversalTrackerValues
        .filter((utrv) => {
          const { stakeholder, verifier } = utrv.stakeholders as StakeholderGroup;
          return stakeholder.some(hasUserId) || verifier.some(hasUserId);
        });
    }

    return this.expandSurveyData(surveyData, user, domainOrigin);
  }

  public static async getAssuranceSurveyData(assurance: AssurancePortfolioExpandedExtra, domainOrigin?: string): Promise<SurveyActionData | undefined> {
    const survey = assurance.survey;
    if (!survey) {
      return undefined;
    }

    if (survey.deletedDate) {
      throw new UserError(`The associated survey has been removed`, { status: 404 });
    }

    const surveyId = survey._id;
    const [surveyData] = await SurveyRepository.getInitiativeSurveyUtrvActions(new ObjectId(surveyId));
    if (!surveyData) {
      throw new UserError(`${SURVEY.CAPITALIZED_SINGULAR} not found ${surveyId}`);
    }

    const utrvIds = assurance.universalTrackerValueAssurances.map(v => String(v.utrvId));
    surveyData.fragmentUniversalTrackerValues = surveyData
      .fragmentUniversalTrackerValues?.filter((utrv) => utrvIds.includes(String(utrv._id)));

    return this.expandSurveyData(surveyData, undefined, domainOrigin);
  }

  public static async getDataShareSurveyData(
    combinedScope: CombinedDataScopeAccess,
    surveyId: string,
    initiativeId: string,
    domainOrigin?: string
  ): Promise<SurveyActionData | undefined> {
    const [surveyData] = await SurveyRepository.getInitiativeSurveyUtrvActions(new ObjectId(surveyId));
    if (!surveyData) {
      throw new UserError(`${SURVEY.CAPITALIZED_SINGULAR} not found ${surveyId}`, { surveyId, initiativeId });
    }

    if (initiativeId !== String(surveyData.initiativeId)) {
      throw PermissionDeniedError.withContext({
        debugMessage: `Trying to access survey for different initiativeId`,
        surveyId,
        initiativeId,
      });
    }

    surveyData.fragmentUniversalTrackerValues = await DataSharePermissions.getDataShareUtrvs({
      combinedScope,
      surveyData,
    });

    return this.expandSurveyData(surveyData, undefined, domainOrigin);
  }

  public static generateScopeUpdates(
    customMetricGroups: MetricGroupPlain[],
    blueprint: Blueprint,
    defaultBlueprint: Blueprint
  ) {
    const customMetricGroupsMap = new Map(
      customMetricGroups.map((customGroup) => [customGroup._id.toString(), customGroup])
    );
    const surveyBlueprintDate = blueprint.date;
    const defaultBlueprintDate = defaultBlueprint.date;
    const scopeUpdates: ScopeUpdate[] = [];
    // compare the dates for sdg
    if (hasChange(surveyBlueprintDate, defaultBlueprintDate)) {
      scopeUpdates.push({ type: GroupType.Custom, name: 'Standards And Frameworks', added: [], removed: [] });
    }
    // differentiate old and new utrs for custom metric
    const surveyCustomMetrics = blueprint.forms.filter((g) => g.utrGroupConfig?.type === 'group');

    surveyCustomMetrics.forEach((surveyCustomMetric) => {
      const { groupId, groupName = '', utrCodes: currentUtrCodes = [] } = surveyCustomMetric.utrGroupConfig ?? {};
      if (groupId) {
        const updatedMetricUtrs = customMetricGroupsMap.get(groupId.toString())
          ?.universalTracker
          ?.map((utr) => utr.code) ?? [];

        const added = updatedMetricUtrs.filter((updated) => !currentUtrCodes.includes(updated));
        const removed = currentUtrCodes.filter((current) => !updatedMetricUtrs.includes(current));
        if (added.length || removed.length) {
          scopeUpdates.push({ type: GroupType.Group, name: groupName ?? '', added, removed });
        }
      }
    });
    return scopeUpdates;
  }

  private static async expandSurveyData(
    surveyData: SurveyActionDataAggregation,
    user: UserPlain | undefined,
    domainOrigin?: string
  ): Promise<SurveyActionData> {
    const [initiative] = surveyData.initiatives;
    if (!initiative) {
      throw new UserError('Reporting level not found');
    }

    const blueprintRepo = getBlueprintRepository();
    const defaultBlueprint = await blueprintRepo.getBlueprint(surveyData.sourceName);
    const blueprint = surveyData.blueprint ?? defaultBlueprint;
    if (!blueprint || !defaultBlueprint) {
      throw new UserError(`Invalid blueprint for survey ${surveyData._id}`);
    }

    const blueprintCode = surveyData.sourceName as Blueprints;
    const bc = getBluePrintContribution();
    if (surveyData.fragmentUniversalTracker) {
      // Populate virtual tags property
      await bc.populateSdgTagsProperty(blueprintCode, surveyData.fragmentUniversalTracker);
    }

    const expandedBlueprint = await blueprintRepo.getExpandedBlueprint(blueprint);
    const questionGroups = SurveyCalculator.createUtrGroupQuestions(
      surveyData,
      expandedBlueprint.forms,
    );

    const customMetricGroups = await InitiativeRepository.getInitiativeKpiGroups(initiative._id, MetricGroupType.Custom);
    let scopeUpdates: ScopeUpdate[] = [];

    // Only for default surveys, if not completed, generate scope updates
    if (!surveyData.completedDate && surveyData.type === SurveyType.Default) {
      if (user && await SurveyPermissions.canManage(surveyData, user)) {
        scopeUpdates = this.generateScopeUpdates(customMetricGroups, blueprint, defaultBlueprint);
      }
    }

    delete surveyData.fragmentUniversalTracker;
    delete surveyData.blueprint;

    const rootInitiativeService = getRootInitiativeService();
    const config = await rootInitiativeService.getConfig(initiative, { domain: domainOrigin });
    const utrSortingConfigMap = getUtrSortingConfigMapByScope(surveyData.scope);

    return {
      ...surveyData,
      scopeConfig: config.survey.scope,
      config: blueprint,
      questionGroups,
      contributions: await bc.getContributions(blueprintCode),
      customMetricGroups,
      scopeUpdates,
      utrSortingConfigMap
    };
  }

  public static getMetricsProjection(metrics: string[], customMetricsUtrs: Record<string, ObjectId[]>): Record<string, object> {
    return {
      typeProjection: metrics.reduce(
        (a, metric) => ({
          ...a,
          [`is_${metric}`]: {
            $cond: {
              if: {
                $or: [
                  { $eq: ['$visibleUtrvs.universalTracker.type', metric] },
                  { $ifNull: [`$visibleUtrvs.universalTracker.alternatives.${metric}`, false] },
                  {
                    $cond: {
                      if: { $isArray: `$visibleUtrvs.universalTracker.tags.${metric}` },
                      then: { $gt: [{ $size: `$visibleUtrvs.universalTracker.tags.${metric}` }, 0] },
                      else: false,
                    },
                  },
                  { $in: [`$visibleUtrvs.universalTracker._id`, customMetricsUtrs[metric] ?? []] },
                ],
              },
              then: 1,
              else: 0,
            },
          },
        }),
        {}
      ),
      statsProjection: metrics.reduce(
        (a, metric) => ({
          ...a,
          [`is_${metric}`]: 1,
          [`isPrivate_${metric}`]: {
            $cond: { if: { $and: [{ $eq: ['$isPrivate', 1] }, { $eq: [`$is_${metric}`, 1] }] }, then: 1, else: 0 },
          },
          [`isNa_${metric}`]: {
            $cond: { if: { $and: [{ $eq: ['$isNa', 1] }, { $eq: [`$is_${metric}`, 1] }] }, then: 1, else: 0 },
          },
          [`isNr_${metric}`]: {
            $cond: { if: { $and: [{ $eq: ['$isNr', 1] }, { $eq: [`$is_${metric}`, 1] }] }, then: 1, else: 0 },
          },
          [`isAnswered_${metric}`]: {
            $cond: { if: { $and: [{ $eq: ['$isAnswered', 1] }, { $eq: [`$is_${metric}`, 1] }] }, then: 1, else: 0 },
          },
        }),
        {}
      ),
      totalProjection: metrics.reduce(
        (a, metric) => ({
          ...a,
          [`total_${metric}`]: { $sum: `$is_${metric}` },
          [`private_${metric}`]: { $sum: `$isPrivate_${metric}` },
          [`na_${metric}`]: { $sum: `$isNa_${metric}` },
          [`nr_${metric}`]: { $sum: `$isNr_${metric}` },
          [`answered_${metric}`]: { $sum: `$isAnswered_${metric}` },
        }),
        {}
      ),
      formatProjection: metrics.reduce(
        (a, metric) => ({
          ...a,
          [`${metric}`]: {
            total: `$total_${metric}`,
            private: `$private_${metric}`,
            na: `$na_${metric}`,
            nr: `$nr_${metric}`,
            answered: `$answered_${metric}`,
          },
        }),
        {}
      ),
    };
  }

  public static async getPortfolioExchangeSurveyData(query: PortfolioExchangeQuery) {
    const {
      initiativeIds,
      startDate,
      endDate,
      period,
      metrics = [],
      customMetricGroups = []
    } = query

    const surveyProjection = {
      _id: 1,
      name: 1,
      initiativeId: 1,
      effectiveDate: 1,
      completedDate: 1,
      period: 1,
    }
    const customMetricsUtrs = customMetricGroups.reduce((acc, g) => {
      acc[g._id.toString()] = g.universalTrackers.map(u => u._id);
      return acc;
    }, {} as Record<string, ObjectId[]>)
    const metricsProjection = this.getMetricsProjection(metrics, customMetricsUtrs);
    const $match: Record<string, unknown> = {
      initiativeId: { $in: initiativeIds },
      ...projectDate({ field: 'effectiveDate', startDate, endDate }),
      // type: { $ne: SurveyType.Aggregation }, // Want to let ABS see aggregated surveys. Questionable impact...
      completedDate: { $exists: true },
      deletedDate: { $exists: false },
    };
    if (period) {
      $match.period = { $eq: period };
    }

    const aggregation: PipelineStage[] = [
      { $match },
      {
        $lookup: {
          from: 'universal-tracker-values',
          localField: 'visibleUtrvs',
          foreignField: '_id',
          as: 'visibleUtrvs'
        }
      },
      {
        $unwind: '$visibleUtrvs'
      },
      {
        $lookup: {
          from: 'universal-trackers',
          localField: 'visibleUtrvs.universalTrackerId',
          foreignField: '_id',
          as: 'visibleUtrvs.universalTracker'
        }
      },
      {
        $unwind: '$visibleUtrvs.universalTracker'
      },
      {
        $project: { ...surveyProjection, visibleUtrvs: { lastUpdated: 1, status: 1, value: 1, valueData: 1, isPrivate: 1, universalTracker: { _id: 1, type: 1, alternatives: 1, tags: 1 } } }
      },
      {
        $project: {
          ...surveyProjection,
          visibleUtrvs: 1,
          isPrivate: { '$cond': { if: { $eq: ['$visibleUtrvs.isPrivate', true] }, then: 1, else: 0 } },
          isNa: { '$cond': { if: { $eq: ['$visibleUtrvs.valueData.notApplicableType', 'not_applicable'] }, then: 1, else: 0 } },
          isNr: { '$cond': { if: { $eq: ['$visibleUtrvs.valueData.notApplicableType', 'not_reported'] }, then: 1, else: 0 } },
          isAnswered: { '$cond': { if: { $or: [{ $eq: ['$visibleUtrvs.status', 'updated'] }, { $eq: ['$visibleUtrvs.status', 'verified'] }] }, then: 1, else: 0 } },
          ...metricsProjection.typeProjection
        }
      },
      {
        $project: {
          ...surveyProjection,
          visibleUtrvs: 1,
          isPrivate: 1,
          isNa: 1,
          isNr: 1,
          isAnswered: 1,
          ...metricsProjection.statsProjection
        }
      },
      {
        $group: {
          '_id': '$_id',
          'name': { '$first': '$name' },
          'initiativeId': { '$first': '$initiativeId' },
          'period': { '$first': '$period' },
          'effectiveDate': { '$first': '$effectiveDate' },
          'completedDate': { '$first': '$completedDate' },
          'private': { '$sum': '$isPrivate' },
          'na': { '$sum': '$isNa' },
          'nr': { '$sum': '$isNr' },
          'answered': { '$sum': '$isAnswered' },
          total: { $sum: 1 },
          ...metricsProjection.totalProjection
        }
      },
      {
        $project: {
          ...surveyProjection,
          private: 1,
          na: 1,
          nr: 1,
          answered: 1,
          total: 1,
          ...metricsProjection.formatProjection
        }
      },
      {
        $sort: {
          'completedDate': -1
        }
      },
      {
        $group: {
          '_id': '$initiativeId',
          'surveys': {
            $push: '$$ROOT'
          }
        }
      }
    ];

    const exchangeSurveys = (await Survey.aggregate(aggregation)
    .exec()) as { _id: ObjectId; surveys: unknown[] }[];

    return exchangeSurveys.reduce((result, current) => {
      result[current._id.toString()] = current.surveys
      return result
    }, {} as Record<string, unknown[]>);
  }

  public static async getLatestSurveyByInitiativeIds(
    query: Pick<PortfolioExchangeQuery, 'initiativeIds' | 'startDate' | 'endDate' | 'period'>
  ) {
    const { initiativeIds, startDate, endDate, period } = query;

    const surveyProjection: KeysEnum<PortfolioLatestSurvey> = {
      _id: 1,
      initiativeId: 1,
      effectiveDate: 1,
      completedDate: 1,
      period: 1,
    };

    const $match: Record<string, unknown> = {
      initiativeId: { $in: initiativeIds },
      ...projectDate({ field: 'effectiveDate', startDate, endDate }),
      deletedDate: { $exists: false },
      ...(period ? { period: { $eq: period } } : {}),
    };

    const surveys = await Survey.aggregate<{ initiativeId: ObjectId; survey: PortfolioLatestSurvey }>([
      { $match: $match },
      { $sort: { effectiveDate: -1 } },
      {
        $group: {
          _id: '$initiativeId',
          survey: { $first: '$$ROOT' },
        },
      },
      {
        $project: {
          _id: 0,
          initiativeId: '$_id',
          survey: surveyProjection,
        },
      },
    ]).exec();

    const initiativeLatestSurveyMap = new Map<string, PortfolioLatestSurvey>();

    surveys.forEach(({ initiativeId, survey }) => {
      initiativeLatestSurveyMap.set(initiativeId.toString(), survey);
    });

    return initiativeLatestSurveyMap;
  }

  public static async getBenchmarkingLatestSurveys(query: BenchmarkingLatestSurveysQuery): Promise<ExchangeSurvey[]> {
    const {
      initiativeId,
      startDate,
      endDate,
      surveyPacks,
      utrIds,
      dateField = 'effectiveDate',
    } = query

    const surveyProjection = {
      _id: 1,
      name: 1,
      initiativeId: 1,
      effectiveDate: 1,
      completedDate: 1,
      period: 1,
      scope: 1,
    }

    const $match: Record<string, unknown> = {
      initiativeId: { $eq: initiativeId },
      ...projectDate({ field: dateField, startDate, endDate }),
      ...SurveyQueryHelper.fromStrings(surveyPacks),
      deletedDate: { $exists: false },
    };

    const aggregation = [
      { $match },
      {
        $lookup: {
          from: 'universal-tracker-values',
          localField: 'visibleUtrvs',
          foreignField: '_id',
          as: 'visibleUtrvs'
        }
      },
      {
        $unwind: '$visibleUtrvs'
      },
      ...(utrIds ? [{ $match: { 'visibleUtrvs.universalTrackerId': { $in: utrIds } } }] : []),
      {
        $project: {
          ...surveyProjection,
          visibleUtrvs: { lastUpdated: 1, status: 1, value: 1, valueData: 1, isPrivate: 1 }
        }
      },
      {
        '$project': {
          ...surveyProjection,
          visibleUtrvs: 1,
          isPrivate: { '$cond': { if: { $eq: ['$visibleUtrvs.isPrivate', true] }, then: 1, else: 0 } },
          isNa: { '$cond': { if: { $eq: ['$visibleUtrvs.valueData.notApplicableType', 'not_applicable'] }, then: 1, else: 0 } },
          isNr: { '$cond': { if: { $eq: ['$visibleUtrvs.valueData.notApplicableType', 'not_reported'] }, then: 1, else: 0 } },
          isAnswered: { '$cond': { if: { $or: [{ $eq: ['$visibleUtrvs.status', 'updated'] }, { $eq: ['$visibleUtrvs.status', 'verified'] }] }, then: 1, else: 0 } },
        }
      },
      {
        '$group': {
          '_id': '$_id',
          'name': { '$first': '$name' },
          'initiativeId': { '$first': '$initiativeId' },
          'period': { '$first': '$period' },
          'effectiveDate': { '$first': '$effectiveDate' },
          'completedDate': { '$first': '$completedDate' },
          'scope': { '$first': '$scope' },
          'private': { '$sum': '$isPrivate' },
          'na': { '$sum': '$isNa' },
          'nr': { '$sum': '$isNr' },
          'answered': { '$sum': '$isAnswered' },
          total: { $sum: 1 },
        }
      },
    ];

    const surveys = await Survey.aggregate(aggregation).sort({ effectiveDate: -1 });
    return surveys;
  }

  public static async getBulkDelegationSurveys(bulkDelegateSurveyQueries: BulkDelegateSurveyQuery): Promise<BulkDelegateSurvey[]> {
    const { datePeriods, initiativeIds } = bulkDelegateSurveyQueries;
    const aggregate: PipelineStage[] = [
      {
        $match: {
          initiativeId: { $in: initiativeIds.map((initiativeId) => new ObjectId(initiativeId)) },
          deletedDate: { $exists: false },
          type: SurveyType.Default,
          $expr: {
            $or: datePeriods.map((datePeriod) => ({
              $and: [
                { $eq: ['$period', datePeriod.period] }, // do we need to check if we don't have period by default?
                { $gte: ['$effectiveDate', moment(datePeriod.effectiveDate).startOf('month').toDate()] },
                { $lte: ['$effectiveDate', moment(datePeriod.effectiveDate).endOf('month').toDate()] },
              ],
            })),
          },
        },
      },
      {
        $project: {
          initiativeId: 1,
          effectiveDate: 1,
          _id: 1,
          scope: 1,
        },
      },
    ];
    return Survey.aggregate(aggregate).exec();
  }

  public static async getInitiativeSurveyPeriods(initiativeIds: string[] | ObjectId[], surveyType: SurveyType): Promise<DataPeriods[]> {
    const aggregate: PipelineStage[] = [
      {
        $match: {
          initiativeId: { $in: initiativeIds },
          deletedDate: { $exists: false },
          type: surveyType,
        },
      },
      {
        $group: {
          _id: '$period',
        },
      },
    ];

    const result = await Survey.aggregate(aggregate).exec();
    return result.map(period => period._id);
  }

  public static async countSurveys(initiativeIds: (ObjectId | string)[]): Promise<number> {
    // count all type of surveys, including auto-aggregated & materiality
    const match = {
      initiativeId: { $in: initiativeIds },
      ...excludeSoftDeleted(),
    };
    return Survey.countDocuments(match).exec();
  }
}
