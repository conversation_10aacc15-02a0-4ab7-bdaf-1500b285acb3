/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import UniversalTrackerValue, {
  UniversalTrackerValueExtended,
  UniversalTrackerValueModel,
  UniversalTrackerValuePlain,
} from '../models/universalTrackerValue';
import { ObjectId } from 'bson';
import { getCompositeData } from "../service/utr/utrvUtil";
import {
  universalTrackerFields,
  universalTrackerValuePlainFields,
  valueValidationProjection
} from './projections';
import { excludeSoftDeleted } from './aggregations';

export interface CompositeUtrRepositoryInterface {
  fetchCompositeUtrvData(utrv: Pick<UniversalTrackerValuePlain, '_id' | 'compositeData' | 'sourceType'>, blueprint?: string): Promise<UniversalTrackerValueExtended[]>;

  getCompositeByFragmentId(id: ObjectId, initiativeId?: ObjectId): Promise<UniversalTrackerValueExtended[]>;

  getCompositeByFragmentIds(ids: ObjectId[], initiativeId?: ObjectId): Promise<UniversalTrackerValueExtended[]>;

  getCompositeById(id: string | ObjectId): Promise<UniversalTrackerValueModel | null>;
}

export class CompositeUtrRepository implements CompositeUtrRepositoryInterface {

  public async fetchCompositeUtrvData(utrv: Pick<UniversalTrackerValuePlain, '_id' | 'compositeData' | 'sourceType'>, blueprint?: string) {

    let utrvIds: ObjectId[] = [utrv._id];
    const compositeData = getCompositeData(utrv, blueprint);
    if (compositeData && Array.isArray(compositeData.fragmentUtrvs)) {
      utrvIds = utrvIds.concat(compositeData.fragmentUtrvs);
    }

    return UniversalTrackerValue.aggregate([
      {
        $match: {
          ...excludeSoftDeleted(),
          '_id': { $in: utrvIds }
        }
      },
      {
        $lookup: {
          from: 'universal-trackers',
          localField: 'universalTrackerId',
          foreignField: '_id',
          as: 'universalTracker'
        }
      },
      {
        $project: {
          ...universalTrackerValuePlainFields,
          universalTracker: {
            $arrayElemAt: [ '$universalTracker', 0 ]
          }
        }
      },
      {
        $lookup: {
          from: 'value-list',
          localField: 'universalTracker.valueValidation.valueList.listId',
          foreignField: '_id',
          as: 'list'
        }
      },
      {
        $project: {
          ...universalTrackerValuePlainFields,
          universalTracker: {
            ...universalTrackerFields,
            ...valueValidationProjection,
          },
        }
      },

    ]);
  }

  public async getCompositeByFragmentId(id: ObjectId, initiativeId?: ObjectId) {
    const $match: any = {
      $or: [
        { 'compositeData.fragmentUtrvs': id, deletedDate: { $exists: false } },
        { 'compositeData.secondary.fragmentUtrvs': id, deletedDate: { $exists: false } },
      ]
    };

    if (initiativeId) {
      $match['initiativeId'] = initiativeId;
    }

    return UniversalTrackerValue.aggregate([
      {
        $match: $match
      },
      {
        $lookup: {
          from: 'universal-trackers',
          localField: 'universalTrackerId',
          foreignField: '_id',
          as: 'universalTracker'
        }
      },
    ]);
  }

  public async getCompositeByFragmentIds(ids: ObjectId[], initiativeId?: ObjectId) {
    const $match: any = {
      ...excludeSoftDeleted(),
      $or: [
        { 'compositeData.fragmentUtrvs': { $in: ids } },
        { 'compositeData.secondary.fragmentUtrvs': { $in: ids } },
      ]
    };

    if (initiativeId) {
      $match['initiativeId'] = initiativeId;
    }

    return UniversalTrackerValue.aggregate([
      {
        $match: $match
      },
      {
        $lookup: {
          from: 'universal-trackers',
          localField: 'universalTrackerId',
          foreignField: '_id',
          as: 'universalTracker'
        }
      },
    ]);
  }

  public async getCompositeById(id: string | ObjectId) {
    return UniversalTrackerValue.findById(id).exec();
  }
}

export const createCompositeUtrRepository = () => {
  return new CompositeUtrRepository();
};
