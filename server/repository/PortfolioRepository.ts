import { ObjectId } from 'bson';
import Survey, { Scope } from '../models/survey';
import { ActionList, DataPeriods } from '../service/utr/constants';
import UniversalTracker from '../models/universalTracker';
import { getBlueprintRepository } from './BlueprintRepository';
import { DefaultBlueprintCode } from '../survey/blueprints';
import { extractVisibleUtrCodes } from '../survey/surveyForms';
import { DownloadScope } from '../service/survey/scope/downloadScope';
import { UtrValueType } from '../models/public/universalTrackerType';
import { FilterOptions } from '../service/portfolio/PortfolioPackUsageService';
import { projectDate } from '../util/date';
import PermissionDeniedError from '../error/PermissionDeniedError';
import { DataScopeAccess } from "../models/dataShare";
import { SurveyQueryHelper } from "../service/survey/SurveyQueryHelper";
import { RequestScope } from '../service/survey/model/DelegationScope';
import { UtrMatchFilters } from "../types/universalTracker";

export interface SubmissionInsightsQuery<T = string> {
  view: SubmissionInsightsView;
  isCompleted: boolean | string;
  scopeGroup?: RequestScope;
  startDate?: T;
  endDate?: T;
}

interface SubmissionInsightExpanded extends Omit<SubmissionInsightsQuery<Date>, 'scopeGroup'> {
  initiativeIds: ObjectId[];
  utrIds: ObjectId[];
  scope: Scope<string>;
}

export enum SubmissionInsightsView {
  Sector = 'sector',
  Company = 'company',
}

export interface UtrvInsightData {
  total: number,
  average: number,
  count: number,
  universalTrackerId: ObjectId,
}

export interface InsightsData {
  _id: string;
  utrvs: UtrvInsightData[],
}


export class PortfolioRepository {

  /**
   * It will first match on survey scope, therefore only full scope is matching
   * { standards: ['gri], custom: [groupIdOne, groupIdTwo] ... } etc.
   *
   * @Note: Not *subgroups* matching is supported here, { standards: ['gri-102'] }
   * will not work, and might give not fully completed results for now.
   *
   * Must pass utrIds utr ids to sub filtering for survey utrvs
   */
  public static async getPortfolioSurveys(filterOptions: FilterOptions, utrIds: ObjectId[]) {
    const { startDate, endDate, initiativeIds, validatedScope } = filterOptions;

    return Survey.aggregate([
      {
        $match: {
          initiativeId: { $in: initiativeIds },
          ...projectDate({ field: 'effectiveDate', startDate, endDate }),
          completedDate: { $exists: true },
          deletedDate: { $exists: false },
          ...SurveyQueryHelper.toScopeMatch(validatedScope.scope),
        },
      },
      {
        $lookup: {
          from: 'universal-tracker-values',
          localField: 'visibleUtrvs',
          foreignField: '_id',
          as: 'utrvs',
        },
      },
      {
        $unwind: '$utrvs',
      },
      {
        $match: {
          'utrvs.universalTrackerId': { $in: utrIds },
        },
      },
      {
        $group: {
          _id: '$_id',
          scope: { $first: '$scope' },
          initiativeId: { $first: '$initiativeId' },
          utrvs: { $push: '$utrvs' },
        },
      },
    ]).exec();
  }

  /** selectedScope must be passed in and is not empty at this point **/
  public static async getSubmissionInsightsUtrs(scope: Scope<string>, ptCustomMetricGroups: string[]) {
    const blueprintRepo = getBlueprintRepository();
    const blueprint = await blueprintRepo.mustFindExpandedByCode(DefaultBlueprintCode);
    const utrCodes = extractVisibleUtrCodes(blueprint);
    const metricGroups = scope.custom;
    if (metricGroups.some((groupId: string) => !ptCustomMetricGroups.includes(groupId))) {
      throw new PermissionDeniedError(`Selected scope is not allowed`);
    }

    const multiScope = await DownloadScope.generateMultiScopeMatch(
      {
        access: DataScopeAccess.Partial, // Must match by at least one?
        scope,
      },
      '',
      '_id'
    );

    return UniversalTracker.find({
      code: { $in: utrCodes },
      valueType: UtrValueType.Number,
      ...multiScope,
    }).lean();
  }

  public static async getSubmissionInsightsData({
    view = SubmissionInsightsView.Company,
    scope,
    initiativeIds,
    startDate,
    endDate,
    utrIds,
  }: SubmissionInsightExpanded): Promise<InsightsData[]> {
    const aggregation = [
      {
        $match: {
          ...SurveyQueryHelper.toScopeMatch(scope),
          period: { $eq: DataPeriods.Yearly },
          initiativeId: { $in: initiativeIds },
          completedDate: { $exists: true },
          ...projectDate({ field: 'effectiveDate', startDate, endDate }),
          deletedDate: { $exists: false },
        },
      },
      {
        $lookup: {
          from: 'universal-tracker-values',
          localField: 'visibleUtrvs',
          foreignField: '_id',
          as: 'utrvs',
        },
      },
      {
        $project: {
          utrvs: {
            _id: 1,
            universalTrackerId: 1,
            status: 1,
            value: 1,
            initiativeId: 1,
            valueData: {
              notApplicableType: 1,
            },
            isPrivate: 1,
          },
        },
      },
      {
        $unwind: '$utrvs',
      },
      {
        $replaceRoot: { newRoot: '$utrvs' },
      },
      {
        $match: {
          universalTrackerId: {
            $in: utrIds,
          },
          status: ActionList.Verified,
          'valueData.notApplicableType': null,
          isPrivate: { $ne: true },
        },
      },
      {
        $group: {
          _id: {
            initiativeId: '$initiativeId',
            universalTrackerId: '$universalTrackerId',
          },
          total: { $sum: '$value' },
          average: { $avg: '$value' },
          count: { $sum: 1 },
        },
      },
      ...this.getGroupByView(view),
    ];
    return Survey.aggregate(aggregation);
  }

  private static getGroupByView(view: SubmissionInsightsView) {
    switch (view) {
      case SubmissionInsightsView.Company:
        return [
          {
            $group: {
              _id: '$_id.initiativeId',
              utrvs: {
                $push: {
                  total: '$total',
                  average: '$average',
                  universalTrackerId: '$_id.universalTrackerId',
                  count: '$count',
                },
              },
            },
          },
        ];
      case SubmissionInsightsView.Sector:
        return [
          {
            $lookup: {
              from: 'initiatives',
              localField: '_id.initiativeId',
              foreignField: '_id',
              as: 'initiatives',
            },
          },
          {
            $addFields: {
              sector: { $arrayElemAt: ['$initiatives.industry.icb2019.level2', 0] },
            },
          },
          {
            $match: {
              sector: { $nin: [null, ''] },
            },
          },
          {
            $group: {
              _id: '$sector',
              utrvs: {
                $push: {
                  total: '$total',
                  average: '$average',
                  universalTrackerId: '$_id.universalTrackerId',
                  count: '$count',
                },
              },
            },
          },
        ];
    }
  }

  // Filter utrs based on scope match and return ids for further filtering
  public static async getPackUsageUtrs(utrFilter: UtrMatchFilters): Promise<ObjectId[]> {
    const blueprintRepo = getBlueprintRepository();
    const blueprint = await blueprintRepo.mustFindExpandedByCode(DefaultBlueprintCode);
    const utrCodes = extractVisibleUtrCodes(blueprint);
    return UniversalTracker.find({ code: { $in: utrCodes }, ...utrFilter }, { _id: 1 })
      .lean()
      .exec()
      .then((utrs) => utrs.map(u => u._id));
  }
}
