/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface, CompositeUtrConfigMap, configs } from '../survey/compositeUtrConfigs';
import { UTrGroupConfigMap } from '../survey/utrGroupConfigs';

export interface CompositeUtrConfigRepositoryInterface {
  listAll(): Promise<CompositeUtrConfigMap | UTrGroupConfigMap>;

  getSurveyByCode(surveyCode: string): Promise<CompositeUtrConfigInterface | undefined>;
}

export class CompositeUtrConfigRepository implements CompositeUtrConfigRepositoryInterface {
  private readonly configs: CompositeUtrConfigMap;

  constructor(compositeConfig: CompositeUtrConfigMap) {
    this.configs = compositeConfig;
  }

  async listAll() {
    return this.configs;
  }

  async getSurveyByCode(surveyCode: string) {
    return this.configs[surveyCode];
  }
}

export const getCompositeUtrConfigRepository = (data = configs) => new CompositeUtrConfigRepository(data);
