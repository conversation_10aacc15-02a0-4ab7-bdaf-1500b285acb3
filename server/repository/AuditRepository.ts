/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import { ObjectId } from 'bson';
import AuditLog from '../models/auditLog';
import {
  AuditFilterOptions,
  AuditStorage,
} from '../service/audit/AuditService';
import { AuditLogEntry, CreateAuditEntry } from '../service/audit/AuditModels';


export class AuditRepository implements AuditStorage {
  public save(auditLog: CreateAuditEntry): Promise<AuditLogEntry> {
    return AuditLog.create(auditLog);
  }

  public findForInitiatives(initiativeIds: ObjectId[], filters: AuditFilterOptions) {
    const { fromDate, toDate } = filters;

    return AuditLog.find(
      {
        $and: [
          { initiativeId: { $in: initiativeIds } },
          ...(fromDate ? [{ eventDate: { $gte: new Date(fromDate) } }] : []),
          ...(toDate ? [{ eventDate: { $lte: new Date(toDate) } }] : []),
        ],
      },
      {
        debugContext: 0,
        client: 0,
        uuid: 0,
        version: 0,
        __v: 0,
        created: 0,
      }
    )
      .sort({ eventDate: -1 })
      .limit(1000)
      .lean()
      .exec();
  }
}
