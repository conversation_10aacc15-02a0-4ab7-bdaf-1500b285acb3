/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { KeysEnum } from '../models/commonProperties';
import SgxIssuer, { SgxIssuerPlain } from '../models/sgxIssuer';
import { ObjectId } from 'bson';

export type SGXIssuerNames = Pick<SgxIssuerPlain, '_id' | 'issuerName' | 'companyRegNo' | 'issuerAddress'>;
const sgxIssuerNamesProjection: KeysEnum<SGXIssuerNames> = {
  _id: 1,
  issuerName: 1,
  companyRegNo: 1,
  issuerAddress: 1
}
export interface SgxIssuerPlainWithInitiative extends SgxIssuerPlain {
  initiativeId: string;
}

export class SGXIssuerRepository {
  public static async findById(id: string): Promise<SgxIssuerPlain | null> {
    return SgxIssuer.findById(new ObjectId(id)).lean().exec();
  }

  public static async getIssuerNames(): Promise<SGXIssuerNames[]> {
    const issuers = await SgxIssuer.find({}, sgxIssuerNamesProjection).lean().exec();
    return this.filterDelisted(issuers);
  }

  private static filterDelisted<T extends Pick<SgxIssuerPlain, 'issuerName'>>(issuers: T[]): T[] {
    // Case-insensitive keywords
    const delistedKeywords = [
      "\\[delisted\\]",
      "\\[deactivate\\]",
      "\\[deactivated\\]",
      "\\[de\\-activated\\]",
      "\\[to be deactivated\\]",
    ];
    const pattern = `^(${delistedKeywords.join('|')})`;
    const regex = new RegExp(pattern, 'i');
    return issuers.filter(issuer => (
      regex.exec(issuer.issuerName?.name ?? '') === null
    ));
  }
}
