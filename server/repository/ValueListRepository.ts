/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import { ObjectId } from 'bson';
import ValueList, { ValueListModel } from '../models/valueList';
import { ValueList as ValueListPlain } from '../models/public/valueList';

export class ValueListRepository {

  public static async findByIds(ids: ObjectId[], lean = true): Promise<ValueListPlain[]> {
    return ValueList.find({ _id: { $in: ids } }).lean(lean).exec();
  }

  public static async findByIdsMin(ids: ObjectId[]): Promise<Pick<ValueListPlain, '_id' | 'options'>[]> {
    return ValueList.find({ _id: { $in: ids } }, { _id: 1, options: 1 }).lean(true).exec();
  }

  public static async findById(id: ObjectId | string, lean = true) {
    return ValueList.findById(id).lean(lean).exec();
  }

  public static async mustFindById(id: string | ObjectId): Promise<ValueListModel> {

    if (!ObjectId.isValid(id)) {
      throw new Error(`ValueList id is not valid`);
    }

    const metricGroup = await ValueList.findById(id).exec();
    if (!metricGroup) {
      throw new Error(`ValueList was not found with id "${id}"`);
    }

    return metricGroup;
  }
}
