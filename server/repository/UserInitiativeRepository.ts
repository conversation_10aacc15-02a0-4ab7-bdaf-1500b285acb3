/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import User, { UserModel, UserPlain } from '../models/user';
import Initiative, {
  InitiativePlain,
  InitiativeTags, isOrganization,
} from '../models/initiative';
import { KeysEnum } from '../models/public/projectionUtils';
import { SurveyRepository } from './SurveyRepository';
import {
  InitiativeRepository,
  InitiativeSuggestion, SurveyDates,
} from './InitiativeRepository';
import { UserRolePermissions } from '../models/commonProperties';
import { ObjectId } from 'bson';
import { SurveyModelPlain, SurveyType } from '../models/survey';
import { InitiativePermissions } from '../service/initiative/InitiativePermissions';
import { UserRoles } from '../service/user/userPermissions';


// Common User/Initiative/Survey queries to avoid circular deps
export class UserInitiativeRepository {

  public static async findUserById(id: string | ObjectId) {
    return User.findById(id).exec();
  }

  public static async findOktaUserIds(initiativeIds: ObjectId[]) {
    const allInitiatives = await InitiativeRepository.getAllChildrenById(initiativeIds);
    const allInitiativeIds = allInitiatives.map(initiative => initiative._id);

    return User.find({
      'permissions.initiativeId': { $in: allInitiativeIds },
      oktaUserId: { $exists: true }
    }, { _id: 1, oktaUserId: 1 })
      .lean<Required<Pick<UserPlain, '_id' | 'oktaUserId'>>[]>()
      .exec();
  }

  public static async getUserInitiative(
    user: UserPlain,
    initiativeId: string | ObjectId
  ): Promise<InitiativePlain | undefined> {
    if (!await InitiativePermissions.canAccess(user, initiativeId)) {
      return undefined;
    }

    return await InitiativeRepository.getInitiativeById(initiativeId, true);
  }

  public static async getInitiativeUserPermissions({ permissions }: UserRolePermissions, initiativeId: string) {
    if (!Array.isArray(permissions) || permissions.length <= 0) {
      return undefined;
    }

    const p = permissions.find((perm: any) => String(perm.initiativeId) === initiativeId);
    if (p) {
      return p;
    }

    const initiative = await InitiativeRepository.getInitiativeById(initiativeId, true);
    if (!initiative) {
      return;
    }

    const initiativePermissions = permissions.map((p) => String(p.initiativeId));
    if (!Array.isArray(initiative.parents)) {
      return undefined;
    }

    for (const parent of initiative.parents) {
      const id = String(parent._id);
      if (initiativePermissions.includes(id)) {
        const p = permissions.find((p: any) => String(p.initiativeId) === id);
        if (p) {
          return p;
        }
      }
    }

    return undefined;
  }

  public static async getCompanyStatusFields(initiativeIds: Pick<InitiativePlain, '_id'>[]) {

    const companyStatusFields: {
      _id: Pick<InitiativePlain, '_id'>,
      userCount: number,
      surveys?: Pick<SurveyModelPlain, 'effectiveDate' | 'completedDate'>[],
      lastSurveyDate: Date,
      lastCompletedSurveyDate: Date
    }[] = await Initiative.aggregate([
      {
        "$match": {
          "_id": {
            "$in": initiativeIds
          },
          'type': 'initiative',
        }
      },
      {
        "$lookup": {
          "from": 'users',
          'localField': '_id',
          'foreignField': 'permissions.initiativeId',
          'as': 'users',
          'pipeline': [
            {
              $match: {
                "isStaff": false
              }
            }
          ]
        }
      },
      {
        "$lookup": {
          "from": 'surveys',
          'localField': '_id',
          'foreignField': 'initiativeId',
          'as': 'surveys',
          "pipeline": [
            {
              $sort: {
                effectiveDate: -1
              }
            },
            {
              $limit: 10
            }
          ]
        }
      },
      {
        "$project": {
          "_id": "$_id",
          "userCount": { $size: "$users" },
          lastSurveyDate: { $max: '$surveys.effectiveDate' },
          lastCompletedSurveyDate: { $max: '$surveys.completedDate' }
        }
      },
    ])

    const results = companyStatusFields.reduce((result, current) => {
      result[current._id.toString()] = {
        userCount: current.userCount,
        lastSurveyDate: current.lastSurveyDate,
        lastCompletedSurveyDate: current.lastCompletedSurveyDate,
      }
      return result
    }, {} as Record<string, {
      userCount: number,
      lastSurveyDate?: Date,
      lastCompletedSurveyDate?: Date
    }>);
    return results;
  }

  public static async getInitiativesByOktaGroupIds(groupIds: string[]): Promise<Pick<InitiativePlain, '_id'>[]> {
    return InitiativeRepository.getInitiativesByOktaGroupId(groupIds, { _id: 1 });
  }

  public static async findUsersByRole(initiativeIds: ObjectId[], role: UserRoles, excludedUser?: UserModel) {
    return User.find({
      ...(excludedUser ? { _id: { $ne: excludedUser._id } } : {}),
      permissions: {
        $elemMatch: { initiativeId: { $in: initiativeIds }, permissions: role },
      },
    }).exec();
  }
}
