/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { VisibilityStatus } from '../service/survey/scope/visibilityStatus';

export const applyVisibilityFilter = (match: Record<string, unknown>, visibility: VisibilityStatus | undefined, prefix = '') => {
  if (visibility === VisibilityStatus.ExcludeData) {
    match[`${prefix}isPrivate`] = { $ne: true };
  }

  return match
}

const getCondition = (elseValue: string) => ({
  $cond: {
    if: { $eq: ['$isPrivate', true] },
    then: '$$REMOVE',
    else: elseValue
  }
});

export const applyVisibilityProject = ($project: Record<string, unknown>, visibility: VisibilityStatus | undefined) => {
  if (visibility === VisibilityStatus.ExcludeValuesOnly) {
    $project.valueData = getCondition('$valueData');
    $project.value = getCondition('$value');
    $project.note = getCondition('$note');
    $project.notes = getCondition('$notes');
    // We need isPrivate in this scenario
    $project.isPrivate = 1;
  }

  return $project
}
