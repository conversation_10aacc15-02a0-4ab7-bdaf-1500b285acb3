import { ObjectId } from 'bson';
import Onboarding, {
  InitiativeOnboardingModel,
  InitiativeOnboardingPlain,
  ObType,
  OnboardingModel,
  OnboardingStatus,
} from '../models/onboarding';
import { OnboardingRepository, OnboardingSearchResult, statusCondition } from './OnboardingRepository';
import { FilterQuery, ProjectionType, QueryOptions } from 'mongoose';
import { KeysEnum } from '../models/commonProperties';

export class InitiativeOnboardingRepository extends OnboardingRepository {
  public async find(
    match: FilterQuery<OnboardingModel>,
    projection?: ProjectionType<OnboardingModel>,
    options?: QueryOptions
  ) {
    return Onboarding.find(
      { ...match, type: { $in: [ObType.Initiative, ObType.JoinRequest] } },
      projection,
      options
    ).exec() as Promise<InitiativeOnboardingModel[]>;
  }

  public async mustFindOne(match: { initiativeId: ObjectId; [key: string]: any }) {
    return Onboarding.findOne(match).orFail().exec() as Promise<InitiativeOnboardingModel>;
  }

  public async findExisting(email: string, initiativeId: ObjectId) {
    return Onboarding.findOne({
      'user.email': email,
      $or: [{ initiativeId: initiativeId }, { 'user.permissions.initiativeId': initiativeId }],
      status: statusCondition,
    }).exec() as Promise<InitiativeOnboardingModel | null>;
  }

  public async findExistingByInitiativeId({
    email,
    initiativeId,
    statuses = statusCondition['$in'],
  }: {
    email: string;
    initiativeId: ObjectId;
    statuses?: OnboardingStatus[];
  }) {
    return Onboarding.find({
      'user.email': email,
      $or: [{ initiativeId }, { 'user.permissions.initiativeId': initiativeId }],
      status: { $in: statuses },
    }).exec() as Promise<InitiativeOnboardingModel[]>;
  }

  // Root onboarding is an initiative where a normal flow onboarding occurs
  // Or it is at the level where bulk onboarding happens
  public async findExistingRootById(onboardingId: string, initiativeId: ObjectId) {
    return Onboarding.findOne({
      _id: onboardingId,
      initiativeId: initiativeId,
      status: statusCondition,
    }).exec() as Promise<InitiativeOnboardingModel | null>;
  }

  public async findExistingByPermissionInitiativeId({
    onboardingId,
    initiativeId,
    statuses = statusCondition['$in'],
  }: {
    onboardingId: string;
    initiativeId: ObjectId;
    statuses?: OnboardingStatus[];
  }) {
    return Onboarding.findOne({
      _id: onboardingId,
      'user.permissions.initiativeId': initiativeId,
      status: { $in: statuses },
    }).exec() as Promise<InitiativeOnboardingModel | null>;
  }

  public async findOnboardingInitiative(initiativeIds: ObjectId[]) {
    return Onboarding.find({
      status: statusCondition,
      initiativeId: { $in: initiativeIds },
    }) as Promise<InitiativeOnboardingModel[]>;
  }

  public async findOnboardingsSortedDescByCreated(initiativeIds: ObjectId[], statuses: OnboardingStatus[]) {
    return Onboarding.find({
      status: { $in: statuses },
      $or: [{ initiativeId: { $in: initiativeIds } }, { 'user.permissions.initiativeId': { $in: initiativeIds } }],
    })
      .sort({ created: 'desc' })
      .lean<InitiativeOnboardingPlain[]>()
      .exec();
  }

  public async findOnboardingCount(initiativeIds: ObjectId[]) {
    return Onboarding.find({
      status: statusCondition,
      initiativeId: { $in: initiativeIds },
    }).countDocuments();
  }

  public async searchOnboardingsForDelegation({
    initiativeIds,
    searchRegex,
  }: {
    initiativeIds: ObjectId[];
    searchRegex: RegExp;
  }): Promise<OnboardingSearchResult[]> {
    const searchOnboardingProject: KeysEnum<Omit<OnboardingSearchResult, 'jobTitle' | 'profile' | 'permissions'>> = {
      _id: 1,
      email: '$user.email',
      firstName: { $ifNull: ['$user.firstName', ''] },
      surname: { $ifNull: ['$user.surName', ''] },
      status: 1,
      user: {
        permissions: '$user.permissions',
      },
    };
    const aggregate = [
      {
        $match: {
          // // Filter out legacy onboardings of AT
          assuranceStakeholders: { $exists: false },
          type: { $in: [ObType.Initiative, ObType.JoinRequest] },
          initiativeId: { $in: initiativeIds },
          status: statusCondition,
          'user.email': { $regex: searchRegex },
        },
      },
      {
        $project: searchOnboardingProject,
      },
    ];

    return Onboarding.aggregate(aggregate).exec();
  }

  public async findExistingByUser(email: string, userId?: ObjectId) {
    const conditions: { [key: string]: any }[] = [{ 'user.email': email }];
    if (userId) {
      conditions.push({ 'user.userId': userId });
    }

    return Onboarding.find({
      $or: conditions,
      status: statusCondition,
      initiativeId: { $exists: true },
    }).exec() as Promise<InitiativeOnboardingModel[]>;
  }
}

let instance: InitiativeOnboardingRepository;
export const getInitiativeOnboardingRepository = () => {
  if (!instance) {
    instance = new InitiativeOnboardingRepository();
  }
  return instance;
};
