export enum BankType {
  AbsBank = 'AbsBank',
  ManualBank = 'ManualBank',
}

export interface Country {
  name: string;
  code: string;
}

export interface Bank {
  name?: string;
  code: string;
  licenceType?: string;
  logo?: string;
  type?: BankType;
  countryCodes?: string[];
  popularCountryCodes?: string[];
  countries?: Country[];
  popularCountries?: Country[];
}

export type BankingSetting = Pick<Bank, 'name' | 'code' | 'type'>;
