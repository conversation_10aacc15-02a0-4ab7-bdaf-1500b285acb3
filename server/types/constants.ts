/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

/**
 * Shared constants to avoid circular dependencies
 */
export enum ActionList {
  Created = 'created',
  Verified = 'verified',
  Updated = 'updated',
  Rejected = 'rejected',
  NotReported = 'not_reported',
}

export enum ActionMap {
  Verify = 'verify',
  Reject = 'reject',
  Update = 'update',
}

export enum UtrvType {
  Target = 'target',
  Actual = 'actual',
  Baseline = 'baseline',
}

export enum DataPeriods {
  Monthly = 'monthly',
  Quarterly = 'quarterly',
  Yearly = 'yearly'
}
