import {
  AssessmentData,
  DoubleMaterialityAssessmentData
} from '../service/materiality-assessment/types';
import { roundTo } from './number';

function getAverageScore({
  financialScore,
  nonFinancialScore,
}: {
  financialScore: number | undefined;
  nonFinancialScore: number | undefined;
}) {
  const validScores = [financialScore, nonFinancialScore].filter((score) => score !== undefined);
  if (validScores.length === 0) {
    return 0;
  }
  return roundTo(validScores.reduce((a, b) => a + b, 0) / validScores.length);
}

export function mapToDoubleMaterialityData({
  financialData,
  nonFinancialData,
  hasCustomOrder,
}: {
  financialData: AssessmentData[];
  nonFinancialData: AssessmentData[];
  hasCustomOrder: boolean;
}): DoubleMaterialityAssessmentData[] {
  const nonFinancialMap = new Map(nonFinancialData.map((data) => [data.code, data]));
  const mappedData = financialData.map((topic) => {
    const nonFinancialScore = nonFinancialMap.get(topic.code)?.score;
    const nonFinancialRelativeScore = nonFinancialMap.get(topic.code)?.relativeScore;
    const financialScore = topic.score;
    const financialRelativeScore = topic.relativeScore;
    const avgRelativeScore = getAverageScore({
      financialScore: financialRelativeScore,
      nonFinancialScore: nonFinancialRelativeScore,
    });
    const avgScore = getAverageScore({
      financialScore,
      nonFinancialScore,
    });
    return {
      ...topic,
      score: avgScore,
      relativeScore: avgRelativeScore,
      financialScore,
      financialRelativeScore,
      nonFinancialScore,
      nonFinancialRelativeScore,
    };
  });

  if (!hasCustomOrder) {
    return mappedData.sort((a, b) => {
      const sumA = (a.financialRelativeScore ?? 0) + (a.nonFinancialRelativeScore ?? 0);
      const sumB = (b.financialRelativeScore ?? 0) + (b.nonFinancialRelativeScore ?? 0);
      return sumB - sumA;
    });
  }

  return mappedData;
}
