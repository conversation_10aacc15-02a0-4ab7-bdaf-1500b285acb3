/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { DataTypes, QueryInterface, Sequelize } from 'sequelize';
import { tableName } from '../../service/email/model/EmailTransaction';

const columnName = 'data';

export default {
  up: function (queryInterface: QueryInterface, db: Sequelize) {
    // logic for transforming into the new state
    return queryInterface.addColumn(
      tableName,
      columnName,
      { type: DataTypes.JSON }
    );
  },

  down: function (queryInterface: QueryInterface) {
    // logic for reverting the changes
    return queryInterface.removeColumn(tableName, columnName);
  }
};
