/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */
const result = require('dotenv').config();
if (result.error) {
  throw result.error;
}

const config = {
  host: process.env.PGHOST,
  username: process.env.PGUSER,
  password: process.env.PGPASSWORD,
  database: process.env.PGDATABASE,
  dialect: 'postgres',
  port: process.env.PGPORT,
};

 module.exports = {
  development: config,
  production: config,
};
