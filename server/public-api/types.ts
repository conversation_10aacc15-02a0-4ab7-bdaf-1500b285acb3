import { UniversalTrackerPublic } from "../models/public/universalTrackerType";
import { KeysEnum } from "../models/public/projectionUtils";
import { UniversalTrackerValuePublic } from "../models/public/universalTrackerValueType";
import { SurveyModelPlain } from "../models/survey";
import { ObjectId } from "bson";


export type PublicSurvey = Pick<SurveyModelPlain,
  '_id' |
  'name' |
  'initiativeId' |
  'effectiveDate' |
  'type' |
  'period' |
  'completedDate' |
  'aggregatedDate' |
  'verificationRequired' |
  'evidenceRequired' |
  'utrvType' |
  'isPrivate'
>

export const publicSurvey: KeysEnum<PublicSurvey> = {
  _id: 1,
  name: 1,
  effectiveDate: 1,
  initiativeId: 1,
  type: 1,
  period: 1,
  completedDate: 1,
  aggregatedDate: 1,
  utrvType: 1,
  verificationRequired: 1,
  evidenceRequired: 1,
  isPrivate: 1,
};

export type PublicUtrv = Pick<UniversalTrackerValuePublic,
  '_id' |
  'universalTrackerId' |
  'initiativeId' |
  'effectiveDate' |
  'status' |
  'value' |
  'valueType' |
  'verificationRequired' |
  'evidenceRequired' |
  'period' |
  'valueData' |
  'assuranceStatus'
>

export const publicUtrv: KeysEnum<PublicUtrv> = {
  _id: 1,
  universalTrackerId: 1,
  initiativeId: 1,
  effectiveDate: 1,
  status: 1,
  value: 1,
  valueType: 1,
  verificationRequired: 1,
  evidenceRequired: 1,
  period: 1,
  valueData: {
    data: 1,
    table: 1,
    notApplicableType: 1,
  },
  assuranceStatus: 1,
}

export type PublicUtr = Pick<UniversalTrackerPublic<ObjectId>,
  '_id' |
  'name' |
  'valueLabel' |
  'type' |
  'valueType' |
  'valueValidation' |
  'typeCode' |
  'unit' |
  'unitType' |
  'numberScale'
>

export const publicUtr: KeysEnum<PublicUtr> = {
  _id: 1,
  name: 1,
  valueLabel: 1,
  type: 1,
  valueType: 1,
  valueValidation: 1,
  typeCode: 1,
  unit: 1,
  unitType: 1,
  numberScale: 1,
};

export interface PublicUtrvWithUtr extends PublicUtrv {
  universalTracker: PublicUtr
}

export interface PublicSurveyWithUtrvs extends PublicSurvey {
  universalTrackerValues: PublicUtrvWithUtr[]
}
