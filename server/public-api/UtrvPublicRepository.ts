import { ObjectId } from "bson";
import UniversalTrackerValue from "../models/universalTrackerValue";
import { publicUtr, publicUtrv, PublicUtrvWithUtr } from "./types";
import { ScopePermissions } from "./scopePermission";
import PermissionDeniedError from "../error/PermissionDeniedError";

import { PublicAuth } from "../routes/public/AuthTypes";
import { ScopePermission } from "./scopePermissionModels";

export class UtrvPublicRepository {

  public static async findById(id: string | ObjectId, user: PublicAuth): Promise<PublicUtrvWithUtr> {

    const utrv = await UniversalTrackerValue
      .findById(id, publicUtrv)
      .populate('universalTracker', publicUtr)
      .lean()
      .orFail()
      .exec()

    if (!await ScopePermissions.can(user, ScopePermission.UniversalTrackerValueRead, utrv)) {
      throw new PermissionDeniedError()
    }
    return utrv as PublicUtrvWithUtr
  }
}
