import { SurveyRepository } from "../repository/SurveyRepository";
import { PublicSurvey, publicSurvey, PublicSurveyWithUtrvs, publicUtrv, PublicUtrvWithUtr } from "./types";
import { ObjectId } from "bson";
import UniversalTrackerValue from "../models/universalTrackerValue";
import Survey, { SurveyModelPlain } from "../models/survey";

interface PublicSurveyLookup {
  survey: PublicSurvey & { toObject? : () => PublicSurvey } & Pick<SurveyModelPlain, 'visibleUtrvs'>
}

export class SurveyPublicRepository {

  public static async findSurveys(initiativeId: ObjectId): Promise<PublicSurvey[]> {
    return SurveyRepository.findSurveys({
      initiativeId,
      deletedDate: { $exists: false },
    }, publicSurvey);
  }

  public static async findSurveysFilter(initiativeId: ObjectId, fromUpdated: Date) {
    return Survey.aggregate<PublicSurvey & { lastUpdated?: Date }>([
      {
        $match: {
          initiativeId,
          deletedDate: { $exists: false },
        },
      },
      {
        $lookup: {
          from: 'universal-tracker-values',
          localField: 'visibleUtrvs',
          foreignField: '_id',
          as: 'visibleUtrvs'
        }
      },
      {
        $addFields: {
          lastUpdated: { $max: '$visibleUtrvs.lastUpdated' }
        }
      },
      {
        $match: {
          'lastUpdated': { $gte: fromUpdated }
        }
      },
      {
        $project: {
          ...publicSurvey,
          lastUpdated: 1
        }
      }
    ]).exec()
  }

  public static async findSurvey({ survey }: PublicSurveyLookup): Promise<PublicSurveyWithUtrvs> {
    const utrvs = await UniversalTrackerValue.aggregate<PublicUtrvWithUtr>([
      {
        $match: { _id: { $in: survey.visibleUtrvs } },
      },
      {
        $lookup: {
          from: 'universal-trackers',
          localField: 'universalTrackerId',
          foreignField: '_id',
          as: 'universalTracker'
        }
      },
      {
        $project: {
          ...publicUtrv,
          universalTracker: {
            $arrayElemAt: ['$universalTracker', 0]
          }
        }
      },
      {
        $project: {
          ...publicUtrv,
          universalTracker: {
            name: 1,
            valueLabel: 1,
            type: 1,
            valueType: 1,
            valueValidation: 1,
            typeCode: 1,
            unit: 1,
            unitType: 1,
            numberScale: 1,
          },
        }
      },
    ]).exec();

    const plainSurvey = survey.toObject ? survey.toObject() : survey;

    return {
      ...plainSurvey,
      visibleUtrvs: undefined,
      universalTrackerValues: utrvs,
    } as PublicSurveyWithUtrvs
  }
}
