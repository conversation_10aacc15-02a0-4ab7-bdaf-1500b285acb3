import { ObjectId } from "bson";
import type { PublicAuth } from "../routes/public/AuthTypes";
import { InitiativeRepository } from "../repository/InitiativeRepository";
import { RoleToScopesMap, ScopePermission } from "./scopePermissionModels";


type PermissionObject = { initiativeId: ObjectId };

export class ScopePermissions {

  public static hasScope(publicAuth: Pick<PublicAuth, 'connection'>, scope: ScopePermission) {
    const { roles, scopes = [] } = publicAuth.connection;

    if (scopes.includes(scope)) {
      return true;
    }

    for (const role of roles) {
      if (RoleToScopesMap[role]?.includes(scope)) {
        return true;
      }
    }

    return false;
  }

  public static async can(publicAuth: Pick<PublicAuth, 'connection'>, permission: ScopePermission, object: PermissionObject) {
    if (!this.hasScope(publicAuth, permission)) {
      return false;
    }

    const rootId = publicAuth.connection.initiativeId.toString();
    const targetInitiativeId = String(object.initiativeId);
    if (rootId === targetInitiativeId) {
      return true;
    }

    // Get all childIds
    const children = await InitiativeRepository.getAllChildrenById<{ _id: ObjectId }>(publicAuth.connection.initiativeId, undefined, { _id: 1 });
    const allIds = children.map(({ _id }) => _id.toString());

    return allIds.includes(targetInitiativeId);
  }
}
