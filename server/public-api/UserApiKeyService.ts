/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */


import User<PERSON><PERSON><PERSON><PERSON>, {
  <PERSON><PERSON><PERSON><PERSON>,
  UserApiKeyModel,
  generatePersonalAccessToken,
} from "../models/userApiKey";
import { UserPlain } from "../models/user";
import { InitiativePlain } from "../models/initiative";
import { KeysEnum } from "../models/public/projectionUtils";
import { LoggerInterface, wwgLogger } from "../service/wwgLogger";
import UserError from "../error/UserError";
import { customDateFormat } from "../util/date";

type SafeApiKeyProps = Pick<UserApiKeyModel,
  | '_id'
  | 'name'
  | 'initiativeId'
  | 'shortToken'
  | 'lastUsed'
  | 'created'
  | 'revokedDate'
  | 'roles'
  | 'scopes'
>;

interface CreateResponse {
  token: string,
  apiKey: SafeApiKeyProps
}

type ResolvedSafeApiKey = SafeApiKeyProps & { initiative?: Pick<InitiativePlain, '_id' | 'name'> };

const safeApiKeyProps: KeysEnum<SafeApiKeyProps> = {
  _id: 1,
  name: 1,
  // Need it for populating the initiative
  initiativeId: 1,
  shortToken: 1,
  lastUsed: 1,
  created: 1,
  revokedDate: 1,
  roles: 1,
  scopes: 1,
}


export class UserApiKeyService {

  constructor(private readonly logger: LoggerInterface) {
  }

  public readonly maxApiKeys = 50;
  public readonly maxValidApiKeys = 5;

  public async listPersonalTokens(user: Pick<UserPlain, '_id'>) {
    return UserApiKey.find({ userId: user._id }, safeApiKeyProps)
      .populate('initiative', { name: 1, _id: 1 })
      .lean<ResolvedSafeApiKey[]>()
      .exec();
  }

  public async createPersonalToken(
    user: Pick<UserPlain, '_id'>,
    createData: Pick<ApiKey, 'name' | 'initiativeId' | 'roles' | 'scopes'>,
  ): Promise<CreateResponse> {

    this.logger.info('Creating user api key', { user: user._id, createData });

    const token = await generatePersonalAccessToken();
    const apiKey = new UserApiKey({
      userId: user._id,
      name: createData.name,
      initiativeId: createData.initiativeId,
      scopes: createData.scopes,
      roles: createData.roles,
      longTokenHash: token.longTokenHash,
      shortToken: token.shortToken,
    })
    const userApiKey = await apiKey.save();
    return {
      apiKey: {
        _id: userApiKey._id,
        initiativeId: userApiKey.initiativeId,
        name: userApiKey.name,
        roles: userApiKey.roles,
        scopes: userApiKey.scopes,
        shortToken: userApiKey.shortToken,
        created: userApiKey.created,
        lastUsed: userApiKey.lastUsed,
        revokedDate: userApiKey.revokedDate,
      },
      token: token.token,
    };
  }

  public async revoke(user: Pick<UserPlain, '_id'>, apiKeyId: string) {
    const apiKey = await UserApiKey.findOne({ _id: apiKeyId, userId: user._id }).orFail().exec();
    this.logger.info('Revoking user api key', { user: user._id, apiKeyId, shortToken: apiKey.shortToken });

    if (apiKey.revokedDate) {
      throw new UserError(`This API key has already been revoked on ${customDateFormat(apiKey.revokedDate)}. No further action is needed.`, {
        apiKeyId,
        shortToken: apiKey.shortToken,
        revokedDate: apiKey.revokedDate
      });
    }

    apiKey.revokedDate = new Date();
    return apiKey.save();
  }
}

let instance: UserApiKeyService;
export const getAccessTokenService = () => {
  if (!instance) {
    instance = new UserApiKeyService(
      wwgLogger,
    );
  }
  return instance;
}
