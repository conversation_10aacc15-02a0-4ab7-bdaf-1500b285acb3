/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

export enum ScopePermission {
  InitiativeRead = 'initiative.read',
  SurveyRead = 'survey.read',
  UniversalTrackerValueRead = 'universal_tracker_value.read',
  UniversalTrackerValueWrite = 'universal_tracker_value.write',
  ValueListRead = 'value_list.read',
}

export enum ConnectionRole {
  DateEntry = 'data_entry',
}

export const RoleToScopesMap = {
  [ConnectionRole.DateEntry]: [
    ScopePermission.InitiativeRead,
    ScopePermission.SurveyRead,
    ScopePermission.UniversalTrackerValueRead,
    ScopePermission.UniversalTrackerValueWrite,
    ScopePermission.ValueListRead,
  ]
}
