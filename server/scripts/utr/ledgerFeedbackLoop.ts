#!/usr/bin/env node
import '../setup';
import cron from 'node-cron';
import { createQldbCommand } from '../../service/ledger/QldbCommand';

const [ , , ...args] = process.argv;
const cmd = createQldbCommand();

if (args.includes('--now')) {
  console.log('Running single instance');
  cmd.execute()
    .then((messagesConsumed) => console.log(`DONE - ${messagesConsumed} consumed.`))
    .then(() => setTimeout(() => process.exit(0), 1000));
} else {
  cron.schedule('*/10 * * * *', () => {
    console.log(new Date());
    cmd.execute()
      .then((messagesConsumed) => console.log(`DONE - ${messagesConsumed} consumed.`, new Date()))
      .catch(() => console.log(`An error occurred. Aborting.`))
  });
}
