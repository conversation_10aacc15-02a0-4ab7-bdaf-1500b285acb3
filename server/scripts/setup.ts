/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

// Load env variables first
import { config } from 'dotenv';

const envFilePath = process.env.G17_API_ENV_FILE;
const result = config({ path: envFilePath });
if (result.error) {
  console.warn(`WARNING: Failed to load env file from "${envFilePath || 'default'}" file path`, { cause: result.error });
}

import { connectDb } from '../service/db/Db';

connectDb();
