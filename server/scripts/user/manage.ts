#!/usr/bin/env node
import '../setup';
import { createManagerUserCmd } from "../../command/ManageUserCommand";

const [ , , action, email] = process.argv;
const managerUserCommand = createManagerUserCmd();
managerUserCommand.execute(action, email)
  .then(() => console.log('DONE'))
  .then(() => {
    setTimeout(() => process.exit(0), 1000);
  }).catch(e => {
    console.error(e);
    process.exit(1)
});

