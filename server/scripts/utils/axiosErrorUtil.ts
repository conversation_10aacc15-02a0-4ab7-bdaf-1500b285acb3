import { AxiosError, isAxiosError } from 'axios';

export interface SimplifiedAxiosError {
  message: string;
  status?: number;
  statusText?: string;
  url?: string;
  method?: string;
  responseData?: any;
  requestData?: any;
}

function isErrorLike(error: unknown): error is { message: string } {
  return (
    typeof error === 'object' &&
    error !== null &&
    'message' in error &&
    typeof (error as Record<string, unknown>).message === 'string'
  );
}

export function simplifyAxiosError(error: unknown): SimplifiedAxiosError {
  if (!error || typeof error !== 'object') {
    return {
      message: String(error || 'Unknown error')
    };
  }

  // Handle AxiosError specifically
  if (isAxiosError(error)) {
    return {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url,
      method: error.config?.method?.toUpperCase(),
      responseData: error.response?.data,
      requestData: error.config?.data ?
        (typeof error.config.data === 'string' ?
          error.config.data.substring(0, 200) :
          error.config.data) : undefined
    };
  }

  // Handle regular Error
  if (error instanceof Error) {
    return {
      message: error.message
    };
  }

  // Handle error-like objects with message property
  if (isErrorLike(error)) {
    return {
      message: error.message
    };
  }

  // Fallback for other objects
  return {
    message: JSON.stringify(error)
  };
}

export function formatAxiosError(error: AxiosError): string {
  const simplified = simplifyAxiosError(error);

  let formatted = `Error: ${simplified.message}`;

  if (simplified.status) {
    formatted += `\nHTTP ${simplified.status}`;
    if (simplified.statusText) {
      formatted += ` ${simplified.statusText}`;
    }
  }

  if (simplified.method && simplified.url) {
    formatted += `\n${simplified.method} ${simplified.url}`;
  }

  if (simplified.responseData) {
    const responseStr = typeof simplified.responseData === 'string' ?
      simplified.responseData :
      JSON.stringify(simplified.responseData, null, 2);

    // Limit response data length for readability
    const maxLength = 500;
    const truncatedResponse = responseStr.length > maxLength ?
      responseStr.substring(0, maxLength) + '...' :
      responseStr;

    formatted += `\nResponse: ${truncatedResponse}`;
  }

  return formatted;
}
