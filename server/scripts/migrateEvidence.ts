// Usage: npx ts-node migrateEvidence.ts --token <JWT> [--limit 1000] [--max 5000] [--url http://localhost:3000] [--log migrate-evidence.log]
// Requires: npm install yargs

import axios, { isAxiosError } from 'axios';
import fs from 'fs';
import path from 'path';
import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';
import ContextError from '../error/ContextError';
import { formatAxiosError } from './utils/axiosErrorUtil';

// eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-call
const argv = yargs(hideBin(process.argv))
  .option('token', {
    alias: 't',
    describe: 'JWT token for Authorization header',
    type: 'string',
    demandOption: true,
  })
  .option('limit', {
    alias: 'l',
    describe: 'Number of records to process per call (max: 10000)',
    type: 'number',
    default: 1000,
  })
  .option('max', {
    alias: 'm',
    describe: 'Total number of records to process before exit',
    type: 'number',
    default: Infinity,
  })
  .option('url', {
    alias: 'u',
    describe: 'Base URL for the API',
    type: 'string',
    default: 'http://localhost:4003',
  })
  .option('log', {
    alias: 'f',
    describe: 'Log file path',
    type: 'string',
    default: 'migrate-evidence.log',
  })
  .help()
  .parseSync();

const LOG_FILE = path.resolve(argv.log);

// Graceful shutdown handling
let isShuttingDown = false;
let shutdownStartTime = 0;

function gracefulShutdown(signal: string) {
  const now = Date.now();

  if (isShuttingDown) {
    // Only force terminate if it's been more than 1 second since first shutdown signal
    if (now - shutdownStartTime > 1000) {
      console.log('\n⚠️  Force terminating...');
      process.exit(1);
    } else {
      console.log('\n⏳ Please wait, gracefully shutting down...');
      return;
    }
  }

  isShuttingDown = true;
  shutdownStartTime = now;
  console.log(`\n🛑 Received ${signal}. Gracefully shutting down...`);
  console.log('⏳ Please wait 2 seconds, or press Ctrl+C again after 1 second to force terminate');

  log(`Migration interrupted by ${signal}`);
  log('--- Migration stopped by user ---');

  // Give more time for logs to flush and provide user feedback
  setTimeout(() => {
    console.log('\n✅ Migration stopped safely.');
    console.log(`📄 Log file: ${LOG_FILE}`);
    process.exit(0);
  }, 2000);
}

// Handle various termination signals
process.on('SIGINT', () => gracefulShutdown('SIGINT (Ctrl+C)'));
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGQUIT', () => gracefulShutdown('SIGQUIT'));

function log(error: unknown) {
  // Use simplified formatting for console output too
  logConsole(error);
  logToFile(error)
}

function logConsole(error: unknown) {
  console.error(isAxiosError(error) ?  formatAxiosError(error) : error);
}

function logToFile(error: unknown) {
  let line: string;
  if (isAxiosError(error)) {
    // Use simplified formatting for Axios errors
    line = `[${new Date().toISOString()}] ${formatAxiosError(error)}`;
  } else if (error instanceof Error) {
    line = `[${new Date().toISOString()}] ${error.message}`;
    if (error.stack) {
      line += `\n${error.stack}`;
    }
  } else {
    line = `[${new Date().toISOString()}] ${JSON.stringify(error)}`;
  }

  fs.appendFileSync(LOG_FILE, line + '\n');
}

export type ApiResponse<T = unknown> = {
  success: boolean;
  message?: string;
  data: T;
  errors?: Record<string, unknown>;
}

type MigrationResult = {
  modifiedCount: number;
  upsertedCount: number;
  matchedCount: number;
  utrvs: string[];
  verification?: {
    totalChecked: number;
    validDocuments: number;
    invalidDocuments: number;
    success: boolean;
    issues: Array<{
      utrvId: string;
      isValid: boolean;
      differences: string[];
    }>;
  };
} | string[];


async function migrateBatch({ url, token, limit }: { url: string; token: string; limit: number }) {
  const fullUrl = `${url}/api/admin/universal-tracker-values/migrate/evidence?limit=${limit}`;
  const response = await axios.post<ApiResponse<MigrationResult>>(
    fullUrl,
    {},
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      timeout: 60000,
    }
  );

  if (!response.data.success) {
    const message = typeof response.data.message === 'string' ? response.data.message : 'Unknown error';
    throw new ContextError(message, {
      code: 'MIGRATE_EVIDENCE_ERROR',
      data: response.data,
      userMessage: message,
      limit,
      fullUrl,
    });
  }
  return response.data;
}

async function main() {
  // Validate required parameters
  if (!argv.token || argv.token.trim() === '') {
    console.error('❌ Error: JWT token is required');
    console.error('Usage: npx ts-node migrateEvidence.ts --token <JWT> [options]');
    process.exit(1);
  }

  // Validate limit constraints
  const MAX_LIMIT = 10000;
  const MIN_LIMIT = 1;

  if (argv.limit < MIN_LIMIT || argv.limit > MAX_LIMIT) {
    console.error('❌ Error: Limit per batch must be between 1 and 10000');
    console.error(`Current limit: ${argv.limit}`);
    console.error('Please use --limit with a value between 1 and 10000');
    process.exit(1);
  }

  // Validate limit vs max relationship
  if (argv.limit > argv.max) {
    console.error('❌ Error: Limit per batch cannot be greater than max total to process');
    console.error(`Current values: limit=${argv.limit}, max=${argv.max}`);
    console.error('Either increase --max or decrease --limit');
    process.exit(1);
  }

  let totalProcessed = 0;
  let batch = 0;

  // Create a masked version of the JWT token for logging
  const tokenMask = argv.token.length > 12 ?
    `${argv.token.substring(0, 6)}...${argv.token.substring(argv.token.length - 6)}` :
    '***masked***';

  log('--- Starting migrate/evidence script ---');
  log(`Base URL: ${argv.url}`);
  log(`JWT Token: ${tokenMask}`);
  log(`Limit per call: ${argv.limit}`);
  log(`Max to process: ${argv.max}`);

  while (totalProcessed < argv.max && !isShuttingDown) {
    batch++;
    log(`\nBatch #${batch} - Processed so far: ${totalProcessed}`);

    // Check for shutdown signal before making API call
    if (isShuttingDown) {
      log('Stopping migration due to shutdown signal');
      break;
    }

    const { data } = await migrateBatch({ url: argv.url, token: argv.token, limit: argv.limit });

    let ids: string[] = [];
    if (Array.isArray(data)) {
      ids = data;
    } else if (data && data.utrvs) {
      ids = data.utrvs;

      // Check verification results and fail if there are issues
      if (data.verification) {
        const { verification } = data;
        log(`Verification results: ${verification.validDocuments}/${verification.totalChecked} valid documents`);

        if (!verification.success || verification.invalidDocuments > 0) {
          log(`MIGRATION VERIFICATION FAILED!`);
          log(`Invalid documents: ${verification.invalidDocuments}`);
          log(`Issues found:`);

          verification.issues.forEach((issue, index) => {
            log(`  UTRV ${issue.utrvId}:`);
            issue.differences.forEach(diff => {
              log(`    - ${diff}`);
            });
            if (index >= 4) { // Limit detailed output
              const remaining = verification.issues.length - index - 1;
              if (remaining > 0) {
                log(`    ... and ${remaining} more issues`);
              }
              return;
            }
          });

          throw new ContextError('Migration verification failed - data was modified unexpectedly', {
            code: 'MIGRATION_VERIFICATION_FAILED',
            invalidDocuments: verification.invalidDocuments,
            totalChecked: verification.totalChecked,
            issues: verification.issues
          });
        } else {
          log(`✅ Verification passed - only evidenceData was added as expected`);
        }
      }
    }

    log(`Returned ${ids.length} IDs in this batch.`);
    if (ids.length === 0) {
      log('No more records to process. Exiting.');
      break;
    }
    logConsole(`IDs: ${ids.slice(-3).join(', ')}`);
    logToFile(`IDs: ${ids.join(', ')}`);
    totalProcessed += ids.length;
    if (totalProcessed >= argv.max) {
      log(`Reached max limit (${argv.max}). Exiting.`);
      break;
    }
  }

  if (isShuttingDown) {
    log(`--- Migration interrupted. Total processed: ${totalProcessed} ---`);
  } else {
    log(`--- Migration completed. Total processed: ${totalProcessed} ---`);
  }
}

main().catch((err) => {
  console.error('\n❌ Migration failed:');
  log(err);
  process.exit(1);
});
