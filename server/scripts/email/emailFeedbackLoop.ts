#!/usr/bin/env node
import '../setup';
import cron from 'node-cron';
import AwsSesEmailCommand from '../../command/AwsSesEmailCommand';
import AwsSesWorkerFactory from '../../service/email/AwsSesWorkerFactory';
import config from '../../config';

const [ , , ...args] = process.argv;
const cmd = new AwsSesEmailCommand(
  AwsSesWorkerFactory(),
  config.queue.emailFeedback.queueName,
  10
);

if (args.includes('--now')) {
  console.log('Running single instance');
  cmd.execute()
    .then(() => console.log('DONE'))
    .then(() => setTimeout(() => process.exit(0), 1000));
} else {
  cron.schedule('*/10 * * * *', () => {
    console.log(new Date());
    cmd.execute().then(() => console.log('DONE', new Date()));
  });
}
