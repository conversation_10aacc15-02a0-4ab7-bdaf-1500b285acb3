#!/usr/bin/env node
import './setup';
import { getBackgroundWorkCommand } from "../service/background-process/BackgroundWorkCommand";
import { wwgLogger } from "../service/wwgLogger";
import ContextError from "../error/ContextError";

const [ , , ...args] = process.argv;
const now = Date.now();

const cleanUp = (exitCode: 0 | 1) => {
  wwgLogger.on('finish', function () {
    console.info(`wwgLogger finished draining events, exiting now with code ${exitCode}`);
    // Finish all log events
    process.exit(exitCode);
  });

  wwgLogger.on('error', function (err) {
    console.error(new ContextError('Error while draining wwgLogger events', { cause: err }));
    // Finish all log events
    process.exit(exitCode);
  });

  wwgLogger.end();
  // Let logger and other things close properly, but in case it still going kill it
  setTimeout(() => {
    console.info(`Timeout reached, exiting now with code ${exitCode}`);
    process.exit(exitCode);
  }, 5000);
}

getBackgroundWorkCommand().process(args)
  .then(() => {
    const durationInMilliseconds = Date.now() - now;
    console.info(`Completed background work run in ${durationInMilliseconds}ms`, { durationInMilliseconds })
  })
  .then(() => {
    console.info('About to close the process with success')
    cleanUp(0);
  })
  .catch(err => {
    wwgLogger.error(err)
    cleanUp(1)
  });
