import { Scope } from '../../models/common/scope';
import { issbSortingConfig } from './issb';
import { UtrSortingConfig } from './types';

const utrSortingConfigMap: Record<string, UtrSortingConfig | undefined> = {
  issb: issbSortingConfig,
};

export const getUtrSortingConfigMapByScope = (scope?: Scope) => {
  if (!scope) {
    return {};
  }
  const { sdg, materiality, standards, frameworks } = scope;
  return [...sdg, ...materiality, ...standards, ...frameworks].reduce<Record<string, UtrSortingConfig>>(
    (config, code) => {
      const matched = utrSortingConfigMap[code];
      if (matched) {
        config[code] = matched;
      }
      return config;
    },
    {}
  );
};
