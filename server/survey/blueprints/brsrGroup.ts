export const brsrGroup = {
  utrGroupConfig: {
    groupName: 'BRSR - Business Responsibility and Sustainability Report',
    groupData: {
      alternativeCode: 'brsr',
    },
    utrCodes: [
      'brsr/2023/a1',
      'brsr/2023/a2',
      'brsr/2023/a3',
      'brsr/2023/a4',
      'brsr/2023/a5',
      'brsr/2023/a6',
      'brsr/2023/a7',
      'brsr/2023/a8',
      'brsr/2023/a9',
      'brsr/2023/a10',
      'brsr/2023/a11',
      'brsr/2023/a12',
      'brsr/2023/a13',
      'brsr/2023/a14',
      'brsr/2023/a15',
      'brsr/2023/a16',
      'brsr/2023/a17',
      'brsr/2023/a18',
      'brsr/2023/a19-a',
      'brsr/2023/a19-b',
      'brsr/2023/a19-c',
      'brsr/2023/a22',
      'brsr/2023/a23',
      'brsr/2023/a24',
      'brsr/2023/a25',
      'brsr/2023/a26',
      'brsr/2023/b1',
      'brsr/2023/b3',
      'brsr/2023/b4',
      'brsr/2023/c1',
      'brsr/2023/c2/a',
      'brsr/2023/c2/b',
      'brsr/2023/c3',
      'brsr/2023/c4',
      'brsr/2023/c5',
      'brsr/2023/c6',
      'brsr/2023/c7',
      'brsr/2023/c8',
      'brsr/2023/c9',
      'brsr/2023/c10',
      'brsr/2023/c11',
      'brsr/2023/c12',
      'brsr/2023/c13',
      'brsr/2023/c14',
      'brsr/2023/c15',
      'brsr/2023/c16',
      'brsr/2023/c17',
      'brsr/2023/c18',
      'brsr/2023/c19',
      'brsr/2023/c20',
      'brsr/2023/c21',
      'brsr/2023/c22',
      'brsr/2023/c23',
      'brsr/2023/c24',
      'brsr/2023/c25',
      'brsr/2023/c26',
      'brsr/2023/c27',
      'brsr/2023/c28',
      'brsr/2023/c29',
      'brsr/2023/c30',
      'brsr/2023/c31',
      'brsr/2023/c32',
      'brsr/2023/c33',
      'brsr/2023/c34',
      'brsr/2023/c35',
      'brsr/2023/c36',
      'brsr/2023/c37',
      'brsr/2023/c38',
      'brsr/2023/c39',
      'brsr/2023/c40',
      'brsr/2023/c41',
      'brsr/2023/c42',
      'brsr/2023/c43',
      'brsr/2023/c44',
      'brsr/2023/c45',
      'brsr/2023/c46',
      'brsr/2023/c47',
      'brsr/2023/c48',
      'brsr/2023/c49',
      'brsr/2023/c50',
      'brsr/2023/c51',
      'brsr/2023/c52',
      'brsr/2023/c53',
      'brsr/2023/c54',
      'brsr/2023/c55',
      'brsr/2023/c56',
      'brsr/2023/c57',
      'brsr/2023/c58',
      'brsr/2023/c59',
      'brsr/2023/c60',
      'brsr/2023/c61',
      'brsr/2023/c62',
      'brsr/2023/c63',
      'brsr/2023/c64',
      'brsr/2023/c65',
      'brsr/2023/c66',
      'brsr/2023/c67',
      'brsr/2023/c68',
      'brsr/2023/c69',
      'brsr/2023/c70',
      'brsr/2023/c72',
      'brsr/2023/c73',
      'brsr/2023/c74',
      'brsr/2023/c75',
      'brsr/2023/c76',
      'brsr/2023/c77',
      'brsr/2023/c78',
      'brsr/2023/c79',
      'brsr/2023/c80',
      'brsr/2023/c81',
      'brsr/2023/c82',
      'brsr/2023/c83',
      'brsr/2023/c84',
      'brsr/2023/c85',
      'brsr/2023/c86',
      'brsr/2023/c87',
      'brsr/2023/c88',
      'brsr/2023/c89',
      'brsr/2023/c90',
      'brsr/2023/c91',
      'brsr/2023/c92',
      'brsr/2023/c93',
      'brsr/2023/c94',
      'brsr/2023/c95',
      'brsr/2023/c96',
      'brsr/2023/c97',
      'brsr/2023/c98',
      'brsr/2023/c99',
      'brsr/2023/c100',
      'brsr/2023/c101',
      'brsr/2023/c102',
      'brsr/2023/c103',
      'brsr/2023/c104',
      'brsr/2023/c105',
      'brsr/2023/c106',
      'brsr/2023/c107',
      'brsr/2023/c108',
      'brsr/2023/c109',
      'brsr/2023/c110',
      'brsr/2023/c111',
    ],
  },
};
