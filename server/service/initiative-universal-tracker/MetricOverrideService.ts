import { ObjectId } from 'bson';
import InitiativeUniversalTracker, { UtrvConfigType } from '../../models/initiativeUniversalTracker';
import UniversalTracker from '../../models/universalTracker';
import { getInitiativeUniversalTrackerService } from '../initiative/InitiativeUniversalTrackerService';

interface SetMetricOverrideParams<T = string> {
  initiativeId: T;
  utrIds: T[];
  utrvConfig: Partial<UtrvConfigType>;
}

export class MetricOverrideService {
  constructor(
    private universalTrackerModel: typeof UniversalTracker,
    private initiativeUniversalTrackerModel: typeof InitiativeUniversalTracker,
    private initiativeUniversalTrackerService: ReturnType<typeof getInitiativeUniversalTrackerService>
  ) {}

  public async override(request: SetMetricOverrideParams) {
    const { utrvConfig } = request;
    const initiativeId = new ObjectId(request.initiativeId);

    const utrs = await this.universalTrackerModel
      .find({ _id: { $in: request.utrIds } }, { _id: 1 })
      .lean<{ _id: ObjectId }[]>()
      .exec();

    const utrIds = utrs.map((utr) => utr._id);

    const initiativeUtrMap = await this.initiativeUniversalTrackerService.getUtrOverridesMap({
      initiativeId,
      utrIds,
    });

    const updates = utrIds.map((universalTrackerId) => {
      const initiativeUtr = initiativeUtrMap.get(universalTrackerId.toString());
      return {
        updateOne: {
          filter: { initiativeId, universalTrackerId },
          update: { $set: { utrvConfig: { ...initiativeUtr?.utrvConfig, ...utrvConfig } } },
          upsert: true,
        },
      };
    });
    return this.initiativeUniversalTrackerModel.bulkWrite(updates);
  }
}

let instance: MetricOverrideService;

export const getMetricOverrideService = () => {
  if (!instance) {
    instance = new MetricOverrideService(
      UniversalTracker,
      InitiativeUniversalTracker,
      getInitiativeUniversalTrackerService()
    );
  }
  return instance;
};
