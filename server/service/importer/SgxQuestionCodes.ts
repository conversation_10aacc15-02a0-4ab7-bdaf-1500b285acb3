export const SgxQuestionCodes = [
  {
    "QuestionCode": "sgx-core-1a",
    "SGX Question name": "SGX Core 1a) GHG (CO2) Absolute emissions – total",
    "ColumnCode": "",
    "valuelist option Code": "",
    "description": "Emissions, GHG Emissions, Total",
    "type/fx": "Numeric",
    "units": "tCO2e",
    "Comments": ""
  },
  {
    "QuestionCode": "gri/2020/302-1/e",
    "SGX Question name": "SGX Core 3) Total energy consumption",
    "ColumnCode": "",
    "valuelist option Code": "",
    "description": "Resources, Energy Usage, Total",
    "type/fx": "Numeric",
    "units": "kWhs",
    "Comments": "Question Unit default is MWhs. Conversion needed from kWhs"
  },
  {
    "QuestionCode": "gri/2020/303-5/a",
    "SGX Question name": "SGX Core 5) Total water consumption",
    "ColumnCode": "",
    "valuelist option Code": "",
    "description": "Resources, Water Use, Total",
    "type/fx": "Numeric",
    "units": "m³",
    "Comments": ""
  },
  {
    "QuestionCode": "gri/2020/405-1/b",
    "SGX Question name": "SGX Core 8, 10 & 22) - Current employees by employee category , gender and age group",
    "ColumnCode": "female",
    "valuelist option Code": "",
    "description": "Employees, Current, Gender (Female)",
    "type/fx": "Percent",
    "units": "People",
    "Comments": ""
  },
  {
    "QuestionCode": "gri/2020/405-1/b",
    "SGX Question name": "SGX Core 8, 10 & 22) - Current employees by employee category , gender and age group",
    "ColumnCode": "male",
    "valuelist option Code": "",
    "description": "Employees, Current, Gender (Male)",
    "type/fx": "Percent",
    "units": "People",
    "Comments": ""
  },
  {
    "QuestionCode": "gri/2020/405-1/b",
    "SGX Question name": "SGX Core 8, 10 & 22) - Current employees by employee category , gender and age group",
    "ColumnCode": "under_30",
    "valuelist option Code": "",
    "description": "Employees, Current, Age (Under 30)",
    "type/fx": "Percent",
    "units": "People",
    "Comments": ""
  },
  {
    "QuestionCode": "gri/2020/405-1/b",
    "SGX Question name": "SGX Core 8, 10 & 22) - Current employees by employee category , gender and age group",
    "ColumnCode": "age_30_50",
    "valuelist option Code": "",
    "description": "Employees, Current, Age (30 to 50)",
    "type/fx": "Percent",
    "units": "People",
    "Comments": ""
  },
  {
    "QuestionCode": "gri/2020/405-1/b",
    "SGX Question name": "SGX Core 8, 10 & 22) - Current employees by employee category , gender and age group",
    "ColumnCode": "over_50",
    "valuelist option Code": "",
    "description": "Employees, Current, Age (Over 50)",
    "type/fx": "Percent",
    "units": "People",
    "Comments": ""
  },
  {
    "QuestionCode": "sgx-custom-70",
    "SGX Question name": "SGX Extended 4) Accident severity rate",
    "ColumnCode": "",
    "valuelist option Code": "",
    "description": "OSH, Rates, Accident Severity Rate",
    "type/fx": "Percent",
    "units": "NM",
    "Comments": ""
  },
  {
    "QuestionCode": "sgx-custom-71",
    "SGX Question name": "SGX Extended 5) Accident frequency rate",
    "ColumnCode": "",
    "valuelist option Code": "",
    "description": "OSH, Rates, Accident Frequency Rate",
    "type/fx": "Percent",
    "units": "NM",
    "Comments": ""
  },
  {
    "QuestionCode": "gri/2020/403-9/a",
    "SGX Question name": "SGX Core 16, 17 & 18) Work-related injuries and fatalities",
    "ColumnCode": "injuries_company_employees_recordable_injuries_number",
    "valuelist option Code": "",
    "description": "OSH, Injuries, Recordable",
    "type/fx": "Numeric",
    "units": "Injuries",
    "Comments": ""
  },
  {
    "QuestionCode": "gri/2020/403-9/a",
    "SGX Question name": "SGX Core 16, 17 & 18) Work-related injuries and fatalities",
    "ColumnCode": "injuries_company_employees_number_fatalities",
    "valuelist option Code": "",
    "description": "OSH, Injuries, Fatalities",
    "type/fx": "Numeric",
    "units": "Injuries",
    "Comments": ""
  },
  {
    "QuestionCode": "gri/2020/403-9/a",
    "SGX Question name": "SGX Core 16, 17 & 18) Work-related injuries and fatalities",
    "ColumnCode": "injuries_company_employees_high-consequence_work-related_injuries_number",
    "valuelist option Code": "",
    "description": "OSH, Injuries, High-Consequence",
    "type/fx": "Numeric",
    "units": "Injuries",
    "Comments": ""
  },
  {
    "QuestionCode": "sgx-custom-42",
    "SGX Question name": "SGX Core 20a) Board independence",
    "ColumnCode": "sgx42-percentage",
    "valuelist option Code": "",
    "description": "BOD/MGT, Composition, Independence",
    "type/fx": "Percent",
    "units": "People",
    "Comments": ""
  },
  {
    "QuestionCode": "sgx-custom-53",
    "SGX Question name": "SGX Core 27a) Assurance of sustainability report",
    "ColumnCode": "sgx53-assurance",
    "valuelist option Code": "sgx53-assurance1",
    "description": "CSR Report, Assurances, External SRA",
    "type/fx": "Boolean",
    "units": "NM",
    "Comments": "Data for this point is in Boolean 1 or 0 form. Can it be arrange that, if value = 1, then select the valueList option code \"sgx53-assurance1"
  },
  {
    "QuestionCode": "gri/2020/404-1/a",
    "SGX Question name": "SGX Core 14 & 15) Average training hours per employee",
    "ColumnCode": "female",
    "valuelist option Code": "",
    "description": "Employees, Training, Gender (Female)",
    "type/fx": "Numeric",
    "units": "Hours",
    "Comments": ""
  },
  {
    "QuestionCode": "gri/2020/404-1/a",
    "SGX Question name": "SGX Core 14 & 15) Average training hours per employee",
    "ColumnCode": "male",
    "valuelist option Code": "",
    "description": "Employees, Training, Gender (Male)",
    "type/fx": "Numeric",
    "units": "Hours",
    "Comments": ""
  },
  {
    "QuestionCode": "gri/2020/403-10/a",
    "SGX Question name": "SGX Core 16, 17 & 18) Work-related injuries and fatalities",
    "ColumnCode": "health_ill_health",
    "valuelist option Code": "",
    "description": "OSH, Diseases, Incidents",
    "type/fx": "Numeric",
    "units": "Incidents",
    "Comments": ""
  }
]