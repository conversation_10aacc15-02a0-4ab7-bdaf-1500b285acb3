/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { Excel, getExcel } from "../file/Excel";

export interface MappingImporterOptions {
  mappingSheetName?: string;
  mappingSheetPath: string
  targetFileName: string;
  targetFilePath: string;
}

export class MappingImporter {

  constructor(
    private excel: Excel,
  ) {
  }

  public async generateImportSheet(options: MappingImporterOptions) {

    const {
      mappingSheetName = "Questions",
      mappingSheetPath,
      targetFileName = "Questions",
      targetFilePath,
    } = options;

    // Load mapping file
    const data = await this.excel.readFile(mappingSheetPath)

    // Extract formulas from mapping sheet
    const sheetWithFormulas = data.Sheets[mappingSheetName]

    // Load the target spreadsheet that contains the data
    const sheetData = this.excel.sheetToJson(sheetWithFormulas)
    // Create "Questions" sheet
    const targetBook = await this.excel.readFile(targetFilePath)
    await this.excel.addToBook(targetBook, sheetData, targetFileName)

    // Use the mapping sheet formulas to populate this new Questions sheet in targer
    return targetBook;
  }

}

let instance: MappingImporter;
export const getMappingImporter = () => {
  if (!instance) {
    instance = new MappingImporter(
      getExcel()
    );
  }
  return instance;
}
