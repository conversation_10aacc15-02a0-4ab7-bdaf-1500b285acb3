/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { RootInitiativeData } from "../../repository/InitiativeRepository";
import { ProductCodes, Subscription, SubscriptionItem } from "../../models/customer";
import { getCustomerManager } from "../payment/CustomerManager";
import ContextError from "../../error/ContextError";
import { getAppConfigProvider } from "../app/AppConfigProvider";
import { AppConfig } from "../app/AppConfig";
import Initiative from "../../models/initiative";
import { getUTCEndOf, toTimestamp } from "../../util/date";
import { LoggerInterface, wwgLogger } from "../wwgLogger";
import { getInnerId } from "../payment/handlers/HandlerType";
import UserError from "../../error/UserError";

export type RootInitiativeWithCustomer = RootInitiativeData & Required<Pick<RootInitiativeData, 'customer'>>;

interface AppChangeRequest {
  rootInitiative: RootInitiativeWithCustomer;

  /** Should be one of the valid ProductCodes value **/
  newProductCode: string;
  /** App config code to apply to the new product tier */
  appConfigCode?: string;

  /**
   * Stripe couponId to apply to new product
   *
   * It's *required* if the current subscription have a discount that
   * is not applicable to the new product.
   */
  couponId?: string;
}

interface ValidUpgradePathCheck {
  from: ProductCodes;
  to: string;
}

interface ValidUpgradePath extends ValidUpgradePathCheck {
  to: ProductCodes;
}

interface ConfigCheck {
  config: AppConfig | undefined;
  appConfigCode?: string;
  newProductCode: ProductCodes;
}

interface SubItemChange extends Pick<AppChangeRequest, 'rootInitiative' | 'couponId'> {
  sub: Subscription;
  item: SubscriptionItem;
  newAppConfig: AppConfig;
}

interface ChangeData {
  appConfigCode: string | undefined;
  check: ValidUpgradePath;
  rootInitiative: RootInitiativeWithCustomer;
  appConfig: AppConfig;
  newProductCode: string;
}

interface CouponChange {
  sub: Subscription;
  couponId?: string;
  priceId: string;
  newProductId: string;
  initiativeId: string;
  newAppConfig: AppConfig;
}

/**
 * Deal with upgrade/downgrade of the app using different tiers.
 */
export class UpgradeAppService {

  constructor(
    private logger: LoggerInterface,
    private customerManager: ReturnType<typeof getCustomerManager>,
    private appConfigProvider: ReturnType<typeof getAppConfigProvider>,
  ) {
  }

  private readonly upgradePaths: Record<ProductCodes, ProductCodes[] | undefined> = {
    // Not allowed yet
    [ProductCodes.SGXESGenome]: [],
    [ProductCodes.CompanyTrackerStarter]: [
      ProductCodes.CompanyTracker,
      ProductCodes.CompanyTrackerPro,
      ProductCodes.CompanyTrackerEnterprise
    ],
    [ProductCodes.CompanyTracker]: [
      ProductCodes.CompanyTrackerPro,
      ProductCodes.CompanyTrackerEnterprise
    ],
    [ProductCodes.CompanyTrackerPro]: [ProductCodes.CompanyTrackerEnterprise],
    [ProductCodes.CompanyTrackerEnterprise]: [],

    // Not supported
    [ProductCodes.PortfolioTracker]: [],
    [ProductCodes.MaterialityTracker]: [],
    [ProductCodes.MaterialityTrackerSolopreneur]: [],
    [ProductCodes.MaterialityTrackerStartup]: [],
    [ProductCodes.MaterialityTrackerMicro]: [],
    [ProductCodes.MaterialityTrackerSME]: [],
    [ProductCodes.MaterialityTrackerMidCap]: [],
    [ProductCodes.MaterialityTrackerMNC]: [],
    [ProductCodes.MaterialityTrackerLarge]: [],
  }

  private isValidUpgradePath(check: ValidUpgradePathCheck): check is ValidUpgradePath {
    if (check.from === check.to) {
      return false;
    }

    return this.upgradePaths[check.from]?.includes(check.to as ProductCodes) ?? false;
  }

  private isValidDowngradePath(check: ValidUpgradePathCheck): check is ValidUpgradePath {
    if (check.from === check.to) {
      return false;
    }
    // if reverse path is valid, allow to downgrade
    return this.upgradePaths[check.to as ProductCodes]?.includes(check.from) ?? false;
  }

  private async mustGetAppConfig(rootInitiative: RootInitiativeData) {
    const appConfig = await this.appConfigProvider.getByOrganization(rootInitiative);
    if (!appConfig) {
      throw new ContextError(`App config not found for ${rootInitiative.name}`, {
        initiativeId: rootInitiative._id,
        appConfigCode: rootInitiative.appConfigCode ?? null,
      });
    }
    return appConfig;
  }


  /**
   * GIVEN I am choosing to upgrade my plan
   * THEN I should be immediately upgraded to the new plan
   *
   * GIVEN I am upgrading my account to a higher plan
   * WHEN I am already paying for additional seats
   * THEN continue charging for the additional seats as well as the upgraded plan
   */
  public async upgrade({ rootInitiative, newProductCode, appConfigCode, couponId }: AppChangeRequest) {

    const appConfig = await this.mustGetAppConfig(rootInitiative);

    const check = { from: appConfig.productCode, to: newProductCode };
    if (!this.isValidUpgradePath(check)) {
      throw new ContextError(`Invalid upgrade path for ${rootInitiative.name}`, {
        ...check,
        initiativeId: rootInitiative._id,
      });
    }
    const { sub, item, newAppConfig } = await this.getChangeData({
      appConfigCode: appConfigCode,
      check: check,
      rootInitiative: rootInitiative,
      appConfig: appConfig,
      newProductCode: newProductCode
    });

    this.logger.info(`${rootInitiative.name} changing product tier from ${check.from} to ${check.to}`, {
      initiativeId: rootInitiative._id.toString(),
      appConfigCode,
      newProductCode,
      couponId,
      newAppConfigCode: newAppConfig.code,
      subscriptionId: sub.id,
    })

    const updatedSub = await this.changeProductTier({
      rootInitiative,
      sub,
      item,
      newAppConfig,
      couponId,
    });

    // Apply config changes
    const result = await Initiative.findByIdAndUpdate(rootInitiative._id, {
      appConfigCode: newAppConfig.code,
      permissionGroup: newAppConfig.permissionGroup,
      // Return object after the update with "new" setting
    }, { new: true }).lean().orFail().exec();

    return {
      _id: result._id,
      appConfigCode: result.appConfigCode,
      permissionGroup: result.permissionGroup,
      subscriptionId: updatedSub.id,
      itemId: item.id,
    }
  }

  private async getChangeData({ appConfigCode, check, rootInitiative, appConfig, newProductCode }: ChangeData) {
    const newAppConfig = await this.getNewAppConfig(appConfigCode, check.to);

    // Update subscription and handle discount if available
    const subscriptions = await this.customerManager.getSubscriptions(rootInitiative._id);

    const sub = this.customerManager.getValidSubscriptionByProduct(
      appConfig.productCode,
      subscriptions
    );
    const item = sub?.items.find(item => item.productCode === appConfig.productCode);

    if (!sub || !item) {
      throw new ContextError(`Initiative does not have valid ${appConfig.productCode} subscription`, {
        initiativeId: rootInitiative._id,
        subProducts: subscriptions.map(s => ({ id: s.id, items: s.items.map(i => i.productCode) })),
        newProductCode,
      });
    }

    const newProductSub = this.customerManager.getValidSubscriptionByProduct(
      newAppConfig.productCode,
      subscriptions
    );

    if (newProductSub) {
      throw new ContextError(`Initiative already have ${newAppConfig.productCode} valid subscription`, {
        subscriptionId: newProductSub.id,
        newProductCode,
        initiativeId: rootInitiative._id,
      });
    }
    return { newAppConfig, sub, item };
  }

  private async changeProductTier(changeData: SubItemChange) {
    const { rootInitiative, sub, item, newAppConfig, couponId } = changeData;

    const interval = item.price?.recurring?.interval ?? 'year';
    const price = await this.customerManager.getPricesForProductCode(newAppConfig.productCode, interval);

    await this.mustValidateCouponChange({
      sub,
      couponId,
      priceId: price.id,
      initiativeId: rootInitiative._id.toString(),
      newProductId: getInnerId(price.product),
      newAppConfig
    });

    const quantity = item.quantity ?? 1;

    /*
     * IDEALLY client will be charged the higher plan rate immediately,
     * minus the pro-rata cost that is now not being spent on the lower tier.
     * Next month they would just be charged the new higher tiers rate
     */
    const updatedSub = await this.customerManager.updateRemoteSubscription(sub.id, {
      items: [
        {
          id: item.id,
          price: price.id,
          quantity: quantity,
        },
      ],
      ...(couponId ? { coupon: couponId } : {}),
      proration_behavior: 'always_invoice',
      proration_date: toTimestamp(getUTCEndOf('day')),
      metadata: { platformUpgrade: new Date().toISOString() }
    })

    this.logger.info(`Update ${rootInitiative.name} item to new product ${newAppConfig.productCode}`, {
      initiativeId: rootInitiative._id.toString(),
      subId: sub.id,
      itemId: item.id,
      priceId: price.id,
    });

    return updatedSub;
  }

  private async mustValidateCouponChange(changeData: CouponChange) {
    const { sub, couponId, newProductId, initiativeId, newAppConfig } = changeData;

    if (!sub.discount?.coupon?.id || couponId) {
      return true;
    }

    const context = { initiativeId, subId: sub.id, newProductId };

    // Need to apply discount to new thing
    this.logger.info(`Existing subscription have discount`, {
      ...context,
      discount: sub.discount,
    });

    const remoteCoupon = await this.customerManager.getCoupon(sub.discount.coupon.id, { expand: ['applies_to'] });
    if (remoteCoupon.applies_to?.products) {

      this.logger.info('Current discount have product limitations', {
        ...context,
        productIds: remoteCoupon.applies_to.products,
      });

      if (remoteCoupon.applies_to.products.every(productId => productId !== newProductId)) {

        const msg = [
          'Current discount cannot be applied to the new product,',
          'please provide alternative coupon id that can be applied to productCode:',
          newAppConfig.productCode,
        ].join(' ');

        throw new UserError(msg, {
          ...context,
          productIds: remoteCoupon.applies_to.products,
        });
      }
    }
  }

  private async getNewAppConfig(
    appConfigCode: string | undefined,
    newProductCode: ProductCodes
  ) {
    if (appConfigCode) {
      const config = await this.appConfigProvider.getByCode(appConfigCode);
      return this.validateConfig({
        config,
        appConfigCode,
        newProductCode,
      });
    }

    const configs = await this.appConfigProvider.getByProductCode(newProductCode);
    const [config] = configs;
    if (!config) {
      throw new ContextError(`No app config found for ${newProductCode}`);
    }

    if (configs.length > 1) {
      throw new ContextError(`Multiple app configs found for ${newProductCode}`, {
        appConfigCodes: [...configs.map(c => c.code)],
        newProductCode,
      });
    }


    return this.validateConfig({ config, newProductCode });
  }

  private validateConfig({ config, appConfigCode, newProductCode }: ConfigCheck): AppConfig {
    if (!config) {
      throw new ContextError(`Failed to find appConfig for productCode ${newProductCode}`, {
        appConfigCode,
        newProductCode,
      });
    }

    if (!config.validProductCodes.includes(newProductCode)) {
      throw new ContextError(`AppConfig ${config.code} does not support ${newProductCode}: `, {
        validProductCodes: config.validProductCodes,
        newProductCode,
      });
    }

    return config;
  }

  /**
   * DOWNGRADE PATH
   *
   * GIVEN I am choosing to downgrade my plan
   * THEN I should continue to have access to my current plan until the end
   * of the current billing cycle
   * AND on the next billing cycle pay the new lowed plans fee rather
   * than the fee I was paying before
   *
   *
   * GIVEN I am downgrading my plan
   * WHEN I have move users now than available seats on the new plan
   * THEN display a warning modal to admin downgrading stating
   *
   * “You have downgraded to a plan that has less available seats than active users.
   * Please purchase xx additional seats before the end of your current billing cycle,
   * otherwise your xx most recently invite users will be removed from your account.”
   *
   * AND send a notification to all admins of this message
   *
   * AND if at the end of the billing cycle the account still has more users
   * than available seats then remove as many most recently added accounts
   * as is required to bring the account to the maximum number of seats
   * *This also applies to custom metrics and subsidiaries
   */
  public async downgrade({ rootInitiative, newProductCode }: AppChangeRequest) {
    const appConfig = await this.mustGetAppConfig(rootInitiative);


    const check = { from: appConfig.productCode, to: newProductCode };
    if (!this.isValidDowngradePath(check)) {
      throw new ContextError(`Invalid downgrade path for ${rootInitiative.name}`, {
        ...check,
        initiativeId: rootInitiative._id,
      });
    }

    throw new ContextError('Downgrade is not supported yet', {
      ...check,
      initiativeId: rootInitiative._id,
    });
  }

}

let instance: UpgradeAppService;
export const getUpgradeAppService = () => {
  if (!instance) {
    instance = new UpgradeAppService(
      wwgLogger,
      getCustomerManager(),
      getAppConfigProvider(),
    );
  }
  return instance;
}
