/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { BackgroundBaseWorker } from "../../background-process/BackgroundBaseWorker";
import { wwgLogger } from "../../wwgLogger";
import { CloneOrganizationWorkflow, getCloneOrganizationWorkflow, SupportedJobModel } from "./CloneWorkflow";

/**
 * Workers are always triggered in Google Cloud Job runs environments
 * allow as to do heavy work without affecting normal API server operations.
 */
export class CloneOrganizationWorker extends BackgroundBaseWorker<SupportedJobModel, CloneOrganizationWorkflow> {
}

let instance: CloneOrganizationWorker;
export const getCloneOrganizationWorker = () => {
  if (!instance) {
    instance = new CloneOrganizationWorker(wwgLogger, getCloneOrganizationWorkflow());
  }
  return instance;
};
