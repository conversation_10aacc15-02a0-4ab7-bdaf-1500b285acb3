/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { HydratedDocument } from "mongoose";
import BackgroundJob, { BackgroundJob<PERSON>odel, BackgroundJobPlain, CreateJob, JobStatus, JobType, Task, TaskStatus, TaskType } from "../../../models/backgroundJob";
import { UserPlain } from "../../../models/user";
import { BackgroundBaseWorkflow, TaskResult } from "../../background-process/BackgroundBaseWorkflow";
import { generatedUUID } from "../../crypto/token";
import { createLogEntry } from "../../jobs";
import { getWorkflowFileStorage, WorkflowFileStorage } from "../../survey/workflow/WorkflowFileStorage";
import { LoggerInterface, wwgLogger } from "../../wwgLogger";
import { getCloneExporter } from "./CloneExporter";
import { getCloneImporter } from "./CloneImporter";
import { ObjectId } from 'bson';

type CloneExportData = {
  userId?: string;
  initiativeId: string;
  filename: string;
  remoteFilePath?: string;
};

type CloneImportData = {
  userId?: string;
  url: string;
  result?: {};
};

export type TaskCloneOrganizationExport = Task<CloneExportData, TaskType.CloneOrganizationExport>;
export type TaskCloneOrganizationImport = Task<CloneImportData, TaskType.CloneOrganizationImport>;
export type SupportedTask = TaskCloneOrganizationExport | TaskCloneOrganizationImport;
export type SupportedJobPlain = BackgroundJobPlain<SupportedTask[]> & { type: JobType.CloneOrganization };
export type SupportedJobModel = HydratedDocument<SupportedJobPlain>;

export class CloneOrganizationWorkflow extends BackgroundBaseWorkflow<SupportedJobModel> {
  constructor(
    protected logger: LoggerInterface,
    protected jobType: JobType.CloneOrganization,
    private fileStorage: WorkflowFileStorage,
  ) {
    super();
  }

  protected async processTask(job: SupportedJobModel, task: SupportedTask): Promise<TaskResult<SupportedJobModel>> {

    const result: TaskResult<SupportedJobModel> = {
      job,
      executeNextTask: false,
    };

    task.status = TaskStatus.Processing;
    job.markModified('tasks');

    let lastSave = Date.now();
    const isTimeToSave = () => Date.now() - lastSave > 10000;
    const addTaskLog = async (message: string, config?: { lossy?: boolean }) => {
      if (!config?.lossy || isTimeToSave()) {
        job.logs.push(createLogEntry(message));
        lastSave = Date.now();
        await job.save();
        return true;
      }
      return false;
    }

    try {
      switch (task.type) {
        case TaskType.CloneOrganizationExport: {
          const cloneManager = getCloneExporter();
          const { remoteFilePath } = await cloneManager.export({
            initiativeId: task.data.initiativeId,
            remoteFolder: `jobs/${job._id}/${task.id}`,
            filename: task.data.filename,
            addTaskLog
          });

          task.data.remoteFilePath = remoteFilePath;

          task.status = TaskStatus.Completed;
          result.executeNextTask = true;
          break;
        }
        case TaskType.CloneOrganizationImport: {
          if (!task.data.userId) {
            throw new Error('Missing userId');
          }
          const cloneManager = getCloneImporter();
          const cloneResult = await cloneManager.import({
            fileUrl: task.data.url,
            userId: task.data.userId,
            addTaskLog
          });

          result.executeNextTask = true;
          task.data.result = {
            success: cloneResult.success,
            config: cloneResult.config,
            rootForeignId: cloneResult.rootForeignId,
            rootLocalId: cloneResult.rootLocalId,
            filePath: cloneResult.filePath,
            folderPath: cloneResult.folderPath,
            error: cloneResult.error,
            reportingLevel: cloneResult.reportingLevelMap.stats,
            users: cloneResult.userMap.stats,
            surveys: cloneResult.surveyMap.stats,
            customMetricGroups: cloneResult.customMetricGroupMap.stats,
            universalTrackers: cloneResult.universalTrackerMap.stats,
            utrvs: cloneResult.utrvMap.stats,
            valueLists: cloneResult.valueListMap.stats,
          };

          if (!cloneResult.success) {
            throw new Error('Import failed: ' + cloneResult.error);
          }
          task.status = TaskStatus.Completed;
          result.executeNextTask = true;
          break;
        }
      }
    } catch (error) {
      task.status = TaskStatus.Error;
      job.status = JobStatus.Error;
      job.logs.push(
        createLogEntry(`Failed task ${task.name}. Error: ${error.message}`, { metadata: { cause: error } })
      );
      wwgLogger.error(error);
    }

    job.markModified('tasks');
    await job.save();

    return result;
  }

  public async createExportJob(workflow: CloneExportData, user: UserPlain): Promise<BackgroundJobModel> {
    const tasks: Task<CloneExportData>[] = [
      {
        id: generatedUUID(),
        type: TaskType.CloneOrganizationExport,
        name: `Clone EXPORT organization ${workflow.initiativeId}`,
        status: TaskStatus.Pending,
        data: {
          userId: user._id.toString(),
          ...workflow,
        },
      },
    ];

    const createData: CreateJob = {
      type: this.jobType,
      name: 'Clone EXPORT organization',
      tasks: tasks,
      logs: [createLogEntry('Starting clone organization export')],
    };

    return BackgroundJob.create(createData);
  }

  public async createImportJob(workflow: CloneImportData, user: UserPlain): Promise<BackgroundJobModel> {
    const tasks: Task<CloneImportData>[] = [
      {
        id: generatedUUID(),
        type: TaskType.CloneOrganizationImport,
        name: `Clone IMPORT organization`,
        status: TaskStatus.Pending,
        data: {
          userId: user._id.toString(),
          ...workflow,
        },
      },
    ];

    const createData: CreateJob = {
      type: this.jobType,
      name: 'Clone IMPORT organization',
      tasks: tasks,
      logs: [createLogEntry('Starting clone organization import')],
    };

    return BackgroundJob.create(createData);
  }

  public async getJobs() {
    return BackgroundJob.find({
      type: JobType.CloneOrganization,
    },
      {
        name: 1,
        status: 1,
        created: 1,
        tasks: 1,
        logs: 1,
      })
      .sort({ created: -1 })
      .limit(10)
      .orFail()
      .lean();
  }

  public async getJob(jobId: string) {

    const job = await BackgroundJob.findOne({
      _id: new ObjectId(jobId),
      type: JobType.CloneOrganization,
    })
      .orFail()
      .lean();

    if (job.status !== JobStatus.Completed) {
      this.logger.info('Job still not complete.');
      return { job };
    }

    const task = job.tasks.find(t => t.status === TaskStatus.Completed);
    if (task?.type !== TaskType.CloneOrganizationExport) {
      this.logger.info('Task not found or not of type CloneOrganizationExport');
      return { job };
    }

    const taskData = task.data as CloneExportData;
    const remoteFilePath = taskData.remoteFilePath;
    const filename = taskData.filename;

    if (!remoteFilePath) {
      this.logger.info('No remoteFilePath found');
      return { job };
    }

    const downloadURL = await this.fileStorage.getDownloadUrl(remoteFilePath, filename);

    return {
      job,
      downloadURL,
    };
  }

}

let instance: CloneOrganizationWorkflow;
export const getCloneOrganizationWorkflow = () => {
  if (!instance) {
    instance = new CloneOrganizationWorkflow(
      wwgLogger,
      JobType.CloneOrganization,
      getWorkflowFileStorage()
    );
  }
  return instance;
};
