import path from 'path';
import { <PERSON>gger<PERSON>nterface, wwgLogger } from '../../wwgLogger';
import { ObjectId } from 'bson';
import fs from 'fs';
import fsAsync from 'fs/promises';
import readline from 'readline';
import Initiative, { InitiativeModel } from '../../../models/initiative';
import User, { LeanUserModel, UserModel } from '../../../models/user';
import Survey, { SurveyModelPlain } from '../../../models/survey';
import { CloneFilename } from './CloneExporter';
import UniversalTracker, { UniversalTrackerPlain } from '../../../models/universalTracker';
import MetricGroup, { MetricGroupPlain } from '../../../models/metricGroup';
import UniversalTrackerValue, { UniversalTrackerValuePlain } from '../../../models/universalTrackerValue';
import ValueList, { ValueListModel } from '../../../models/valueList';
import envConfig from '../../../config';
import { UserRoles } from '../../user/userPermissions';
import { downloadFile } from '../../file/file';
import { exec } from 'child_process';
import { promisify } from 'util';
import { AnyBulkWriteOperation } from 'mongoose';
const execPromise = promisify(exec);

type Clone<T extends { _id: string }> = Omit<T, '_id'> & Partial<Pick<T, '_id'>> & { __v?: number };

type ForeignId = string;
type LocalId = string;
type ImportMap = {
  complete: boolean;
  map: {
    [key: ForeignId]: LocalId;
  };
  stats: {
    imported: number;
    mapped: number;
    updated: number;
    deleted: number;
    failed: number;
    skipped: number;
  };
  errors: string[];
}

interface Results {
  config: {
    appEnv: string;
    sourceId: string;
  };
  success: boolean;
  rootForeignId?: string;
  rootLocalId?: string;
  filePath: string;
  folderPath: string;
  reportingLevelMap: ImportMap;
  userMap: ImportMap;
  surveyMap: ImportMap;
  customMetricGroupMap: ImportMap;
  universalTrackerMap: ImportMap;
  utrvMap: ImportMap;
  valueListMap: ImportMap;
  error?: string;
}

const getInitialImportMap = (): ImportMap => ({
  complete: false,
  map: {},
  errors: [],
  stats: {
    imported: 0,
    mapped: 0,
    updated: 0,
    deleted: 0,
    failed: 0,
    skipped: 0,
  }
});

const ALLOWED_ENVS = ['development', 'staging', 'nightly' ];
const BATCH_SIZE = 100;

interface CloneImporterProps {
  fileUrl: string;
  userId: string;
  addTaskLog: (message: string, config?: { lossy?: boolean }) => Promise<boolean>;
}

class CloneImporter {
  private fakeEmail = 'dev+?@g17.eco';
  private addTaskLog: (message: string, config?: { lossy?: boolean }) => Promise<boolean> = () => Promise.resolve(true);

  constructor(private logger: LoggerInterface) {}

  private async log(message: string) {
    this.logger.info(message);
    return this.addTaskLog(message, { lossy: false });
  }

  private async lossyLog(message: string) {
    const log = await this.addTaskLog(message, { lossy: true });
    if (log) {
      this.logger.info(message);
    }
  }

  private downloadFile = async (fileUrl: string) => {
    await this.log('Downloading clone file...');

    // Extract filename from fileURL, ensuring filtering out any query parameters
    const parseURL = new URL(fileUrl);
    const filename = path.basename(parseURL.pathname);

    // If file extension is not `.g17` then throw an error
    if (!filename || !filename.endsWith('.g17')) {
      throw new Error('Invalid file extension. Please provide a valid clone file url.');
    }

    // Download the file from the provided URL
    const filePath = `/tmp/${filename}`;
    await downloadFile(fileUrl, filePath);
    return { filePath };
  }

  public import = async ({ fileUrl, userId, addTaskLog }: CloneImporterProps) => {

    if (!ALLOWED_ENVS.includes(envConfig.appEnv)) {
      throw new Error(`Disallowed Environment for Cloning Import: ${envConfig.appEnv}`);
    }

    this.addTaskLog = addTaskLog;

    const { filePath } = await this.downloadFile(fileUrl);

    await this.log(`Importing organization from ${filePath}`);

    const results: Results = {
      config: {
        appEnv: '',
        sourceId: '',
      },
      success: false,
      rootForeignId: '',
      rootLocalId: '',
      filePath,
      folderPath: '',
      reportingLevelMap: getInitialImportMap(),
      userMap: getInitialImportMap(),
      surveyMap: getInitialImportMap(),
      customMetricGroupMap: getInitialImportMap(),
      universalTrackerMap: getInitialImportMap(),
      utrvMap: getInitialImportMap(),
      valueListMap: getInitialImportMap(),
    };

    try {

      const user = await User.findById(userId).orFail().exec();
      await this.log('Clone initiated by user: ' + user.firstName);

      results.folderPath = await this.extract(filePath);

      const metadata = await fsAsync.readFile(path.join(results.folderPath, 'metadata.json'));
      const metadataJson = JSON.parse(metadata.toString());
      results.config.sourceId = String(metadataJson?.sourceInitiativeId);
      results.config.appEnv = String(metadataJson?.sourceAppEnv ?? envConfig.appEnv);

      // Import all reporting levels
      const { rootForeignId, rootLocalId } = await this.importReportingLevels(results);
      results.rootForeignId = rootForeignId;
      results.rootLocalId = rootLocalId;

      // Import all users
      await this.importUsers(results);

      // Add current user to the new root initiative
      await this.givePermissionToCurrentUser(user, rootLocalId);

      // Import all value lists
      await this.importValueLists(results);

      // Import all a lookup of UTR codes to ids
      await this.populateUniversalTrackerMap(results);

      // Import all custom metrics
      // Do this after UTR Map, as it may override UTR map and we want this to win
      await this.importCustomMetrics(results);

      // Import all custom metric groups
      await this.importCustomMetricGroups(results);

      // Import all surveys, except for UTRVs and survey references
      await this.importSurveys(results);

      // Import all universal tracker values
      await this.importUniversalTrackerValues(results);

      // Now that UTRVs are imported, we can update the surveys
      await this.updateSurveyUtrvs(results);

      results.success = true;
    }
    catch(error) {
      results.error = String(error);
    }
    finally {
      await this.cleanup(results);
    }

    // Do regenerate on all surveys?
    return results;
  }

  private cleanup = async (results: Results) => {
    await this.log('Cleaning up');
    const { folderPath } = results;
    if (folderPath) {
      await fsAsync.rm(folderPath, { recursive: true });
    }
    await fsAsync.unlink(results.filePath);
  }

  private givePermissionToCurrentUser = async (user: UserModel, initiativeId?: string) => {
    if (!initiativeId) {
      throw new Error('Cannot give permissions to current user without an initiativeId');
    }

    await this.log('Granting permissions to clone import user');
    user.permissions.push({
      initiativeId: new ObjectId(initiativeId),
      permissions: [UserRoles.Owner, UserRoles.Manager, UserRoles.User, UserRoles.Contributor, UserRoles.Verifier, UserRoles.Viewer],
    });
    return user.save();
  }

  private getReversibleCode = (config: Results['config'], foreignId: string) => {
    return `${config.appEnv}/parent/${config.sourceId}/${foreignId}`;
  }

  private getReversibleEmail = (config: Results['config'], foreignId: string) => {
    const cloneRef = `clone/${this.getReversibleCode(config, foreignId)}`;
    return this.fakeEmail.replace('?', cloneRef);
  }

  private importReportingLevels = async (results: Results) => {
    await this.log('Importing reporting levels');
    const { config, folderPath } = results;
    const filename = CloneFilename.ReportingLevels;

    let linesInFile = 0;
    let linesImported = 0;

    const reportingLevelsByParentId: { [key: string]: InitiativeModel<string>[] } = {};
    // We need to execute this in the tree-order,
    // So first we extract all the lines into a parentId map, then we import them recursively
    await this.readFileByLine<InitiativeModel<string>>(
      { folderPath, filename, populateMap: false },
      results.reportingLevelMap,
      async ({ data }) => {
        linesInFile++;
        const parentId = data.parentId ?? 'root';
        if (!reportingLevelsByParentId[parentId]) {
          reportingLevelsByParentId[parentId] = [];
        }
        reportingLevelsByParentId[parentId].push(data);
        return undefined;
      }
    );

    const importByParentId = async (reportingLevels: InitiativeModel<string>[] | undefined, parentId?: string) => {
      if (!reportingLevels) {
        // Leaf node
        return;
      }

      for (const reportingLevel of reportingLevels) {
        const foreignId = reportingLevel._id;
        linesImported++;
        const reversibleCode = this.getReversibleCode(config, foreignId);

        const data = { ...reportingLevel } as Clone<InitiativeModel<string>>;

        // TO REMAP
        data.code = reversibleCode;
        data.parentId = parentId;
        data.name = `${data.name} (cloned)`;

        // TO CLEAR AS FOR CURRENT USE-CASE NOT NEEDED
        delete data._id;
        delete data.metadata;
        delete data.managedBy;
        delete data.initiativeGroupId;
        delete data.organizationId;
        delete data.customer;

        data.linkedUniversalTrackers = [];

        const doc = await Initiative.findOneAndUpdate(
          { code: reversibleCode },
          { $set: data },
          { upsert: true, new: true },
        );
        if (doc.isNew) {
          results.reportingLevelMap.stats.imported++;
        } else {
          results.reportingLevelMap.stats.updated++;
        }
        const localId = String(doc._id);
        results.reportingLevelMap.map[foreignId] = localId;
        await importByParentId(reportingLevelsByParentId[foreignId], localId);
      }
    };

    // Now we import the reporting levels in the tree-order
    await this.log('Will start to import in a tree structure...');
    await importByParentId(reportingLevelsByParentId['root'], undefined);

    if (linesImported !== linesInFile) {
      throw new Error(`Not all reporting levels were imported. Imported ${linesImported} out of ${linesInFile}`);
    }

    results.reportingLevelMap.complete = true;

    const rootForeignId = reportingLevelsByParentId['root'][0]._id;
    const rootLocalId = results.reportingLevelMap.map[rootForeignId];
    return { rootForeignId, rootLocalId };
  }

  private importUsers = async (results: Results) => {
    await this.log('Importing users');
    const { config, folderPath, reportingLevelMap } = results;
    if (!reportingLevelMap.complete) {
      throw new Error('Cannot import users before reporting levels');
    }

    const filename = CloneFilename.Users;
    await this.readFileByLine<LeanUserModel<string>>(
      { folderPath, filename },
      results.userMap,
      async ({ clonedData, foreignId }) => {
        const reversibleEmail = this.getReversibleEmail(config, foreignId);

        if (clonedData.oktaUserId) {
          // oktaUserId is a unique column.
          // If it exists in this different environment, then maybe use that user instead of fake user
          const oktaUser = await User.findOne({ oktaUserId: clonedData.oktaUserId }).exec();
          if (oktaUser) {

            clonedData.permissions.forEach((permission) => {
              const localInitiativeId = new ObjectId(reportingLevelMap.map[permission.initiativeId]);

              const hasPermission = oktaUser.permissions.some((p) => p.initiativeId.equals(localInitiativeId));
              if (!hasPermission) {
                oktaUser.permissions.push({
                  ...permission,
                  initiativeId: localInitiativeId,
                });
              }
            });
            await oktaUser.save();
            results.userMap.stats.updated++;
            return oktaUser._id;
          }
        }

        delete clonedData.organizationId;
        // delete clonedData.scorecardFavourites;
        clonedData.email = reversibleEmail;

        clonedData.permissions = clonedData.permissions.map((permission) => ({
            ...permission,
            initiativeId: reportingLevelMap.map[permission.initiativeId],
        }));

        const doc = await User.findOneAndUpdate(
          { email: reversibleEmail },
          { $set: clonedData },
          { upsert: true, new: true },
        );

        if (doc.isNew) {
          results.userMap.stats.imported++;
        } else {
          results.userMap.stats.updated++;
        }
        return doc._id;
      }
    );

    results.userMap.complete = true;
  }

  private importValueLists = async (results: Results) => {
    await this.log('Importing value lists');
    const { config, folderPath } = results;

    const filename = CloneFilename.ValueLists;
    await this.readFileByLine<ValueListModel<string>>(
      { folderPath, filename },
      results.userMap,
      async ({ clonedData, foreignId }) => {
        const foreignCode = clonedData.code;
        const reversibleCode = this.getReversibleCode(config, foreignId);
        clonedData.code = reversibleCode;

        const existingValueList = await ValueList.findOne({ code: foreignCode }).lean();
        if (existingValueList) {
          // This may be shared, so don't touch, just return it.
          return existingValueList._id;
        }

        const doc = await ValueList.findOneAndUpdate(
          { code: reversibleCode },
          { $set: clonedData },
          { upsert: true, new: true },
        );

        if (doc.isNew) {
          results.valueListMap.stats.imported++;
        } else {
          results.valueListMap.stats.updated++;
        }
        return doc._id;
      }
    );
    results.valueListMap.complete = true;
  }

  private populateUniversalTrackerMap = async (results: Results) => {
    await this.log('Populating UTR map');
    const { folderPath } = results;
    const utrCodes: { [key: string]: string } = {};

    const filename = CloneFilename.UniversalTrackers;
    await this.readFileByLine<Pick<UniversalTrackerPlain<string>, '_id' | 'code'>>(
      { folderPath, filename, populateMap: false },
      results.universalTrackerMap,
      async ({ data }) => {
        utrCodes[data.code] = data._id;
        return undefined;
      }
    );

    const utr = await UniversalTracker.find({ code: { $in: Object.keys(utrCodes) }}).lean();
    for (const doc of utr) {
      const localId = String(doc._id);
      const foreignId = utrCodes[doc.code];
      results.universalTrackerMap.map[foreignId] = localId;
      results.universalTrackerMap.stats.mapped++;
    }
  }

  private importCustomMetrics = async (results: Results) => {
    await this.log('Importing custom metrics');
    const { folderPath } = results;
    if (!results.valueListMap.complete) {
      throw new Error('Cannot import custom metrics before value lists');
    }

    const filename = CloneFilename.CustomMetrics;
    await this.readFileByLine<UniversalTrackerPlain<string>>(
      { folderPath, filename },
      results.universalTrackerMap,
      async ({ clonedData, foreignId }) => {
        const ownerId = clonedData.ownerId;
        if (!ownerId) {
          throw new Error(`Missing ownerId for custom metric ${foreignId}`);
        }
        const localOwnerId = results.reportingLevelMap.map[ownerId];
        if (!localOwnerId) {
          throw new Error(`Owner not found for custom metric ${foreignId}`);
        }

        clonedData.ownerId = String(localOwnerId);

        if (clonedData.valueValidation) {
          if (clonedData.valueValidation.valueList?.listId) {
            const foreignListId = clonedData.valueValidation.valueList.listId;
            clonedData.valueValidation.valueList.listId = new ObjectId(results.valueListMap.map[String(foreignListId)]);
          }
          if (clonedData.valueValidation.table) {
            for (const tableColumn of clonedData.valueValidation.table.columns ?? []) {
              tableColumn.listId = new ObjectId(results.valueListMap.map[String(tableColumn.listId)]);
            }
          }
        }
        const existing = await UniversalTracker.findOne({ code: clonedData.code }, { code: 1, ownerId: 1 }).lean();
        if (existing) {
          // This may be shared, so don't touch, just return it.
          if (!existing.ownerId || existing.ownerId.toHexString() !== clonedData.ownerId) {
            return existing._id;
          }
        }

        const doc = await UniversalTracker.findOneAndUpdate(
          { code: clonedData.code, ownerId: clonedData.ownerId },
          { $set: clonedData },
          { upsert: true, new: true },
        );
        if (doc.isNew) {
          results.universalTrackerMap.stats.imported++;
        } else {
          results.universalTrackerMap.stats.updated++;
        }
        return doc._id;
      }
    );
    results.universalTrackerMap.complete = true;
  }

  private importCustomMetricGroups = async (results: Results) => {
    await this.log('Importing custom metric groups');
    const { folderPath } = results;
    if (!results.reportingLevelMap.complete || !results.userMap.complete) {
      throw new Error('Cannot import custom metric groups before reporting levels and users');
    }

    const filename = CloneFilename.CustomMetricGroups;

    // We don't have a metric group code, or reliable way to upsert docs, so we delete all and start again
    // @TODO would be better to make this more reliable?
    const deleted = await MetricGroup.deleteMany({ initiativeId: { $in: Object.values(results.reportingLevelMap.map) } });
    results.customMetricGroupMap.stats.deleted = deleted.deletedCount;

    await this.readFileByLine<MetricGroupPlain<string>>(
      { folderPath, filename },
      results.customMetricGroupMap,
      async ({ clonedData }) => {
        clonedData.initiativeId = results.reportingLevelMap.map[clonedData.initiativeId];

        const utrsBefore = clonedData.universalTrackers.length;

        clonedData.universalTrackers = clonedData.universalTrackers.map(foreignId => results.universalTrackerMap.map[foreignId]).filter(Boolean);
        clonedData.share = [];
        clonedData.createdBy = results.userMap.map[clonedData.createdBy];

        const utrsAfter = clonedData.universalTrackers.length;

        if (utrsBefore !== utrsAfter) {
          throw new Error(`Some universal trackers not found for custom metric group ${clonedData._id}. Aborting.`);
        }

        const doc = new MetricGroup(clonedData);
        await doc.save();
        if (doc.isNew) {
          results.customMetricGroupMap.stats.imported++;
        } else {
          results.customMetricGroupMap.stats.updated++;
        }
        return doc._id;
      }
    );
    results.customMetricGroupMap.complete = true;
  }

  private getFakeEvidenceId = () => {
    // @TODO - replace with a 'real' fake doc id
    return String(new ObjectId());
  }

  private getExistingUtrvs = async (reportingLevelMap: ImportMap) => {
    const existingMap: { [key: string]: string } = {};

    const UTRV_BATCH_SIZE = 100000;

    let skip = 0;
    let hasMore = true;
    while (hasMore) {
      const existing: Pick<UniversalTrackerValuePlain, '_id' | 'universalTrackerId' | 'compositeData'>[] = await UniversalTrackerValue.find(
        { initiativeId: { $in: Object.values(reportingLevelMap.map) }},
        { universalTrackerId: 1, compositeData: 1, _id: 1 },
        { skip, limit: UTRV_BATCH_SIZE }
      ).lean();

      if (existing.length > 0) {
        for (const doc of existing) {
          if (!doc.universalTrackerId || !doc.compositeData?.surveyId) {
            continue;
          }
          const key = `${String(doc.universalTrackerId)}-${String(doc.compositeData?.surveyId)}`;
          existingMap[key] = String(doc._id);
        }
        skip += UTRV_BATCH_SIZE; // Move to the next batch
      } else {
        hasMore = false; // No more documents to process
      }
    }

    await this.lossyLog(`Found ${Object.keys(existingMap).length} existing UTRVs`);
    return existingMap;
  };

  private importUniversalTrackerValues = async (results: Results) => {
    await this.log('Importing universal tracker values');
    const { folderPath, reportingLevelMap, universalTrackerMap, userMap, surveyMap } = results;
    if (!reportingLevelMap.complete || !universalTrackerMap.complete || !userMap.complete || !surveyMap.complete) {
      throw new Error('Cannot import universal tracker values before reporting levels, universal trackers, surveyMap and users');
    }

    const filename = CloneFilename.Utrvs;

    // Find all existing UTRVs
    await this.log(`Checking existing UTRVs`);
    const existingMap = await this.getExistingUtrvs(reportingLevelMap);

    let bulkOps: { foreignId: string, op: AnyBulkWriteOperation }[] = [];

    const batchSave = async () => {
      const bulkResult = await UniversalTrackerValue.bulkWrite(bulkOps.map(b => b.op));
      if (bulkResult.insertedCount !== bulkOps.length) {
        throw new Error(`Failed to bulk insert ${bulkOps.length} UTRVs`);
      }

      results.utrvMap.stats.imported += bulkResult.insertedCount;
      results.utrvMap.stats.updated += bulkResult.modifiedCount;
      results.utrvMap.stats.failed += bulkOps.length - bulkResult.insertedCount;

      for(const [id, localId] of Object.entries(bulkResult.insertedIds)) {
        const index = Number(id);
        const bulkOp = bulkOps[index];
        const foreignId = bulkOp.foreignId;
        results.utrvMap.map[foreignId] = String(localId);
      }

      bulkOps = [];
    }

    await this.log(`Starting UTRV import...`);
    await this.readFileByLine<UniversalTrackerValuePlain<string>>(
      { folderPath, filename, populateMap: false },
      results.utrvMap,
      async ({ clonedData, foreignId }) => {

        const localInitiativeId = reportingLevelMap.map[clonedData.initiativeId];
        const localUniversalTrackerId = universalTrackerMap.map[clonedData.universalTrackerId];
        const localSurveyId = clonedData.compositeData?.surveyId ? surveyMap.map[clonedData.compositeData.surveyId] : undefined;

        const existingKey = `${localUniversalTrackerId}-${localSurveyId}`;
        const isExisting = existingMap[existingKey];
        if (isExisting) {
          // Skip
          results.utrvMap.stats.skipped++;
          results.utrvMap.map[foreignId] = isExisting;
          return undefined;
        }

        clonedData.initiativeId = localInitiativeId;
        clonedData.universalTrackerId = localUniversalTrackerId;
        if (clonedData.compositeData) {
          if (localSurveyId) {
            clonedData.compositeData.surveyId = localSurveyId;
          }
          if (clonedData.compositeData.fragmentUtrvs) {
            clonedData.compositeData.fragmentUtrvs = clonedData.compositeData.fragmentUtrvs.map((utrvId) => results.utrvMap.map[utrvId]);
          }
        }

        clonedData.sourceItems = undefined; // @TODO - Don't think we need this?

        clonedData.history = clonedData.history.map(({ userId, ...history }) => {
          const fakeEvidenceId = this.getFakeEvidenceId();
          return {
            ...history,
            utrvAssuranceId: undefined,
            userId: userMap.map[userId],
            evidence: history.evidence?.map(() => fakeEvidenceId),
            evidenceData: history.evidence?.map(() => ({ documentId: fakeEvidenceId })),
          };
        });

        if (clonedData.stakeholders) {
          clonedData.stakeholders = {
            stakeholder: clonedData.stakeholders.stakeholder.map((userId) => userMap.map[userId]),
            verifier: clonedData.stakeholders.verifier.map((userId) => userMap.map[userId]),
            escalation: clonedData.stakeholders.escalation.map((userId) => userMap.map[userId]),
          };
        }

        bulkOps.push({ foreignId, op: { insertOne: { document: clonedData }}});
        if (bulkOps.length >= BATCH_SIZE) {
          await batchSave();
        }
        return undefined;
      }
    ).then(() => {
      return batchSave(); // Flush any remaining
    });

    results.utrvMap.complete = true;
  }

  private importSurveys = async (results: Results) => {
    await this.log('Importing surveys');
    const { config, folderPath, userMap, customMetricGroupMap, reportingLevelMap } = results;
    if (!userMap.complete || !customMetricGroupMap.complete || !reportingLevelMap.complete) {
      throw new Error('Cannot import surveys before users, custom metric groups and reporting levels');
    }

    const filename = CloneFilename.Surveys;
    await this.readFileByLine<SurveyModelPlain<string>>(
      { folderPath, filename },
      results.surveyMap,
      async ({ clonedData, foreignId }) => {
        const reversibleCode = this.getReversibleCode(config, foreignId);

        clonedData.code = reversibleCode;
        clonedData.initiativeId = reportingLevelMap.map[clonedData.initiativeId];

        if (clonedData.scope) {
          clonedData.scope = {
            ...clonedData.scope,
            custom: clonedData.scope.custom.map(foreignId => customMetricGroupMap.map[foreignId]).filter(Boolean),
          };
        }

        if (clonedData.stakeholders) {
          clonedData.stakeholders = {
            stakeholder: clonedData.stakeholders.stakeholder.map((userId) => userMap.map[userId]).filter(Boolean),
            verifier: clonedData.stakeholders.verifier.map((userId) => userMap.map[userId]).filter(Boolean),
            escalation: clonedData.stakeholders.escalation.map((userId) => userMap.map[userId]).filter(Boolean),
          };
        }

        if (clonedData.visibleStakeholders) {
          clonedData.visibleStakeholders = clonedData.visibleStakeholders.map((userId) => userMap.map[userId]).filter(Boolean);
        }

        if (clonedData.roles) {
          clonedData.roles = {
            admin: clonedData.roles.admin.map((userId) => userMap.map[String(userId)]).filter(Boolean).map(id => new ObjectId(id)),
            viewer: clonedData.roles.viewer.map((userId) => userMap.map[String(userId)]).filter(Boolean).map(id => new ObjectId(id))
          };
        }
        if (clonedData.creatorId) {
          clonedData.creatorId = userMap.map[clonedData.creatorId];
        }
        if (clonedData.blueprint?.forms) {
          clonedData.blueprint.forms = clonedData.blueprint.forms.map(form => {
            if (!form.utrGroupConfig?.groupId) {
              return form;
            }
            const groupId = String(form.utrGroupConfig.groupId);
            const localGroupId = customMetricGroupMap.map[groupId];
            form.utrGroupConfig = {
              ...form.utrGroupConfig,
              groupId: new ObjectId(localGroupId),
            };
            return form;
          });
        }

        const doc = await Survey.findOneAndUpdate(
          { code: reversibleCode },
          { $set: clonedData },
          { upsert: true, new: true },
        );

        if (doc.isNew) {
          results.surveyMap.stats.imported++;
        } else {
          results.surveyMap.stats.updated++;
        }
        return doc._id;
      }
    );
    results.surveyMap.complete = true;
  }

  private updateSurveyUtrvs = async (results: Results) => {
    await this.log('Updating surveys with UTRVs');
    const { utrvMap, surveyMap } = results;
    if (!utrvMap.complete || !surveyMap.complete) {
      throw new Error('Cannot update before surveys and universal tracker values.');
    }

    const remapUtrv = (utrvId: ObjectId) => utrvMap.map[String(utrvId)];

    let lineNum = 0;
    for (const surveyId of Object.values(surveyMap.map)) {
      const survey = await Survey.findById(surveyId).exec();
      if (!survey) {
        throw new Error(`Survey not found for id ${surveyId} for update.`);
      }

      lineNum++;
      if (lineNum % 100 === 0) {
        await this.lossyLog(`Importing ${lineNum} of ${Object.keys(surveyMap.map).length}`);
      }

      if (survey.references) {
        survey.references = survey.references.map((reference) => ({
            ...reference,
            surveyId: new ObjectId(results.surveyMap.map[reference.surveyId.toHexString()]),
          })
        );
      }
      if (survey.sourceItems) {
        survey.sourceItems = survey.sourceItems.map((sourceItem) => ({
            ...sourceItem,
            sourceId: new ObjectId(results.surveyMap.map[sourceItem.sourceId.toHexString()]),
          })
        );
      }

      survey.visibleUtrvs = survey.visibleUtrvs.map(remapUtrv).filter(Boolean).map(id => new ObjectId(id));
      survey.compositeUtrvs = survey.compositeUtrvs.map(remapUtrv).filter(Boolean).map(id => new ObjectId(id));
      survey.fragmentUtrvs = survey.fragmentUtrvs.map(remapUtrv).filter(Boolean).map(id => new ObjectId(id));
      survey.disabledUtrvs = survey.disabledUtrvs.map(remapUtrv).filter(Boolean).map(id => new ObjectId(id));
      survey.subFragmentUtrvs = survey.subFragmentUtrvs.map(remapUtrv).filter(Boolean).map(id => new ObjectId(id));

      await survey.save();
    }
  }

  private extract = async (zipFilePath: string) => {
    await this.log(`Extracting zip file ${zipFilePath}`);
    const outputFolderPath = `${zipFilePath}_extracted`;

    if (fs.existsSync(outputFolderPath)) {
      throw new Error(`Zip extracting folder already exists. Aborting: ${zipFilePath}`);
    }

    await fsAsync.mkdir(outputFolderPath, { recursive: true });

    this.logger.info(`Extracting files to ${outputFolderPath}`);

    try {
      await execPromise(`unzip -o ${zipFilePath} -d ${outputFolderPath}`);
      this.logger.info('Extraction complete');
      return outputFolderPath;
    } catch (err) {
      this.logger.error(`Error extracting zip file ${zipFilePath}`, err);
      throw err;
    }
  };

  private readFileByLine = async <ExpectedDoc extends { _id: string }>(
    { folderPath, filename, populateMap = true }: { folderPath: string, filename: string, populateMap?: boolean },
    importMap: ImportMap,
    processLine: ({data, clonedData, foreignId }: {data: ExpectedDoc, clonedData: Clone<ExpectedDoc>, foreignId: string }) => Promise<ObjectId | undefined>
  ): Promise<void> => {

    const filePath = path.join(folderPath, filename);
    const fileStream = fs.createReadStream(filePath);

    const rl = readline.createInterface({
      input: fileStream,
      crlfDelay: Infinity, // Recognize all instances of CR LF ('\r\n') as a single line break
    });

    let lineNum = 0;
    for await (const line of rl) {
      lineNum++;
      if (lineNum % 100 === 0) {
        await this.lossyLog(`Reading ${filename}: Line ${lineNum}`);
      }

      try {
        if (!line) {
          continue;
        }
        const data = JSON.parse(line) as ExpectedDoc;
        const foreignId = data._id;
        const clonedData: Clone<ExpectedDoc> = { ...data };
        delete clonedData._id;
        delete clonedData.__v;

        const localId = await processLine({ data, clonedData, foreignId });
        if (populateMap && localId) {
          importMap.map[foreignId] = String(localId);
        }
      } catch (error) {
        const errorMessage = `Error processing data on line ${lineNum}: ${String(error.message)}. Skipping.`;
        this.logger.error(errorMessage);
        importMap.errors.push(errorMessage);
        throw error;
      }
    }

    await this.log(`Finished reading ${lineNum} lines`);
    rl.close();
  };
}

let instance: CloneImporter;
export const getCloneImporter = () => {
  if (!instance) {
    instance = new CloneImporter(wwgLogger);
  }
  return instance;
};
