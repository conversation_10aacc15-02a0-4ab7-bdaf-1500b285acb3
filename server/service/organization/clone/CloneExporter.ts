import path from 'path';
import { InitiativeRepository } from '../../../repository/InitiativeRepository';
import { LoggerInterface, wwgLogger } from '../../wwgLogger';
import { ObjectId } from 'bson';
import fs from 'fs/promises';
import { SurveyRepository } from '../../../repository/SurveyRepository';
import { UniversalTrackerRepository } from '../../../repository/UniversalTrackerRepository';
import { UniversalTrackerValueRepository } from '../../../repository/UniversalTrackerValueRepository';
import { MetricGroupRepository } from '../../../repository/MetricGroupRepository';
import ValueList from '../../../models/valueList';
import UniversalTracker from '../../../models/universalTracker';
import config from '../../../config';
import { EOL } from 'os';
import User from '../../../models/user';
import { getWorkflowFileStorage, WorkflowFileStorage } from '../../survey/workflow/WorkflowFileStorage';
import { createWriteStream } from 'fs';
import archiver from 'archiver';

export enum CloneFilename {
  ReportingLevels = 'reporting-levels.json',
  Users = 'users.json',
  Surveys = 'surveys.json',
  CustomMetricGroups = 'custom-metrics-groups.json',
  CustomMetrics = 'custom-metrics.json',
  UniversalTrackers = 'universal-trackers.json',
  Utrvs = 'universal-tracker-values.json',
  ValueLists = 'value-lists.json',
}

interface CloneExporterProps {
  initiativeId: string;
  remoteFolder: string;
  filename: string;
  addTaskLog: (message: string, config?: { lossy?: boolean }) => Promise<boolean>;
}

class CloneExporter {
  private path = '/tmp';
  private addTaskLog: (message: string, config?: { lossy?: boolean }) => Promise<boolean> = () => Promise.resolve(true);

  constructor(
    private logger: LoggerInterface,
    private fileStorage: WorkflowFileStorage
  ) {}

  private async log(message: string) {
    this.logger.info(message);
    return this.addTaskLog(message, { lossy: false });
  }

  private async lossyLog(message: string) {
    const log = await this.addTaskLog(message, { lossy: true });
    if (log) {
      this.logger.info(message);
    }
  }

  export = async (props: CloneExporterProps) => {
    const { initiativeId, remoteFolder, filename, addTaskLog } = props;

    this.addTaskLog = addTaskLog;

    const folderPath = `${this.path}/${initiativeId}`;

    // Ensure the folder exists
    await fs.mkdir(folderPath, { recursive: true });

    await this.log(`Exporting organization ${initiativeId}`);
    // Export all reporting levels

    const { initiativeIds } = await this.exportInitiativeTree({ folderPath, initiativeId });

    // Export all users
    const { userIds } = await this.exportUsers({ folderPath, initiativeIds });

    // Export all surveys
    const { surveyIds } = await this.exportSurveys({ folderPath, initiativeIds });

    // Export all universal tracker values
    const { universalTrackerValueIds, universalTrackerIds } = await this.exportUniversalTrackerValues({ folderPath, initiativeIds });

    // Export a reference to any UTRs used, and it's ID so it can be remapped after clone
    await this.exportUniversalTrackers({ folderPath, universalTrackerIds });

    // Export all custom metric groups
    const { customMetricGroupIds } = await this.exportCustomMetricGroups({ folderPath, initiativeIds });

    // Export all custom metrics
    const { customMetricIds, valueListIds } = await this.exportCustomMetrics({ folderPath, initiativeIds });

    // Export all valueLists
    await this.exportValueLists({ folderPath, valueListIds });

    await this.createMetadataFile({ folderPath,
      data: {
        date: new Date(),
        sourceAppEnv: config.appEnv,
        sourceInitiativeId: initiativeId,
        release: config.release,
        userIds,
        surveyIds,
        customMetricGroupIds,
        customMetricIds,
        universalTrackerValueIds,
        universalTrackerIds
      }
    });

    const tmpZipFile = `/tmp/${filename}`;
    await this.zip(folderPath, tmpZipFile);

    const remoteFilePath = await this.upload({ source: tmpZipFile, remoteFolder, filename });

    await this.cleanup(folderPath, tmpZipFile);

    await this.log('COMPLETE');

    return { remoteFilePath };
  }

  private async upload({
    source,
    filename,
    remoteFolder,
  }: {
    source: string;
    filename: string;
    remoteFolder: string;
  }) {
    await this.log('Uploading to cloud storage...');
    const { path: filePath } = await this.fileStorage.uploadFile({
      source,
      destination: path.join(remoteFolder, filename),
      contentType: 'application/zip'
    });
    await this.log(`Uploaded to: ${filePath}`);
    return filePath;
  }

  private cleanup = async (folderPath: string, tmpZipFile: string) => {
    await this.log('Cleaning up...');
    await fs.rm(folderPath, { recursive: true });
    await fs.rm(tmpZipFile);
  }

  private createMetadataFile = async ({ folderPath, data }: { folderPath: string, data: object }) => {
    await this.log('Creating metadata file');
    await this.write({ data: [data], folderPath, filename: 'metadata.json', pretty: true });
  }

  private exportInitiativeTree = async ({ folderPath, initiativeId}: { folderPath: string, initiativeId: string }): Promise<{ initiativeIds: ObjectId[] }> => {
    await this.log('Exporting reporting levels');
    const initiatives = await InitiativeRepository.getAllChildrenById(initiativeId, undefined, { fakeProp: 0 }); // fakeProp will override projections to return everything instead
    if (initiatives.length === 0) {
      throw new Error('No reporting levels found');
    }

    const initialInitiative = initiatives.find((initiative) => initiative._id.toHexString() === initiativeId);
    if (initialInitiative?.parentId) {
      throw new Error(`Initiative has a parentId, so cannot be exported. Parent: ${initialInitiative.parentId}`);
    }
    await this.write({ data: initiatives, folderPath, filename: CloneFilename.ReportingLevels });
    return {
      initiativeIds: initiatives.map((initiative) => initiative._id),
    };
  }

  private exportUsers = async ({ folderPath, initiativeIds}: { folderPath: string, initiativeIds: ObjectId[] }) => {
    await this.log('Exporting users');
    const users = await User.find({ 'permissions.initiativeId': { $in: initiativeIds }}).lean();

    const initiativeIdStrings = initiativeIds.map(String);

    // Create clones of users to minimize risk of data leakage
    const fakeUsers = users.map((user) => {
      const userId = user._id.toHexString();
      return {
        _id: userId,
        title: user.title,
        firstName: userId.slice(0, 5),
        surname: userId.slice(-5),
        type: user.type,
        active: user.active,
        oktaUserId: user.oktaUserId,
        permissions: user.permissions.filter(p => initiativeIdStrings.includes(String(p.initiativeId))),
      }
    });

    await this.write({ data: fakeUsers, folderPath, filename: CloneFilename.Users });
    return {
      userIds: fakeUsers.map((user) => user._id),
    };
  }

  private exportSurveys = async ({ folderPath, initiativeIds}: { folderPath: string, initiativeIds: ObjectId[] }) => {
    await this.log('Exporting surveys');
    const surveys = await SurveyRepository.findSurveys({ initiativeId: { $in: initiativeIds } }, {}, true);
    await this.log(`Exported ${surveys.length} surveys`);
    await this.write({ data: surveys, folderPath, filename: CloneFilename.Surveys });

    return {
      surveyIds: surveys.map((survey) => survey._id),
    };
  }

  private exportCustomMetricGroups = async ({ folderPath, initiativeIds}: { folderPath: string, initiativeIds: ObjectId[] }) => {
    await this.log('Exporting custom metrics groups');
    const customMetricsGroups = await MetricGroupRepository.find({ initiativeId: { $in: initiativeIds } });
    await this.write({ data: customMetricsGroups, folderPath, filename: CloneFilename.CustomMetricGroups });
    const universalTrackerIds = customMetricsGroups.reduce((acc, cmg) => {
      cmg.universalTrackers.forEach((utrId) => acc.add(utrId.toHexString()));
      return acc;
    }, new Set<string>());

    return {
      customMetricGroupIds: customMetricsGroups.map((cmg) => cmg._id),
      universalTrackerIds: Array.from(universalTrackerIds),
    };
  }

  private exportCustomMetrics = async ({ folderPath, initiativeIds}: { folderPath: string, initiativeIds: ObjectId[] }) => {
    await this.log('Exporting custom metrics');
    const customMetrics = await UniversalTrackerRepository.find({ ownerId: { $in: initiativeIds } }, {}, { lean: true });

    const valueListIds = new Set<string>();
    customMetrics.forEach((cm) => {
      if (cm.valueValidation?.valueList?.listId) {
        valueListIds.add(cm.valueValidation.valueList.listId.toHexString());
      }
      if (cm.valueValidation?.table?.columns) {
        cm.valueValidation.table.columns.forEach((c) => {
          if (c.listId) {
            valueListIds.add(c.listId.toHexString());
          }
        });
      }
    });

    await this.write({ data: customMetrics, folderPath, filename: CloneFilename.CustomMetrics });
    return {
      customMetricIds: customMetrics.map((cm) => cm._id),
      valueListIds: Array.from(valueListIds.values())
    };
  }

  private exportValueLists = async ({ folderPath, valueListIds }: { folderPath: string, valueListIds: string[]  }) => {
    await this.log('Exporting value lists');
    const valueLists = await ValueList.find({ _id: { $in: valueListIds } }).lean();
    await this.write({ data: valueLists, folderPath, filename: CloneFilename.ValueLists });
  }

  private exportUniversalTrackers = async ({ folderPath, universalTrackerIds}: { folderPath: string, universalTrackerIds: string[] }) => {
    await this.log('Exporting UTR references');
    const universalTrackers = await UniversalTracker.find({ _id: { $in: universalTrackerIds } }, { _id: 1, code: 1 }).lean();
    // Only export the _id and code so that it can be mapped on import
    await this.write({ data: universalTrackers, folderPath, filename: CloneFilename.UniversalTrackers });
  }

  private exportUniversalTrackerValues = async ({ folderPath, initiativeIds}: { folderPath: string, initiativeIds: ObjectId[] }) => {
    await this.log('Exporting UTR values');

    const utrIds = new Set<string>();
    const utrvIds = new Set<string>();

    const CHUNK_SIZE = 10000;
    let skip = 0;
    let hasMore = true;
    let total = 0;

    while (hasMore) {
      const utrvs = await UniversalTrackerValueRepository.find(
        { initiativeId: { $in: initiativeIds } },
        {},
        { lean: true, skip, limit: CHUNK_SIZE }
      );

      if (utrvs.length > 0) {
        await this.write({ data: utrvs, folderPath, filename: CloneFilename.Utrvs, verbose: false });
        total += utrvs.length;
        skip += CHUNK_SIZE;
        utrvs.forEach((utrv) => {
          utrIds.add(utrv.universalTrackerId.toHexString());
          utrvIds.add(utrv._id.toHexString());
        });
        await this.lossyLog(`Running total: ${total} UTRVs`);
      } else {
        hasMore = false;
      }
    }
    await this.log(`Wrote: ${total} UTRVs`);

    return {
      universalTrackerIds: Array.from(utrIds),
      universalTrackerValueIds: Array.from(utrvIds),
    };
  };

  private write = async ({ data, folderPath, filename, pretty = false, verbose = true }: { data: {}[], folderPath: string, filename: string, pretty?: boolean, verbose?: boolean }) => {
    if (verbose) {
      await this.log(`Writing ${data.length} records to ${filename}`);
    }
    const filePath = path.join(folderPath, filename);
    const file = await fs.open(filePath, 'a');
    for (const record of data) {
      await file.appendFile(JSON.stringify(record, null, pretty ? 2 : undefined) + EOL);
    }
    await file.close();
  }

  private zip = async (folderPath: string, outputFilePath: string) => {
    await this.log(`Compressing ${folderPath}...`);

    await new Promise<void>((resolve, reject) => {
      const output = createWriteStream(outputFilePath);
      const archive = archiver('zip', {
        zlib: { level: 1 }
      });

      output.on('close', () => {
        this.logger.info(`Compressed ${archive.pointer()} total bytes to ${outputFilePath}`);
        resolve();
      });

      archive.on('error', (err) => {
        this.logger.error(`Error compressing ${folderPath}`, err);
        reject(err);
      });

      archive.pipe(output);

      // Append files from the folder to the archive
      archive.directory(folderPath, false);

      archive.finalize().catch(reject);
    });
  }
}

let instance: CloneExporter;
export const getCloneExporter = () => {
  if (!instance) {
    instance = new CloneExporter(
      wwgLogger,
      getWorkflowFileStorage()
    );
  }
  return instance;
};
