/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { RootInitiativeData } from '../../repository/InitiativeRepository';
import { mergeConfigScope } from './domainConfig';
import { InitiativeTypes } from '../../models/initiative';
import {
  consolidateSubscriptionFeatures,
  convertProductCodesToCustomScope,
  convertProductCodesToFeatures
} from '../payment/subscriptionCodes';
import { allowAccessStatuses, ProductCodes } from '../../models/customer';
import { FeatureDetails } from '@g17eco/core';
import { defaultCTBase } from '../app/company-tracker/defaultCTBase';
import { getCustomerManager } from '../payment/CustomerManager';
import { AppConfigProvider, getAppConfigProvider } from "../app/AppConfigProvider";
import { CustomScope } from "../../models/customScopeSchema";
import { getSponsorshipService } from "../referral/SponsorshipService";
import { trace } from "@opentelemetry/api";

interface SubscriptionConfig {
  scope?: CustomScope[];
  features?: FeatureDetails[];
}

type InitiativeSubSetup = Pick<RootInitiativeData, '_id' | 'customer' | 'referrals' | 'permissionGroup' | 'type' | 'tags'>;

export class SubscriptionManager {

  constructor(
    private appConfigProvider: AppConfigProvider,
    private sponsorshipService: ReturnType<typeof getSponsorshipService>,
    private customerManager: ReturnType<typeof getCustomerManager>,
  ) {
  }

  /**
   * The order is important here, as it will later be used to merge the scope
   * based on the type#code unique attributes
   */
  public async getSubscriptionConfig(rootInitiative: InitiativeSubSetup): Promise<Required<SubscriptionConfig>> {
    const tracer = trace.getTracer('getSubscriptionConfig');

    const permissionGroupProductCode = this.fromPermissionGroup(rootInitiative);
    const baseScope = await tracer.startActiveSpan('getSponsorshipScopes', async (span) => {
      // Priority (lower to highest), last one wins
      const customScopes = mergeConfigScope({
        currentScope: convertProductCodesToCustomScope([
          permissionGroupProductCode,
          ...(rootInitiative.tags ?? []),
        ]),
        updateScope: await this.sponsorshipService.getSponsorshipScopes(rootInitiative),
      });

      span.end();
      return customScopes;
    });

    const subscriptionConfig = await tracer.startActiveSpan('getFromSubscriptions', async (span) => {
      const config = await this.getFromSubscriptions(rootInitiative);
      span.end();
      return config;
    });

    return {
      scope: mergeConfigScope({
        currentScope: baseScope,
        updateScope: subscriptionConfig.scope,
      }),
      features: subscriptionConfig.features
    }
  }

  public fromPermissionGroup({ permissionGroup, type, appConfigCode }: Pick<RootInitiativeData, 'permissionGroup' | 'type' | 'appConfigCode'>): ProductCodes {
    if (!permissionGroup) {
      return defaultCTBase.productCode;
    }

    if (type === InitiativeTypes.Group) {
      return ProductCodes.PortfolioTracker
    }

    return this.appConfigProvider.getAppProductCode({
      permissionGroup,
      appConfigCode,
    });
  }

  public async getFromSubscriptions(rootInitiative: Pick<RootInitiativeData, '_id' | 'customer'>) {
    const codes: string[] = [];
    const features: FeatureDetails[] = [];

    const subscriptions = await this.customerManager.getSubscriptions(rootInitiative._id);
    subscriptions.forEach(r => {
      if (allowAccessStatuses.includes(r.status)) {
        r.items.forEach(item => {
          if (item.productCode) {
            codes.push(item.productCode);

            convertProductCodesToFeatures(item.productCode, item.quantity)
              .forEach((f) => features.push(f))
          }
        });
      }
    });

    return {
      scope: convertProductCodesToCustomScope(codes),
      features: consolidateSubscriptionFeatures(features),
    } satisfies SubscriptionConfig
  }
}

let instance: SubscriptionManager;
export const getSubscriptionManager = () => {
  if (!instance) {
    instance = new SubscriptionManager(
      getAppConfigProvider(),
      getSponsorshipService(),
      getCustomerManager(),
    );
  }
  return instance;
}
