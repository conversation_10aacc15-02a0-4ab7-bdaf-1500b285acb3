/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { allowAccessStatuses, Subscription, SubscriptionItem } from "../../models/customer";
import { RootInitiativeData } from "../../repository/InitiativeRepository";
import { getCustomerManager, SubscriptionProrationBehavior } from "../payment/CustomerManager";
import { LoggerInterface, wwgLogger } from "../wwgLogger";
import { getAppConfigProvider } from "../app/AppConfigProvider";
import ContextError from "../../error/ContextError";
import { BundleCodes, featureCodeToBundle, SubscriptionProductCode } from "../payment/subscriptionCodes";
import { getCurrencyService } from "../currency/CurrencyService";
import { InitiativeWithCustomer } from "../../models/initiative";
import { getFeatureUsageService, UsageDetails } from "./FeatureUsageService";
import { FeatureCode } from "@g17eco/core";
import { getUTCEndOf, timestampToISOString, toTimestamp } from "../../util/date";
import UserError from "../../error/UserError";
import Stripe from "stripe";
import { InvoiceLineItem, toInvoiceLines } from "../payment/StripeClient";
import { PriceInterval } from "../../types/subscription";

export interface UpgradeBasePlan {
  additionalUnits: number;
  interval?: PriceInterval;
}

interface UpgradePlan extends UpgradeBasePlan {
  featureCode: FeatureCode;
  productCode: SubscriptionProductCode;
  prorationDate: Date;
  prorationBehaviour: SubscriptionProrationBehavior;
}

interface ProductDetails {
  interval?: PriceInterval;
  price: number;
  flatAmount: number;
  currencySymbol: string;
  currency: string
}

interface CanUpgradeDetails {
  canUpgrade: true;
  usageDetails: UsageDetails;
  productDetails: ProductDetails;
}

export type UpgradeDetails = CanUpgradeDetails | { canUpgrade: false };

export interface UpgradeResult {
  details: {
    subscriptionId: string;
    itemId: string;
    quantity: number;
    total: number;
  };
}

export interface PreviewUpgradeResult {
  subscriptionId: string;
  status: Subscription['status'],
  periodStart: string;
  periodEnd: string;
  total: number;
  currency: string;
  currencySymbol?: string;
  lines: InvoiceLineItem[];
}

interface MainSubscription {
  rootInitiative: RootInitiativeData;
  upgradeDetails: UpgradePlan;
}

interface UpdateSubscriptionItem {
  item: SubscriptionItem;
  rootInitiative: InitiativeWithCustomer;
  upgradeDetails: UpgradePlan;
}

export class UpgradeService {

  constructor(
    private logger: LoggerInterface,
    private customerManager: ReturnType<typeof getCustomerManager>,
    private appConfigProvider: ReturnType<typeof getAppConfigProvider>,
    private currencyService: ReturnType<typeof getCurrencyService>,
    private featureUsageService: ReturnType<typeof getFeatureUsageService>,
  ) {

  }


  /**
   * Resolve based on the required  customer details
   */
  public async getCurrentDetails(
    rootInitiative: RootInitiativeData,
    featureCode: FeatureCode,
  ): Promise<UpgradeDetails> {


    const baseContext = {
      initiativeId: rootInitiative._id.toString(),
      customerId: rootInitiative.customer?.id,
      appConfigCode: rootInitiative.appConfigCode,
      featureCode,
    }

    this.logger.info(`Starting to get current details for featureCode ${featureCode}`, baseContext)

    const productCode = featureCodeToBundle(featureCode);

    const appConfig = await this.appConfigProvider.getByOrganization(rootInitiative)
    if (!appConfig) {
      this.logger.error(new ContextError(`Cannot upgrade ${rootInitiative.name} reporting level`, baseContext))
      return { canUpgrade: false };
    }

    const sub = await this.getAppProductSubscription(rootInitiative, appConfig.productCode);
    const price = await this.getPriceForSub(sub, productCode);

    if (price.tiers_mode !== 'volume' || price.tiers?.length !== 1) {
      this.logger.error(new ContextError(`Trying upgrade ${productCode} using a price that is not tiered`, {
        ...baseContext,
        priceId: price.id,
        tiers: price.tiers,
        tierMode: price.tiers_mode,
      }));

      return { canUpgrade: false };
    }

    // We're only dealing with a single tier for
    const [firstTier] = price.tiers;

    // Find current usage
    const usageDetails = await this.featureUsageService.getUsage({
      rootInitiative,
      featureCode,
    })

    if (usageDetails.limit === 0) {
      throw new ContextError(`Trying to upgrade a feature that have 0 limit`, {
        debugMessage: 'Essentially means this is disabled or unlimited',
        ...baseContext,
        usageDetails,
      })
    }

    return {
      canUpgrade: true,
      usageDetails,
      productDetails: {
        interval: price.recurring?.interval,
        currency: price.currency,
        currencySymbol: this.currencyService.toSymbol(price.currency),
        price: (firstTier.unit_amount ?? 0) / 100,
        flatAmount: (firstTier.flat_amount ?? 0 / 100),
      }
    } satisfies UpgradeDetails
  }

  /**
   * Assumption that we are dealing with existing sub with main product already.
   * It should contain interval for it.
   */
  private async getPriceForSub(sub: Subscription, productCode: BundleCodes) {
    // Check existing product code for the app or bundle
    const appSubItem = sub.items.find(item => item.productCode === productCode);

    if (appSubItem?.priceId) {
      return this.customerManager.getPriceById(appSubItem.priceId)
    }

    // Check if current items have price to avoid remote stripe call
    for (const item of sub.items) {
      if (item.price) {
        // We have migrated item with price, use it
        return this.customerManager.getPricesForProductCode(productCode, item.price.recurring?.interval);
      }
    }

    // No price, must fetch from remote
    const remoteSub = await this.customerManager.retrieveSubscription(sub.id);
    // All of them should be same interval or none set (enforced by Stripe), so any item works
    const remoteItem = remoteSub.items.data.find(item => item.price.recurring);
    return this.customerManager.getPricesForProductCode(productCode, remoteItem?.price.recurring?.interval);
  }

  public async upgrade(
    rootInitiative: InitiativeWithCustomer,
    upgradeBasePlan: UpgradeBasePlan,
    featureCode: FeatureCode
  ): Promise<UpgradeResult> {

    const upgradeDetails = this.getUpgradeDetails(upgradeBasePlan, featureCode);
    this.logger.info(`Starting upgrade process for ${rootInitiative.name}`, {
      initiativeId: rootInitiative._id.toString(),
      customerId: rootInitiative.customer?.id,
      upgradeDetails,
    });

    const sub = await this.getMainSubscription({
      rootInitiative,
      upgradeDetails,
    });

    // Check existing product code for the app or bundle
    const item = sub.items.find(item => item.productCode === upgradeDetails.productCode);

    if (!item) {
      return this.createNewAddon(rootInitiative, sub, upgradeDetails)
    }

    return this.updateSubscriptionItem({
      item: item,
      rootInitiative: rootInitiative,
      upgradeDetails: upgradeDetails
    });
  }

  public async preview(
    rootInitiative: InitiativeWithCustomer,
    upgradeBasePlan: UpgradeBasePlan,
    featureCode: FeatureCode
  ): Promise<PreviewUpgradeResult> {

    const upgradeDetails = this.getUpgradeDetails(upgradeBasePlan, featureCode);
    const sub = await this.getMainSubscription({
      rootInitiative,
      upgradeDetails,
    });

    // Check existing product code for the app or bundle
    const items = await this.generatePreviewItems(sub, upgradeDetails);
    this.logger.info(`Generating new preview items`, {
      initiativeId: rootInitiative._id,
      items,
    });

    const invoice = await this.customerManager.previewInvoice({
      items,
      customerId: rootInitiative.customer.id,
      subscriptionId: sub.id,
      subscriptionProrationBehavior: 'always_invoice',
      prorationDate: toTimestamp(upgradeDetails.prorationDate),
    });

    const lines = invoice.lines.data.map(line => toInvoiceLines(line, this.currencyService.toSymbol(line.currency)));
    this.logger.info(`Generated Preview invoice for ${upgradeDetails.productCode} with total ${invoice.total}`, {
      initiativeId: rootInitiative._id,
      customerId: rootInitiative.customer.id,
      subscriptionId: sub.id,
      total: invoice.total,
      lines: lines,
    });

    return {
      subscriptionId: sub.id,
      status: sub.status,
      periodStart: timestampToISOString(invoice.period_start),
      periodEnd: timestampToISOString(invoice.period_end),
      total: invoice.total / 100,
      currency: invoice.currency,
      currencySymbol: this.currencyService.toSymbol(invoice.currency),
      lines,
    }
  }

  private async generatePreviewItems(sub: Subscription, upgradeDetails: UpgradePlan) {

    const item = sub.items.find(item => item.productCode === upgradeDetails.productCode);
    const quantity = (item?.quantity ?? 0) + upgradeDetails.additionalUnits;

    if (item) {
      return sub.items.map(i => ({
        id: i.id,
        price: i.priceId,
        quantity: i.id === item?.id ? quantity : i.quantity,
      }));
    }

    const items: Stripe.InvoiceListUpcomingLinesParams.InvoiceItem[] = sub.items.map(i => ({
      id: i.id,
      price: i.priceId,
      quantity: i.quantity,
    }));

    const price = await this.customerManager.getPricesForProductCode(upgradeDetails.productCode, upgradeDetails.interval);
    this.logger.info(`Generating new preview item using price ${price.id}`, {
      priceId: price.id,
      priceType: price.type,
      priceRecurring: price.recurring,
    })

    items.push({
      quantity: upgradeDetails.additionalUnits,
      price: price.id,
    })

    return items;
  }

  private async updateSubscriptionItem({ item, rootInitiative, upgradeDetails }: UpdateSubscriptionItem) {
    const remoteItem = await this.customerManager.getSubscriptionItem(item.id);

    const { productCode } = upgradeDetails;

    const currentQuantity = remoteItem.quantity;
    if (currentQuantity === undefined) {
      throw new ContextError(`Remote quantity is undefined`, {
        initiativeId: rootInitiative._id.toString(),
        sub: remoteItem.subscription,
        itemId: remoteItem.id,
      })
    }

    this.logger.info(`Found subscription product ${productCode} to upgrade process for ${rootInitiative.name}`, {
      initiativeId: rootInitiative._id.toString(),
      sub: remoteItem.subscription,
      itemId: remoteItem.id,
      currentQuantity,
      upgradeDetails,
    })

    const quantity = currentQuantity + upgradeDetails.additionalUnits;

    const updated = await this.customerManager.updateSubscriptionItem(remoteItem.id, {
      quantity,
      proration_behavior: upgradeDetails.prorationBehaviour,
      proration_date: toTimestamp(upgradeDetails.prorationDate)
    })

    // Update subscription straight away
    await this.customerManager.updateSubscription(rootInitiative, updated.subscription);

    return {
      details: {
        subscriptionId: updated.subscription,
        itemId: updated.id,
        quantity,
        total: 0,
      }
    } satisfies UpgradeResult
  }

  private getDefaultProrationDate() {
    return getUTCEndOf('day');
  }

  private async getMainSubscription({ rootInitiative, upgradeDetails }: MainSubscription) {
    const baseContext = {
      initiativeId: rootInitiative._id.toString(),
      customerId: rootInitiative.customer?.id,
      upgradeDetails,
    };

    if (upgradeDetails.additionalUnits <= 0 || upgradeDetails.additionalUnits > 100) {
      throw new ContextError(`Additional units must be between 1 and 100`, baseContext);
    }

    const appConfig = await this.appConfigProvider.getByOrganization(rootInitiative)
    if (!appConfig) {
      throw new ContextError(`Cannot upgrade ${rootInitiative.name} reporting level`, {
        initiativeId: rootInitiative._id.toString(),
      })
    }

    return this.getAppProductSubscription(rootInitiative, appConfig.productCode);
  }

  private async getAppProductSubscription(rootInitiative: RootInitiativeData, productCode: SubscriptionProductCode) {
    const subscriptions = await this.customerManager.getSubscriptions(rootInitiative._id)

    const sub = this.customerManager.getSubscriptionByProductAndStatus(
      productCode,
      allowAccessStatuses,
      subscriptions
    );

    if (!sub) {
      throw new ContextError(`Cannot upgrade ${rootInitiative.name} reporting level`, {
        debugMessage: 'Unable to find active or trialing subscription',
        initiativeId: rootInitiative._id.toString(),
        customerId: rootInitiative.customer?.id,
        productCode: productCode,
        appConfigCode: rootInitiative.appConfigCode,
      })
    }

    if (sub.status === 'trialing' && !rootInitiative.customer?.defaultPaymentMethod) {
      throw new UserError(`Upgrade is not available while on trial, please add your card details first`, {
        debugMessage: 'Not yet supported to buy addons while still on trial due to missing card details',
        initiativeId: rootInitiative._id,
        customerId: rootInitiative.customer?.id,
        subscriptionId: sub.id,
      })
    }

    return sub;
  }

  private async createNewAddon(
    rootInitiative: InitiativeWithCustomer,
    sub: Subscription,
    upgradeDetails: UpgradePlan,
  ): Promise<UpgradeResult> {

    const price = await this.customerManager.getPricesForProductCode(upgradeDetails.productCode, upgradeDetails.interval);

    const item = await this.customerManager.createItem({
      subscription: sub.id,
      price: price.id,
      quantity: upgradeDetails.additionalUnits,
      proration_behavior: upgradeDetails.prorationBehaviour,
      proration_date: toTimestamp(upgradeDetails.prorationDate),
    });

    // Update subscription straight away
    await this.customerManager.updateSubscription(rootInitiative, item.subscription);

    return {
      details: {
        subscriptionId: item.subscription,
        itemId: item.id,
        quantity: item.quantity ?? 0,
        total: 0,
      }
    };
  }

  private getUpgradeDetails(
    upgradeBasePlan: UpgradeBasePlan,
    featureCode: FeatureCode,
  ): UpgradePlan {
    return {
      prorationDate: this.getDefaultProrationDate(),
      prorationBehaviour: 'always_invoice',
      featureCode,
      productCode: featureCodeToBundle(featureCode),
      additionalUnits: upgradeBasePlan.additionalUnits,
      interval: upgradeBasePlan.interval,
    };
  }
}

let instance: UpgradeService;
export const getUpgradeService = () => {
  if (!instance) {
    instance = new UpgradeService(
      wwgLogger,
      getCustomerManager(),
      getAppConfigProvider(),
      getCurrencyService(),
      getFeatureUsageService(),
    );
  }
  return instance;
}
