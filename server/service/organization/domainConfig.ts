/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { FeatureDetails } from "@g17eco/core";
import type { CustomScope } from "../../models/customScopeSchema";

export enum RequesterCode {
  SGX = 'sgx-companies',
  ABS = 'abs-companies',
  ASME = 'asme-companies',
}

export enum BrandingTemplate {
  G17Eco = 'g17eco',
  SGX = 'sgx',
}

export interface DomainConfig {
  customMetric: {
    allowAll: boolean,
  };
  brandingTemplate: BrandingTemplate;
  subdomain: string[];
  portfolioTrackerCode: RequesterCode;
  logoUrl: string;
  subscription: {
    currency: string;
  }
  scope: CustomScope[];
}

export interface ProductBundle {
  scope?: DomainConfig['scope'];
  features?: FeatureDetails[];
}

export const sgxLogo = 'https://wwg-sg.s3.ap-southeast-1.amazonaws.com/logos/wwg-sgx-logo.png';
export const domainConfig: DomainConfig[] = [
  {
    customMetric: {
      allowAll: true,
    },
    brandingTemplate: BrandingTemplate.SGX,
    subdomain: ['sg', 'sg.staging', 'singapore', 'singapore.staging', 'singapore.demo'],
    portfolioTrackerCode: RequesterCode.SGX,
    logoUrl: sgxLogo,
    subscription: {
      currency: 'sgd',
    },
    scope: [
      { scopeType: 'frameworks', code: 'ctl', required: false },
    ],
  },
]

const getUniqueKey = (s: CustomScope) => s.scopeType + '#' + s.code;

interface MergeConfigScopeParams {
  currentScope: CustomScope[];
  updateScope: CustomScope[] | undefined;
}

export const mergeConfigScope = ({ currentScope, updateScope }: MergeConfigScopeParams) => {
  const uniqueScope = new Map<string, CustomScope>(currentScope.map(s => {
    return [getUniqueKey(s), { ...s }]
  }))

  if (updateScope) {
    updateScope.forEach(s => {
      const uniqueKey = getUniqueKey(s);

      const existing = uniqueScope.get(uniqueKey);
      // @TODO: Is that always the case?
      // If anything have set as not required, then it's not required anymore
      // regardless of the follow up values, essentially allow to turn off
      // the required flag by any override
      const required = existing?.required === false ? false : s.required;
      return uniqueScope.set(uniqueKey, { ...s, required: required });
    })
  }

  return Array.from(uniqueScope.values());
};

