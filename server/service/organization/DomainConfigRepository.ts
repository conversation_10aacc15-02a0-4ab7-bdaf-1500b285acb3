/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { domainConfig, DomainConfig } from "./domainConfig";
import { getSubdomain } from '../../util/url';

export interface DomainConfigRepository {
  getBySubDomain(subdomain: string): Promise<Readonly<DomainConfig> | undefined>
  getByDomain(domain: string | undefined): Promise<Readonly<DomainConfig> | undefined>
}

export class DomainConfigLocal implements DomainConfigRepository {

  constructor(private domainConfigs: DomainConfig[]) {
  }

  public async getBySubDomain(subdomain: string): Promise<Readonly<DomainConfig> | undefined> {
    return this.domainConfigs.find(c => c.subdomain.includes(subdomain));
  }

  public async getByDomain(domain: string | undefined) {
    const subdomain = getSubdomain(domain);
    return subdomain ? this.getBySubDomain(subdomain) : undefined;
  }
}

let instance: DomainConfigRepository;
export const getDomainConfigRepository = () => {
  if (!instance) {
    instance = new DomainConfigLocal(domainConfig);
  }
  return instance;
}
