/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { getSponsorshipService, SponsorshipAgreementData } from "../referral/SponsorshipService";
import { RootInitiativeData } from "../../repository/InitiativeRepository";
import { AgreementConfig } from "../app/AppConfig";
import { isBefore } from "../../util/date";
import { CompanyAgreement, InitiativeModel } from "../../models/initiative";
import UserError from "../../error/UserError";
import { ObjectId } from "bson";
import { AcceptedAgreement } from "../../types/agreement";

interface AgreementsToAccept {
  sponsorship: SponsorshipAgreementData;
  rootInitiative: Pick<RootInitiativeData, 'created'>;
}

interface AgreementAccept extends Pick<AgreementConfig, 'code'> {
  userId: ObjectId;
  type: AgreementConfig['type'];
}


type InitiativeAgreementModel = Pick<InitiativeModel, '_id' | 'save' | 'metadata' | 'created'>;

/**
 * Eventually this should also handle user agreements or any other kind of T&C
 */
export class AgreementService {

  constructor(
    private sponsorshipService: ReturnType<typeof getSponsorshipService>,
  ) {
  }

  private getAgreementsToAccept({ rootInitiative, sponsorship }: AgreementsToAccept): AgreementConfig<string>[] {
    const agreements = sponsorship.sponsorshipConfig?.agreements ?? [];
    const currentAgreements = sponsorship.agreements?.map(agreement => agreement.code) ?? [];

    return agreements.filter(agreement => {
      if (currentAgreements.includes(agreement.code)) {
        return false;
      }
      // No date set means it's always valid, or it's valid from a certain date
      return !agreement.fromDate || isBefore(rootInitiative.created, agreement.fromDate);
    })
  }

  public async getAgreementConfigs(rootInitiative: RootInitiativeData): Promise<AgreementConfig[]> {
    const companyAgreements = await this.sponsorshipService.getAgreements(rootInitiative);

    return companyAgreements.reduce((acc, sponsorship) => {
      const agreements = this.getAgreementsToAccept({ rootInitiative, sponsorship });
      agreements.forEach(agreement => {
        acc.push({ ...agreement, type: 'sponsorship' });
      })
      return acc;
    }, [] as AgreementConfig[]);
  }

  public isCompanyAgreementCode = (code: unknown): code is CompanyAgreement => {
    return Object.values(CompanyAgreement).includes(code as CompanyAgreement);
  }

  public async acceptAgreement(initiative: InitiativeAgreementModel, agreement: AgreementAccept): Promise<AcceptedAgreement> {
    switch (agreement.type) {
      case 'sponsorship':
        return this.acceptSponsorshipAgreement(initiative, agreement);
      case 'company':
        return this.acceptCompanyAgreement(initiative, agreement);
      default:
        throw new UserError('Unknown agreement type', {
          code: agreement.code,
          initiativeId: initiative._id,
          status: 400,
        });
    }
  }

  public async acceptCompanyAgreement(initiative: InitiativeAgreementModel, agreement: AgreementAccept): Promise<AcceptedAgreement> {
    const { code, userId } = agreement;
    if (!this.isCompanyAgreementCode(code)) {
      throw new UserError('The agreement is not available for organization', {
        code,
        initiativeId: initiative._id,
        status: 400,
      });
    }

    if (!initiative.metadata) {
      initiative.metadata = {};
    }

    if (!initiative.metadata.agreements) {
      initiative.metadata.agreements = {};
    }
    const acceptedAgreement = { date: new Date(), userId };
    if (!initiative.metadata.agreements[code]) {
      initiative.metadata.agreements[code] = acceptedAgreement;
    }

    await initiative.save()

    return { code, date: acceptedAgreement.date, userId: acceptedAgreement.userId };
  }

  private async acceptSponsorshipAgreement(
    initiative: InitiativeAgreementModel,
    agreementToAccept: AgreementAccept,
  ): Promise<AcceptedAgreement> {
    const sponsorships = await this.sponsorshipService.getAgreements(initiative);

    const agreed: AcceptedAgreement[] = [];

    for (const sponsorship of sponsorships) {
      if (!sponsorship.sponsorshipConfig) {
        continue; // No config, move on
      }

      const agreements = sponsorship.sponsorshipConfig.agreements ?? [];
      if (!agreements.some(agreement => agreement.code === agreementToAccept.code)) {
        continue; // Not part of this config, move on
      }

      const alreadyAgreed = sponsorship.agreements?.filter(agreement => agreement.code === agreementToAccept.code);
      if (!alreadyAgreed?.length) {
        // We should accept for this sponsorship
        return this.sponsorshipService.acceptAgreement(sponsorship._id, {
          code: agreementToAccept.code,
          date: new Date(),
          userId: agreementToAccept.userId
        });
      }

      // Track all agreements that have been accepted, in case there are multiple sponsorships
      // with the same code, we should check the next pending one
      agreed.push(...alreadyAgreed);
    }

    const [existingAgreement] = agreed;
    if (!existingAgreement) {
      throw new UserError('No agreement found to accept', {
        code: agreementToAccept.code,
        initiativeId: initiative._id,
        sponsorshipIds: sponsorships.map(s => s._id.toHexString()),
      });
    }

    return existingAgreement;
  }
}

let instance: AgreementService;
export const getAgreementService = () => {
  if (!instance) {
    instance = new AgreementService(
      getSponsorshipService(),
    );
  }
  return instance;
}
