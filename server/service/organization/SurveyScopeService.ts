import { ObjectId } from 'bson';
import { InitiativeRepository } from '../../repository/InitiativeRepository';
import { InitiativeDataShareService, getInitiativeDataShareService } from '../initiative/InitiativeDataShareService';
import { Group, frameworks, standards } from '@g17eco/core';

export class SurveyScopeService {
  constructor(private initiativeDataShareService: InitiativeDataShareService) {}

  public async getUsedScopes(initiativeId: ObjectId | string) {
    const rootInitiativeWithChildren = await InitiativeRepository.getOrganizationWithChildren(initiativeId);
    const initiativeIds = rootInitiativeWithChildren.map((initiative) => initiative._id);

    const combinedScope = await this.initiativeDataShareService.getCombinedScope(initiativeIds);

    const { standards: allStandards, frameworks: allFrameworks } = combinedScope;

    const usedStandards = this.processByScopeType({ scopeCodes: allStandards, isStandard: true });
    const usedFrameworks = this.processByScopeType({ scopeCodes: allFrameworks });

    return { standards: usedStandards, frameworks: usedFrameworks };
  }

  /*
  return used scopes which has format: { [key: groupCode]: [subGroupCode-level-1] }
  input: scopeCodes: ['sgx_metrics', 'gri-1', 'gri-2-201'], isStandard: true
  output: { sgx_metrics: [], gri: ['gri-1', 'gri-2'] }
  */
  private processByScopeType({ scopeCodes, isStandard = false }: { scopeCodes: string[]; isStandard?: boolean }) {
    const groups = isStandard ? standards : frameworks;
    const scope = {} as { [key: string]: string[] };
    scopeCodes.forEach((scopeCode) => {
      const inScope = groups[scopeCode];
      // full group added
      if (inScope) {
        scope[scopeCode] = groups[scopeCode].subgroups?.map((g) => g.code) ?? [];
        return;
      }
      // partial group (subgroups) added
      const { groupCode, subGroupCode } = this.findGroupWithDirectSubGroupBySubGroupCode({
        subGroupCode: scopeCode,
        isStandard,
      });
      if (subGroupCode) {
        scope[groupCode] = [...new Set([...(scope[groupCode] ?? []), subGroupCode])];
      }
    });
    return scope;
  }

  /*
  find group & the first subgroup by subgroup code of any level
  input: subGroupCode: 'gri-2-205', isStandard: true
  output: { groupCode: 'gri', subGroupCode: 'gri-2' }
  */
  private findGroupWithDirectSubGroupBySubGroupCode({
    subGroupCode,
    isStandard = false,
  }: {
    subGroupCode: string;
    isStandard?: boolean;
  }) {
    const groups = isStandard ? standards : frameworks;
    const groupCodes = Object.keys(groups);
    for (const groupCode of groupCodes) {
      const subGroups = groups[groupCode].subgroups;
      if (!subGroups) {
        continue;
      }
      for (const subGroup of subGroups) {
        if (this.checkIsSubGroup(subGroupCode, subGroup)) {
          return { groupCode, subGroupCode: subGroup.code };
        }
      }
    }
    return { groupCode: undefined, subGroupCode: undefined };
  }

  private checkIsSubGroup(subGroupCode: string, subGroup: Group) {
    if (subGroupCode === subGroup.code) {
      return true;
    }

    const subGroups = subGroup.subgroups;
    if (!subGroups) {
      return false;
    }
    for (const group of subGroups) {
      if (this.checkIsSubGroup(subGroupCode, group)) {
        return true;
      }
    }
    return false;
  }
}

let instance: SurveyScopeService;
export const getSurveyScopeService = () => {
  if (!instance) {
    instance = new SurveyScopeService(getInitiativeDataShareService());
  }
  return instance;
};
