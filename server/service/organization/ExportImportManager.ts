import { LoggerInterface, wwgLogger } from '../wwgLogger';
import { ObjectId } from 'bson';
import { InitiativeRepository } from '../../repository/InitiativeRepository';
import Initiative, { DEFAULT_USAGE, InitiativePlain } from '../../models/initiative';
import { readCSVFile, stringifyCsvFile } from '../file/writer/CsvFileWriter';
import { generatedUUID } from '../crypto/token';
import path from 'path';
import { downloadFile } from '../file/file';
import { saveProfile } from '../file/profile';
import * as fs from 'fs';
import { KeysEnum } from '../../models/public/projectionUtils';
import UserError from '../../error/UserError';

type ConvertedRow = Pick<
  InitiativePlain,
  | '_id'
  | 'name'
  | 'code'
  | 'parentId'
  | 'tags'
  | 'permissionGroup'
  | 'missionStatement'
  | 'startDate'
  | 'endDate'
  | 'geoLocation'
  | 'description'
  | 'profile'
  | 'usage'
> & {
  logoUrl?: string;
};

export interface InitiativeSheetRow extends Omit<ConvertedRow, '_id' | 'parentId' | 'profile' | 'tags'> {
  parentInitiativeCode?: string;
  tags?: string;
}

const columnsMap: { [key: string]: keyof Omit<ConvertedRow, '_id' | 'logoUrl'> } = {
  name: 'name',
  code: 'code',
  parentInitiativeCode: 'parentId',
  tags: 'tags',
  permissionGroup: 'permissionGroup',
  missionStatement: 'missionStatement',
  startDate: 'startDate',
  endDate: 'endDate',
  geoLocation: 'geoLocation',
  description: 'description',
  logoUrl: 'profile',
};

enum ValidateStatus {
  New = 'new',
  Invalid = 'invalid',
  Existing = 'existing',
}

type ExistingInitiative = Pick<InitiativePlain, '_id' | 'code' | 'usage' | 'parentId'>;
const existingInitiativeProjection: KeysEnum<ExistingInitiative, 1> = { _id: 1, code: 1, usage: 1, parentId: 1 };

type InitiativeMap = Map<string, ExistingInitiative & { parentInitiativeCode?: string; status: ValidateStatus }>;

type ImportResult = {
  upserted: number;
  modified: number;
  errored: number;
};

class ExportImportManager {
  constructor(private logger: LoggerInterface) {}
  public async exportSubsidiariesImportTemplate(initiativeId: string | ObjectId): Promise<string> {

    const projection = Object.values(columnsMap).reduce((acc, col) => ({ ...acc, [col]: 1 }), { _id: 1 });
    const initiatives = await InitiativeRepository.getAllChildrenById(initiativeId, undefined, projection);
    const initiativeMap = new Map(
      initiatives.map((initiative: InitiativePlain<ObjectId>) => [initiative._id.toString(), initiative])
    );

    const sheetData = initiatives.map((initiative: InitiativePlain<ObjectId>) => {
      return Object.entries(columnsMap).reduce((acc, [key, value]) => {
        switch (value) {
          case 'parentId': {
            const parentId = initiative[value]?.toString() || '';
            return {
              ...acc,
              [key]: initiativeMap.get(parentId)?.code,
            };
          }
          case 'tags': {
            const tags = initiative[value];
            return {
              ...acc,
              [key]: tags ? tags.join(',') : '',
            };
          }
          case 'startDate':
          case 'endDate': {
            const date = initiative[value];
            return {
              ...acc,
              [key]: date ? date.toISOString() : '',
            };
          }
          default:
            return {
              ...acc,
              [key]: initiative[value]?.toString(),
            };
        }
      }, {} as InitiativeSheetRow);
    });

    const headers = Object.keys(columnsMap).map((column) => ({
      title: column,
      id: column,
    }));

    const csvString = stringifyCsvFile({ header: headers, records: sheetData });

    this.logger.info(`Processed subsidiaries import template, returning ${initiatives.length} `, {
      initiativeId: initiativeId.toString(),
    });

    return csvString;
  }

  // create a map of initiatives by code and parentInitiativeCode with pre-validate status
  private async createInitiativeToImportMap(
    rawInitiatives: InitiativeSheetRow[],
    currentInitiativeTree: InitiativePlain[]
  ): Promise<InitiativeMap> {
    const initiativeCodes = Array.from(
      rawInitiatives.reduce((acc, { parentInitiativeCode, code }) => {
        if (parentInitiativeCode) {
          acc.add(parentInitiativeCode);
        }
        if (code) {
          acc.add(code);
        }
        return acc;
      }, new Set<string>())
    );

    const existingInitiatives = await Initiative.find({ code: { $in: initiativeCodes } }, existingInitiativeProjection)
      .lean<ExistingInitiative[]>()
      .exec();
    const existingInitiativeMap = new Map(existingInitiatives.map((initiative) => [initiative.code, initiative]));
    const existingById = new Map(currentInitiativeTree.map((initiative) => [initiative._id.toString(), initiative]));
    const resultMap = new Map();

    rawInitiatives.forEach((rawInitiative) => {
      const existing = existingInitiativeMap.get(rawInitiative.code);
      if (!existing) {
        resultMap.set(rawInitiative.code, this.getNewInitiative(rawInitiative.code, rawInitiative.parentInitiativeCode));
      } else {
        resultMap.set(rawInitiative.code, this.getExistingInitiative(existing, existingById, rawInitiative.parentInitiativeCode));
      }

      if (rawInitiative.parentInitiativeCode && !resultMap.has(rawInitiative.parentInitiativeCode)) {
        const existingParent = existingInitiativeMap.get(rawInitiative.parentInitiativeCode);

        if (!existingParent) {
          resultMap.set(rawInitiative.parentInitiativeCode, this.getNewInitiative(rawInitiative.parentInitiativeCode, undefined));
        } else {
          const grandParent = existingParent.parentId ? existingById.get(existingParent.parentId.toString()) : undefined;
          resultMap.set(
            rawInitiative.parentInitiativeCode,
            this.getExistingInitiative(existingParent, existingById, grandParent?.code)
          );
        }
      }
    });

    return resultMap;
  }

  private getNewInitiative(code: string, parentInitiativeCode: string | undefined) {
    return {
      _id: new ObjectId(),
      code,
      status: ValidateStatus.New,
      usage: DEFAULT_USAGE,
      parentInitiativeCode
    };
  }

  private getExistingInitiative(existingInitiative: ExistingInitiative, existingById: Map<string, ExistingInitiative | undefined>, parentInitiativeCode?: string) {
    return {
      ...existingInitiative,
      // If the length > 0, usage was set before, otherwise use DEFAULT_USAGE which enable initiative in the org map.
      usage: existingInitiative.usage.length ? existingInitiative.usage : DEFAULT_USAGE,
      status: existingById.has(existingInitiative._id.toString()) ? ValidateStatus.Existing : ValidateStatus.Invalid,
      parentInitiativeCode,
    };
  }

  private preValidateParentAssignment(
    rawInitiatives: InitiativeSheetRow[],
    parentInitiative: ExistingInitiative & { status: ValidateStatus }
  ) {
    if (parentInitiative.status === ValidateStatus.New) {
      return rawInitiatives.some((initiative) => initiative.code === parentInitiative.code);
    }
    return parentInitiative.status !== ValidateStatus.Invalid;
  }

  private filterRows({
    rawInitiatives,
    initiativeToImportMap,
    rootInitiativeCode,
  }: {
    rawInitiatives: InitiativeSheetRow[];
    initiativeToImportMap: InitiativeMap;
    rootInitiativeCode: string;
  }) {
    return rawInitiatives.reduce(
      (acc, rawInitiative, index) => {
        const tags =
          rawInitiative.tags
            ?.split(',')
            .map((v) => v.trim())
            .filter(Boolean) || [];
        const permissionGroup = rawInitiative.permissionGroup?.trim() || undefined;
        const parentInitiativeCode = rawInitiative.parentInitiativeCode;

        const initiativeToImport = initiativeToImportMap.get(rawInitiative.code);

        if (!initiativeToImport || initiativeToImport.status === ValidateStatus.Invalid) {
          acc.erroredRowIndexes.push(index);
          return acc;
        }

        // only root node can have parentInitiativeCode undefined
        if (!parentInitiativeCode) {
          if (initiativeToImport.code !== rootInitiativeCode) {
            acc.erroredRowIndexes.push(index);
          }
          return acc;
        }

        // must import new node or existing node and have parentInitiativeCode
        const parentInitiative = initiativeToImportMap.get(parentInitiativeCode);
        if (!parentInitiative || !this.preValidateParentAssignment(rawInitiatives, parentInitiative)) {
          acc.erroredRowIndexes.push(index);
          return acc;
        }

        // check assigning a node to its child case, prevent circular reference
        let parent: typeof parentInitiative | undefined = parentInitiative;
        while (parent?.parentInitiativeCode) {
          parent = initiativeToImportMap.get(parent.parentInitiativeCode);
          if (parent && parent.parentInitiativeCode === rawInitiative.code) {
            acc.erroredRowIndexes.push(index);
            return acc;
          }
        }

        // valid initiative
        acc.validRows.push({
          ...rawInitiative,
          tags,
          permissionGroup,
          parentId: parentInitiative._id,
          _id: initiativeToImport._id,
          usage: initiativeToImport.usage,
        });
        return acc;
      },
      {
        erroredRowIndexes: [] as number[],
        validRows: [] as ConvertedRow[],
      }
    );
  }

  private async updateProfileImages(validRows: ConvertedRow[]) {
    const urlsMap = validRows.reduce((acc, row) => {
      if (typeof row.logoUrl === 'string' && row.logoUrl.toLowerCase().startsWith('http')) {
        acc.set(row._id.toString(), row.logoUrl);
      }
      return acc;
    }, new Map<string, string>());

    const fileMap = new Map<string, Express.Multer.File>();

    for (const [initiativeId, fileUrl] of urlsMap.entries()) {
      const destination = `/tmp/${generatedUUID()}${path.extname(fileUrl) ?? ''}`;
      const now = Date.now();
      try {
        const existingFile = fileMap.get(initiativeId);
        if (existingFile) {
          const msg = await saveProfile(initiativeId, 'initiative', [existingFile]);
          wwgLogger.info(msg, { duration: Date.now() - now });
          continue;
        }
        const { contentLength, contentType }: { contentLength: string; contentType: string } = await downloadFile(
          fileUrl,
          destination
        );
        const file = {
          mimetype: contentType,
          originalname: path.basename(fileUrl),
          path: destination,
          size: Number(contentLength),
        } as Express.Multer.File;
        fileMap.set(initiativeId, file);
        const msg = await saveProfile(initiativeId, 'initiative', [file]);
        wwgLogger.info(msg, { duration: Date.now() - now });
      } catch (error) {
        wwgLogger.error(error);
      }
    }

    fileMap.forEach((file) => {
      fs.unlinkSync(file.path);
    });
  }

  public async importFile(initiative: Pick<InitiativePlain, '_id' | 'code'>, filepath: string): Promise<ImportResult> {
    const initiativeId = initiative._id;

    this.logger.info('Parsing csv file', { initiativeId, filepath });
    // TODO: Mask the raw data to prevent wrong columns from being uploaded
    const rawInitiatives = (await readCSVFile(filepath)) as InitiativeSheetRow[];

    const currentInitiativeTree = await InitiativeRepository.getAllChildrenById(initiativeId);

    const initiativeToImportMap = await this.createInitiativeToImportMap(rawInitiatives, currentInitiativeTree);

    const { erroredRowIndexes, validRows } = this.filterRows({
      rawInitiatives,
      initiativeToImportMap,
      rootInitiativeCode: initiative.code,
    });

    if (erroredRowIndexes.length) {
      const invalidRowsText = erroredRowIndexes.map((rowIndex) => rowIndex + 1).join(', ');
      const message = `Found ${erroredRowIndexes.length} errored row${
        erroredRowIndexes.length > 1 ? 's' : ''
      }: Row ${invalidRowsText}`;
      this.logger.error(message, { initiativeId });
      throw new UserError(message, { initiativeId });
    }

    const updates = validRows.map(({ _id, ...row }) => {
      const initiative = initiativeToImportMap.get(row.code);
      return {
        updateOne: {
          filter: { code: row.code },
          update: initiative?.status === ValidateStatus.Existing ? row : { _id, ...row },
          upsert: true,
        },
      };
    });

    const result = await Initiative.bulkWrite(updates);

    this.logger.info(`Processed ${updates.length} valid rows`, {
      initiativeId,
      result,
    });

    await this.updateProfileImages(validRows);

    return {
      upserted: result.upsertedCount,
      modified: result.modifiedCount,
      errored: erroredRowIndexes.length,
    };
  }
}

let instance: ExportImportManager;
export const getExportImportManager = () => {
  if (!instance) {
    instance = new ExportImportManager(wwgLogger);
  }
  return instance;
};
