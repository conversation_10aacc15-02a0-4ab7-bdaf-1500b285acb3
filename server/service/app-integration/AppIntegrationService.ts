import type { User<PERSON>lain } from '../../models/user';
import UserError from '../../error/UserError';
import type { InitiativeModel } from '../../models/initiative';
import type { CustomerManager} from '../payment/CustomerManager';
import { getCustomerManager } from '../payment/CustomerManager';
import type { AppConfig } from '../app/AppConfig';
import { defaultMT } from '../app/materiality-tracker';
import { defaultCTPro } from '../app/company-tracker/defaultCTPro';
import { getUnixSeconds, getUTCEndOf, oneDayInSeconds } from '../../util/date';
import type {
  SurveyCreateProps,
  SelfOnboardingManager} from '../onboarding/SelfOnboardingManager';
import {
  getSelfOnboardingManager
} from '../onboarding/SelfOnboardingManager';
import { DataPeriods } from '../utr/constants';
import { SurveyScope } from '../survey/SurveyScope';
import type {
  MaterialityMetricGroupService} from '../materiality-assessment/MaterialityMetricGroupService';
import {
  getMaterialityMetricGroupService
} from '../materiality-assessment/MaterialityMetricGroupService';
import { getSlackNotificationService, OnboardingMaterialityTrackerType } from '../notification/SlackNotificationService';
import { wwgLogger } from '../wwgLogger';
import ContextError from '../../error/ContextError';

interface AddCompanyTrackerProps {
  initiative: InitiativeModel;
  user: UserPlain;
  domain?: string;
  appConfig?: AppConfig;
}

export class AppIntegrationService {
  constructor(
    private customerManager: CustomerManager,
    private selfOnboardingManager: SelfOnboardingManager,
    private metricGroupService: MaterialityMetricGroupService,
    private slackNotificationService: ReturnType<typeof getSlackNotificationService>,
  ) {}

  async addCompanyTracker({ initiative, user, domain, appConfig = defaultCTPro }: AddCompanyTrackerProps) {
    const initiativeId = initiative._id;
    if (initiative.appConfigCode !== defaultMT.code || initiative.permissionGroup !== defaultMT.permissionGroup) {
      throw new UserError('Your organization is not valid Materiality Tracker', {
        initiativeId,
        appConfigCode: initiative.appConfigCode,
        permissionGroup: initiative.permissionGroup,
      });
    }

    const subscriptions = await this.customerManager.getSubscriptions(initiativeId);
    const integratedSubscriptions = subscriptions.filter((sub) =>
      sub.items.some((item) => item.productCode === appConfig.productCode)
    );
    if (integratedSubscriptions.length) {
      throw new UserError('Your organization has already connected Company Tracker', {
        initiativeId,
        subscriptions: integratedSubscriptions.map((sub) => sub.id),
      });
    }

    const result = await this.customerManager.createProductSubscription({
      user,
      initiative,
      productCode: appConfig.productCode,
      trialEnd: getUnixSeconds(oneDayInSeconds * 14 + 3600),
    });

    initiative.permissionGroup = appConfig.permissionGroup;
    initiative.appConfigCode = appConfig.code;
    await initiative.save();
    await this.createCTSurvey({ initiative, user, domain });

    return result;
  }

  async addMaterialityTracker({ initiative, user }: { initiative: InitiativeModel; user: UserPlain }) {
    const initiativeId = initiative._id;
    const subscriptions = await this.customerManager.getSubscriptions(initiativeId);
    const integratedSubscription = this.customerManager.getValidSubscriptionByProduct(
      defaultMT.productCode,
      subscriptions
    );
    if (integratedSubscription) {
      throw new UserError('Your organization has already connected Materiality Tracker', {
        initiativeId,
        subscriptions: integratedSubscription.id,
      });
    }

    const result = await this.customerManager.createProductSubscription({
      user,
      initiative,
      productCode: defaultMT.productCode,
    });
    
    try {
      await this.slackNotificationService.sendMaterialityOnboardingNotification({
        initiativeId,
        user,
        type: OnboardingMaterialityTrackerType.CTIntegration,
      });
    } catch (e) {
      wwgLogger.error(
        new ContextError('Failed to send materiality onboarding notification', {
          cause: e,
          initiativeId: initiativeId.toString(),
          userId: user._id.toString(),
        })
      );
    }

    return result;
  }

  private async createCTSurvey({
    initiative,
    user,
    domain,
  }: Pick<AddCompanyTrackerProps, 'initiative' | 'user' | 'domain'>) {
    const { metricGroupId, effectiveDate } =
      (await this.metricGroupService.getLatestMetricGroup(initiative, user._id)) ?? {};

    const createSurveyData: SurveyCreateProps = {
      effectiveDate: (effectiveDate ?? getUTCEndOf('month', new Date())).toISOString(),
      period: DataPeriods.Yearly,
      scope: { ...SurveyScope.createEmpty(), custom: metricGroupId ? [metricGroupId] : [] },
      verificationRequired: true,
    };

    await this.selfOnboardingManager.createNewSurvey(initiative, user, { ...createSurveyData, domain });
  }
}

let instance: AppIntegrationService;
export function getAppIntegrationService() {
  if (!instance) {
    instance = new AppIntegrationService(
      getCustomerManager(),
      getSelfOnboardingManager(),
      getMaterialityMetricGroupService(),
      getSlackNotificationService(),
    );
  }
  return instance;
}
