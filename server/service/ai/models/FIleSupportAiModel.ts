import { OpenAI } from 'openai';
import { FileDeleted, FileObject, FilePurpose } from 'openai/resources/files';
import { MessageCreateParams } from 'openai/resources/beta/threads/messages';
import { AssistantCreateParams, AssistantDeleted } from 'openai/resources/beta/assistants';
import { Uploadable } from 'openai/uploads';
import z, { ZodType } from 'zod';

// TODO: Make this interface less OpenAI specific.
export interface FileSupportAiModel {
  createFile(params: { file: Uploadable; purpose?: FilePurpose }): Promise<FileObject>;
  deleteFile(fileId: string): Promise<FileDeleted>;
  createAssistant(body: Pick<AssistantCreateParams, 'name' | 'instructions' | 'tools'>): Promise<OpenAI.Beta.Assistants.Assistant>;
  deleteAssistant(assistantId: string): Promise<AssistantDeleted>;
  runThreadWithAssistant<Output extends ZodType>(params: {
    assistantId: string;
    message: MessageCreateParams;
    jsonSchema: ZodType;
  }): Promise<z.infer<Output> | undefined>;
}
