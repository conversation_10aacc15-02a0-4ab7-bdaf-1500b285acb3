/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { OpenAI } from 'openai';
import { AIModel, AIPrompt, AIResponse } from './AIModel';
import config from '../../../config';
import { Logger } from 'winston';
import UserError from '../../../error/UserError';
import { wwgLogger } from '../../wwgLogger';
import { ResponseFormat } from '../../../routes/validation-schemas/ai-response-formats/ai-assistant';
import { ChatCompletionParseParams } from 'openai/resources/beta/chat/completions';
import { DEFAULT_MAX_TOKEN } from '../utrv-assistant/constants';
import { FilePurpose } from 'openai/resources/files';
import { MessageCreateParams } from 'openai/resources/beta/threads/messages';
import ContextError from '../../../error/ContextError';
import { AssistantCreateParams } from 'openai/resources/beta/assistants';
import { Uploadable } from 'openai/uploads';
import z, { ZodType } from 'zod';
import { zodResponseFormat } from 'openai/helpers/zod';
import { AIInteraction } from '../../../models/ai-interaction';
import { FileSupportAiModel } from './FIleSupportAiModel';

export const OPENAI_MODEL = 'gpt-4.1-2025-04-14';
const TOKEN_LIMIT = 100000;

export interface OpenAiResponse extends Partial<OpenAI.Chat.Completions.ChatCompletion> {
  usage?: OpenAI.Completions.CompletionUsage | undefined;
}

export class ChatGPT implements AIModel, FileSupportAiModel {
  private openai = new OpenAI({
    apiKey: config.ai.chatGPT.apiKey,
  });

  constructor(private logger: Logger, private aiInteractionModel: typeof AIInteraction) {}

  public async runCompletion(messages: AIPrompt[], maxTokens?: number) {
    try {
      const response = await this.openai.chat.completions.create({
        model: OPENAI_MODEL,
        messages,
        max_tokens: Math.min(TOKEN_LIMIT, maxTokens || DEFAULT_MAX_TOKEN),
      });
      return {
        content: response.choices[0].message.content ?? '',
        usage: response.usage,
      };
    } catch (e) {
      throw new UserError('Unable to generate a response. Please try again later', { messages, cause: e });
    }
  }

  public async parseCompletion<T = any>(messages: AIPrompt[], maxTokens?: number, responseFormat?: ResponseFormat): Promise<AIResponse<T>> {
    try {
      const tokens = Math.min(TOKEN_LIMIT, maxTokens || DEFAULT_MAX_TOKEN);
      const response = await this.openai.beta.chat.completions.parse<ChatCompletionParseParams, T>({
        model: OPENAI_MODEL,
        messages,
        max_tokens: tokens,
        ...(responseFormat ? { response_format: responseFormat } : {}),
      });
      this.aiInteractionModel
        .create({
          model: OPENAI_MODEL,
          maxTokens: tokens,
          usage: {
            promptTokens: response.usage?.prompt_tokens ?? 0,
            completionTokens: response.usage?.completion_tokens ?? 0,
            totalTokens: response.usage?.total_tokens ?? 0,
          },
          prompt: messages?.[0].content,
        })
        .catch(() => {
          this.logger.error(
            new ContextError(`Failed to create AI interaction`, {
              prompt: messages?.[0].content,
            })
          );
        });
      return {
        content: response.choices[0].message.parsed as T,
        usage: response.usage,
      };
    } catch (e) {
      throw new UserError('Unable to communicate with ChatGPTAI. Please try again later', { messages, cause: e });
    }
  }

  public getModelVersion(): string {
    return OPENAI_MODEL;
  }

  public async createFile({ file, purpose = 'assistants' }: { file: Uploadable; purpose?: FilePurpose }) {
    return this.openai.files.create({
      file,
      purpose,
    });
  }

  public async deleteFile(fileId: string) {
    return this.openai.files.del(fileId);
  }

  public async createAssistant(body: Pick<AssistantCreateParams, 'name' | 'instructions' | 'tools'>) {
    return this.openai.beta.assistants.create({ ...body, model: OPENAI_MODEL });
  }

  public async deleteAssistant(assistantId: string) {
    return this.openai.beta.assistants.del(assistantId);
  }

  public async runThreadWithAssistant<Output extends ZodType>({
    assistantId,
    message,
    jsonSchema,
  }: {
    assistantId: string;
    message: MessageCreateParams;
    jsonSchema: Output;
  }): Promise<z.infer<Output> | undefined> {
    // 1. Create a thread
    const thread = await this.openai.beta.threads.create();

    // 2. Add the user message to the thread
    await this.openai.beta.threads.messages.create(thread.id, message);

    // 3. Run the assistant AND wait for completion
    const responseFormat = zodResponseFormat(jsonSchema, 'output');
    const run = await this.openai.beta.threads.runs.createAndPoll(thread.id, {
      assistant_id: assistantId,
      response_format: responseFormat,
      max_completion_tokens: TOKEN_LIMIT,
    });

    await this.aiInteractionModel.create({
      model: run.model,
      maxTokens: run.max_completion_tokens,
      usage: {
        promptTokens: run.usage?.prompt_tokens ?? 0,
        completionTokens: run.usage?.completion_tokens ?? 0,
        totalTokens: run.usage?.total_tokens ?? 0,
      },
      prompt: message.content,
    });

    if (run.status === 'failed') {
      throw new ContextError('Thread run failed', {
        assistantId,
        threadId: thread.id,
        runId: run.id,
        cause: run.last_error,
      });
    }

    if (run.status === 'incomplete') {
      throw new ContextError('Thread run incomplete', {
        assistantId,
        threadId: thread.id,
        runId: run.id,
        cause: run.incomplete_details,
      });
    }

    // 4. Fetch messages once the run is complete
    const messages = await this.openai.beta.threads.messages.list(thread.id);
    const assistantMessage = messages.data.find((m) => m.role === 'assistant');

    if (!assistantMessage) {
      this.logger.error(new ContextError('Thread message not found', { assistantId, threadId: thread.id, messages }));
      return;
    }

    // 5. Extract the assistant's content text
    const rawResult = assistantMessage.content[0].type === 'text' ? assistantMessage.content[0].text.value : '';
    try {
      return jsonSchema.parse(JSON.parse(rawResult));
    } catch (e) {
      this.logger.error(
        new ContextError('Failed to parse AI response to result', {
          assistantId,
          threadId: thread.id,
          responseContent: rawResult,
          cause: e,
        })
      );
    }
  }
}

let instance: ChatGPT;
export const getChatGPT = () => {
  if (!instance) {
    instance = new ChatGPT(wwgLogger, AIInteraction);
  }
  return instance;
};
