import { JobResult } from '../../background-process/types';
import { JobType } from '../../../models/backgroundJob';
import { LoggerInterface, wwgLogger } from '../../wwgLogger';
import { AIDocumentUtrMappingWorkflow, getAIDocumentLibraryScanWorkflow } from './AIDocumentUtrMappingWorkflow';
import ContextError from '../../../error/ContextError';

export class AIDocumentUtrMappingWorker {
  constructor(private logger: LoggerInterface, protected workflow: AIDocumentUtrMappingWorkflow) {}

  canHandle(jobType: JobType): boolean {
    return jobType === JobType.AIDocumentUtrMapping;
  }

  public async process(jobId: string, options: { retry: boolean }): Promise<JobResult> {
    this.logger.info(`Start processing ${JobType.AIDocumentUtrMapping}, jobId: ${jobId || '-'}`);
    const job = jobId ? await this.workflow.findJob(jobId) : await this.workflow.findPendingJob();

    if (!this.workflow.isAIDocumentUtrMapping(job)) {
      throw new ContextError(`Received invalid job ${job._id} type ${job.type}`, {
        jobId: job._id,
        type: job.type,
        created: job.created,
      });
    }

    // Progress job to the next stage.
    return this.workflow.progressJob(job, { ...options, continueOnError: true });
  }
}

let instance: AIDocumentUtrMappingWorker;

export function getAIDocumentLibraryScanWorker(): AIDocumentUtrMappingWorker {
  if (!instance) {
    instance = new AIDocumentUtrMappingWorker(wwgLogger, getAIDocumentLibraryScanWorkflow());
  }
  return instance;
}
