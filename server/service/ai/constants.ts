import { AIUtrvSuggestion } from './types';

export const fallbackSuggestion: AIUtrvSuggestion = {
  predictedAnswer: '',
  questionExplanation: '',
  bestPractice: [],
  keyInfo: [],
  suggestedEvidence: {
    primaryDocumentation: [],
    supportingDocumentation: [],
  },
  whereToFind: {
    externalSource: [],
    internalSource: [],
  },
};

export enum ToolType {
  CodeInterpreter = 'code_interpreter',
}
