/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { wwgLogger } from '../wwgLogger';
import { Logger } from 'winston';
import { AIModelFactory, AIModelType, getAIModelFactory } from './AIModelFactory';
import { AIPrompt } from './models/AIModel';
import ContextError from '../../error/ContextError';

const CHUNK_SIZE = 75;
const TOKEN_LIMIT = 4096; // Must be less than 4096

export type AssessmentScoreMap = { [materialityCode: string]: number };

interface ResponseFormat {
  scores: { code: string; score: number }[];
  references: { url: string; label: string }[];
}

export interface ImpactScores {
  scores: AssessmentScoreMap;
  references: ResponseFormat['references'];
  usage: number;
  topicCount: number;
  errorCount: number;
}

export class MaterialityAssessmentAIService {
  constructor(private logger: Logger, private aiModelFactor: AIModelFactory) {}

  private getImpactPrompt(
    topics: { code: string; name: string }[],
    questionsAndAnswers: { question: string; answers: string[] }[],
    company: {
      name: string;
      industry?: string;
      country?: string;
    }
  ): AIPrompt[] {
    return [
      {
        role: 'system',
        content:
          `
          You are a materiality assessment AI consultant performaning double-materiality on behalf of a company.
          The financial materiality has already been completed, so your job is to do the Environmental and Social
          materiality.
          You will be provided with a pre-determined list of material topics, which I will refer to as M1.
          Your job is to provide a score for each topic in M1, from 0 to 100, based on the impact of the topic on
          the company, specifically focussing on their non-financial materiality.

          The scoring should represent an absolute score, not a relative score of the topics to each other.
          If they have the same impact score them the same. Do not add any new topics.
          To do this you will analyze the responses from an assessment the company has completed, and also
          look at online sources, including but not limited to recent news articles, social media references,
          the country's sustainability roadmap, and industry trends.

          You must provide the response as a JSON object without any additional text or code block formatting
          in the following format:
          { scores: { code: string, score: number }[], references: { url: string, label: string }[] }

          Your response must contain the scores for each topic and a list of reference links to online source pages
          that were used to generate these scores.
          Please make sure the URLs provided are working and auditable pages used for the assessment scoring.

          The list of material topics you will score against is provided here in JSON format:
          ` + JSON.stringify(topics),
      },
      {
        role: 'user',
        content: `
          Company Name: ${company.name}.
          Company Industry: ${company.industry ?? 'Unknown'}.
          Company Country: ${company.country ?? 'United Kingdom'}.
          Company Assessment Responses in JSON format:
          ${JSON.stringify(questionsAndAnswers)}
          `,
      },
    ];
  }

  public async getImpactScores(
    topics: { code: string; name: string }[],
    questionsAndAnswers: { question: string; answers: string[] }[],
    companyInfo: { name: string; industry?: string, country?: string },
    modelType: AIModelType = AIModelType.ChatGPT
  ): Promise<ImpactScores> {
    const scores: AssessmentScoreMap = {};
    let errorCount: number = 0;
    const usage = {
      completion_tokens: 0,
      prompt_tokens: 0,
      total_tokens: 0,
    };

    const references: ResponseFormat['references'] = [];


    // Create chunks topics to send to ChatGPT then concatenate the response
    for (let i = 0; i < topics.length; i += CHUNK_SIZE) {
      const chunkTopics = topics.slice(i, i + CHUNK_SIZE);
      const response = await this.aiModelFactor.getModel(modelType).runCompletion(
        this.getImpactPrompt(chunkTopics, questionsAndAnswers, companyInfo),
        TOKEN_LIMIT
      );
      if (response.usage !== undefined) {
        usage.prompt_tokens += response.usage?.prompt_tokens ?? 0;
        usage.completion_tokens += response.usage?.completion_tokens ?? 0;
        usage.total_tokens += response.usage?.total_tokens ?? 0;
      }

      const content = response.content;
      if (!content) {
        continue;
      }
      try {
        const json = JSON.parse(content) as ResponseFormat;
        for (const { code, score } of json.scores) {
          scores[code] = score;
        }
        references.push(...json.references);
      } catch (e) {
        errorCount += chunkTopics.length;
        this.logger.error(new ContextError('Error parsing JSON response', { cause: e, content }))
      }
    }
    this.logger.info('Materiality Assessment AI Service', {
      errorCount,
       usage,
       scores: Object.keys(scores).length,
       topics: topics.length
    });

    return {
      scores,
      references,
      topicCount: topics.length,
      errorCount,
      usage: usage.total_tokens,
    };
  }
}

let instance: MaterialityAssessmentAIService | undefined;
export const getMaterialityAssessmentAIService = () => {
  if (!instance) {
    instance = new MaterialityAssessmentAIService(
      wwgLogger,
      getAIModelFactory()
    );
  }
  return instance;
};
