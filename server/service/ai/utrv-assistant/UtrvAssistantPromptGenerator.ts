import { UtrValueType } from '../../../models/public/universalTrackerType';
import { AIPrompt } from '../models/AIModel';
import { COMMAND_PROMPT, INPUT_FORMAT_PROMPT, ROLE_PROMPT, ANSWER_TEMPLATE } from './constants';
import { UtrvPromptInput } from './types';

export class UtrvAssistantPromptGenerator {
  private input: UtrvPromptInput;
  private prompt: string = '';

  constructor(input: UtrvPromptInput) {
    this.input = input;
  }

  public generatePrompt(): AIPrompt {
    return this.populateRole()
      .populateInputFormat()
      .populateContext()
      .populateCommand()
      .populateAnswerTemplate()
      .toRequestMessage();
  }

  private populateRole() {
    this.prompt = this.prompt.concat(ROLE_PROMPT);
    return this;
  }

  private populateInputFormat() {
    this.prompt = this.prompt.concat(
      INPUT_FORMAT_PROMPT[this.input.valueType] ?? INPUT_FORMAT_PROMPT[UtrValueType.Text]
    );
    return this;
  }

  private populateContext() {
    // Dynamically prepare the context, based on the valueType, and wrap it in triple square brackets
    this.prompt = this.prompt.concat(`[[[\n${this.getContext()}\n]]]`);
    return this;
  }

  private getContext(): string {
    switch (this.input.valueType) {
      case UtrValueType.Number:
        return JSON.stringify({
          title: this.input.title,
          type: this.input.type,
          period: this.input.period,
          effectiveDate: this.input.effectiveDate,
          unitType: this.input.unitType,
          unit: `${this.input.numberScale ?? ''} ${this.input.unit ?? ''}`.trim(),
          industry: this.input.industry,
          furtherExplanation: this.input.furtherExplanation,
          instructions: this.input.instructions,
          previousUtrvs: this.input.previousUtrvs,
        });
      case UtrValueType.Percentage:
      case UtrValueType.Text:
        return JSON.stringify({
          title: this.input.title,
          type: this.input.type,
          period: this.input.period,
          effectiveDate: this.input.effectiveDate,
          industry: this.input.industry,
          furtherExplanation: this.input.furtherExplanation,
          instructions: this.input.instructions,
          previousUtrvs: this.input.previousUtrvs,
        });
      case UtrValueType.NumericValueList:
      case UtrValueType.TextValueList:
      case UtrValueType.Table:
        return JSON.stringify({
          title: this.input.title,
          type: this.input.type,
          period: this.input.period,
          effectiveDate: this.input.effectiveDate,
          industry: this.input.industry,
          furtherExplanation: this.input.furtherExplanation,
          instructions: this.input.instructions,
          columns: this.input.columns,
          previousUtrvs: this.input.previousUtrvs,
        });
      default:
        return JSON.stringify({
          title: this.input.title,
          type: this.input.type,
          period: this.input.period,
          effectiveDate: this.input.effectiveDate,
          industry: this.input.industry,
          furtherExplanation: this.input.furtherExplanation,
          instructions: this.input.instructions,
        });
    }
  }

  private populateCommand() {
    this.prompt = this.prompt.concat(COMMAND_PROMPT[this.input.valueType] ?? INPUT_FORMAT_PROMPT[UtrValueType.Text]);
    return this;
  }

  private populateAnswerTemplate() {
    this.prompt = this.prompt.concat(ANSWER_TEMPLATE);
    return this;
  }

  private toRequestMessage(): AIPrompt {
    return {
      role: 'system',
      content: this.prompt,
    };
  }
}
