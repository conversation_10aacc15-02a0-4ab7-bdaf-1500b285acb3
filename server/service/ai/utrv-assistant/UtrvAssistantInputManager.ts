import { AdditionalContext, SupportedValueType, UtrvInputData, UtrvPromptInput, UtrvReferenceData } from './types';
import UniversalTrackerValue, { UniversalTrackerValueExtended, UniversalTrackerValuePlain } from '../../../models/universalTrackerValue';
import { UtrValueType } from '../../../models/public/universalTrackerType';
import { ActionList, DataPeriods, UtrvType } from '../../utr/constants';
import { getIndustryLevelsText } from '../../reporting/FrameworkMapping';
import { InitiativePlain } from '../../../models/initiative';
import { DateFormat, customDateFormat, getStartOfMonth } from '../../../util/date';
import { MULTI_INPUT_TYPES, SUPPORTED_VALUE_TYPES } from './constants';
import { universalTrackerFields, universalTrackerValuePlainFields, valueValidationProjection } from '../../../repository/projections';
import { ValueListRepository } from '../../../repository/ValueListRepository';
import { ObjectId } from 'bson';
import { ValueList as ValueListPlain } from '../../../models/public/valueList';

export class UtrvAssistantInputManager {
  constructor(private utrvModel: typeof UniversalTrackerValue) {}
  public async prepareUtrvPromptInput({
    initiative,
    utrv,
    additionalContext,
  }: {
    initiative: InitiativePlain;
    utrv: UniversalTrackerValueExtended;
    additionalContext?: AdditionalContext;
  }): Promise<UtrvPromptInput> {
    const valueType = (utrv.valueType ?? utrv.universalTracker?.valueType) as SupportedValueType;
    return {
      title: utrv.universalTracker?.valueLabel ?? '',
      type: utrv.universalTracker?.type ?? '',
      period: utrv.period ?? DataPeriods.Yearly,
      effectiveDate: customDateFormat(utrv.effectiveDate, DateFormat.MonthYear, false),
      unitType: utrv.universalTracker?.unitType,
      numberScale: utrv.universalTracker?.numberScale ?? '',
      unit: utrv.universalTracker?.unit ?? '',
      valueType,
      industry: getIndustryLevelsText(initiative.industry)?.industryText,
      furtherExplanation: utrv.notes?.stakeholder?.note ?? utrv?.note ?? '',
      instructions: utrv.universalTracker?.instructions,
      previousUtrvs: SUPPORTED_VALUE_TYPES.includes(valueType)
        ? await this.getPreviousUtrvs({ initiative, utrv, additionalContext })
        : [],
      ...(MULTI_INPUT_TYPES.includes(valueType) ? { columns: await this.getColumns(utrv) } : {}),
    };
  }

  private async getPreviousUtrvs({
    initiative,
    utrv,
    additionalContext,
    limit = 5,
  }: {
    initiative: InitiativePlain;
    utrv: UniversalTrackerValueExtended;
    additionalContext: AdditionalContext | undefined;
    limit?: number;
  }): Promise<UtrvReferenceData[]> {
    const utrvs: UniversalTrackerValueExtended[] = await this.utrvModel.aggregate([
      {
        $match: {
          initiativeId: initiative._id,
          universalTrackerId: utrv.universalTrackerId,
          period: utrv.period,
          deletedDate: { $exists: false },
          effectiveDate: { $lt: getStartOfMonth(utrv.effectiveDate) },
          status: { $in: [ActionList.Updated, ActionList.Verified] },
          type: { $in: [UtrvType.Actual, UtrvType.Baseline] },
        },
      },
      {
        $sort: {
          effectiveDate: -1,
        },
      },
      { $limit: limit },
      {
        $lookup: {
          from: 'universal-trackers',
          localField: 'universalTrackerId',
          foreignField: '_id',
          as: 'universalTracker',
        },
      },
      {
        $lookup: {
          from: 'value-list',
          localField: 'universalTracker.valueValidation.valueList.listId',
          foreignField: '_id',
          as: 'valueList',
        },
      },
      { $unwind: '$universalTracker' },
      {
        $project: {
          ...universalTrackerValuePlainFields,
          universalTracker: {
            ...universalTrackerFields,
            ...valueValidationProjection,
          },
        },
      },
    ]);

    const tableValueLists = await this.getTableValueLists(utrv);

    const previousUtrvs = utrvs.map((utrv) => ({
      effectiveDate: customDateFormat(utrv.effectiveDate, DateFormat.MonthYear, false),
      inputData: this.getUtrvInputData({ utrv, tableValueLists }),
    }));

    // add current utrv data as an additional context
    return additionalContext
      ? [this.getAdditionalUtrvContext({ utrv, tableValueLists, additionalContext }), ...previousUtrvs]
      : previousUtrvs;
  }

  private async getTableValueLists(utrv: UniversalTrackerValueExtended) {
    const valueType = (utrv.valueType ?? utrv.universalTracker?.valueType) as SupportedValueType;
    if (valueType !== UtrValueType.Table) {
      return [];
    }
    const tableValueListIdsSet = (utrv.universalTracker.valueValidation?.table?.columns ?? []).reduce((acc, col) => {
      if (col.listId) {
        acc.add(col.listId.toHexString());
      }
      return acc;
    }, new Set<string>());

    if (tableValueListIdsSet.size === 0) {
      return [];
    }
    return ValueListRepository.findByIds(Array.from(tableValueListIdsSet).map((id) => new ObjectId(id)));
  }

  private getAdditionalUtrvContext({
    utrv,
    tableValueLists,
    additionalContext,
  }: {
    utrv: UniversalTrackerValueExtended;
    tableValueLists: ValueListPlain[];
    additionalContext: AdditionalContext;
  }) {
    const inputData = utrv.universalTracker.valueValidation?.table?.columns.map((col) => {
      let list;
      if (col.listId && tableValueLists.length > 0) {
        list = tableValueLists.find((item) => item._id.toHexString() === col.listId?.toHexString());
      }
      return {
        code: col.code,
        value: additionalContext.inputData?.find((cell) => cell.code === col.code)?.value,
        ...(list ? { options: list.options } : {}),
      };
    });

    return {
      effectiveDate: customDateFormat(utrv.effectiveDate, DateFormat.MonthDayYear, false),
      inputData,
    };
  }

  private getUtrvInputData({
    utrv,
    tableValueLists,
  }: {
    utrv: UniversalTrackerValueExtended;
    tableValueLists: ValueListPlain[];
  }): UtrvInputData {
    const valueType = utrv.valueType ?? utrv.universalTracker.valueType;
    switch (valueType) {
      case UtrValueType.Text: {
        return utrv?.valueData?.data;
      }
      case UtrValueType.Number:
      case UtrValueType.Percentage: {
        return utrv.value;
      }
      case UtrValueType.NumericValueList:
      case UtrValueType.TextValueList: {
        return utrv.universalTracker.valueValidation?.valueList?.list?.map((col) => ({
          code: col.code,
          value: utrv.valueData?.data[col.code],
        }));
      }
      case UtrValueType.Table: {
        return utrv.universalTracker.valueValidation?.table?.columns.map((col) => {
          let list;
          if (col.listId && tableValueLists.length > 0) {
            list = tableValueLists.find((item) => item._id.toHexString() === col.listId?.toHexString());
          }
          return {
            code: col.code,
            value: utrv.valueData?.table?.[0]?.find((row) => row.code === col.code)?.value,
            ...(list ? { options: list.options } : {}),
          };
        });
      }
      default:
        return;
    }
  }

  private async getColumns(utrv: UniversalTrackerValuePlain) {
    const valueType = utrv.valueType ?? utrv.universalTracker?.valueType;
    switch (valueType) {
      case UtrValueType.NumericValueList:
      case UtrValueType.TextValueList: {
        const listId = utrv.universalTracker?.valueValidation?.valueList?.listId;
        if (!listId) {
          return [];
        }
        const valueList = await ValueListRepository.mustFindById(listId);
        return valueList.options;
      }
      case UtrValueType.Table: {
        return utrv.universalTracker?.valueValidation?.table?.columns.map((col) => ({
          code: col.code,
          name: col.name,
          type: col.type,
        }));
      }
      case UtrValueType.Text:
      case UtrValueType.Number:
      case UtrValueType.Percentage:
      default:
        return;
    }
  }
}

let instance: UtrvAssistantInputManager;

export const getUtrvAssistantInputManager = () => {
  if (!instance) {
    instance = new UtrvAssistantInputManager(UniversalTrackerValue);
  }

  return instance;
};
