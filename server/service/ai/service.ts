/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import ScorecardFactory from '../scorecard/ScorecardFactory';
import { ObjectId } from 'bson';
import { Logger } from 'winston';
import { wwgLogger } from '../wwgLogger';
import { UniversalTrackerRepository } from '../../repository/UniversalTrackerRepository';
import {
  NotApplicableTypes,
  UniversalTrackerValueExtended,
  UniversalTrackerValuePlain,
} from '../../models/universalTrackerValue';
import { InitiativeRepository } from '../../repository/InitiativeRepository';
import { getIndustryText } from '../reporting/FrameworkMapping';
import { InitiativePlain, MaterialityMap } from '../../models/initiative';
import { UniversalTrackerPlain } from '../../models/universalTracker';
import { getStandardName } from '../utr/standards';
import { standards } from '@g17eco/core';
import moment from 'moment';
import { SurveyModelPlain } from '../../models/survey';
import ContextError from '../../error/ContextError';
import { Materiality } from '../../models/materiality';
import { AIModelFactory, AIModelType, getAIModelFactory } from './AIModelFactory';
import { AIPrompt, AIResponse } from './models/AIModel';
import { getUtrvAssistantInputManager, UtrvAssistantInputManager } from './utrv-assistant/UtrvAssistantInputManager';
import { AdditionalContext, UtrvPromptInput } from './utrv-assistant/types';
import { AIUtrvSuggestion } from './types';
import { UtrvAssistantPromptGenerator } from './utrv-assistant/UtrvAssistantPromptGenerator';
import { zodResponseFormat } from 'openai/helpers/zod';
import { utrvAssistantResponseDto } from '../../routes/validation-schemas/ai-response-formats/ai-assistant';
import { DEFAULT_MAX_TOKEN } from './utrv-assistant/constants';
import { fallbackSuggestion, ToolType } from './constants';
import { ChatGPT } from './models/ChatGPT';

let instance: AiService | undefined;

export class AiService {
  constructor(
    private logger: Logger,
    private aiFactory: AIModelFactory,
    private assistantInputManager: UtrvAssistantInputManager,
    private defaultModel: AIModelType = AIModelType.ChatGPT
  ) {}

  public async runCompletion({
    messages,
    maxTokens,
    modelType = this.defaultModel,
  }: {
    messages: AIPrompt[];
    maxTokens?: number;
    modelType: AIModelType;
  }) {
    return this.aiFactory.getAiModel(modelType).runCompletion(messages, maxTokens);
  }

  private getMateriality(sdgCode: string, materialityMap?: MaterialityMap) {
    if (materialityMap) {
      for (const key of [Materiality.Low, Materiality.Medium, Materiality.High]) {
        if (materialityMap[key].includes(sdgCode)) {
          return key;
        }
      }
    }
    return Materiality.None;
  }

  public async getReportText({
    question,
    modelType = this.defaultModel,
  }: {
    question: string;
    modelType?: AIModelType;
  }) {
    const response = await this.runCompletion({
      messages: [
        {
          role: 'system',
          content: question,
        },
      ],
      modelType,
    });

    if (response.usage) {
      this.trackUsage('getReportText', response.usage, { question });
    }

    return response;
  }

  public async getSummaryMaterialSDGContributions(
    initiative: Pick<InitiativePlain, '_id' | 'industry' | 'name' | 'materialityMap'>,
    survey: Pick<SurveyModelPlain, '_id'>,
    userId: ObjectId,
    modelType: AIModelType = this.defaultModel
  ) {
    const length = 300;
    const scorecardFactory = new ScorecardFactory();
    const scorecard = await scorecardFactory.getBySurveyId(survey._id);

    const userInput = scorecard.goals.map((g) => ({
      SDG: g.sdgCode,
      Materiality: this.getMateriality(g.sdgCode, initiative.materialityMap),
      Contribution: `${g.actual ?? 0}%`,
    }));

    const response = await this.runCompletion({
      messages: [
        {
          role: 'system',
          content:
            `A company ${this.getSectorPrompt(initiative)} called "${initiative.name}" has reported ` +
            `the following SDG contributions and materiality in JSON format:\n\n` +
            `"${JSON.stringify(userInput)}"\n\n.` +
            'Your task is to create a short narrative about that data with subheadings for each SDG ' +
            'focussing only on the most material goals and indicating the contribution score.' +
            `Your narrative should be ${length} words or fewer. ` +
            'Use language that is clear, direct, and unambiguous. ' +
            'Refer to yourself as “we” or “our”, not "Our company" or "Our organization".',
        },
      ],
      modelType,
    });

    if (response.usage) {
      this.trackUsage('getSummaryMaterialSDGContributions', response.usage, {
        initiativeId: initiative._id,
        surveyId: survey._id,
        userId: userId.toString(),
      });
    }

    return response;
  }

  public async getAnswerToQuestion({
    initiative,
    survey,
    question,
    userId,
    length = 150,
    modelType = this.defaultModel,
  }: {
    initiative: Pick<InitiativePlain, '_id' | 'industry' | 'materialityMap'>;
    survey: Pick<SurveyModelPlain, '_id'>;
    question: string;
    userId: ObjectId;
    length?: number;
    modelType?: AIModelType;
  }) {
    const scorecardFactory = new ScorecardFactory();
    const scorecard = await scorecardFactory.getBySurveyId(survey._id);

    const userInput = scorecard.goals.map((g) => ({
      SDG: g.sdgCode,
      Materiality: this.getMateriality(g.sdgCode, initiative.materialityMap),
      Contribution: `${Math.round(10 * (g.actual ?? 0)) / 10}%`,
    }));

    const response = await this.runCompletion({
      messages: [
        {
          role: 'system',
          content:
            `The user wants to know about their company ${this.getSectorPrompt(initiative)}. ` +
            'The company has reported the following SDG contributions and materiality in JSON format:\n\n' +
            `"${JSON.stringify(userInput)}"\n\n.` +
            'Your task is to answer the user question while referring to the data.' +
            `Your answer should be ${length} words or fewer. ` +
            'Use language that is clear, direct, and unambiguous. ',
        },
        {
          role: 'user',
          content: question,
        },
      ],
      modelType,
    });

    if (response.usage) {
      this.trackUsage('getAnswerToQuestion', response.usage, {
        initiativeId: initiative._id,
        surveyId: survey._id,
        question,
        userId: userId.toString(),
      });
    }

    return response;
  }

  private getSectorPrompt(initiative: Pick<InitiativePlain, 'industry'>): string {
    if (!initiative.industry) {
      return '';
    }
    const sector = getIndustryText(initiative.industry);
    return `in the ${sector} sector `;
  }

  private getStandardsContextPrompt(utr: Pick<UniversalTrackerPlain, 'type'>): string {
    if (!standards[utr.type]) {
      // @TODO - need to support user's currently selected standard on the frontend
      return '';
    }

    const standard = getStandardName(utr.type);
    return (
      `Include any relevant background information such as "${standard}" to support your response, ` +
      `but avoid referencing it explicitly in your explanation. `
    );
  }

  public async getFurtherNotesDraft(
    utrv: UniversalTrackerValueExtended,
    draftData: Pick<UniversalTrackerValuePlain, 'value' | 'unit' | 'numberScale' | 'valueData'>,
    userId: ObjectId,
    modelType: AIModelType = this.defaultModel
  ) {
    const length = 150;
    const utr = utrv.universalTracker;

    const initiative = await InitiativeRepository.findMainUtrvInitiative(utrv);

    if (!initiative) {
      throw new ContextError('Invalid Initiative for Universal Tracker Value', {
        utrvId: utrv._id,
        initiativeId: utrv.initiativeId,
      });
    }

    const reportingPeriod = moment(utrv.effectiveDate).format('MMMM YYYY');
    const { previousUtrvs = [] } = await this.assistantInputManager.prepareUtrvPromptInput({ initiative, utrv });

    const convertTableData = () => {
      const tableData = draftData.valueData?.table ?? [[]];
      return Object.fromEntries(tableData.entries());
    };

    const naType =
      draftData.valueData?.notApplicableType === NotApplicableTypes.NA ? NotApplicableTypes.NA : NotApplicableTypes.NR;

    const context = {
      title: utr.name,
      question: utr.valueLabel,
      description: utr.description,
      answers: draftData.valueData?.notApplicableType
        ? naType
        : {
            value: draftData.value ?? utrv.value ?? null,
            unit: draftData.unit,
            numberScale: draftData.numberScale,
            description: draftData.valueData?.data === 'string' ? draftData.valueData.data : null,
            tableData: convertTableData(),
            ...(previousUtrvs.length > 0 ? { previousUtrvs } : {}),
          },
    };

    const systemPrompt =
      `As a company ${this.getSectorPrompt(
        initiative
      )}, you have answered the following sustainability question: \n\n` +
      `"${context.question}: ${context.description}".\n\n` +
      `Your answer was for the reporting period of ${reportingPeriod}: ` +
      `${JSON.stringify(context.answers)}.\n\n` +
      'Provide explanatory notes about that answer, utilizing all the data provided. ' +
      `${this.getStandardsContextPrompt(utr)} ` +
      `Your explanation should be ${length} words or fewer. ` +
      'Use language that is clear, direct, and unambiguous. ' +
      'Refer to yourself as “we” or “our”, not "Our company" or "Our organization".';

    const response = await this.runCompletion({
      messages: [
        {
          role: 'system',
          content: systemPrompt,
        },
      ],
      modelType,
    });

    if (response.usage) {
      this.trackUsage('getFurtherNotesDraft', response.usage, {
        initiativeId: initiative._id.toString(),
        utrId: utr._id.toString(),
        userId: userId.toString(),
      });
    }

    return response;
  }

  public async getInputTextDraft(utrId: ObjectId, userId: ObjectId, modelType: AIModelType = this.defaultModel) {
    const length = 150;

    const utr = await UniversalTrackerRepository.findById(utrId);
    if (!utr) {
      throw new ContextError('Invalid Universal Tracker Id', { utrId });
    }

    const context = {
      // title: utr.name,
      question: utr.valueLabel,
      description: utr.description,
    };

    const systemPrompt =
      'As a company, you have answered the following sustainability question: \n\n' +
      `"${context.question}: ${context.description}".\n\n` +
      'Create an answer for this question from the point of view of the company ' +
      `Provide and answer for this question, in less than ${length} words. ` +
      'Use language that is clear, direct, and unambiguous. ' +
      'Refer to yourself as “we” or “our”, not "Our company" or "Our organization".';

    const response = await this.runCompletion({
      messages: [
        {
          role: 'system',
          content: systemPrompt,
        },
      ],
      modelType,
    });

    if (response.usage) {
      this.trackUsage('getInputTextDraft', response.usage, { utrId, userId: userId.toString() });
    }

    return response;
  }

  private trackUsage(serviceName: string, usage: AIResponse['usage'], contextData: {}) {
    // @TODO We should keep track of usage in a better place in the future...
    this.logger.info('AI Service usage', {
      serviceName,
      usage,
      contextData,
    });
  }

  public async getUtrvAssistantResponse({
    initiative,
    utrv,
    additionalContext,
    aiModelType = this.defaultModel,
  }: {
    initiative: InitiativePlain;
    utrv: UniversalTrackerValueExtended;
    additionalContext?: AdditionalContext;
    aiModelType?: AIModelType;
  }): Promise<{ modelVersion: string; promptInput: UtrvPromptInput; content: AIUtrvSuggestion }> {
    const promptInput = await this.assistantInputManager.prepareUtrvPromptInput({
      initiative,
      utrv,
      additionalContext,
    });
    const prompt = new UtrvAssistantPromptGenerator(promptInput).generatePrompt();
    const responseFormat = zodResponseFormat(utrvAssistantResponseDto, 'utrvAssistantExtraction');
    const aiModel = this.aiFactory.getAiModel(aiModelType);
    const response = await aiModel.parseCompletion<AIUtrvSuggestion>([prompt], DEFAULT_MAX_TOKEN, responseFormat);
    return { modelVersion: aiModel.getModelVersion(), promptInput, content: response.content || fallbackSuggestion };
  }

  public async getDocumentUtrvAssistantResponse({
    initiative,
    utrv,
    assistantId,
    relatedDocumentIds,
  }: {
    initiative: InitiativePlain;
    utrv: UniversalTrackerValueExtended;
    assistantId: string;
    relatedDocumentIds: string[];
  }) {
    this.logger.info('Asking AI to answer UTR...', { utrvId: utrv._id, initiativeId: initiative._id });
    const promptInput = await this.assistantInputManager.prepareUtrvPromptInput({ initiative, utrv });
    const aiModel = this.aiFactory.getAiModel(AIModelType.ChatGPT) as ChatGPT;
    const prompt = new UtrvAssistantPromptGenerator(promptInput).generatePrompt();
    const threadMessageContent = `
      The attached files are the documents to analyze.
      Please answer the UTR based on the content of the uploaded documents if have any and the previousUtrvs.
      Return only structured data (no explanations or commentary).
      `
      .trim()
      .concat(prompt.content);
    const result = await aiModel.runThreadWithAssistant({
      assistantId,
      message: {
        role: 'user',
        content: threadMessageContent,
        attachments: relatedDocumentIds.map((id) => ({
          file_id: id,
          tools: [{ type: ToolType.CodeInterpreter }],
        })),
      },
      jsonSchema: utrvAssistantResponseDto,
    });
    return { modelVersion: aiModel.getModelVersion(), promptInput, content: result || fallbackSuggestion };
  }
}

export const getAiService = () => {
  if (!instance) {
    instance = new AiService(wwgLogger, getAIModelFactory(), getUtrvAssistantInputManager(), AIModelType.ChatGPT);
  }
  return instance;
};
