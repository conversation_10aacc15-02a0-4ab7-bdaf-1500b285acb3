import { HydratedDocument } from 'mongoose';
import { Background<PERSON>ob<PERSON><PERSON>, JobStatus, JobType, Task, TaskType } from '../../../models/backgroundJob';
import { ObjectId } from 'bson';
import { UserPlain } from '../../../models/user';
import { ValueData } from '../../../models/public/universalTrackerValueType';
import { RelevantDocumentMappings } from '../document-utr-mapping/types';

export type AutoAnswerUtrv = {
  _id: ObjectId;
  universalTrackerId: ObjectId;
  utrCode: string;
}

type SetupContext = Pick<WorkflowCreate, 'useDocumentLibrary' | 'isOverwriteMetric' | 'surveyId'> & {
  utrvs: AutoAnswerUtrv[];
};
export interface TaskAIAutoAnswerSetup extends Task<SetupContext> {
  type: TaskType.AIAutoAnswerSetup;
}

type PrepareDocumentsContext = Omit<SetupContext, 'useDocumentLibrary'> & {
  mappings: RelevantDocumentMappings[];
};
export interface TaskAIAutoAnswerPrepareDocuments extends Task<PrepareDocumentsContext> {
  type: TaskType.AIAutoAnswerPrepareDocuments;
}

type InitialProcessContext = {
  utrvId: ObjectId;
  isOverwriteMetric: boolean;
  relatedDocumentIds?: string[];
  assistantId?: string;
};
export type ProcessedContext = Pick<ProcessedAnswerResult, 'isSuccess' | 'errorMessage'> & InitialProcessContext;
export interface TaskAIAutoAnswerProcess<T extends InitialProcessContext = InitialProcessContext> extends Task<T> {
  type: TaskType.AIAutoAnswerProcess;
}

type CompleteContext = {
  completedUtrvs: ObjectId[];
  errorUtrvs: ObjectId[];
};
export interface TaskAIAutoAnswerComplete extends Task<CompleteContext> {
  type: TaskType.AIAutoAnswerComplete;
}

type CleanupContext = {
  documentIdMap: Record<string, string>;
};
export interface TaskAIAutoAnswerCleanup extends Task<CleanupContext> {
  type: TaskType.AIAutoAnswerCleanup;
}

export type AIAutoAnswerTask =
  | TaskAIAutoAnswerSetup
  | TaskAIAutoAnswerPrepareDocuments
  | TaskAIAutoAnswerProcess
  | TaskAIAutoAnswerComplete
  | TaskAIAutoAnswerCleanup;

export type SupportedJobPlain = Omit<BackgroundJobPlain<AIAutoAnswerTask[]>, 'initiativeId' | 'userId'> & {
  type: JobType.AIAutoAnswerSurvey;
  initiativeId: ObjectId;
  userId: ObjectId;
};
export type SupportedJobModel = HydratedDocument<SupportedJobPlain>;

export interface WorkflowCreate {
  initiativeId: ObjectId;
  surveyId: ObjectId;
  userId: ObjectId;
  useDocumentLibrary: boolean;
  isOverwriteMetric: boolean;
}

export interface CreatedJob {
  jobId: string;
  status: JobStatus;
}

export interface ProcessedAnswerResult {
  isSuccess: boolean;
  errorMessage?: string;
  value?: number;
  valueData?: ValueData;
  note?: string;
}

export interface NotificationParams {
  job: SupportedJobModel;
  user: Pick<UserPlain, '_id' | 'email'>;
  title: string;
  content: string;
  surveyId: ObjectId;
  initiativeId: ObjectId;
}
