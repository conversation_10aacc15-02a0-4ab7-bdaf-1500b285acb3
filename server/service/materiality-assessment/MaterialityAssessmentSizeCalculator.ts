import {
  CreateSurveyDto,
  MaterialityAssessmentUtrCodes,
} from '../../routes/validation-schemas/materiality-assessment';
import { MaterialityAssessmentScope } from './types';

export type ScopeCalculatorContext = Omit<CreateSurveyDto, MaterialityAssessmentUtrCodes.Sector>;

export class MaterialityAssessmentScopeCalculator {
  constructor(
  ) {}

  static getStartupScopeTag(context: ScopeCalculatorContext): MaterialityAssessmentScope | undefined {
    /*
      operation-time
        blue	  = Less than a year
        red	    = 1-3 years
        green	  = 3-5 years
        orange  =	More than 5 years
    */

    const isStartup = ['blue', 'red', 'green'].includes(context[MaterialityAssessmentUtrCodes.OperationTime]);
    if (isStartup) {
      return MaterialityAssessmentScope.Startup;
    }
    return undefined;
  }

  static getSizeScopeTag(context: ScopeCalculatorContext): MaterialityAssessmentScope {
    /*
      num_staff
        blue    =	1 employee (self-employed individual)
        red	    = 2 to 50
        orange  = 51 and 250
        purple  = 251 to 2,000
        yellow  = 2,001-9,999
        teal	  = 10,000 or more

      annual_sales
        blue    = < $250k
        red     = $250k - $1m
        orange	= $1m - $50m
        purple	= $50m - $500m
        yellow	= $500m - $5b
        teal	  = $5b +

      capital_employed
        blue	  = < $250k
        red	    = $250k - $1m
        orange	= $1m - $50m
        purple	= $50m - $500m
        yellow	= $500m - $5b
        teal	  = $5b +
    */

    const sizes = [
      { option: 'blue', scope: MaterialityAssessmentScope.Solopreneur },
      { option: 'red', scope: MaterialityAssessmentScope.Micro },
      { option: 'orange', scope: MaterialityAssessmentScope.SME },
      { option: 'purple', scope: MaterialityAssessmentScope.MidCap },
      { option: 'yellow', scope: MaterialityAssessmentScope.MNC },
      { option: 'teal', scope: MaterialityAssessmentScope.Large },
    ];

    const sizeMetrics = [
      MaterialityAssessmentUtrCodes.NumStaff,
      MaterialityAssessmentUtrCodes.AnnualSales,
      MaterialityAssessmentUtrCodes.CapitalEmployed,
    ] as const;

    // In order of largest-to-smallest, if any answer within that range, then use that as the correct size
    const size = sizes.reverse().find((size) => {
      const count = sizeMetrics.filter((code) => context[code] === size.option);
      return count.length >= 1; // If 1 answers are in this range, that is the scope
    });

    if (!size) {
      return MaterialityAssessmentScope.Solopreneur; // No clear size, so default to solopreneur
    }
    return size.scope;
  }
}
