/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import MaterialityMetric, { MaterialityAssessmentType, MaterialityMetricWithUtrAndValueListPlain } from '../../models/materialityMetric';
import { MaterialityAssessmentScope } from './types';
import { ObjectId } from 'bson';

export class MaterialityMetricRepository {
  public static async findMetricsByScope({
    scope,
    types,
  }: {
    scope: MaterialityAssessmentScope[];
    types: MaterialityAssessmentType[];
  }) {
    return MaterialityMetric.find({
      tags: { $in: scope },
      type: { $in: types },
    }).lean();
  }

  public static async findFinancialByUtrCodes(utrCodes: string[]): Promise<MaterialityMetricWithUtrAndValueListPlain<ObjectId>[]> {
    return MaterialityMetric.find({ utrCode: { $in: utrCodes }, type: MaterialityAssessmentType.Financial })
      .populate(['utr', 'valueList'])
      .lean();
  }

  public static async findImpactByUtrCodes(utrCodes: string[]): Promise<MaterialityMetricWithUtrAndValueListPlain<ObjectId>[]> {
    return MaterialityMetric
      .find({ utrCode: { $in: utrCodes }, type: MaterialityAssessmentType.Impact })
      .populate(['utr', 'valueList'])
      .lean();
  }
}
