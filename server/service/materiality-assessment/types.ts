import { MaterialTopicCategories, UtrMapping } from '../../models/materialTopics';
import { AssessmentScoreMap } from '../ai/MaterialityAssessmentAIService';
import { MaterialityMetricWithUtrAndValueListPlain } from '../../models/materialityMetric';
import { MaterialitySizeMultipliers } from './assessments/MaterialityLookupCalculator';
import { CreateSurveyDto } from '../../routes/validation-schemas/materiality-assessment';
import { UserPlain } from '../../models/user';
import { ObjectId } from 'bson';

export type AssessmentMinData = {
  code: string;
  score: number;
  relativeScore?: number;
};

export type AssessmentData = AssessmentMinData & {
  name?: string;
  utrMapping?: UtrMapping[];
  categories?: MaterialTopicCategories;
  description?: string;
  action?: string;
};

export type DoubleMaterialityAssessmentData = AssessmentData & {
  financialScore: number | undefined;
  nonFinancialScore: number | undefined;
  financialRelativeScore: number | undefined;
  nonFinancialRelativeScore: number | undefined;
}

export type AssessmentInputs = Map<string, string[] | undefined>;

export enum AssessmentResultType {
  Financial = 'financial',
  Impact = 'nonFinancial',
}

export interface AssessmentResult<T extends AssessmentMinData = AssessmentMinData> {
  [AssessmentResultType.Financial]: T[];
  [AssessmentResultType.Impact]: T[];
  nonFinancialReferences?: { url: string; label: string }[];
  debug?: {
    [AssessmentResultType.Financial]: { [key: string]: AssessmentScoreMap },
    [AssessmentResultType.Impact]: { [key: string]: AssessmentScoreMap }
  }
}

export enum MaterialityAssessmentScope {
  Startup = 'startup',
  Solopreneur = 'solopreneur',
  Micro = 'micro',
  SME = 'sme',
  MidCap = 'mid-cap',
  MNC = 'mnc',
  Large = 'large',
}

export interface MetricConfiguration {
  scopeMetrics: Map<MaterialityAssessmentScope, MaterialityMetricWithUtrAndValueListPlain[]>;
  weights: MaterialitySizeMultipliers;
}

export interface CreateAssessmentParams {
  initiativeId: ObjectId;
  user: UserPlain;
  returnUrl: string;
  promoCode?: string;
  context: CreateSurveyDto;
  metadata: {
    [key: string]: string;
  };
}