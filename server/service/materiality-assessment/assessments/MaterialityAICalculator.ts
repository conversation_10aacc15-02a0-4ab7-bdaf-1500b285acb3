/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { MaterialTopicPlain } from '../../../models/materialTopics';
import { MaterialityAssessmentType, MaterialityMetricWithUtrAndValueListPlain } from '../../../models/materialityMetric';
import { MaterialityAssessmentAIService, ImpactScores } from '../../ai/MaterialityAssessmentAIService';
import { MaterialTopicRepository } from '../MaterialTopicRepository';
import { AssessmentInputs } from '../types';
import { AIModelType } from '../../ai/AIModelFactory';

export class MaterialityAICalculator {
  private materialityTopics: undefined | Pick<MaterialTopicPlain, 'code' | 'name' | 'utrMapping' | 'categories'>[] =
    undefined;

  constructor(
    private materialityAIService: MaterialityAssessmentAIService,
    private inputs: AssessmentInputs,
    private materialityMetrics: Pick<
      MaterialityMetricWithUtrAndValueList<PERSON>lain,
      'utr' | 'utrCode' | 'tags' | 'valueList'
    >[],
    private companyInfo: { name: string; industry?: string, country?: string }
  ) {}

  private getQuestionsAndAnswers(): { question: string; answers: string[] }[] {
    return this.materialityMetrics
      .filter((metric) => metric.tags?.includes(MaterialityAssessmentType.Impact))
      .map((metric) => {
        const answerCodes = this.inputs.get(metric.utrCode);
        const question = metric.utr?.name;
        if (!question) {
          return { question: '', answers: [] };
        }

        const answers = answerCodes
          ? metric.valueList?.options.filter((option) => answerCodes.includes(option.code))
          : [];
        if (!answers) {
          return { question, answers: ['Not answered'] };
        }

        return { question, answers: answers.map((answer) => answer.name) };
      });
  }

  private async loadMaterialTopics() {
    this.materialityTopics = await MaterialTopicRepository.findImpactMaterialTopics({ name: 1, code: 1 });
  }

  public async getResult({ modelType = AIModelType.ChatGPT }: { modelType: AIModelType }): Promise<ImpactScores> {
    await this.loadMaterialTopics();
    if (!this.materialityTopics) {
      return { scores: {}, usage: 0, references: [], topicCount: 0, errorCount: 0 };
    }

    const questionsAndAnswers = this.getQuestionsAndAnswers();
    const topics = this.materialityTopics?.map((topic) => ({
      code: topic.code,
      name: topic.name,
    }));

    return this.materialityAIService.getImpactScores(topics, questionsAndAnswers, this.companyInfo, modelType);
  }
}
