/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { MaterialityMetric } from '../../../models/materialityMetric';
import { AssessmentInputs } from '../types';

type AssessmentScoreMap = { [materialityCode: string]: number };

export enum CoreQuestion {
  Sector = 'materiality-2024/sector',
  StakeholderEngagement = 'materiality-2024/stakeholder-engagement',
  OrgStructure = 'materiality-2024/org-structure',
  NumStaff = 'materiality-2024/num-staff',
  AnnualSales = 'materiality-2024/annual-sales',
  CapitalEmployed = 'materiality-2024/capital-employed',
  HqSituated = 'materiality-2024/hq-situated',
  OperationsSituated = 'materiality-2024/operations-situated',
  CustomersSituated = 'materiality-2024/customers-situated',
  SuppliersSituated = 'materiality-2024/suppliers-situated',
  StakeholdersSituated = 'materiality-2024/stakeholders-situated',
  FinanceSituated = 'materiality-2024/finance-situated',

  OperationTime = 'materiality-2024/operation-time',
}

export interface MaterialitySizeMultipliers {
  getSectorScoresTop10: number;
  getSectorScoresRemainder: number;
  getEngagementScores: number;
  getOrgStructureScores: number;
  getSizeScores: number;
  getOperationTimeScores: number;
  getLocationScoresHq: number;
  getLocationScoresOperations: number;
  getLocationScoresCustomer: number;
  getLocationScoresSuppliers: number;
  getLocationScoresStakeholders: number;
  getLocationScoresFinance: number;
}

export class MaterialityLookupCalculator {
  private debugMode: Boolean = false;
  private debugData: {
    [materialityCode: string]: { [utrCode: string]: number }
  } = {};

  constructor(
    private inputs: AssessmentInputs,
    private materialityMetrics: Pick<MaterialityMetric, 'utrCode' | 'options'>[],
    private multipliers: MaterialitySizeMultipliers,
  ) {}

  private getMaterialityMetric(utrCode: string): Pick<MaterialityMetric, 'utrCode' | 'options'> | undefined {
    return this.materialityMetrics?.find((metric) => metric.utrCode === utrCode);
  }

  private getScoresByUtrCode(utrCode: string): AssessmentScoreMap {
    const answerCodes = this.inputs.get(utrCode);
    if (!answerCodes) {
      return {} as AssessmentScoreMap;
    }

    const materialityMetric = this.getMaterialityMetric(utrCode);
    if (!materialityMetric) {
      return {} as AssessmentScoreMap;
    }

    return materialityMetric.options.reduce((acc, option) => {
      if (answerCodes.includes(option.optionCode)) {
        option.scores.forEach(({ materialityCode, score }) => {
          acc[materialityCode] = (acc[materialityCode] ?? 0) + Number(score);
        });
      }
      return acc;
    }, {} as AssessmentScoreMap);
  }

  private addScores(currentScores: AssessmentScoreMap, scoresToAdd: AssessmentScoreMap, utrCode?: string): AssessmentScoreMap {
    Object.keys(scoresToAdd).forEach((materialityCode) => {
      const score = Number(scoresToAdd[materialityCode]);
      currentScores[materialityCode] = (currentScores[materialityCode] ?? 0) + score;
      if (this.debugMode && utrCode) {
        if (!this.debugData[materialityCode]) {
          this.debugData[materialityCode] = {};
        }
        this.debugData[materialityCode][utrCode] = score;
      }

    });
    return currentScores;
  }

  public getDebugData() {
    return this.debugData;
  }

  private multiplyScores(
    currentScores: AssessmentScoreMap,
    multiplierFunc: (code: string, value: number) => number
  ): AssessmentScoreMap {
    Object.keys(currentScores).forEach((materialityCode) => {
      currentScores[materialityCode] =
        Number(currentScores[materialityCode]) * multiplierFunc(materialityCode, currentScores[materialityCode]);
    });
    return currentScores;
  }

  private getTop(scores: AssessmentScoreMap, top = 10): string[] {
    return Object.entries(scores)
      .sort(([, a], [, b]) => b - a)
      .slice(0, top)
      .map((score) => score[0]);
  }

  private getSectorScores(): AssessmentScoreMap {
    const sectorScores = this.getScoresByUtrCode(CoreQuestion.Sector);
    // top 10 material topics get a fixed amount, and the rest get another fixed amount
    const topMaterial = this.getTop(sectorScores, 10);
    const multiplierFunc = (code: string) => (topMaterial.includes(code) ? this.multipliers.getSectorScoresTop10 : this.multipliers.getSectorScoresRemainder);
    return this.multiplyScores(sectorScores, multiplierFunc);
  }

  private getEngagementScores(): AssessmentScoreMap {
    const engagementScores = this.getScoresByUtrCode(CoreQuestion.StakeholderEngagement);
    return this.multiplyScores(engagementScores, () => this.multipliers.getEngagementScores);
  }

  private getOrgStructureScores(): AssessmentScoreMap {
    const orgStructureScores = this.getScoresByUtrCode(CoreQuestion.OrgStructure);
    return this.multiplyScores(orgStructureScores, () => this.multipliers.getOrgStructureScores);
  }

  private getSizeScores(): AssessmentScoreMap {
    const sizeScores: AssessmentScoreMap = {};
    // Check the selected option for each question
    // Find the highest tier
    // Use that score

    const sizes = [
      { option: 'blue' }, // scope: MaterialityAssessmentScope.Solopreneur },
      { option: 'red' }, // scope: MaterialityAssessmentScope.Micro },
      { option: 'orange' }, // scope: MaterialityAssessmentScope.SME },
      { option: 'purple' }, // scope: MaterialityAssessmentScope.MidCap },
      { option: 'yellow' }, // scope: MaterialityAssessmentScope.MNC },
      { option: 'teal' }, // scope: MaterialityAssessmentScope.Large },
    ];

    const questions = [CoreQuestion.NumStaff, CoreQuestion.AnnualSales, CoreQuestion.CapitalEmployed];

    let questionMatch: CoreQuestion = questions[0];

    // In order of largest-to-smallest, if any answer within that range, then use that as the correct size
    sizes.reverse().find((size) => {
      return questions.find((question) => {
        const answer = this.inputs.get(question)?.[0] ?? 'blue';
        questionMatch = question;
        return answer === size.option;
      });
    });

    this.addScores(sizeScores, this.getScoresByUtrCode(questionMatch));
    return this.multiplyScores(sizeScores, () => this.multipliers.getSizeScores);
  }

  private getOperationTimeScores(): AssessmentScoreMap {
    const operationTimeScores = this.getScoresByUtrCode(CoreQuestion.OperationTime);
    return this.multiplyScores(operationTimeScores, () => this.multipliers.getOperationTimeScores);
  }

  private getLocationScores(): AssessmentScoreMap {
    const locationScores: AssessmentScoreMap = {};
    this.addScores(
      locationScores,
      this.multiplyScores(this.getScoresByUtrCode(CoreQuestion.HqSituated), () => this.multipliers.getLocationScoresHq),
      CoreQuestion.HqSituated
    );
    this.addScores(
      locationScores,
      this.multiplyScores(this.getScoresByUtrCode(CoreQuestion.OperationsSituated), () => this.multipliers.getLocationScoresOperations),
      CoreQuestion.OperationsSituated
    );
    this.addScores(
      locationScores,
      this.multiplyScores(this.getScoresByUtrCode(CoreQuestion.CustomersSituated), () => this.multipliers.getLocationScoresCustomer),
      CoreQuestion.CustomersSituated
    );
    this.addScores(
      locationScores,
      this.multiplyScores(this.getScoresByUtrCode(CoreQuestion.SuppliersSituated), () => this.multipliers.getLocationScoresSuppliers),
      CoreQuestion.SuppliersSituated
    );
    this.addScores(
      locationScores,
      this.multiplyScores(this.getScoresByUtrCode(CoreQuestion.StakeholdersSituated), () => this.multipliers.getLocationScoresStakeholders),
      CoreQuestion.StakeholdersSituated
    );
    this.addScores(
      locationScores,
      this.multiplyScores(this.getScoresByUtrCode(CoreQuestion.FinanceSituated), () => this.multipliers.getLocationScoresFinance),
      CoreQuestion.FinanceSituated
    );
    return locationScores;
  }

  public async getResult(debug: Boolean = false): Promise<{ scores: AssessmentScoreMap }> {
    const scores: AssessmentScoreMap = {};

    this.debugMode = debug;

    // Work out Core score first
    this.addScores(scores, this.getSectorScores(), CoreQuestion.Sector);
    this.addScores(scores, this.getEngagementScores(), CoreQuestion.StakeholderEngagement);
    this.addScores(scores, this.getOrgStructureScores(), CoreQuestion.OrgStructure);
    this.addScores(scores, this.getSizeScores(), 'size-scores');
    this.addScores(scores, this.getOperationTimeScores(), CoreQuestion.OperationTime);
    this.addScores(scores, this.getLocationScores());

    this.multiplyScores(scores, () => 100); // Core scores should have 100x more weight

    // Now calculate non-core scores
    const coreQuestions: string[] = Object.values(CoreQuestion);
    const nonCoreScores: AssessmentScoreMap = {};
    this.inputs.forEach((_, utrCode) => {
      if (coreQuestions.includes(utrCode)) {
        return; // Skip as already counted above
      }
      this.addScores(nonCoreScores, this.getScoresByUtrCode(utrCode), utrCode);
    });

    // Add the two together
    this.addScores(scores, nonCoreScores);

    return { scores };
  }
}
