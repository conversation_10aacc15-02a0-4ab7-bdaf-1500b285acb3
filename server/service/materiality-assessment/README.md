## Materiality Assessment Tool

# Overview

- The assessment is a series of multiple-choice questions
- Sustainability team manage a series of spreadsheets on Google Drive, with formulas, and that result in lookup tables for each question in JSON format that are manually checked into the codebase. Every question results in it's own file.
- The tool reads the answers for each question, reads the corresponding lookup file, and extracts the materiality scores for the answer.
- The tool then has some logic around how it adds up the resulting materiality scores for each question. Some have a weighting applied, most just get summed.


# Generating the JSON Lookup files

1) Sustainability Manage spreadsheets with scores. Currently located on Google Drive:
  `https://drive.google.com/drive/u/0/folders/1pwMpIHNtVZxT6cITGRYD3E08gSd9ACpT?ths=true`

  - Each set of questions is in it's own folder. 
  - In that folder there is an index file for the questions, with a filename that starts with `00. xxx`. That file has a lookup column for each question code.
  - The lookup code is used to find a corresponding file in the same folder, which in turn has a materiality score lookup for each possible answer to that question.

2) Download files to `/path/to/files`

3) Update `MaterialityAssessmentScriptService.ts` file which contains a list of the index files to the lookups. They usually start with `00. xxxx` and are in a separate folder.

3) Execute: `npm run /path/to/files`. This will generate files inside `server/service/materiality-assessment/json/`

4) git add/commit the files


# Materiality Assessment Inputs

The tool has two types of questions, `Core` and `Non-Core`.
1) Core questions, are common for all companies. Currently these classify the company by:
  - Sector
  - Organization structure
  - Size
  - Location
2) Non-core questions. We use the output of the core questions, to classify the company will then lead to a series of relevant non-core questions. The current categories include:
  - Solopreneur
  - Micro
  - SME

# Materiality Assessment Outputs

Once completed, the output of the tools is a list of `Materiality Codes with a Score`. Ultimately there should be metrics or metric groups that get associated with each Materiality Code, and based on the scores this would allow us to create a bespoke set of metrics for their materiality scores.

