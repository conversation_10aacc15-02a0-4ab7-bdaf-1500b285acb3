/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import { wwgLogger } from '../wwgLogger';
import BackgroundJob, { JobType, TaskStatus, TaskType } from '../../models/backgroundJob';
import { generatedUUID } from '../crypto/token';
import { BackgroundJobService, getBackgroundJobService } from '../background-process/BackgroundJobService';
import { MaterialityAssessmentContext, SupportedJobModel } from './background-job/types';

export type AssessmentInputs = Map<string, string[] | undefined>;

export class MaterialityAssessmentBackgroundJobService {
  constructor(private backgroundJobService: BackgroundJobService) {}

  private getIndempotencyKey(context: { initiativeId: ObjectId; surveyId: ObjectId }) {
    return `initiativeId:${context.initiativeId}-surveyId:${context.surveyId}`;
  }

  private async getExistingJob(idempotencyKey: string) {
    return BackgroundJob.find<SupportedJobModel>({
      type: JobType.MaterialityAssessmentScores,
      idempotencyKey,
    })
      .sort({ created: -1 })
      .limit(1)
      .exec()
      .then((d) => d[0]);
  }

  public async findExistingJob(context: {
    initiativeId: ObjectId;
    surveyId: ObjectId;
  }): Promise<SupportedJobModel | undefined> {
    return this.getExistingJob(this.getIndempotencyKey(context));
  }

  public async createOrReturnJob(context: { initiativeId: ObjectId; surveyId: ObjectId, userId: ObjectId }): Promise<SupportedJobModel> {
    const existingJob = await this.getExistingJob(this.getIndempotencyKey(context));
    if (existingJob) {
      return existingJob;
    }

    return this.createJob(context);
  }

  public async createJob(context: Omit<MaterialityAssessmentContext, 'result'>): Promise<SupportedJobModel> {
    const job = (await BackgroundJob.create({
      name: 'Generate Materiality Tracker Scores',
      type: JobType.MaterialityAssessmentScores,
      idempotencyKey: this.getIndempotencyKey(context),
      userId: context.userId,
      tasks: [
        {
          id: generatedUUID(),
          type: TaskType.GenerateMaterialityAssessmentScores,
          name: 'Generate Scores',
          status: TaskStatus.Pending,
          data: {
            initiativeId: context.initiativeId,
            surveyId: context.surveyId,
            modelType: context.modelType,
          },
        },
        {
          id: generatedUUID(),
          type: TaskType.GenerateMaterialityAssessmentMetricGroup,
          name: 'Generate Materiality Metric Group',
          status: TaskStatus.Pending,
          data: {
            initiativeId: context.initiativeId,
            surveyId: context.surveyId,
          },
        },
      ],
    })) as unknown as SupportedJobModel;

    this.backgroundJobService.runFromJob(job).catch(wwgLogger.error);
    return job;
  }
}

let instance: MaterialityAssessmentBackgroundJobService;
export function getMaterialityAssessmentBackgroundJobService() {
  if (!instance) {
    instance = new MaterialityAssessmentBackgroundJobService(getBackgroundJobService());
  }
  return instance;
}
