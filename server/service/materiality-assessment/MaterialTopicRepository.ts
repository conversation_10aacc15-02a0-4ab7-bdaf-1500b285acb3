/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { ProjectionType } from 'mongoose';
import MaterialTopic, { MaterialTopicPlain } from '../../models/materialTopics';
import { MaterialityAssessmentType } from '../../models/materialityMetric';
import { MaterialityAssessmentScope } from './types';

export class MaterialTopicRepository {
  public static async findByCodes(codes: string[]): Promise<MaterialTopicPlain[]> {
    return MaterialTopic.find({ code: { $in: codes } }).lean();
  }

  public static async findMaxScore(
    scope: MaterialityAssessmentScope,
    type: MaterialityAssessmentType
  ): Promise<number> {
    /**
     * After running internal release, scopeScores will have "type" field.
     * Currently falling back to "financial" if "type" is not present.
     */
    const result = await MaterialTopic.aggregate([
      {
        $match: {
          'scopeScores.scope': scope,
        },
      },
      {
        $unwind: '$scopeScores',
      },
      {
        $match: {
          'scopeScores.scope': scope,
          $or: [
            { 'scopeScores.type': type },
            {
              'scopeScores.type': { $exists: false },
              $expr: { $eq: [type, MaterialityAssessmentType.Financial] },
            },
            {
              'scopeScores.type': null,
              $expr: { $eq: [type, MaterialityAssessmentType.Financial] },
            },
          ],
        },
      },
      {
        $project: {
          maxScore: { $ifNull: ['$scopeScores.maxScore', 0] }
        }
      },
      {
        $group: {
          _id: null,
          maxScore: { $max: '$maxScore' }
        }
      },
      {
        $project: {
          _id: 0,
          maxScore: 1
        }
      }
    ]);

    return result[0]?.maxScore ?? 0;
  }

  public static async findImpactMaterialTopics(
    projection?: ProjectionType<MaterialTopicPlain>
  ): Promise<MaterialTopicPlain[]> {
    return MaterialTopic.find({ types: MaterialityAssessmentType.Impact }, projection)
      .lean<MaterialTopicPlain[]>()
      .exec();
  }

  // Not used yet
  // public static async findFinancialMaterialTopics(
  //   projection?: ProjectionType<MaterialTopicPlain>
  // ): Promise<MaterialTopicPlain[]> {
  //   return MaterialTopic.find({ types: MaterialityAssessmentType.Impact }, projection)
  //     .lean<MaterialTopicPlain[]>()
  //     .exec();
  // }
}
