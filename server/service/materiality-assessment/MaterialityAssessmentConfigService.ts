import UserError from '../../error/UserError';
import { MaterialityAssessmentConfig, UpdateImpactScope } from '../../models/materiality';
import {
  getMaterialityAssessmentBackgroundJobService,
  MaterialityAssessmentBackgroundJobService,
} from './MaterialityAssessmentBackgroundJobService';
import { ObjectId } from 'bson';
import { getMTPPTXReportService } from '../pptx-report/MTPPTXReportService';
import { SurveyRepository } from '../../repository/SurveyRepository';
import { getMaterialityMetricGroupService } from './MaterialityMetricGroupService';

export class MaterialityAssessmentConfigService {
  constructor(
    private backgroundJobService: MaterialityAssessmentBackgroundJobService,
    private pptxService: ReturnType<typeof getMTPPTXReportService>,
    private surveyRepo: typeof SurveyRepository,
    private metricGroupService: ReturnType<typeof getMaterialityMetricGroupService>
  ) {}

  public async update({
    initiativeId,
    surveyId,
    config,
    impactScopes,
    userId,
  }: {
    initiativeId: ObjectId;
    surveyId: ObjectId;
    config?: MaterialityAssessmentConfig;
    impactScopes: UpdateImpactScope[];
    userId: ObjectId;
  }) {
    const assessmentJob = await this.backgroundJobService.findExistingJob({
      initiativeId,
      surveyId,
    });

    if (!assessmentJob || !assessmentJob.tasks?.[0]?.data) {
      throw new UserError('Materiality Assessment results not found', { initiativeId, surveyId });
    }

    assessmentJob.tasks[0].data.config = config;
    assessmentJob.markModified('tasks');
    await assessmentJob.save();

    const survey = await this.surveyRepo.mustFindById(surveyId);

    if (impactScopes.includes(UpdateImpactScope.Reports)) {
      const context = this.pptxService.getMTReportJobContext({
        userId,
        initiativeId: new ObjectId(initiativeId),
        survey,
        assessmentJob,
      });
      await this.pptxService.createJob(context);
    }

    if (impactScopes.includes(UpdateImpactScope.ModuleMetrics)) {
      const metricGroup = await this.metricGroupService.findOrCreateMetricGroup({ userId, survey });
      await this.metricGroupService.processGenerateUtrs({ job: assessmentJob, metricGroup, survey });
    }

    return assessmentJob.tasks[0].data.config;
  }
}

let instance: MaterialityAssessmentConfigService;
export const getMaterialityAssessmentConfigService = () => {
  if (!instance) {
    instance = new MaterialityAssessmentConfigService(
      getMaterialityAssessmentBackgroundJobService(),
      getMTPPTXReportService(),
      SurveyRepository,
      getMaterialityMetricGroupService()
    );
  }
  return instance;
};
