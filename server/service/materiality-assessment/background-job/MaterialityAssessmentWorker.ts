
/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { BackgroundBaseWorker } from '../../background-process/BackgroundBaseWorker';
import { wwgLogger } from '../../wwgLogger';
import { MaterialityAssessmentWorkflow, getMaterialityAssessmentWorkflow } from './MaterialityAssessmentWorkflow';
import { SupportedJobModel } from './types';

let instance: BackgroundBaseWorker<SupportedJobModel, MaterialityAssessmentWorkflow>;
export const getMaterialityAssessmentWorker = () => {
  if (!instance) {
    instance = new BackgroundBaseWorker<SupportedJobModel, MaterialityAssessmentWorkflow>(wwgLogger, getMaterialityAssessmentWorkflow());
  }
  return instance;
};
