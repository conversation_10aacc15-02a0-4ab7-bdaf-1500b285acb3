import { ObjectId } from "bson";
import { BackgroundJobPlain, JobType, Task, TaskType } from "../../../models/backgroundJob";
import { HydratedDocument } from "mongoose";
import { AssessmentData, AssessmentResult } from '../types';
import { AIModelType } from "../../ai/AIModelFactory";
import { MaterialityAssessmentConfig } from '../../../models/materiality';

export type MaterialityAssessmentContext = {
  initiativeId: ObjectId;
  surveyId: ObjectId;
  modelType?: AIModelType;
  result?: AssessmentResult<AssessmentData>;
  config?: MaterialityAssessmentConfig;
  userId: ObjectId;
};

export interface TaskMaterialityAssessmentScores extends Task<MaterialityAssessmentContext> {
  type: TaskType.GenerateMaterialityAssessmentScores;
}

interface TaskMaterialityAssessmentMetricGroup extends Task<MaterialityAssessmentContext> {
  type: TaskType.GenerateMaterialityAssessmentMetricGroup;
}

export type SupportedTask = (TaskMaterialityAssessmentScores | TaskMaterialityAssessmentMetricGroup);
export type SupportedJobPlain = BackgroundJobPlain<SupportedTask[]> & { type: JobType.MaterialityAssessmentScores }
export type SupportedJobModel = HydratedDocument<SupportedJobPlain>;
