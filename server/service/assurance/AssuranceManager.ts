/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import User, { UserModel, UserPlain } from '../../models/user';
import UniversalTrackerValueAssurance from '../../models/universalTrackerValueAssurance';
import UserError from '../../error/UserError';
import { AssuranceAction, AssuranceStatus, PartialAssuranceField, UtrvAssuranceCreate, UtrvAssuranceExtended } from './model/Assurance';
import { createAssuranceFileBundler, UtrvFileBundler } from './UtrvFileBundler';
import { wwgLogger } from '../wwgLogger';
import {
  AssuranceDocument,
  AssuranceDocumentType,
  AssurancePortfolioAction,
  AssurancePortfolioCreate,
  AssurancePortfolioCreateBody,
  AssurancePortfolioExpanded,
  AssurancePortfolioModel,
  AssurancePortfolioPlain,
  AssurancePortfolioStatus,
  AssurancePortfolioUpdateBody,
  History,
} from './model/AssurancePortfolio';
import AssurancePortfolio from '../../models/assurancePortfolio';
import { AssuranceErrorMessages } from '../../error/ErrorMessages';
import { AssuranceRepository } from '../../repository/AssuranceRepository';
import { createGroupUpdate, StakeholderGroupManager } from '../stakeholder/StakeholderGroupManager';
import { OrganizationRepository } from '../../repository/OrganizationRepository';
import { getOnboardingManager, OnboardingManager } from '../onboarding/OnboardingManager';
import { DocumentModel, DocumentPlain } from '../../models/document';
import UniversalTrackerActionManager from '../utr/UniversalTrackerActionManager';
import { OrganizationPartnerTypes } from "../../models/organization"
import config from '../../config';
import { Actions, OptionalAction, validateActionType } from '../action/Actions';
import { StakeholderGroup } from '../../models/public/universalTrackerValueType';
import { loadDocumentDownloadUrls } from '../storage/fileStorage';
import { UniversalTrackerValueRepository } from '../../repository/UniversalTrackerValueRepository';
import { getNotificationManager, NotificationManager, } from '../notification/NotificationManager';
import { AssurancePortfolioPermission } from '../../models/assurancePermission';
import { AssurancePermissions, MAP_ROLES } from './AssurancePortfolioPermissions';
import { OnboardingModelPlain } from '../../models/onboarding';
import UniversalTrackerValue, { UtrvAssuranceStatus } from '../../models/universalTrackerValue';
import { ActionList } from '../utr/constants';
import { InitiativePermissions } from '../initiative/InitiativePermissions';
import { getUniversalTrackerValueManager } from '../utr/UniversalTrackerValueManager';
import { applyAssuranceComplete, applyAssurancePartialComplete, applyAssuranceReject } from "./assuranceStatus";
import ContextError from '../../error/ContextError';

interface PortfolioPermissionsUpdateRequest {
  userId?: string;
  email?: string;
  action: OptionalAction;
  role: AssurancePortfolioPermission;
}

interface DocumentUpdateState {
  removed: AssuranceDocument[],
  remaining: AssuranceDocument[]
}

export class AssuranceManager {

  constructor(
    private bundler: UtrvFileBundler,
    private obManager: OnboardingManager,
    private notificationManager: NotificationManager,
    private logger = wwgLogger,
  ) {
  }

  public async createPortfolio(data: AssurancePortfolioCreateBody, user: UserModel, files?: any): Promise<AssurancePortfolioModel> {

    const org = await OrganizationRepository.findOne({
      _id: data.organizationId,
      partnerTypes: OrganizationPartnerTypes.Assurer
    });

    if (!org) {
      throw new Error(`Assurance organization was not found`);
    }

    const createData: AssurancePortfolioCreate = {
      ...data,
      permissions: [
        { userId: new ObjectId(data.assurancePrimaryContactId), permissions: [AssurancePortfolioPermission.Admin] },
      ],
      organizationId: org._id,
      status: AssurancePortfolioStatus.Created,
      history: [{ action: AssuranceAction.Created, userId: user._id }],
    };

    const assurancePortfolio = new AssurancePortfolio(createData);

    const docs = await this.bundler.uploadPortfolioFiles(assurancePortfolio, user, files as Express.Multer.File[]);
    if (docs.length > 0) {
      const documents = docs.map((d, i) => ({
        type: this.getFileType(files[i]),
        documentId: d._id
      }));

      assurancePortfolio.documents = documents;
      assurancePortfolio.history.push({
        action: AssurancePortfolioAction.UpdateDocuments,
        documents: documents,
        userId: user._id,
      })

    }

    await assurancePortfolio.save();

    this.notificationManager.sendSetupForAssurer(assurancePortfolio, data.assurancePrimaryContactId)
      .catch(e => this.logger.error(e));

    return assurancePortfolio;
  }

  public async update(
    portfolio: AssurancePortfolioModel,
    user: UserPlain,
    data: AssurancePortfolioUpdateBody,
    files: Express.Multer.File[]
  ) {

    if (portfolio.status !== AssurancePortfolioStatus.Created) {
      throw new Error(AssuranceErrorMessages.UpdateNotAllowed);
    }

    const docs = await this.bundler.uploadPortfolioFiles(portfolio, user, files);
    if (docs.length > 0) {

      const validTypes = [AssuranceDocumentType.BasisOfReporting, AssuranceDocumentType.ManagementStatement];
      const newDocuments = docs.map((d, i) => ({
        type: this.getFileType(files[i]),
        documentId: d._id
      })).filter(d => validTypes.includes(d.type));

      const combinedDocuments = portfolio.documents.concat(newDocuments);

      // This will make sure new documents will override older ones based on type
      // as we currently only allow one per type
      const docMap = combinedDocuments.reduce((a, d) => {
        a[d.type] = { type: d.type, documentId: d.documentId };
        return a;
      }, {} as Record<string, AssuranceDocument>)

      this.logger.info('Updating assurance portfolio documents', {
        portfolioId: portfolio._id.toString(),
        userId: user._id.toString(),
        docMap,
      });

      portfolio.documents = Object.values(docMap);

      portfolio.history.push({
        action: AssurancePortfolioAction.UpdateDocuments,
        documents: newDocuments, // Pushing original new documents
        userId: user._id,
      })
    }

    if (ObjectId.isValid(data.assurancePrimaryContactId)) {
      portfolio.permissions = [
        { userId: new ObjectId(data.assurancePrimaryContactId), permissions: [AssurancePortfolioPermission.Admin] },
      ];
      // Update primary contact that is first user
      portfolio.markModified('permissions');
    }

    if (data.organizationId && ObjectId.isValid(data.organizationId)) {
      portfolio.organizationId = new ObjectId(data.organizationId);
    }

    if (data.description) {
      portfolio.description = data.description;
    }

    if (data.portfolioType) {
      portfolio.portfolioType = data.portfolioType;
    }

    return portfolio.save();
  }


  public async updateQuestions(assurancePortfolio: AssurancePortfolioPlain, utrvIds: string[], user: UserModel) {

    const existing = await AssuranceRepository
      .findUniversalTrackerValueAssuranceExtended(assurancePortfolio._id);

    // Filter Removed, it will be re-added  by createForUtrv
    const existingUtrvs = existing
      .filter((({ status }) => status !== AssuranceStatus.Removed))
      .map(utrvAssurance => utrvAssurance.utrvId);

    const { add, remove } = createGroupUpdate<ObjectId, string>(existingUtrvs, utrvIds);

    const utrvAddedPromises = add.map((_id) => {
      return this.createForUtrv(new ObjectId(_id), user, assurancePortfolio);
    });

    const result = await Promise.all(utrvAddedPromises);

    const utrvDisabledPromises = remove.reduce((acc, _id) => {
      const utrvAssurance = existing.find(utrva => String(utrva.utrvId) === String(_id));
      if (!utrvAssurance || [AssuranceStatus.Completed, AssuranceStatus.Removed].includes(utrvAssurance.status)) {
        return acc; // Skip, do not allow to remove completed
      }
      acc.push(this.removeUtrvAssurance(utrvAssurance, user, assurancePortfolio._id))
      return acc;
    }, [] as Promise<UtrvAssuranceExtended>[]);

    const removeResult = await Promise.all(utrvDisabledPromises);
    this.logger.info(`Added ${result.length}, removed ${removeResult.length} questions to ${assurancePortfolio._id} portfolio`);

    return { add: result.length, remove: removeResult.length };
  }

  public async setReadyForAssurer(assurancePortfolio: AssurancePortfolioExpanded) {
    assurancePortfolio.status = AssurancePortfolioStatus.Pending;
    await assurancePortfolio.save();

    this.notificationManager.sendReadyForAssurer(assurancePortfolio)
      .catch(e => this.logger.error(e));

    return assurancePortfolio;
  }

  public async removeDocuments(
    assurancePortfolio: AssurancePortfolioModel,
    user: UserModel,
    documentIds?: string[],
  ) {
    if (assurancePortfolio.status !== AssurancePortfolioStatus.Created) {
      throw new Error(AssuranceErrorMessages.UpdateNotAllowed);
    }

    if (!Array.isArray(documentIds)) {
      throw new UserError('No documents to remove.');
    }

    const update = assurancePortfolio.documents.reduce((a, d) => {
      if (documentIds.includes(String(d.documentId))) {
        a.removed.push(d);
      } else {
        a.remaining.push(d)
      }
      return a
    }, { removed: [], remaining: [] } as DocumentUpdateState);

    assurancePortfolio.documents = update.remaining;

    assurancePortfolio.history.push({
      action: AssurancePortfolioAction.RemoveDocuments,
      documents: update.removed, // Pushing original new documents
      userId: user._id,
    });

    return assurancePortfolio.save();
  }

  public async uploadDocuments(
    assurancePortfolio: AssurancePortfolioPlain,
    user: UserModel,
    files: any) {
    // Upload evidence
    return await this.bundler.uploadPortfolioFiles(assurancePortfolio, user, files);
  }

  public async completeQuestions(
    assurancePortfolio: AssurancePortfolioModel,
    utrvAssuranceIds: string[],
    user: UserModel,
    docs: any[] = []
  ) {

    if (!Array.isArray(utrvAssuranceIds) || utrvAssuranceIds.length === 0) {
      throw new UserError('Please provide at least one question to complete');
    }

    const existing = await AssuranceRepository.findUniversalTrackerValueAssuranceExtended(
      assurancePortfolio._id,
      {
        status: { $ne: AssuranceStatus.Removed }
      }
    );

    const documents = docs.map((d) => ({
      type: AssuranceDocumentType.AssuranceStatement,
      documentId: d._id
    }));

    const filteredAssurances = existing.filter(
      (utrvAssurance) =>
        utrvAssuranceIds.includes(String(utrvAssurance._id)) &&
        utrvAssurance.universalTrackerValue.status === ActionList.Verified
    );

    const completeUpdates = await Promise.all(
      filteredAssurances.map(utrvAssurance => this.completeUtrvAssurance(utrvAssurance, user, documents)
        .then(() => AssuranceStatus.Completed)
      )
    );

    if (completeUpdates.length === 1) {
      await this.notificationManager.sendUtrvAssuredNotification({ utrv: filteredAssurances[0].universalTrackerValue, assurancePortfolio });
    }

    const completedCount = completeUpdates.filter(status => status === AssuranceStatus.Completed).length;
    const removedCount = completeUpdates.length - completedCount;
    this.logger.info(
      `Updated ${completeUpdates.length}, completed: ${completedCount}, removed: ${removedCount}  assurance utrvs`
    );
  }

  public async disputeQuestions(
    assurancePortfolio: AssurancePortfolioModel,
    data: { questions: string[], text: string },
    user: UserModel,
  ) {
    const { questions: utrvAssuranceIds, text } = data;
    const commentIds: ObjectId[] = [];

    if (!Array.isArray(utrvAssuranceIds) || utrvAssuranceIds.length === 0) {
      throw new UserError('Please provide at least one question to dispute');
    }

    const filteredAssurances = await AssuranceRepository.findUniversalTrackerValueAssuranceExtended(
      assurancePortfolio._id,
      {
        _id: { $in: utrvAssuranceIds },
        status: { $ne: AssuranceStatus.Removed },
      }
    );
    const utrvs = await UniversalTrackerValue.find({
      _id: { $in: filteredAssurances.map((assurance) => assurance.utrvId) },
    }).exec();

    const disputeUpdates = await Promise.all(
      filteredAssurances.map(async (utrvAssurance) => {
        const utrv = utrvs.find(utrv => utrvAssurance.utrvId.equals(utrv.id));
        if (utrv) {
          const { commentId } = await getUniversalTrackerValueManager().addUtrvComment(utrv, text, user);
          if (commentId) {
            commentIds.push(commentId);
          }
        }
        return this.disputeUtrvAssurance(utrvAssurance, user);
      })
    );

    if (disputeUpdates.length === 1) {
      const utrv = await UniversalTrackerValue.findById(filteredAssurances[0].utrvId).exec();
      if (utrv) {
        await this.notificationManager.sendUtrvDisputedNotification({ utrv, assurancePortfolio, commentId: commentIds.shift() });
      }
    }

    this.logger.info(`Applying dispute to ${disputeUpdates.length} questions`, {
      utrvAssuranceIds,
      portfolioId: assurancePortfolio._id,
    });
  }

  public async partialAssuranceQuestion(params: {
    assurancePortfolio: AssurancePortfolioModel;
    utrvId: string;
    user: UserPlain;
    partialFields: PartialAssuranceField[];
  }) {
    const { assurancePortfolio, utrvId, partialFields, user } = params;
    const utrvAssurance = (await UniversalTrackerValueAssurance.findOne({
      portfolioId: assurancePortfolio._id,
      status: { $ne: AssuranceStatus.Removed },
      utrvId: new ObjectId(utrvId),
    })
      .populate('universalTrackerValue')
      .orFail(() => new ContextError('Not found any assurance', { portfolio: assurancePortfolio._id, utrvId }))
      .exec()) as UtrvAssuranceExtended;

    // If we already have completed assurance, or completed open, or not verified question then not update
    if (
      utrvAssurance.status === AssuranceStatus.Completed ||
      utrvAssurance.universalTrackerValue.status !== ActionList.Verified
    ) {
      throw new ContextError('This utrv assurance was completed', {
        portfolio: assurancePortfolio._id,
        utrvAssurance: utrvAssurance._id,
        utrvAssuranceStatus: utrvAssurance.status,
        utrv: utrvAssurance.universalTrackerValue._id,
      });
    }

    const partialAssurance = await this.completePartiallyUtrvAssurance({ utrvAssurance, partialFields, user });

    await this.notificationManager.sendUtrvAssuredNotification({
      utrv: partialAssurance.universalTrackerValue,
      assurancePortfolio,
    });
    this.logger.info(`Partial assurance completed for utrv assurance ${utrvAssurance._id}`, {
      portfolioId: assurancePortfolio._id,
      utrvAssuranceId: utrvAssurance._id,
      utrvId: utrvAssurance.universalTrackerValue._id,
    })
  }

  public async completePortfolio(
    assurancePortfolio: AssurancePortfolioExpanded,
    user: UserModel,
    docs: any[]
  ) {

    if (docs.length > 0) {
      const documents = docs.map((d: DocumentModel) => ({
        type: AssuranceDocumentType.AssuranceStatement,
        documentId: d._id
      }));

      assurancePortfolio.documents.push(...documents);
      assurancePortfolio.history.push({
        action: AssurancePortfolioAction.UpdateDocuments,
        documents: documents,
        userId: user._id,
      });
    }

    // Update portfolio
    assurancePortfolio.history.push({
      action: AssurancePortfolioAction.Completed,
      userId: user._id,
    });
    assurancePortfolio.status = AssurancePortfolioStatus.Completed;
    await assurancePortfolio.save();

    this.notificationManager.sendAssuranceCompleted(assurancePortfolio).catch(e => this.logger.error(e));

    return assurancePortfolio;
  }

  private async removeUtrvAssurance(utrvAssurance: UtrvAssuranceExtended, user: UserPlain, portfolioId: ObjectId) {

    if (utrvAssurance.status === AssuranceStatus.Completed) {
      throw new Error(`Cannot remove complete assurance (${utrvAssurance._id})`)
    }

    utrvAssurance.status = AssuranceStatus.Removed;
    utrvAssurance.history.push({
      action: AssuranceAction.RemoveFromPortfolio,
      userId: user._id,
    });

    await UniversalTrackerActionManager
      .removeAssurance(utrvAssurance.universalTrackerValue, portfolioId)
      .then(m => m.save());

    return utrvAssurance.save();
  }

  /**
   * User permissions checks must be done before calling this method
   */
  private async createForUtrv(
    utrvId: ObjectId,
    user: UserModel,
    assurancePortfolio: AssurancePortfolioPlain
  ): Promise<UtrvAssuranceExtended> {
    const existingAssurance = <UtrvAssuranceExtended>(
      await AssuranceRepository.findOneUniversalTrackerValueAssuranceExtended(assurancePortfolio._id, utrvId)
    );
    if (existingAssurance) {
      // Re-add
      if (existingAssurance.status === AssuranceStatus.Removed) {
        existingAssurance.status = AssuranceStatus.Created;
        existingAssurance.history.push({
          action: AssuranceAction.AddedToPortfolio,
          userId: user._id,
        });
      }

      const existingUtrv = existingAssurance.universalTrackerValue;
      if (!existingUtrv.assuranceStatus) {
        existingUtrv.assuranceStatus = UtrvAssuranceStatus.Created;
        await existingUtrv.save();
      }
      return existingAssurance.save();
    }

    const utrv = await UniversalTrackerValueRepository.mustFindById(utrvId);
    const initiativeId = assurancePortfolio ? assurancePortfolio.initiativeId : utrv.initiativeId;
    if (!(await InitiativePermissions.canManageInitiative(user, initiativeId))) {
      throw new Error('user does not have permission to execute this action');
    }

    const utrvHistoryIndex = utrv.history.length;
    const createData: UtrvAssuranceCreate = {
      utrvId,
      utrvHistoryIndex,
      stakeholders: { stakeholder: [user._id], verifier: [], escalation: [] },
      permissions: [],
      status: AssuranceStatus.Created,
      history: [{ action: AssuranceAction.Created, utrvHistoryIndex, userId: user._id }],
    };

    if (assurancePortfolio) {
      createData.portfolioId = assurancePortfolio._id;
    }

    const universalTrackerValueAssurance = new UniversalTrackerValueAssurance(createData);
    universalTrackerValueAssurance.universalTrackerValue = utrv;

    await universalTrackerValueAssurance.save();

    if (!utrv.assuranceStatus) {
      utrv.assuranceStatus = UtrvAssuranceStatus.Created;
      await utrv.save();
    }

    return universalTrackerValueAssurance as UtrvAssuranceExtended;
  }

  private getFileType(file: any) {
    if (!file) {
      throw new Error('Failed to find assurance file');
    }

    switch (file.fieldname) {
      case AssuranceDocumentType.ManagementStatement:
        return AssuranceDocumentType.ManagementStatement;
      case AssuranceDocumentType.AssuranceStatement:
        return AssuranceDocumentType.AssuranceStatement;
      case AssuranceDocumentType.BasisOfReporting:
        return AssuranceDocumentType.BasisOfReporting;
      default:
        throw new Error(`Assurance document type ${file.fieldname} is not supported`);
    }
  }

  private isRemovingLastAdmin({
    action,
    role,
    assurancePortfolio: { permissions },
  }: {
    action: Actions;
    role: AssurancePortfolioPermission;
    assurancePortfolio: Pick<AssurancePortfolioModel, 'permissions'>;
  }) {
    return (
      action === Actions.Remove &&
      role === AssurancePortfolioPermission.Admin &&
      permissions.filter((p) => p.permissions.includes(AssurancePortfolioPermission.Admin)).length <= 1
    );
  }

  public async updatePermissions(
    assurancePortfolio: AssurancePortfolioModel,
    body: PortfolioPermissionsUpdateRequest,
    delegator: UserModel
  ) {
    const { userId, action, email, role } = body;
    const validAction = validateActionType(action);

    if (!Object.values(AssurancePortfolioPermission).includes(role)) {
      throw new UserError('Invalid role');
    }

    if (!userId && !email) {
      throw new UserError('Missing user id and email');
    }

    if (this.isRemovingLastAdmin({ action: validAction, role, assurancePortfolio })) {
      throw new UserError('The last admin cannot be removed');
    }

    if (action === Actions.Remove) {
      // dealing with self removal
      if ((userId && delegator._id.equals(userId)) || (delegator.email === email)) {
        return this.updatePermissionsGroup(assurancePortfolio, validAction, delegator, role);
      }
    }

    if (userId) {
      const user = await User.findById(userId).orFail().exec();
      return this.updatePermissionsGroup(assurancePortfolio, validAction, user, role);
    }

    if (email) {
      const user = await User.findOne({ email }).exec();
      if (user) {
        // User already exist - we can add it straight to the stakeholders
        return this.updatePermissionsGroup(assurancePortfolio, validAction, user, role);
      }

      return this.obManager.manageAssuranceAction(
        validAction,
        email,
        // admin => verifier / user => stakeholder
        MAP_ROLES[role.toUpperCase() as keyof typeof MAP_ROLES] as keyof StakeholderGroup,
        assurancePortfolio,
        delegator
      );
    }
  }

  private async updatePermissionsGroup(
    assurancePortfolio: AssurancePortfolioModel,
    action: Actions,
    user: UserModel,
    role: AssurancePortfolioPermission,
  ) {
    if (action === Actions.Add) {
      this.ensureUserOrganization(user, assurancePortfolio)
    }

    assurancePortfolio.permissions = AssurancePermissions.mergeData(assurancePortfolio.toObject().permissions, {
      data: { userId: user._id, permissions: [role] },
      action,
    });

    // Add user to organization if user is not part of any
    if (!user.organizationId) {
      user.organizationId = assurancePortfolio.organizationId;
      await user.save();
    }

    return assurancePortfolio.save();
  }

  /**
   * User must be part of a single organization
   * in this case it must be the same one as assurance portfolio or not set
   * otherwise updating permissions will still not allow that user to access it
   */
  private ensureUserOrganization(user: UserModel, assurancePortfolio: AssurancePortfolioModel) {
    // No org, we can safely set to the assurance organizationId as user org
    if (!user.organizationId) {
      return true;
    }

    // Otherwise it must be the same organization
    if (assurancePortfolio.organizationId && user.organizationId.equals(assurancePortfolio.organizationId)) {
      return true;
    }

    throw new UserError(`You cannot add a user that is already part of another assurance organisation`);
  }

  public async getAvailablePermissions(portfolio: AssurancePortfolioModel) {
    const [portfolioPermissions] = await AssuranceRepository.getPermissions(portfolio._id);

    const existingOnboardings = await this.obManager.findOnboarding(
      'assuranceStakeholders',
      portfolio._id,
      portfolio.initiativeId
    );
    const mergedOnboardings = existingOnboardings.reduce((accumulator, onboarding) => {
      const key = onboarding.user.email;
      const existedOnboarding = accumulator.get(key);
      if (!existedOnboarding) {
        return accumulator.set(key, onboarding.toObject());
      }
      const result: OnboardingModelPlain = {
        ...existedOnboarding,
        assuranceStakeholders: StakeholderGroupManager.mergeGroup(
          existedOnboarding.assuranceStakeholders ?? StakeholderGroupManager.getEmptyGroup(),
          {
            add: onboarding.assuranceStakeholders ?? StakeholderGroupManager.getEmptyGroup(),
            remove: StakeholderGroupManager.getEmptyGroup(),
          }
        ),
      };
      return accumulator.set(key, result);
    }, new Map<string, OnboardingModelPlain>());

    mergedOnboardings.forEach(({ user, assuranceStakeholders }) => {
      portfolioPermissions.assurers.push({
        email: user.email,
        isAdmin: assuranceStakeholders?.verifier.some((s) => s.equals(portfolio._id)),
        isAssurer: assuranceStakeholders?.stakeholder.some((s) => s.equals(portfolio._id)),
      });
    });

    return portfolioPermissions;
  }

  private async completeUtrvAssurance(
    utrvAssurance: UtrvAssuranceExtended,
    user: UserModel,
    assuranceDocuments: AssuranceDocument[],
  ) {

    const evidence = assuranceDocuments.map(d => d.documentId);
    if (utrvAssurance.status === AssuranceStatus.Completed) {
      if (evidence.length === 0) {
        throw new Error(`Assurance (${utrvAssurance._id}) is already completed `);
      }
    }

    applyAssuranceComplete({
      utrvAssurance,
      utrv: utrvAssurance.universalTrackerValue,
      userId: user._id,
      documents: evidence,
    });
    await utrvAssurance.save();

    const updatedUtrv = await UniversalTrackerActionManager.assure({
      utrv: utrvAssurance.universalTrackerValue,
      utrvAssurance,
      userId: user._id,
      evidence
    });
    await updatedUtrv.save();

    return utrvAssurance;
  }

  public async completePartiallyUtrvAssurance(
    params: {
      utrvAssurance: UtrvAssuranceExtended,
      partialFields: PartialAssuranceField[],
      user: UserPlain,
    }
  ) {
    const { utrvAssurance, user, partialFields } = params;

    applyAssurancePartialComplete({
      utrvAssurance,
      utrv: utrvAssurance.universalTrackerValue,
      userId: user._id,
      partialFields,
    });
    await utrvAssurance.save();

    const updatedUtrv = await UniversalTrackerActionManager.assure({
      utrv: utrvAssurance.universalTrackerValue,
      utrvAssurance,
      userId: user._id,
      assuranceFields: partialFields
    });
    await updatedUtrv.save();

    return utrvAssurance;
  }

  private async disputeUtrvAssurance(
    utrvAssurance: UtrvAssuranceExtended,
    user: UserModel,
  ) {

    const utrv = utrvAssurance.universalTrackerValue;
    applyAssuranceReject({ utrvAssurance, utrv, userId: user._id });
    await utrvAssurance.save();

    const updatedUtrv = await UniversalTrackerActionManager.dispute(
      utrv,
      utrvAssurance,
      user._id,
    );
    await updatedUtrv.save();

    return utrvAssurance;
  }

  public async download(portfolio: AssurancePortfolioModel, user: UserPlain, questionIds: string[] = []) {

    const existing = await AssuranceRepository.findUniversalTrackerValueAssuranceExtended(portfolio._id, {
      status: { $ne: AssuranceStatus.Removed },
      ...(questionIds.length > 0 ? { utrvId: { $in: questionIds.map((id) => new ObjectId(id)) } } : {}),
    });

    if (existing.length === 0) {
      return <any>[];
    }

    const utrvHistorySum = existing.reduce((a, c) => a + c.universalTrackerValue.history.length, 0);
    const bundleHistory = this.getBundleHistory(portfolio);
    // Has anything changed?
    if (this.canUseExistingHistory(utrvHistorySum, bundleHistory)) {
      return this.getBundleDocuments(bundleHistory);
    }

    portfolio.history.push({ action: AssurancePortfolioAction.BundleStart, userId: user._id });
    await portfolio.save();

    try {
      const bundleDocument = await this.bundler.createPortfolioAssuranceBundle(portfolio, existing, user);

      portfolio.history.push({
        action: AssurancePortfolioAction.BundleComplete,
        documents: [{ type: AssuranceDocumentType.EvidenceBundle, documentId: bundleDocument._id }],
        userId: user._id,
        utrvHistorySum: utrvHistorySum,
      });
    } catch (e) {
      wwgLogger.error(e);
      portfolio.history.push({ action: AssurancePortfolioAction.BundleFailed, userId: user._id });
    }

    await portfolio.save();
    return this.getBundleDocuments(this.getBundleHistory(portfolio));
  }

  private canUseExistingHistory(utrvHistorySum: number, bundleHistory?: History) {
    if (!bundleHistory || bundleHistory.utrvHistorySum !== utrvHistorySum) {
      return false;
    }
    return bundleHistory.created && bundleHistory.created > config.cache.bundleZipDate;
  }

  public async deletePortfolio(portfolio: AssurancePortfolioModel, user: UserPlain) {

    if (portfolio.status === AssurancePortfolioStatus.Deleted) {
      throw new Error('Assurance Portfolio is already deleted')
    }

    portfolio.history.push({
      action: AssurancePortfolioAction.DeletePortfolio,
      userId: user._id,
    });

    portfolio.status = AssurancePortfolioStatus.Deleted;

    const existing = await AssuranceRepository.findUniversalTrackerValueAssuranceExtended(
      portfolio._id,
      { status: { $ne: AssuranceStatus.Removed } }
    );

    const completeUpdates = existing.map((utrvAssurance => {
      utrvAssurance.status = AssuranceStatus.Disabled;
      return utrvAssurance.save()
        .then(() => UniversalTrackerActionManager.removeAssurance(
          utrvAssurance.universalTrackerValue,
          portfolio._id
        ))
        .then(m => m.save())
        .then(() => AssuranceStatus.Removed)
    }));
    await Promise.all(completeUpdates);

    return portfolio.save();
  }

  private async getBundleDocuments(bundleHistory?: History): Promise<DocumentPlain[]> {
    if (!bundleHistory?.documents) {
      return [];
    }

    const ids = bundleHistory.documents
      .filter(d => d.type === AssuranceDocumentType.EvidenceBundle)
      .map(d => d.documentId);

    return loadDocumentDownloadUrls(ids)
  }

  private getBundleHistory({ history }: AssurancePortfolioModel) {
    let bundleHistory;
    for (const h of history) {
      if (h.action == AssurancePortfolioAction.BundleComplete) {
        bundleHistory = h
      }
    }
    return bundleHistory;
  }
}

export const createAssuranceManager = () => {
  return new AssuranceManager(
    createAssuranceFileBundler(),
    getOnboardingManager(),
    getNotificationManager(),
    wwgLogger,
  );
};

let instance: AssuranceManager;
export const getAssuranceManager = () => {
  if (!instance) {
    instance = createAssuranceManager();
  }
  return instance;
}
