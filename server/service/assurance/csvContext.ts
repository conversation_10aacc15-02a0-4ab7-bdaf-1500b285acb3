/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { BundleContext } from './UtrvFileBundler';
import { customDateFormat, DateFormat } from '../../util/date';
import sanitize from 'sanitize-filename';
import { getFilename } from '../file/file';
import { DocumentType } from '../../models/document';
import { UniversalTrackerPlain } from '../../models/universalTracker';
import { ObjectId } from 'bson';
import { AssuranceDocumentType } from "./model/AssurancePortfolio"
import { getStandardName } from '../utr/standards';
import { createFrameworkColumns } from './csvFramework';
import {
  Alternative,
  ColumnType,
  TableColumn,
  UtrValueType,
  ValueValidation,
} from "../../models/public/universalTrackerType";
import { RowData, ValueData } from "../../models/public/universalTrackerValueType";
import { Option, ValueList } from '../../models/public/valueList';
import { MetricUnitManager, UnitTypes } from '../units/MetricUnitManager';
import config from '../../config';
import { getAssuranceText, NotApplicableTypes } from '../../models/universalTrackerValue';
import { isUtrvAssuranceComplete } from './assuranceStatus';
import { SupportedMeasureUnits, UnitConfig } from '../units/unitTypes';
import {
  getNumberScaleCode,
  getTableNumberScaleCode,
  getTableUnitCode,
  getUnitCode,
  getUtrvDataProp,
  getUtrvTableProp,
  getUtrvValue
} from '../utr/utrvUtil';
import { getTagTextByUtrId } from '../../util/metric-group';
import { getUtrvStatus } from '../custom-report/utils';
import { DownloadDisplayOptions } from '../survey/scope/downloadScope';
import { PACK, QUESTION, SURVEY } from '../../util/terminology';

// @TODO: Remove and replace this with simple report data resolver

type propertyAccessor = (bundle: BundleContext) => any;
export type CsvPropertyAccessor = string | propertyAccessor;

export interface CsvColumnSetup {
  name: string;
  property: CsvPropertyAccessor;
}


// | Contributor Comment | Contributor Evidence
// | Contributor | Contributor Timestamp | Verifier Comment | Verifier Evidence
// | Verifier | Verifier Timestamp | Status |
/** @todo: [EvidenceData] this needs to switch to use the new property evidenceData */
const createHistoryColumns = (type: 'contributor' | 'verifier') => {
  const name = type === 'contributor' ? 'Contributor' : 'Verifier';
  const historyProp = type === 'contributor' ? 'updateHistory' : 'verifyHistory';
  return [
    // {
    //   name: name,
    //   property: (bundleContext: BundleContext) => {
    //     if (!bundleContext[historyProp]) {
    //       return '';
    //     }
    //     const userId = String(bundleContext[historyProp].userId);
    //     const user = bundleContext.context.users.find((u) => userId === String(u._id));
    //     if (!user) {
    //       return userId;
    //     }
    //
    //     const nameParts = ['firstName', 'surname'].map((p: keyof  UserPlain) => user[p]).filter(Boolean);
    //     // nameParts.push(nameParts.length > 0 ? `<${user.email}>` : user.email);
    //
    //     return nameParts.join(' ');
    //   },
    // },
    {
      name: name + ' Timestamp',
      property: (bundleContext: BundleContext) => {
        const bundleProp = bundleContext[historyProp];
        if (!bundleProp || !bundleProp.date) {
          return '';
        }
        return customDateFormat(bundleProp.date, DateFormat.SortableSlash);
      },
    },
    {
      name: name + ' Evidence',
      property: (bundleContext: BundleContext) => {
        const evidenceProp = bundleContext[historyProp];
        if (!evidenceProp || !evidenceProp.evidence) {
          return '';
        }

        const ids = evidenceProp.evidence.map(String);
        return bundleContext.files?.filter((f) => ids.includes((String(f._id))))
          .map((f) => f.type === DocumentType.Link ? f.path : getFilename(f))
          .join(',') ?? '';
      },
    },
    {
      name: name + ' Notes',
      property: (bundleContext: BundleContext) => {
        return bundleContext[historyProp]?.note ?? '';
      },
    },
  ];
};

const createTagColumn = (displayTag: boolean = false) => {
  if (!displayTag) {
    return [];
  }
  return [
    {
      name: 'Tags',
      property: ({ context, utrTagMap }: BundleContext) => {
        const {
          universalTracker: { _id },
          universalTrackerId,
        } = context;
        const utrId = String(_id || universalTrackerId);
        return getTagTextByUtrId(utrTagMap, utrId);
      },
    },
  ];
}

const getDuplicateText = (utr: UniversalTrackerPlain, type: string) => {
  if (utr.type === type) {
    return utr.valueLabel;
  }
  return utr.alternatives?.[type]?.valueLabel ?? '';
}

export const getPreferredAltName = (utr: Pick<UniversalTrackerPlain, 'alternatives' | 'type'> & Alternative, types: string[]) => {
  if (types.length > 0) {
    return getPreferredAlternative(utr, types).name ?? utr.name
  }
  return utr.name
}

export const getPreferredTypeCode = (utr: Pick<UniversalTrackerPlain, 'alternatives' | 'type'> & Alternative, types: string[]) => {
  if (types.length > 0) {
    return getPreferredAlternative(utr, types).typeCode ?? utr.typeCode
  }
  return utr.typeCode
}

export const getPreferredValueLabel = (utr: Pick<UniversalTrackerPlain, 'alternatives' | 'type'> & Alternative, types: string[]) => {
  if (types.length > 0) {
    return getPreferredAlternative(utr, types).valueLabel ?? utr.valueLabel
  }
  return utr.valueLabel
}

export const getPreferredAlternative = (utr: Pick<UniversalTrackerPlain, 'alternatives' | 'type'> & Alternative, types: string[]): Alternative & { type: string } => {

  if (!utr.alternatives) {
    return utr;
  }

  for (const type of types) {

    // Check if its the root one,
    // in case there is alternative for root type as well, root takes priority
    if (type === utr.type) {
      return utr;
    }

    if (utr.alternatives[type]) {
      return { ...utr.alternatives[type], type };
    }
  }

  return utr;
}

interface MappingItem {
  name: string,
  type: string,
}

const getAltMapping = ({ name, type }: MappingItem) => ({
  name,
  property: (bundleContext: BundleContext) => getDuplicateText(
    bundleContext.context.universalTracker,
    type,
  ),
});

const evidenceColumnName = 'Provenance & Evidence';

const mappingItems: MappingItem[] = [
  { name: 'CDP Water', type: 'cdp_water' },
  { name: 'CDP Forest', type: 'cdp_forest' },
  { name: 'CDP Climate', type: 'cdp_climate' },
  { name: 'Refinitiv', type: 'refinitiv' },
  { name: 'TruCost', type: 'trucost' },
  { name: 'B Labs', type: 'blabs' },
  { name: 'Future Fit', type: 'futurefit' },
  { name: 'IRIS+', type: 'iris' },
  { name: 'SAM', type: 'sam_csa' },
  { name: 'Vigeo Eiris', type: 'vigeo_eiris' },
  { name: 'World Benchmarking Alliance', type: 'wba' },
];

// SDG Task Mapping:       SDG 7.2, SDG 7.3, SDG 8.4, SDG 12.2, SDG 13.1
// CDP Mapping:            C8.2a
// Refinitiv Mapping:
// TruCost Mapping:
// B Labs Mapping:
// Future-fit Mapping:     BE01 – Energy is from renewable sources
function createMappingColumns() {
  return [
    {
      name: 'Sustainable Development Goal Targets (SDGs)',
      property: (bundleContext: BundleContext) => {
        const { code } = bundleContext.context.universalTracker;

        const contribution = bundleContext.contributions[code] ?? [];
        return contribution
          ?.filter((c) => c.startsWith('sdg/') && c.includes('.'))
          .map((c) => c.replace('sdg/', ''))
          .sort((a, b) => Number(a) - Number(b))
          .map(n => `SDG ${n}`)
          .join(', ')
      },
    },
    ...mappingItems.map(getAltMapping),
  ];
}

function getAssuranceDocumentName(bundleContext: BundleContext, type: AssuranceDocumentType) {
  if (!isUtrvAssuranceComplete(bundleContext.context.assuranceStatus)) {
    return '';
  }
  const doc = bundleContext.assuranceEvidence.find((d) => d.type === type)
  return doc?.document.metadata?.name ?? ''
}

export const assuranceFileHeaders = [
  'Assurance Basis of Reporting',
  'Assurance Management Statement',
  'Assurance Statement',
] as const;

const [basis, management, statement] = assuranceFileHeaders;

function createAssuranceColumns() {
  return [
    {
      name: 'Assurance Status',
      property: ({ context }: BundleContext) => getAssuranceText(context.assuranceStatus),
    },
    {
      name: basis,
      property: (bundleContext: BundleContext) => getAssuranceDocumentName(
        bundleContext,
        AssuranceDocumentType.BasisOfReporting,
      ),
    },
    {
      name: management,
      property: (bundleContext: BundleContext) => getAssuranceDocumentName(
        bundleContext,
        AssuranceDocumentType.ManagementStatement,
      ),
    },
    {
      name: statement,
      property: (bundleContext: BundleContext) => getAssuranceDocumentName(
        bundleContext,
        AssuranceDocumentType.AssuranceStatement
      ),
    },
  ];
}

const getTableColumnValue = (
  colMeta: TableColumn,
  valueLists: ValueList[],
  col: RowData,
  unitConfig: UnitConfig | undefined,
  displayUserInput?: boolean
) => {
  const value = col.value;
  if (value === undefined || value === '') {
    return ''; // Skip early, nothing we can do
  }

  if (colMeta.listId) {
    const id = String(colMeta.listId);
    const valueList = valueLists.find(v => String(v._id) === id);
    if (valueList) {
      const option = valueList.options.find(o => o.code === col.value);
      if (option) {
        return option.name;
      }
    }
  }

  if (ColumnType.Number === colMeta.type && colMeta.unitType === SupportedMeasureUnits.currency) {
    const numberScale = getTableNumberScaleCode(colMeta, col, { displayUserInput });
    return MetricUnitManager.withMetrics({
      value,
      unit: unitConfig?.currency || colMeta.unit,
      numberScale,
      unitType: colMeta.unitType,
    })
  }

  return MetricUnitManager.withMetrics({
    value,
    unit: getTableUnitCode(colMeta, col, { displayUserInput }),
    unitType: colMeta.unitType,
  })
};

const convertTableData = (
  valueData: ValueData,
  universalTracker: UniversalTrackerPlain,
  valueLists: ValueList[],
  tableDataIndex: number,
  surveyUnitConfig: UnitConfig | undefined,
  displayUserInput: boolean,
) => {

  const table = getUtrvTableProp({ valueData }, displayUserInput);
  const row = table?.[tableDataIndex];
  if (!row) {
    return '';
  }

  const options = universalTracker.valueValidation?.table?.columns ?? [];

  return row
    .filter(col => col.value !== undefined)
    .map((col) => {
      const colMeta = options.find(o => o.code === col.code);

      if (!colMeta) {
        return `${col.code}: ${col.value ?? ''}`;
      }

      const value = getTableColumnValue(colMeta, valueLists, col, surveyUnitConfig, displayUserInput) ?? '';
      return `${colMeta.name}: ${value}`;
    }).join(', ');
};

// Question | Value Chain | Value | Unit
export const getCsvHeaders = (props: DownloadDisplayOptions = {}): CsvColumnSetup[] => {
  const { displayUserInput = false, displayTag = false } = props;
  const displayOptions = { displayUserInput };
  return [
    {
      name: 'Data Source',
      property: () => 'G17Eco',
    },
    {
      name: `${SURVEY.CAPITALIZED_ADJECTIVE} Level`,
      property: ({ context }: BundleContext) => {
        return context.initiative ? context.initiative.name : '';
      },
    },
    {
      name: `${SURVEY.CAPITALIZED_ADJECTIVE} Date`,
      property: ({ context }: BundleContext) => {
        const { survey, effectiveDate } = context;
        const date = survey ? survey.effectiveDate : effectiveDate;
        return customDateFormat(date, DateFormat.YearMonth);
      },
    },
    {
      name: 'Id',
      property: ({ context }: BundleContext) => {
        return context._id.toString();
      },
    },
    {
      name: 'QuestionId',
      property: ({ context }: BundleContext) => {
        return context.universalTracker._id.toString();
      },
    },
    {
      name: `${QUESTION.CAPITALIZED_SINGULAR} Type`,
      property: ({ context }: BundleContext) => {
        return context.universalTracker.valueType;
      },
    },
    {
      name: 'Code',
      property: ({ context }: BundleContext) => {
        return context.universalTracker.code;
      },
    },
    {
      name: 'Comment',
      property: ({ context }: BundleContext) => {
        return context.note;
      },
    },
    {
      name: `Reporting ${PACK.CAPITALIZED_SINGULAR}`,
      property: ({ context, preferredTypes = [] }: BundleContext) => {
        const { type } = getPreferredAlternative(context.universalTracker, preferredTypes);
        return getStandardName(type);
      },
    },
    {
      name: QUESTION.CAPITALIZED_SINGULAR,
      property: ({ context, preferredTypes = [], tableDataIndex = 0 }: BundleContext) => {
        const { valueType } = context.universalTracker;
        const { valueLabel, type, typeCode } = getPreferredAlternative(context.universalTracker, preferredTypes);

        const suffix = valueType === UtrValueType.Table ? `#${tableDataIndex + 1}` : '';

        if (type === 'gri') {
          return `${String(type).toUpperCase()} ${typeCode ?? ''}: ${valueLabel ?? ''} ${suffix}`;
        }

        return `${valueLabel} ${suffix}`;
      },
    },
    {
      name: 'Unit',
      property: ({ context }: BundleContext) => {
        const {
          value,
          universalTracker: { unit, metricUnit, unitType, valueType },
          survey,
        } = context;
        if (valueType === UtrValueType.Table) {
          return ''; // Table display inline units
        }

        if (unitType === UnitTypes.currency) {
          return survey?.unitConfig.currency || unit || '';
        }

        const unitValue = getUnitCode(context, context.universalTracker, displayOptions);
        if (unitValue) {
          return MetricUnitManager.getUnitDesc(unitValue, value, unitValue);
        }

        if (metricUnit) {
          return `${metricUnit.prefix || ''} ${metricUnit.suffix || ''}`.trim();
        }
        return '';
      },
    },
    {
      name: 'Number Scale',
      property: ({ context }: BundleContext) => {
        const {
          universalTracker: { unitType, valueType },
        } = context;
        // Not strictly true, but correct as we only have it for currency, and skip table
        if (unitType !== SupportedMeasureUnits.currency || valueType === UtrValueType.Table) {
          return '';
        }
        return getNumberScaleCode(context, context.universalTracker, displayOptions);
      },
    },
    {
      name: 'Input',
      property: ({ context, tableDataIndex, valueLists }: BundleContext) => {
        const { universalTracker, value, valueData, survey } = context;
        // @TODO This check is a bit messed up but works I guess because table is always defined
        if (!valueData || (!valueData.data && !valueData.table)) {
          return value ?? '';
        }

        if (valueData.notApplicableType === NotApplicableTypes.NA) {
          return 'N/A';
        }
        if (valueData.notApplicableType === NotApplicableTypes.NR) {
          return 'N/R';
        }

        if ([UtrValueType.Number, UtrValueType.Percentage].includes(universalTracker.valueType as UtrValueType)) {
          return getUtrvValue(context, displayUserInput).value ?? '';
        }

        if (value === undefined) {
          if (universalTracker.valueType === UtrValueType.Table) {
            return convertTableData(
              valueData,
              universalTracker,
              valueLists,
              tableDataIndex as number,
              survey?.unitConfig,
              displayUserInput
            );
          }

          return convertValueData(valueData, universalTracker, valueLists, displayUserInput);
        }
        const totalValue = getUtrvValue({ value, valueData }, displayUserInput).value;
        return `Total: ${totalValue}, ${convertValueData(valueData, universalTracker, valueLists, displayUserInput)}`;
      },
    },
    ...createTagColumn(displayTag),
    {
      name: evidenceColumnName,
      property: ({ context }: BundleContext) => {
        const { _id, universalTrackerId } = context;
        return `${config.email.publicHostname}/universal-tracker-provenance/${universalTrackerId}/${_id}`;
      },
    },
    ...createHistoryColumns('contributor'),
    ...createHistoryColumns('verifier'),
    {
      name: 'Status',
      property: ({ context }: BundleContext) => {
        return getUtrvStatus({ status: context.status });
      },
    },
    ...createAssuranceColumns(),
    ...createMappingColumns(),
    ...createFrameworkColumns(),
  ];
}


const multiSurveyFields = [
  'Data Source',
  'Id',
  `${SURVEY.CAPITALIZED_ADJECTIVE} Date`,
  `${QUESTION.CAPITALIZED_SINGULAR} Type`,
  QUESTION.CAPITALIZED_SINGULAR,
  'Value',
  'Unit',
  'Status',
  evidenceColumnName,
  `Reporting ${PACK.CAPITALIZED_SINGULAR}`,
  ...createMappingColumns().map(c => c.name),
  ...createFrameworkColumns().map(c => c.name),
];
export const multiSurveyColumns = getCsvHeaders().filter((h) => multiSurveyFields.includes(h.name))

const defaultFields = [
  'Data Source',
  'Reporting Level',
  `${SURVEY.CAPITALIZED_ADJECTIVE} Date`,
  'Id',
  `${QUESTION.CAPITALIZED_SINGULAR} Type`,
  `Reporting ${PACK.CAPITALIZED_SINGULAR}`,
  'QuestionId',
  QUESTION.CAPITALIZED_SINGULAR,
  'Value Chain',
  'Input',
  'Tags',
  'Unit',
  'Number Scale',
  ...createHistoryColumns('contributor').map(c => c.name),
  ...createHistoryColumns('verifier').map(c => c.name),
  'Status',
  evidenceColumnName,
  ...createAssuranceColumns().map(c => c.name),
  ...createMappingColumns().map(c => c.name),
  ...createFrameworkColumns().map(c => c.name),
];

export const defaultColumns = (props: DownloadDisplayOptions = {}) => {
  const { displayUserInput, displayTag } = props;
  return getCsvHeaders({ displayUserInput, displayTag }).filter((h) => defaultFields.includes(h.name));
};

export const defaultProperties = (props: DownloadDisplayOptions = {}) => {
  const { displayUserInput, displayTag } = props;
  return defaultColumns({ displayUserInput, displayTag }).map((h) => h.property);
};

export const defaultHeaders = (props: DownloadDisplayOptions = {}) => {
  const { displayUserInput, displayTag } = props;
  return defaultColumns({ displayUserInput, displayTag }).map((h) => h.name);
};

export const surveyDownloadColumns = defaultColumns().filter((h) => {
  const excludedColumns = [
    'Data Source',
    'Id',
    'QuestionId',
    ...createMappingColumns().map(c => c.name),
    ...createFrameworkColumns().map(c => c.name),
  ]
  return !['Reporting Level', ...excludedColumns, ...assuranceFileHeaders].includes(h.name);
})

function getKey(obj: any, key: string) {
  return key.split('.').reduce((a, b) => a && a[b], obj);
}

const JOIN_STRING = ', ' as const;
const getOptions = (valueLists: ValueList[], valueValidation?: ValueValidation): Option[] | undefined => {
  if (!valueValidation?.valueList) {
    return;
  }

  const { listId, list } = valueValidation.valueList;
  if (list) {
    return list;
  }

  if (listId) {
    const id = String(listId);
    return valueLists.find(v => String(v._id) === id)?.options;
  }
  return;
};

const convertValueData = (
  valueData: ValueData,
  { valueType, valueValidation }: UniversalTrackerPlain,
  valueLists: ValueList[],
  displayUserInput: boolean,
) => {
  let { data } = getUtrvDataProp({ valueData }, displayUserInput) ?? {};
  if (typeof data === 'string') {
    if (valueType !== UtrValueType.ValueList) {
      return data;
    }
    // Convert to be handled by ValueListMulti
    data = [data];
  }

  const options = getOptions(valueLists, valueValidation);
  if (Array.isArray(data)) {
    const output = !options ? data : data.map(key => options.find(o => o.code === key)?.name ?? key);
    return output.join(JOIN_STRING);
  }

  if (typeof data !== 'object') {
    return '';
  }

  return Object.entries(data).map(([key, value]) => {
    if (!options) {
      return `${key}: ${value}`;
    }

    const option = options.find(o => o.code === key);
    return option ? `${option.name}: ${value}` : `${key}: ${value}`;
  }).join(JOIN_STRING);
};

/*
CSV Columns: Question | Value Chain | Value | Unit | Contributor Comment
| Contributor Evidence | Contributor | Contributor Timestamp
| Verifier Comment | Verifier Evidence | Verifier | Verifier Timestamp | Status |

Question:               GRI 302-1 a) Fuel Consumption Non-Renewable Sources
Value Chain:            Composite | Product & Services | Operations | External CSR
Value:                  100000.00 | “Total: 9556093656, Electricity: 9555037200.00, Heating: n/a, Cooling: n/a, Steam: 1056456.00”
Unit:                   CO2 Tons etc.
Contributor Comment:    “Lorem ipsum dolor sit amet, ....”
Contributor Evidence:   “myEvidence.pdf, http://www.bob.com/myreport/, secretFile”
Contributor:            NameOne SurName <<EMAIL>>
Contributor Timestamp:  08/10/2019 11:36:58
Verifier Comment:       “....em. ....”
Verifier Evidence:
Verifier:               NameOne SurName <<EMAIL>>
Verifier Timestamp:     08/10/2019 11:36:58
Status:                 Verified
SDG Task Mapping:       SDG 7.2, SDG 7.3, SDG 8.4, SDG 12.2, SDG 13.1
CDP Mapping:            C8.2a
Refinitiv Mapping:
TruCost Mapping:
B Labs Mapping:
Future-fit Mapping:     BE01 – Energy is from renewable sources
CDP Mapping:            C8.2a
==================================================
*/
export const convertRecord = (
  bundleContext: BundleContext,
  properties = defaultProperties({
    displayUserInput: bundleContext.displayUserInput,
    displayTag: bundleContext.displayTag,
  })
): any[] => {
  const record: any[] = [];
  const { context: utrv } = bundleContext;

  properties.forEach((accessor: CsvPropertyAccessor) => {

    if (typeof accessor === 'function') {
      return record.push(accessor(bundleContext))
    }

    record.push(getKey(utrv, accessor) || '');
  });

  return record;
};

interface CsvName {
  _id: ObjectId | string,
  initiative?: { name: string },
  survey?: { name?: string, effectiveDate: string | Date },
  type?: string,
}

export const getCsvName = ({ initiative, survey, _id, type }: CsvName) => {

  const nameParts = [];

  // Add if it's available, and it's not empty ""
  if (survey?.name) {
    nameParts.push(survey.name);
  } else if (initiative) {
    nameParts.push(initiative.name);
  }

  if (survey) {
    nameParts.push(
      customDateFormat(survey.effectiveDate, DateFormat.YearMonth)
    );
  }

  if (type) {
    nameParts.push(type)
  }

  // Fallback to id
  if (nameParts.length === 0) {
    nameParts.push('(' + String(_id) + ')');
  }

  return sanitize(nameParts.join(' '));
};
