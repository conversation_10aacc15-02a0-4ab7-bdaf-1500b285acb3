/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import TrackerEvidence from '../evidence/TrackerEvidence';
import EvidenceUploader, { createEvidenceUploader } from '../evidence/EvidenceUploader';

export class AssuranceEvidence extends TrackerEvidence {

  constructor(uploader: EvidenceUploader) {
    super(uploader, 'assurance');
  }

  public async upload(ownerId: string, userId: string, files: any) {
    return super.processUpload({ ownerId, userId, files });
  }
}

export const createAssuranceEvidence = () => new AssuranceEvidence(createEvidenceUploader());
