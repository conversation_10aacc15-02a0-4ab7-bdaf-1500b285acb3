import { AssurancePermissionType, OrganizationPermission } from '../../models/assurancePermission';
import { wwgLogger } from '../wwgLogger';
import Organization, { OrganizationCreate, OrganizationPermissionsMin } from '../../models/organization';
import User, { User<PERSON>odel, User<PERSON>lain } from '../../models/user';
import { ObjectId } from 'bson';
import { UserRepository } from '../../repository/UserRepository';
import UserError from '../../error/UserError';
import ContextError from '../../error/ContextError';
import { mergePermissions } from './utils';
import {
  getOrganizationOnboardingManager,
  OrganizationOnboardingManager,
} from '../onboarding/OrganizationOnboardingManager';

export class OrganizationManager {
  constructor(private logger = wwgLogger, private organizationObManager: OrganizationOnboardingManager) {}

  public getUserPermissions({
    user,
    permissions,
    organizationId,
  }: {
    user: Pick<UserPlain, '_id' | 'organizationId'>;
    permissions: OrganizationPermission[];
    organizationId: ObjectId;
  }): AssurancePermissionType<OrganizationPermission> | undefined {
    if (user.organizationId && !user.organizationId.equals(organizationId)) {
      this.logger.info('Conflicted organization assignment. User has already linked to an existing organization', {
        assigningOrganizationId: organizationId,
        userOrganizationId: user.organizationId,
      });
      return;
    }
    return { userId: user._id, permissions };
  }

  private async calculateUpcomingChanges({
    emails,
    permissions,
    organizationId,
    delegator,
  }: {
    emails: string[];
    permissions: OrganizationPermission[];
    organizationId: ObjectId;
    delegator: Pick<UserModel, '_id'>;
  }) {
    const users = await UserRepository.findManyByEmail(emails);
    const userMap = new Map(users.map((user) => [user.email, user]));
    const upcomingPermissions = [];
    const usersWithoutOrganizationIds = [];

    for (const email of emails) {
      const user = userMap.get(email);
      if (!user) {
        await this.organizationObManager.onboardEmail({ email, organization: { _id: organizationId }, delegator, permissions });
        continue;
      }
      const userPermissions = this.getUserPermissions({ user, permissions, organizationId });
      if (userPermissions) {
        upcomingPermissions.push(userPermissions);
        if (!user.organizationId) {
          usersWithoutOrganizationIds.push(user._id);
        }
      }
    }

    return { upcomingPermissions, usersWithoutOrganizationIds };
  }

  public async onboardEmails({
    organization,
    emails,
    permissions,
    delegator,
  }: {
    organization: OrganizationPermissionsMin;
    emails: string[];
    permissions: OrganizationPermission[];
    delegator: Pick<UserModel, '_id'>;
  }) {
    const currentPermissions = organization.permissions ?? [];
    const { upcomingPermissions, usersWithoutOrganizationIds } = await this.calculateUpcomingChanges({
      emails,
      permissions,
      organizationId: organization._id,
      delegator
    });

    // Add user to organization if user is not part of any
    await User.updateMany({ _id: { $in: usersWithoutOrganizationIds } }, { organizationId: organization._id });

    const mergedPermissions = mergePermissions({ currentPermissions, upcomingPermissions });

    return Organization.findByIdAndUpdate(
      organization._id,
      {
        permissions: mergedPermissions,
        // Return object after the update with "new" setting
      },
      { new: true }
    )
      .lean()
      .exec();
  }

  public isRemovingLastAdmin({
    userId,
    overridePermissions,
    organizationPermissions,
  }: {
    userId: ObjectId | string;
    overridePermissions: OrganizationPermission[];
    organizationPermissions: AssurancePermissionType<OrganizationPermission>[];
  }) {
    if (overridePermissions.includes(OrganizationPermission.Admin)) {
      return false;
    }

    const adminList = organizationPermissions.filter((p) => p.permissions.includes(OrganizationPermission.Admin));

    // Having no admin (legacy) or >= 2 admins will not be treated as last admin
    if (adminList.length !== 1) {
      return false;
    }

    return adminList[0].userId.equals(userId);
  }

  public async updateOrganizationPermissions({
    organization,
    user,
    overridePermissions,
  }: {
    organization: OrganizationPermissionsMin;
    user: UserModel;
    overridePermissions: OrganizationPermission[];
  }) {
    let organizationPermissions = organization.permissions || [];
    if (
      this.isRemovingLastAdmin({
        userId: user._id,
        organizationPermissions,
        overridePermissions,
      })
    ) {
      throw new UserError('The last admin cannot be removed', {
        organizationId: organization._id.toString(),
        userId: user._id.toString(),
      });
    }
    const userPermissions = organizationPermissions.find((p) => p.userId.equals(user._id));

    if (!userPermissions) {
      // User with organizationId but no permissions in organization must be legacy user
      organizationPermissions.push({ userId: user._id, permissions: overridePermissions });
    } else {
      organizationPermissions = organizationPermissions.map((p) => {
        if (p.userId.equals(user._id)) {
          p.permissions = overridePermissions;
        }
        return p;
      });
    }

    await Organization.updateOne({ _id: organization._id }, { permissions: organizationPermissions }).orFail().exec();

    this.logger.info('Updated user permissions for organization', {
      organizationId: organization._id.toString(),
      userId: user._id.toString(),
      overridePermissions,
    });

    return organizationPermissions;
  }

  /**
   * Create Assurance Tracker is available for staff only
   */
  public async create(createData: OrganizationCreate) {
    const createUserIds = (createData.permissions ?? []).map((p) => p.userId);
    const users = await User.find({ _id: { $in: createUserIds }, organizationId: { $exists: false } }).exec();
    const validUserIds = users.map(({ _id }) => _id);

    const validPermissions = createData.permissions?.filter((p) => validUserIds.some((id) => id.equals(p.userId)));
    const created = await Organization.create({ ...createData, permissions: validPermissions });

    // update organizationId of admin users
    for (const user of users) {
      user.organizationId = created._id;
      await user.save();
    }

    // exist invalid ids
    if (validUserIds.length < createUserIds.length) {
      const errorContext = {
        organizationId: created._id.toString(),
        inValidUsers: createUserIds.reduce((acc, userId) => {
          if (!validUserIds.some((id) => id.equals(userId))) {
            acc.push(userId.toString());
          }
          return acc;
        }, [] as string[]),
      };
      this.logger.error(new ContextError('Create assurance tracker process includes invalid users', errorContext));
    }

    this.logger.info('An assurance tracker is created', {
      organizationId: created._id.toString(),
      users: users.map(({ _id }) => _id.toString()),
    });

    return created;
  }
}

let instance: OrganizationManager;
export const getOrganizationManager = () => {
  if (!instance) {
    instance = new OrganizationManager(wwgLogger, getOrganizationOnboardingManager());
  }
  return instance;
};
