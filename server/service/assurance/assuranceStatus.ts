/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import {
  AssuranceAction,
  AssuranceStatus,
  inactiveUtrvStatuses,
  PartialAssuranceField,
  UniversalTrackerValueAssuranceModel
} from './model/Assurance';
import { UniversalTrackerValuePlain, UtrvAssuranceStatus } from '../../models/universalTrackerValue';
import { ObjectId } from "bson";


export const isUtrvAssuranceComplete = (status: undefined | UtrvAssuranceStatus) => {
  if (!status) {
    return false;
  }

  return [
    UtrvAssuranceStatus.Completed,
    UtrvAssuranceStatus.CompletedOpen,
  ].includes(status)

}

export const isAssuredOrPartiallyAssured = (status: undefined | UtrvAssuranceStatus) => {
  if (!status) {
    return false;
  }

  return [UtrvAssuranceStatus.Completed, UtrvAssuranceStatus.Partial].includes(status);
};

export const isCompletedOpenOrRestated = (status: undefined | UtrvAssuranceStatus) => {
  if (!status) {
    return false;
  }

  return [UtrvAssuranceStatus.CompletedOpen, UtrvAssuranceStatus.Restated].includes(status);
};

export const generateUtrvAssuranceStatus = (
  utrv: Pick<UniversalTrackerValuePlain, 'assuranceStatus'>,
  initialAssurances: { status?: AssuranceStatus }[]
) => {
  // Ensure we only have valid ones, excluding inactive statuses (should be filtered)
  const assurances = initialAssurances.filter(a => {
    return a.status && !inactiveUtrvStatuses.includes(a.status);
  }) as { status: AssuranceStatus }[];

  if (assurances.length === 0) {
    // No more assurances left, remove status;
    return undefined;
  }

  // some is rejected => rejected
  const hasDispute = assurances.some(a => a.status === AssuranceStatus.Rejected);
  if (hasDispute) {
    return UtrvAssuranceStatus.Rejected;
  }

  // if the current status is complete open, keep it
   if (utrv.assuranceStatus === UtrvAssuranceStatus.CompletedOpen) {
    return UtrvAssuranceStatus.CompletedOpen;
  }

  const hasComplete = assurances.some(a => a.status === AssuranceStatus.Completed);
  // Something else has completed, therefore we keep it as completed
  if (hasComplete) {
    return UtrvAssuranceStatus.Completed;
  }

  const hasPartialAssurance = assurances.some(a => a.status === AssuranceStatus.Partial);
  // Something else has partial assurance, therefore it is partial
  if (hasPartialAssurance) {
    return UtrvAssuranceStatus.Partial;
  }

  // Nothing left as completed or partial assurance, then it is created
  return UtrvAssuranceStatus.Created;
}

interface ApplyAssuranceRejectParams {
  utrvAssurance: Pick<UniversalTrackerValueAssuranceModel, 'history' | 'status' | 'partialFields'>;
  utrv: UniversalTrackerValuePlain;
  userId: ObjectId;
}

export const applyAssuranceReject = ({ utrvAssurance, utrv, userId }: ApplyAssuranceRejectParams) => {
  utrvAssurance.history.push({
    utrvHistoryIndex: utrv.history.length,
    action: AssuranceAction.Rejected,
    userId,
  });
  utrvAssurance.status = AssuranceStatus.Rejected;
  utrvAssurance.partialFields = undefined;

  return utrvAssurance
};

export const applyAssurancePending = ({ utrvAssurance, utrv, userId }: ApplyAssuranceRejectParams) => {
  utrvAssurance.history.push({
    utrvHistoryIndex: utrv.history.length,
    action: AssuranceAction.Pending,
    userId,
  });
  utrvAssurance.status = AssuranceStatus.Pending;

  return utrvAssurance
};

export const applyAssurancePendingAfterComplete = ({ utrvAssurance, utrv, userId }: ApplyAssuranceRejectParams) => {
  utrvAssurance.history.push({
    utrvHistoryIndex: utrv.history.length,
    action: AssuranceAction.PendingAfterPortfolioComplete,
    userId,
  });
  utrvAssurance.status = AssuranceStatus.PendingAfterPortfolioComplete;

  return utrvAssurance
};

interface AssureParams extends ApplyAssuranceRejectParams {
  documents: ObjectId[];
}

interface PartialAssureParams extends ApplyAssuranceRejectParams {
  partialFields: PartialAssuranceField[];
}

export const applyAssuranceComplete = ({ utrvAssurance, utrv, userId, documents }: AssureParams) => {

  utrvAssurance.history.push({
    utrvHistoryIndex: utrv.history.length,
    action: AssuranceAction.Completed,
    userId,
    documents,
  });
  utrvAssurance.status = AssuranceStatus.Completed;
  utrvAssurance.partialFields = undefined;

  return utrvAssurance
};

export const applyAssurancePartialComplete = ({ utrvAssurance, utrv, userId, partialFields }: PartialAssureParams) => {

  utrvAssurance.history.push({
    utrvHistoryIndex: utrv.history.length,
    action: AssuranceAction.Partial,
    userId,
    partialFields
  });
  utrvAssurance.partialFields = partialFields;
  utrvAssurance.status = AssuranceStatus.Partial;

  return utrvAssurance
};
