/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import sanitize from 'sanitize-filename';
import { wwgLogger } from '../wwgLogger';
import { createAssuranceEvidence } from './AssuranceEvidence';
import documentModel, { DocumentModel, DocumentPlain, DocumentType, } from '../../models/document';
import { UtrvAssuranceExtended } from './model/Assurance';
import { UserPlain } from '../../models/user';
import { FileStorageInterface, getStorage } from '../storage/fileStorage';
import { ArchiveFile, createArchive } from '../file/archive';
import { getDocumentExtension } from '../file/extension';
import { createTempDir, mkDirByPathSync, removeFile, removeTmpFolder, } from '../file/filesystem';
import { AssuranceExporter, createAssuranceExporter, } from './AssuranceExporter';
import { AssurancePortfolioModel, AssurancePortfolioPlain, } from './model/AssurancePortfolio';
import { BundleData, UniversalTrackerRepository, } from '../../repository/UniversalTrackerRepository';
import { defaultHeaders, getCsvName } from './csvContext';
import { ObjectId } from 'bson';
import { AssuranceRepository } from '../../repository/AssuranceRepository';
import { combinedEvidence, getStakeholdersEvidence } from '../utr/utrvHistory';
import { ValueHistory } from '../../models/universalTrackerValue';
import { BlueprintRepository, getBlueprintRepository, } from '../../repository/BlueprintRepository';
import { bundleInstructions } from './files/bundleInstructions';
import TrackerEvidence from "../evidence/TrackerEvidence"
import { SurveyPlainExtended } from "../../models/survey"
import { ReportGenerator } from '../scorecard/ReportGenerator';
import {
  BlueprintContribution,
  BlueprintContributions,
  getBluePrintContribution,
} from '../survey/BlueprintContribution';
import { Blueprints } from '../../survey/blueprints';
import { ValueList } from '../../models/public/valueList';
import { UtrValueType } from '../../models/public/universalTrackerType';
import { FileParserType } from '../survey/transfer/parserTypes';
import { writeFile } from '@sheet/core';
import { DownloadDisplayOptions } from '../survey/scope/downloadScope';

export interface AssuranceDocumentExtended {
  document: DocumentPlain;
  type: string;
}

interface GlobalBundleInfo extends DownloadDisplayOptions {
  user: UserPlain;
  assuranceEvidence: AssuranceDocumentExtended[];
  preferredTypes?: string[];
  valueLists: ValueList[];
  utrTagMap?: Map<string, string[]>;
}

interface Bundle extends GlobalBundleInfo {
  evidenceDir?: string;
}

export interface BundleContext extends GlobalBundleInfo {
  context: BundleData,
  files?: DocumentPlain[],
  updateHistory?: ValueHistory,
  verifyHistory?: ValueHistory,
  tableDataIndex?: number,
  contributions: BlueprintContributions,
}

interface BundleArchiveData {
  records: any[];
}

interface SurveyBundle extends DownloadDisplayOptions {
  survey: SurveyPlainExtended;
  ids: ObjectId[];
  user: UserPlain;
  preferredTypes: string[];
  filenameType?: string;
  utrTagMap?: Map<string, string[]>;
}

export class UtrvFileBundler {
  private readmeFile = {
    path: bundleInstructions,
    name: 'README.txt',
    isStream: true
  };

  private readonly evidenceFolderName = 'evidence';

  constructor(
    private bundleUploader: TrackerEvidence,
    private storage: FileStorageInterface,
    private assuranceExporter: AssuranceExporter,
    private blueprintRepository: BlueprintRepository,
    private blueprintContribution: BlueprintContribution,
  ) {
  }

  public async createAssuranceBundle(context: BundleData, user: UserPlain): Promise<DocumentModel> {

    const { _id, compositeData } = context;
    const modelId = String(_id);

    const utrvContextData = await UniversalTrackerRepository.getBundleData([
      _id,
      ...(compositeData?.fragmentUtrvs ?? []),
    ]);

    const evidenceDir = await createTempDir(modelId);
    const docs = await this.getBundleAssuranceDocs(utrvContextData, evidenceDir)
    const bundles = await Promise.all(utrvContextData.map(this.createBundle({
      user,
      evidenceDir,
      assuranceEvidence: docs,
      valueLists: [],
    })));

    return this.createBundleArchive(bundles, this.getName(context), modelId, user, evidenceDir);
  }

  private async getBundleAssuranceDocs(utrvContextData: BundleData[], evidenceDir: string): Promise<AssuranceDocumentExtended[]> {
    const portfolioIds = this.portfolioIds(utrvContextData);
    const id = portfolioIds.pop();
    if (!id) {
      return [];
    }

    const portfolio = await AssuranceRepository.findPortfolioById(id)
    if (!portfolio) {
      return [];
    }

    return this.getAssuranceDocs(portfolio, evidenceDir)
  }

  private async uploadBundleFile(path: string, filename: string, size: number, modelId: string, user: UserPlain) {
    const file = {
      encoding: '',
      fieldname: '',
      destination: path,
      filename: filename,
      mimetype: 'application/zip',
      originalname: `${filename}_bundle.zip`,
      path: path,
      size: size,
    } as Express.Multer.File;

    const bundleZipDocuments = await this.bundleUploader.processUpload(
      {
        ownerId: modelId,
        userId: user._id,
        files: [file],
      }
    );

    // Clean up zip
    removeFile(path);

    const [bundle] = bundleZipDocuments;
    if (!bundle) {
      throw new Error(`Failed to upload bundle from path: ${path}, filename: ${filename}, modelId: ${modelId}`)
    }

    return bundle;
  }

  public uploadPortfolioFiles (portfolio: AssurancePortfolioPlain, user: UserPlain, files: any[]): Promise<DocumentModel[]> {
    return this.bundleUploader.processUpload(
      {
        ownerId: String(portfolio._id),
        userId: user._id,
        files
      }
    );
  }

  private async getEvidenceFiles(evidence: ObjectId[]) {
    const docs = await documentModel.find({ _id: { $in: evidence } }).lean().exec();
    const links: DocumentPlain[] = [];
    const fileDocs: DocumentPlain[] = [];
    docs.forEach((d: DocumentPlain) => d.type === DocumentType.File ? fileDocs.push(d) : links.push(d));
    return { links, fileDocs };
  }

  private async downloadBundleFiles(fileDocs: DocumentPlain[], downloadPath = '/tmp') {

    // Download all evidenceFiles
    const fileRequests: Promise<ArchiveFile>[] = fileDocs.map((d: DocumentPlain) => {
      const ext = getDocumentExtension(d);
      const fileName = d.metadata && d.metadata.name ? d.metadata.name : `${d._id}${ext ? '.' + ext : ''}`;
      const destination = `${downloadPath}/${sanitize(fileName)}`;
      return this.storage.downloadFile(d.path, destination).then(() => ({
        path: destination,
        name: d.metadata ? d.metadata.name : String(d._id),
      })).catch((e) => {
        wwgLogger.error(e);
        return { path: destination, name: '--'}
      });
    });

    // Bundle up
    return Promise.all(fileRequests);
  }

  async createPortfolioAssuranceBundle(portfolio: AssurancePortfolioModel, utrvAssurances: UtrvAssuranceExtended[], user: UserPlain): Promise<DocumentPlain> {

    const expandedPortfolio = await AssuranceRepository.getAssuranceExpanded(portfolio._id);
    if (!expandedPortfolio || !expandedPortfolio.survey) {
      throw new Error(`Failed to find portfolio with id ${portfolio._id}`)
    }
    const filename = getCsvName(expandedPortfolio);

    const ids = utrvAssurances.map(a => a.utrvId);
    const modelId = String(portfolio._id);

    const utrvContextData = await UniversalTrackerRepository.getBundleData(ids);

    const evidenceDir = await createTempDir(String(portfolio._id));

    const { initiative, survey } = expandedPortfolio;
    if (initiative && survey) {
      const type = FileParserType.Xlsx;
      const filePath = `${evidenceDir}/${filename}.${type}`;
      const workbook = await this.assuranceExporter.exportSurveyXlsx({
        user,
        survey,
        utrvIds: ids,
        type: type,
        additionalColumns: [{ id: 'provenance' }],
        applyMultiRowStyle: true,
      });
      writeFile(workbook, filePath, { type: 'file',  cellStyles: true });
      wwgLogger.info(`Added ${type} export file to assurance bundle`, { evidenceDir, filePath })
    }

    const docs = await this.getAssuranceDocs(portfolio, evidenceDir)
    const bundles = await Promise.all(utrvContextData.map(this.createBundle({
      user,
      evidenceDir,
      assuranceEvidence: docs,
      valueLists: [],
    })));

    return this.createBundleArchive(bundles, filename, modelId, user, evidenceDir);
  }

  private async getAssuranceDocs(portfolio: AssurancePortfolioPlain, evidenceDir: string) {
    let docs: AssuranceDocumentExtended[] = [];
    const documents = portfolio.documents;
    if (documents) {
      const ids = documents.map(({ documentId }) => documentId)
      if (ids.length > 0) {
        const downloadDir = evidenceDir + '/assurance';
        mkDirByPathSync(downloadDir);
        const { fileDocs } = await this.getEvidenceFiles(ids);
        await this.downloadBundleFiles(fileDocs, downloadDir);

        docs = fileDocs.map((d) => {
          return {
            type: documents.find(({ documentId }) => String(documentId) === String(d._id))?.type ?? 'unknown',
            document: d
          }
        })
      }
    }
    return docs
  }

  private portfolioIds(utrvContextData: BundleData[]) {
    return utrvContextData.reduce((a, { universalTrackerValueAssurances }) => {
      universalTrackerValueAssurances.forEach(({ portfolioId }) => {
        if (portfolioId) {
          a.push(portfolioId)
        }
      })
      return a;
    }, <ObjectId[]>[])
  }

  public async createSurveyBundle({ survey, ids, user, preferredTypes, filenameType }: SurveyBundle): Promise<DocumentModel> {

    const initiative = survey.initiative;
    const filename = getCsvName({ _id: survey._id, initiative: initiative, survey, type: filenameType });
    const modelId = String(survey._id);

    const utrvContextData = await UniversalTrackerRepository.getBundleData(ids);

    const evidenceDir = await createTempDir(modelId);

    const { data, headers } = await ReportGenerator.generate(initiative, user, survey._id);
    const csvName = ReportGenerator.getCsvFileName(initiative);
    await this.assuranceExporter.writeObjectFile(`${evidenceDir}/${csvName}`, data, headers);

    // Survey already have assurance here
    const [portfolio] = survey.assurance;
    const docs = portfolio ? await this.getAssuranceDocs(portfolio, evidenceDir) : [];

    const bundles = await Promise.all(utrvContextData.map(this.createBundle({
      user,
      evidenceDir,
      assuranceEvidence: docs,
      preferredTypes,
      valueLists: [],
    })));

    return this.createBundleArchive(bundles, filename, modelId, user, evidenceDir);
  }

  private async createBundleArchive(
    bundles: BundleArchiveData[],
    filename: string,
    modelId: string,
    user: UserPlain,
    evidenceDir: string
  ) {
    const csvName = filename + '.csv';
    const records = [defaultHeaders()];
    bundles.forEach((bundle) => records.push(...bundle.records));
    await this.assuranceExporter.writeFile(`${evidenceDir}/${csvName}`, records);

    const { path, size } = await createArchive(
      [{ path: evidenceDir, name: 'bundle', isDir: true }, this.readmeFile],
      `/tmp/${modelId}.zip`
    );

    await removeTmpFolder(evidenceDir);

    return await this.uploadBundleFile(path, filename, size, modelId, user);
  }

  private createBundle(bundle: Bundle) {
    return async (utrvBundleData: BundleData) => {
      const {
        user,
        evidenceDir,
        assuranceEvidence,
        valueLists,
        utrTagMap,
        preferredTypes = [],
        displayUserInput,
        displayTag,
      } = bundle;

      const { updateHistory, verifyHistory } = getStakeholdersEvidence(utrvBundleData);
      const { links, fileDocs } = await this.getEvidenceFiles(combinedEvidence([updateHistory, verifyHistory]));


      const survey = utrvBundleData.survey;
      const contributions = survey ? await this.blueprintContribution.getContributions(survey.sourceName as Blueprints) : {};

      const name = this.getName(utrvBundleData);

      try {
        const records = await this.createRecords({
          context: utrvBundleData,
          user,
          updateHistory,
          verifyHistory,
          assuranceEvidence,
          preferredTypes,
          valueLists,
          utrTagMap,
          files: links.concat(...fileDocs),
          contributions,
          displayUserInput,
          displayTag,
        });

        if (evidenceDir && fileDocs.length > 0) {
          const downloadDir = `${evidenceDir}/${this.evidenceFolderName}/${this.getUtrFolderName(utrvBundleData)}`;
          mkDirByPathSync(downloadDir);
          await this.downloadBundleFiles(fileDocs, downloadDir);
        }

        return { evidenceDir, name, records };
      } catch (e) {
        wwgLogger.error(e);
        return { evidenceDir, name, records: [] };
      }
    };
  }

  private async createRecords(bundleContext: BundleContext) {

    if (this.isMultiRowTable(bundleContext.context)) {
      const tableData = bundleContext.context.valueData?.table ?? [];
      const updates = tableData.map((_, index) => {
        return this.assuranceExporter.createRecord({
          ...bundleContext,
          tableDataIndex: index,
        })
      })
      return Promise.all(updates);
    }

    return Promise.all([this.assuranceExporter.createRecord(bundleContext)]);
  }

  private isMultiRowTable(utrvBundleData: BundleData) {
    if (utrvBundleData.universalTracker.valueType !== UtrValueType.Table) {
      return false;
    }

    const table = utrvBundleData.valueData?.table;
    return table && table?.length > 0;
  }

  private getName(utrvBundleData: BundleData) {
    const { type, name: utrName, typeCode } = utrvBundleData.universalTracker;
    const prefixName = utrName || `${type}-${typeCode}`;

    return sanitize(String(prefixName + ' (' + utrvBundleData._id + ')'));
  }

  private getUtrFolderName({ universalTracker }: BundleData) {
    return sanitize(String(universalTracker.name)) + '/Composite';
  }
}

export const createAssuranceFileBundler = (uploader: TrackerEvidence = createAssuranceEvidence()) => {
  return new UtrvFileBundler(
    uploader,
    getStorage(),
    createAssuranceExporter(),
    getBlueprintRepository(),
    getBluePrintContribution()
  )
};
