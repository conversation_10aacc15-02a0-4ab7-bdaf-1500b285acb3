/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { writeCsvFile, writeObjectCsvFile } from '../file/writer/CsvFileWriter';
import { BundleContext } from './UtrvFileBundler';
import { convertRecord } from './csvContext';
import { FileExporter, getFileExporter, ExportOptions } from '../survey/FileExporter';

export class AssuranceExporter {

  constructor(private fileExporter: FileExporter) {
  }

  public async exportSurveyXlsx(options: ExportOptions) {
    return this.fileExporter.exportXlsx(options);
  }

  public async writeFile(path: string, records: any[]) {
    await writeCsvFile({ path, records, });
    return path;
  }

  public async writeObjectFile(path: string, records: any[], header: any[]) {
    await writeObjectCsvFile({ path, records, header });
    return path;
  }

  public createRecord(bundleContext: BundleContext, type = 'csv') {
    return convertRecord(bundleContext);
  }
}

export const createAssuranceExporter = () => new AssuranceExporter(
  getFileExporter(),
);
