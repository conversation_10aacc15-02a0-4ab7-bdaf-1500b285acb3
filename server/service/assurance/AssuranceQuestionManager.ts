/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { SurveyCalculator } from '../survey/SurveyCalculator';
import {
  AssurancePortfolioAction,
  AssurancePortfolioModel,
  AssurancePortfolioPlain
} from './model/AssurancePortfolio';
import {
  BlueprintRepository,
  getBlueprintRepository
} from '../../repository/BlueprintRepository';
import { AssuranceRepository } from '../../repository/AssuranceRepository';
import { AssuranceAction, AssuranceStatus, UtrvAssuranceExtended } from './model/Assurance';
import { UserModel, UserPlain } from '../../models/user';
import { wwgLogger } from '../wwgLogger';
import { UtrvFileBundler, createAssuranceFileBundler } from './UtrvFileBundler';
import { ObjectId } from 'bson';
import { DocumentPlain } from '../../models/document';
import { UniversalTrackerRepository } from '../../repository/UniversalTrackerRepository';
import { SurveyRepository } from '../../repository/SurveyRepository';
import { loadDocumentDownloadUrls } from '../storage/fileStorage';
import { AssurancePermissions } from './AssurancePortfolioPermissions';
import { Actions, OptionalAction, validateActionType } from '../action/Actions';
import UserError from '../../error/UserError';
import PermissionDeniedError from '../../error/PermissionDeniedError';
import { AssurancePermissionType, UtrvAssurancePermissions } from '../../models/assurancePermission';
import { UserRepository } from '../../repository/UserRepository';
import BadRequestError from '../../error/BadRequestError';

interface PermissionsUpdateRequest {
  userId?: string;
  email?: string;
  action: OptionalAction;
  utrvAssuranceIds: string[];
}

export class AssuranceQuestionManager {

  constructor(
    private surveyRepo: BlueprintRepository,
    private bundler: UtrvFileBundler
  ) {
  }


  public async getSelection(assurancePortfolio: AssurancePortfolioPlain) {
    if (!assurancePortfolio.surveyId) {
      return undefined;
    }

    const [action] = await SurveyRepository.getInitiativeSurveyUtrvActions(
      assurancePortfolio.surveyId
    );

    const blueprint = await this.surveyRepo.getExpandedBlueprintByCode(action.sourceName);
    if (!blueprint) {
      throw new Error(`Failed to find blueprint for "${action.sourceName}"`)
    }
    await SurveyCalculator.processSurveyActions(action, blueprint);

    const utrvAssurances = await AssuranceRepository.getPortfolioQuestions(assurancePortfolio._id);

    return {
      current: utrvAssurances.map(utrva => utrva.utrvId),
      survey: action,
    }
  }

  public async download(portfolio: AssurancePortfolioModel, questionId: string, user: UserPlain) {
    const assuranceUtrv = <UtrvAssuranceExtended>await AssuranceRepository.findByUtrvIdExtended(new ObjectId(questionId));
    if (!assuranceUtrv) {
      throw new Error('Failed to find assurance question with id ' + questionId);
    }
    // get latest download
    const existingDocs = await this.getBundleDocuments(assuranceUtrv);
    if (existingDocs.length > 0) {
      return existingDocs;
    }

    await this.createBundle(assuranceUtrv, user);
    return this.getBundleDocuments(assuranceUtrv);
  }

  public async createBundle(utrvAssurance: UtrvAssuranceExtended, user: UserPlain) {
    utrvAssurance.history.push({ action: AssuranceAction.BundleStart, userId: user._id });
    await utrvAssurance.save();

    try {
      const [context] = await UniversalTrackerRepository.getBundleData([utrvAssurance.utrvId]);
      const bundleDocument = await this.bundler.createAssuranceBundle(context, user);

      const items = {
        action: AssuranceAction.BundleComplete,
        documents: [bundleDocument._id],
        userId: user._id,
        utrvHistoryIndex: utrvAssurance.universalTrackerValue.history.length,
      };
      utrvAssurance.history.push(items);

    } catch (e) {
      wwgLogger.error(e);
      utrvAssurance.history.push({ action: AssuranceAction.BundleFailed, userId: user._id });
    }

    return utrvAssurance.save();
  }

  private async handleAssigneePermissions({
    assignee,
    utrvAssuranceIds,
    action,
    assurancePortfolio,
    delegator,
  }: {
    assignee: UserModel,
    utrvAssuranceIds: string[],
    action: Actions,
    assurancePortfolio: AssurancePortfolioPlain,
    delegator: UserModel,
  }) {
    const filteredAssurances = await AssuranceRepository.findUniversalTrackerValueAssuranceExtended(
      assurancePortfolio._id,
      {
        status: { $ne: AssuranceStatus.Removed },
        utrvId: { $in: utrvAssuranceIds.map(id => new ObjectId(id)) }
      }
    );

    await Promise.all(
      filteredAssurances.map((utrvAssurance) => {
        utrvAssurance.permissions = AssurancePermissions.mergeData(
          utrvAssurance.toObject().permissions as AssurancePermissionType<UtrvAssurancePermissions>[],
          {
            data: { userId: assignee._id, permissions: [UtrvAssurancePermissions.AssurerFollower] },
            action,
          }
        );
        return utrvAssurance.save();
      })
    );
    wwgLogger.info(`Followed or delegated users to ${utrvAssuranceIds.length} questions`, {
      assurancePortfolioId: assurancePortfolio._id,
      delegatorId: delegator._id,
      utrvAssuranceIds,
      action
    });

    return {
      _id: assignee._id,
      firstName: assignee.firstName,
      surname: assignee.surname,
      profile: assignee.profile,
      active: assignee.active,
    };
  }

  public async updateQuestionPermissions(
    assurancePortfolio: AssurancePortfolioPlain,
    body: PermissionsUpdateRequest,
    delegator: UserModel
  ) {
    const { userId: id, action, email, utrvAssuranceIds } = body;
    const userId = !id && !email ? delegator._id : id;

    const validAction = validateActionType(action);
    if (!Array.isArray(utrvAssuranceIds) || utrvAssuranceIds.length === 0) {
      throw new UserError('Please provide at least one question to follow or to assign users');
    }

    if (!id && !email) {
      // dealing with self
      if (!(await AssurancePermissions.canAccessAssurancePortfolio(assurancePortfolio, delegator))) {
        throw new PermissionDeniedError();
      }
      return this.handleAssigneePermissions({
        assignee: delegator,
        assurancePortfolio,
        utrvAssuranceIds,
        action: validAction,
        delegator,
      });
    }

    if (!(await AssurancePermissions.isAdmin(assurancePortfolio, delegator))) {
      throw new PermissionDeniedError();
    }

    const assignee = String(delegator._id) === String(userId) ? delegator : await UserRepository.findByIdOrEmail({ userId, email });
    if (!assignee) {
      throw new BadRequestError('Failed to find target user.');
    }

    if (assignee.organizationId && String(assignee.organizationId) !== String(assurancePortfolio.organizationId)) {
      throw new BadRequestError('Conflicted organization assignment. User has already linked to an existing organization', {
        assignee: assignee._id.toString(),
        assigneeOrganizationId: assignee.organizationId?.toString(),
        organizationId: assurancePortfolio.organizationId?.toString(),
        delegator: delegator._id.toString(),
      });
    }

    return this.handleAssigneePermissions({ assignee, assurancePortfolio, utrvAssuranceIds, action: validAction, delegator });
  }

  public async getSelectedDelegators(assurancePortfolio: AssurancePortfolioPlain, body: { utrvAssuranceIds: string[] }, delegator: UserModel) {
    const { utrvAssuranceIds } = body;
    const isAdmin = await AssurancePermissions.isAdmin(assurancePortfolio, delegator);
    const existing = await AssuranceRepository.findSelectedUniversalTrackerValueAssuranceUsers(
      {
        portfolioId: assurancePortfolio._id,
        utrvAssuranceIds,
      }
    );

    if (!isAdmin) {
      return existing.filter(user => user._id.equals(delegator._id));
    }

    return existing;
  }

  private async getBundleDocuments(utrvAssurance: UtrvAssuranceExtended): Promise<DocumentPlain[]> {

    const { history, universalTrackerValue} = utrvAssurance;

    let bundleHistory;
    for (const h of history) {
      if (h.action == AssurancePortfolioAction.BundleComplete
        && h.utrvHistoryIndex === universalTrackerValue.history.length) {
        bundleHistory = h
      }
    }

    if (!bundleHistory?.documents) {
      return [];
    }

    return loadDocumentDownloadUrls(bundleHistory.documents)
  }
}

export const createAssuranceQuestionManager = () => {
  return new AssuranceQuestionManager(
    getBlueprintRepository(),
    createAssuranceFileBundler(),
  );
};
