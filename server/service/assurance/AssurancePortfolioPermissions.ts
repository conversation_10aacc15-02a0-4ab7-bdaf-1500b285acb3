/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import { AssuranceRepository } from '../../repository/AssuranceRepository';
import { UserModel, UserPlain } from '../../models/user';
import PermissionDeniedError from '../../error/PermissionDeniedError';
import {
  AssurancePortfolioExpanded,
  AssurancePortfolioModel,
  AssurancePortfolioPlain,
} from './model/AssurancePortfolio';
import { AssurancePermissionType, AssurancePortfolioPermission } from '../../models/assurancePermission';
import { Actions } from '../action/Actions';
import { InitiativePermissions } from '../initiative/InitiativePermissions';
import { AssuranceOrganizationPermissions } from './AssuranceOrganizationPermissions';

export interface AssurancePermissionsUpdate<T extends string> {
  data: AssurancePermissionType<T>;
  action: Actions;
}

export const MAP_ROLES = {
  ADMIN: 'verifier',
  VERIFIER: 'admin',
  STAKEHOLDER: 'user',
  USER: 'stakeholder',
};

export class AssurancePermissions {
  public static async getPortfolio(portfolioId: string | ObjectId, user: UserModel): Promise<AssurancePortfolioModel> {
    const portfolio = await AssuranceRepository.findPortfolioById(portfolioId, false);

    if (!portfolio) {
      throw new Error(`Portfolio not found`);
    }

    if (!(await this.hasAccess(portfolio, user))) {
      throw new PermissionDeniedError();
    }

    return portfolio as AssurancePortfolioModel;
  }

  public static async getPortfolioExpanded(
    portfolioId: string | ObjectId,
    user: UserModel
  ): Promise<AssurancePortfolioExpanded> {
    const portfolio = await AssuranceRepository.getAssuranceExpanded(new ObjectId(portfolioId));

    if (!portfolio) {
      throw new Error(`Portfolio not found`);
    }

    if (!(await this.hasAccess(portfolio, user))) {
      throw new PermissionDeniedError();
    }

    return portfolio;
  }

  public static async hasAccess(
    portfolio: Pick<AssurancePortfolioPlain, 'organizationId' | 'initiativeId' | 'permissions'>,
    user: UserPlain
  ) {
    return (
      (await this.canAccessAssurancePortfolio(portfolio, user)) ||
      (await InitiativePermissions.canAccess(user, portfolio.initiativeId))
    );
  }

  public static async isAdmin(
    portfolio: Pick<AssurancePortfolioPlain, 'permissions' | 'organizationId'>,
    user: Pick<UserModel, '_id'>
  ) {
    return (
      portfolio.permissions.some(
        (permission) =>
          permission.userId.equals(user._id) && permission.permissions.includes(AssurancePortfolioPermission.Admin)
      ) || (await AssuranceOrganizationPermissions.canManageOrganization(user._id, portfolio.organizationId))
    );
  }

  public static async canAssureMetrics(
    portfolio: Pick<AssurancePortfolioPlain, 'permissions' | 'organizationId'>,
    user: Pick<UserModel, '_id'>
  ) {
    return (
      portfolio.permissions.some(({ userId, permissions }) => {
        return (
          userId.equals(user._id) &&
          permissions.some((permission) =>
            [AssurancePortfolioPermission.Admin, AssurancePortfolioPermission.Assurer].includes(permission)
          )
        );
      }) || (await AssuranceOrganizationPermissions.canAssureMetrics(user._id, portfolio.organizationId))
    );
  }

  /**
   * We should ensure that permissions is never empty in schema level?
   * for now we do it here
   */
  public static async canAccessAssurancePortfolio(
    portfolio: Pick<AssurancePortfolioPlain, 'permissions' | 'organizationId'>,
    user: Pick<UserModel, '_id'>
  ) {
    return (
      portfolio.permissions.some(({ userId, permissions }) => {
        return (
          userId.equals(user._id) &&
          permissions.some((permission) =>
            [AssurancePortfolioPermission.Admin, AssurancePortfolioPermission.Assurer].includes(permission)
          )
        );
      }) || (await AssuranceOrganizationPermissions.canAccessPortfolios(user._id, portfolio.organizationId))
    );
  }

  public static mergeData<T extends string>(
    current: AssurancePermissionType<T>[],
    { data, action }: AssurancePermissionsUpdate<T>
  ) {
    let updatingData = current.slice().find((permissionData) => permissionData.userId.equals(data.userId));
    const unchangedData = current.filter((permissionData) => !permissionData.userId.equals(data.userId));
    switch (action) {
      case Actions.Add:
        if (!updatingData) {
          return [...unchangedData, data];
        }
        updatingData = {
          ...updatingData,
          permissions: Array.from(new Set([...updatingData.permissions, ...data.permissions])),
        };
        return [...unchangedData, updatingData];
      case Actions.Remove:
        if (!updatingData) {
          return unchangedData;
        }
        updatingData = {
          ...updatingData,
          permissions: updatingData.permissions.filter((permission) => !data.permissions.includes(permission)),
        };

        if (updatingData.permissions.length === 0) {
          return unchangedData;
        }
        return [...unchangedData, updatingData];
      default:
        throw new Error('Stakeholder group only support add/remove actions');
    }
  }
}
