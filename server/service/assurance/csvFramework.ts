/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { UniversalTrackerPlain } from '../../models/universalTracker';
import type { Tags } from '../../types/universalTrackerValue';
import { BundleContext } from './UtrvFileBundler';
import { frameworkMap } from '../utr/frameworks';

export interface MappingItem {
  name: string,
  type: keyof Tags,
}

const getFrameworkText = (utr: UniversalTrackerPlain, tag: keyof Tags) => {
  const codes = utr.tags?.[tag];
  if (!codes) {
    return '';
  }

  return codes
    .map(code => frameworkMap.get(code)?.name)
    .filter(Boolean)
    .join(', ');
}

const mappingItems: MappingItem[] = [
  { name: 'TCFD', type: 'tcfd' },
  { name: 'PIC', type: 'pic' },
  { name: 'ESE', type: 'ese' },
  { name: 'WEF', type: 'wef' },
  { name: 'UNGC', type: 'ungc' },
  { name: 'CDSB', type: 'cdsb' },
];


export const createFrameworkColumns = () => {
  return mappingItems.map(({ name, type }: MappingItem) => ({
    name,
    property: (bundleContext: BundleContext) => getFrameworkText(
      bundleContext.context.universalTracker,
      type,
    ),
  }))
}
