/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import { StakeholderGroup } from '../../../models/stakeholderGroup';
import { HydratedDocument } from 'mongoose';
import { AssurancePermissionType, UtrvAssurancePermissions } from '../../../models/assurancePermission';
import type { AssurancePortfolioModel } from './AssurancePortfolio';
import type { UniversalTrackerValueModel } from '../../../models/universalTrackerValue';
import type { PartialAssuranceField } from '../../../types/assurance';

interface History<T = ObjectId> {
  _id?: T;
  created?: Date;
  action: string;
  userId: T;
  documents?: T[];
  utrvHistoryIndex?: number;
  partialFields?: PartialAssuranceField[];
}

export interface UtrvAssuranceCreate<T = ObjectId> {
  utrvId: T;
  utrvHistoryIndex: number;
  stakeholders?: StakeholderGroup<T>;
  permissions: AssurancePermissionType<UtrvAssurancePermissions>[],
  status: AssuranceStatus;
  history: History<T>[];
  portfolioId?: T;
  partialFields?: PartialAssuranceField[];
}

export interface UniversalTrackerValueAssurancePlain<T = ObjectId> extends UtrvAssuranceCreate<T> {
  _id: T;
  created: Date;
}

interface UniversalTrackerValueAssuranceWithPortfolio<T = ObjectId> extends UniversalTrackerValueAssurancePlain<T> {
  universalTrackerValue?: UniversalTrackerValueModel;
  portfolio: AssurancePortfolioModel;
}

export type UniversalTrackerValueAssuranceModel = HydratedDocument<UniversalTrackerValueAssuranceWithPortfolio>;

export interface UtrvAssuranceExtended extends UniversalTrackerValueAssuranceModel {
  universalTrackerValue: UniversalTrackerValueModel;
}

export interface UtrvAssuranceWithPortfolio extends UniversalTrackerValueAssuranceModel {
  portfolio: AssurancePortfolioModel;
}

export enum AssuranceStatus {

  /** Added to portfolio **/
  Created = 'created',

  /** @deprecated **/
  Processing = 'processing',

  /**
   * Utrv was updated, need to reset
   */
  Pending = 'pending',

  /** Assured, but underlying utrv was updated after completing assurance portfolio  **/
  PendingAfterPortfolioComplete = 'pending_after_portfolio_complete',

  /** Triggered when whole portfolio is soft deleted **/
  Disabled = 'disabled',

  /** Triggered when question is removed assurance portfolio **/
  Removed = 'removed',

  /** Assured, by either individual assurance or portfolio complete action **/
  Completed = 'completed',

  /** Marked as disputed, can still be assured later **/
  Rejected = 'rejected',

  /** Marked as restated, can still be assured later */
  Restated = 'restated',

  /** Marked as partial assurance, can still be assured later */
  Partial = 'partial',
}

export const assuranceInProgressStatuses = [
  AssuranceStatus.Created,
  AssuranceStatus.Processing,
  AssuranceStatus.Pending,
  AssuranceStatus.Rejected,
];

/** These are no longer active statuses, that queries should always ignore **/
export const disabledUtrvAssuranceStatuses = [
  AssuranceStatus.Removed,
  AssuranceStatus.Disabled
];

/**
 * These are no longer active statuses,
 * that should be ignored in combined utrv assurance status
 */
export const inactiveUtrvStatuses = [
  AssuranceStatus.Removed,
  AssuranceStatus.Disabled,
  AssuranceStatus.PendingAfterPortfolioComplete, // Special case here
];

export enum AssuranceAction  {
  Created = 'created',
  BundleStart = 'bundle_start',
  BundleComplete = 'bundle_complete',
  BundleFailed = 'bundle_failed',
  RemoveFromPortfolio = 'remove_from_portfolio',
  AddedToPortfolio = 'add_to_portfolio',
  Completed = 'completed',
  Rejected = 'rejected',
  Pending = 'pending',
  PendingAfterPortfolioComplete = 'pending_after_portfolio_complete',
  Partial = 'partial'
}

export { PartialAssuranceField } from '../../../types/assurance';

export type PartialFieldsUtrvAssurances = Pick<UniversalTrackerValueAssurancePlain, 'utrvId' | 'partialFields'>[];
