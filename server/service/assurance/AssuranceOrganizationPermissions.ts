import { ObjectId } from 'bson';
import { AssurancePermissionType, OrganizationPermission } from '../../models/assurancePermission';
import Organization, {
  isOrganizationPermissionsMin,
  OrganizationLike,
  OrganizationPlain,
} from '../../models/organization';
import User, { UserPlain } from '../../models/user';
import { wwgLogger } from '../wwgLogger';

export class AssuranceOrganizationPermissions {
  private static hasIncludedPermissions({
    userPermissions,
    includedPermissions,
  }: {
    userPermissions: AssurancePermissionType<OrganizationPermission> | undefined;
    includedPermissions: OrganizationPermission[];
  }) {
    if (!userPermissions || userPermissions.permissions.length === 0) {
      return false;
    }
    return userPermissions.permissions.some((p) => includedPermissions.includes(p));
  }

  private static async havePermissionsForOrganization(
    userId: ObjectId | string,
    organizationOrId: OrganizationLike,
    permissions: OrganizationPermission[]
  ) {
    if (!organizationOrId) {
      return false;
    }

    if (isOrganizationPermissionsMin(organizationOrId)) {
      return this.hasIncludedPermissions({
        userPermissions: organizationOrId.permissions?.find((p) => p.userId.equals(userId)),
        includedPermissions: permissions,
      });
    }

    if (typeof organizationOrId !== 'string' && !ObjectId.isValid(organizationOrId)) {
      return false;
    }

    const isExist = await Organization.exists({
      _id: new ObjectId(organizationOrId),
      permissions: {
        $elemMatch: { userId, permissions: { $in: permissions } },
      },
    }).exec();

    return Boolean(isExist);
  }

  /* Included legacy users */
  public static async canAccessOrganization(userId: ObjectId | string, organizationOrId: OrganizationLike) {
    if (!organizationOrId) {
      return false;
    }

    if (isOrganizationPermissionsMin(organizationOrId)) {
      return this.havePermissionsForOrganization(userId, organizationOrId, Object.values(OrganizationPermission));
    }

    // Legacy users will be treated as restricted users
    const isExist = await Organization.exists({ _id: organizationOrId, 'permissions.userId': userId }).exec();
    return Boolean(isExist);
  }

  public static canManageOrganization(userId: ObjectId | string, organizationOrId: OrganizationLike) {
    return this.havePermissionsForOrganization(userId, organizationOrId, [OrganizationPermission.Admin]);
  }

  public static canAssureMetrics(userId: ObjectId | string, organizationOrId: OrganizationLike) {
    return this.havePermissionsForOrganization(userId, organizationOrId, [
      OrganizationPermission.Admin,
      OrganizationPermission.Assurer,
    ]);
  }

  public static canAccessPortfolios(userId: ObjectId | string, organizationOrId: OrganizationLike) {
    return this.havePermissionsForOrganization(userId, organizationOrId, [
      OrganizationPermission.Admin,
      OrganizationPermission.Assurer,
      OrganizationPermission.Viewer,
    ]);
  }

  public static async migrateLegacyUsers(userIds?: string[]) {
    wwgLogger.info('Start migrating organization legacy users permissions');

    const match: Record<string, any> = { organizationId: { $exists: true } };
    if (userIds?.length) {
      match._id = { $in: userIds.map((id) => new ObjectId(id)) };
    }

    const users = await User.find(match, { _id: 1, organizationId: 1 })
      .lean<Required<Pick<UserPlain, '_id' | 'organizationId'>>[]>()
      .exec();

    if (users.length === 0) {
      wwgLogger.info('No legacy users found. End migrating organization legacy users permissions');
      return { migratedUsersCount: 0, migratedOrganizationsCount: 0 };
    }

    const organizationUsersMap = new Map<string, ObjectId[]>();
    for (const user of users) {
      const organizationId = user.organizationId.toString();
      const organizationUserIds = organizationUsersMap.get(organizationId) ?? [];
      organizationUserIds.push(user._id);
      organizationUsersMap.set(organizationId, organizationUserIds);
    }

    const organizationIds = Array.from(organizationUsersMap.keys());
    const organizations = await Organization.find(
      { _id: { $in: organizationIds.map((id) => new ObjectId(id)) } },
      { _id: 1, permissions: 1 }
    )
      .lean<Pick<OrganizationPlain, '_id' | 'permissions'>[]>()
      .exec();

    const organizationPermissions = organizations.map(({ _id, permissions = [] }) => {
      const userIds = organizationUsersMap.get(_id.toString()) ?? [];
      userIds.forEach((userId) => {
        const userPermissions = permissions.find((p) => p.userId.equals(userId));

        if (!userPermissions) {
          permissions.push({ userId, permissions: [OrganizationPermission.RestrictedUser] });
          return;
        }

        if (userPermissions.permissions.length === 0) {
          userPermissions.permissions.push(OrganizationPermission.RestrictedUser);
        }
      });

      return {
        _id,
        permissions,
      };
    });

    const { modifiedCount: migratedOrganizationsCount } = await Organization.bulkWrite(
      organizationPermissions.map(({ _id, permissions }) => ({
        updateOne: {
          filter: { _id },
          update: {
            permissions,
          },
        },
      }))
    );

    wwgLogger.info('End migrating organization legacy users permissions', {
      migratedOrganizationsCount,
      organizationIds,
      migratedUsersCount: users.length,
      userIds: users.map(({ _id }) => _id.toString()),
    });

    return { migratedUsersCount: users.length, migratedOrganizationsCount };
  }
}
