import { ObjectId } from 'bson';
import { PartialAssuranceField, PartialFieldsUtrvAssurances } from './model/Assurance';
import { AssurancePermissionType, OrganizationPermission } from '../../models/assurancePermission';

export const getCombinedUtrvPartialFields = (combinedUtrvAssurances: PartialFieldsUtrvAssurances, utrvId: ObjectId) => {
  const combinedUtrvPartialFields: PartialAssuranceField[] = combinedUtrvAssurances
    .filter((utrvAssurance) => utrvAssurance.utrvId.equals(utrvId))
    .reduce((acc, utrvAssurance) => {
      const { partialFields = [] } = utrvAssurance;
      return [...acc, ...partialFields];
    }, [] as PartialAssuranceField[]);
  return combinedUtrvPartialFields;
};

export const mergePermissions = ({
  currentPermissions,
  upcomingPermissions,
}: {
  upcomingPermissions: AssurancePermissionType<OrganizationPermission>[];
  currentPermissions: AssurancePermissionType<OrganizationPermission>[];
}) =>
  upcomingPermissions.reduce(
    (acc, permission) => {
      const existingPermission = acc.find((p) => p.userId.equals(permission.userId));
      if (existingPermission) {
        // Merge the existing permission in the list
        existingPermission.permissions = [...new Set([...permission.permissions, ...existingPermission.permissions])];
        return acc;
      }
      acc.push(permission);
      return acc;
    },
    [...currentPermissions]
  );
