/*
* Copyright (c) 2021. World Wide Generation Ltd
*/

import { QldbDriver } from "amazon-qldb-driver-nodejs";
import { QLDB, GetBlockRequest, GetBlockResponse } from "@aws-sdk/client-qldb";
import { createHash } from "crypto";
import { dom, dumpText, load } from "ion-js";
import { ValueHistory } from "../../models/universalTrackerValue";
import config from "../../config";
import { wwgLogger } from "../wwgLogger";

export interface QLDBDigest {
  digest: string;
  digestTipAddress: {
    strandId: string;
    sequenceNo: number;
  }
}

interface ValidateResponse {
  verified: boolean;
  expectedHash?: Uint8Array;
  proofHash?: Uint8Array;
  errorDescription?: string;
}

export interface QLDBValueHistory extends ValueHistory {
  utrvId?: string;
}

export interface QLDBHistory {
  blockAddress: {
    sequenceNo: number;
    strandId: string;
  };
  data: QLDBValueHistory;
  hash: string;
  metadata: {
    id: string;
    txId: string;
    txTime: string;
    version: number;
  }
}

export interface EventRequest {
  utrvId: string;
  historyId?: string;
  app_env: string;
  ledgerDocumentId: string | string[];
  ledgerDocumentDate: string;
}

export class Qldb {
  static ledgerName: string = config.ledger.options.ledgerName;
  HASH_SIZE = 32;

  constructor(
    private qldbClient: QLDB,
    private qldbDriver: QldbDriver,
    private logger = wwgLogger,
  ) {
  }

  static getDriver() {
    if (!config.ledger.options.accessKeyId || !config.ledger.options.secretAccessKey) {
      return;
    }

    return new QldbDriver(
      this.ledgerName,
      {
        region: config.ledger.options.region,
        credentials: {
          accessKeyId: config.ledger.options.accessKeyId,
          secretAccessKey: config.ledger.options.secretAccessKey,
        }
      }
    );
  }

  static getClient() {
    if (!config.ledger.options.accessKeyId || !config.ledger.options.secretAccessKey) {
      return;
    }

    return new QLDB({
      region: config.ledger.options.region,
      credentials: {
        accessKeyId: config.ledger.options.accessKeyId,
        secretAccessKey: config.ledger.options.secretAccessKey,
      }
    });
  }

  public async getHistoryByDocumentId(documentId: string): Promise<dom.Value[]> {
    const result = await this.qldbDriver.executeLambda(async (txn) => {
      return txn.execute(`SELECT * FROM history(UniversalTrackerValueHistory) as h WHERE h.metadata.id = ?`, documentId);
    });

    return result.getResultList();
  }

  public async validateBlock(digest: QLDBDigest): Promise<GetBlockResponse> {
    const blockAddress = digest.digestTipAddress;

    const getBlockRequest: GetBlockRequest = {
      Name: Qldb.ledgerName,
      BlockAddress: {
        IonText: dumpText(blockAddress)
      },
      DigestTipAddress: { IonText: JSON.stringify(blockAddress) }
    };

    return await this.qldbClient.getBlock(getBlockRequest);
  }

  public validateDigest(digest: QLDBDigest, blockResponse: GetBlockResponse): ValidateResponse {

    const ret = (verified: boolean, str?: string): ValidateResponse => {
      return {
        verified: verified,
        errorDescription: str
      }
    };

    const proofIonText = blockResponse.Proof?.IonText;
    const ionText = blockResponse.Block?.IonText;
    if (!ionText) {
      return ret(false, 'Invalid BlockResponse');
    }
    if (!proofIonText) {
      return ret(false, 'Invalid Proof IonText');
    }

    const expectedDigest: Buffer = Buffer.from(digest.digest);

    const blockValue: dom.Value | null = load(ionText);
    if (blockValue === null) {
      return ret(false, 'Invalid blockValue');
    }

    let blockHash: Uint8Array | null | undefined = blockValue.get("blockHash")?.uInt8ArrayValue();
    if (blockHash === undefined || blockHash === null) {
      return ret(false, 'Invalid blockHash');
    }

    const proofValue = load(proofIonText);
    proofValue?.elements().forEach((proofHash: dom.Value) => {
      blockHash = this.dot((blockHash as Uint8Array), (proofHash.uInt8ArrayValue() as Uint8Array));
    });

    const verified = this.isEqual(expectedDigest, blockHash);

    return ret(verified, !verified ? 'Hashes do not match' : undefined);
  }

  /**
   * Takes two hashes, sorts them, concatenates them, and calculates a digest based on the concatenated hash.
   * @param h1 Byte array containing one of the hashes to compare.
   * @param h2 Byte array containing one of the hashes to compare.
   * @returns The digest calculated from the concatenated hash values.
   */
  private dot(h1: Uint8Array, h2: Uint8Array): Uint8Array {
    if (h1.length === 0) {
      return h2;
    }
    if (h2.length === 0) {
      return h1;
    }

    const newHashLib = createHash("sha256");

    let concatenated: Uint8Array;
    if (this.hashComparator(h1, h2) < 0) {
      concatenated = this.concatenate(h1, h2);
    } else {
      concatenated = this.concatenate(h2, h1);
    }
    newHashLib.update(concatenated);
    return newHashLib.digest();
  }

  /**
   * Compares two hashes by their **signed** byte values in little-endian order.
   * @param hash1 The hash value to compare.
   * @param hash2 The hash value to compare.
   * @returns Zero if the hash values are equal, otherwise return the difference of the first pair of non-matching
   *          bytes.
   * @throws RangeError When the hash is not the correct hash size.
   */
  private hashComparator(hash1: Uint8Array, hash2: Uint8Array): number {
    if (hash1.length !== this.HASH_SIZE || hash2.length !== this.HASH_SIZE) {
      throw new RangeError("Invalid hash.");
    }
    for (let i = hash1.length - 1; i >= 0; i--) {
      const difference: number = (hash1[i] << 24 >> 24) - (hash2[i] << 24 >> 24);
      if (difference !== 0) {
        return difference;
      }
    }
    return 0;
  }

  /**
   * Helper method that concatenates two Uint8Array.
   * @param arrays List of arrays to concatenate, in the order provided.
   * @returns The concatenated array.
   */
  private concatenate(...arrays: Uint8Array[]): Uint8Array {
    let totalLength = 0;
    for (const arr of arrays) {
      totalLength += arr.length;
    }
    const result = new Uint8Array(totalLength);
    let offset = 0;
    for (const arr of arrays) {
      result.set(arr, offset);
      offset += arr.length;
    }
    return result;
  }

  /**
   * Helper method that checks for equality between two Uint8Array.
   * @param expected Byte array containing one of the hashes to compare.
   * @param actual Byte array containing one of the hashes to compare.
   * @returns Boolean indicating equality between the two Uint8Array.
   */
  private isEqual(expected: Uint8Array, actual: Uint8Array): boolean {
    if (expected === actual) return true;
    if (expected == null || actual == null) return false;
    if (expected.length !== actual.length) return false;

    for (let i = 0; i < expected.length; i++) {
      if (expected[i] !== actual[i]) {
        return false;
      }
    }
    return true;
  }

}
