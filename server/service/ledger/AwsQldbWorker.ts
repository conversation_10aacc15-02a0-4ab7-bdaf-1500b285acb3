/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { QueueWorker } from '../queue/QueueWorker';
import { DeleteMessageRequest, ReceiveMessageRequest, SQS, Message } from "@aws-sdk/client-sqs";
import LedgerUniversalTrackerValue
  from '../../models/ledgerUniversalTrackerValue';
import { ObjectId } from 'bson';
import { wwgLogger } from '../wwgLogger';
import { UniversalTrackerValueRepository } from '../../repository/UniversalTrackerValueRepository';


export interface EventRequest {
  utrvId: string;
  historyId?: string;
  app_env: string;
  ledgerDocumentId: string | string[];
  ledgerDocumentDate: string;
}

class AwsQldbWorker implements QueueWorker {

  constructor(
    private client: SQS,
    private ledgerUtrvRepo: typeof LedgerUniversalTrackerValue,
    private utrvRepo: typeof UniversalTrackerValueRepository,
    private logger = wwgLogger,
  ) {
  }

  public async handle(message: Message) {

    const updateEvent: EventRequest = JSON.parse(message.Body as string);
    const utrvId = new ObjectId(updateEvent.utrvId);
    const historyId = new ObjectId(updateEvent.historyId);
    if (!historyId) {
      this.logger.error(`Received a ledger message for utrvId=${utrvId} with no historyId: ${updateEvent.app_env}`);
      return false;
    }

    const utrv = await this.utrvRepo.findById(utrvId);
    if (!utrv) {
      this.logger.info(`Failed to find utrvId="${utrvId}", event app_env: ${updateEvent.app_env}`);
      return false;
    }

    const ledgerUtrv = await this.ledgerUtrvRepo.findOne({ utrvId: utrvId }).exec();

    if (ledgerUtrv) {
      const ledgerEventDate = new Date(updateEvent.ledgerDocumentDate);
      const isNewer = ledgerEventDate > ledgerUtrv.lastUpdatedDate;
      if (isNewer) {
        ledgerUtrv.lastUpdatedDate = ledgerEventDate;
      }

      if (!Array.isArray(ledgerUtrv.historyIds)) {
        // For legacy reasons as old models may not have this newly-added property
        ledgerUtrv.historyIds = [];
      }
      ledgerUtrv.historyIds.push(historyId);
      // Just in case this changed (migration)
      ledgerUtrv.documentId = Array.isArray(updateEvent.ledgerDocumentId) ? updateEvent.ledgerDocumentId[0] : updateEvent.ledgerDocumentId;

      await ledgerUtrv.save();
      return true;
    }

    await LedgerUniversalTrackerValue.create({
      utrvId: utrvId,
      historyIds: [historyId],
      lastUpdatedDate: updateEvent.ledgerDocumentDate,
      documentId: updateEvent.ledgerDocumentId,
    })
    return true;
  }

  public getQueueUrl(queueName: string) {
    return this.client
      .getQueueUrl({ QueueName: queueName })
      .then((result) => result.QueueUrl);
  }

  public receiveMessages(paramsReceiveMessage: ReceiveMessageRequest) {
    return this.client.receiveMessage(paramsReceiveMessage);
  }

  public deleteMessage(params: DeleteMessageRequest) {
    return this.client.deleteMessage(params);
  }
}

export default AwsQldbWorker;
