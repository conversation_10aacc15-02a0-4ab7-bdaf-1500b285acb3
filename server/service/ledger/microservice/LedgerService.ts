import axios from "axios";
import config from "../../../config";
import { UniversalTrackerValueRepository } from "../../../repository/UniversalTrackerValueRepository";

interface LedgerStatus {
  pubSubService: { [key: string]: string },
  ledgerService: { [key: string]: string },
}


interface HistoryStatus {
  id: string;
  verified: boolean;
  date: string;
  error?: string;
}

export interface LedgerServiceValidatorResponse {
  ledger: {
    verified: boolean;
    error?: string;
  };
  ethereum: {
    verified: boolean;
    error?: string;
  };
  utrv: {
    id: string;
    verified: boolean;
    history: HistoryStatus[];
    error?: string;
  }
}

export class LedgerService {
  private host = config.ledgerMicroservice.host;
  private port = config.ledgerMicroservice.port;
  private baseUrl = `http://${this.host}:${this.port}`;

  isEnabled = () => !!this.host;

  async verifyUTRVHistory(utrvId: string): Promise<LedgerServiceValidatorResponse> {
    const utrv = await UniversalTrackerValueRepository.findById(utrvId);
    if (!utrv) {
      throw new Error(`UTRV with id ${utrvId} not found`);
    }
    const history = utrv.history;
    const body = {
      history
    }
    const url = `${this.baseUrl}/validate/utrv/${utrvId}`;
    const result = await axios.post<{ results: LedgerServiceValidatorResponse }>(url,
      body,
      {
        timeout: 5000
      }
    );

    return result.data.results;
  }


  async getStatus(): Promise<{ [key: string]: string }> {
    if (!this.host) {
      return {
        status: 'No host defined'
      };
    }

    const url = `${this.baseUrl}/checks`;
    const result = await axios.get<LedgerStatus>(url, {
      timeout: 5000
    });

    if (!result.data) {
      return {
        url,
        status: 'Not Connected'
      };
    }

    const status: { [key: string]: string } = {};
    const ledgerStatus = result.data;
    Object.entries(ledgerStatus).forEach(([key, values]) => {
      Object.entries(values as { [key: string]: string }).forEach(([prop, value]) => {
        status[`${key}.${prop}`] = value;
      });
    });

    return status;
  }
}

let instance: LedgerService;
export const getLedgerService = () => {
  if (!instance) {
    instance = new LedgerService();
  }
  return instance;
}
