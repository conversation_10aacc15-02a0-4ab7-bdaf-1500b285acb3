/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import config from '../../config';
import AwsQldbWorker from './AwsQldbWorker';
import LedgerUniversalTrackerValue from '../../models/ledgerUniversalTrackerValue';
import { UniversalTrackerValueRepository } from '../../repository/UniversalTrackerValueRepository';
import QueueCommand from '../queue/QueueCommand';
import { getQueueClient } from '../queue/QueueClient';

export const createQldbCommand = () => {
  const awsQldbWorker = new AwsQldbWorker(
    getQueueClient(),
    LedgerUniversalTrackerValue,
    UniversalTrackerValueRepository,
  );

  return new QueueCommand(awsQldbWorker, config.queue.ledger.queueName, 10);
};
