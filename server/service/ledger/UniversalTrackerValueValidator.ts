/*
* Copyright (c) 2021. World Wide Generation Ltd
*/

import { GetBlockResponse } from '@aws-sdk/client-qldb';
import { LedgerUniversalTrackerValueRepository } from "../../repository/LedgerUniversalTrackerValueRepository";
import { UniversalTrackerValueRepository } from "../../repository/UniversalTrackerValueRepository";
import { ValueHistory } from "../../models/universalTrackerValue";
import { wwgLogger } from "../wwgLogger";
import { Ethereum, EthereumTransaction } from "./Ethereum";
import { Qldb, QLDBDigest, QLDBHistory, QLDBValueHistory } from "./Qldb";
import { dom } from "ion-js";

interface UTRVValidatorResponse {
  errorDescription: string;
  ethereum: {
    valid: boolean;
    transaction?: EthereumTransaction
    digest?: QLDBDigest,
    errorDescription?: string;
  },
  qldb: {
    valid: boolean;
    blockResponse?: GetBlockResponse,
    errorDescription?: string;
  },
  proof: {
    valid: boolean;
    errorDescription?: string;
  },
  utrv: {
    qldbDocumentId: string;
    latest: {
      valid: boolean;
      qldb?: QLDBValueHistory;
      g17?: ValueHistory;
    };
    history: {
      valid: boolean;
      qldb?: QLDBValueHistory[];
      g17?: ValueHistory[];
    }
  };
}

export class UniversaleTrackerValueValidator {
  response: UTRVValidatorResponse = {
    errorDescription: '',
    ethereum: {
      valid: false,
    },
    qldb: {
      valid: false,
    },
    proof: {
      valid: false,
    },
    utrv: {
      qldbDocumentId: '',
      latest: {
        valid: false
      },
      history: {
        valid: false,
      }
    }
  };

  constructor(
    private qldbService: Qldb,
    private ethereumService: Ethereum,
    private logger = wwgLogger,
  ) { }

  public async getStatus(
    utrvId: string): Promise<UTRVValidatorResponse> {

    try {
      await this.validateLedgerStatus();
    } catch (e) {
      this.response.errorDescription = e;
      this.logger.error(`Error trying to validate ledger status: ${e}`);
      return this.response;
    }

    try {
      await this.validateUtrvHistory(utrvId);
    } catch (e) {
      this.response.errorDescription = e;
      this.logger.error(`Error trying to validate utrv status: ${e}`);
    }

    return this.response;
  }

  // Get History for UTRV
  // Get Latest History for UTRV
  // Get the LedgerUTV foreign ID for QLDB
  // Get QLDB History for the LedgerUTV
  // Get all IDs for the history for both G17 and QLDB
  // Compare the IDs

  private async validateUtrvHistory(utrvId: string) {
    const utrv = await UniversalTrackerValueRepository.mustFindById(utrvId, { history: 1 });
    const utrvObj = utrv.toObject();

    this.response.utrv.history.g17 = utrvObj.history;
    this.response.utrv.latest.g17 = utrvObj.history[utrv.history.length - 1];

    const latestHistoryId = String(this.response.utrv.latest.g17?._id);

    const utrvHistory = await LedgerUniversalTrackerValueRepository.mustFindByUtrvId(utrvId);
    const qldbDocumentId = utrvHistory.documentId; // '7j4KVNA6EnW89eeEbwVCHs';
    this.response.utrv.qldbDocumentId = qldbDocumentId;

    const qldbUtrvHistory = await this.qldbService.getHistoryByDocumentId(qldbDocumentId);

    this.response.utrv.history.qldb = qldbUtrvHistory.map(q => this.convert(q)).map(q => q.data);
    this.response.utrv.latest.qldb = (this.response.utrv.history.qldb.find(q => String(q._id) === String(latestHistoryId)));

    const qldbIds = this.response.utrv.history.qldb.map(d => String(d._id));
    const g17Ids = this.response.utrv.history.g17?.map(d => String(d._id)) ?? [];

    // @TODO - This is a very basic check, especially since at this time there will be a disconnect between datasets
    // In the future we can ensure the IDs are also the latest ones, or based on UTRV created date, then ensure absolute match
    this.response.utrv.history.valid = qldbIds.every(qldbId => g17Ids.includes(qldbId));
    this.response.utrv.latest.valid = this.compare(this.response.utrv.latest.g17, this.response.utrv.latest.qldb);
  }

  private convert(obj: dom.Value): QLDBHistory {
    return JSON.parse(JSON.stringify(obj)); // Don't know how else to do it
  }

  private compare(h1?: ValueHistory, h2?: QLDBValueHistory) {
    if (!h1 || !h2) {
      return false;
    }

    const strippedH2 = { ...h2 };
    delete strippedH2.utrvId; // Should be the only difference as this gets added during QLDB insert by Lambda function
    const sortKeys = Object.keys(h1).sort(); // Hopefully nested objects aren't an issue, as this won't work then
    return JSON.stringify(h1, sortKeys) === JSON.stringify(strippedH2, sortKeys);
  }

  private async validateLedgerStatus() {

    let ethereumQLDBDigest;

    // Fetch latest digest from Ethereum
    try {
      const ethereumTransaction = await this.ethereumService.getLatestTransaction();
      ethereumQLDBDigest = this.ethereumService.getDigest(ethereumTransaction);
      this.response.ethereum.valid = true;
      this.response.ethereum.transaction = ethereumTransaction;
      this.response.ethereum.digest = ethereumQLDBDigest;
      this.response.ethereum.errorDescription = undefined;
    } catch (e) {
      this.logger.error(e);
      throw Error('Could not fetch digest from Ethereum');
    }

    let blockResponse;
    if (ethereumQLDBDigest) {
      try {
        // Check that the ethereum Digest exists on QLDB, which at least validates the block exists, but doesn't get the digest.
        blockResponse = await this.qldbService.validateBlock(ethereumQLDBDigest);
        this.response.qldb.valid = true;
        this.response.qldb.blockResponse = blockResponse;
        this.response.qldb.errorDescription = undefined;
      } catch (e) {
        this.logger.error(e);
        throw Error('Could not validate Ethereum digest block reference on QLDB');
      }

      if (blockResponse) {
        // validateLedgerStatus
        // @TODO
        // THIS DOES NOT SEEM TO WORK YET, NEEDS INVESTIGATION
        // ASSUME THAT JUST THE BLOCK EXISTING IS GOOD ENOUGH FOR NOW
        try {
          this.response.proof.errorDescription = 'QLDB digest does not match Ethereum digest for block';
          // Check digest from Ethereum with digest created using block on QLDB.
          const validateDigest = this.qldbService.validateDigest(ethereumQLDBDigest, blockResponse);
          this.response.proof.valid = validateDigest.verified;
          this.response.proof.errorDescription = undefined;
        } catch (e) {
          // this.logger.error(e); //This will fail always, so don't log it for now
          throw Error('QLDB digest does not match Ethereum digest for block');
        }
      }
    }
  }
}
