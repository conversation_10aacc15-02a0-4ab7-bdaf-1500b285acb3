/*
* Copyright (c) 2021. World Wide Generation Ltd
*/

import axios from "axios";
import config from "../../config";
import { wwgLogger } from "../wwgLogger";
import { QLDBDigest } from './Qldb';

export interface EthereumTransaction {
  blockNumber: string; //"10256415",
  timeStamp: string; //"1621310314",
  hash: string; //"0x3743831062b2b285b3662373b0a17476bce731687a39d5e9c37d14fa865b868c",
  nonce: string; //"118",
  blockHash: string; //"0x37d97e7575143a8502fcd6ff31c4284a3371d41f76ecc237bb14b1e2a146078d",
  transactionIndex: string; //"78";
  from: string; //"******************************************",
  to: string; //"******************************************";
  value: string; //"0";
  gas: string; //"48500";
  gasPrice: string; //"1020674028";
  isError: string; //"0";
  txreceipt_status: string; //"1";
  input: string; //"0x3d7403a3000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000877b22646967657374223a22696d2b6d45553535614c4f6b7479654a4b42663163615249653078386b654a4f55626f594d5861756546733d222c2264696765737454697041646472657373223a227b737472616e6449643a5c22396341727275484a3971654334466a514372464e6e455c222c73657175656e63654e6f3a313931323135337d227d00000000000000000000000000000000000000000000000000";
  contractAddress: string; //"";
  cumulativeGasUsed: string; //"********";
  gasUsed: string; //"48500";
  confirmations: string; //"1745"
}

export class Ethereum {
  CONTRACT_ADDRESS = config.ethereum.options.contractAddress;
  DOMAIN = config.ethereum.options.domain;

  constructor(
    private logger = wwgLogger,
  ) {
  }

  public async getLatestTransaction(): Promise<EthereumTransaction> {
    const ethereumUrl = `${this.DOMAIN}/api?module=account&action=txlist&address=${this.CONTRACT_ADDRESS}&sort=desc`;
    const ethereumTransactionList = await axios.get(ethereumUrl);
    return ethereumTransactionList.data.result[0];
  }

  public getDigest(latestTransaction: EthereumTransaction): QLDBDigest | undefined {
    if (!latestTransaction.input) {
      return;
    }

    const input = this.hexToAscii(latestTransaction.input);
    const json = JSON.parse(input);

    const cleanUpDigestTip = json.digestTipAddress
      .replace('strandId', '"strandId"')
      .replace('sequenceNo', '"sequenceNo"')

    return {
      digest: json.digest,
      digestTipAddress: JSON.parse(cleanUpDigestTip)
    }
  }

  private hexToAscii(hex: string) {
    let str = "";

    // Strip method name
    hex = hex.slice(10);

    let i = 0, l = hex.length;
    for (; i < l; i += 2) {
      const code = parseInt(hex.substr(i, 2), 16);
      str += String.fromCharCode(code);
    }

    // Remove non-printable characters
    return str.replace(/[^\x20-\x7E]/g, '');
  }
}
