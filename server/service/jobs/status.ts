/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { JobStatus, TaskStatus } from '../../models/backgroundJob';


export const finalStatuses = [
  JobStatus.Completed,
  JobStatus.Error,
  JobStatus.Deleted,
]
export const isJobFinished = (status: JobStatus) => finalStatuses.includes(status)


const taskCompleteStatuses = [
  TaskStatus.Completed,
  TaskStatus.Error,
]

export const isTaskComplete = (status: TaskStatus) => taskCompleteStatuses.includes(status);
