/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { LEVEL } from '../event/Events';
import { LogMessage } from '../../models/backgroundJob';

export const createLog = (message: string, extra: Partial<Omit<LogMessage, 'message'>> = {}): LogMessage => {

  const { severity, created, ...metadata } = extra;

  return {
    severity: severity ?? LEVEL.INFO,
    message,
    created: created ?? new Date(),
    metadata,
  }
}


/** Log entry with metadata **/
export const createLogEntry = (message: string, extra: Partial<Omit<LogMessage, 'message'>> = {}): LogMessage => {

  const { severity, created, metadata } = extra;

  return {
    severity: severity ?? LEVEL.INFO,
    message,
    created: created ?? new Date(),
    metadata,
  }
}
