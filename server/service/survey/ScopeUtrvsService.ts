import { ObjectId } from 'bson';
import { filterByScope } from './scope/filterScope';
import { MetricGroupRepository } from '../../repository/MetricGroupRepository';
import { BlueprintContributions } from './BlueprintContribution';
import { ScopeUtrv } from '../../util/scope-utrv';
import { type Scope } from '../../models/common/scope';

/**
 * To get the list of utrvs in scope for a survey
 */
export class ScopeUtrvsService {
  constructor(private metricGroupRepository: typeof MetricGroupRepository) {}

  async filterUtrvsByScope({
    utrvs,
    contribution,
    scope,
  }: {
    utrvs: ScopeUtrv[];
    contribution: BlueprintContributions;
    scope: Partial<Scope<ObjectId | string>>;
  }): Promise<ScopeUtrv[]> {
    const utrs = utrvs.map((utrv) => utrv.universalTracker);
    const filteredUtrs = filterByScope({
      utrs,
      scope,
      contribution,
      customUtrIds: scope.custom ? await this.metricGroupRepository.getUtrIds(scope.custom) : [],
    });
    const filteredUtrIds = new Set(filteredUtrs.map((utr) => utr._id.toString()));

    return utrvs.filter((utrv) => filteredUtrIds.has(utrv.universalTrackerId.toString()));
  }
}

let instance: ScopeUtrvsService;
export const getScopeUtrvsService = () => {
  if (!instance) {
    instance = new ScopeUtrvsService(MetricGroupRepository);
  }
  return instance;
};
