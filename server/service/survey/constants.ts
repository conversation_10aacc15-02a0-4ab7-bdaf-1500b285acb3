interface RequiredMetricMappingItem {
  targetColumnCode: string;
  sourceUtrCode: string;
  altSourceUtrCode?: string;
}

interface MetricMappingItem extends RequiredMetricMappingItem {
  value?: string;
}

export interface MetricsMapping {
  [targetUtrCode: string]: {
    requiredColumns: RequiredMetricMappingItem[];
    columns?: MetricMappingItem[];
  }[];
}

export const EMISSION_METRICS_MAPPING: MetricsMapping = {
  'sgx-custom-56': [
    {
      requiredColumns: [
        { targetColumnCode: 'sgx56-rev', sourceUtrCode: 'organizational-metric/revenue' },
        { targetColumnCode: 'sgx56-total-emissions', sourceUtrCode: 'sgx-core-1a' },
      ],
      columns: [
        {
          targetColumnCode: 'sgx56-denominator',
          value: 'sgx56-denominator1', // populate 'Revenue' option of value-list
          sourceUtrCode: 'organizational-metric/revenue',
        },
      ],
    },
    {
      requiredColumns: [
        { targetColumnCode: 'sgx56-headcount', sourceUtrCode: 'organizational-metric/headcount' },
        { targetColumnCode: 'sgx56-total-emissions', sourceUtrCode: 'sgx-core-1a' },
      ],
      columns: [
        {
          targetColumnCode: 'sgx56-denominator',
          value: 'sgx56-denominator2', // populate 'Headcount' option of value-list
          sourceUtrCode: 'organizational-metric/headcount',
        },
      ],
    },
    {
      requiredColumns: [
        { targetColumnCode: 'sgx56-floorarea', sourceUtrCode: 'organizational-metric/floorarea' },
        { targetColumnCode: 'sgx56-total-emissions', sourceUtrCode: 'sgx-core-1a' },
      ],
      columns: [
        {
          targetColumnCode: 'sgx56-denominator',
          value: 'sgx56-denominator3', // populate 'Floor Area' option of value-list
          sourceUtrCode: 'organizational-metric/floorarea',
        },
      ],
    },
    {
      requiredColumns: [
        { targetColumnCode: 'sgx56-other-org-metric-amount', sourceUtrCode: 'organizational-metric/other' },
        { targetColumnCode: 'sgx56-total-emissions', sourceUtrCode: 'sgx-core-1a' },
      ],
      columns: [
        {
          targetColumnCode: 'sgx56-denominator',
          value: 'sgx56-denominator4', // populate 'Other' option of value-list
          sourceUtrCode: 'organizational-metric/other',
        },
      ],
    },
  ],
  'sgx-custom-57': [
    {
      requiredColumns: [
        { targetColumnCode: 'sgx57-rev', sourceUtrCode: 'organizational-metric/revenue' },
        { targetColumnCode: 'sgx57-scope1-emissions', sourceUtrCode: 'gri/2020/305-1/a' },
      ],
      columns: [
        {
          targetColumnCode: 'sgx57-denominator',
          value: 'sgx56-denominator1', // populate 'Revenue' option of value-list
          sourceUtrCode: 'organizational-metric/revenue',
        },
      ],
    },
    {
      requiredColumns: [
        { targetColumnCode: 'sgx57-headcount', sourceUtrCode: 'organizational-metric/headcount' },
        { targetColumnCode: 'sgx57-scope1-emissions', sourceUtrCode: 'gri/2020/305-1/a' },
      ],
      columns: [
        {
          targetColumnCode: 'sgx57-denominator',
          value: 'sgx56-denominator2', // populate 'Headcount' option of value-list
          sourceUtrCode: 'organizational-metric/headcount',
        },
      ],
    },
    {
      requiredColumns: [
        { targetColumnCode: 'sgx57-floorarea', sourceUtrCode: 'organizational-metric/floorarea' },
        { targetColumnCode: 'sgx57-scope1-emissions', sourceUtrCode: 'gri/2020/305-1/a' },
      ],
      columns: [
        {
          targetColumnCode: 'sgx57-denominator',
          value: 'sgx56-denominator3', // populate 'Floor Area' option of value-list
          sourceUtrCode: 'organizational-metric/floorarea',
        },
      ],
    },
    {
      requiredColumns: [
        { targetColumnCode: 'sgx57-other-org-metric-amount', sourceUtrCode: 'organizational-metric/other' },
        { targetColumnCode: 'sgx57-scope1-emissions', sourceUtrCode: 'gri/2020/305-1/a' },
      ],
      columns: [
        {
          targetColumnCode: 'sgx57-denominator',
          value: 'sgx56-denominator4', // populate 'Other' option of value-list
          sourceUtrCode: 'organizational-metric/other',
        },
      ],
    },
  ],
  'sgx-custom-58': [
    {
      requiredColumns: [
        { targetColumnCode: 'sgx58-revenue', sourceUtrCode: 'organizational-metric/revenue' },
        {
          targetColumnCode: 'sgx58-scope2-emissions',
          sourceUtrCode: 'gri/2020/305-2/b',
          altSourceUtrCode: 'gri/2020/305-2/a',
        },
      ],
      columns: [
        {
          targetColumnCode: 'sgx58-denominator',
          value: 'sgx56-denominator1', // populate 'Revenue' option of value-list
          sourceUtrCode: 'organizational-metric/revenue',
        },
      ],
    },
    {
      requiredColumns: [
        { targetColumnCode: 'sgx58-headcount', sourceUtrCode: 'organizational-metric/headcount' },
        {
          targetColumnCode: 'sgx58-scope2-emissions',
          sourceUtrCode: 'gri/2020/305-2/b',
          altSourceUtrCode: 'gri/2020/305-2/a',
        },
      ],
      columns: [
        {
          targetColumnCode: 'sgx58-denominator',
          value: 'sgx56-denominator2', // populate 'Headcount' option of value-list
          sourceUtrCode: 'organizational-metric/headcount',
        },
      ],
    },
    {
      requiredColumns: [
        { targetColumnCode: 'sgx58-floorarea', sourceUtrCode: 'organizational-metric/floorarea' },
        {
          targetColumnCode: 'sgx58-scope2-emissions',
          sourceUtrCode: 'gri/2020/305-2/b',
          altSourceUtrCode: 'gri/2020/305-2/a',
        },
      ],
      columns: [
        {
          targetColumnCode: 'sgx58-denominator',
          value: 'sgx56-denominator3', // populate 'Floor Area' option of value-list
          sourceUtrCode: 'organizational-metric/floorarea',
        },
      ],
    },
    {
      requiredColumns: [
        { targetColumnCode: 'sgx58-other-org-metric-amount', sourceUtrCode: 'organizational-metric/other' },
        {
          targetColumnCode: 'sgx58-scope2-emissions',
          sourceUtrCode: 'gri/2020/305-2/b',
          altSourceUtrCode: 'gri/2020/305-2/a',
        },
      ],
      columns: [
        {
          targetColumnCode: 'sgx58-denominator',
          value: 'sgx56-denominator4', // populate 'Other' option of value-list
          sourceUtrCode: 'organizational-metric/other',
        },
      ],
    },
  ],
  'sgx-custom-60': [
    {
      requiredColumns: [
        { targetColumnCode: 'sgx60-revenue', sourceUtrCode: 'organizational-metric/revenue' },
        { targetColumnCode: 'sgx60-scope3-emissions', sourceUtrCode: 'gri/2020/305-3/a' },
      ],
      columns: [
        {
          targetColumnCode: 'sgx60-denominator',
          value: 'sgx56-denominator1', // populate 'Revenue' option of value-list
          sourceUtrCode: 'organizational-metric/revenue',
        },
      ],
    },
    {
      requiredColumns: [
        { targetColumnCode: 'sgx60-headcount', sourceUtrCode: 'organizational-metric/headcount' },
        { targetColumnCode: 'sgx60-scope3-emissions', sourceUtrCode: 'gri/2020/305-3/a' },
      ],
      columns: [
        {
          targetColumnCode: 'sgx60-denominator',
          value: 'sgx56-denominator2', // populate 'Headcount' option of value-list
          sourceUtrCode: 'organizational-metric/headcount',
        },
      ],
    },
    {
      requiredColumns: [
        { targetColumnCode: 'sgx60-floorarea', sourceUtrCode: 'organizational-metric/floorarea' },
        { targetColumnCode: 'sgx60-scope3-emissions', sourceUtrCode: 'gri/2020/305-3/a' },
      ],
      columns: [
        {
          targetColumnCode: 'sgx60-denominator',
          value: 'sgx56-denominator3', // populate 'Floor Area' option of value-list
          sourceUtrCode: 'organizational-metric/floorarea',
        },
      ],
    },
    {
      requiredColumns: [
        { targetColumnCode: 'sgx60-other-org-metric-amount', sourceUtrCode: 'organizational-metric/other' },
        { targetColumnCode: 'sgx60-scope3-emissions', sourceUtrCode: 'gri/2020/305-3/a' },
      ],
      columns: [
        {
          targetColumnCode: 'sgx60-denominator',
          value: 'sgx56-denominator4', // populate 'Other' option of value-list
          sourceUtrCode: 'organizational-metric/other',
        },
      ],
    },
  ],
};

export const SURVEY_AGGREGATOR_VERSION: number = 20250301 // YYYYMMDD