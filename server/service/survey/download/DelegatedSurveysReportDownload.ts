import { ObjectId } from 'bson';
import { SurveyRepository } from '../../../repository/SurveyRepository';
import { SurveyPermissions } from '../SurveyPermissions';
import { SurveyModelPlain } from '../../../models/survey';
import User, { UserModel } from '../../../models/user';
import { getSimpleReportGenerator, SimpleReportGenerator } from '../../custom-report/SimpleReportGenerator';
import { LoggerInterface, wwgLogger } from '../../wwgLogger';
import { DownloadMultiScope } from '../scope/downloadScope';
import { PipelineStage } from 'mongoose';
import { ReportDataGeneratorResult } from '../../custom-report/type';
import { FileParserType } from '../transfer/parserTypes';
import { getSurveyExcel, SurveyExcel } from '../transfer/SurveyExcel';
import { write } from '@sheet/core';
import { stringifyArrayCsvFile } from '../../file/writer/CsvFileWriter';
import { InitiativePermissions } from '../../initiative/InitiativePermissions';
import { DELEGATED_SURVEYS_COLUMNS } from '../../custom-report/columnUtils';

export class DelegatedSurveysReportDownload {
  constructor(
    private logger: LoggerInterface,
    private reportGenerator: SimpleReportGenerator,
    private surveyExcel: SurveyExcel
  ) {}

  private async getSurveyFiltersMap({
    surveys,
    user,
    initiativeId,
  }: {
    surveys: SurveyModelPlain[];
    user: UserModel;
    initiativeId: ObjectId;
  }) {
    const canAccessData = await InitiativePermissions.canAccessAllSurveyData(user, initiativeId);
    if (canAccessData) {
      return new Map(surveys.map((survey) => [survey._id.toString(), []]));
    }

    const delegatedUtrvFilters = [
      {
        $match: {
          $or: [{ 'utrvs.stakeholders.stakeholder': user._id }, { 'utrvs.stakeholders.verifier': user._id }],
        },
      },
    ];
    const filterMap = new Map<string, PipelineStage[]>();

    for (const survey of surveys) {
      /** TODO: [GU-6024] Should handle case where user is delegated to a workgroup that has access to the survey,
       * optimize for an array of surveys */
      const hasAccess = SurveyPermissions.hasSurveyLevelDataAccess(survey, user);
      if (hasAccess) {
        filterMap.set(survey._id.toString(), []);
      } else {
        filterMap.set(survey._id.toString(), delegatedUtrvFilters);
      }
    }

    return filterMap;
  }

  public async getDownloadData({
    initiativeId,
    userIds,
    surveyIds,
    downloadScope,
  }: {
    initiativeId: ObjectId;
    userIds: ObjectId[];
    surveyIds: ObjectId[];
    downloadScope: DownloadMultiScope;
  }) {
    this.logger.info('Getting download data for delegated surveys', {
      initiativeId: initiativeId.toString(),
      userIds: userIds.map((id) => id.toString()),
      surveyIds: surveyIds.map((id) => id.toString()),
      downloadScope,
    });

    const surveys = await SurveyRepository.findSurveys({ _id: { $in: surveyIds }, initiativeId });
    /** Currently support only one user */
    const user = await User.findOne({ _id: userIds[0] }).orFail().exec();
    const surveyFiltersMap = await this.getSurveyFiltersMap({ surveys, user, initiativeId });
    return this.reportGenerator.getDownloadData({
      surveys,
      downloadScope,
      initiativeId,
      surveyFiltersMap,
      columns: DELEGATED_SURVEYS_COLUMNS.map((code) => ({ code })),
    });
  }

  public async getFileContent({
    exportType,
    downloadData: { headers, records },
  }: {
    exportType: FileParserType.Csv | FileParserType.Xlsx;
    downloadData: ReportDataGeneratorResult;
  }) {
    if (exportType === FileParserType.Xlsx) {
      const result = await this.surveyExcel.createSimpleReportSheet({
        headers,
        sheets: [{ name: 'Data Report', data: [headers ?? [], ...records] }],
        isBoldHeader: true,
      });
      return write(result, { type: 'buffer', bookType: exportType, cellStyles: true });
    }
    return stringifyArrayCsvFile({ records: this.surveyExcel.getPlainSheetData(records), header: headers });
  }
}

let instance: DelegatedSurveysReportDownload;
export const getDelegatedSurveysReportDownload = () => {
  if (!instance) {
    instance = new DelegatedSurveysReportDownload(wwgLogger, getSimpleReportGenerator(), getSurveyExcel());
  }
  return instance;
};
