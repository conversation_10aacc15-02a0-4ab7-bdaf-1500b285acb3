import { UserModel } from "../../models/user";

export interface MigrationResult {
  success: boolean;
  message: string;
  results: object;
}

export type AddTaskLog = (message: string, config?: { lossy?: boolean }) => Promise<boolean>;
interface MigrationAction {
  code: string;
  label: string;
}

export interface Migration {
  code: string;
  description: string;

  getActions(): {
    up: MigrationAction[];
    down: MigrationAction[];
    dryRun: MigrationAction[];
  }

  dryRun(action: string, addTaskLog: AddTaskLog, user: UserModel): Promise<MigrationResult>;
  up(action: string, addTaskLog: AddTaskLog, user: UserModel): Promise<MigrationResult>;
  down(action: string, addTaskLog: AddTaskLog, user: UserModel): Promise<MigrationResult>;
}
