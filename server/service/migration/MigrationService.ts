/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { UserModel } from '../../models/user';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import { OFI20240925_Migration } from './migrations/20240925-ofi';
import { AddTaskLog, Migration, MigrationResult } from './types';

export class MigrationService {
  constructor(
    private logger: LoggerInterface,
    private migrations: Migration[]
  ) {}

  getList() {
    return this.migrations.map(m => ({
      code: m.code,
      description: m.description,
      actions: m.getActions()
    }));
  }

  private getMigration(migrationCode: string) {
    const migration = this.migrations.find(m => m.code === migrationCode);
    if (!migration) {
      throw new Error(`Migration ${migrationCode} not found`);
    }
    return migration;
  }

  dryRun = (migrationCode: string, migrationAction: string, addTaskLog: AddTaskLog, user: UserModel): Promise<MigrationResult> =>
    this.getMigration(migrationCode).dryRun(migrationAction, addTaskLog, user);

  up = (migrationCode: string, migrationAction: string, addTaskLog: AddTaskLog, user: UserModel): Promise<MigrationResult> =>
    this.getMigration(migrationCode).up(migrationAction, addTaskLog, user);

  down = (migrationCode: string, migrationAction: string, addTaskLog: AddTaskLog, user: UserModel): Promise<MigrationResult> =>
    this.getMigration(migrationCode).down(migrationAction, addTaskLog, user);

}

let instance: MigrationService;
export const getMigrationService = () => {
  if (!instance) {
    instance = new MigrationService(
      wwgLogger,
      [
        new OFI20240925_Migration(wwgLogger)
      ]
    );
  }
  return instance;
};
