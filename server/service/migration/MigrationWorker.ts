/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { BackgroundBaseWorker } from "../background-process/BackgroundBaseWorker";
import { wwgLogger } from "../wwgLogger";
import { getMigrationWorkflow, MigrationWorkflow, SupportedJobModel } from "./MigrationWorkflow";

/**
 * Workers are always triggered in Google Cloud Job runs environments
 * allow as to do heavy work without affecting normal API server operations.
 */
export class MigrationWorker extends BackgroundBaseWorker<SupportedJobModel, MigrationWorkflow> {
}

let instance: MigrationWorker;
export const getMigrationWorker = () => {
  if (!instance) {
    instance = new MigrationWorker(wwgLogger, getMigrationWorkflow());
  }
  return instance;
};
