// @ts-nocheck ignore this file for now
import { AddTaskLog, Migration, MigrationResult } from "../types";
import { RowData, TableData } from "../../../models/public/universalTrackerValueType";
import UniversalTrackerValue, { UniversalTrackerValueModel } from "../../../models/universalTrackerValue";
import ContextError from "../../../error/ContextError";
import UniversalTracker, { UniversalTrackerModel, UniversalTrackerPlain, UtrConditionVisibility } from "../../../models/universalTracker";
import UniversalTrackerActionManager from "../../utr/UniversalTrackerActionManager";
import { ActionList } from "../../utr/constants";
import { LoggerInterface } from "../../wwgLogger";
import MetricGroup from "../../../models/metricGroup";
import Survey, { isAggregatedSurvey } from "../../../models/survey";
import { getSurveyManager } from "../../survey/SurveyManager";
import { UserModel } from "../../../models/user";
import { InitiativeRepository } from "../../../repository/InitiativeRepository";
import { getSurveyAggregator } from "../../survey/SurveyAggregator";

/*
NOTES
 - If you add the new pack to survey, it creates the UTRVs, so we shouldn't need to clone.
   It also means that the 'down' will delete them and orphan the IDs on survey. Removing/Readding pack will fix this.
 - In CM-1, units are appearing in undefined columns. This should be a frontend fix.
*/

interface DryRunAction {
  fromUtrvId: string;
  toUtrvId: string;
  utrvActions: {
    action: 'update' | 'notReported' | 'reject' | 'verify',
  }[]
};

interface MigrationResults {
  totalUtrs: number;
  totalToUtrvs: number;
  totalFromUtrvs: number;
  skipped: number;
  updated: number;
  rejected: number;
  verified: number;
  actions: {
    utrCode: string;
    utrActions: DryRunAction[];
  }[];
}

enum DownAction {
  DeleteNewUTRVs = 'delete-new-utrvs',
  AddOldUtrvsToGroups = 'add-old-utrvs-to-groups',
  LockUTRs = 'lock-new-utrs',
  UnlockOldUTRs = 'unlock-old-utrs',
  UnlockNewUTRs = 'unlock-new-utrs',
}

enum UpAction {
  CloneAndMigrate = 'clone-and-migrate',
  LockUTRs = 'lock-utrs',
  UnlockUTRs = 'unlock-utrs',
  RemoveOldUtrsFromGroups = 'remove-utrs-from-groups',
}

export class OFI20240925_Migration implements Migration {
  public code = 'OFI20240925_Migration';
  public description = 'Migrate data for OFI CM-1 Custom Metrics CM-1 UTRVs to new data model';

  constructor(
    private logger: LoggerInterface
  ) {}

  public getActions() {
    return {
      up: [
        {
          code: UpAction.LockUTRs,
          label: 'Stage 1: Lock All UTRs'
        },
        {
          code:  UpAction.CloneAndMigrate,
          label: 'Stage 2: Clone and Migrate UTRs'
        },
        {
          code: UpAction.UnlockUTRs,
          label: 'Stage 3: Unlock New UTRs',
        },
        {
          code: UpAction.RemoveOldUtrsFromGroups,
          label: 'Stage 4: Remove Old UTRs from Metric Groups',
        }
      ],
      down: [
        {
          code: DownAction.DeleteNewUTRVs,
          label: 'UTRV Cleanup: Delete newly created UTRVs'
        },
        {
          code: DownAction.AddOldUtrvsToGroups,
          label: 'Re-add Old UTRs to Metric Groups'
        },
        {
          code: DownAction.LockUTRs,
          label: 'Lock OLD and NEW UTRs'
        },
        {
          code: DownAction.UnlockOldUTRs,
          label: 'Unlock OLD UTRs'
        },
        {
          code: DownAction.UnlockNewUTRs,
          label: 'Unlock NEW UTRs'
        },
      ],
      dryRun: [],
    }
  }

  private async getUtr(utrCode: string): Promise<UniversalTrackerModel> {
    const utr = await UniversalTracker.findOne({ code: utrCode }, { _id: 1, code: 1, valueType: 1 }).exec();
    if (!utr) {
      throw new ContextError('TODO - UTR not found');
    }
    return utr;
  }

  private async getUtrvs(utr: Pick<UniversalTrackerPlain, 'code' | '_id'>) {
    const find = UniversalTrackerValue.find({ universalTrackerId: utr._id, deletedDate: { $exists: false } });
    return find.exec();
  }

  private async getLeanUtrvs(utr: Pick<UniversalTrackerPlain, 'code' | '_id'>) {
    const find = UniversalTrackerValue.find({ universalTrackerId: utr._id, deletedDate: { $exists: false } });
    return find.lean();
  }

  private transposeData(tableData: TableData, reducer: Reducer) {
    return tableData.reduce(reducer, <RowData[][]>[]);
  }

  private async migrateUtrvs(dryRun = true, addTaskLog: AddTaskLog) {
    await addTaskLog('Starting migration...');
    const migrationConfig = getMigrationConfig();

    const results: MigrationResults = {
      totalUtrs: migrationConfig.length,
      totalToUtrvs: 0,
      totalFromUtrvs: 0,
      skipped: 0,
      updated: 0,
      rejected: 0,
      verified: 0,
      actions: []
    };

    const totalConfigs = migrationConfig.length;
    let ix = 0;

    for (const config of migrationConfig) {
      const { from, to, reducer } = config;
      this.logger.info(`Migrating ${from} to ${to}`);

      ix++;

      const fromUtr = await this.getUtr(from);
      const fromUtrvs = await this.getLeanUtrvs(fromUtr);
      const toUtr = await this.getUtr(to);
      const toUtrvs = await this.getUtrvs(toUtr);

      results.totalToUtrvs += toUtrvs.length;
      results.totalFromUtrvs += fromUtrvs.length;

      const utrAction: MigrationResults['actions'][0] = {
        utrCode: from,
        utrActions: []
      };

      const totalUtrvs = fromUtrvs.length;
      let iix = 0;
      for (const fromUtrv of fromUtrvs) {
        iix++;
        if (iix % 100 === 0) {
          await addTaskLog(`Migrating UTRVs. Config ${ix}/${totalConfigs}. UTRV ${iix}/${totalUtrvs}`, { lossy: true });
        }

        const toUtrv = toUtrvs.find(toUtrv => {
          return toUtrv.compositeData?.surveyId && fromUtrv.compositeData?.surveyId &&
            toUtrv.compositeData.surveyId.equals(fromUtrv.compositeData.surveyId)
        });
        if (!toUtrv) {
          results.skipped++;
          continue;
        }

        const utrvActions: DryRunAction = {
          fromUtrvId: fromUtrv._id,
          toUtrvId: toUtrv._id,
          utrvActions: []
        };

        const reverseHistory = [...fromUtrv.history].reverse();
        const updateActions: string[] = [ActionList.Updated];

        const lastUpdateHistory = reverseHistory.find(h => updateActions.includes(h.action));
        if (lastUpdateHistory) {
          UniversalTrackerActionManager.hydrateUpdate({
            utrv: toUtrv,
            userId: lastUpdateHistory.userId,
            value: lastUpdateHistory.value,
            note: lastUpdateHistory.note,
            editorState: lastUpdateHistory.editorState,
            valueData: {
              ...lastUpdateHistory.valueData,
              table: lastUpdateHistory.valueData?.table ? this.transposeData(lastUpdateHistory.valueData.table, reducer) : undefined,
              input: {
                ...lastUpdateHistory.valueData?.input,
                table: lastUpdateHistory.valueData?.input?.table ? this.transposeData(lastUpdateHistory.valueData.input.table, reducer) : undefined
              },
            },
            location: lastUpdateHistory.location,
            numberScale: lastUpdateHistory.numberScale,
            unit : lastUpdateHistory.unit,
          });
          utrvActions.utrvActions.push({
            action: 'update',
          });
          const updateHistory = toUtrv.history[toUtrv.history.length - 1];
          updateHistory.evidence = lastUpdateHistory.evidence;
          updateHistory.evidenceData = lastUpdateHistory.evidenceData;
          results.updated++;
        }

        toUtrv.universalTracker = toUtr;
        const lastAction = reverseHistory[0];
        switch (lastAction.action) {
          case ActionList.Created:
          case ActionList.Updated:
          case ActionList.NotReported:
            // Nothing to do
            break;
          case ActionList.Rejected:
            UniversalTrackerActionManager.hydrateReject(toUtrv, lastAction.userId, lastAction.note, lastAction.location, lastAction.editorState);
            const newRejectHistory = toUtrv.history[toUtrv.history.length - 1];
            newRejectHistory.evidence = lastAction.evidence;
            newRejectHistory.evidenceData = lastAction.evidenceData;
            results.rejected++;
            utrvActions.utrvActions.push({
              action: 'reject',
            });
            break;
          case ActionList.Verified:
            UniversalTrackerActionManager.hydrateVerify(toUtrv, lastAction.userId, lastAction.note, lastAction.location, lastAction.editorState);
            const newVerifyHistory = toUtrv.history[toUtrv.history.length - 1];
            newVerifyHistory.evidence = lastAction.evidence;
            newVerifyHistory.evidenceData = lastAction.evidenceData;
            results.verified++;
            utrvActions.utrvActions.push({
              action: 'verify',
            });
            break;
        }

        toUtrv.evidence = fromUtrv.evidence;
        toUtrv.evidenceData = fromUtrv.evidenceData;
        utrAction.utrActions.push(utrvActions);
        if (!dryRun) {
          toUtrv.markModified('history');
          toUtrv.markModified('valueData');
          await toUtrv.save();
        }
      }
      results.actions.push(utrAction);
    }
    this.logger.info(`Finished migration`);

    return results;
  }

  private async deleteUtrvs() {
    const migrationConfig = getMigrationConfig();

    const results = {
      totalUtrs: migrationConfig.length,
      utrvsToBeDeleted: 0,
      utrvsDeleted: 0,
    }

    for (const config of migrationConfig) {
      const { to } = config;
      const toUtr = await this.getUtr(to);
      const toUtrvs = await this.getUtrvs(toUtr);

      results.utrvsToBeDeleted += toUtrvs.length;

      for (const toUtrv of toUtrvs) {
        // toUtrv.deletedDate = new Date();
        // await toUtrv.save();
        results.utrvsDeleted++;
        await toUtrv.deleteOne();
      }
    }

    return results;
  }

  private async cloneUtrvs(migrationConfig: OFIMigrationConfig[], addTaskLog: AddTaskLog) {

    await addTaskLog('Cloning Utrvs...');

    const results = {
      utrsToBeCloned: migrationConfig.length,
      utrvsToBeCloned: 0,
      utrvsToBeSkipped: 0,
      utrvsSkipped: 0,
      utrvsSuccessfullyCloned: 0,
    }

    const getUniqueKey = (utrv: Pick<UniversalTrackerValueModel, 'compositeData' | 'universalTrackerId'>) => {
      if (!utrv.compositeData?.surveyId) {
        throw new ContextError('TODO - This should not happen! UTRV without a SurveyID!');
      }
      return `${utrv.universalTrackerId}-${utrv.compositeData.surveyId ?? ''}`;
    }

    const totalConfigs = migrationConfig.length;
    let ix = 0;
    for (const config of migrationConfig) {
      const { from, to } = config;

      this.logger.info(`Cloning UTRVs for UTR ${config.from} to ${config.to}`);

      const toUtr = await this.getUtr(to);
      const toUtrvs = await this.getLeanUtrvs(toUtr);
      results.utrvsToBeSkipped += toUtrvs.length;

      const skipUtrvs = new Set(toUtrvs.map(getUniqueKey));

      const fromUtr = await this.getUtr(from);
      const fromUtrvs = await this.getLeanUtrvs(fromUtr);
      results.utrvsToBeCloned += fromUtrvs.length;

      const totalUtrvs = fromUtrvs.length;

      ix++;
      let iix = 0;
      for (const fromUtrv of fromUtrvs) {
        iix++;
        if (iix % 100 === 0) {
          await addTaskLog(`Cloning UTRVs. Config ${ix}/${totalConfigs}. UTRV ${iix}/${totalUtrvs}`, { lossy: true });
        }

        const toUtrv = { ...fromUtrv };
        delete toUtrv._id;
        toUtrv.universalTrackerId = toUtr._id;
        const uniqueKey = getUniqueKey(toUtrv);
        if (skipUtrvs.has(uniqueKey)) {
          results.utrvsSkipped++;
          continue;
        }
        const newUtrv = new UniversalTrackerValue(toUtrv);
        await newUtrv.save();
        results.utrvsSuccessfullyCloned++;
      }
    }
    return results;
  }

  private convertToTabular(migrationResults: MigrationResults): (string | object)[][] {
    const { actions, ...rest } = migrationResults;
    const results: (string | object)[][] = [[rest,'']];

    actions.forEach((result) => {
      results.push(['utrCode', result.utrCode]);
      result.utrActions.forEach((utrAction) => {
        results.push(['fromUtrvId', utrAction.fromUtrvId]);
        results.push(['toUtrvId', utrAction.toUtrvId]);
      });
      results.push(['','']); // Spacer
    });
    return results;
  }

  public async dryRun(action: string, addTaskLog: AddTaskLog): Promise<MigrationResult> {
    try {
      const results = await this.migrateUtrvs(true, addTaskLog);
      return { success: true, results, message: 'OFI20240925_Migration dry-run completed.' };
    }
    catch (e) {
      addTaskLog(`OFI20240925_Migration dry-run failed: ${e.message}`);
      this.logger.error(new ContextError('OFI20240925_Migration dry-run failed', { cause: e }));
      throw e;
    }
  }

  private async updateMetricGroups(migrationConfig: OFIMigrationConfig[], addTaskLog: AddTaskLog) {
    await addTaskLog('Updating Metric Groups...');
    const metricGroupIds = new Set<string>();

    const results = {
      utrs: migrationConfig.length,
      updatedUtrs: 0,
      skippedUtrs: 0,
      metricGroupIds: [] as string[],
    };


    let ix = 0;
    for (const migration of migrationConfig) {
      ix++;
      await addTaskLog(`Adding new UTRs to Metric Group ${ix}/${results.utrs}`, { lossy: true });

      let updated = 0;
      const fromUtr = await this.getUtr(migration.from);
      const fromUtrId = fromUtr._id;
      const toUtr = await this.getUtr(migration.to);
      const toUtrId = toUtr._id;

      const customMetricGroups = await MetricGroup.find({ universalTrackers: fromUtrId }).exec();
      for (const cmg of customMetricGroups) {
        const existingUtrIds = cmg.universalTrackers.map(utr => utr.toString());
        if (!existingUtrIds.includes(toUtrId.toString())) {
          metricGroupIds.add(cmg._id.toString());
          this.logger.info(`Will update ${migration.from} on metric group ${cmg.groupName} (${cmg._id.toHexString()})...`);
          cmg.universalTrackers.push(toUtrId);
          await cmg.save();
          updated++;
        }
      }
      if (updated) {
        results.updatedUtrs++;
      } else {
        results.skippedUtrs++;
      }
    }

    results.metricGroupIds = Array.from(metricGroupIds);
    await addTaskLog('Finished updating Metric Groups.');
    return results;
  }

  private async disableUtr(utr: UniversalTrackerModel, addTaskLog: AddTaskLog, userMessage: string | null) {
    const conditionCode = 'ofi-migration-2024';
    utr.conditions = utr.conditions?.filter(c => c.code !== conditionCode) ?? [];

    if (userMessage) {
      utr.conditions.push({
        code: conditionCode,
        calculation: {
          variables: new Map(),
          formula: '1', // Always true
        },
        result: {
          visibility: UtrConditionVisibility.Disabled,
          userMessage,
        }
      });
      this.logger.info(`Updating UTR ${utr.code} with condition...`);
    } else {
      this.logger.info(`Removing UTR ${utr.code} condition...`);
    }
    return utr.save();
  }

  private async regenerateSurveys(groupIds: string[], user: UserModel, addTaskLog: AddTaskLog) {
    await addTaskLog(`Regenerating Surveys...`);

    const affectedSurveys = await Survey.find({ 'scope.custom': { $in: groupIds } }).exec();
    const total = affectedSurveys.length;
    let ix = 0;

    const surveyManager = getSurveyManager();
    const surveyAggregator = getSurveyAggregator();

    for (const survey of affectedSurveys) {
      ix++;
      const surveyId = survey._id.toHexString();
      const isAggregated = isAggregatedSurvey(survey.type);
      const logMessage = `Regenerating ${isAggregated ? 'Aggregated' : 'Standard'} Survey ${surveyId}: ${survey.effectiveDate} ${survey.name} (${ix}/${total})...`;
      this.logger.info(logMessage);

      if (isAggregated) {
        const initiative = await InitiativeRepository.mustFindById(survey.initiativeId, { _everything: -1 })
        await surveyAggregator.updateAggregatedSurvey(survey, initiative, user);
      } else {
        await surveyManager.regenerate(survey, user);
      }
    }

    await addTaskLog(`Finished regenerating Surveys.`);
    return { total, regenerated: ix };
  }

  public async up(action: string, addTaskLog: AddTaskLog, user: UserModel): Promise<MigrationResult> {
    switch(action) {
      case UpAction.LockUTRs:
        return this.lockAllUtrs(addTaskLog);
      case UpAction.UnlockUTRs:
        return this.stage3(addTaskLog);
      case UpAction.RemoveOldUtrsFromGroups:
        return this.stage4(addTaskLog, user);
      case UpAction.CloneAndMigrate:
      default:
        return this.stage2(addTaskLog, user);
    }
  }

  private async lockAllUtrs(addTaskLog: AddTaskLog): Promise<MigrationResult> {
    // What this does:
    // 1. Puts a conditional message to lock all old and new UTRs to prevent updates during migration

    const migrationConfig = getMigrationConfig();
    await addTaskLog('Locking UTRs...');
    const metricGroupIds = new Set<string>();

    const results = {
      utrs: migrationConfig.length,
      updatedUtrs: 0,
      skippedUtrs: 0,
      metricGroupIds: [] as string[],
    };

    let ix = 0;
    for (const migration of migrationConfig) {
      ix++;
      await addTaskLog(`Updating UTR ${migration.from} ${ix}/${results.utrs}`, { lossy: true });

      const fromUtr = await this.getUtr(migration.from);
      const toUtr = await this.getUtr(migration.to);

      await this.disableUtr(fromUtr, addTaskLog, 'This question has been migrated to a new question. Please use the new question.');
      await this.disableUtr(toUtr, addTaskLog, 'This question is currently being migrated. Please wait until migration is complete before answering.');
    }

    results.metricGroupIds = Array.from(metricGroupIds);
    await addTaskLog('Finished locking UTRs.');
    return {
      success: true,
      results: [
        results
      ],
      message: 'OFI20240925_Migration stage1 up.'
    };
  }

  private async stage2(addTaskLog: AddTaskLog, user: UserModel): Promise<MigrationResult> {
    try {
      // What this does:
      // 1. Clone the target UTRVs so that we do not modify originals
      // 2. Find all the Custom Metric Groups that contain the target UTRs and add the new UTRs
      // 3. Regenerate surveys that contain the target Custom Metric Groups
      // 4. Migrate the UTRVs

      const migrationConfig = getMigrationConfig();

      const cloneResults = await this.cloneUtrvs(migrationConfig, addTaskLog);
      await addTaskLog('Clone plan: ' + JSON.stringify(cloneResults));

      const metricGroupResults = await this.updateMetricGroups(migrationConfig, addTaskLog);

      const regenerationResults = await this.regenerateSurveys(metricGroupResults.metricGroupIds, user, addTaskLog);

      const migrationResults = await this.migrateUtrvs(false, addTaskLog);
      migrationResults.actions = []; // Too much data to store in the job

      await addTaskLog('Results: ' + JSON.stringify(migrationResults));

      return {
        success: true,
        results: [
          cloneResults,
          metricGroupResults,
          regenerationResults,
          migrationResults
        ],
        message: 'OFI20240925_Migration stage2 up.'
      };
    }
    catch (e) {
      addTaskLog(`OFI20240925_Migration dry-run failed: ${e.message}`);
      this.logger.error(new ContextError('OFI20240925_Migration up failed', { cause: e }));
      throw e;
    }
  }

  private async stage3(addTaskLog: AddTaskLog): Promise<MigrationResult> {
    // What this does:
    // 1. Removes the lock new UTRs only to prevent updates during migration

    const migrationConfig = getMigrationConfig();
    await addTaskLog('Removing locks on UTRs...');
    const metricGroupIds = new Set<string>();

    const results = {
      utrs: migrationConfig.length,
      updatedUtrs: 0,
      skippedUtrs: 0,
      metricGroupIds: [] as string[],
    };

    let ix = 0;
    for (const migration of migrationConfig) {
      ix++;
      await addTaskLog(`Updating UTRs on Metric Group ${ix}/${results.utrs}`, { lossy: true });
      const toUtr = await this.getUtr(migration.to);
      await this.disableUtr(toUtr, addTaskLog, null);
    }

    results.metricGroupIds = Array.from(metricGroupIds);
    await addTaskLog('Finished updating UTRs.');
    return {
      success: true,
      results: [
        results
      ],
      message: 'OFI20240925_Migration stage3 up.'
    };
  }

  private async stage4(addTaskLog: AddTaskLog, user: UserModel): Promise<MigrationResult> {
    // What this does:
    // 1. Removes the old UTRs from the groups

    const migrationConfig = getMigrationConfig();
    await addTaskLog('Updating Metric Groups...');
    const metricGroupIds = new Set<string>();

    const results = {
      utrs: migrationConfig.length,
      updatedUtrs: 0,
      skippedUtrs: 0,
      metricGroupIds: [] as string[],
    };


    let ix = 0;
    for (const migration of migrationConfig) {
      ix++;
      await addTaskLog(`Removing old UTRs from Metric Group ${ix}/${results.utrs}`, { lossy: true });

      let updated = 0;
      const fromUtr = await this.getUtr(migration.from);
      const fromUtrId = fromUtr._id;
      const toUtr = await this.getUtr(migration.to);
      const toUtrId = toUtr._id;

      const customMetricGroups = await MetricGroup.find({ universalTrackers: fromUtrId }).exec();
      for (const cmg of customMetricGroups) {
        const existingUtrIds = cmg.universalTrackers.map(utr => utr.toString());
        const fromUtrIdString = fromUtrId.toString();
        const isFromUtrInGroup = existingUtrIds.includes(fromUtrId.toString());
        const isToUtrInGroup = existingUtrIds.includes(toUtrId.toString());

        if (isToUtrInGroup && isFromUtrInGroup) {
          this.logger.info(`Will update ${migration.from} on metric group ${cmg.groupName} (${cmg._id.toHexString()})...`);
          cmg.universalTrackers = cmg.universalTrackers.filter(u => u._id.toString() !== fromUtrIdString);
          await cmg.save();
          updated++;
        }
      }
      if (updated) {
        results.updatedUtrs++;
      } else {
        results.skippedUtrs++;
      }
    }

    results.metricGroupIds = Array.from(metricGroupIds);
    await addTaskLog('Finished updating Metric Groups.');

    const regenerationResults = await this.regenerateSurveys(results.metricGroupIds, user, addTaskLog);

    return {
      success: true,
      results: [
        results,
        regenerationResults
      ],
      message: 'OFI20240925_Migration stage4 up.'
    };
  }

  public async down(action: string, addTaskLog: AddTaskLog, user: UserModel): Promise<MigrationResult> {
    switch(action) {
      case DownAction.DeleteNewUTRVs:
        return this.downDeleteUtrvs(addTaskLog);
      case DownAction.AddOldUtrvsToGroups:
        return this.downAddOldUtrvsToGroups(addTaskLog, user);
      case DownAction.LockUTRs:
        return this.lockAllUtrs(addTaskLog);
      case DownAction.UnlockOldUTRs:
        return this.downUnlockOldUTRs(addTaskLog);
      case DownAction.UnlockNewUTRs:
        return this.downUnlockNewUTRs(addTaskLog);
      default:
        return {
          success: false,
          results: [],
          message: `Unknown action: ${action}`
        };
    }
  }

  private async downDeleteUtrvs(addTaskLog: AddTaskLog): Promise<MigrationResult> {
    try {
      const results = await this.deleteUtrvs();

      return {
        success: true,
        results,
        message: 'OFI20240925_Migration down.',
      };
    }
    catch (e) {
      addTaskLog(`OFI20240925_Migration dry-run failed: ${e.message}`);
      this.logger.error(new ContextError('OFI20240925_Migration down failed', { cause: e }));
      throw e;
    }
  }

  private async downAddOldUtrvsToGroups(addTaskLog: AddTaskLog, user: UserModel): Promise<MigrationResult> {
    await addTaskLog('Updating Metric Groups...');
    const metricGroupIds = new Set<string>();

    const migrationConfig = getMigrationConfig();
    const results = {
      utrs: migrationConfig.length,
      updatedUtrs: 0,
      skippedUtrs: 0,
      metricGroupIds: [] as string[],
      surveyRegeneration: {
        total: 0,
        regenerated: 0,
      }
    };


    let ix = 0;
    for (const migration of migrationConfig) {
      ix++;
      await addTaskLog(`Adding new UTRs to Metric Group ${ix}/${results.utrs}`, { lossy: true });

      const fromUtr = await this.getUtr(migration.from);
      const fromUtrId = fromUtr._id;
      const toUtr = await this.getUtr(migration.to);
      const toUtrId = toUtr._id;

      const customMetricGroups = await MetricGroup.find({ universalTrackers: { $in: [ fromUtrId, toUtrId ] }}).exec();
      let cmgUpdated = false;
      for (const cmg of customMetricGroups) {
        const existingUtrIds = cmg.universalTrackers.map(utr => utr.toString());
        if (!existingUtrIds.includes(toUtrId.toString())) {
          metricGroupIds.add(cmg._id.toString());
          this.logger.info(`Will update ${migration.from} on metric group ${cmg.groupName} (${cmg._id.toHexString()})...`);
          cmg.universalTrackers.push(toUtrId);
          await cmg.save();
          cmgUpdated = true;
        }
        if (!existingUtrIds.includes(fromUtrId.toString())) {
          metricGroupIds.add(cmg._id.toString());
          this.logger.info(`Will update ${migration.to} on metric group ${cmg.groupName} (${cmg._id.toHexString()})...`);
          cmg.universalTrackers.push(fromUtrId);
          await cmg.save();
          cmgUpdated = true;
        }
      }
      if (cmgUpdated) {
        results.updatedUtrs++;
      } else {
        results.skippedUtrs++;
      }
    }

    results.metricGroupIds = Array.from(metricGroupIds);
    await addTaskLog('Finished updating Metric Groups.');

    results.surveyRegeneration = await this.regenerateSurveys(results.metricGroupIds, user, addTaskLog);

    return {
      success: true,
      results,
      message: '',
    };
  }

  private async downUnlockOldUTRs(addTaskLog: AddTaskLog): Promise<MigrationResult> {
    const migrationConfig = getMigrationConfig();
    await addTaskLog('Removing locks on OLD UTRs...');
    const metricGroupIds = new Set<string>();

    const results = {
      utrs: migrationConfig.length,
      updatedUtrs: 0,
      skippedUtrs: 0,
      metricGroupIds: [] as string[],
    };

    let ix = 0;
    for (const migration of migrationConfig) {
      ix++;
      await addTaskLog(`Updating UTRs on Metric Group ${ix}/${results.utrs}`, { lossy: true });
      const fromUtr = await this.getUtr(migration.from);
      await this.disableUtr(fromUtr, addTaskLog, null);
    }

    results.metricGroupIds = Array.from(metricGroupIds);
    await addTaskLog('Finished updating UTRs.');
    return {
      success: true,
      results: [
        results
      ],
      message: 'OFI20240925_Migration downUnlockOldUTRs.'
    };
  }

  private async downUnlockNewUTRs(addTaskLog: AddTaskLog): Promise<MigrationResult> {
    const migrationConfig = getMigrationConfig();
    await addTaskLog('Removing locks on OLD UTRs...');
    const metricGroupIds = new Set<string>();

    const results = {
      utrs: migrationConfig.length,
      updatedUtrs: 0,
      skippedUtrs: 0,
      metricGroupIds: [] as string[],
    };

    let ix = 0;
    for (const migration of migrationConfig) {
      ix++;
      await addTaskLog(`Updating UTRs on Metric Group ${ix}/${results.utrs}`, { lossy: true });
      const toUtr = await this.getUtr(migration.to);
      await this.disableUtr(toUtr, addTaskLog, null);
    }

    results.metricGroupIds = Array.from(metricGroupIds);
    await addTaskLog('Finished updating UTRs.');
    return {
      success: true,
      results: [
        results
      ],
      message: 'OFI20240925_Migration downUnlockOldUTRs.'
    };
  }
}

interface OFIMigrationConfig {
  from: string;
  to: string;
  reducer: (accumulator: RowData[][], currentValue: RowData[]) => RowData[][];
}

interface ReducerConfig {
  columnToValueListCode: {
    textFromValueListCode: string;
    valueFromColCode: string;
    textToColCode: string;
    valueToColCode: string;
  }[];
  columnAndValueMap: {
    textFromColCode: string;
    valueFromColCode: string;
    textToColCode: string;
    valueToColCode: string;
  }[];
}

const getFuelConsumptionMigrationConfig = (): OFIMigrationConfig => {

  const reducerConfig: ReducerConfig = {
    columnToValueListCode: [
      { textFromValueListCode: 'ofi2/cm/1-1-1',  valueFromColCode: 'ofi/cm/1-1',  textToColCode: 'ofi2/cm/1-1', valueToColCode: 'ofi2/cm/1-3' }, // Coal
      { textFromValueListCode: 'ofi2/cm/1-1-2',  valueFromColCode: 'ofi/cm/1-3',  textToColCode: 'ofi2/cm/1-1', valueToColCode: 'ofi2/cm/1-3' }, // Natural Gas  (Non-US)
      { textFromValueListCode: 'ofi2/cm/1-1-3',  valueFromColCode: 'ofi/cm/1-4',  textToColCode: 'ofi2/cm/1-1', valueToColCode: 'ofi2/cm/1-3' }, // Natural Gas US only
      { textFromValueListCode: 'ofi2/cm/1-1-4',  valueFromColCode: 'ofi/cm/1-7',  textToColCode: 'ofi2/cm/1-1', valueToColCode: 'ofi2/cm/1-3' }, // LPG  (Liquified petroleum Gas)
      { textFromValueListCode: 'ofi2/cm/1-1-5',  valueFromColCode: 'ofi/cm/1-10', textToColCode: 'ofi2/cm/1-1', valueToColCode: 'ofi2/cm/1-3' }, // Propane -  Non-Vehicle
      { textFromValueListCode: 'ofi2/cm/1-1-6',  valueFromColCode: 'ofi/cm/1-2',  textToColCode: 'ofi2/cm/1-1', valueToColCode: 'ofi2/cm/1-2' }, // Oil
      { textFromValueListCode: 'ofi2/cm/1-1-7',  valueFromColCode: 'ofi/cm/1-5',  textToColCode: 'ofi2/cm/1-1', valueToColCode: 'ofi2/cm/1-2' }, // Petrol  - OFI owned/leased Vehicles
      { textFromValueListCode: 'ofi2/cm/1-1-8',  valueFromColCode: 'ofi/cm/1-6',  textToColCode: 'ofi2/cm/1-1', valueToColCode: 'ofi2/cm/1-2' }, // Petrol  - Stationary Sources including Generators
      { textFromValueListCode: 'ofi2/cm/1-1-9',  valueFromColCode: 'ofi/cm/1-8',  textToColCode: 'ofi2/cm/1-1', valueToColCode: 'ofi2/cm/1-2' }, // Diesel  - OFI owned/leased Vehicles
      { textFromValueListCode: 'ofi2/cm/1-1-10', valueFromColCode: 'ofi/cm/1-9',  textToColCode: 'ofi2/cm/1-1', valueToColCode: 'ofi2/cm/1-2' }, // Diesel  - Stationary Sources including Generators
      { textFromValueListCode: 'ofi2/cm/1-1-11', valueFromColCode: 'ofi/cm/1-11', textToColCode: 'ofi2/cm/1-1', valueToColCode: 'ofi2/cm/1-2' }, // Propane (OFI owned/leased Vehicles)
    ],
    columnAndValueMap: [
      { textFromColCode: 'ofi/cm/1-13', valueFromColCode: 'ofi/cm/1-14', textToColCode: 'ofi2/cm/1-1', valueToColCode: 'ofi2/cm/1-2' }, // @TODO - How to know which valueToColCode column to put this in???
      { textFromColCode: 'ofi/cm/1-16', valueFromColCode: 'ofi/cm/1-17', textToColCode: 'ofi2/cm/1-1', valueToColCode: 'ofi2/cm/1-2' }, // @TODO - How to know which valueToColCode column to put this in???
      { textFromColCode: 'ofi/cm/1-19', valueFromColCode: 'ofi/cm/1-20', textToColCode: 'ofi2/cm/1-1', valueToColCode: 'ofi2/cm/1-2' }, // @TODO - How to know which valueToColCode column to put this in???
    ]
  };

  return {
    from: 'ofi/cm/1',
    to: 'ofi2/cm/1',
    reducer: createReducer(reducerConfig),
  }
}

const getMigrationConfig = (): OFIMigrationConfig[] => {
  const migrationConfig: OFIMigrationConfig[] = [];

  // ofi/cm/1
  migrationConfig.push(getFuelConsumptionMigrationConfig());

  // ofi/cm/2 to ofi/cm/19
  migrationConfig.push(...getStandardMigrationConfig());
  return migrationConfig;
}

const getStandardMigrationConfig = (): OFIMigrationConfig[] => {
  const migrationConfig: OFIMigrationConfig[] = [];
  const config = [
    { code: 2, start: 1, end: 11 },
    { code: 3, start: 1, end: 8 },
    { code: 4, start: 1, end: 8 },
    { code: 5, start: 1, end: 7 },
    { code: 6, start: 1, end: 5 },
    { code: 7, start: 1, end: 5 },
    { code: 8, start: 1, end: 6 },
    { code: 9, start: 1, end: 5 },
    { code: 10, start: 1, end: 6 },
    { code: 11, start: 1, end: 6 },
    { code: 12, start: 1, end: 6 },
    { code: 13, start: 1, end: 6 },
    { code: 14, start: 1, end: 6 },
    { code: 15, start: 1, end: 3 },
    { code: 16, start: 1, end: 2 },
    { code: 17, start: 1, end: 1 },
    { code: 18, start: 1, end: 8 },
    { code: 19, start: 1, end: 18 },
  ];

  config.forEach(({ code, start = 1, end }) => {
    const columnToValueListCode: ReducerConfig['columnToValueListCode'] = [];
    for(let i = start; i <= end; i++) {
      columnToValueListCode.push({
        valueFromColCode: `ofi/cm/${code}-${i}`,
        textFromValueListCode: `ofi2/cm/${code}-1-${i}`,
        textToColCode: `ofi2/cm/${code}-1`,
        valueToColCode: `ofi2/cm/${code}-2`,
      });
    }

    const rest = [
      [end+2, end+3],
      [end+5, end+6],
      [end+8, end+9],
    ];

    const columnAndValueMap: ReducerConfig['columnAndValueMap'] = [];
    rest.forEach(([k, v]) => {
      columnAndValueMap.push({
        textFromColCode: `ofi/cm/${code}-${k}`,
        valueFromColCode: `ofi/cm/${code}-${v}`,
        textToColCode: `ofi2/cm/${code}-1`,
        valueToColCode: `ofi2/cm/${code}-2`,
      });
    });

    migrationConfig.push({
      from: `ofi/cm/${code}`,
      to: `ofi2/cm/${code}`,
      reducer: createReducer({
        columnToValueListCode,
        columnAndValueMap,
      }),
    });
  });
  return migrationConfig;
}

type Reducer = (acc: RowData[][], row: RowData[]) => RowData[][];
const createReducer = (reducerConfig: ReducerConfig): Reducer => {
  return (acc: RowData[][], row: RowData[]) => {
    for (const columnToValueListCode of reducerConfig.columnToValueListCode) {
      const { valueFromColCode, textFromValueListCode, textToColCode, valueToColCode } = columnToValueListCode;
      const col = row.find(c => c.code === valueFromColCode);
      if (!col) {
        // throw new ContextError(`Column ${valueFromColCode} not found`);
        // Probably partial data, so we can skip
        continue;
      }
      acc.push([
        {
          code: textToColCode,
          value: textFromValueListCode,
        },
        {
          ...col, // To copy the entire value column including units and numberScale
          code: valueToColCode,
        },
      ]);
    }

    for (const columnAndValueMap of reducerConfig.columnAndValueMap) {
      const { valueFromColCode, textFromColCode, textToColCode, valueToColCode } = columnAndValueMap;
      const valueListCol = row.find(c => c.code === textFromColCode);
      if (!valueListCol) {
        // throw new ContextError(`Value list column not found: ${textFromColCode}. Valid columns: ${row.map(c => c.code).join(', ')}`);
        continue;
      }
      const valueCol = row.find(c => c.code === valueFromColCode);
      if (!valueCol) {
        // throw new ContextError(`Value column not found: ${valueFromColCode}. Valid columns: ${row.map(c => c.code).join(', ')}`);
        continue;
      }
      if (!valueListCol.value) {
        continue;
      }
      acc.push([
        {
          code: textToColCode,
          value: valueListCol.value,
        },
        {
          ...valueCol, // To copy units and numberScale
          code: valueToColCode,
        },
      ]);
    }

    return acc;
  };
}
