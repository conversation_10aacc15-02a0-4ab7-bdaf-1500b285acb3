/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import BackgroundJob, { BackgroundJobModel, BackgroundJobPlain, CreateJob, JobType, Task, TaskStatus, TaskType } from '../../models/backgroundJob';
import { BackgroundBaseWorkflow, TaskResult } from '../background-process/BackgroundBaseWorkflow';
import { HydratedDocument } from 'mongoose';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import { createLogEntry } from '../jobs';
import { getMigrationService } from './MigrationService';
import { MigrationResult } from './types';
import { JobStatus } from '../../models/surveyTemplateHistory';
import { generatedUUID } from '../crypto/token';
import User from '../../models/user';

type MigrationData = {
  userId?: string;
  migrationCode: string;
  migrationAction: string;
  results?: MigrationResult;
};

export type TaskMigrationUp = Task<MigrationData, TaskType.MigrationScriptUp>;
export type TaskMigrationUpDryRun = Task<MigrationData, TaskType.MigrationScriptUpDryRun>;
export type TaskMigrationDown = Task<MigrationData, TaskType.MigrationScriptDown>;
export type SupportedTask = TaskMigrationUp | TaskMigrationUpDryRun | TaskMigrationDown;
export type SupportedJobPlain = BackgroundJobPlain<SupportedTask[]> & { type: JobType.MigrationScript };
export type SupportedJobModel = HydratedDocument<SupportedJobPlain>;

export class MigrationWorkflow extends BackgroundBaseWorkflow<SupportedJobModel> {
  constructor(
    protected logger: LoggerInterface,
    protected jobType: JobType.MigrationScript,
  ) {
    super();
  }

  protected async processTask(job: SupportedJobModel, task: SupportedTask): Promise<TaskResult<SupportedJobModel>> {
    const result: TaskResult<SupportedJobModel> = {
      job,
      executeNextTask: false,
    };

    task.status = TaskStatus.Processing;
    job.markModified('tasks');

    const migrationService = getMigrationService();

    const { migrationCode, migrationAction } = task.data;

    let lastSave = Date.now();
    const isTimeToSave = () => Date.now() - lastSave > 10000;
    const addTaskLog = async (message: string, config?: { lossy?: boolean }) => {
      if (!config?.lossy || isTimeToSave()) {
        wwgLogger.info(message);
        job.logs.push(createLogEntry(message));
        lastSave = Date.now();
        await job.save();
        return true;
      }
      return false;
    }

    try {
      const user = await User.findById(task.data.userId);
      if (!user) {
        throw new Error('User not found: ' + task.data.userId);
      }

      switch (task.type) {
        case TaskType.MigrationScriptUp: {
          task.data.results = await migrationService.up(migrationCode, migrationAction, addTaskLog, user);
          task.status = TaskStatus.Completed;
          result.executeNextTask = true;
          break;
        }
        case TaskType.MigrationScriptUpDryRun: {
          task.data.results = await migrationService.dryRun(migrationCode, migrationAction, addTaskLog, user);
          task.status = TaskStatus.Completed;
          result.executeNextTask = true;
          break;
        }
        case TaskType.MigrationScriptDown: {
          task.data.results = await migrationService.down(migrationCode, migrationAction, addTaskLog, user);
          task.status = TaskStatus.Completed;
          result.executeNextTask = true;
          break;
        }
      }
    } catch (error) {
      task.status = TaskStatus.Error;
      job.status = JobStatus.Error;
      job.logs.push(
        createLogEntry(`Failed task ${task.name}. Error: ${error.message}`, { metadata: { cause: error } })
      );
      wwgLogger.error(error);
    }

    job.markModified('tasks');
    await job.save();

    return result;
  }

  public async createJob(data: MigrationData, taskType: TaskType): Promise<BackgroundJobModel> {
    const tasks: Task<MigrationData>[] = [
      {
        id: generatedUUID(),
        type: taskType,
        name: `Migration Task ${data.migrationCode}`,
        status: TaskStatus.Pending,
        data
      },
    ];

    const createData: CreateJob = {
      type: this.jobType,
      name: `Migration Job: ${data.migrationCode}`,
      tasks: tasks,
      logs: [createLogEntry('Created migration job')],
    };

    return BackgroundJob.create(createData);
  }
}

let instance: MigrationWorkflow;
export const getMigrationWorkflow = () => {
  if (!instance) {
    instance = new MigrationWorkflow(
      wwgLogger,
      JobType.MigrationScript,
    );
  }
  return instance;
};
