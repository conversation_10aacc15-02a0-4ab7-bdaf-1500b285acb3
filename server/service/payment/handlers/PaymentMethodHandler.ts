/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import <PERSON><PERSON> from "stripe";
import { getInnerOptionalId, HandleResult, HandlerEvent, HandlerType } from "./HandlerType";
import { LoggerInterface } from "../../wwgLogger";
import { CustomerManager } from "../CustomerManager";
import { InitiativeWithCustomer } from "../../../models/initiative";

export class PaymentMethodHandler implements HandlerType {

  private name = 'PaymentMethod';

  constructor(
    private logger: LoggerInterface,
    private customerManager: CustomerManager,
  ) {
  }

  shouldHandle(event: HandlerEvent): boolean {
    return [
      'payment_method.attached',
    ].includes(event.type);
  }

  public async handle(event: HandlerEvent<Stripe.PaymentMethod>): Promise<HandleResult> {

    const paymentMethod = event.data.object;

    const customerId = getInnerOptionalId(paymentMethod.customer);
    if (!customerId) {
      return this.createResult([`Failed to find initiative due to missing customer for payment method "${paymentMethod.id}"`]);
    }

    const initiative = await this.customerManager.findInitiativeByCustomerId(customerId)

    if (!initiative) {
      return this.createResult([`Failed to find initiative by "${customerId}"`]);
    }

    if (event.type === 'payment_method.attached') {
      return this.attached(event, initiative);
    }

    return this.createResult()
  }

  private createResult(errors?: unknown[]) {
    return {
      success: !errors || errors.length === 0,
      name: this.name,
      errors: errors
    };
  }

  private async attached(event: HandlerEvent<Stripe.PaymentMethod>, initiative: InitiativeWithCustomer) {

    const paymentMethod = event.data.object;
    const paymentMethodId = paymentMethod.id;

    if (!initiative.customer.defaultPaymentMethod) {
      const customer = await this.customerManager.getRemoteCustomer(initiative.customer.id)
      if (!customer.deleted) {
        this.logger.info(`Adding default payment method to initiative ${initiative.name}`, {
          initiativeId: initiative._id.toString(),
          customerId: initiative.customer.id,
          paymentMethodId,
        })

        const currentPaymentId = getInnerOptionalId(customer.invoice_settings.default_payment_method);
        if (!currentPaymentId) {
          await this.customerManager.addDefaultInvoicePaymentMethod({
            customerId: customer.id,
            paymentMethodId,
          });
          initiative.customer.defaultPaymentMethod = paymentMethodId;
        } else {
          // Somehow this is not aligned, update it.
          initiative.customer.defaultPaymentMethod = currentPaymentId;
        }

        await initiative.save();
      }
    }

    return this.createResult()
  }
}
