/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import <PERSON><PERSON> from "stripe";
import {
  getInnerId,
  HandleResult,
  HandlerEvent,
  HandlerType
} from "./HandlerType";
import { LoggerInterface } from "../../wwgLogger";
import { CustomerManager } from "../CustomerManager";
import { InitiativeWithCustomer } from "../../../models/initiative";

export class SubscriptionHandler implements HandlerType {
  private name = 'CustomerSubscription';

  constructor(
    private logger: LoggerInterface,
    private customerManager: CustomerManager,
  ) {
  }


  shouldHandle(event: HandlerEvent): boolean {
    return [
      "customer.subscription.created",
      "customer.subscription.updated",
      "customer.subscription.deleted",
      "customer.subscription.trial_will_end",
    ].includes(event.type);
  }

  public async handle(event: HandlerEvent<Stripe.Subscription>): Promise<HandleResult> {

    const sub = event.data.object;

    const customerId = getInnerId(sub.customer);
    const initiative = await this.customerManager.findInitiativeByCustomerId(customerId)

    if (!initiative) {
      return this.createResult([`Failed to find initiative by "${customerId}"`]);
    }

    switch (event.type) {
      // Occurs whenever a subscription changes (e.g., switching from one plan to another,
      // or changing the status from trial to active).
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
      case 'customer.subscription.deleted':
      case 'coupon.created':
      case 'coupon.deleted':
      case 'coupon.updated':
        return this.updated(event, initiative);
      case 'customer.subscription.trial_will_end':
      default:
        // Unhandled event type
        return this.createResult()
    }
  }

  private createResult(errors?: unknown[]) {
    return {
      success: !errors || errors.length === 0,
      name: this.name,
      errors: errors
    };
  }

  private async updated(event: HandlerEvent<Stripe.Subscription>, initiative: InitiativeWithCustomer) {
    await this.customerManager.updateSubscription(initiative, event.data.object.id);
    return this.createResult();
  }
}
