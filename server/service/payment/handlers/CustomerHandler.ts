/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import <PERSON><PERSON> from "stripe";
import {
  getInnerId,
  HandleResult,
  HandlerEvent,
  HandlerType
} from "./HandlerType";
import { LoggerInterface } from "../../wwgLogger";
import { CustomerManager } from "../CustomerManager";
import { InitiativeWithCustomer } from "../../../models/initiative";

export class CustomerHandler implements HandlerType {
  private name = 'Customer';

  constructor(
    private logger: LoggerInterface,
    private customerManager: CustomerManager,
  ) {
  }


  shouldHandle(event: HandlerEvent): boolean {
    return [
      "customer.updated",
    ].includes(event.type);
  }

  public async handle(event: HandlerEvent<Stripe.Customer>): Promise<HandleResult> {

    const customerId = event.data.object.id;
    const initiative = await this.customerManager.findInitiativeByCustomerId(customerId)

    if (!initiative) {
      return this.createResult([`Failed to find initiative by "${customerId}"`]);
    }

    switch (event.type) {
      case 'customer.updated': {
        return this.updated(event, initiative);
      }
      default:
        // Unhandled event type
        return this.createResult()
    }
  }

  private createResult(errors?: unknown[]) {
    return {
      success: !errors || errors.length === 0,
      name: this.name,
      errors: errors
    };
  }

  private async updated(event: HandlerEvent<Stripe.Customer>, initiative: InitiativeWithCustomer) {

    const customer = event.data.object;
    const paymentMethod = customer.invoice_settings.default_payment_method;
    const paymentMethodId = paymentMethod ? getInnerId(paymentMethod) : undefined;

    if (initiative.customer.defaultPaymentMethod !== paymentMethodId) {
      this.logger.info(`Customer "${initiative.name}" default payment method has changed`, {
        initiativeId: initiative.id,
        customerId: customer.id,
        oldPaymentMethod: initiative.customer.defaultPaymentMethod,
        newPaymentMethod: paymentMethodId,
      });
      initiative.customer.defaultPaymentMethod = paymentMethodId;
    }

    initiative.customer.currency = customer.currency ?? undefined;
    await initiative.save();

    return this.createResult();
  }
}
