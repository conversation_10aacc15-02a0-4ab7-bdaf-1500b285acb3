/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import <PERSON><PERSON> from "stripe";
import { getInnerOptionalId, HandleResult, HandlerEvent, HandlerType } from "./HandlerType";
import { LoggerInterface } from "../../wwgLogger";
import { CustomerManager } from "../CustomerManager";
import { InitiativeWithCustomer } from "../../../models/initiative";

export class InvoiceHandler implements HandlerType {

  private name = 'Invoice';

  constructor(
    private logger: LoggerInterface,
    private customerManager: CustomerManager,
  ) {
  }

  shouldHandle(event: HandlerEvent): boolean {
    return [
      "invoice.paid",
      "invoice.payment_failed",
    ].includes(event.type);
  }

  public async handle(event: HandlerEvent<Stripe.Invoice>): Promise<HandleResult> {

    const invoice = event.data.object;

    const customerId = getInnerOptionalId(invoice.customer);
    if (!customerId) {
      return this.createResult([`Failed to find initiative due to missing customer for invoice "${invoice.id}"`]);
    }

    const initiative = await this.customerManager.findInitiativeByCustomerId(customerId)

    if (!initiative) {
      return this.createResult([`Failed to find initiative by "${customerId}"`]);
    }

    switch (event.type) {
      case 'invoice.paid':
        // Continue to provision the subscription as payments continue to be made.
        // Store the status in your database and check when a user accesses your service.
        // This approach helps you avoid hitting rate limits.
        return this.paid(event, initiative);
      case 'invoice.payment_failed':
        // The payment failed or the customer does not have a valid payment method.
        // The subscription becomes past_due. Notify your customer and send them to the
        // customer portal to update their payment information.
        return this.createResult()
      default:
        // Unhandled event type
        return this.createResult()
    }
  }

  private createResult(errors?: unknown[]) {
    return {
      success: !errors || errors.length === 0,
      name: this.name,
      errors: errors
    };
  }

  private async paid(event: HandlerEvent<Stripe.Invoice>, initiative: InitiativeWithCustomer) {

    const invoice = event.data.object;
    const subscriptionId = getInnerOptionalId(invoice.subscription);
    if (!subscriptionId) {
      return this.createResult([`Failed to process paid invoice for initiative by "${initiative._id}", due to missing subscription. Invoice id ${invoice.id}`]);
    }

    await this.customerManager.updateSubscription(initiative, subscriptionId);
    return this.createResult()
  }
}
