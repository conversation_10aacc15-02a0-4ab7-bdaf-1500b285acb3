/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import Stripe from "stripe";

export interface HandleResult {
  name: string;
  success: boolean
  errors?: unknown[];
}

export interface HandlerEvent<T extends object = object> extends Stripe.EventBase {
  data: {
    object: T;
    previous_attributes?: object
  }
}

export interface HandlerType {
  shouldHandle(event: HandlerEvent): boolean;
  handle(event: HandlerEvent): Promise<HandleResult>;
}

export const defaultHandler: HandlerType = {
  shouldHandle: () => false,
  handle: async () => ({ success: true, name: 'Default handler' }),
}

export const getInnerId = (dataObject: { id: string } | string) => {
  return typeof dataObject === 'string' ? dataObject : dataObject.id;
}

export const getInnerOptionalId = (dataObject: { id: string } | string | null ) => {
  return typeof dataObject === 'string' ? dataObject : dataObject?.id;
}
