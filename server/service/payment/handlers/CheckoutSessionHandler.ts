/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import Strip<PERSON> from "stripe";
import {
  getInnerOptionalId,
  HandleResult,
  HandlerEvent,
  HandlerType,
} from "./HandlerType";
import { LoggerInterface } from "../../wwgLogger";
import { CustomerManager } from "../CustomerManager";
import { InitiativeWithCustomer } from '../../../models/initiative';

export class CheckoutSessionHandler implements HandlerType {

  constructor(
    private logger: LoggerInterface,
    private customerManager: CustomerManager,
  ) {
  }

  private readonly name = 'CheckoutSession'

  public async handle(event: HandlerEvent<Stripe.Checkout.Session>): Promise<HandleResult> {

    const eventObject = event.data.object;
    const clientReferenceId = eventObject.client_reference_id ?? '';
    const customer = eventObject.customer;

    if (!customer) {
      return this.createResult([`Failed to process event ${event.type} due to missing customer id`])
    }

    const customerId = typeof customer === 'string' ? customer : customer.id;
    const meta = {
      customerId,
      clientReferenceId,
      metadata: eventObject.metadata,
      paymentStatus: eventObject.payment_status,
    };

    this.logger.info(`Payment checkout session completed! Payment: "${eventObject.payment_status}"`, meta);

    if (eventObject.payment_status === 'paid') {
      return this.processPaid(event, meta, customerId);
    }

    return this.createResult();
  }

  private async processPaid(event: HandlerEvent<Stripe.Checkout.Session>, meta: Record<string, unknown>, customerId: string) {

    const eventObject = event.data.object;
    this.logger.info(`Customer ${customerId} has paid, processing event...`, meta);
    const initiative = await this.customerManager.findInitiativeByCustomerId(customerId);
    if (!initiative) {
      return this.createResult([`Failed to find initiative for "${customerId}"`]);
    }

    switch(eventObject.mode) {
      case "subscription":
        return this.processSubscriptionEvent(eventObject, initiative, meta);
      default:
        return this.createResult([`Failed to process event ${eventObject.id}, unknown mode "${eventObject.mode}"`]);
    }
  }

  private async processSubscriptionEvent(eventObject: Stripe.Checkout.Session, initiative: InitiativeWithCustomer, meta: Record<string, unknown>) {
    this.logger.info(`Processing subscription...`, meta);
    const subscriptionId = getInnerOptionalId(eventObject.subscription);
    if (!subscriptionId) {
      return this.createResult([`Failed to parse event ${eventObject.id}, missing subscriptionId "${subscriptionId}"`]);
    }
    await this.processReferralUsage(eventObject, initiative).catch(e => this.logger.error(e));

    await this.customerManager.updateSubscription(initiative, subscriptionId);
    this.logger.info(
      `Added product to initiative: "${eventObject.payment_status}"`,
      { ...meta, initiativeId: initiative._id }
    );

    return {
      success: true,
      name: this.name,
    };
  }

  private async processReferralUsage(eventObject: Stripe.Checkout.Session, initiative: InitiativeWithCustomer) {
    const referralCode = eventObject.metadata?.referralCode;
    const referrals = initiative.referrals;

    if (referralCode && referrals && eventObject.payment_status === 'paid') {

      // Look if we need to expire initiative referral code
      const referralToUse = referrals.find(r => r.code === referralCode && !r.usedDate);

      if (referralToUse) {
        // Expire
        referralToUse.usedDate = new Date();
        initiative.markModified('referrals');
        await initiative.save();
      }
    }
  }

  private createResult(errors?: unknown[]) {
    return {
      success: !errors || errors.length === 0,
      name: this.name,
      errors: errors
    };
  }

  shouldHandle(event: HandlerEvent): boolean {
    return [
      "checkout.session.completed",
    ].includes(event.type);
  }
}
