/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import Stripe from "stripe";
import { LoggerInterface, wwgLogger } from "../wwgLogger";
import StripeEvent, { StripeEventModel } from "../../models/stripeEvent";
import { getCustomerManager } from "./CustomerManager";
import { defaultHandler, HandlerType } from "./handlers/HandlerType";
import { CheckoutSessionHandler } from "./handlers/CheckoutSessionHandler";
import { SubscriptionHandler } from "./handlers/SubscriptionHandler";
import { InvoiceHandler } from "./handlers/InvoiceHandler";
import { CustomerHandler } from './handlers/CustomerHandler';
import { PaymentMethodHandler } from "./handlers/PaymentMethodHandler";
import { HydratedDocument } from "mongoose";
import { MaterialityAssessmentCheckoutSessionHandler } from "./handlers/MaterialityAssessmentCheckoutSessionHandler";

export class EventHandler {

  constructor(
    private logger: LoggerInterface,
    private handlers: HandlerType[],
  ) {
  }

  /**
   * accept event received from stripe event hook endpoint.
   * It should only add event to the database and the async process
   * the actual event to ensure webhook endpoint is not timing out.
   */
  public async acknowledge(event: Pick<Stripe.Event, 'id' | 'type'>) {

    const exists = await StripeEvent.findOne({ 'event.id': event.id }, { _id: 1 })
      .lean()
      .exec();

    if (exists) {
      this.logger.warn(`Stripe event with id "${event.id}" already exists`, {
        eventId: event.id,
        eventType: event.type
      })
      return;
    }

    const newEvent = new StripeEvent({ event: event });
    await newEvent.save();

    // Stored in database, can process the rest async
    this.process(newEvent).catch(e => this.logger.error(e));
  }

  private async process(stripeEvent: HydratedDocument<StripeEventModel>) {

    const event = stripeEvent.event;
    try {
      const results = await Promise.all(
        this.handlers
          .filter(h => h.shouldHandle(event))
          .map(h => h.handle(event))
      );

      this.logger.info(`Processed stripe event ${event.type}`, {
        service: 'stripe',
        results,
        eventType: event.type,
        eventId: event.id,
      });

      stripeEvent.processedDate = new Date();
      await stripeEvent.save();
    } catch (e) {
      this.logger.error(e);
    }
  }
}

let instance: EventHandler;
export const getEventHandler = () => {
  if (!instance) {
    const cm = getCustomerManager();
    const logger = wwgLogger;
    instance = new EventHandler(
      logger,
      [
        new CheckoutSessionHandler(logger, cm ),
        new SubscriptionHandler(logger, cm),
        new CustomerHandler(logger, cm),
        new InvoiceHandler(logger, cm),
        new PaymentMethodHandler(logger, cm),
        new MaterialityAssessmentCheckoutSessionHandler(logger, cm),
        defaultHandler
      ]
    );
  }
  return instance;
}
