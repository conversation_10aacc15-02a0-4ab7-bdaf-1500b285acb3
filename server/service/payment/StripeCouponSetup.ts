/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */


import UserError from "../../error/UserError";
import { getProductManager, ProductManager } from "./ProductManager";
import { ProductCodes } from "../../models/customer";
import StripeClient from "./StripeClient";
import { LoggerInterface, wwgLogger } from "../wwgLogger";
import ContextError from "../../error/ContextError";
import Strip<PERSON> from "stripe";

interface SetupOptions {
  limit?: number;
  shouldCreateMissing?: boolean;
}

interface CouponFilters {
  productCode: ProductCodes;
  durationInMonths?: number;
  isStaff?: boolean;
  discountPercentage?: number;
  limit?: number;
}

interface PromotionCodeCreate {

  /** Coupon we are associating this new promotion code with **/
  couponId: string;

  /** User provided description or reason why this code was generated **/
  description: string;

  /** User generate code, if omitted will be generated automatically */
  code?: string;

  /** A positive integer specifying the number of times the promotion code can be redeemed */
  maxRedemptions?: number;
}

export class StripeCouponSetup {

  private readonly staffKey = 'isStaff';

  private readonly supportedProductCodes = [
    ProductCodes.CompanyTracker,
    ProductCodes.CompanyTrackerPro,
    ProductCodes.CompanyTrackerEnterprise,
    ProductCodes.SGXESGenome,
  ];

  private readonly durationInMonths = [1, 3, 6, 12, 18, 24, 36, 48, 60];

  constructor(
    private logger: LoggerInterface,
    private stripe: typeof StripeClient,
    private productManager: ProductManager,
  ) {
  }

  public createPromotionCode({ couponId, description, code, maxRedemptions = 1 }: PromotionCodeCreate) {
    return this.stripe.promotionCodes.create({
      coupon: couponId,
      metadata: { description },
      code,
      max_redemptions: maxRedemptions,
    })
  }

  public async getCoupons(filters: CouponFilters) {
    const { durationInMonths, productCode, isStaff, discountPercentage, limit = 100 } = filters;
    const product = await this.productManager.getProduct(productCode);

    if (!product) {
      throw new UserError(`Failed to find product for ${productCode}`, { filters })
    }

    const coupons = await this.getCouponList(limit);

    return coupons.data.filter(coupon => {

      if (coupon.applies_to && !coupon.applies_to.products.includes(product.id)) {
        return false;
      }

      if (durationInMonths && coupon.duration_in_months !== durationInMonths) {
        return false;
      }

      if (discountPercentage !== undefined && coupon.percent_off !== discountPercentage) {
        return false;
      }

      if (isStaff !== undefined && Boolean(coupon.metadata?.isStaff) !== isStaff) {
        return false;
      }

      return coupon.valid;
    })
  }

  public async process(options: SetupOptions) {

    const { map: populatedMap, productIdToCodeMap } = await this.populateCouponMap(
      options,
      this.getDefaultConfiguration(),
    );
    this.logMapDetails(populatedMap, 'Finished populating product coupon map');

    if (options.shouldCreateMissing) {
      return this.createMissing(populatedMap, productIdToCodeMap)
    }

    return Object.fromEntries(populatedMap);
  }

  private getProductCodes() {
    return this.supportedProductCodes;
  }

  private getKey(productCode: string, duration: number) {
    return `${productCode}|${duration}`;
  }

  private reverseKey(key: string) {
    const [productCode, duration] = key.split('|')
    return { productCode, duration }
  }

  private getDefaultConfiguration() {
    const discountMap = new Map<string, string[]>()
    this.getProductCodes().forEach(productCode => {
      this.durationInMonths.forEach(duration => {
        discountMap.set(this.getKey(productCode, duration), [])
      })
    })

    return discountMap
  }

  private logMapDetails(populatedMap: Map<string, string[]>, msg: string) {
    const available: Record<string, string[]> = {};
    const missing: Record<string, string[]> = {}
    populatedMap.forEach((ids, key) => {
      if (ids.length > 0) {
        available[key] = ids
      } else {
        missing[key] = ids
      }
    })

    const availableCount = Object.keys(available);
    const missingCount = Object.keys(available);
    this.logger.info(`${msg}. Available: ${availableCount}, missing: ${missingCount}`, {
      available,
      missing,
    })
  }

  private async populateCouponMap(options: SetupOptions, map: Map<string, string[]>) {
    const { limit = 100 } = options;
    const coupons = await this.getCouponList(limit);
    const productCodes = this.getProductCodes();
    const productIdToCodeMap = await this.getProductMap(productCodes);

    coupons.data.forEach(c => {

      // Limit to staff metadata only and have duration
      const durationInMonths = c.duration_in_months;
      if (!c.metadata || !c.metadata[this.staffKey] || !durationInMonths || !c.valid) {
        return;
      }
      const codes = this.getValidProductCodes(productIdToCodeMap, c.applies_to?.products)

      codes.forEach(code => {
        const k = this.getKey(code, durationInMonths)

        const entry = map.get(k);
        if (entry) {
          entry.push(c.id);
        }
      })
    })

    return { map, productIdToCodeMap };
  }

  private async getCouponList(limit: number) {
    return this.stripe.coupons.list({
      limit,
      expand: ['data.applies_to']
    })
  }

  private async getProductMap(productCodes: ProductCodes[]) {
    const productQuery = this.createProductQuery(productCodes)
    const productIds = await this.stripe.products.search({ query: productQuery });
    return new Map(productIds.data.reduce((acc, product) => {
      const code = product.metadata?.productCode;
      if (code && productCodes.includes(code as ProductCodes)) {
        acc.push([product.id, code])
      }
      return acc;
    }, [] as [string, string][]));
  }

  private getValidProductCodes(productMap: Map<string, string | undefined>, productIds: string[] | undefined) {
    if (!productIds) {
      return this.supportedProductCodes;
    }

    return productIds.reduce((acc, id) => {
      const code = productMap.get(id);
      if (code) {
        acc.push(code);
      }
      return acc;

    }, [] as string[]);
  }

  // https://stripe.com/docs/search#metadata
  private createProductQuery(codes: ProductCodes[]) {
    //currency:"usd" OR currency:"eur"
    return codes.map(code => `metadata["productCode"]: "${code}"`).join(' OR ');
  }

  private async createMissing(populatedMap: Map<string, string[]>, productIdToCodeMap: Map<string, string>) {

    const reverseMap = new Map();
    productIdToCodeMap.forEach((productCode, productId) => {
      reverseMap.set(productCode, productId);
    })

    let createdCount = 0;
    for (const [key, ids] of populatedMap.entries()) {

      if (ids.length > 0) {
        continue; // Already have one, skip
      }

      const { productCode, duration } = this.reverseKey(key)
      const productId = reverseMap.get(productCode);

      if (!productId) {
        throw new ContextError(`Failed to find reversed lookup for productCode ${productCode}`, {
          productCode,
          duration,
          key,
        })
      }

      const params: Stripe.CouponCreateParams = {
        // Max 40 character
        name: `[PLATFORM] ${productCode}:${duration}M:100%`.slice(0, 40),
        applies_to: { products: [productId] },
        metadata: { isStaff: 'true' },
        percent_off: 100,
        duration: 'repeating',
        duration_in_months: Number(duration),
      };

      this.logger.info(`Creating Stripe coupon ${params.name}`, params)

      const coupon = await this.stripe.coupons.create(params)
      ids.push(coupon.id);
      createdCount++;
    }

    this.logMapDetails(populatedMap, `Finished creating missing product coupon map, created: ${createdCount}`);

    return Object.fromEntries(populatedMap);
  }
}

let instance: StripeCouponSetup;
export const getStripeCouponSetup = () => {
  if (!instance) {
    instance = new StripeCouponSetup(
      wwgLogger,
      StripeClient,
      getProductManager(),
    );
  }
  return instance;
}
