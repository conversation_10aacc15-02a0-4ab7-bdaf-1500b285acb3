/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import Stripe from 'stripe';
import config from '../../config';
import { getInnerId, getInnerOptionalId } from "./handlers/HandlerType";
import { Coupon, Discount, Subscription } from "../../models/customer";
import { wwgLogger } from '../wwgLogger';
import { timestampToISOString } from "../../util/date";


const StripeClient = new Stripe(
  config.payment.stripe.secret,
  { apiVersion: '2024-06-20' }
);


const isExpandedProduct = (product: string | Stripe.Product | Stripe.DeletedProduct): product is (Stripe.Product | Stripe.DeletedProduct) => {
  return typeof product === 'object';
}

const isExtendedStripeDiscount = (discount: string | Stripe.Discount | null | undefined): discount is Stripe.Discount => {
  return !!discount && typeof discount === 'object';
}

const mapStripeCouponToLocal = (stripeCoupon: Stripe.Coupon | null | undefined): Coupon | undefined => {
  return stripeCoupon
    ? {
        id: stripeCoupon.id,
        name: stripeCoupon.name ?? undefined,
        currency: stripeCoupon.currency,
        percent_off: stripeCoupon.percent_off,
      }
    : undefined;
};

const mapStripeDiscountsToLocal = (
  stripeDiscounts: (Stripe.Discount | string)[] | undefined
): Discount[] => {
  if (!stripeDiscounts) {
    throw new Error('Received invalid stripe discounts');
  }
  return stripeDiscounts.map(mapSingleStripeDiscountToLocal).filter((item): item is Discount => Boolean(item));
};

const mapSingleStripeDiscountToLocal = (stripeDiscount: Stripe.Discount | string | null | undefined): Discount | undefined => {
  if (!isExtendedStripeDiscount(stripeDiscount)) {
    return undefined;
  }

  return {
    id: stripeDiscount.id,
    start: stripeDiscount.start,
    end: stripeDiscount.end,
    coupon: mapStripeCouponToLocal(stripeDiscount.coupon),
  };
};

export const fromStripeSubscriptionWithProduct = (subscription: Stripe.Subscription): Subscription => ({
  id: subscription.id,
  status: subscription.status,
  items: subscription.items.data.map(item => {
    const product = item.price.product;
    if (!isExpandedProduct(product)) {
      throw new Error(`Require expanded product property for subscription ${subscription.id}, item: ${item.id}, received: ${product}`)
    }

    // If it's deleted or not set, should update the code
    const productCode = product.deleted ? undefined : product.metadata.productCode;
    if (!productCode) {
      wwgLogger.error(new Error(`Subscription ${subscription.id}, item ${item.id} Product ${product.id} is missing productCode metadata`));
    }

    return ({
      id: item.id,
      priceId: item.price.id,
      productId: getInnerId(product),
      productCode: productCode,
      quantity: item.quantity,
      created: item.created,
      price: {
        id: item.price.id,
        type: item.price.type,
        recurring: item.price.recurring ?? undefined,
      }
    });
  }),
  startDate: subscription.start_date,
  endDate: subscription.ended_at ?? undefined,
  cancelDate: subscription.canceled_at ?? subscription.cancel_at ?? undefined,
  periodStart: subscription.current_period_start,
  periodEnd: subscription.current_period_end,
  trialStart: subscription.trial_start ?? undefined,
  trialEnd: subscription.trial_end ?? undefined,
  discount: mapSingleStripeDiscountToLocal(subscription.discount),
  discounts: mapStripeDiscountsToLocal(subscription.discounts),
});

export interface InvoiceLineItem {
  id: string;
  amount: number;
  currency: string;
  currencySymbol?: string;
  description: string | null;
  periodStart: string;
  periodEnd: string;
  itemId: string | undefined;
  proration: boolean;
  quantity: number | null;
  price?: number;
}

function getPrice(line: Stripe.InvoiceLineItem): number | undefined {
  const price = line.price;
  if (!price || !line.quantity) {
    return undefined;
  }

  if (price.tiers_mode === 'volume' && price.tiers) {
    let currentPrice: number | undefined;
    for (const tier of price.tiers) {

      if (!tier.unit_amount) {
        continue;
      }

      if (tier.up_to === null) {
        currentPrice = tier.unit_amount / 100;
        continue
      }

      if (line.quantity <= tier.up_to) {
        currentPrice = tier.unit_amount / 100;
      }
    }
    return currentPrice;
  }

  return typeof price.unit_amount === "number" ? price.unit_amount / 100 : undefined;
}

export function toInvoiceLines(line: Stripe.InvoiceLineItem, currencySymbol?: string): InvoiceLineItem {
  return {
    id: line.id,
    amount: line.amount / 100,
    description: line.description,
    currency: line.currency,
    currencySymbol,
    periodStart: timestampToISOString(line.period.start),
    periodEnd: timestampToISOString(line.period.end),
    itemId: getInnerOptionalId(line.invoice_item ?? null),
    proration: line.proration,
    quantity: line.quantity,
    price: getPrice(line)
  };
}

export default StripeClient;
