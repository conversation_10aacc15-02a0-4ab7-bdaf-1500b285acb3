/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import <PERSON><PERSON> from 'stripe';
import StripeClient from './StripeClient';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import { ProductCodes } from '../../models/customer';
import { Referral } from '../../models/initiative';
import { getSubscriptionCodes, mustHaveScopeMapping, SubscriptionProductCode } from './subscriptionCodes';
import ContextError from "../../error/ContextError";
import type { PriceInterval } from "../../types/subscription";
import { getUnixSeconds, oneDayInSeconds } from '../../util/date';

type TrialMap = Record<string, number | undefined>;

export interface TrialEndTimestamp {
  code: SubscriptionProductCode;
  promoCode?: string;
  promotion?: Stripe.PromotionCode
}

export class ProductManager {

  // Add one extra hour to ensure user can see full days amount at the beginning
  // Has to be at least 48 hours in the future
  private readonly trialCodeMap: Record<string, TrialMap | undefined> = {
    [ProductCodes.SGXESGenome]: {
      SDGHEALTH2: (oneDayInSeconds * 2) + 3600,
      CTL7GHT: (oneDayInSeconds * 7) + 3600,
      CTL14HT: (oneDayInSeconds * 14) + 3600,
    },
    [ProductCodes.CompanyTracker]: {
      default: (oneDayInSeconds * 7) + 3600,
    },
    [ProductCodes.CompanyTrackerStarter]: {
      ASME31: (oneDayInSeconds * 30) + 3600,
      TOLI: (oneDayInSeconds * 28) + 3600,
      default: (oneDayInSeconds * 14) + 3600,
    },
  }

  constructor(
    private logger: LoggerInterface,
    private stripe: Stripe,
  ) {
  }

  public async getPriceForCode(productCode: string): Promise<string> {
    const price = await this.getPricesForProductCode({ productCode })
    return price.id;
  }

  /**
   * Avoid dealing with complex logic of default and pass in interval directly to stripe
   */
  public async getIntervalPrice({ productCode, interval }: { productCode: string, interval: PriceInterval }) {
    const product = await this.getProduct(productCode);
    if (!product) {
      throw new ContextError(`Failed to find product with metadata productCode="${productCode}"`)
    }

    const prices = await this.getPrices(product.id, {
      recurring: {
        interval,
      }
    });

    if (prices.length === 0) {
      throw new ContextError(`No valid prices found for "${productCode}" and ${interval}`);
    }

    if (prices.length > 1) {
      this.logger.error(new ContextError(`Found more than one valid price for "${productCode}" and ${interval}`));
    }

    return prices[0];
  }

  public async getPricesForProductCode({ productCode }: { productCode: string }) {

    const product = await this.getProduct(productCode);
    if (!product) {
      throw new Error(`Failed to find product with metadata productCode="${productCode}"`)
    }

    const defaultPrice = product.default_price;
    const prices = await this.getPrices(product.id);
    if (prices.length === 0) {
      throw new Error(`No valid prices found for "${productCode}"`);
    }

    if (defaultPrice) {
      const price = prices.find(p => p.id === defaultPrice);
      if (price) {
        return price;
      }

      throw new Error(`Failed to find default price for product "${productCode}"`)
    }

    if (prices.length > 1) {
      this.logger.error(`Found more than one valid price for "${productCode}"`);
    }

    return prices[0];
  }

  public getTrialEndTimestamp({ code, promoCode, promotion }: TrialEndTimestamp) {
    if (promotion?.coupon.percent_off === 100) {
      return undefined;
    }

    const mapping = this.trialCodeMap[code];
    if (mapping) {
      const trialSeconds = promoCode && mapping[promoCode] ? mapping[promoCode] : mapping.default;
      if (trialSeconds) {
        return getUnixSeconds(trialSeconds);
      }
    }
    return undefined;
  }

  public selectBestTrialCode(code: SubscriptionProductCode, referralCodes?: Referral[]): string | undefined {
    if (!referralCodes) {
      return;
    }

    const availableMap = this.trialCodeMap[code]
    if (!availableMap) {
      return;
    }

    return Object.entries(availableMap)
      .sort((a, b) => (a[1] ?? -1) - (b[1] ?? -1))
      .map(([k]) => k)
      .find(code => {
        return referralCodes.some((referral) => !referral.usedDate && referral.code === code)
      })
  }

  public async getProduct(productCode: string): Promise<Stripe.Product | undefined> {
    const productResults = await this.stripe.products.search({
      query: `active:'true' AND metadata['productCode']:"${productCode}"`,
    });

    if (productResults.data.length > 1) {
      this.logger.error(new ContextError(`Stripe is returning too many (${productResults.data.length}) products for ${productCode}`, {
        debugMessage: `Will use the first, but this indicates something is not configured correctly.`,
        products: productResults.data.map(p => ({
          id: p.id,
          name: p.name,
          active: p.active,
          metadata: p.metadata,
          created: new Date(p.created * 1000).toISOString()
        })),
        productCode,
      }));
    }

    return productResults.data[0];
  }

  private async getPrices(productId: string, extraParams?: Omit<Stripe.PriceListParams, 'expand'>) {
    const pricesData = await this.stripe.prices.list({
      active: true,
      product: productId,
      limit: 10, // Should be more than enough (default),
      ...extraParams,
      // Ensure these are always expanded.
      expand: ['data.product', 'data.tiers'],
    })

    return pricesData.data.filter(price => {
      // Should be expanded
      if (typeof price.product !== 'object' || price.product.deleted) {
        return false;
      }
      return true;
    });
  }

  public convertToSubscriptionCode(productCodes: string[]): SubscriptionProductCode {

    if (productCodes.length === 0) {
      throw new Error(`At least one product code must be provided`)
    }

    if (productCodes.length === 1) {
      return mustHaveScopeMapping(productCodes[0])
    }

    const [first, ...additional] = getSubscriptionCodes(productCodes);

    if (!first) {
      throw new Error(`Failed to find subscription code for product codes ${productCodes.join(',')}`)
    }

    if (additional.length > 0) {
      // Not dealing with logic to select "best fit" at the moment,
      // but we might have some preferences at later stage, log it for now
      this.logger.info(`Found more than one available subscription code, selected ${first}`, {
        first,
        additional,
        productCodes
      })
    }

    return mustHaveScopeMapping(first)
  }

  public getPriceById(id: string) {
    return this.stripe.prices.retrieve(id, { expand: ['tiers'] });
  }
}

let instance: ProductManager;
export const getProductManager = () => {
  if (!instance) {
    instance = new ProductManager(
      wwgLogger,
      StripeClient,
    );
  }
  return instance;
}
