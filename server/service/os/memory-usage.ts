/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


const formatMemoryUsage = (metric: number) => `${Math.round(metric / 1024 / 1024 * 100) / 100} MB`;


export const getMemoryUsage = () => {
  const memoryData = process.memoryUsage();
  return {
    // -> Resident Set Size - total memory allocated for the process execution`,
    rss: `${formatMemoryUsage(memoryData.rss)}`,
    //  -> total size of the allocated heap`,
    heapTotal: `${formatMemoryUsage(memoryData.heapTotal)}`,
    //  -> actual memory used during the execution`,
    heapUsed: `${formatMemoryUsage(memoryData.heapUsed)}`,
    //  -> V8 external memory
    external: `${formatMemoryUsage(memoryData.external)}`,
  };
}
