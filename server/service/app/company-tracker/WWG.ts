/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { ProductCodes } from '../../../models/customer';
import { AppCode, AppConfig, defaultCTLFeatures } from '../AppConfig';
import config from '../../../config';
import { availableAddonsCtl, companyTrackerServicesAgreement } from './ctl-common';
import { PERMISSION_GROUPS } from '../../../models/initiative';

export const WWG: AppConfig = {
  code: AppCode.WWG,
  productCode: ProductCodes.CompanyTracker,
  validProductCodes: [ProductCodes.CompanyTracker],
  permissionGroup: PERMISSION_GROUPS.COMPANY_TRACKER_LIGHT,
  name: 'WorldWideGeneration',
  logo: `${config.assets.cdn}/apps/wwg/wwg-logo.svg`,
  rootAppPath: 'wwg',
  onboardingPath: 'wwg',

  whitelabel: {
    logo: `${config.assets.cdn}/apps/wwg/wwg-logo-landscape.svg`,
  },
  settings: {
    overviewRecommendedAddons: ['ctl', 'tcfd', 'tcfd_standard'],
    settingsRecommendedAddons: ['ctl', 'tcfd', 'tcfd_standard'],
    availableAddons: [...availableAddonsCtl],
    baseFeatures: defaultCTLFeatures,
    companyAgreementsRequired: [
      companyTrackerServicesAgreement
    ],
  },
};
