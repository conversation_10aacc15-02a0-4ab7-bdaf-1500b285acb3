/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { ProductCodes } from '../../../models/customer';
import { AppCode, AppConfig } from '../AppConfig';
import config from '../../../config';
import { companyTrackerServicesAgreement } from './ctl-common';
import { BrandingTemplate, ProductBundle } from "../../organization/domainConfig";
import { PERMISSION_GROUPS } from '../../../models/initiative';
import { FeatureCode, FeatureTag, getFeatureDetails } from "@g17eco/core";

export const defaultCTrackerStarter: AppConfig = {
  code: AppCode.CompanyTrackerStarter,
  productCode: ProductCodes.CompanyTrackerStarter,
  validProductCodes: [ProductCodes.CompanyTrackerStarter],
  permissionGroup: PERMISSION_GROUPS.COMPANY_TRACKER_STARTER,
  name: 'Company Tracker Starter',
  logo: `${config.assets.cdn}/apps/default/Company_Tracker_logo.svg`,
  onboardingPath: 'company-tracker-starter',
  rootAppPath: 'company-tracker',

  settings: {
    overviewRecommendedAddons: ['eesg_2024', 'gri2021'],
    settingsRecommendedAddons: ['eesg_2024', 'gri2021'],
    // Only allow single pack
    availableAddons: ['eesg_2024', 'gri2021'],
    baseFeatures: [
      {
        name: 'Custom Metrics - Starter',
        code: FeatureCode.CustomMetrics,
        active: true,
        config: {
          limit: 2
        }
      },
      getFeatureDetails(FeatureTag.ReportingLevelsS),
      {
        name: 'Users - Starter',
        code: FeatureCode.Users,
        active: true,
        config: {
          limit: 5
        },
      },
      getFeatureDetails(FeatureTag.ScopePacksStarter),
      getFeatureDetails(FeatureTag.MetricAssistantAI),
      getFeatureDetails(FeatureTag.DraftFurtherExplanationAI),
      getFeatureDetails(FeatureTag.SDGInsightAI),
    ],
    betaFeatures: [FeatureTag.PPTXReportAI, FeatureTag.AIAccessDocumentLibrary, FeatureTag.AIAutoAnswer],
    companyAgreementsRequired: [
      companyTrackerServicesAgreement
    ],
    canShowTargetsAndTrends: true,
  },

  // Setup whitelabel as well to ensure we can override domainConfig if selected
  whitelabel: {
    brandingTemplate: BrandingTemplate.G17Eco,
    emailLogo: config.assets.defaultLogo,
  }
};



/**
 * This allows you to buy the product if you click one of the scope groups
 * In theory we don't need this? or this have high overlap with
 * appConfigCode provided features
 */
export const companyTrackerStarterBundle: [string, ProductBundle] = [
  ProductCodes.CompanyTrackerStarter,
  {
    scope: [
      {
        productCode: ProductCodes.CompanyTrackerStarter,
        scopeType: 'frameworks',
        code: 'eesg_2024',
        // Start enforce a single pack, therefore it is required to use
        required: true
      },
    ],
    features: [...defaultCTrackerStarter.settings.baseFeatures]
  }
];
