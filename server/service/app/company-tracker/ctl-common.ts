import { CompanyAgreement } from "../../../models/initiative"
import { AgreementConfig } from "../AppConfig";

/** All of these codes can be added to scope for free or bought if premium **/
export const availableAddonsCtl = [
  'ctl',
  'sgx_metrics',
  'sgx_extended',
  'gri',
  'gri2021',
  'tcfd',
  'tcfd_standard',
  'ungc',
  'cdsb',
  'wef',
  'sasb',
  'cdp_2022',
  // 'sam_csa',
  // 'vigeo_eiris',
  // 'bof',
  'rspo',
  'iogp',
  'ipieca',
  'issb',
  'csrd',
  // 'refinitiv',
  'asean_2024'
]

export const companyTrackerServicesAgreement: AgreementConfig<CompanyAgreement> = {
  code: CompanyAgreement.CompanyTrackerServicesAgreement,
  fromDate: new Date('2023-04-25T00:00:00.000Z'),
};
