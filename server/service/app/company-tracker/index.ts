/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { AppConfig } from '../AppConfig';
import { defaultCTBase } from './defaultCTBase';
import { defaultCTPro } from './defaultCTPro';
import { defaultCTEnterprise } from './defaultCTEnterprise';
import { SGXESGenome } from './SGXESGenome';
import { WWG } from './WWG';
import { defaultCTrackerStarter } from "./CTStarter";
import { toli } from "./toli";

export const ctAppConfigs: AppConfig[] = [
  WWG,
  toli,
  SGXESGenome,
  defaultCTrackerStarter,
  defaultCTBase,
  defaultCTPro,
  defaultCTEnterprise,
]
