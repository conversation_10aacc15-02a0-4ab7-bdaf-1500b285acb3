/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */




import { getAppConfigService } from './AppConfigService';
import { RootInitiativeData } from '../../repository/InitiativeRepository';
import { AppCode, AppConfig } from './AppConfig';
import { FeatureDetails } from "@g17eco/core";
import { ProductCodes } from "../../models/customer";

export type LookupProductCode = Pick<RootInitiativeData, 'appConfigCode'> & Required<Pick<RootInitiativeData, 'permissionGroup'>>;

export interface AppConfigProvider {

  /**
   * Handle legacy permissions where app config code is not available
   * @param initiative
   */
  getAppConfigCode(initiative: RootInitiativeData): AppCode;

  getByOrganization(initiative: RootInitiativeData): Promise<AppConfig | undefined>

  getAll(): Promise<AppConfig[]>

  getByCode(code: string): Promise<AppConfig | undefined>

  mustGetByOnboardingPath(onboardingPath: string): Promise<AppConfig>

  getPermissionGroupFeatures(initiative: Pick<RootInitiativeData, 'permissionGroup'>): FeatureDetails[]

  /**
   * Very much same as resolving AppConfig, but this always
   * return a default product code from the app.
   */
  getAppProductCode({ permissionGroup, appConfigCode }: LookupProductCode): ProductCodes;

  /** Reverse lookup of product code to app config */
  getByProductCode(productCode: ProductCodes): Promise<AppConfig[]>

  getConfigs(configCodes: string[]): Promise<AppConfig[]>;
}

let instance: AppConfigProvider;
export const getAppConfigProvider = () => {
  if (!instance) {
    instance = getAppConfigService();
  }
  return instance;
}
