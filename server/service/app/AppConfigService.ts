/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { RootInitiativeData } from '../../repository/InitiativeRepository';
import { AppConfigProvider, LookupProductCode } from './AppConfigProvider';
import { AppCode, AppConfig, CTPro2022Features } from './AppConfig';
import { ctAppConfigs } from './company-tracker';
import {
  LegacyPermissionGroups,
  CompanyTrackerProIds,
  PERMISSION_GROUPS,
  CompanyTrackerEnterpriseIds,
  PortfolioTrackerIds,
  CompanyTrackerStarterIds
} from '../../models/initiative';
import { ptAppConfigs } from "./portfolio-tracker";
import { FeatureDetails } from "@g17eco/core";
import { defaultCTBase } from "./company-tracker/defaultCTBase";
import { defaultCTEnterprise } from "./company-tracker/defaultCTEnterprise";
import { defaultCTPro } from "./company-tracker/defaultCTPro";
import { defaultCTrackerStarter } from "./company-tracker/CTStarter";
import { ProductCodes } from "../../models/customer";
import { SGXESGenome } from "./company-tracker/SGXESGenome";
import { matAppConfigs } from './materiality-tracker';

const appConfigs: AppConfig[] = [
  ...ctAppConfigs,
  ...ptAppConfigs,
  ...matAppConfigs,
];

export const validOnboardingPaths = appConfigs.filter(c => c.onboardingPath).map(c => c.onboardingPath);

type InitiativeCheck = Pick<RootInitiativeData, 'permissionGroup' | 'appConfigCode'>;

export class AppConfigService implements AppConfigProvider {
  private appConfigMap: Map<AppCode, Readonly<AppConfig>>;
  private pathToConfigMap: Map<string, Readonly<AppConfig>>;
  private CT_DEFAULT = AppCode.CompanyTracker;

  constructor(configs: AppConfig[]) {
    this.appConfigMap = new Map(configs.map(c => [c.code, c]));
    this.pathToConfigMap = new Map(configs.map(c => [c.onboardingPath, c]));
  }

  /**
   * @TODO this should prioritise appConfigCode instead of legacy permissionGroup?
   * Leaving current order
   */
  public getAppConfigCode(initiative: InitiativeCheck): AppCode {
    const permissionGroup = initiative.permissionGroup ?? PERMISSION_GROUPS.FREE;

    if (CompanyTrackerProIds.includes(permissionGroup)) {
      return AppCode.CompanyTrackerPro;
    }

    if (CompanyTrackerEnterpriseIds.includes(permissionGroup)) {
      return AppCode.CompanyTrackerEnterprise;
    }

    if (PortfolioTrackerIds.includes(permissionGroup)) {
      return AppCode.PortfolioTrackerExchange;
    }

    return initiative.appConfigCode as AppCode ?? this.CT_DEFAULT;
  }

  async getByOrganization(initiative: InitiativeCheck) {
    return this.appConfigMap.get(this.getAppConfigCode(initiative));
  }


  /**
   * This does have a lot of overlap with how we resolve
   * appConfig in getAppProductCode, maybe this should a wrapper around it?
   */
  public getPermissionGroupFeatures(initiative: Pick<RootInitiativeData, 'permissionGroup'>): FeatureDetails[] {
    // Backwards compatibility to map old permissionGroup to new features
    if (!initiative.permissionGroup) {
      return defaultCTBase.settings.baseFeatures;
    }

    if (CompanyTrackerEnterpriseIds.includes(initiative.permissionGroup)) {
      return defaultCTEnterprise.settings.baseFeatures;
    }

    if (CompanyTrackerProIds.includes(initiative.permissionGroup)) {
      // @TODO this is different compared to how resolve ConfigMap
      if (LegacyPermissionGroups.includes(initiative.permissionGroup)) {
        return CTPro2022Features;
      }
      return defaultCTPro.settings.baseFeatures;
    }

    if (CompanyTrackerStarterIds.includes(initiative.permissionGroup)) {
      return defaultCTrackerStarter.settings.baseFeatures;
    }

    // Company Tracker Default
    return defaultCTBase.settings.baseFeatures;
  }

  /**
   * Consolidated a place where we're resolving group to config or
   * config related properties.
   */
  public getAppProductCode({ permissionGroup, appConfigCode }: LookupProductCode): ProductCodes {

    if (PERMISSION_GROUPS.COMPANY_TRACKER_LIGHT === permissionGroup) {
      if (appConfigCode === SGXESGenome.code) {
        return SGXESGenome.productCode;
      }
      return defaultCTBase.productCode;
    }

    if (CompanyTrackerProIds.includes(permissionGroup)) {
      return defaultCTPro.productCode;
    }

    if (CompanyTrackerEnterpriseIds.includes(permissionGroup)) {
      return defaultCTEnterprise.productCode;
    }

    if (CompanyTrackerStarterIds.includes(permissionGroup)) {
      return defaultCTrackerStarter.productCode;
    }

    return defaultCTBase.productCode;
  }

  async getAll() {
    return Array.from(this.appConfigMap.values())
  }

  public async getByProductCode(productCode: ProductCodes): Promise<AppConfig[]> {
    return Array.from(this.appConfigMap.values()).filter(c => c.productCode === productCode);
  }

  getDefault(): AppConfig {
    const defaultAppConfig = this.appConfigMap.get(this.CT_DEFAULT);
    if (!defaultAppConfig) {
      throw new Error(`Unexpectedly failed to resolve appConfig for default CT: ${this.CT_DEFAULT}`);
    }
    return defaultAppConfig;
  }

  async getByCode(code: AppCode | string) {
    return this.appConfigMap.get(code as AppCode);
  }

  async mustGetByOnboardingPath(onboardingPath: string): Promise<AppConfig> {
    const config = this.pathToConfigMap.get(onboardingPath);
    if (!config) {
      throw new Error(`Invalid onboarding path: ${onboardingPath}`);
    }
    return config;
  }

  public async getConfigs(configCodes: string[]): Promise<AppConfig[]> {
    return configCodes.reduce((acc, code) => {
        const config = this.appConfigMap.get(code as AppCode);
        if (config) {
          acc.push(config);
        }
        return acc;
      }, [] as AppConfig[]
    );
  }
}

let instance: AppConfigService;
export const getAppConfigService = () => {
  if (!instance) {
    instance = new AppConfigService(appConfigs);
  }
  return instance;
}
