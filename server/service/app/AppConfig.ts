/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { DisplayOption, DownloadUtrvStatusCombined } from '../../types/download';
import { ProductCodes } from '../../models/customer';
import { CompanyAgreement } from '../../models/initiative';
import { UserAgreement } from '../../models/user';
import { BrandingTemplate } from '../organization/domainConfig';
import { FeatureDetails, FeatureTag, getFeatureDetails } from '@g17eco/core';
import { ActionList } from '../utr/constants';

export enum SurveyOverviewMode {
  /** @deprecated **/
  Universal,
  Scope,
  ScopeGroups,
  SdgAndScope,
}

export enum OnboardingStep {
  Signup = 'Signup',
  VerifyEmail = 'VerifyEmail',
  CompanyInfo = 'CompanyInfo',
  SectorInfo = 'SectorInfo',
  ReferralInfo = 'ReferralInfo',
  Activation = 'Activation',
  Complete = 'Complete',
  EmailDomainCompanyOnboardOrNot = 'EmailDomainCompanyOnboardOrNot',

  // Referral+Lookup page for listed companies
  SGXReferrerCodeIssuerLookup = 'SGXReferrerCodeIssuerLookup',
  IssuerCompanyOnboardOrNot = 'IssuerCompanyOnboardOrNot',

  // Materiality Tracker
  CreateAssessment = 'CreateAssessment',
  CompleteMAT = 'CompleteMAT',
}

export type AgreementType = 'user' | 'company' | 'sponsorship';

export interface AgreementConfig<T extends string = string> {
  code: T;
  type?: AgreementType;
  fromDate?: Date;
}

interface AppSettings {
  /**
   * Define what steps and what order to do the onboarding for an App
   */
  onboardingSteps?: OnboardingStep[];

  /**
   * Used by question progress circles to determine what to display or "add" to scope
   */
  overviewRecommendedAddons: string[];

  /**
   * Determine what appears at the top of scope view (core packs)
   * Also used the preferred alternative codes for SDG Contributions
   * detail view, question list and insight references etc.
   * Therefore order is important
   **/
  settingsRecommendedAddons: string[];

  /**
   * All of these codes will be split to free and premium groups and displayed
   * as option to add to the scope
   */
  availableAddons?: string[];

  /**
   * Sets the default settings on the platform
   */
  baseFeatures: FeatureDetails[];
  betaFeatures?: FeatureTag[];

  defaultSurveyOverviewMode?: SurveyOverviewMode;
  requiresTermsAndConditions?: boolean;
  userAgreementsRequired?: AgreementConfig<UserAgreement>[];
  companyAgreementsRequired?: AgreementConfig<CompanyAgreement>[];
  canViewAllPacks?: boolean;
  canEditInsightOverview?: boolean;
  canShowSDGDetails?: boolean;
  defaultDownloadOptions?: {
    metricStatuses?: (DownloadUtrvStatusCombined | ActionList)[];
    metricOverrides?: DisplayOption[];
  };
  /** Control the visibility of targets and trends section on the survey overview page */
  canShowTargetsAndTrends?: boolean;
}

// This is stored inside initiative.appConfigCode
export enum AppCode {
  WWG = 'wwg',
  TOLI = 'toli',
  SGXESGenome = 'sgx_esgenome',
  /** Even lower level than company tracker **/
  CompanyTrackerStarter = 'company_tracker_starter',
  CompanyTracker = 'company_tracker_light',
  CompanyTrackerPro = 'company_tracker_pro',
  CompanyTrackerEnterprise = 'company_tracker_enterprise',
  PortfolioTrackerExchange = 'portfolio_tracker_exchange',
  PortfolioTrackerExchangeSGX = 'portfolio_tracker_exchange_sgx',
  MaterialityTracker = 'materiality_assessment',
}

export interface AppConfig {
  code: AppCode;
  productCode: ProductCodes;
  validProductCodes: ProductCodes[];
  permissionGroup: string;

  // App Setup
  name: string;
  logo: string;
  rootAppPath: string;
  onboardingPath: string;

  /** Overall website change **/
  whitelabel?: {
    logo?: string;
    emailLogo?: string;
    brandingTemplate?: BrandingTemplate;
  };
  /** App specific changes **/
  settings: AppSettings;
}

export const defaultCTLFeatures: FeatureDetails[] = [
  getFeatureDetails(FeatureTag.CustomMetricsS),
  getFeatureDetails(FeatureTag.ReportingLevelsS),
  getFeatureDetails(FeatureTag.UsersS),
  getFeatureDetails(FeatureTag.ScopePacksBasic),
]

export const defaultCTProFeatures: FeatureDetails[] = [
  getFeatureDetails(FeatureTag.AdminDashboard),
  getFeatureDetails(FeatureTag.BulkImporting),
  getFeatureDetails(FeatureTag.CombinedReport),
  getFeatureDetails(FeatureTag.CombinedSurvey),
  getFeatureDetails(FeatureTag.CustomMetricsM),
  getFeatureDetails(FeatureTag.ReportingLevelsM),
  getFeatureDetails(FeatureTag.SurveyTemplates),
  getFeatureDetails(FeatureTag.UsersM),
  getFeatureDetails(FeatureTag.Verification),
  getFeatureDetails(FeatureTag.BulkDelegation),
  getFeatureDetails(FeatureTag.CustomDashboards),
  getFeatureDetails(FeatureTag.ScopePacksBasic),
]

export const defaultCTEnterpriseFeatures: FeatureDetails[] = [
  ...defaultCTProFeatures,
  getFeatureDetails(FeatureTag.Assurance),
  getFeatureDetails(FeatureTag.CustomMetricsL),
  getFeatureDetails(FeatureTag.ReportingLevelsL),
  getFeatureDetails(FeatureTag.UsersL),
  getFeatureDetails(FeatureTag.NoteInstructions),
  getFeatureDetails(FeatureTag.Workgroups3),
];

export const CTPro2022Features: FeatureDetails[] = [
  ...defaultCTProFeatures,
  {
    ...getFeatureDetails(FeatureTag.CustomMetricsM),
    config: {
      limit: 50
    }
  },
  {
    ...getFeatureDetails(FeatureTag.ReportingLevelsM),
    config: {
      limit: 40
    }
  }
]

export enum ProductAppType {
  MaterialityTracker = 'materiality-tracker',
  CompanyTracker = 'company-tracker',
  SGXESGenome = 'sgx-esgenome',
  PortfolioTracker = 'portfolio-tracker',
  AssuranceTracker = 'assurance-tracker',
}
