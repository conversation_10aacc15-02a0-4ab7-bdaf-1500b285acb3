/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { AppCode, AppConfig } from '../AppConfig';
import { ProductCodes } from "../../../models/customer";
import { PERMISSION_GROUPS } from "../../../models/initiative";
import config from "../../../config";
import { BrandingTemplate, sgxLogo } from "../../organization/domainConfig";
import { FeatureTag, getFeatureDetails } from "@g17eco/core";

const ptDefaultExchange: AppConfig = {
  code: AppCode.PortfolioTrackerExchange,
  productCode: ProductCodes.PortfolioTracker,
  validProductCodes: [ProductCodes.PortfolioTracker],
  permissionGroup: PERMISSION_GROUPS.PORTFOLIO_TRACKER_EXCHANGE,
  name: 'Portfolio Tracker Exchange',
  logo: `${config.assets.cdn}/apps/default/Portfolio_Tracker_logo.svg`,
  rootAppPath: 'portfolio-tracker',
  onboardingPath: 'portfolio-tracker-exchange',

  settings: {
    overviewRecommendedAddons: ['ctl'],
    settingsRecommendedAddons: ['ctl'],
    baseFeatures: [
      getFeatureDetails(FeatureTag.CustomMetricsL),
      getFeatureDetails(FeatureTag.UsersL),
      getFeatureDetails(FeatureTag.ReportingLevelsS),
    ],
  },
  // Setup whitelabel as well to ensure we can override domainConfig if selected
  whitelabel: {
    brandingTemplate: BrandingTemplate.G17Eco,
    emailLogo: config.assets.defaultLogo,
  }
};

const ptExchangeSgx: AppConfig = {
  code: AppCode.PortfolioTrackerExchangeSGX,
  productCode: ProductCodes.PortfolioTracker,
  validProductCodes: [ProductCodes.PortfolioTracker],
  permissionGroup: PERMISSION_GROUPS.PORTFOLIO_TRACKER_EXCHANGE,

  name: 'Portfolio Tracker Exchange SGX',
  logo: `${config.assets.cdn}/apps/default/Portfolio_Tracker_logo.svg`,
  rootAppPath: 'portfolio-tracker',
  onboardingPath: 'portfolio-tracker-exchange-sgx',

  settings: {
    overviewRecommendedAddons: ['ctl'],
    settingsRecommendedAddons: ['ctl'],
    baseFeatures: [
      getFeatureDetails(FeatureTag.CustomMetricsL),
      getFeatureDetails(FeatureTag.UsersL),
      getFeatureDetails(FeatureTag.ReportingLevelsS),
    ],
  },
  // Setup whitelabel as well to ensure we can override domainConfig if selected
  whitelabel: {
    brandingTemplate: BrandingTemplate.SGX,
    emailLogo: sgxLogo,
  }
};



export const ptAppConfigs: AppConfig[] = [
  ptDefaultExchange,
  ptExchangeSgx
];
