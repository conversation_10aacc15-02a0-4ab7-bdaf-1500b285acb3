/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { AppConfigListener, UserEventListener } from "../event/AppEventEmitter";
import ContextError from "../../error/ContextError";
import { LoggerInterface, wwgLogger } from "../wwgLogger";
import { AppCode } from "./AppConfig";
import Initiative, { InitiativePlain } from "../../models/initiative";
import { getUserPermissionService, UserPermissionService } from "../user/UserPermissionService";
import { UserRoles } from "../user/userPermissions";
import { Actions } from "../action/Actions";
import { ObjectId } from "bson";
import { UserPlain } from "../../models/user";

interface UsersToMT {
  demoId: ObjectId;
  users: Pick<UserPlain, '_id' | 'permissions'>[];
}

export class ToliEventListener {

  /**
   * Could be part of config or array of initiative codes
   * Keep it simple and have hardcoded single version here
   */
  private readonly toliDemoCode = 'platform-toli-demo';

  // Cache it, so we only resolve it once
  private toliDemoAccess: undefined | ObjectId | null = undefined;

  constructor(
    private logger: LoggerInterface,
    private userPermissionService: UserPermissionService,
  ) {
  }


  // On App Config change, should do something too.
  public onAppConfigChange: AppConfigListener = async (data) => {
    const { initiative, after, before } = data;

    if (before.appConfigCode === after.appConfigCode) {
      this.logger.error(new ContextError(`AppConfigEventLister received same appConfigCode`, {
        initiativeId: initiative._id.toHexString(),
        before,
        after,
      }));
      return;
    }

    if (!this.isNowTOLI(data)) {
      return;
    }

    const demoMTId = await this.getDemoId();
    if (!demoMTId) {
      return;
    }

    // Add permissions to the initiative
    const users = await this.userPermissionService.getInitiativeUsers(initiative._id);

    return this.addUsersToDemoInitiative({ demoId: demoMTId, users });
  }

  private isNowTOLI = ({ before, after }: Parameters<AppConfigListener>[0]) => {
    return before.appConfigCode !== AppCode.TOLI && after.appConfigCode === AppCode.TOLI;
  }

  private addUsersToDemoInitiative = ({ demoId, users }: UsersToMT) => {
    const idToAdd = demoId.toHexString();
    const usersToAdd = users.filter((user) => {
      if (!user.permissions) {
        return true;
      }
      return user.permissions.every(p => p.initiativeId.toHexString() !== idToAdd);
    });

    this.logger.info(`AppConfigEventLister adding demo access to ${usersToAdd.length} users`, {
      demoInitiativeId: demoId.toHexString(),
    });

    return this.userPermissionService.addInitiativeUserPermissions({
      userIds: usersToAdd.map((u) => u._id),
      permission: { initiativeId: demoId, permissions: [UserRoles.Viewer] },
    });
  }
  private getDemoId = async () => {
    if (this.toliDemoAccess === undefined) {
      const demoInitiative = await Initiative.findOne({
        code: this.toliDemoCode
      }, { _id: 1 }).lean<Pick<InitiativePlain, '_id'>>().exec();

      if (!demoInitiative) {
        this.logger.error(new ContextError(`AppConfigEventLister failed to find initiative`, {
          code: this.toliDemoCode,
        }));
      }

      this.toliDemoAccess = demoInitiative ? demoInitiative._id : null;
    }

    return this.toliDemoAccess;
  }

  /**
   * Specifically dealing with MT, no need to care about recursive parents for now
   */
  public onUserPermissionChange: UserEventListener = async (data) => {
    const addActions = data.filter(({ action }) => action === Actions.Add);
    if (!addActions.length) {
      return;
    }

    this.logger.info(`UserPermissionEventLister onUserPermissionChange ${addActions.length} add actions`);
    const initiativeIds = addActions.map(event => event.initiativeId);

    const initiatives = await Initiative.find({
      _id: { $in: initiativeIds },
      appConfigCode: AppCode.TOLI,
    }, { _id: 1 }).lean<{ _id: ObjectId }[]>().exec();

    this.logger.info(`UserPermissionEventLister found ${initiatives.length} TOLI initiatives`, {
      initiativeIds: initiatives.map(c => c._id.toHexString()),
    });

    if (!initiatives.length) {
      return;
    }

    const demoId = await this.getDemoId();
    if (!demoId) {
      return;
    }

    return this.addUsersToDemoInitiative({
      demoId,
      users: addActions.map(({ user }) => user),
    });
  }
}

let instance: ToliEventListener;
export const getToliEventListener = () => {
  if (!instance) {
    instance = new ToliEventListener(
      wwgLogger,
      getUserPermissionService(),
    );
  }
  return instance;
}
