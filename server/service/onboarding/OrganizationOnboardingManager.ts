import { Logger } from 'winston';
import Onboarding, {
  ObType,
  OnboardingStatus,
  OrganizationOnboardingCreateData,
  OrganizationOnboardingModel,
  OrganizationOnboardingPlain,
} from '../../models/onboarding';
import { UserModel } from '../../models/user';
import { createUserEventService, UserEventService } from '../event/UserEventService';
import { modifyProfile } from '../file/profile';
import { wwgLogger } from '../wwgLogger';
import { ONBOARDING } from '../event/Events';
import { createOnboardingCrmService, OnboardingCrmService } from './OnboardingCrmManager';
import { OrganizationRepository } from '../../repository/OrganizationRepository';
import { mergePermissions } from '../assurance/utils';
import { OrganizationPermission } from '../../models/assurancePermission';
import Organization, { OrganizationPermissionsMin } from '../../models/organization';
import {
  getOrganizationOnboardingRepository,
  OrganizationOnboardingRepository,
} from '../../repository/OrganizationOnboardingRepository';
import { ObjectId } from 'bson';
import { OrganizationEmailTemplate } from '../email/templates/onboarding/organizationInitial';
import ContextError from '../../error/ContextError';

interface OnboardingResult {
  success: boolean;
}

export class OrganizationOnboardingManager {
  constructor(
    private onboardingCrm: OnboardingCrmService,
    private logger: Logger,
    private userEventService: UserEventService,
    private organizationOnboardingRepo: OrganizationOnboardingRepository
  ) {}

  public getEmailTemplate(permissions: OrganizationPermission[]) {
    if (permissions.some((role) => [OrganizationPermission.Admin].includes(role))) {
      return OrganizationEmailTemplate.Manager;
    }

    if (
      permissions.some((role) =>
        [OrganizationPermission.Assurer, OrganizationPermission.Viewer].includes(role)
      )
    ) {
      return OrganizationEmailTemplate.User;
    }

    return OrganizationEmailTemplate.Guest;
  }

  private async executeStart(ids: (string | ObjectId)[]) {

    const objectIds = ids.map(id => new ObjectId(id));
    const onboardingModels = await this.organizationOnboardingRepo.find({
      _id: { $in: objectIds },
      status: OnboardingStatus.NotStarted,
    });

    const actionResult = { count: 0, failCount: 0 };
    const organizations = await Organization.find({
      _id: { $in: onboardingModels.map(m => m.organizationId) },
    }, { _id: 1, name: 1 }).lean().exec();

    const organizationMap = new Map(organizations.map((o) => {
      return [o._id.toString(), { _id: o._id, name: o.name }];
    }));

    for (const model of onboardingModels) {
      try {
        const organizationId = String(model.organizationId);
        const details = await this.onboardingCrm.organizationOnboard(
          model,
          organizationMap.get(organizationId),
        );
        actionResult.count++;

        model.status = OnboardingStatus.Pending;
        model.startDate = new Date();
        await model.save();

        const userId = model.user.userId ? model.user.userId.toString() : undefined;
        this.userEventService.emit({
          userId: userId,
          email: model.user.email,
          eventDate: new Date(),
          service: ONBOARDING.service,
          event: ONBOARDING.events.start,
          data: { message: 'Started onboarding process. Added user to CRM', ...details }
        }).catch(e => this.logger.error(e.message));
      } catch (e) {
        this.logger.error(e);
        actionResult.failCount++;
      }
    }

    return actionResult;
  }

  private createOnboarding(data: OrganizationOnboardingCreateData) {
    const model = new Onboarding(data);
    return model.save();
  }

  public async onboardEmail({
    email,
    organization,
    delegator,
    permissions,
  }: {
    email: string;
    organization: Pick<OrganizationPermissionsMin, '_id'>;
    delegator: Pick<UserModel, '_id'>;
    permissions: OrganizationPermission[];
  }) {
    if (!permissions.length) {
      throw new ContextError(`permissions are required`, {
        email,
        delegatorId: delegator._id,
        organizationId: organization._id,
      });
    }

    // Look up existing
    const existingOnboardings = await this.organizationOnboardingRepo.findExistingByOrganizationId({
      email,
      organizationId: organization._id,
    });

    if (existingOnboardings.length > 0) {
      for (const onboarding of existingOnboardings) {
        onboarding.organizationRoles = onboarding.organizationRoles.map((orgRole) => {
          let orgRolePermissions = orgRole.permissions;
          if (orgRole.modelId.equals(organization._id)) {
            orgRolePermissions = [...new Set([...permissions, ...orgRolePermissions])];
          }
          return {
            ...orgRole,
            permissions: orgRolePermissions,
          };
        });
        await onboarding.save();
        if (onboarding.status === OnboardingStatus.NotStarted) {
          await this.executeStart([onboarding._id.toString()]);
        }
      }
      return;
    }

    const data: OrganizationOnboardingCreateData = {
      type: ObType.Organization,
      user: {
        complete: false,
        permissions: [],
        emailTemplate: this.getEmailTemplate(permissions),
        email: email,
      },
      organizationId: organization._id,
      createdBy: delegator._id,
      organizationRoles: [
        {
          modelId: organization._id,
          permissions,
        },
      ],
    };


    const onboarding = await this.createOnboarding(data);

    await this.executeStart([onboarding._id.toString()]);
  }

  public async updateOrgPermissions({ organizationId, userId, permissions}: { organizationId: ObjectId; userId: ObjectId; permissions: OrganizationPermission[] }) {
    const organization = await OrganizationRepository.mustFindById(organizationId);

    organization.permissions = mergePermissions({
      currentPermissions: organization.permissions ?? [],
      upcomingPermissions: [{ userId, permissions }],
    });

    return organization.save();
  }

  private async addUserToOrg(onboarding: OrganizationOnboardingPlain, user: UserModel) {
    // Expect the organization roles to have one item only
    if (onboarding.organizationRoles.length !== 1) {
      this.logger.error(
        new ContextError('Assurance onboarding should have only one organization role', {
          onboardingId: onboarding._id,
          userId: user._id,
        })
      );
    }
    const [orgRole] = onboarding.organizationRoles;

    const metadata = {
      userId: user._id.toString(),
      organizationId: onboarding.organizationId.toString(),
      roleOrganizationId: orgRole.modelId.toString(),
    };

    if (!onboarding.organizationId.equals(orgRole.modelId)) {
      this.logger.error(
        new ContextError(`Assurance onboarding has different organizationId than organization role id`, metadata)
      );
    }

    this.logger.info('Adding user to assurance organization', metadata);

    await this.updateOrgPermissions({
      organizationId: onboarding.organizationId,
      userId: user._id,
      permissions: orgRole.permissions,
    });

    // Re-add the organizationId to allow users to pass organizationId checks
    // TODO: Remove this once organizationId is finished cleaning up
    if (!user.organizationId) {
      user.organizationId = onboarding.organizationId;
      await user.save();
    }
  }

  public async acceptOrganizationOnboarding(
    onboarding: OrganizationOnboardingModel,
    user: UserModel
  ): Promise<OnboardingResult> {
    if (!user.profile && onboarding.user?.profile) {
      await modifyProfile({ model: user, url: onboarding.user.profile, type: 'user' });
      await user.save();
    }

    if (!onboarding.organizationRoles || onboarding.organizationRoles.length === 0) {
      return { success: true };
    }

    try {
      await this.addUserToOrg(onboarding.toObject(), user);
      const msg = `Successfully onboard organization ${onboarding.organizationId} for user ${user.email}.`;
      this.logger.info(msg);
      this.userEventService
        .emit({
          userId: user._id.toString(),
          email: user.email,
          eventDate: new Date(),
          service: ONBOARDING.service,
          event: ONBOARDING.events.accept,
          data: { message: msg, onboarding: onboarding._id.toString() },
        })
        .catch((e) => this.logger.error(e.message));

      onboarding.status = OnboardingStatus.Complete;
      await onboarding.save();
      await this.onboardingCrm.removeOnboarding(onboarding);
      return { success: true };
    } catch (e) {
      this.logger.error(
        new ContextError(`Failed to execute onboarding ${onboarding._id} for user ${user.email}`, {
          cause: e,
          userId: user._id.toString(),
        })
      );
      return { success: false };
    }
  }
}

let instance: OrganizationOnboardingManager;
export const createOrganizationOnboardingManager = () => {
  return new OrganizationOnboardingManager(
    createOnboardingCrmService(),
    wwgLogger,
    createUserEventService(),
    getOrganizationOnboardingRepository()
  );
};

export const getOrganizationOnboardingManager = () => {
  if (!instance) {
    instance = createOrganizationOnboardingManager();
  }

  return instance;
};
