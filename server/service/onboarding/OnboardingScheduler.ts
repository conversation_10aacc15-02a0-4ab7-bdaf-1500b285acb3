/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { getOnboardingEmailService, OnboardingEmailService } from './OnboardingEmailService';
import { getOnboardingRepository, OnboardingRepository } from '../../repository/OnboardingRepository';
import {
  ObNotificationCode,
  ObNotificationService,
  OnboardingStatus,
  OnboardingWithNotifications
} from '../../models/onboarding';
import moment from 'moment';
import { LoggerInterface, wwgLogger } from '../wwgLogger';

interface PendingOnboardings {
  notificationCode: ObNotificationCode;
  service?: ObNotificationService;
  limit?: number;
}

/**
 * Onboarding reminder scheduler
 *
 * Main functionality is responsible for sending reminder emails as replacement
 * for CRM functionality that is now deprecated.
 */
export class OnboardingScheduler {
  private remindersInDays = {
    [ObNotificationCode.FirstReminder]: 1,
    [ObNotificationCode.FinalReminder]: 7,
  };

  constructor(
    private emailService: OnboardingEmailService,
    private obRepo: OnboardingRepository,
    private logger: LoggerInterface
  ) {}

  public async process(obNotificationRun: PendingOnboardings) {
    const { notificationCode, service = ObNotificationService.Local, limit = 100 } = obNotificationRun;

    const onboardings = await this.getOnboardings(notificationCode, service, limit);

    const updates = onboardings.map(async (ob) => {
      try {
        this.ensureDoesNotHaveNotificationCode({ ob, code: notificationCode, type: 'email' });

        const updateItem = await this.emailService.handleFollowUpEmail(ob, notificationCode);

        return {
          _id: ob._id,
          initiativeId: ob.initiativeId,
          type: 'email',
          code: notificationCode,
          status: 'success',
          completedDate: updateItem.completedDate.toISOString(),
        };
      } catch (e) {
        this.logger.error(e, { onboardingId: ob._id, notificationCode });
        return {
          _id: ob._id,
          initiativeId: ob.initiativeId,
          status: 'error',
        };
      }
    });

    return Promise.all(updates);
  }

  private async getOnboardings(notificationCode: ObNotificationCode, service: ObNotificationService, limit: number) {
    switch (notificationCode) {
      case ObNotificationCode.FirstReminder:
        return (await this.obRepo.find(
          {
            status: OnboardingStatus.Pending,
            created: { $lte: moment().subtract(this.remindersInDays[notificationCode], 'day').toDate() },
            'notifications.service': service,
            'notifications.items': [], // empty array, as it's get created automatically
          },
          undefined,
          { limit }
        )) as OnboardingWithNotifications[];
      case ObNotificationCode.FinalReminder: {
        const date = moment().subtract(this.remindersInDays[notificationCode], 'day').toDate();
        return (await this.obRepo.find(
          {
            status: OnboardingStatus.Pending,
            'notifications.service': service,
            'notifications.items.code': { $ne: ObNotificationCode.FinalReminder },
            'notifications.items': {
              $elemMatch: {
                code: { $eq: ObNotificationCode.FirstReminder },
                completedDate: { $lte: date },
              },
            },
          },
          undefined,
          { limit }
        )) as OnboardingWithNotifications[];
      }
      default:
        throw new Error(`Not supported notification code ${notificationCode}`);
    }
  }

  private ensureDoesNotHaveNotificationCode({
    ob,
    code,
    type,
  }: {
    ob: OnboardingWithNotifications;
    code: ObNotificationCode;
    type: string;
  }) {
    const hasOb = ob.notifications.items.some((item) => {
      return item.type === type && item.code === code && code !== ObNotificationCode.ManualReminder;
    });

    if (hasOb) {
      throw new Error(`Update notification ${type} and ${code} already exists for onboarding ${ob._id}`);
    }
  }
}

let instance: OnboardingScheduler;
export const getOnboardingScheduler = () => {
  if (!instance) {
    instance = new OnboardingScheduler(
      getOnboardingEmailService(),
      getOnboardingRepository(),
      wwgLogger,
    );
  }
  return instance;
}
