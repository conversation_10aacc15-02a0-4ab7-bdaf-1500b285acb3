/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import User, { UserModel } from '../../models/user';
import Onboarding, { InitiativeOnboardingModel, ObType, OnboardingModel } from '../../models/onboarding';
import Initiative, { InitiativeModel } from '../../models/initiative';
import { InitiativeManager } from '../initiative/InitiativeManager';
import InitiativeGroup from '../../models/initiativeGroup';
import OnboardingList from '../../models/onboardingList';
import { ObjectId } from 'bson';
import { OnboardingImportData } from './OnboardingData';
import { EmailTemplate } from '../email/templates/onboarding/initial';
import { DataPeriods, UtrvType } from '../utr/constants';
import { filterValidRoles } from '../user/userPermissions';
import { SurveyQuery } from '../survey/SurveyQuery';
import { SurveyType } from '../../models/survey';
import {
  getInitiativeOnboardingRepository,
  InitiativeOnboardingRepository,
} from '../../repository/InitiativeOnboardingRepository';

export class OnboardingImporter {

  private static async createUserData(onboarding: OnboardingModel, body: OnboardingImportData) {

    if (!onboarding.user) {
      onboarding.user = {
        email: body.userEmail,
        complete: false,
        permissions: [],
      };
    }

    const user = await User.findOne({ email: body.userEmail }).exec();
    if (user) {
      onboarding.user.complete = true;
      onboarding.user.userId = user._id;
      onboarding.user.firstName = user.firstName;
      onboarding.user.surname = user.surname;
    } else {
      if (body.userFirstName) {
        onboarding.user.firstName = body.userFirstName;
      }
      if (body.userSurname) {
        onboarding.user.surname = body.userSurname;
      }
      if (body.userProfile) {
        onboarding.user.profile = body.userProfile;
      }
    }

    if (body.userInitiatives && body.userDashboardPermissions) {
      const codes = body.userInitiatives.split(',');
      const initiatives = await Initiative.find({ code: { $in: codes } }).lean().exec();

      if (codes.length !== initiatives.length) {
        throw new Error(`Failed to resolve all the user initiative codes ${body.userInitiatives}`);
      }

      const permissions = filterValidRoles(body.userDashboardPermissions.split(','));
      onboarding.user.permissions = initiatives.map((i) => ({
        initiativeId: i._id,
        permissions,
      }));
    }

    const isAdmin = String(body.userIsAdmin).trim().toLowerCase() === 'true';
    onboarding.user.emailTemplate = isAdmin ? EmailTemplate.Manager : EmailTemplate.SurveyContributorVerifierAssurer;

    return onboarding;
  }

  private static async importOnboardingList(id?: ObjectId | string, code?: string, name?: string) {

    if (!id && !code) {
      return;
    }

    let onboardingList;
    if (id) {
      onboardingList = await OnboardingList.findById(id);
      if (!onboardingList) {
        throw new Error('Invalid onboardingListId');
      }
      return onboardingList;
    }

    onboardingList = await OnboardingList.findOne({ code: code });
    if (!onboardingList && name && code) {
      onboardingList = new OnboardingList();
      onboardingList.name = name;
      onboardingList.code = code;
      await onboardingList.save();
    }

    return onboardingList;
  }

  constructor(private onboardingRepo: InitiativeOnboardingRepository) {
  }

  public async processImport(body: OnboardingImportData, user: UserModel): Promise<InitiativeOnboardingModel> {

    const onboardingList = await OnboardingImporter.importOnboardingList(
      body.onboardingListId,
      body.onboardingListCode,
      body.onboardingListName,
    );

    if (!body.userEmail || !body.initiativeCode) {
      throw new Error('Missing required userEmail or initiativeCode');
    }

    let initiative: InitiativeModel | null = null;
    let onboarding: InitiativeOnboardingModel | null = null;

    if (body.initiativeCode) {
      initiative = await Initiative.findOne({ code: body.initiativeCode }).exec();

      // Check for existing onboarding
      if (initiative) {
        const existing = await this.onboardingRepo.findExisting(body.userEmail, initiative._id);
        if (existing) {
          onboarding = existing;
        }
      } else {
        initiative = await InitiativeManager.createChildInitiative(body);
      }
    }

    if (!initiative) {
      throw new Error(`initiativeCode is required for survey setup. initiativeCode "${body.initiativeCode}". Aborting.`);
    }

    const initiativeId = initiative._id.toString();

    onboarding = new Onboarding({
      ...body,
      type: ObType.Initiative,
      initiativeId: initiative._id,
    }) as InitiativeOnboardingModel;

    if (onboardingList) {
      onboarding.onboardingListId = onboardingList._id;
    }

    // add who created it
    if (!onboarding.createdBy) {
      onboarding.createdBy = user._id;
    }

    if (body.initiativeGroupId) {
      const initiativeGroup = await InitiativeGroup.findById(body.initiativeGroupId).exec();
      if (!initiativeGroup) {
        throw new Error('Invalid InitiativeGroup. Aborting.');
      }
      if (!body.weight) {
        throw new Error('InitiativeGroup requires \'weight\'. Aborting.');
      }
      const initiativeInGroup = initiativeGroup.group.find((g) => g.initiativeId.toString() === initiativeId);
      if (!initiativeInGroup) {
        const initiativeGroupInitiative = {
          initiativeId: initiative._id,
          weight: Number(body.weight)
        };
        initiativeGroup.group.push(initiativeGroupInitiative);
        await initiativeGroup.save();
      }
    }

    if (body.surveySourceName) {

      const requiredFields: (keyof OnboardingImportData)[] = [
        'surveyInstanceCode',
        'surveyEffectiveDate',
        'surveyUtrvType',
        'surveyEvidenceRequired',
        'surveyVerificationRequired'
      ];
      for (const fieldName of requiredFields) {
        if (!body[fieldName]) {
          throw new Error('Cannot import surveySourceName without field for ' + fieldName + '. Aborting.');
        }
      }
      if (!['actual', 'baseline', 'target'].includes(body.surveyUtrvType as string)) {
        throw new Error('Invalid surveyUtrvType ' + body.surveyUtrvType + '. Aborting.');
      }
      onboarding.surveyConfig = {
        code: body.surveyInstanceCode as string,
        sourceName: body.surveySourceName,
        period: DataPeriods.Yearly,
        effectiveDate: new Date(body.surveyEffectiveDate as string),
        utrvType: body.surveyUtrvType as UtrvType,
        evidenceRequired: (body.surveyEvidenceRequired?.toLowerCase() === 'true'),
        verificationRequired: (body.surveyVerificationRequired?.toLowerCase() === 'true'),
        complete: false
      };
    }

    await OnboardingImporter.createUserData(onboarding, body);
    return onboarding;
  }

  public async checkOnboardingData(model: InitiativeOnboardingModel): Promise<string[]> {
    if (!model.surveyConfig) {
      return [];
    }

    const conflictingSurveys = await SurveyQuery.findBy({
      initiativeId: model.initiativeId,
      effectiveDate: model.surveyConfig.effectiveDate,
      sourceName: model.surveyConfig.sourceName,
      type: model.surveyConfig.type ?? SurveyType.Default,
      period: model.surveyConfig.period,
    });

    const messages: string[] = [];

    conflictingSurveys.forEach(conflictingSurvey => {
      if (conflictingSurvey && model.surveyConfig?.code !== conflictingSurvey.code) {
        const { name, _id, sourceName, initiativeId, period } = conflictingSurvey;
        const date = model.surveyConfig?.effectiveDate.toISOString();
        messages.push(`Found conflicting survey ${period} ${name} (${_id}) for "${sourceName}" and initiative ${initiativeId} and date ${date}.`);
      }
    })

    return messages;
  }
}

export const getOnboardingImporter = () => {
  return new OnboardingImporter(getInitiativeOnboardingRepository());
};
