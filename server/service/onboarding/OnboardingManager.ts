/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import { createOnboardingCrmService, OnboardingCrmService } from './OnboardingCrmManager';
import { createOnboardingEmailService, OnboardingEmailService } from './OnboardingEmailService';
import { createUserEventService, UserEventService } from '../event/UserEventService';
import { createDelegationManager, DelegationManager } from '../delegation/DelegationManager';
import Onboarding, {
  hasAssurance,
  hasSurveyConfig,
  hasUtrvConfig,
  InitiativeOnboardingModel,
  InitiativeOnboardingPlain,
  isInitiativeOnboardingModel,
  isOrganizationOnboardingModel,
  ObType,
  OnboardingCommon,
  OnboardingCreateData,
  OnboardingModel,
  OnboardingModelPlain,
  OnboardingStatus,
  OnboardingUser,
  OnboardingWithAssurance,
  OnboardingWithSurveyConfig,
  OnboardingWithUtrConfig,
  SurveyRoles,
} from '../../models/onboarding';
import Initiative from '../../models/initiative';
import User, { User<PERSON>ode<PERSON>, User<PERSON>lain } from '../../models/user';
import { Logger } from 'winston';
import { wwgLogger } from '../wwgLogger';
import { getUserManager } from '../user/UserManager';
import { getSurveyManager, SurveyManager } from '../survey/SurveyManager';
import Survey, { SurveyModel } from '../../models/survey';
import { ONBOARDING } from '../event/Events';
import { UniversalTrackerValueModel } from '../../models/universalTrackerValue';
import { StakeholderGroupManager, StakeholderGroupUpdate } from '../stakeholder/StakeholderGroupManager';
import { getAllIds, updateStakeholders } from '../stakeholder/StakeholderUtil';
import { OnboardingOutcome } from '../crm/CrmClient';
import { StakeholderGroup } from '../../models/stakeholderGroup';
import UserError from '../../error/UserError';
import { InitiativeRepository } from '../../repository/InitiativeRepository';
import { AssurancePortfolioModel } from '../assurance/model/AssurancePortfolio';
import AssurancePortfolio from '../../models/assurancePortfolio';
import { Actions } from '../action/Actions';
import { VisibleStakeholders } from '../survey/VisibleStakeholders';
import { OnboardingWithRole, SurveyUserRoles } from '../survey/SurveyUsers';
import { EmailTemplate } from '../email/templates/onboarding/initial';
import { isDelegationScopeEmpty, processUserUpdate } from "../survey/surveyDelegationProcess";
import { getUserPermissionService } from "../user/UserPermissionService";
import { UniversalTrackerValueRepository } from '../../repository/UniversalTrackerValueRepository';
import config from '../../config';
import { UserRoles } from '../user/userPermissions';
import { UniversalTrackerValueDelegation } from '../delegation/UniversalTrackerValueDelegation';
import { getNotificationManager, NotificationManager } from '../notification/NotificationManager';
import { tryGetContext } from '../../middleware/audit/contextMiddleware';
import { AssurancePermissions, MAP_ROLES } from '../assurance/AssurancePortfolioPermissions';
import { AssurancePortfolioPermission, OrganizationPermission } from '../../models/assurancePermission';
import { InitiativePermissions } from '../initiative/InitiativePermissions';
import { addGroupPermissions } from '../user/userGroups';
import { AuditLogger, getAuditLogger } from '../audit/AuditLogger';
import { InitiativeAudit } from '../audit/events/Initiative';
import { modifyProfile } from "../file/profile";
import { ProcessMassOnboardingDelegation } from '../survey/model/DelegationScope';
import { SurveyRepository } from '../../repository/SurveyRepository';
import { getInitiativeOnboardingRepository, InitiativeOnboardingRepository } from '../../repository/InitiativeOnboardingRepository';
import { getOrganizationOnboardingManager, OrganizationOnboardingManager } from './OrganizationOnboardingManager';
import { UtrvIds } from '../../types/universalTrackerValue';

enum OnboardingAction {
  Start = 'start',
}

export enum OnboardingUserDecision {
  Accept = 'accept',
  Reject = 'reject',
}

interface OnboardingResult {
  success: boolean;
  survey?: SurveyModel;
}

export type StakeholderTypes = 'stakeholder' | 'verifier' | 'escalation';

export type BulkPermissions = {initiativeId: ObjectId, permissions: UserRoles[]}[]
export interface OnboardingEnrollData {
  userId?: string;
  email: string;
  permissions: UserRoles[];
  bulkPermissions?: BulkPermissions;
  onboardingListId?: ObjectId;
}

export interface MultipleOnboardingEnrollData {
  emails: string[];
  permissions: UserRoles[];
}

export interface JoinOnboardingData {
  initiativeIds: string[];
  firstName: string;
  lastName: string;
  email: string;
  message?: string;
}

type StakeholderGroupProperty = 'surveyStakeholders' | 'assuranceStakeholders' | 'utrvConfig';

export type OnboardingUtrvUser = OnboardingWithRole & {
  status: OnboardingStatus;
  _id: ObjectId;
  count: { contributed: number; verified: number };
  utrvIds: UtrvIds;
  user: Pick<OnboardingUser, 'permissions'>;
};

export class OnboardingManager {

  constructor(
    private onboardingCrm: OnboardingCrmService,
    private surveyManager: SurveyManager,
    private emailService: OnboardingEmailService,
    private logger: Logger,
    private userEventService: UserEventService,
    private delegationManager: DelegationManager,
    private notificationManager: NotificationManager,
    private auditLogger: AuditLogger,
    private userPermissionService: ReturnType<typeof getUserPermissionService>,
    private initiativeOnboardingRepo: InitiativeOnboardingRepository,
    private organizationObManager: OrganizationOnboardingManager,
  ) {
  }

  public async userDecision(onboarding: OnboardingModel, user: UserModel, decision: string): Promise<OnboardingResult> {
    const status = onboarding.status;
    if (status === OnboardingStatus.Complete || OnboardingStatus.Rejected === status) {
      this.logger.error(`Trying to apply ${decision} to onboarding model with status ${status}`);
      throw new Error(`Cannot apply "${decision}", process has reached final stage`);
    }

    switch (decision) {
      case OnboardingUserDecision.Accept: {
        if (isOrganizationOnboardingModel(onboarding)) {
          return this.organizationObManager.acceptOrganizationOnboarding(onboarding, user);
        }
        if (isInitiativeOnboardingModel(onboarding)) {
          return this.acceptOnboarding(onboarding, user);
        }
        throw new UserError(`Unsupported onboarding type: ${onboarding.type}`, {
          onboardingId: onboarding._id,
          type: onboarding.type,
        });
      }
      case OnboardingUserDecision.Reject: {
        return this.rejectOnboarding(onboarding, user);
      }
      default: {
        throw new Error(`Action "${decision}" is not supported`);
      }
    }
  }

  public async createOnboardingUser(onboarding: OnboardingModel, userData: any) {

    const { userId, email } = onboarding.user;
    if (email !== userData.email) {
      throw new UserError(`Email cannot be changed. Please use original invitation email`);
    }
    let user = await this.getUser(email, userId);
    this.logger.info(`Processing onboarding user ${email}`, { userId, email });
    const userManager = getUserManager();
    let isNewUser = false;
    if (!user) {
      this.logger.info(`Creating new onboarding user ${userData.email}`);
      user = await userManager.createUser({
        ...userData,
        registrationData: {
          organizationName: userData.organizationName,
          onboardingId: onboarding._id
        }
      });

      if (!user.profile && onboarding.user?.profile) {
        const u = await modifyProfile({ model: user, url: onboarding.user.profile, type: 'user' });
        await u.save();
      }

      isNewUser = true;
      this.emailService.sendUserCreatedToSupport(onboarding, user).catch(this.logger.error);
      this.userEventService.userOnboarded(onboarding, user)
        .catch(e => this.logger.error(e.message));
    }

    if (!user.oktaUserId) {
      await userManager.migrateOktaUser(user, { value: userData.password }, [config.authentication.appGroup])
    }

    onboarding.user.userId = user._id;
    onboarding.user.firstName = user.firstName;
    onboarding.user.surname = user.surname;

    await this.addUserInitiativePermissions(onboarding, user);
    if (!this.hasConfigSetup(onboarding)) {
      // Nothing else left to do. Complete it
      onboarding.status = OnboardingStatus.Complete;
    }
    await onboarding.save();
    try {
      await this.onboardingCrm.linkContact(onboarding);
    } catch (e) {
      this.logger.error(e)
    }


    // Auto-accept surveys
    const status = onboarding.status;
    if (status !== OnboardingStatus.Complete && OnboardingStatus.Rejected !== status) {
      await this.userDecision(onboarding, user, OnboardingUserDecision.Accept);
    }

    return { user, isNewUser };
  }

  private async getUser(email: string, userId: ObjectId | undefined) {
    if (userId) {
      const user = await User.findById(userId).exec();
      if (user) {
        return user;
      }
    }

    return User.findOne({ email }).exec();
  }

  public async executeAction(action: string, ids: string[]) {
    switch (action) {
      case OnboardingAction.Start:
        return this.executeStart(ids);
      default:
        throw new Error(`${action} is not supported`);
    }
  }

  public async startOnboarding(onboarding: OnboardingModel) {
    await onboarding.save();
    this.executeStart([onboarding._id]);
    return onboarding;
  }

  private async executeStart(ids: (string | ObjectId)[]) {

    const objectIds = ids.map(id => new ObjectId(id));
    const models = await this.initiativeOnboardingRepo.find({
      _id: { $in: objectIds },
      status: OnboardingStatus.NotStarted,
    });

    const actionResult = { count: 0, failCount: 0 };
    const initiatives = await Initiative.find({
      _id: { $in: models.map(m => m.initiativeId) },
    }, { _id: 1, name: 1, appConfigCode: 1 }).lean().exec();

    const initiativeMap = new Map(initiatives.map((i) => {
      return [i._id.toString(), { _id: i._id, name: i.name, appConfigCode: i.appConfigCode }];
    }));

    for (const model of models) {
      try {
        const initiativeId = String(model.initiativeId);
        const details = await this.onboardingCrm.onboard(
          model,
          initiativeMap.get(initiativeId),
        );
        actionResult.count++;

        model.status = OnboardingStatus.Pending;
        model.startDate = new Date();
        await model.save();

        const userId = model.user.userId ? model.user.userId.toString() : undefined;
        this.userEventService.emit({
          userId: userId,
          email: model.user.email,
          eventDate: new Date(),
          service: ONBOARDING.service,
          event: ONBOARDING.events.start,
          data: { message: 'Started onboarding process. Added user to CRM', ...details }
        }).catch(e => this.logger.error(e.message));
      } catch (e) {
        this.logger.error(e);
        actionResult.failCount++;
      }
    }

    return actionResult;
  }

  private hasConfigSetup(ob: OnboardingModel) {
    return (
      ob.surveyConfig ||
      ob.utrvConfig ||
      ob.surveyStakeholders ||
      ob.assuranceStakeholders ||
      ob.surveyRoles ||
      ob.delegationScope
    );
  }

  private async acceptOnboarding(onboarding: InitiativeOnboardingModel, user: UserModel): Promise<OnboardingResult> {

    await this.addUserInitiativePermissions(onboarding, user);

    if (!user.profile && onboarding.user?.profile) {
      await modifyProfile({ model: user, url: onboarding.user.profile, type: 'user' });
      await user.save();
    }

    let survey;
    let success = true;
    if (this.hasConfigSetup(onboarding)) {
      let msg = '';
      try {
        const plainOnboarding = onboarding.toObject();
        if (hasSurveyConfig(onboarding)) {
          survey = await this.surveyManager.onboardSurveyUser(plainOnboarding as OnboardingWithSurveyConfig, user);
          onboarding.surveyConfig.complete = true;
          onboarding.surveyConfig.surveyId = survey._id;
          msg += `Successfully onboard survey ${survey._id} for user ${user.email}.`;
          this.logger.info(msg);
        }

        if (onboarding.surveyStakeholders) {
          await this.surveyManager.onboardExistingSurveys(plainOnboarding, user);
        }

        if (hasUtrvConfig(plainOnboarding)) {
          await this.onboardExistingUtrvs(plainOnboarding, user);
        }

        if (hasAssurance(plainOnboarding)) {
          await this.onboardExistingAssurance(plainOnboarding, user);
        }

        if (plainOnboarding.surveyRoles) {
          await this.onboardExistingSurveyRoles(plainOnboarding, user);
        }

        if (plainOnboarding.delegationScope) {
          await this.delegationManager.onboardDelegationScope(plainOnboarding, user);
        }

        this.userEventService.emit({
          userId: user._id.toString(),
          email: user.email,
          eventDate: new Date(),
          service: ONBOARDING.service,
          event: ONBOARDING.events.accept,
          data: { message: msg, onboarding: onboarding._id.toString() }
        }).catch(e => this.logger.error(e.message));

        onboarding.status = OnboardingStatus.Complete;

        await this.onboardingCrm.removeOnboarding(onboarding);
      } catch (e) {
        this.logger.error(`Failed to onboard survey ${onboarding._id} for user ${user.email}`);
        this.logger.error(e);
        success = false;
      }
    }
    await onboarding.save();

    return { success, survey };
  }

  private async addUserInitiativePermissions(onboarding: OnboardingModel, user: UserModel) {
    const { permissions } = onboarding.user;
    if (Array.isArray(permissions)) {
      await this.userPermissionService.addMultipleAndSave({
        user,
        permissions
      });
    }
  }

  public async rejectOnboarding(onboarding: OnboardingModel, user?: UserPlain): Promise<OnboardingResult> {

    await this.onboardingCrm.removeOnboarding(onboarding, OnboardingOutcome.Rejected);
    onboarding.status = OnboardingStatus.Rejected;
    await onboarding.save();

    this.userEventService.rejectOnboarding(onboarding, user);
    return { success: true };
  }

  public async remove(onboarding: OnboardingModel, user?: UserPlain) {
    await this.onboardingCrm.removeOnboarding(onboarding, OnboardingOutcome.Deleted);
    onboarding.status = OnboardingStatus.Deleted;
    await onboarding.save();

    this.userEventService.removeOnboarding(onboarding, user);

    if (onboarding.type === ObType.JoinRequest) {
      await this.emailService.sendJoinRequestRemoval(onboarding)
    }

    return onboarding;
  }

  public async removeOnboardInitiative(onboardingId: string, initiativeId: string, delegator: UserModel) {
    const initiative = await InitiativeRepository.mustFindById(initiativeId);
    await this.delegationManager.checkInitiative({ initiative, user: delegator });
    const onboarding = await this.initiativeOnboardingRepo.findExistingRootById(onboardingId, initiative._id);
    if (!onboarding) {
      return false;
    }

    await this.remove(onboarding);
    return true;
  }

  public async removePermissionFromOnboarding(onboardingId: string, initiativeId: string, delegator: UserModel) {
    const initiative = await InitiativeRepository.mustFindById(initiativeId);
    await this.delegationManager.checkInitiative({ initiative, user: delegator });
    const onboarding = await this.initiativeOnboardingRepo.findExistingByPermissionInitiativeId({
      onboardingId,
      initiativeId: initiative._id,
    });

    if (!onboarding) {
      throw new UserError(`Looks like we couldn't find that invitation to remove. It may have already been revoked.`, {
        onboardingId,
        initiativeId,
      });
    }

    if (onboarding.initiativeId.equals(initiative._id)) {
      return this.remove(onboarding);
    }

    onboarding.user.permissions = onboarding.user.permissions.filter(
      (permission) => !permission.initiativeId.equals(initiative._id)
    );

    return onboarding.save();
  }

  public async removeOnboardUtrv(email: string, type: StakeholderTypes, utrvId: string, delegator: UserModel) {
    this.validateParams(type);

    const utrv = await UniversalTrackerValueRepository.mustFindById(utrvId);
    await this.delegationManager.checkUtrv({ utrv, user: delegator, type });
    const initiative = await this.getUtrvInitiative(utrv);
    const onboarding = await this.initiativeOnboardingRepo.findExisting(email, initiative._id);
    if (!onboarding || !onboarding.utrvConfig) {
      return false;
    }

    const { utrvConfig } = onboarding;
    const filterUtrvId = (value: ObjectId) => value.toString() !== utrvId;

    utrvConfig.stakeholder = utrvConfig.stakeholder.filter(filterUtrvId);
    utrvConfig.verifier = utrvConfig.verifier.filter(filterUtrvId);
    utrvConfig.escalation = utrvConfig.escalation.filter(filterUtrvId);

    if (this.shouldRemoveOnboarding(onboarding)) {
      // Soft delete onboarding, no utr and no survey
      onboarding.status = OnboardingStatus.Deleted;
      await this.onboardingCrm.removeOnboarding(onboarding, OnboardingOutcome.Deleted);
    }

    await onboarding.save();
    return true;
  }

  shouldRemoveOnboarding({ utrvConfig, surveyStakeholders, assuranceStakeholders, surveyConfig, surveyRoles, delegationScope }: OnboardingModel) {

    const surveyStakeholdersCheck = !utrvConfig ||
      utrvConfig.stakeholder.length === 0
      && utrvConfig.verifier.length === 0
      && utrvConfig.escalation.length === 0;

    const utrvCheck = !surveyStakeholders ||
      surveyStakeholders.stakeholder.length === 0
      && surveyStakeholders.verifier.length === 0
      && surveyStakeholders.escalation.length === 0;

    const assuranceCheck = !assuranceStakeholders ||
      assuranceStakeholders.stakeholder.length === 0
      && assuranceStakeholders.verifier.length === 0
      && assuranceStakeholders.escalation.length === 0;

    const surveyRolesCheck = !surveyRoles ||
      surveyRoles.viewer.length === 0 && surveyRoles.admin.length === 0;

    return surveyStakeholdersCheck
      && utrvCheck
      && assuranceCheck
      && !surveyConfig
      && surveyRolesCheck
      && isDelegationScopeEmpty(delegationScope);
  }

  /**
   * @param onboardingListId - determine whether an onboard is normal flow or belongs to a bulk import
   */
  public async onboardEmail({
    email,
    initiativeId,
    delegator,
    onboardingData,
  }: {
    email: string;
    initiativeId: string;
    delegator: UserModel;
    onboardingData: OnboardingEnrollData;
  }) {
    if (!ObjectId.isValid(initiativeId)) {
      throw new Error('initiativeId is not valid');
    }

    const permissions = onboardingData.permissions;
    if (!permissions) {
      throw new Error(`permissions are required`)
    }

    const id = new ObjectId(initiativeId);
    const initiative = await Initiative.findById(id).exec();
    if (!initiative) {
      throw new Error('Invalid initiative ID');
    }
    await this.delegationManager.checkInitiative({ initiative, user: delegator });

    const user = await User.findOne({ email }).exec();
    if (user) {
      // User already exist - so just add permissions
      const bulkPermissions = onboardingData.bulkPermissions ?? [
        { initiativeId: initiative._id, permissions: onboardingData.permissions },
      ];
      return this.manageUserInitiative(user, bulkPermissions, 'add');
    }

    // Look up existing
    const existingOnboardings = await this.initiativeOnboardingRepo.findExistingByInitiativeId({
      email,
      initiativeId: initiative._id,
    });

    // TODO: GU-5695 to decide how to handle multiple existing onboardings, update all found for now
    if (existingOnboardings.length > 0) {
      for (const onboarding of existingOnboardings) {
        await this.updateUserPermissions(delegator, onboarding, {
          ...onboardingData,
          initiativeId: initiative._id,
        });
        if (onboarding.status === OnboardingStatus.NotStarted) {
          await this.executeStart([onboarding._id.toString()]);
        }
        await onboarding.save();
      }
      return true;
    }

    const data: OnboardingCreateData = {
      type: ObType.Initiative,
      metadata: {
        domain: tryGetContext()?.origin,
      },
      user: {
        complete: false,
        permissions: [],
        emailTemplate: this.getEmailTemplate(permissions),
        email: email,
      },
      initiativeId: initiative._id,
      createdBy: delegator._id,
      onboardingListId: onboardingData.onboardingListId,
    };

    await this.updateUserPermissions(delegator, data, onboardingData);

    const onboarding = await this.createOnboarding(data);

    await this.executeStart([onboarding._id.toString()]);

    return onboarding;
  }

  public async onboardEmails(
    emails: string[],
    initiativeId: string,
    delegator: UserModel,
    multipleOnboardingData: MultipleOnboardingEnrollData
  ): Promise<(boolean | OnboardingModel)[]> {
    return Promise.all(
      emails.map((email) => {
        const onboardingData: OnboardingEnrollData = {
          email,
          permissions: multipleOnboardingData.permissions,
        };
        return this.onboardEmail({email, initiativeId, delegator, onboardingData});
      })
    );
  }

  public async joinRequestByOnboardingData(
    initiativeId: string,
    onboardingData: Omit<JoinOnboardingData, 'initiativeIds'>,
    context: { domain?: string },
  ) {
    const { email, message, firstName, lastName: surname } = onboardingData;
    if (!email) {
      throw new Error('invalid join request');
    }

    if (!ObjectId.isValid(initiativeId)) {
      throw new Error('ID is not valid');
    }

    const initiative = await Initiative.findById(initiativeId).orFail().exec();
    const user = await User.findOne({ email }).exec();
    if (user) {
      throw new UserError('You cannot request to join this company because you are already registered.');
    }

    // Look up existing
    const existingOnboarding = await this.initiativeOnboardingRepo.findExisting(email, initiative._id);
    if (existingOnboarding) {
      throw new UserError('You already have an outstanding request to join.');
    }

    const userRoles = [
      UserRoles.Viewer
    ];

    const data: OnboardingCreateData = {
      metadata: {
        domain: context.domain,
        app: initiative.permissionGroup,
      },
      type: ObType.JoinRequest,
      user: {
        complete: false,
        permissions: [],
        emailTemplate: this.getEmailTemplate(userRoles),
        email,
        firstName,
        surname,
        message,
      },
      initiativeId: initiative._id
    };

    addGroupPermissions(data.user, initiativeId, userRoles);

    const onboarding = await this.createOnboarding(data);
    await this.notificationManager.sendJoinRequest(onboarding, context)

    return onboarding;
  }

  public async joinRequestByUser(
    initiativeId: string,
    user: UserPlain,
    onboardingData: Pick<JoinOnboardingData, 'message'>,
    context: { domain?: string },
  ) {
    if (!ObjectId.isValid(initiativeId)) {
      throw new Error('ID is not valid');
    }

    const initiative = await Initiative.findById(initiativeId).orFail().exec();

    const { message } = onboardingData;
    const { email, firstName, surname } = user;

    // Look up existing
    const existingOnboarding = await this.initiativeOnboardingRepo.findExisting(email, initiative._id);
    if (existingOnboarding) {
      throw new UserError('You already have an outstanding request to join.');
    }

    const userRoles = [
      UserRoles.Viewer
    ];

    const data: OnboardingCreateData = {
      metadata: {
        domain: context.domain,
        app: initiative.permissionGroup,
      },
      type: ObType.JoinRequest,
      user: {
        complete: false,
        permissions: [],
        emailTemplate: this.getEmailTemplate(userRoles),
        email,
        firstName,
        surname,
        message
      },
      initiativeId: initiative._id
    };

    addGroupPermissions(data.user, initiativeId, userRoles);

    const onboarding = await this.createOnboarding(data);
    await this.notificationManager.sendJoinRequest(onboarding, context)

    return onboarding;
  }

  public getEmailTemplate(userRoles: UserRoles[]) {
    if (userRoles.some(role => [
      UserRoles.User,
      UserRoles.Contributor,
      UserRoles.Verifier,
      UserRoles.Viewer
    ].includes(role))) {
      return EmailTemplate.User;
    }

    if (userRoles.some(role => [
      UserRoles.Manager,
    ].includes(role))) {
      return EmailTemplate.Manager;
    }

    return EmailTemplate.Guest;
  }

  public async onboardPortfolioInitiative(data: OnboardingCreateData) {
    const onboarding = await this.createOnboarding(data);
    return this.executeStart([onboarding._id.toString()]);
  }

  public async onboardUtrv(
    email: string,
    type: StakeholderTypes,
    utrvId: string,
    delegator: UserModel,
    onboardingData: OnboardingEnrollData,
  ) {
    this.validateParams(type);

    if (!ObjectId.isValid(utrvId)) {
      throw new Error('Utrv id is not valid');
    }

    const id = new ObjectId(utrvId);
    const utrv = await UniversalTrackerValueRepository.mustFindById(id);
    await this.delegationManager.checkUtrv({ utrv, user: delegator, type });

    const user = await User.findOne({ email }).exec();
    if (user) {
      // User already exist - we can add it straight to the utrv stakeholders
      await this.userPermissionService.addAndUpdate({
        user,
        initiativeId: utrv.initiativeId,
        permissions: [UserRoles.User],
      })
      return this.manageUserUtrv(user, utrv, type, 'add');
    }

    const initiative = await this.getUtrvInitiative(utrv);

    // Look up existing
    const existingOnboarding = await this.initiativeOnboardingRepo.findExisting(email, initiative._id);

    if (existingOnboarding) {
      this.updateUtrvConfig(existingOnboarding, utrv._id, type);

      if (existingOnboarding.status === OnboardingStatus.NotStarted) {
        await this.executeStart([existingOnboarding._id.toString()]);
      }
      await this.updateUserPermissions(delegator, existingOnboarding, onboardingData);
      await existingOnboarding.save();
      return true;
    }

    const data: OnboardingCreateData = {
      type: ObType.Initiative,
      metadata: {
        domain: tryGetContext()?.origin,
      },
      user: {
        complete: false,
        permissions: [],
        email: email,
      },
      initiativeId: initiative._id,
      utrvConfig: {
        stakeholder: [],
        verifier: [],
        escalation: [],
      },
      createdBy: delegator._id
    };
    await this.updateUserPermissions(delegator, data, onboardingData);


    this.updateUtrvConfig(data, utrv._id, type);
    const onboarding = await this.createOnboarding(data);

    await this.executeStart([onboarding._id.toString()]);

    return true;
  }

  public async applySurveyUtrvsDelegations(massDelegation: Omit<ProcessMassOnboardingDelegation, 'survey' | 'delegator'>) {
    const { delegationUtrvIds, roles, onboardingIds, action } = massDelegation;
    if (!delegationUtrvIds.length) {
      return [];
    }

    const existingOnboardings = await this.initiativeOnboardingRepo.find({
      _id: { $in: onboardingIds },
    });

    const result: OnboardingModel[] = [];
    while (existingOnboardings.length) {
      const batchResult = await Promise.all(
        existingOnboardings.splice(0, 5).map(async (onboarding) => {
          onboarding.utrvConfig = StakeholderGroupManager.mergeGroup(
            onboarding.utrvConfig ?? StakeholderGroupManager.getEmptyGroup(),
            StakeholderGroupManager.createUpdateGroup(action, roles, delegationUtrvIds)
          );

          if (onboarding.status === OnboardingStatus.NotStarted) {
            await this.executeStart([onboarding._id.toString()]);
          }

          return onboarding.save();
        })
      );

      result.push(...batchResult);
    }
    return result;
  }

  private async getUtrvInitiative(utrv: UniversalTrackerValueModel) {
    const initiative = await InitiativeRepository.findMainUtrvInitiative(utrv);
    if (!initiative) {
      throw new Error(`Failed to find initiative for utrv ${utrv._id}`);
    }

    return initiative;
  }

  public async updateUserPermissions(
    delegator: UserModel,
    onboarding: Pick<OnboardingCreateData, 'initiativeId' | 'user'>,
    data: Pick<OnboardingEnrollData, 'permissions' | 'bulkPermissions'> & { initiativeId?: ObjectId }
  ) {
    const permissions = data.bulkPermissions ?? [
      // initiativeId is the company's id where the user is invited to
      // allow to update user.permissions where user.permissions.initiativeId matches the company's id
      { initiativeId: data.initiativeId ?? onboarding.initiativeId, permissions: data.permissions },
    ];
    return Promise.all(
      permissions.map(async ({ initiativeId, permissions }) => {
        // Delegator have access to initiative and have at least group permissions
        if (!(await InitiativePermissions.canAccess(delegator, initiativeId))) {
          return;
        }

        addGroupPermissions(onboarding.user, initiativeId.toString(), permissions);
      })
    );
  }

  private async manageUserInitiative(user: UserModel, bulkPermissions: BulkPermissions, action: 'add'): Promise<boolean> {
    if (!user.permissions) {
      user.permissions = [];
    }

    if (action === 'add') {
      const userManager = getUserManager();
      // Update the permissions sequentially as this is a single user, and each update will call a save();
      for (const { initiativeId, permissions } of bulkPermissions) {
        if (user.isStaff) {
          this.auditLogger
            .fromContext({
              initiativeId: initiativeId.toString(),
              auditEvent: InitiativeAudit.userAdd,
              message: `A staff member has been given access to a company`,
              targets: [{ id: initiativeId.toString(), type: 'Initiative' }, this.auditLogger.userTarget(user)],
            })
            .catch(wwgLogger.error);
        }
        await userManager.updateInitiativePermissions(user, initiativeId.toString(), permissions, false);
      }
    }

    return true;
  }

  private async manageUserUtrv(user: UserPlain, utrv: UniversalTrackerValueModel, type: keyof StakeholderGroup, action: 'add' | 'remove') {
    const update: StakeholderGroupUpdate = {
      add: { stakeholder: [], verifier: [], escalation: [] },
      remove: { stakeholder: [], verifier: [], escalation: [] }
    };

    update[action] = updateStakeholders(type, user._id);
    utrv.stakeholders = StakeholderGroupManager.mergeGroup(
      utrv.stakeholders as StakeholderGroup,
      update
    );
    await utrv.save();
    await this.surveyManager.updateUtrvStakeholderChange(utrv._id, update);
    return true;
  }

  public async userUtrvAction(
    userId: string,
    utrvId: string,
    type: keyof StakeholderGroup,
    action: Actions,
    delegator: UserModel
  ) {
    const user = await User.findById(userId).exec();
    if (!user) {
      throw new Error('Invalid User id');
    }

    const utrv = await UniversalTrackerValueRepository.mustFindById(utrvId);
    await this.delegationManager.checkUtrv({ utrv, user: delegator, type });

    const result = await this.manageUserUtrv(user, utrv, type, action);

    await this.userPermissionService.addAndUpdate({
      user,
      initiativeId: utrv.initiativeId,
      permissions: [UserRoles.User],
    });

    if (utrv.compositeData?.surveyId) {
      const survey = await Survey.findById(utrv.compositeData.surveyId).orFail().exec();
      this.notificationManager.sendBulkDelegation({
        survey,
        questionCount: 1,
        userId,
        action,
        utrv,
        delegator,
      }).catch(wwgLogger.error)
    }

    return result;
  }

  public async onboardSurvey(
    email: string,
    type: StakeholderTypes,
    surveyId: string,
    delegator: UserModel,
    onboardingData: OnboardingEnrollData,
  ): Promise<OnboardingModel | Partial<OnboardingModel>> {
    this.validateParams(type);
    const survey = await SurveyRepository.mustFindById(surveyId);

    await this.delegationManager.checkSurvey({ survey, user: delegator, type });

    const user = await User.findOne({ email }).exec();
    if (user) {
      // Frontend calling wrong method, add user straight to the survey.
      await this.surveyManager.updateStakeholderGroup(survey, {
        add: updateStakeholders(type, user._id),
        remove: { stakeholder: [], verifier: [], escalation: [] },
      });
      await this.userPermissionService.addAndUpdate({
        user,
        initiativeId: survey.initiativeId,
        permissions: [UserRoles.User],
      });
      // Undefined cause success: false, therefore converting to error on frontend
      return {};
    }

    const initiative = await InitiativeRepository.mustFindById(survey.initiativeId);

    // Look up existing
    const existingOnboarding = await this.initiativeOnboardingRepo.findExisting(email, initiative._id);

    if (existingOnboarding) {
      existingOnboarding.surveyStakeholders = updateStakeholders(
        type,
        survey._id,
        existingOnboarding.surveyStakeholders
      );
      if (onboardingData.permissions) {
        await this.updateUserPermissions(delegator, existingOnboarding, onboardingData);
      }
      if (existingOnboarding.status === OnboardingStatus.NotStarted) {
        await this.executeStart([existingOnboarding._id.toString()]);
      }
      await existingOnboarding.save();
      return existingOnboarding;
    }

    const data: OnboardingCreateData = {
      type: ObType.Initiative,
      metadata: {
        domain: tryGetContext()?.origin,
      },
      user: {
        permissions: [],
        complete: false,
        email: email,
      },
      initiativeId: initiative._id,
      surveyStakeholders: {
        stakeholder: [],
        verifier: [],
        escalation: [],
      },
      createdBy: delegator._id,
    };

    if (onboardingData.permissions) {
      await this.updateUserPermissions(delegator, data, onboardingData);
    }
    data.surveyStakeholders = updateStakeholders(type, survey._id, data.surveyStakeholders);
    const onboarding = await this.createOnboarding(data);

    await this.executeStart([onboarding._id.toString()]);

    return onboarding;
  }

  public async removeOnboardSurvey(email: string, type: StakeholderTypes, surveyId: string, delegator: UserModel): Promise<Boolean> {
    this.validateParams(type);
    const survey = await SurveyRepository.mustFindById(surveyId);

    await this.delegationManager.checkSurvey({ survey, user: delegator, type });

    const onboarding = await this.initiativeOnboardingRepo.findExisting(email, survey.initiativeId);
    if (!onboarding || !onboarding.surveyStakeholders) {
      return false;
    }

    const { surveyStakeholders } = onboarding;
    const cleanGroup = (value: ObjectId) => value.toString() !== surveyId;

    switch (type) {
      case 'verifier':
        surveyStakeholders.verifier = surveyStakeholders.verifier.filter(cleanGroup);
        break;
      case 'stakeholder':
        surveyStakeholders.stakeholder = surveyStakeholders.stakeholder.filter(cleanGroup);
        break;
      case 'escalation':
        surveyStakeholders.escalation = surveyStakeholders.escalation.filter(cleanGroup);
        break;
    }

    if (this.shouldRemoveOnboarding(onboarding)) {
      // Soft delete onboarding, no utr and no survey
      onboarding.status = OnboardingStatus.Deleted;
      await this.onboardingCrm.removeOnboarding(onboarding, OnboardingOutcome.Deleted);
    }
    await onboarding.save();

    return true;
  }

  private validateParams(type: string) {
    if (!['stakeholder', 'verifier', 'escalation'].includes(type)) {
      throw new Error('Permitted Stakeholder Types: stakeholder | verifier | escalation');
    }
  }

  private updateUtrvConfig(data: OnboardingCommon, utrvId: ObjectId, type: string) {
    data.utrvConfig = updateStakeholders(type, utrvId, data.utrvConfig);
  }

  public async findOnboardingUtrvUsers(utrvs: Pick<UniversalTrackerValueModel, '_id'>[], initiativeId: ObjectId) {
    const utrvIds = utrvs.map(utrv => utrv._id);

    const initiativeIds: ObjectId[] = await InitiativeRepository.getFullTree(String(initiativeId)).then(
      (initiativeFullTree) => initiativeFullTree.map((initiative) => initiative._id)
    );

    const onboardings = (await this.initiativeOnboardingRepo.find({
      $or: [
        { 'utrvConfig.stakeholder': { $in: utrvIds } },
        { 'utrvConfig.verifier': { $in: utrvIds } },
        { 'utrvConfig.escalation': { $in: utrvIds } },
      ],
      status: {
        $in: [
          OnboardingStatus.Pending,
          OnboardingStatus.NotStarted,
        ]
      },
      initiativeId: { $in: initiativeIds },
    })) as OnboardingWithUtrConfig[];

    const utrvIdSet = new Set(utrvIds.map(String));
    const lookupFn = (id: ObjectId) => utrvIdSet.has(String(id));
    return onboardings.map(({ _id, user, utrvConfig, status }) => {
      const contributingUtrvIds = utrvConfig.stakeholder.filter(lookupFn);
      const verifyingUtrvIds = utrvConfig.verifier.filter(lookupFn);

      const userOnboarding: OnboardingUtrvUser = {
        _id,
        email: user.email,
        firstName: user.firstName ?? '',
        surname: user.surname ?? '',
        roles: [],
        status,
        count: {
          contributed: contributingUtrvIds.length,
          verified: verifyingUtrvIds.length,
        },
        utrvIds: {
          [SurveyUserRoles.Stakeholder]: contributingUtrvIds,
          [SurveyUserRoles.Verifier]: verifyingUtrvIds,
        },
        user,
      };

      if (contributingUtrvIds.length > 0) {
        userOnboarding.roles.push(SurveyUserRoles.Stakeholder);
      }

      if (verifyingUtrvIds.length > 0) {
        userOnboarding.roles.push(SurveyUserRoles.Verifier);
      }

      return userOnboarding;
    });
  }

  public async findOnboarding(type: StakeholderGroupProperty, modelId: ObjectId, initiativeId: ObjectId) {

    const $or = ['stakeholder', 'verifier', 'escalation'].map(groupType => {
      return ({ [`${type}.${groupType}`]: modelId });
    });

    return this.initiativeOnboardingRepo.find({
      $or,
      status: {
        $in: [
          OnboardingStatus.Pending,
          OnboardingStatus.NotStarted,
        ]
      },
      initiativeId,
    });
  }

  public async findOnboardingSurveyUsers(utrvId: ObjectId, initiativeId: ObjectId) {
    return this.initiativeOnboardingRepo.find({
      $or: [
        { 'surveyStakeholders.stakeholder': utrvId },
        { 'surveyStakeholders.verifier': utrvId },
        { 'surveyStakeholders.escalation': utrvId },
      ],
      status: {
        $in: [
          OnboardingStatus.Pending,
          OnboardingStatus.NotStarted,
        ]
      },
      // Initiative is defined so it must be InitiativeOnboardingPlain
      initiativeId,
    }) as Promise<InitiativeOnboardingPlain[]>;
  }

  private createOnboarding(data: OnboardingCreateData) {
    const model = new Onboarding(data);
    return model.save();
  }

  private async onboardExistingUtrvs(onboarding: OnboardingWithUtrConfig, user: UserModel) {

    const updates: Promise<unknown>[] = [];
    Object.entries(onboarding.utrvConfig).map(([key, ids]) => {
      if (ids.length > 0) {
        updates.push(UniversalTrackerValueDelegation.applyDelegationChange({
          ids,
          role: key as keyof StakeholderGroup,
          userId: user._id,
          action: Actions.Add,
        }))
      }
    });

    const utrvIds = getAllIds(onboarding.utrvConfig);

    wwgLogger.info('Onboarding existing utrvs', {
      onboardingId: onboarding._id,
      userId: user._id,
      utrvIds,
    });

    await Promise.all(updates);
    if (utrvIds.length === 0) {
      return;
    }

    wwgLogger.info('Updating survey visible stakeholders', {
      onboardingId: onboarding._id,
      userId: user._id,
      utrvIds,
    });

    return Survey.updateMany(
      {
        visibleUtrvs: { $in: utrvIds },
        visibleStakeholders: { $nin: [user._id] },
      },
      { $push: { visibleStakeholders: user._id } },
      { multi: true },
    );
  }

  private async onboardExistingAssurance(onboarding: OnboardingWithAssurance, user: UserModel) {

    const group = onboarding.assuranceStakeholders;
    const models = await AssurancePortfolio
      .find({ _id: { $in: getAllIds(group) } }).exec();
    // if user is part of stakeholders it should be added to portfolio permission
    Object.entries(group).forEach(([key, assurancePortfolioIds]) => {
      for (const idToOnboard of assurancePortfolioIds) {
        const model = models.find((s) => s._id.toString() === idToOnboard.toString());
        if (!model) {
          continue;
        }
        // reverse mapping userId with role to AP permissions (verifier => admin / stakeholder => user)
        const role = MAP_ROLES[key.toUpperCase() as keyof typeof MAP_ROLES] as AssurancePortfolioPermission;
        model.permissions = AssurancePermissions.mergeData(model.toObject().permissions, {
          data: { userId: user._id, permissions: [role] },
          action: Actions.Add,
        });
      }
    });
    const r = await Promise.all(models.map(u => u.save()));
    await this.addUserToOrgThroughPortfolio(models, user);
    return r;
  }

  private async addUserToOrgThroughPortfolio(models: AssurancePortfolioModel[], user: UserModel) {
    const ids = Array.from(new Set(models.map(m => String(m.organizationId))))

    // Only care about the first id if passing multiple ids
    const [firstId, secondId] = ids;
    if (!firstId) {
      return;
    }

    const metadata = {
      userId: String(user._id),
      userOrganizationId: user.organizationId?.toString(),
      organizationIds: ids,
      portfolioIds: models.map(p => String(p._id)),
    }

    if (secondId) {
      // Cannot really support it, log and try to add the first one anyway
      this.logger.error(`Assurance onboarding has multiple organization portfolios`, metadata)
    }

    this.logger.info('Adding user to assurance organization', metadata);
    // Add new logic organization permissions, you will be restricted user
    await this.organizationObManager.updateOrgPermissions({
      organizationId: new ObjectId(firstId),
      userId: user._id,
      permissions: [OrganizationPermission.RestrictedUser],
    });
    // Re-add the organizationId to allow users to pass organizationId checks
    // TODO: Remove this once organizationId is finished cleaning up
    if (!user.organizationId) {
      user.organizationId = new ObjectId(firstId);
    }
    return user.save();
  }

  public async manageAssuranceAction(
    action: Actions,
    email: string,
    type: keyof StakeholderGroup,
    assurancePortfolio: AssurancePortfolioModel,
    delegator: Pick<UserPlain, '_id'>,
  ) {

    const modelId = assurancePortfolio._id;
    const initiativeId = assurancePortfolio.initiativeId;
    if (action === Actions.Remove) {
      return this.removeAssuranceStakeholders(initiativeId, email, type, modelId);
    }

    return this.addAssuranceStakeholders(initiativeId, email, type, modelId, delegator);
  }

  private async addAssuranceStakeholders(
    initiativeId: ObjectId,
    email: string,
    type: keyof StakeholderGroup,
    modelId: ObjectId,
    delegator: Pick<UserPlain, '_id'>
  ) {
    const property = 'assuranceStakeholders'
    const initiative = await InitiativeRepository.mustFindById(initiativeId);

    // Look up existing
    const existingOnboarding = await this.initiativeOnboardingRepo.findExisting(email, initiative._id);

    if (existingOnboarding) {
      existingOnboarding[property] = StakeholderGroupManager.mergeGroup(
        existingOnboarding[property] ?? StakeholderGroupManager.getEmptyGroup(),
        StakeholderGroupManager.createUpdateGroup(Actions.Add, type, [modelId])
      );
      if (existingOnboarding.status === OnboardingStatus.NotStarted) {
        await this.executeStart([existingOnboarding._id.toString()]);
      }
      return existingOnboarding.save();
    }

    const data: OnboardingCreateData = {
      type: ObType.Initiative,
      metadata: {
        domain: tryGetContext()?.origin,
      },
      user: { complete: false, email: email, permissions: [] },
      initiativeId: initiative._id,
      [property]: StakeholderGroupManager.getEmptyGroup(),
      createdBy: delegator._id,
    };

    data[property] = updateStakeholders(type, modelId, data[property]);
    const onboarding = await this.createOnboarding(data);
    await this.executeStart([onboarding._id.toString()]);

    return onboarding;
  }

  private async removeAssuranceStakeholders(
    initiativeId: ObjectId,
    email: string,
    type: keyof StakeholderGroup,
    modelId: ObjectId,
  ) {
    const property = 'assuranceStakeholders'
    const initiative = await InitiativeRepository.mustFindById(initiativeId);

    // Look up existing
    const onboarding = await this.initiativeOnboardingRepo.findExisting(email, initiative._id);

    if (!onboarding) {
      return;
    }

    const removeId = String(modelId);

    const obProp = onboarding[property];
    if (obProp && obProp[type]) {
      obProp[type] = obProp[type].filter((value: ObjectId) => value.toString() !== removeId);
    }

    if (this.shouldRemoveOnboarding(onboarding)) {
      // Soft delete onboarding, no utr and no survey
      onboarding.status = OnboardingStatus.Deleted;
      await this.onboardingCrm.removeOnboarding(onboarding, OnboardingOutcome.Deleted);
    }

    return onboarding.save();
  }

  public async tryOnboardingRemove(onboarding: OnboardingModel) {
    if (!this.shouldRemoveOnboarding(onboarding)) {
      return onboarding;
    }
    // Soft delete onboarding, no utr and no survey
    onboarding.status = OnboardingStatus.Deleted;
    await this.onboardingCrm.removeOnboarding(onboarding, OnboardingOutcome.Deleted);
    return onboarding.save();
  }

  private async onboardExistingSurveyRoles(
    onboarding: OnboardingModelPlain,
    user: UserModel
  ) {
    const surveyRoles = onboarding.surveyRoles;
    if (!surveyRoles) {
      return;
    }

    const { admin, viewer } = surveyRoles;
    const ids: ObjectId[] = [...admin, ...viewer];

    if (ids.length === 0) {
      return;
    }

    const surveys = await Survey.find({ _id: { $in: ids } }).exec();

    const userId = String(user._id);

    for (const survey of surveys) {

      if (!survey.roles) {
        survey.roles = { viewer: [], admin: [] };
      }
      const roles = survey.roles;

      (['viewer', 'admin'] as (keyof SurveyRoles)[]).forEach((role) => {
        if (surveyRoles[role].some((id) => id.equals(survey._id))) {
          roles[role] = processUserUpdate(Actions.Add, roles[role], userId);
        }
      });

      await survey.save(); // Can't do Promise.all() or else it causes conflicts on same-doc saves
      await VisibleStakeholders.updateUserId(survey, userId, Actions.Add).catch(wwgLogger.error);
    }
  }
}

let instance: OnboardingManager;
// @TODO [REFACTOR] Too many dependencies
// Should split into initiative onboarding manager and organization onboarding manager
export const createOnboardingManager = () => {
  return new OnboardingManager(
    createOnboardingCrmService(),
    getSurveyManager(),
    createOnboardingEmailService(),
    wwgLogger,
    createUserEventService(),
    createDelegationManager(),
    getNotificationManager(),
    getAuditLogger(),
    getUserPermissionService(),
    getInitiativeOnboardingRepository(),
    getOrganizationOnboardingManager(),
  );
};

export const getOnboardingManager = () => {
  if (!instance) {
    instance = createOnboardingManager();
  }

  return instance;
};
