/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { z } from "zod";
import { OnboardingModel, OnboardingStatus } from "../../models/onboarding";
import { mustValidate } from "../../util/validation";
import User, { UserModel, User<PERSON>lain } from "../../models/user";
import { LoggerInterface, wwgLogger } from "../wwgLogger";
import { getOnboardingManager, OnboardingUserDecision } from "./OnboardingManager";
import { getOnboardingRepository, OnboardingRepository } from "../../repository/OnboardingRepository";
import { InitiativeRepository } from "../../repository/InitiativeRepository";
import { InitiativePlain, isOrganization } from "../../models/initiative";
import { ObjectId } from "bson";
import ContextError from "../../error/ContextError";


interface ExecuteData {
  users: UserModel[];
  pendingOnboardings: OnboardingModel[];
  initiative: <PERSON><PERSON>lain;
  /** Actor who is initiating the process **/
  user: Pick<UserPlain, '_id' | 'isStaff'>;
  dryRun?: boolean;
}

interface OnboardingUserResult {
  _id: ObjectId;
  userId: ObjectId;
  success: boolean;
  dryRun?: boolean;
}

export interface AcceptResult {
  initiativeId: ObjectId,
  results: OnboardingUserResult[],
}

const dataEntrySchema = z.object({
  initiativeIds: z.string().array().min(1),
  userEmails: z.string().array().optional(),
  dryRun: z.boolean().optional(),
});

type AcceptData = z.infer<typeof dataEntrySchema>;

export class OnboardingAdminService {

  constructor(
    private logger: LoggerInterface,
    private onboardingRepo: OnboardingRepository,
    private onboardingManager: ReturnType<typeof getOnboardingManager>,
  ) {
  }

  public getMustValidate(body: unknown): AcceptData {
    return mustValidate(body, dataEntrySchema);
  }

  public async acceptNewUser(user: Pick<UserPlain, '_id' | 'isStaff' | 'email'>, initiativeId: ObjectId) {
    const [root] = await InitiativeRepository.getRootInitiativesForIds([initiativeId]);
    if (!root) {
      throw new ContextError(`Failed to find Root Initiative for ${initiativeId}`, {
        initiativeId,
        userId: user._id,
      });
    }

    return this.acceptExistingUsers({
      initiativeIds: [root._id.toString()],
      userEmails: [user.email],
    }, user)
  }

  public async acceptExistingUsers(data: AcceptData, user: ExecuteData['user']) {

    const responseData: AcceptResult[] = [];

    this.logger.info(`Accepting existing users for ${data.initiativeIds.length} initiatives`, {
      userId: user._id,
      isStaff: user.isStaff,
      data,
    });

    for (const initiativeId of data.initiativeIds) {

      // Get all Children
      const initiativeTree = await InitiativeRepository.getMainTreeChildren(initiativeId);
      const initiative = initiativeTree.find((i) => i._id.toString() === initiativeId);

      if (!initiative || !isOrganization(initiative)) {
        this.logger.error(`Failed to process ${initiative?.name ?? initiativeId}, it must be organization`, {
          userId: user._id,
          initiativeId,
        });
        continue;
      }

      const pendingOnboardings = await this.onboardingRepo.find({
        initiativeId: { $in: initiativeTree.map((i) => i._id) },
        status: OnboardingStatus.Pending,
        // Apply userEmails filter if provided
        ...data.userEmails ? { 'user.email': { $in: data.userEmails } } : {},
      });

      const userEmails = new Set(pendingOnboardings.map((ob) => ob.user.email));

      this.logger.info(`${initiative.name} found ${pendingOnboardings.length} pending onboardings`, {
        initiativeId,
        onboardingEmailCount: userEmails.size,
      });


      // Active users that we can accept pending onboardings for this tree
      const users = await User.find({ email: { $in: Array.from(userEmails) } }).exec();

      this.logger.info(`${initiative.name} found ${users.length} active users from ${userEmails.size} onboardings`, {
        initiativeId,
        userIds: users.map((u) => u._id.toString()),
      });

      const userResults = await this.execute({
        dryRun: data.dryRun,
        users,
        pendingOnboardings,
        initiative,
        user,
      });

      responseData.push({ initiativeId: initiative._id, results: userResults });
    }

    return responseData;
  }

  private async execute({ users, pendingOnboardings, initiative, user, dryRun }: ExecuteData) {
    const userMap = new Map<string, UserModel>(users.map((u) => [u.email, u]));
    const results: OnboardingUserResult[] = [];

    for (const ob of pendingOnboardings) {
      const userForOnboarding = userMap.get(ob.user.email);
      if (userForOnboarding) {

        if (dryRun) {
          results.push({ _id: ob._id, userId: userForOnboarding._id, success: false, dryRun })
          continue;
        }

        const success = await this.onboardingManager
          .userDecision(ob, userForOnboarding, OnboardingUserDecision.Accept)
          .then(r => r.success)
          .catch((error) => {
            this.logger.error(error);
            return false;
          })

        results.push({ _id: ob._id, userId: userForOnboarding._id, success })
      }
    }

    this.logger.info(`Completed existing users for initiative ${initiative.name}`, {
      userId: user._id,
      initiativeId: initiative._id,
      results,
      users: users.map((u) => u._id.toString()),
    });

    return results;
  }
}

let instance: OnboardingAdminService;
export const getOnboardingAdminService = () => {
  if (!instance) {
    instance = new OnboardingAdminService(
      wwgLogger,
      getOnboardingRepository(),
      getOnboardingManager(),
    );
  }
  return instance;
}
