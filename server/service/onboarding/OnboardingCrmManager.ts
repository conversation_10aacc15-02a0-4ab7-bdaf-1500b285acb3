/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { InitiativeOnboardingModel, OnboardingModel, OrganizationOnboardingModel } from '../../models/onboarding';
import { generateUnsubscribeToken } from '../subscription/token';
import { CloseCrmOnboardingData, CreateCrmOnboardingData, CreateCrmOrganizationOnboardingData } from '../crm/contact';
import { getDummyCrmClient } from '../crm/local/DummyCrmClient';
import { CrmClient, ItemTypes, OnboardingOutcome } from '../crm/CrmClient';
import { createOnboardingEmailService, OnboardingEmailService } from './OnboardingEmailService';
import { wwgLogger } from '../wwgLogger';
import { tryGetContext } from '../../middleware/audit/contextMiddleware';
import { UrlMapper } from '../url/UrlMapper';
import { InitiativePlain } from '../../models/initiative';


type InitiativeOnboard = Pick<InitiativePlain, '_id' | 'name' | 'appConfigCode'> | undefined

export class OnboardingCrmService {

  constructor(private crmClient: CrmClient, private emailService: OnboardingEmailService) {
  }

  public async onboard(model: InitiativeOnboardingModel, initiative: InitiativeOnboard): Promise<object> {

    const context = tryGetContext();
    const onboardingUrl = UrlMapper.onboardingUrl({ token :model.token, domain: context?.origin });

    const createOnboarding: CreateCrmOnboardingData = {
      contactId: '',
      externalId: model._id.toString(),
      appConfigCode: initiative?.appConfigCode,
      initiativeName: initiative?.name ?? model.initiativeId.toHexString(),
      onboardingUrl,
      token: model.token,
      type: ItemTypes.Onboarding,
      unsubscribeUrl: UrlMapper.onboardingUnsubscribeUrl({
        token: generateUnsubscribeToken(model.user.email, model.user.userId),
        domain: context?.origin
      }),
    };

    this.emailService
      .sendOnboardingInitial(model, createOnboarding)
      .catch(wwgLogger.error);

    try {
      createOnboarding.contactId = await this.crmClient.linkContact(model);
      return await this.crmClient.createOnboarding(createOnboarding);
    } catch (e) {
      wwgLogger.error(e)
      return {};
    }

  }

  public async organizationOnboard(model: OrganizationOnboardingModel, organization: InitiativeOnboard): Promise<object> {

    const context = tryGetContext();
    const onboardingUrl = UrlMapper.onboardingUrl({ token: model.token, domain: context?.origin });

    const createOnboarding: CreateCrmOrganizationOnboardingData = {
      contactId: '',
      externalId: model._id.toString(),
      organizationName: organization?.name ?? model.organizationId.toHexString(),
      onboardingUrl,
      token: model.token,
      type: ItemTypes.Onboarding,
      unsubscribeUrl: UrlMapper.onboardingUnsubscribeUrl({
        token: generateUnsubscribeToken(model.user.email, model.user.userId),
        domain: context?.origin
      }),
    };

    this.emailService
      .sendOrganizationOnboardingInitial(model, createOnboarding)
      .catch(wwgLogger.error);

    try {
      createOnboarding.contactId = await this.crmClient.linkContact(model);
      return await this.crmClient.createOnboarding(createOnboarding);
    } catch (e) {
      wwgLogger.error(e)
      return {};
    }

  }

  public async removeOnboarding(model: OnboardingModel, outcome: OnboardingOutcome = OnboardingOutcome.Completed) {

    try {
      const contactId = await this.crmClient.linkContact(model);
      const closeData: CloseCrmOnboardingData = {
        contactId: contactId,
        externalId: model._id.toString(),
        status: outcome,
        type: ItemTypes.Onboarding,
      };

      return await this.crmClient.closeOnboarding(closeData);
    } catch (e) {
      wwgLogger.error(e)
    }
  }

  public linkContact(model: OnboardingModel) {
    return this.crmClient.linkContact(model);
  }
}

export const createOnboardingCrmService = () => {
  return new OnboardingCrmService(
    getDummyCrmClient(),
    createOnboardingEmailService(),
  );
};
