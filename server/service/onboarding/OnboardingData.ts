/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { ObjectId } from "bson";

interface OnboardingListData {
  onboardingListId?: ObjectId;
  onboardingListCode?: string;
  onboardingListName?: string;
}

export interface OnboardingImportData extends OnboardingListData {

  // Initiative Setup
  initiativeCode: string;
  initiativeName?: string;
  parentInitiativeCode?: string;

  // Initiative group setup
  initiativeGroupId?: string;
  weight?: string;

  // Setup survey
  surveyInstanceCode?: string;
  surveySourceName?: string;
  surveyEffectiveDate?: string; // YYYY-MM-DD format
  surveyUtrvType?: string;
  surveyEvidenceRequired?: string;
  surveyVerificationRequired?: string;


  // User
  userEmail: string;
  userFirstName?: string;
  userSurname?: string;
  userProfile?: string;
  userIsAdmin?: string;
  // Comma delimited list of initiative codes "uniliver,wwg"
  userInitiatives?: string;

  // Comma delimited list of dashboard permissions "summary, frameworks"
  /** @deprecated */
  userDashboardPermissions?: string;
}
