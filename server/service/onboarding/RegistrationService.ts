/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { createActivationToken } from "../../models/userToken";
import { UrlMapper } from "../url/UrlMapper";
import { ConfigFormData, getSelfOnboardingManager } from "./SelfOnboardingManager";
import { getUserManager, UserManager } from "../user/UserManager";
import { getCustomerManager } from "../payment/CustomerManager";
import { AppConfig } from "../app/AppConfig";
import { DomainConfig } from "../organization/domainConfig";
import { UserCreateData, UserModel } from "../../models/user";
import { getSponsorshipService } from "../referral/SponsorshipService";
import { LoggerInterface, wwgLogger } from "../wwgLogger";
import { ObjectId } from "bson";
import { SurveyScope } from "../survey/SurveyScope";
import { CustomScope } from "../../models/customScopeSchema";
import { SponsorshipConfigPlain } from "../../models/sponsorshipConfig";
import { FeatureTag } from '@g17eco/core';

export interface SelfOnboardingData extends ConfigFormData {
  firstName: string;
  surname: string;
  password: string;
  rPassword: string;
  email: string;
  telephoneNumber?: string;
}

export type SelfOnboardingUserData = Pick<UserCreateData,
  'firstName' |
  'surname' |
  'email' |
  'telephoneNumber' |
  'password'
> & Pick<ConfigFormData,
  'referenceCompany' |
  'referralCode'
> & {
  rPassword: string;
  telephoneNumber?: string;
}

interface RegistrationContext {
  domain: string | undefined;
  appConfig: AppConfig;
}

interface ProcessSGXRegistrationParams extends RegistrationContext {
  data: SelfOnboardingData;
  domainConfig: DomainConfig | undefined;
}

interface Activation {
  newUser: UserModel;
  initiativeId?: ObjectId;
  surveyId?: ObjectId;
  domain?: string;
  domainConfig?: DomainConfig;
  appConfig?: AppConfig;
}

interface ProcessRegistrationParams extends RegistrationContext {
  newUser: UserModel;
  data: SelfOnboardingData;
}

/**
 * Glue different logic related to registration into a single class
 * Most of the logic are done in the underlying services
 */
export class RegistrationService {

  constructor(
    private logger: LoggerInterface,
    private userManager: UserManager,
    private selfOnboardingManager: ReturnType<typeof getSelfOnboardingManager>,
    private customerManager: ReturnType<typeof getCustomerManager>,
    private sponsorshipService: ReturnType<typeof getSponsorshipService>
  ) {
  }

  /**
   * SGX flow does user registration at the same time as well.
   */
  public async processSGXRegistration({ data, domain, domainConfig, appConfig }: ProcessSGXRegistrationParams) {
    const newUser = await this.createUser(data, appConfig);

    const { survey } = await this.processRegistration({
      newUser,
      data,
      domain,
      appConfig,
    });

    // Want to send out survey even if it's not available
    await this.sendActivation({
      newUser,
      initiativeId: survey.initiativeId,
      surveyId: survey._id,
      domain,
      appConfig,
      domainConfig,
    });

    return survey
  }

  public async processMATRegistration({ newUser, data, domain, appConfig }: ProcessRegistrationParams) {

    const initiative = await this.selfOnboardingManager.processCompany(
      newUser,
      {
        ...data,
        permissionGroup: appConfig.permissionGroup,
        domain,
        appConfig: appConfig,
      },
      { extraTags: [FeatureTag.LimitMaterialityTrackerToStaffOnly] }
    );

    const { subscription, promotion } = await this.customerManager.createProductSubscription({
      initiative: initiative,
      user: newUser,
      productCode: appConfig.productCode,
      promoCode: data.referralCode,
    });

    const { dataShares, sponsorship, sponsorshipConfig } = await this.sponsorshipService.createSponsorship({
      initiative,
      user: newUser,
      referralCode: data.referralCode,
      subscriptions: [subscription],
    });
    data.scope = this.applyScope(data.scope, sponsorshipConfig);

    this.logger.info(`Processed subscriptions for new company ${initiative.name}`, {
      dataShares: dataShares.map(dataShare => dataShare._id.toString()),
      initiativeId: initiative._id.toString(),
      subscriptionId: subscription.id,
      promotionCode: data.referralCode,
      couponId: promotion?.coupon.id,
      sponsorshipId: sponsorship?._id.toString(),
    });

    return initiative;
  }

  /**
   * Normal flow expect user to be already created and activated.
   */
  public async processRegistration({ newUser, data, appConfig, domain }: ProcessRegistrationParams) {
    const initiative = await this.selfOnboardingManager.processCompany(newUser, {
      ...data,
      permissionGroup: appConfig.permissionGroup,
      domain,
      appConfig,
    });

    // Process registration here for subs
    const { subscription, promotion } = await this.customerManager.createProductSubscription({
      initiative: initiative,
      user: newUser,
      productCode: appConfig.productCode,
      promoCode: data.referralCode,
    });

    const { dataShares, sponsorship, sponsorshipConfig } = await this.sponsorshipService.createSponsorship({
      initiative,
      user: newUser,
      referralCode: data.referralCode,
      subscriptions: [subscription],
    });
    data.scope = this.applyScope(data.scope, sponsorshipConfig);

    this.logger.info(`Processed subscriptions for new company ${initiative.name}`, {
      dataShares: dataShares.map(dataShare => dataShare._id.toString()),
      initiativeId: initiative._id.toString(),
      subscriptionId: subscription.id,
      promotionCode: data.referralCode,
      couponId: promotion?.coupon.id,
      sponsorshipId: sponsorship?._id.toString(),
    });

    const survey = await this.selfOnboardingManager.createNewSurvey(
      initiative,
      newUser,
      data,
    );

    return { survey };
  }

  private applyScope = (dataScope : SelfOnboardingData["scope"], sponsorshipConfig: SponsorshipConfigPlain | undefined) => {
    if (!sponsorshipConfig) {
      return dataScope;
    }

    // if you config and custom scopes, any of them have registration
    const registrationScopes = sponsorshipConfig.surveyConfig?.customScopes?.reduce((acc, customScope) => {
      if (customScope.onRegistration) {
        acc.push(customScope);
      }
      return acc;
    }, [] as CustomScope[]) ?? [];
    return SurveyScope.fromCustomScopes(registrationScopes, dataScope);
  }

  public async createUser(data: SelfOnboardingUserData, appConfig: AppConfig) {
    return this.userManager.createUser({
      ...data,
      registrationData: {
        onboardingData: {
          appConfigCode: appConfig.code,
          referenceCompany: data.referenceCompany,
          referralCode: data.referralCode
        }
      },
      oktaActive: true,
      active: false,
    });
  }

  public async sendActivation({ newUser, initiativeId, surveyId, domain, appConfig, domainConfig }: Activation) {
    const userToken = createActivationToken(newUser._id);
    await userToken.save();
    const activationPath = UrlMapper.onboardingActivation({
      action: 'activate',
      token: userToken.token,
      // We don't need this as company doesn't exist
      initiativeId: initiativeId ?? '_',
      surveyId: surveyId ?? '_',
      domain,
      appConfig,
    });
    await this.userManager.sendActivationRequiredEmail({
      user: newUser,
      activationPath,
      userToken,
      domain,
      domainConfig,
      appConfig,
    });
  }
}

let instance: RegistrationService;
export const getRegistrationService = () => {
  if (!instance) {
    instance = new RegistrationService(
      wwgLogger,
      getUserManager(),
      getSelfOnboardingManager(),
      getCustomerManager(),
      getSponsorshipService()
    );
  }
  return instance;
}

