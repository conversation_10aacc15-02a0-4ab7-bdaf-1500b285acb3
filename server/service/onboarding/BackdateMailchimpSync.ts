import config from "../../config";
import Initiative, { InitiativePlain } from "../../models/initiative";
import Organization from "../../models/organization";
import { UserRepository } from "../../repository/UserRepository";
import { AppConfigService } from "../app/AppConfigService";
import { MailchimpService, MemberData, MemberTagPayload, TagStatus } from "../email/MailChimpService";
import { RootInitiativeService } from "../organization/RootInitiativeService";
import { LoggerInterface } from "../wwgLogger";
import { UserPlain } from '../../models/user';
import ContextError from "../../error/ContextError";

type RemoteTagsByEmail = Record<string, { id: string, tags: string[] }>

export class BackdateMailchimpSync {

  constructor(
    private mailchimpService: MailchimpService,
    private logger: LoggerInterface,
    private appConfigService: AppConfigService,
    private rootInitiativeService: RootInitiativeService,
    private appEnv?: string
  ) {
  }

  /**
   * Finds initiative based on the user first permission entry
   * activate/deactivate relevant tags and sends it to mailchimp one member at time
   */
  public async process({ batchSize }: { batchSize: number }) {

    let offset = 0
    let len = 0
    const updated: { email: string, tags: MemberTagPayload[] }[] = []
    do {
      const membersEmail: string[] = []
      const userDetails: { userId: string, tags: MemberTagPayload[] }[] = [];
      const data = await this.mailchimpService.getAudienceMembers(config.email.mailChimpAudienceId, batchSize, offset)
      if (!('members' in data)) {
        this.logger.error(data.detail)
        break
      }
      const members: MemberData[] = data.members
      len = members.length
      this.logger.info(`Process ${members.length} members at offset ${offset}`)

      if (len === 0) {
        break
      }
      offset += len

      membersEmail.push(...members.map((el) => el.email_address))

      const allMembersRemoteTags = this.getMembersRemoteTags(members)
      const users = await UserRepository.findManyByEmail(membersEmail)
      for (const u of users) {
        const tags = await this.updateMemberTagsWithRetry(u, allMembersRemoteTags)
        if (!tags) {
          continue
        }
        updated.push({ email: u.email, tags })
        userDetails.push({ userId: u._id.toString(), tags });

        // We keep hitting timeout on their servers, and library have default 120000ms (2min)
        // timeout, therefore we are delayed and need to retry after 2 minutes, therefore we add delay
        // delay to prevent timeout that will cause slower process overall
        // Takes around 200ms-300ms to execute and +500ms for delay on the next one
        await new Promise((resolve) => setTimeout(() => resolve(true), 500))
      }
      this.logger.info(`Process updated #${updated.length}`, { userDetails })
    } while (len === batchSize)
    this.logger.info(`Process finished updated #${updated.length}`)

    return updated
  }

  private async updateMemberTagsWithRetry(u: UserPlain, remoteTagsByEmail: RemoteTagsByEmail, maxRetries = 1, retried = 0): Promise<MemberTagPayload[] | undefined> {

    if (retried > maxRetries) {
      return
    }
    if (!remoteTagsByEmail[u.email] || !u.active) {
      return
    }
    const firstInitiative = await this.getUserFirstInitiative(u)
    if (!firstInitiative) {
      return
    }

    const tags = await this.getTagsPayload(u, firstInitiative, remoteTagsByEmail[u.email].tags, this.appEnv)

    try {
      const resp = await this.mailchimpService.updateMemberTags(
        config.email.mailChimpAudienceId,
        remoteTagsByEmail[u.email].id,
        tags
      );

      if (this.mailchimpService.isErrorResponse(resp)) {
        this.logger.error(resp.detail)
        return
      }

    } catch (err) {

      if (this.mailchimpService.isTimeoutException(err)) {
        this.logger.info(`Timeout retrying batch`, {
          userId: u._id.toString(),
          maxRetries,
          retried,
        });
        return this.updateMemberTagsWithRetry(u, remoteTagsByEmail, maxRetries, retried + 1)
      }
      throw err
    }

    return tags;
  }

  private getMembersRemoteTags(members: MemberData[]) {
    return members.reduce((result, current) => {
      result[current.email_address] = { id: current.id, tags: current.tags.map(t => t.name) };
      return result
    }, {} as RemoteTagsByEmail)
  }

  private async getAppconfig(initiative: InitiativePlain) {
    if (initiative.appConfigCode) {
      return this.appConfigService.getByCode(initiative.appConfigCode)
    }
    const org = await this.rootInitiativeService.getOrganization(initiative);
    return this.appConfigService.getByOrganization(org)
  }

  /**
   *  Get payload to add or remove tags through active/inactive status
   */
  public async getTagsPayload(
    user: UserPlain,
    firstInitiative: InitiativePlain,
    remoteTags?: string[],
    appEnv?: string,
  ) {

    const appConfig = await this.getAppconfig(firstInitiative)
    if (!appConfig) {
      return [];
    }
    const organization = user.organizationId ? await Organization.findById(user.organizationId, { organizationType: 1 }) : undefined

    const userRefreshedTags = await this.mailchimpService.getMailChimpTags(appConfig, organization?.organizationType, appEnv)
    const tagsToRemove: MemberTagPayload[] | undefined = remoteTags?.reduce(
      (result, current) => {
        if (this.mailchimpService.canRemoveTag(current, userRefreshedTags)) {
          result.push({ name: current, status: TagStatus.INACTIVE })
        }
        return result
      }, [] as MemberTagPayload[]);

    return [...userRefreshedTags.map(tag => ({ name: tag, status: TagStatus.ACTIVE })), ...tagsToRemove ?? []];
  }

  private async getUserFirstInitiative(user: UserPlain) {
    const firstInitiative = user.permissions?.[0]?.initiativeId
    if (!firstInitiative) {
      this.logger.error(new ContextError(`Failed to find user first initiative`, {
        userId: user._id,
        permissionCount: user.permissions.length,
      }))
      return;
    }
    return Initiative.findById(firstInitiative);
  }
}
