import moment from "moment";
import config from "../../config";
import Initiative, { InitiativePlain } from "../../models/initiative";
import Organization from "../../models/organization";
import { UserRepository } from "../../repository/UserRepository";
import { customDateFormat, DateFormat } from "../../util/date";
import { AppConfigService } from "../app/AppConfigService";
import { BatchListMemberErrorCodes, MailchimpService } from "../email/MailChimpService";
import { RootInitiativeService } from "../organization/RootInitiativeService";
import { LoggerInterface } from "../wwgLogger";
import mailchimp, { ErrorResponse } from '@mailchimp/mailchimp_marketing';
interface OnboardingMailchimpResult {
  added: number,
  errors: number,
  alreadyAdded: number,
  startDate: Date,
  endDate: Date
}

export class OnboardingMailchimpSync {

  constructor(
    private mailchimp: MailchimpService,
    private logger: LoggerInterface,
    private appConfigService: AppConfigService,
    private rootInitiativeService: RootInitiativeService,
    private appEnv?: string,
  ) {
  }

  private parseBatchResponses(responses: (mailchimp.lists.BatchListMembersResponse | ErrorResponse)[]) {
    return responses?.reduce((results, r) => {
      if (!('new_members' in r)) {
        return results
      }
      if (!r) {
        return results
      }
      results.added += r.new_members ? r.new_members.length : 0
      const actualErrors = r.errors ? r.errors.filter(el => el.error_code !== BatchListMemberErrorCodes.contactExists) : []
      results.alreadyAdded += r.errors ? (r.errors.length - actualErrors.length) : 0
      results.errors += actualErrors.length

      if (actualErrors.length > 0) {
        this.logger.error(JSON.stringify(actualErrors))
      }

      return results

    }, { added: 0, errors: 0, alreadyAdded: 0 })
  }

  /**
   * Finds users created during 'onboardDate' day
   * Finds initiative based on the user onboarding collection
   * applies relevant tags and sends it to mailchimp
   */
  public async process({ startDate = moment(), endDate = moment() }): Promise<OnboardingMailchimpResult | undefined> {
    const users = await UserRepository.findByCreatedBetween(startDate.toDate(), endDate.toDate(), true)
    if (users.length === 0) {
      return { added: 0, errors: 0, alreadyAdded: 0, startDate: startDate.toDate(), endDate: endDate.toDate() }
    }
    const members = await Promise.all(users.map(async u => {
      const firstInitiativeId = u.permissions[0]?.initiativeId
      const initiative = await Initiative.findById(firstInitiativeId)
      if (!initiative) {
        this.logger.warn(`No Initiative for user ${u._id}`)
        return
      }
      const organization = u.organizationId ? await Organization.findById(u.organizationId) : undefined
      const onboardDate = customDateFormat(u.created, DateFormat.Sortable)
      const appConfig = await this.getAppconfig(initiative)
      if (!appConfig) {
        return
      }

      const tags = await this.mailchimp.getMailChimpTags(appConfig, organization?.organizationType, this.appEnv)
      const batchListMember: mailchimp.lists.BatchListMembersBodyMembersObject = {
        email_address: u.email,
        email_type: 'html',
        status: 'subscribed',
        tags: tags,
        timestamp_signup: onboardDate,
        timestamp_opt: onboardDate,
        merge_fields: {
          FNAME: u.firstName,
          LNAME: u.surname,
          MMERGE3: initiative.name
        }
      }
      return batchListMember
    }))
    this.logger.info(`Users found #${members.length} since ${startDate.toISOString()} to ${endDate.toISOString()}`)
    const responses = await this.mailchimp.batchAddToAudience(
      config.email.mailChimpAudienceId,
      members.filter(m => m?.email_address && m.email_address.length > 0) as mailchimp.lists.BatchListMembersBodyMembersObject[]
    )
    return { ...this.parseBatchResponses(responses), startDate: startDate.toDate(), endDate: endDate.toDate() }
  }

  private async getAppconfig(initiative: InitiativePlain) {
    if (initiative.appConfigCode) {
      return await this.appConfigService.getByCode(initiative.appConfigCode)
    }
    const org = await this.rootInitiativeService.getOrganization(initiative);
    return await this.appConfigService.getByOrganization(org)
  }


}
