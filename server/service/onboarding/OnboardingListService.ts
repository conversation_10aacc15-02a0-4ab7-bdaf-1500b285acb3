/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import OnboardingList, { OnboardingListExtended, OnboardingListPlain } from '../../models/onboardingList';
import Initiative from '../../models/initiative';
import { PipelineStage } from 'mongoose';
import { userMinFields } from '../../models/user';

export class OnboardingListService {
  public async getByInitiative(initiativeId: ObjectId) {
    const initiative = await Initiative.findById(initiativeId).orFail().exec();

    const pipeline: PipelineStage[] = [
      {
        $match: {
          initiativeId: initiative._id,
        },
      },
      {
        $lookup: {
          from: 'onboarding',
          localField: '_id',
          foreignField: 'onboardingListId',
          as: 'onboardings',
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'createdBy',
          foreignField: '_id',
          as: 'creator',
          pipeline: [{ $project: userMinFields }], // only return properties we need
        },
      },
      {
        $project: {
          _id: 1,
          name: 1,
          code: 1,
          fileName: 1,
          initiativeId: 1,
          createdBy: 1,
          created: 1,
          onboardings: 1,
          creator: { $arrayElemAt: ['$creator', 0] },
        },
      },
    ];

    return OnboardingList.aggregate<OnboardingListExtended[]>(pipeline);
  }

  public async create(data: Omit<OnboardingListPlain, '_id' | 'created'>) {
    return OnboardingList.create(data);
  }
}

let instance: OnboardingListService;
export const getOnboardingListService = () => {
  if (!instance) {
    instance = new OnboardingListService();
  }
  return instance;
};
