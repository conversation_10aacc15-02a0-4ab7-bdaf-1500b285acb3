/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import {
  CustomReportInitiativesExtended,
  CustomReportModel,
  CustomReportPlain,
  CustomReportTemplate,
  isCustomReportTemplate,
  isInitiativesReport,
  isSurveyRangeReport,
  SurveyFilter,
  SurveyRange,
} from '../../models/customReport';
import UniversalTrackerValueService from '../utr/UniversalTrackerValueService';
import { InitiativePlain } from '../../models/initiative';
import { CustomReportData, CustomReportGenerator, getCustomReportGenerator } from './CustomReportGenerator';
import { getSurveyExcel, SurveyExcel } from '../survey/transfer/SurveyExcel';
import { ObjectId } from 'bson';
import { extractListId } from '../utr/utrUtil';
import { ValueListRepository } from '../../repository/ValueListRepository';
import Survey, { SurveyModelPlain } from '../../models/survey';
import { reportUtrArrayElemProjection, reportUtrvProjection } from './reportTypes';
import { FileParserType } from "../survey/transfer/parserTypes";
import { CUSTOM_REPORT_EXAMPLE } from '../../static/custom-report-example';
import sanitize from "sanitize-filename";
import { InitiativeRepository } from "../../repository/InitiativeRepository";
import { ConnectedStats } from "../initiative/InitiativeStatsService";
import { PipelineStage } from 'mongoose';
import { SurveyConfigService } from "../initiative/SurveyConfigService";
import { CustomTagManager } from "../metric/CustomTagManager";
import { CustomReportSDGGenerator, getCustomReportSDGGenerator } from './CustomReportSDGGenerator';
import { InitiativeMapType, SurveyMapType } from './constants';
import { getCustomReportTemplateDownload } from './CustomReportTemplateDownload';
import { CustomReportWFNGenerator, getCustomReportWFNGenerator } from './CustomReportWFNGenerator';
import { CustomReportManager, getCustomReportManager } from './CustomReportManager';
import { generateDateMatch } from '../../util/date';

enum CustomReportDownloadType {
  Calculated = 'calculated',
  Subsidiary = 'subsidiary',
  Date = 'date',
  Template = 'template',
}

interface TreeNodeType extends Omit<ConnectedStats, 'status' | 'surveys' | 'parent'> {
  parent?: TreeNodeType
}

export class CustomReportDownload {

  constructor(
    private customReportManager: CustomReportManager,
    private reportGenerator: CustomReportGenerator,
    private surveyExcel: SurveyExcel,
    private reportSDGGenerator: CustomReportSDGGenerator,
    private reportWfnGenerator: CustomReportWFNGenerator,
  ) {

  }

  public async getCustomReportDownload(customReport: CustomReportModel, initiative: InitiativePlain) {
    // Subsidiary report
    if (isInitiativesReport(customReport)) {
      return this.getCustomReportInitiativesDownload(
        await this.customReportManager.getSubsidiaryComparisonReport(customReport)
      );
    }

    // Date comparison report
    if (isSurveyRangeReport(customReport)) {
      return this.getCustomReportDateComparisonDownload(customReport);
    }

    if (isCustomReportTemplate(customReport)) {
      return getCustomReportTemplateDownload().generate({
        initiativeId: initiative._id,
        customReport: await this.customReportManager.getCustomReportTemplate(
          customReport.toObject() as CustomReportTemplate
        ),
      });
    }

    const utrService = new UniversalTrackerValueService(initiative);
    return utrService.getCustomReportDownload(customReport);
  }

  private async getCustomReportInitiativesDownload(customReport: CustomReportInitiativesExtended) {

    const reportData = await this.getSubsidiaryComparisonData(customReport);
    const initiativeConfig = await SurveyConfigService.findByInitiative(customReport.initiativeId);

    const ids: ObjectId[] = [];
    reportData.forEach(utr => {
      ids.push(...extractListId(utr.valueValidation));
    });

    const utrIds = reportData.map(utr => utr._id);

    const utrTagMap = await CustomTagManager.getUtrTagMap(customReport.initiativeId, utrIds);

    const valueLists = await ValueListRepository.findByIds(ids);

    const { initiativeIds } = customReport.survey;

    const initiatives = await InitiativeRepository.getMainTreeChildren(customReport.initiativeId);
    const initiativeMap = new Map<string, InitiativeMapType>(initiatives.map(i => {
      return [String(i._id), {
        _id: i._id,
        name: i.name,
        type: i.type,
        initiativeGroupId: i.initiativeGroupId,
        parentId: i.parentId,
        parentNames: [],
      }]
    }));

    const groupedByInitiatives = this.getGroupedByInitiatives(initiativeMap, customReport);
    const stringIds = new Set(initiativeIds.map(String));
    const sortedTree = this.sortGroupedInitiatives(groupedByInitiatives).filter((initiative) =>
      stringIds.has(String(initiative._id))
    );

    const initiativeWithMatchedSurveyFilters = await this.getInitiativeWithMatchedSurveyFilters(
      customReport,
      sortedTree
    );

    const { headers, records } = await this.reportGenerator.convertData({
      data: reportData,
      initiatives: initiativeWithMatchedSurveyFilters,
      utrTagMap,
      valueLists,
      surveyFilters: customReport.survey.surveyFilters,
      unitConfig: initiativeConfig.unitConfig
    });

    const { headers: sdgHeaders, records: sdgRecords } = await this.reportSDGGenerator.getInitiativesSDGData({
      initiatives: sortedTree,
    });

    const { wfnSummaryData, wfnMetricsData } = await this.reportWfnGenerator.getWfnSummaryAndMetricsData({
      reportData,
      surveyFilters: customReport.survey.surveyFilters,
      initiatives: initiativeWithMatchedSurveyFilters,
    });

    return this.surveyExcel.createSheetFromArray({
      sheets: [
        {
          name: 'Subsidiary View',
          data: [headers, ...records],
        },
        {
          name: 'WFN scorecard summary',
          data: [wfnSummaryData.headers, ...wfnSummaryData.records],
        },
        {
          name: 'WFN individual scores',
          data: [wfnMetricsData.headers, ...wfnMetricsData.records],
        },
        {
          name: 'SDG comparison',
          data: [sdgHeaders, ...sdgRecords],
        },
      ],
    });
  }

  private getGroupedByInitiatives(initiativeMap: Map<string, InitiativeMapType>, customReport: CustomReportPlain) {
    const values: TreeNodeType[] = Array.from(initiativeMap.values()).map((i) => {
      return {
        ...i,
        id: i._id.toHexString(),
        parentIdString: i.parentId?.toHexString(),
      };
    });

    const parentId = customReport.initiativeId;
    const parentNode = values.find(({ id }) => id === parentId);

    if (!parentNode) {
      return Array.from(initiativeMap.values());
    }

    const leafNodes: TreeNodeType[] = [];
    // Generate tree and create references
    let children = [parentNode];
    let nextChildren: TreeNodeType[] = [];
    while (children.length > 0) {
      nextChildren = [];
      children.forEach((c) => {
        const innerChildren = values.filter((v) => v.parentIdString === c.id);

        const value = initiativeMap.get(c.id);
        if (value) {
          const parent = initiativeMap.get(String(c.parentId));
          value.parentNames = parent ? [...(parent.parentNames ?? []), parent.name] : [];
        }

        if (innerChildren.length === 0) {
          leafNodes.push(c);
          return;
        }

        innerChildren.forEach((inner) => {
          inner.parent = c;
          nextChildren.push(inner);
        });
      });
      children = nextChildren;
    }

    // Traverse back from leaf nodes to the top
    leafNodes.forEach((n) => {
      let parent = n.parent;
      while (parent) {
        parent = parent.parent;
      }
    });

    return Array.from(initiativeMap.values()).sort((a, b) => a.parentNames.length - b.parentNames.length);
  }

  private sortGroupedInitiatives(initiatives: InitiativeMapType[]) {
    const sortedList = initiatives.slice();
    for (let i = 1; i < sortedList.length; i++) {
      const currentInitiative = sortedList[i];
      let j = i - 1;
      while (
        j >= 0 &&
        String(sortedList[j]._id) !== String(currentInitiative.parentId) &&
        String(sortedList[j].parentId) !== String(currentInitiative.parentId)
      ) {
        sortedList[j + 1] = sortedList[j];
        j = j - 1;
      }
      sortedList[j + 1] = currentInitiative;
    }
    return sortedList;
  }

  private async getCustomReportDateComparisonDownload(customReport: SurveyRange) {

    const reportData = await this.getDateComparisonData(customReport);

    const ids: ObjectId[] = [];
    reportData.forEach(utr => {
      ids.push(...extractListId(utr.valueValidation));
    });

    const utrIds = reportData.map(utr => utr._id);

    const utrTagMap = await CustomTagManager.getUtrTagMap(customReport.initiativeId, utrIds);

    const valueLists = await ValueListRepository.findByIds(ids);

    const surveys = await Survey.find(
      {
        _id: { $in: customReport.survey.ids },
        initiativeId: customReport.initiativeId,
      },
      { name: 1, effectiveDate: 1, unitConfig: 1, period: 1, type: 1 },
    )
      .lean<SurveyMapType[]>()
      .exec();

    const { headers, records } = await this.reportGenerator.convertSurveyData({
      data : reportData,
      surveys,
      valueLists,
      utrTagMap,
    });

    const { headers: sdgHeaders, records: sdgRecords } = await this.reportSDGGenerator.getDateComparisonSDGData({
      surveys,
    });

    return this.surveyExcel.createSheetFromArray({
      sheets: [
        {
          name: 'Subsidiary View',
          data: [headers, ...records],
        },
        {
          name: 'SDG comparison',
          data: [sdgHeaders, ...sdgRecords],
        },
      ]
    });
  }

  public async getCustomReportDownloadExample(reportType: string) {
    const exportType = FileParserType.Xlsx;
    const workBook = await this.surveyExcel.createSheet({ sheets: this.getExampleData(reportType)});
    const fileName = sanitize(`Custom Report Download Example_${this.getFileName(reportType)} Report.${exportType}`);

    return { fileName, exportType, workBook };
  }

  private getExampleData(type: string) {
    switch (type) {
      case CustomReportDownloadType.Calculated:
        return [
          { name: 'Single metric', data: CUSTOM_REPORT_EXAMPLE.calculated.single },
          { name: 'Total or % calculation', data: CUSTOM_REPORT_EXAMPLE.calculated.calculation },
        ];
      case CustomReportDownloadType.Subsidiary:
        return [{ name: 'Subsidiary comparison', data: CUSTOM_REPORT_EXAMPLE.subsidiary }];
      case CustomReportDownloadType.Template:
        return [{ name: 'Template', data: CUSTOM_REPORT_EXAMPLE.template }];
      case CustomReportDownloadType.Date:
      default:
        return [{ name: 'Date comparison', data: CUSTOM_REPORT_EXAMPLE.date }];
    }
  }

  private getFileName(type: string) {
    switch (type) {
      case CustomReportDownloadType.Calculated:
        return 'Calculated';
      case CustomReportDownloadType.Subsidiary:
        return 'Subsidiary';
      case CustomReportDownloadType.Template:
        return 'Template';
      case CustomReportDownloadType.Date:
      default:
        return 'Date Comparison';
    }
  }

  private generateSurveyMatch(customReport: CustomReportInitiativesExtended) {
    const { initiativeIds, surveyFilters } = customReport.survey;
    return {
      initiativeId: { $in: initiativeIds },
      deletedDate: { $exists: false },
      $or: surveyFilters.map(({ effectiveDate, type, period }) => ({
        effectiveDate: generateDateMatch(effectiveDate),
        period,
        type,
      })),
    };
  }

  /**
   * Load all the data required to generate report
   */
  private async getSubsidiaryComparisonData(
    customReport: CustomReportInitiativesExtended
  ): Promise<CustomReportData[]> {
    const { statuses, assuranceStatus } = customReport.survey;
    const basePipeline: PipelineStage[] = [
      {
        $match: this.generateSurveyMatch(customReport),
      },
      {
        $lookup: {
          from: 'universal-tracker-values',
          localField: 'visibleUtrvs',
          foreignField: '_id',
          as: 'utrvs'
        }
      },
      {
        $project: {
          initiativeId: 1,
          unitConfig: 1,
          utrvs: {
            ...reportUtrvProjection,
            universalTrackerId: 1,
            surveyId: '$_id',
            surveyPeriod: '$period',
            surveyType: '$type',
            status: 1,
            assuranceStatus: 1,
          },
        }
      },
      {
        $unwind: { 'path': '$utrvs' }
      }
    ]

    if (statuses && statuses.length > 0) {
      basePipeline.push({ $match: { 'utrvs.status': { $in: statuses } } })
    }

    if (assuranceStatus && assuranceStatus.length > 0) {
      basePipeline.push({ $match: { 'utrvs.assuranceStatus': { $in: assuranceStatus } } })
    }

    return Survey.aggregate([
      ...basePipeline,
      {
        $group: {
          _id: '$utrvs.universalTrackerId',
          utrvs: { $push: '$utrvs' },
          unitConfig: { $last: '$unitConfig' },
          maxRows: {
            $max: {
              $size: {
                $ifNull: ['$utrvs.valueData.table', []],
              },
            },
          },
        },
      },
      {
        $lookup: {
          from: 'universal-trackers',
          localField: '_id',
          foreignField: '_id',
          as: 'universalTracker',
        },
      },
      {
        $project: {
          ...reportUtrArrayElemProjection,
          maxRows: 1,
          unitConfig: 1,
          utrvs: reportUtrvProjection,
        },
      },
      {
        $sort: {
          name: 1,
        },
      },
    ]).exec();
  }

  private getMatchedSurveyFiltersPerInitiative(
    customReport: CustomReportInitiativesExtended
  ): Promise<{ initiativeId: ObjectId, surveys: Pick<SurveyModelPlain, 'effectiveDate' | 'type' | 'period'>[]}[]> {
    const aggregate: PipelineStage[] = [
      {
        $match: this.generateSurveyMatch(customReport),
      },
      {
        $group: {
          _id: '$initiativeId',
          surveys: {
            $push: {
              type: '$type',
              period: '$period',
              effectiveDate: '$effectiveDate',
            },
          },
        },
      },
      {
        $project: {
          _id: 0,
          initiativeId: '$_id',
          surveys: {
            $sortArray: { input: '$surveys', sortBy: { effectiveDate: 1 } },
          },
        },
      },
    ]; 
    return Survey.aggregate(aggregate).exec();
  }

  private async getInitiativeWithMatchedSurveyFilters(
    customReport: CustomReportInitiativesExtended,
    initiatives: InitiativeMapType[]
  ) {
    const surveyFilters = await this.getMatchedSurveyFiltersPerInitiative(customReport);
    return initiatives.map((initiative) => {
      const matchedSurveyFilters =
        surveyFilters.find((item) => item.initiativeId.equals(initiative._id))?.surveys ?? ([] as SurveyFilter[]);
      return { ...initiative, matchedSurveyFilters };
    });
  }

  /**
   * Load all the data required to generate date comparison report
   */
  private async getDateComparisonData(customReport: SurveyRange): Promise<CustomReportData[]> {
    const { statuses, assuranceStatus } = customReport.survey;
    const basePipeline: PipelineStage[] = [
      {
        $match: {
          initiativeId: new ObjectId(customReport.initiativeId),
          deletedDate: { $exists: false },
          _id: { $in: customReport.survey.ids },
        },
      },
      {
        $lookup: {
          from: 'universal-tracker-values',
          localField: 'visibleUtrvs',
          foreignField: '_id',
          as: 'utrvs',
        },
      },
      {
        $project: {
          initiativeId: 1,
          utrvs: {
            ...reportUtrvProjection,
            universalTrackerId: 1,
            surveyId: '$_id',
            status: 1,
            assuranceStatus: 1,
          },
        },
      },
      {
        $unwind: { path: '$utrvs' },
      },
    ];

    if (statuses && statuses.length > 0) {
      basePipeline.push({ $match: { 'utrvs.status': { $in: statuses } } })
    }

    if (assuranceStatus && assuranceStatus.length > 0) {
      basePipeline.push({ $match: { 'utrvs.assuranceStatus': { $in: assuranceStatus } } })
    }

    return Survey.aggregate([
      ...basePipeline,
      {
        $group: {
          _id: "$utrvs.universalTrackerId",
          utrvs: { $push: "$utrvs" },
          maxRows: {
            $max: {
              $size: {
                $ifNull: ['$utrvs.valueData.table', []],
              }
            },
          }
        }
      },
      {
        $lookup: {
          from: 'universal-trackers',
          localField: '_id',
          foreignField: '_id',
          as: 'universalTracker'
        }
      },
      {
        $project: {
          ...reportUtrArrayElemProjection,
          maxRows: 1,
          utrvs: reportUtrvProjection,
        }
      },
      {
        $sort: {
          name: 1
        }
      }
    ]).exec();
  }
}

let instance: CustomReportDownload;
export const getCustomReportDownload = () => {
  if (!instance) {
    instance = new CustomReportDownload(
      getCustomReportManager(),
      getCustomReportGenerator(),
      getSurveyExcel(),
      getCustomReportSDGGenerator(),
      getCustomReportWFNGenerator(),
    );
  }
  return instance;
}
