import { UtrValueType } from '../../models/public/universalTrackerType';
import { isMultiRowTable } from '../utr/utrUtil';
import { BaseReportGenerator } from './BaseReportGenerator';
import { getBaseReportDownload } from './BaseReportDownload';
import { RecordRow } from './constants';
import { getTransposedReportDataResolver, TransposedReportDataResolver } from './TransposedReportDataResolver';
import { ConvertDataParams, DataColumn, ReportDataGeneratorParams, ReportDataGeneratorResult } from './type';
import {
  checkAggregationByGroupExcluded,
  checkIsHistoryIncluded,
  getDownloadNumberScaleCode,
  getDownloadTableNumberScaleCode,
  getDownloadTableUnitCode,
  getDownloadUnitCode,
} from './utils';
import { getBaseColumns, getOrderedColumns, getSimpleReportColumns } from './columnUtils';
import { ProcessUtrParams } from './ReportDataResolver';

export class TransposedReportGenerator extends BaseReportGenerator {
  private readonly INPUT_COLUMN_CODE = 'input';

  constructor(
    private baseReportDownload: ReturnType<typeof getBaseReportDownload>,
    private transposedReportDataResolver: TransposedReportDataResolver
  ) {
    super();
  }

  public async getDownloadData({
    surveys,
    downloadScope,
    initiativeId,
    columns,
    orderingColumns,
  }: ReportDataGeneratorParams): Promise<ReportDataGeneratorResult> {
    const {
      reportDataWithAltCode,
      valueLists,
      utrTagMap,
      assuranceMap,
      surveyUnitConfigMap,
      utrvAssurances,
      initiativeMap,
      userMap,
    } = await this.baseReportDownload.getReportDataWithExtras({
      surveys,
      downloadScope,
      initiativeId,
      columns,
      orderingColumns,
    });
    const { displayUserInput, displayTag, displayMetricOverrides } = downloadScope;

    return this.convertData({
      data: reportDataWithAltCode,
      valueLists,
      utrTagMap,
      assuranceMap,
      surveyUnitConfigMap,
      utrvAssurances,
      columns: columns ?? getSimpleReportColumns({ downloadScope, assuranceMap }),
      orderingColumns,
      displayOptions: { displayUserInput, displayTag, displayMetricOverrides },
      downloadScope,
      initiativeMap,
      userMap,
    });
  }

  private convertData(params: ConvertDataParams) {
    const { data, columns, orderingColumns } = params;
    const combinedColumns = this.getAvailableColumns(params);
    const records: RecordRow[] = this.getRecords(params);

    return {
      headers: this.getHeaders({ dataColumns: combinedColumns, columns }),
      records: this.sortRecords({
        records,
        orderingColumns,
        dataColumns: combinedColumns,
        data,
        inputColumnPrefix: this.INPUT_COLUMN_CODE,
      }),
    };
  }

  private getRecords(params: ConvertDataParams) {
    const { data, columns, valueLists, assuranceMap, downloadScope, displayOptions, initiativeMap } = params;
    const isHistoryIncluded = checkIsHistoryIncluded(columns);
    const isAggregationByGroupExcluded = checkAggregationByGroupExcluded(columns);
    const columnConfig = { isHistoryIncluded, isAggregationByGroupExcluded };

    const valueListMap = new Map(valueLists.map((list) => [String(list._id), list.options]));
    const combinedColumns = this.getAvailableColumns(params);

    return data
      .map((utr) => {
        const valueList = utr.valueValidation?.valueList;
        // Expand valueList
        if (valueList?.listId && !valueList.list) {
          valueList.list = valueListMap.get(String(valueList.listId));
        }

        const utrParams: ProcessUtrParams = {
          combinedColumns,
          utr,
          valueListMap,
          extraData: { assuranceMap, initiativeMap },
          columnConfig,
          downloadScope,
          displayOptions,
        };

        return this.transposedReportDataResolver.processUtr(utrParams);
      })
      .flat();
  }

  private getAvailableColumns({
    columns,
    surveyUnitConfigMap,
    displayOptions,
    utrvAssurances,
    utrTagMap,
    userMap,
  }: ConvertDataParams) {
    const baseColumns = getBaseColumns({ utrvAssurances, utrTagMap, userMap });
    const answerColumn = this.getAnswerColumn({ surveyUnitConfigMap, displayOptions });
    const unitColumn = this.getUnitColumn({ surveyUnitConfigMap, displayOptions });
    const numberScaleColumn = this.getNumberScaleColumn({ displayOptions });
    const rowColumn = this.getRowColumn();
    const groupByAggregationColumn = this.getGroupByAggregationColumn({ surveyUnitConfigMap, displayOptions });

    const allColumns: DataColumn[] = [...baseColumns, answerColumn, rowColumn, unitColumn, numberScaleColumn, groupByAggregationColumn];
    return getOrderedColumns({ columns, allColumns, inputColumnPrefix: this.INPUT_COLUMN_CODE });
  }

  private getAnswerColumn({
    surveyUnitConfigMap,
    displayOptions,
  }: Pick<ConvertDataParams, 'surveyUnitConfigMap' | 'displayOptions'>): DataColumn {
    return {
      code: this.INPUT_COLUMN_CODE,
      header: `Answer`,
      accessor: (utr) => {
        const utrv = utr.utrvs[0];
        if (!utrv) {
          return undefined;
        }

        return this.transposedReportDataResolver.extractValue({
          utr: {
            ...utr,
            _currentRow: { ...utr._currentRow },
          },
          utrv,
          unitConfig: surveyUnitConfigMap.get(utrv.surveyId.toString()),
          displayOptions,
          extractValueOnly: true,
        });
      },
    };
  }

  private getRowColumn(): DataColumn {
    return {
      code: 'row',
      header: `Row`,
      accessor: (utr) => {
        if (!isMultiRowTable(utr) || utr._currentRow.tableRowIndex === undefined) {
          return '';
        }
        return utr._currentRow.tableRowIndex + 1;
      },
    };
  }

  private getUnitColumn({
    surveyUnitConfigMap,
    displayOptions,
  }: Pick<ConvertDataParams, 'surveyUnitConfigMap' | 'displayOptions'>): DataColumn {
    return {
      code: 'unit',
      header: 'Unit',
      accessor: (utr) => {
        if (utr.valueType === UtrValueType.Table) {
          return getDownloadTableUnitCode({ utr, displayOptions, surveyUnitConfigMap });
        }
        return getDownloadUnitCode({ utr, displayOptions, surveyUnitConfigMap });
      },
    };
  }

  private getNumberScaleColumn({ displayOptions }: Pick<ConvertDataParams, 'displayOptions'>): DataColumn {
    return {
      code: 'numberScale',
      header: 'Number Scale',
      accessor: (utr) => {
        if (utr.valueType === UtrValueType.Table) {
          return getDownloadTableNumberScaleCode({ utr, displayOptions });
        }
        return getDownloadNumberScaleCode({ utr, displayOptions });
      },
    };
  }

  private getGroupByAggregationColumn({
    surveyUnitConfigMap,
    displayOptions,
  }: Pick<ConvertDataParams, 'surveyUnitConfigMap' | 'displayOptions'>): DataColumn {
    return {
      code: 'aggregationGroupBy',
      header: 'Group by aggregation',
      accessor: (utr) => {
        const utrv = utr.utrvs[0];
        const groupedByCodes = utr.extraData.groupedByCodes;
        if (!utrv || !groupedByCodes || groupedByCodes?.length === 0) {
          return undefined;
        }
        const groupedByColumns = utr.valueValidation?.table?.columns.filter((col) => groupedByCodes.includes(col.code)) ?? [];
        return groupedByColumns
          .map((col) =>
            this.transposedReportDataResolver.extractValue({
              utr: {
                ...utr,
                _currentRow: { ...utr._currentRow, column: col },
              },
              utrv,
              unitConfig: surveyUnitConfigMap.get(utrv.surveyId.toString()),
              displayOptions,
              extractValueOnly: true,
            })
          )
          .join(', ');
      },
    };
  }
}

let instance: TransposedReportGenerator;

export const getTransposedReportGenerator = () => {
  if (!instance) {
    instance = new TransposedReportGenerator(getBaseReportDownload(), getTransposedReportDataResolver());
  }
  return instance;
};
