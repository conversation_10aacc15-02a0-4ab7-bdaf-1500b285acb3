/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { UtrValueType } from '../../models/public/universalTrackerType';
import { BaseReportGenerator } from './BaseReportGenerator';
import { BaseReportDownload, getBaseReportDownload } from './BaseReportDownload';
import { CurrentRow, getReportDataResolver, ProcessUtrParams, ReportDataResolver } from './ReportDataResolver';
import { RecordRow } from './constants';
import { ConvertDataParams, DataColumn, ReportDataGeneratorParams, ReportDataGeneratorResult } from './type';
import {
  checkIsHistoryIncluded,
  getDownloadNumberScaleCode,
  getDownloadTableNumberScaleCode,
  getDownloadTableUnitCode,
  getDownloadUnitCode,
} from './utils';
import { getBaseColumns, getSimpleReportColumns, getOrderedColumns } from './columnUtils';
import { ReportUtrv } from './reportTypes';
import { isMultiRowTable } from '../utr/utrUtil';
import { getFullName } from '../../models/user';
import { checkMatchedNumberScale } from '../units/utils';
import { PipelineStage } from 'mongoose';

interface MultiRowTableConfig {
  isMatchedUnit: boolean;
  isMatchedNumberScale: boolean;
}

interface SimpleReportDataGeneratorParams extends ReportDataGeneratorParams {
  surveyFiltersMap?: Map<string, PipelineStage[]>;
}

export class SimpleReportGenerator extends BaseReportGenerator {
  private readonly INPUT_COLUMN_PREFIX = 'input-';

  constructor(private baseReportDownload: BaseReportDownload, private dataResolver: ReportDataResolver) {
    super();
  }

  public async getDownloadData({
    surveys,
    downloadScope,
    initiativeId,
    columns,
    orderingColumns,
    surveyFiltersMap
  }: SimpleReportDataGeneratorParams): Promise<ReportDataGeneratorResult> {
    const {
      reportDataWithAltCode,
      valueLists,
      utrTagMap,
      assuranceMap,
      surveyUnitConfigMap,
      utrvAssurances,
      initiativeMap,
      utrvToEvidencesMap,
      utrvToStakeholdersMap,
      userMap,
    } = await this.baseReportDownload.getReportDataWithExtras({
      surveys,
      downloadScope,
      initiativeId,
      columns,
      orderingColumns,
      surveyFiltersMap,
    });

    const { displayUserInput, displayTag, displayMetricOverrides } = downloadScope;
    return this.convertData({
      data: reportDataWithAltCode,
      valueLists,
      utrTagMap,
      assuranceMap,
      surveyUnitConfigMap,
      utrvAssurances,
      columns: columns ?? getSimpleReportColumns({ downloadScope, assuranceMap }),
      orderingColumns,
      displayOptions: { displayUserInput, displayTag, displayMetricOverrides },
      downloadScope,
      initiativeMap,
      utrvToEvidencesMap,
      utrvToStakeholdersMap,
      userMap,
    });
  }

  private generateKeyForInputMap({
    utrv: { lastUpdated, status, compositeData },
    utrCode,
    columnCode = '',
  }: {
    utrv: ReportUtrv;
    utrCode: string;
    columnCode?: string;
  }) {
    return `${compositeData?.surveyId?.toString()}_${utrCode}_${columnCode}_${lastUpdated.getTime()}_${status}`;
  }

  private getMeasurementMatchedConfig({
    utr,
    multiRowTableConfigMap,
  }: {
    utr: CurrentRow<Partial<Pick<ConvertDataParams, 'initiativeMap' | 'assuranceMap'>>>;
    multiRowTableConfigMap: Map<string, MultiRowTableConfig>;
  }) {
    let currentConfig: MultiRowTableConfig | undefined;
    const [utrv] = utr.utrvs;
    if (utrv && isMultiRowTable(utr)) {
      const uniqueKey = this.generateKeyForInputMap({
        utrv,
        utrCode: utr.code,
        columnCode: utr._currentRow.column?.code,
      });
      currentConfig = multiRowTableConfigMap.get(uniqueKey);
    }
    return currentConfig ?? { isMatchedUnit: false, isMatchedNumberScale: false };
  }

  private getAvailableColumns({
    data,
    surveyUnitConfigMap,
    displayOptions,
    utrvAssurances,
    utrTagMap,
    multiRowTableConfigMap,
    utrvToEvidencesMap,
    utrvToStakeholdersMap,
    userMap,
  }: Pick<
    ConvertDataParams,
    | 'displayOptions'
    | 'utrvAssurances'
    | 'utrTagMap'
    | 'data'
    | 'surveyUnitConfigMap'
    | 'utrvToEvidencesMap'
    | 'utrvToStakeholdersMap'
    | 'userMap'
  > & {
    multiRowTableConfigMap: Map<string, MultiRowTableConfig>;
  }): DataColumn[] {
    return [
      {
        code: 'unit',
        header: 'Unit',
        accessor: (utr) => {
          if (utr.valueType === UtrValueType.Table) {
            const { isMatchedUnit } = this.getMeasurementMatchedConfig({ utr, multiRowTableConfigMap });
            const ignoreMultiRowTable = !isMatchedUnit;
            return getDownloadTableUnitCode({ utr, displayOptions, surveyUnitConfigMap, ignoreMultiRowTable });
          }
          return getDownloadUnitCode({ utr, displayOptions, surveyUnitConfigMap });
        },
      },
      {
        code: 'numberScale',
        header: 'Number Scale',
        accessor: (utr) => {
          if (utr.valueType === UtrValueType.Table) {
            const { isMatchedNumberScale } = this.getMeasurementMatchedConfig({ utr, multiRowTableConfigMap });
            const ignoreMultiRowTable = !isMatchedNumberScale;
            return getDownloadTableNumberScaleCode({ utr, displayOptions, ignoreMultiRowTable });
          }
          return getDownloadNumberScaleCode({ utr, displayOptions });
        },
      },
      ...getBaseColumns({ utrvAssurances, utrTagMap, userMap }),
      ...this.getInputColumns({
        data,
        surveyUnitConfigMap,
        displayOptions,
        multiRowTableConfigMap,
      }),
      ...this.getEvidenceColumns(utrvToEvidencesMap),
      ...this.getExplicitDelegationColumns(utrvToStakeholdersMap),
    ];
  }

  /**
   * scope: multi-row table
   * unit/number scale is considered matched across a column if every row has the same value
   * */
  private prepareMultiRowTableMatchedInputMap({
    data,
    isHistoryIncluded,
    downloadScope,
    displayOptions = { displayUserInput: false, displayMetricOverrides: false },
  }: Pick<ConvertDataParams, 'data' | 'downloadScope' | 'displayOptions'> & { isHistoryIncluded: boolean }): Map<
    string,
    MultiRowTableConfig
  > {
    const inputMap = new Map();

    const inputData = this.dataResolver.getAllDataPoints({ data, downloadScope, isHistoryIncluded });

    inputData.forEach((dataPoint) => {
      // Is it always supposed to have 1 item?
      const utrv = dataPoint.utrvs[0];

      // Only deal with multiRow table
      if (!utrv || !isMultiRowTable(dataPoint)) {
        return;
      }

      // Expected shape: [[{ col1: data1 }, { col2: data2 }], [{ col1: data3 }, { col2: data4 }]]
      const table = utrv.valueData?.input?.table || [];
      const isDisplayingUserInput = displayOptions.displayUserInput && !displayOptions.displayMetricOverrides;

      const codes = dataPoint.valueValidation?.table?.columns.map((column) => column.code) || [];
      for (const code of codes) {
        const uniqueKey = this.generateKeyForInputMap({
          utrv,
          utrCode: dataPoint.code,
          columnCode: code,
        });
        if (table.length === 0 || !isDisplayingUserInput) {
          // Unanswered data or user input is not displayed, then unit and numberScale must be match
          inputMap.set(uniqueKey, { isMatchedUnit: true, isMatchedNumberScale: true });
          continue;
        }
        const matchedColumns = table.flat().filter((column) => column.code === code);
        const uniqueNumberScales = new Set(matchedColumns.map((column) => column.numberScale));
        inputMap.set(uniqueKey, {
          isMatchedUnit: new Set(matchedColumns.map((column) => column.unit)).size === 1,
          isMatchedNumberScale: checkMatchedNumberScale(uniqueNumberScales),
        });
      }
    });

    return inputMap;
  }

  public convertData({
    data,
    valueLists,
    utrTagMap,
    assuranceMap,
    surveyUnitConfigMap,
    displayOptions = { displayUserInput: false, displayMetricOverrides: false },
    utrvAssurances,
    columns,
    orderingColumns,
    downloadScope,
    initiativeMap,
    utrvToEvidencesMap,
    utrvToStakeholdersMap,
    userMap,
  }: ConvertDataParams) {
    const isHistoryIncluded = checkIsHistoryIncluded(columns);
    const columnConfig = { isHistoryIncluded };

    const multiRowTableConfigMap = this.prepareMultiRowTableMatchedInputMap({
      data,
      isHistoryIncluded,
      downloadScope,
      displayOptions,
    });

    const allColumns = this.getAvailableColumns({
      data,
      surveyUnitConfigMap,
      displayOptions,
      utrvAssurances,
      utrTagMap,
      multiRowTableConfigMap,
      utrvToEvidencesMap,
      utrvToStakeholdersMap,
      userMap,
    });

    const dataColumns = getOrderedColumns({ columns, allColumns, inputColumnPrefix: this.INPUT_COLUMN_PREFIX });

    if (!dataColumns.length) {
      return {
        headers: [],
        records: [],
      };
    }

    const valueListMap = new Map(valueLists.map((list) => [String(list._id), list.options]));

    // Generate data array
    const outputData: RecordRow[] = [];

    data.forEach((utr) => {
      const vl = utr.valueValidation?.valueList;
      // Expand valueList
      if (vl?.listId && !vl.list) {
        vl.list = valueListMap.get(String(vl.listId));
      }

      const utrParams: ProcessUtrParams = {
        combinedColumns: dataColumns,
        utr,
        valueListMap,
        extraData: { assuranceMap, initiativeMap },
        columnConfig,
        downloadScope,
      };

      const records = this.dataResolver.processUtr(utrParams);

      outputData.push(...records);
    });

    return {
      headers: this.getHeaders({ dataColumns, columns }),
      records: this.sortRecords({ records: outputData, dataColumns, orderingColumns, data }),
      utrvToEvidencesMap,
    };
  }

  private getInputColumns({
    data,
    surveyUnitConfigMap,
    displayOptions,
    multiRowTableConfigMap,
  }: Pick<ConvertDataParams, 'data' | 'surveyUnitConfigMap' | 'displayOptions'> & {
    multiRowTableConfigMap: Map<string, MultiRowTableConfig>;
  }) {
    let highestMaxRow = 1;
    data.forEach((utr) => {
      if (utr.maxRows && utr.maxRows > highestMaxRow) {
        highestMaxRow = utr.maxRows;
      }
    });

    // Expand columns with input columns
    const inputColumns: DataColumn[] = Array.from({ length: highestMaxRow }).map((_input, index) => {
      const id = index + 1;
      return {
        code: `${this.INPUT_COLUMN_PREFIX}${String(id)}`,
        header: `Input ${id}`,
        accessor: (utr) => {
          const utrv = utr.utrvs[0];
          if (!utrv) {
            return undefined;
          }
          // Additional inputs (index > 0) should only render for tables
          if (utr.valueType !== UtrValueType.Table && index > 0) {
            return undefined;
          }

          const { isMatchedUnit, isMatchedNumberScale } = this.getMeasurementMatchedConfig({
            utr,
            multiRowTableConfigMap,
          });

          return this.dataResolver.extractValue({
            utr: {
              ...utr,
              _currentRow: {
                ...utr._currentRow,
                tableRowIndex: index, // Override the index to act as table row index
              },
            },
            utrv,
            unitConfig: surveyUnitConfigMap.get(utrv.surveyId.toString()),
            displayOptions,
            isMatchedUnit,
            isMatchedNumberScale,
          });
        },
      };
    });

    return inputColumns;
  }

  private getEvidenceColumns(utrvToEvidencesMap: ConvertDataParams['utrvToEvidencesMap']): DataColumn[] {
    if (!utrvToEvidencesMap) {
      return [];
    }

    return [
      {
        code: 'evidenceFiles',
        header: `Evidence Files`,
        accessor: (data) => {
          const [utrv] = data.utrvs;
          if (!utrv) {
            return '';
          }

          const fileDocs = utrvToEvidencesMap.get(utrv._id.toString())?.fileDocs ?? [];
          return fileDocs.map((doc) => doc.metadata?.name ?? '').filter(Boolean).join(', ');
        },
      },
      {
        code: 'externalEvidenceLinks',
        header: `External Evidence Links`,
        accessor: (data) => {
          const [utrv] = data.utrvs;
          if (!utrv) {
            return '';
          }

          const links = utrvToEvidencesMap.get(utrv._id.toString())?.externalLinks ?? [];
          return links.map((doc) => doc.path).filter(Boolean).join(', ');
        },
      },
      {
        code: 'internalEvidenceLinks',
        header: `Internal Evidence Links`,
        accessor: (data) => {
          const [utrv] = data.utrvs;
          if (!utrv) {
            return '';
          }

          const links = utrvToEvidencesMap.get(utrv._id.toString())?.internalLinks ?? [];
          return links.map((doc) => doc.path).filter(Boolean).join(', ');
        },
      },
    ];
  }

  private getExplicitDelegationColumns(
    utrvToStakeholdersMap: ConvertDataParams['utrvToStakeholdersMap']
  ): DataColumn[] {
    if (!utrvToStakeholdersMap) {
      return [];
    }

    return [
      {
        code: 'explicitContributors',
        header: `Explicitly Delegated Contributors`,
        accessor: (data) => {
          const [utrv] = data.utrvs;
          if (!utrv) {
            return '';
          }

          const { contributors = [] } = utrvToStakeholdersMap.get(utrv._id.toString()) ?? {};
          return contributors.map((contributor) => getFullName(contributor)).join(', ');
        },
      },
      {
        code: 'explicitVerifiers',
        header: `Explicitly Delegated Verifiers`,
        accessor: (data) => {
          const [utrv] = data.utrvs;
          if (!utrv) {
            return '';
          }

          const { verifiers = [] } = utrvToStakeholdersMap.get(utrv._id.toString()) ?? {};
          return verifiers.map((verifier) => getFullName(verifier)).join(', ');
        },
      },
    ];
  }
}

let instance: SimpleReportGenerator;
export const getSimpleReportGenerator = () => {
  if (!instance) {
    instance = new SimpleReportGenerator(getBaseReportDownload(), getReportDataResolver());
  }
  return instance;
};
