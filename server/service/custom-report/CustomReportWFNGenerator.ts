import { getGroup } from '@g17eco/core';
import UniversalTracker from '../../models/universalTracker';
import { RatingType, WFN_MODULE_CODE } from '../../types/wfn';
import { customDateFormat, DateFormat, isSame } from '../../util/date';
import { getRating, getScore, mapValueListToNumber } from '../../util/wfn';
import { InitiativeWithMatchedSurveyFilters } from './constants';
import { CustomReportData } from './CustomReportGenerator';
import { BaseUtr, reportUtrProjection, ReportUtrv } from './reportTypes';
import { generateSurveyFilterKey, getInitiativeReportHeader } from './utils';
import { SurveyFilter } from '../../models/customReport';
import { DataPeriods } from '../utr/constants';
import { SurveyType } from '../../models/survey';
import { QUESTION } from '../../util/terminology';

interface ColumnSetup<AccessorProps = { field: string; initiativesWfnSummary: InitiativesWfnSummaryMap }> {
  id: string;
  name: string;
  accessor: (row: AccessorProps) => string | number | undefined;
}

const wfnSubgroups = getGroup('standards', WFN_MODULE_CODE)?.subgroups ?? [];

const wfnSubgroupCodes = wfnSubgroups.map(({ code }) => code) ?? [];

type WfnReportData = {
  utr: BaseUtr;
  utrvs: ReportUtrv[];
};

type InitiativesWfnSummary = {
  [key: string]: {
    [key: string]: number | undefined;
  } & { total: number | undefined; rating: RatingType | undefined };
};

type InitiativesWfnSummaryMap = Map<string, InitiativesWfnSummary>;

interface CustomReportWFNGeneratorProps {
  reportData: CustomReportData[];
  initiatives: InitiativeWithMatchedSurveyFilters[];
  surveyFilters: SurveyFilter[];
}

type ReportDataGenerator = Omit<CustomReportWFNGeneratorProps, 'reportData'> & {
  wfnReportData: WfnReportData[];
};

export class CustomReportWFNGenerator {
  constructor(private utrModel: typeof UniversalTracker) {}

  public async getWfnSummaryAndMetricsData(props: CustomReportWFNGeneratorProps) {
    const { reportData, surveyFilters, initiatives } = props;
    const wfnReportData = this.getWfnDataWithScore(reportData);

    const wfnSummaryData = this.getWfnSummaryData({ wfnReportData, surveyFilters, initiatives });
    const wfnMetricsData = await this.getWfnMetricsData({ wfnReportData, surveyFilters, initiatives });

    return { wfnSummaryData, wfnMetricsData };
  }

  private getWfnDataWithScore(reportData: CustomReportData[]) {
    const wfnReportData = reportData.reduce<{ utr: BaseUtr; utrvs: ReportUtrv[] }[]>((acc, { utrvs, ...utr }) => {
      // only take wfn questions
      if (utr.type === WFN_MODULE_CODE) {
        acc.push({ utr, utrvs });
      }
      return acc;
    }, []);

    return mapValueListToNumber(wfnReportData);
  }

  private getWfnSummaryData({ wfnReportData, initiatives, surveyFilters }: ReportDataGenerator) {
    const columns = this.getSummaryColumns({ surveyFilters, initiatives });
    const initiativesWfnSummary = this.getInitiativesWfnSummary({ wfnReportData, initiatives, surveyFilters });
    const outputData = this.prepareSummaryData({ columns, initiativesWfnSummary });

    return {
      headers: columns.map((h) => h.name),
      records: outputData,
    };
  }

  private getSummaryColumns({
    surveyFilters,
    initiatives,
  }: Pick<ReportDataGenerator, 'surveyFilters' | 'initiatives'>) {
    const hasSingleSurvey = surveyFilters.length === 1;
    const columns: ColumnSetup[] = [
      ...(hasSingleSurvey
        ? [
            {
              id: 'effectiveDate',
              name: 'Report',
              accessor: () => customDateFormat(surveyFilters[0].effectiveDate, DateFormat.MonthYear),
            },
          ]
        : []),
      {
        id: 'pillar',
        name: 'Pillar',
        accessor: ({ field }) => {
          if (field === 'total') {
            return 'Total score';
          }
          if (field === 'rating') {
            return 'Rating';
          }
          return wfnSubgroups.find((subGroup) => subGroup.code === field)?.name ?? '';
        },
      },
    ];

    const subsidiaryColumns: ColumnSetup[] = initiatives
      .map((initiative) => {
        return initiative.matchedSurveyFilters.map(({ effectiveDate, period, type }) => {
          const surveyKey = generateSurveyFilterKey({ effectiveDate, period, type });
          const id = initiative._id.toString();
          return {
            id,
            name: getInitiativeReportHeader({
              name: initiative.name,
              effectiveDate,
              period,
              type,
              surveyIncluded: !hasSingleSurvey,
            }),
            accessor: (row: { field: string; initiativesWfnSummary: InitiativesWfnSummaryMap }) => {
              const { field, initiativesWfnSummary } = row;
              return initiativesWfnSummary.get(surveyKey)?.[id][field];
            },
          };
        });
      })
      .flat();

    return columns.concat(subsidiaryColumns);
  }

  private prepareSummaryData({
    columns,
    initiativesWfnSummary,
  }: {
    columns: ColumnSetup[];
    initiativesWfnSummary: InitiativesWfnSummaryMap;
  }) {
    return [...wfnSubgroupCodes, 'total', 'rating'].map((field) => {
      const row = columns.map(({ accessor }) => {
        return accessor({ field, initiativesWfnSummary });
      });
      return row;
    });
  }

  private getInitiativesWfnSummary(
    props: Pick<ReportDataGenerator, 'wfnReportData' | 'initiatives' | 'surveyFilters'>
  ) {
    const { wfnReportData, initiatives, surveyFilters } = props;
    const initialInitiativeSummary = this.generateInitialInitiativesWfnSummary({ initiatives, surveyFilters });

    return wfnReportData.reduce((initiativesWfnSummary, { utr, utrvs }) => {
      const subGroupCode = wfnSubgroupCodes.find((subGroupCode) => utr.typeTags?.includes(subGroupCode));
      if (!subGroupCode) {
        return initiativesWfnSummary;
      }
      return this.processUtrvs(initiativesWfnSummary, subGroupCode, utrvs);
    }, initialInitiativeSummary);
  }

  private generateInitialInitiativesWfnSummary({
    initiatives,
    surveyFilters,
  }: Pick<ReportDataGenerator, 'initiatives' | 'surveyFilters'>) {
    const wfnSubgroupsInitialScore = wfnSubgroupCodes.reduce(
      (acc, subGroupCode) => ({ ...acc, [subGroupCode]: undefined }),
      {} as Record<string, undefined>
    );

    return new Map(
      surveyFilters.map((surveyFilter) => {
        const initiativeMap = initiatives.reduce((acc, { _id }) => {
          const initiativeId = _id.toString();
          acc[initiativeId] = {
            ...wfnSubgroupsInitialScore,
            total: undefined,
            rating: undefined,
          };
          return acc;
        }, {} as InitiativesWfnSummary);
        return [generateSurveyFilterKey(surveyFilter), initiativeMap];
      })
    );
  }

  private processUtrvs(initiativesWfnSummary:  InitiativesWfnSummaryMap, subGroupCode: string, utrvs: ReportUtrv[]) {
    return utrvs.reduce((acc, utrv) => {
      const initiativeId = utrv.initiativeId.toString();
      const { effectiveDate, surveyPeriod = DataPeriods.Yearly, surveyType = SurveyType.Default } = utrv;
      const surveyKey = generateSurveyFilterKey({ effectiveDate, period: surveyPeriod, type: surveyType });
      const score = getScore(utrv?.valueData?.data);

      if (score === undefined) {
        return acc;
      }
      const existing = acc.get(surveyKey)?.[initiativeId];
      if (existing) {
        existing[subGroupCode] = (existing[subGroupCode] || 0) + score;
        existing.total = (existing.total || 0) + score;
        existing.rating = getRating(existing.total);
      }

      return acc;
    }, initiativesWfnSummary);
  }

  private async getWfnMetricsData({ surveyFilters, wfnReportData, initiatives }: ReportDataGenerator) {
    const utrs = await this.utrModel
      .find({ type: WFN_MODULE_CODE }, reportUtrProjection)
      .sort({ code: 1 })
      .lean<BaseUtr[]>()
      .exec();
    const columns = this.getMetricColumns({ surveyFilters, initiatives });
    const outputData = this.prepareWfnMetricsData({ columns, utrs, wfnReportData });

    return {
      headers: columns.map((h) => h.name),
      records: outputData,
    };
  }

  private getMetricColumns({ surveyFilters, initiatives }: Pick<ReportDataGenerator, 'surveyFilters' | 'initiatives'>) {
    const hasSingleSurvey = surveyFilters.length === 1;
    const columns: ColumnSetup<WfnReportData>[] = [
      ...(hasSingleSurvey
        ? [
            {
              id: 'effectiveDate',
              name: 'Report',
              accessor: () => customDateFormat(surveyFilters[0].effectiveDate, DateFormat.MonthYear),
            },
          ]
        : []),
      {
        id: 'code',
        name: 'Code',
        accessor: ({ utr }) => utr.code,
      },
      {
        id: 'name',
        name: `${QUESTION.CAPITALIZED_SINGULAR} Title`,
        accessor: ({ utr }) => utr.name,
      },
    ];

    const subsidiaryColumns: ColumnSetup<WfnReportData>[] = initiatives
      .map((initiative) => {
        return initiative.matchedSurveyFilters.map(({ effectiveDate, period, type }) => {
          const id = initiative._id.toString();
          return {
            id,
            name: getInitiativeReportHeader({
              name: initiative.name,
              surveyIncluded: !hasSingleSurvey,
              effectiveDate,
              period,
              type,
            }),
            accessor: ({ utrvs }: WfnReportData) => {
              const utrv = utrvs.find(
                (utrv) =>
                  utrv.initiativeId.toHexString() === id &&
                  isSame(utrv.effectiveDate, effectiveDate, 'day') &&
                  utrv.surveyPeriod === period &&
                  utrv.surveyType === type
              );
              return getScore(utrv?.valueData?.data);
            },
          };
        });
      })
      .flat();

    return columns.concat(subsidiaryColumns);
  }

  private prepareWfnMetricsData({
    utrs,
    columns,
    wfnReportData,
  }: {
    utrs: BaseUtr[];
    columns: ColumnSetup<WfnReportData>[];
    wfnReportData: ReportDataGenerator['wfnReportData'];
  }) {
    const wfnDataMap = new Map(wfnReportData.map((data) => [data.utr.code, data]));
    return utrs.map((utr) => {
      const rowData = wfnDataMap.get(utr.code) || { utr, utrvs: [] };
      return columns.map(({ accessor }) => accessor(rowData));
    });
  }
}

let instance: CustomReportWFNGenerator;
export const getCustomReportWFNGenerator = () => {
  if (!instance) {
    instance = new CustomReportWFNGenerator(UniversalTracker);
  }
  return instance;
};
