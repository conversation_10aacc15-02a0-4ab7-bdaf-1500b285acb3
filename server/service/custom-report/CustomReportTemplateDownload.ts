import { ColumnCode, CustomReportTemplate, CustomReportTemplateType } from '../../models/customReport';
import { DataScopeAccess } from '../../models/dataShare';
import { QUESTION } from '../../util/terminology';
import { ObjectId } from 'bson';
import { getTransposedReportGenerator } from './TransposedReportGenerator';
import { getTabularReportGenerator } from './TabularReportGenerator';
import { getSimpleReportGenerator } from './SimpleReportGenerator';
import { getCustomReportTemplateExcel } from './CustomReportTemplateExcel';
import { ReportDataGeneratorResult } from './type';
import { SurveyRepository } from '../../repository/SurveyRepository';

interface CustomReportTemplateDownloadParams {
  initiativeId: ObjectId;
  customReport: Pick<CustomReportTemplate, 'survey' | 'config'>;
}

export const OVERRIDDEN_COLUMN_NAMES: Partial<Record<ColumnCode, string>> = {
  input: 'Input',
  valueLabel: `${QUESTION.CAPITALIZED_SINGULAR} Label`,
  columnLabel: `${QUESTION.CAPITALIZED_SINGULAR} Input`,
};

export const SHEET_NAME = 'Data';

export class CustomReportTemplateDownload {
  constructor(
    private surveyRepo: typeof SurveyRepository,
    private customReportTemplateExcel: ReturnType<typeof getCustomReportTemplateExcel>,
    private simpleReportGenerator: ReturnType<typeof getSimpleReportGenerator>,
    private transposedReportGenerator: ReturnType<typeof getTransposedReportGenerator>,
    private tabularReportGenerator: ReturnType<typeof getTabularReportGenerator>
  ) {}

  public async generate({ initiativeId, customReport }: CustomReportTemplateDownloadParams) {
    const generator = this.getGenerator(customReport.config?.templateType ?? CustomReportTemplateType.Default);
    const params = await this.getDownloadParams({ initiativeId, customReport });
    const { headers, records, maxColumn } = await generator.getDownloadData(params);
    return this.createSheet({ headers, records, maxColumn });
  }

  private getGenerator(templateType: CustomReportTemplateType) {
    switch (templateType) {
      case CustomReportTemplateType.Transposed:
        return this.transposedReportGenerator;
      case CustomReportTemplateType.Tabular:
        return this.tabularReportGenerator;
      case CustomReportTemplateType.Default:
      default:
        return this.simpleReportGenerator;
    }
  }

  private async getDownloadParams({ initiativeId, customReport }: CustomReportTemplateDownloadParams) {
    const {
      survey: {
        initiativeIds = [],
        surveyFilters = [],
        statuses,
        assuranceStatus,
        visibility,
        scope,
        displayMetricOverrides,
        displayUserInput,
      },
      config: { columns = [], ordering } = {},
    } = customReport;

    const surveys =
      initiativeIds.length && surveyFilters.length
        ? await this.surveyRepo.findSurveys({
            initiativeId: { $in: initiativeIds },
            deletedDate: { $exists: false },
            $or: surveyFilters,
          })
        : [];

    return {
      surveys,
      initiativeId,
      downloadScope: {
        scope,
        statuses,
        access: DataScopeAccess.Full,
        assuranceStatus,
        visibilityStatus: visibility,
        displayMetricOverrides,
        displayUserInput,
        displayTag: true,
      },
      columns: columns.map((column) => ({
        ...column,
        header: column.header ?? OVERRIDDEN_COLUMN_NAMES[column.code as ColumnCode],
      })),
      orderingColumns: ordering?.columns,
    };
  }

  private async createSheet({ headers, records, maxColumn }: ReportDataGeneratorResult) {
    const data = headers ? [headers, ...records] : [...records];

    return this.customReportTemplateExcel.createCustomReportTemplateSheet({
      sheets: [{ name: SHEET_NAME, data }],
      maxColumn,
    });
  }
}

let instance: CustomReportTemplateDownload;

export const getCustomReportTemplateDownload = () => {
  if (!instance) {
    instance = new CustomReportTemplateDownload(
      SurveyRepository,
      getCustomReportTemplateExcel(),
      getSimpleReportGenerator(),
      getTransposedReportGenerator(),
      getTabularReportGenerator()
    );
  }
  return instance;
};
