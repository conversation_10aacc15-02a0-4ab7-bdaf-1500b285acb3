/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { ColumnType, TableColumn, UtrValueType } from '../../models/public/universalTrackerType';
import { BaseUtr, ReportUtrv } from './reportTypes';
import { SupportedMeasureUnits, UnitConfig } from '../units/unitTypes';
import { ActionList } from '../utr/constants';
import { NotApplicableTypes, UtrvAssuranceStatus } from '../../models/universalTrackerValue';
import { Option } from '../../models/public/valueList';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import {
  getTableNumberScaleCode,
  getDisplayedTableUnitCode,
  getUtrvDataProp,
  getUtrvTableProp,
  getUtrvValue,
} from '../utr/utrvUtil';
import { MetricUnitManager } from '../units/MetricUnitManager';
import { getCellValueForTableInputWithFormat, getCellValueWithFormat, getColumnValue, getConvertedValue, getDefaultDisplayOptions } from './utils';
import { RecordRow } from './constants';
import { DownloadDisplayOptions, DownloadScopeData } from '../survey/scope/downloadScope';
import { NUMERIC_VALUE_LIST_TOTAL_CODE } from '../../models/universalTracker';
import { ColumnConfig, ConvertDataParams } from './type';

export interface CustomReportData extends BaseUtr {
  /** Max rows from available utrv tables **/
  maxRows?: number;
  /** All values available within this question **/
  utrvs: ReportUtrv[];
}

type BaseExtraData = Record<string, any>;
type DefaultExtraData = Record<string, any>;

export interface CurrentRow<T extends BaseExtraData> extends CustomReportData {
  _currentRow: {
    valueListMap: Map<string, Option[]>;
    /** Definition of table column **/
    column?: ExportColumn;
    /** Which row are we dealing with in answer table **/
    tableRowIndex?: number;
  };
  /** Extra data for the extra columns we want to render **/
  extraData: T;
}

export type ExportColumn = Pick<
  TableColumn,
  | 'code'
  | 'type'
  | 'name'
  | 'unit'
  | 'unitType'
  | 'numberScale'
  | 'listId'
  | 'unitInput'
  | 'numberScaleInput'
  | 'validation'
>;

export interface FormattedCell {
  value: string | number | undefined;
  options?: {
    format?: string;
    type?: string;
  };
}
export type CellData = string | number | FormattedCell | undefined;

export interface DataResolverColumn<T extends BaseExtraData> {
  code: string;
  header: string;
  accessor: (utr: CurrentRow<T>) => CellData;
}

export interface ProcessUtrParams<T extends BaseExtraData = DefaultExtraData> {
  combinedColumns: DataResolverColumn<T>[];
  extraData: T;
  utr: CustomReportData;
  valueListMap: Map<string, Option[]>;
  downloadScope?: Pick<DownloadScopeData, 'statuses' | 'assuranceStatus'>;
  displayOptions?: Pick<DownloadDisplayOptions, 'displayUserInput'>;
  columnConfig?: ColumnConfig;
}

export interface ExtractValueParams<T extends BaseExtraData = DefaultExtraData> {
  utr: CurrentRow<T>;
  utrv: ReportUtrv;
  unitConfig?: UnitConfig;
  displayOptions?: DownloadDisplayOptions;
  extractValueOnly?: boolean;
  isMatchedUnit?: boolean;
  isMatchedNumberScale?: boolean;
}

export class ReportDataResolver {
  private valueListNumericTotal = NUMERIC_VALUE_LIST_TOTAL_CODE;

  constructor(private logger: LoggerInterface) {}

  public getNumericTotalColumnCode() {
    return this.valueListNumericTotal;
  }

  public extractValue(params: ExtractValueParams): CellData {
    const { utr, utrv } = params;

    const naType = this.getNaType(utrv);
    if (naType) {
      return this.naType(naType);
    }

    switch (utr.valueType) {
      case UtrValueType.Text:
      case UtrValueType.Date:
        return utrv.valueData?.data;
      case UtrValueType.ValueList:
      case UtrValueType.ValueListMulti:
        return this.resolveValueListMulti(params);
      case UtrValueType.TextValueList:
        return this.resolveTextValueList(params);
      case UtrValueType.NumericValueList:
        return this.resolveNumericValueList(params);
      case UtrValueType.Table:
        return this.resolveTable(params);
      case UtrValueType.Sample:
      case UtrValueType.Percentage:
      case UtrValueType.Number:
      default:
        return this.resolveNumber(params);
    }
  }

  public processUtr(params: ProcessUtrParams): RecordRow[] {
    const { utr, combinedColumns, valueListMap, extraData, downloadScope, columnConfig } = params;

    const dataPoints = this.getDataPoints({
      columnConfig,
      utr,
      downloadScope,
    });

    const rows = dataPoints.map((utrWithDataPoint) => {
      if (utr.valueType === UtrValueType.Table) {
        return this.processTable({ ...params, utr: utrWithDataPoint });
      }

      if ([UtrValueType.NumericValueList, UtrValueType.TextValueList].includes(utr.valueType as UtrValueType)) {
        return this.processComplexValueList({ ...params, utr: utrWithDataPoint });
      }

      // All the single row values
      const row = combinedColumns.map(({ accessor }) => {
        return accessor({
          ...utrWithDataPoint,
          _currentRow: { valueListMap },
          extraData,
        });
      });

      return [row];
    });

    return rows.flat();
  }

  /**
   * Retrieves the data points (utrv only or including history).
   *
   */
  public getDataPoints({
    columnConfig = {},
    utr,
    downloadScope,
  }: Pick<ProcessUtrParams, 'utr' | 'columnConfig' | 'downloadScope'>) {
    const utrv = utr.utrvs[0];
    if (!utrv) {
      return [utr];
    }

    const isHistoryIncluded = columnConfig.isHistoryIncluded;
    if (!isHistoryIncluded) {
      return [
        {
          ...utr,
          utrvs: [
            {
              ...utrv,
              historyUserId: utrv.history.at(-1)?.userId,
            },
          ],
        },
      ];
    }

    const { statuses, assuranceStatus } = downloadScope ?? {};

    const { dataPoints } = utrv.history.reduce<{
      dataPoints: ReportUtrv[];
      mostRecentUpdated?: ReportUtrv['history'][0];
      mostRecentAssuranceStatus?: UtrvAssuranceStatus;
    }>(
      ({ dataPoints, mostRecentUpdated, mostRecentAssuranceStatus }, historyUtrv, index) => {
        if (historyUtrv.action === ActionList.Created) {
          return { dataPoints };
        }

        // Use utrv as the last history data point
        if (index === utrv.history.length - 1) {
          dataPoints.push({ ...utrv, historyUserId: historyUtrv.userId });
          return { dataPoints };
        }

        // For cases like verified/rejected/restated happen after first restated, the assuranceStatus is not saved so we use the most recent assuranceStatus.
        const dataPointAssuranceStatus = historyUtrv.assuranceStatus ?? mostRecentAssuranceStatus;

        // Skip restated if the next one is verified because those actions always happen at the same time.
        if (
          historyUtrv.assuranceStatus === UtrvAssuranceStatus.Restated &&
          utrv.history[index + 1]?.action === ActionList.Verified
        ) {
          return {
            dataPoints,
            mostRecentUpdated: historyUtrv,
            mostRecentAssuranceStatus: dataPointAssuranceStatus,
          };
        }

        const newMostRecentUpdated = historyUtrv.action === ActionList.Updated ? historyUtrv : mostRecentUpdated;

        // Shadow the logic in SimpleReportDownload.getSimpleReportData to skip history data points by status and assuranceStatus
        if (
          (statuses && statuses.length > 0 && !statuses.includes(historyUtrv.action as ActionList)) ||
          (assuranceStatus &&
            assuranceStatus.length > 0 &&
            !assuranceStatus.includes(dataPointAssuranceStatus as UtrvAssuranceStatus))
        ) {
          return {
            dataPoints,
            mostRecentUpdated: newMostRecentUpdated,
            mostRecentAssuranceStatus: dataPointAssuranceStatus,
          };
        }

        const dataPoint: ReportUtrv = {
          ...utrv,
          status: historyUtrv.action,
          assuranceStatus: dataPointAssuranceStatus,
          value: historyUtrv.value,
          // For cases like verified/rejected the valueData is not saved so we use the most recent updated valueData.
          valueData: historyUtrv.valueData ?? mostRecentUpdated?.valueData,
          note: historyUtrv.note,
          lastUpdated: historyUtrv.date ?? utrv.lastUpdated, // we should always have historyUtrv.date, but probably due to legacy issues when history schema was shared with FrameworkUtr. TODO: Update the field to required.
          unit: historyUtrv.unit ?? mostRecentUpdated?.unit ?? utrv.unit,
          numberScale: historyUtrv.numberScale ?? mostRecentUpdated?.numberScale ?? utrv.numberScale,
          valueType: historyUtrv.valueType ?? mostRecentUpdated?.valueType ?? utrv.valueType,
          historyUserId: historyUtrv.userId,
        };

        dataPoints.push(dataPoint);
        return {
          dataPoints,
          mostRecentUpdated: newMostRecentUpdated,
          mostRecentAssuranceStatus: dataPointAssuranceStatus,
        };
      },
      {
        dataPoints: [],
      }
    );

    return dataPoints.length ? dataPoints.map((dataPoint) => ({ ...utr, utrvs: [dataPoint] })) : [utr];
  }

  public getAllDataPoints(params: Pick<ConvertDataParams, 'data' | 'downloadScope'> & { isHistoryIncluded: boolean }) {
    const { data, isHistoryIncluded, downloadScope } = params;
    return data.reduce<CustomReportData[]>((acc, utr) => {
      const dataPoints = this.getDataPoints({
        columnConfig: { isHistoryIncluded },
        utr,
        downloadScope,
      });
      acc.push(...dataPoints);
      return acc;
    }, []);
  }

  private processTable({ utr, valueListMap, combinedColumns, extraData }: ProcessUtrParams) {
    const output: RecordRow[] = [];
    const tableColumns = utr.valueValidation?.table?.columns;

    if (!tableColumns) {
      this.logger.error(new Error(`UTR ${utr._id} valueType=${utr.valueType} is missing table configuration`));
      return [];
    }

    tableColumns.forEach((c) => {
      // Represent a row for single utrv table column for each row
      const outputRow = combinedColumns.map(({ accessor }) => {
        return accessor({
          ...utr,
          extraData,
          _currentRow: {
            valueListMap,
            column: c,
          },
        });
      });

      output.push(outputRow);
    });
    return output;
  }

  private getValueListOptions(utr: BaseUtr): Option[] | undefined {
    const valueList = utr.valueValidation?.valueList;
    return valueList?.list ?? valueList?.custom;
  }

  /**
   * A copy of frontend getNaType logic
   */
  private getNaType({ status, value, valueData }: ReportUtrv): string {
    // Created items can't have a value set, so can't be N/A
    if (status === ActionList.Created) {
      return '';
    }

    // Value is set, can't be N/A
    if (value !== undefined) {
      return '';
    }

    // value is empty and there is no valueData, must be N/A (legacy utrvs)
    if (!valueData) {
      // This seems to be wrong on frontend, fixed by returning NA rather than '';
      // but I guess never happen, as valueData is always defined
      return NotApplicableTypes.NA;
    }

    //This property is a dead give-away, but is not guaranteed
    const { notApplicableType, table, data } = valueData;

    if (notApplicableType) {
      return notApplicableType;
    }

    // ValueData table/data are both empty
    const emptyTable = table === undefined || table.length === 0;
    return data === undefined && emptyTable ? NotApplicableTypes.NA : '';
  }

  /** ValueList does not contain numeric values, therefore no need to do user input **/
  private resolveValueListMulti({ utrv, utr }: ExtractValueParams) {
    const value = utrv.valueData?.data;
    if (!value) {
      return undefined;
    }
    const options = this.getValueListOptions(utr);
    if (!Array.isArray(options)) {
      return undefined;
    }
    return this.resolveOptions(value, options);
  }

  private resolveOptions(value: string[] | string, options: Option[]) {
    const valueArray = Array.isArray(value) ? value : [value];
    return valueArray
      .reduce((a, code) => {
        const option = options.find((o) => o.code === code);
        return option ? [...a, option.name] : [...a, code];
      }, <string[]>[])
      .join(', ');
  }

  private naType(notApplicableType: string) {
    if (notApplicableType === NotApplicableTypes.NR) {
      return 'NR';
    }
    return 'NA';
  }

  private resolveNumericValueList({ utrv, utr, displayOptions = getDefaultDisplayOptions() }: ExtractValueParams) {
    const { displayUserInput, displayMetricOverrides } = displayOptions;
    const column = utr._currentRow.column;
    const { data, unit, numberScale } = getUtrvDataProp(utrv, displayUserInput) ?? {};
    const decimal = utr.valueValidation?.decimal;
    const shouldConvert = Boolean(displayUserInput && displayMetricOverrides);

    if (column && typeof data === 'object') {
      const conversionProps = {
        overrideProps: { unitInput: utr.unitInput, numberScaleInput: utr.numberScaleInput },
        defaultProps: { unit, numberScale },
      };
      // Handle special case of total (total is not available on import sheet)
      if (column.code === this.valueListNumericTotal) {
        const values: number[] = Object.values(data);
        if (values.length > 0) {
          const rawTotal = values.reduce((a, value) => (!isNaN(value) ? a + Number(value) : a), 0);
          const total = getConvertedValue({ shouldConvert, rawValue: rawTotal, ...conversionProps });
          return getCellValueWithFormat(total, decimal);
        }
        return undefined;
      }

      // convert default value & default unit to preferred unit
      const rawValue = data[column.code];
      const columnValue = getConvertedValue({ shouldConvert, rawValue, ...conversionProps });
      // Add decimal when column value is number
      return getCellValueWithFormat(columnValue, decimal);
    }
  }

  private resolveTextValueList({ utrv, utr }: ExtractValueParams): string | undefined {
    if (utr._currentRow.column && typeof utrv.valueData?.data === 'object') {
      return utrv.valueData.data[utr._currentRow.column.code];
    }
    return undefined;
  }

  private resolveTable({
    utrv,
    utr,
    unitConfig,
    displayOptions = {},
    extractValueOnly,
    isMatchedUnit,
    isMatchedNumberScale
  }: ExtractValueParams) {
    const { column, tableRowIndex = 0, valueListMap } = utr._currentRow;
    const { displayUserInput } = displayOptions;
    const isSingleRow = utr.valueValidation?.table?.validation?.maxRows === 1;
    const tableData = getUtrvTableProp(utrv, displayUserInput);

    if (column && Array.isArray(tableData)) {
      const colData = tableData[tableRowIndex]?.find((col) => col.code === column.code);
      const value = colData?.value;

      // Need pass through 0 here as well
      if (!colData || value === undefined || value === '') {
        // @TODO a quick fix/hack to differentiate table vs others
        return isSingleRow ? undefined : '';
      }

      // Text can use values from valueList, (ValueList was just Text + listId)
      if (
        column.listId &&
        [ColumnType.ValueListMulti, ColumnType.ValueList, ColumnType.Text].includes(column.type as ColumnType)
      ) {
        const options = valueListMap.get(column.listId.toHexString()) ?? [];
        return this.resolveOptions(value, options);
      }

      const colValue = getColumnValue({ colData, col: column, displayOptions });
      if (extractValueOnly ?? isSingleRow) {
        return getCellValueForTableInputWithFormat(colValue, column.validation?.decimal);
      }

      if (ColumnType.Number === column.type && column.unitType === SupportedMeasureUnits.currency) {
        const numberScale = getTableNumberScaleCode(column, colData, displayOptions);
        const currencyValue = MetricUnitManager.withMetrics({
          value: colValue,
          unit: isMatchedUnit ? undefined : unitConfig?.currency || column.unit,
          numberScale: isMatchedNumberScale ? undefined : numberScale,
          unitType: column.unitType,
        });
        return getCellValueForTableInputWithFormat(currencyValue, column.validation?.decimal);
      }

      const numericValue = MetricUnitManager.withMetrics({
        value: colValue,
        unit: isMatchedUnit ? undefined : getDisplayedTableUnitCode(column, colData, displayOptions),
        numberScale: isMatchedNumberScale ? undefined : getTableNumberScaleCode(column, colData, displayOptions),
        unitType: column.unitType,
      });

      return getCellValueForTableInputWithFormat(numericValue, column.validation?.decimal);
    }
  }

  private resolveNumber({ utr, utrv, displayOptions = getDefaultDisplayOptions() }: ExtractValueParams) {
    const { displayMetricOverrides, displayUserInput } = displayOptions;
    const { value: rawValue, unit, numberScale } = getUtrvValue(utrv, displayUserInput);
    const value = getConvertedValue({
      shouldConvert: Boolean(displayUserInput && displayMetricOverrides),
      rawValue,
      defaultProps: { unit, numberScale },
      overrideProps: { unitInput: utr.unitInput, numberScaleInput: utr.numberScaleInput },
    });
    const decimal = utr.valueValidation?.decimal;
    return getCellValueWithFormat(value, decimal);
  }

  public processComplexValueList(params: ProcessUtrParams): RecordRow[] {
    const { utr, combinedColumns, extraData, valueListMap } = params;

    let options = this.getValueListOptions(utr);
    if (!Array.isArray(options)) {
      return [];
    }

    if (utr.valueType === UtrValueType.NumericValueList) {
      options = [...options, { code: this.valueListNumericTotal, name: 'Total' }];
    }

    return options.map((c) => {
      // Represent a row for single utrv value list option
      return combinedColumns.map(({ accessor }) => {
        return accessor({
          ...utr,
          extraData,
          _currentRow: {
            valueListMap,
            column: { ...c, type: ColumnType.Text },
          },
        });
      });
    });
  }
}

let instance: ReportDataResolver;
export const getReportDataResolver = () => {
  if (!instance) {
    instance = new ReportDataResolver(wwgLogger);
  }
  return instance;
};
