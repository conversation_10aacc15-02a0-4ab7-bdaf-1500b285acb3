import { ColumnType, UtrValueType } from '../../models/public/universalTrackerType';
import { BaseReportGenerator } from './BaseReportGenerator';
import { getBaseReportDownload } from './BaseReportDownload';
import { RecordRow } from './constants';
import { CurrentRow, CustomReportData, ProcessUtrParams } from './ReportDataResolver';
import { getTabularReportDataResolver } from './TabularReportDataResolver';
import {
  ConvertDataParams,
  DataColumn,
  ExtendedConvertDataParams,
  ReportDataGeneratorParams,
  ReportDataGeneratorResult,
  TableConfig,
} from './type';
import { checkIsHistoryIncluded, getDownloadNumberScaleCode, getDownloadUnitCode } from './utils';
import { getBaseColumns, getSimpleReportColumns, getOrderedColumns } from './columnUtils';
import { NUMERIC_VALUE_LIST_TOTAL_CODE } from '../../models/universalTracker';
import { Option } from '../../models/public/valueList';
import { getDisplayedTableUnitCode, getTableNumberScaleCode, getUtrvTableProp } from '../utr/utrvUtil';
import { ReportUtrv } from './reportTypes';
import { ALLOWED_SIZES, SINGLE_NUMBER_SCALE_VALUES } from '../units/unitTypes';
import { RowData } from '../../models/public/universalTrackerValueType';
import { ActionList } from '../utr/constants';

export class TabularReportGenerator extends BaseReportGenerator {
  private readonly INPUT_COLUMN_PREFIX = 'column-';
  constructor(
    private baseReportDownload: ReturnType<typeof getBaseReportDownload>,
    private tabularReportDataResolver: ReturnType<typeof getTabularReportDataResolver>
  ) {
    super();
  }

  public async getDownloadData({
    surveys,
    downloadScope,
    initiativeId,
    columns,
    orderingColumns,
  }: ReportDataGeneratorParams) {
    const {
      reportDataWithAltCode,
      valueLists,
      utrTagMap,
      assuranceMap,
      surveyUnitConfigMap,
      utrvAssurances,
      initiativeMap,
      userMap,
    } = await this.baseReportDownload.getReportDataWithExtras({
      surveys,
      downloadScope,
      initiativeId,
      columns,
      orderingColumns,
    });
    const { displayUserInput, displayTag, displayMetricOverrides } = downloadScope;

    return this.convertData({
      data: reportDataWithAltCode,
      valueLists,
      utrTagMap,
      assuranceMap,
      surveyUnitConfigMap,
      utrvAssurances,
      columns: columns ?? getSimpleReportColumns({ downloadScope, assuranceMap }),
      orderingColumns,
      displayOptions: { displayUserInput, displayTag, displayMetricOverrides },
      downloadScope,
      initiativeMap,
      userMap,
    });
  }

  private convertData(params: ConvertDataParams): ReportDataGeneratorResult {
    const { data, columns, orderingColumns, valueLists, downloadScope, displayOptions } = params;

    const valueListMap = new Map(valueLists.map((list) => [String(list._id), list.options]));
    const isHistoryIncluded = checkIsHistoryIncluded(columns);
    const dataMap = this.convertReportDataToMap(data);

    const tableConfigMap = this.prepareTableMatchedInputMap({
      data,
      isHistoryIncluded,
      downloadScope,
      displayOptions,
    });

    const { maxColumn, utrTables } = Array.from(dataMap).reduce(
      (acc, [_code, utrs]) => {
        const { rows, combinedColumns } = this.processUtrsPerTable({
          ...params,
          utrs,
          valueListMap,
          columnConfig: {
            isHistoryIncluded,
          },
          tableConfigMap,
        });
        if (rows.length > 0) {
          const headers = this.getHeaders({ dataColumns: combinedColumns, columns });
          const records = this.sortRecords({
            records: rows,
            orderingColumns,
            dataColumns: combinedColumns,
            data,
            inputColumnPrefix: this.INPUT_COLUMN_PREFIX,
          });
          acc.maxColumn = headers.length > acc.maxColumn ? headers.length : acc.maxColumn;
          acc.utrTables.push({ headers, records });
        }

        return acc;
      },
      { maxColumn: 0, utrTables: [] } as { maxColumn: number; utrTables: { headers: string[]; records: RecordRow[] }[] }
    );

    return {
      records: utrTables.map(({ headers, records }) => [headers, ...records, []]).flat(),
      maxColumn,
    };
  }

  /**
   * report data can contain utrs with same code but different utrvs due to different surveys
   * so we need to group them by code
   */
  private convertReportDataToMap(data: CustomReportData[]) {
    return data.reduce((acc, utr) => {
      const existingUtr = acc.get(utr.code);
      if (existingUtr) {
        existingUtr.push(utr);
        return acc;
      }
      acc.set(utr.code, [utr]);
      return acc;
    }, new Map<string, CustomReportData[]>());
  }

  /**
   * scope: both single-row and multi-row table
   * unit/number scale is considered matched across a row if all columns have the same value.
   * */
  private prepareTableMatchedInputMap({
    data,
    isHistoryIncluded,
    downloadScope,
    displayOptions = { displayUserInput: false, displayMetricOverrides: false },
  }: Pick<ConvertDataParams, 'data' | 'downloadScope' | 'displayOptions'> & { isHistoryIncluded: boolean }): Map<
    string,
    TableConfig
  > {
    const inputMap = new Map();

    const dataPoints = this.tabularReportDataResolver.getAllDataPoints({ data, isHistoryIncluded, downloadScope });

    dataPoints.forEach((utr) => {
      const utrv = utr.utrvs[0];

      if (!utrv || utr.valueType !== UtrValueType.Table) {
        return;
      }

      const initialTableData = [utr.valueValidation?.table?.columns.map((c) => ({ code: c.code } as RowData)) ?? []];
      const tableData = getUtrvTableProp(utrv, displayOptions.displayUserInput) ?? [];
      const table = utrv.status === ActionList.Created ? initialTableData : tableData;

      table.forEach((row, index) => {
        const uniqueKey = this.generateKeyForInputMap({
          utrv,
          rowIndex: index,
        });

        const { unitsInRow, numberScalesInRow } = row.reduce(
          (acc, inputColumn) => {
            const column = utr.valueValidation?.table?.columns?.find((c) => c.code === inputColumn.code);
            // if metric is not answered, still allow unit/number scale matching logic happens below, based on displayOptions (default or override)
            // if metric is answered, ignore unanswered columns
            if (!column || (utrv.status !== ActionList.Created && inputColumn.value === undefined)) {
              return acc;
            }
            const unit = getDisplayedTableUnitCode(column, inputColumn, displayOptions);
            const numberScale = getTableNumberScaleCode(column, inputColumn, displayOptions);
            acc.unitsInRow.add(unit);
            acc.numberScalesInRow.add(numberScale);
            return acc;
          },
          { unitsInRow: new Set(), numberScalesInRow: new Set() }
        );

        inputMap.set(uniqueKey, {
          unitsInRow: Array.from(unitsInRow),
          numberScalesInRow: Array.from(numberScalesInRow),
        });
      });
    });

    return inputMap;
  }

  private processUtrsPerTable(
    params: ConvertDataParams &
      Pick<ProcessUtrParams, 'valueListMap' | 'columnConfig'> & { utrs: CustomReportData[] } & Pick<
        ExtendedConvertDataParams,
        'tableConfigMap'
      >
  ) {
    const {
      assuranceMap,
      initiativeMap,
      downloadScope,
      displayOptions,
      valueListMap,
      columnConfig,
      utrs,
      tableConfigMap,
    } = params;

    /**
     * utrs share the same UTR information, except [utrvs]
     * so we can leverage the first utr to process related utr data & avoid duplication work */
    const [utr] = utrs;
    if (!utr) {
      return { combinedColumns: [], rows: [] };
    }

    const valueList = utr.valueValidation?.valueList;
    // Expand valueList
    let valueListOptions: Option[] | undefined;
    if (valueList?.listId && !valueList.list) {
      valueListOptions = valueListMap.get(String(valueList.listId));
      valueList.list = valueListOptions;
    }
    const combinedColumns = this.getAvailableColumns({ ...params, utr, tableConfigMap });

    const rows = utrs.reduce<RecordRow[]>((acc, utr) => {
      const valueList = utr.valueValidation?.valueList;
      // Expand valueList
      if (valueList?.listId && !valueList.list) {
        valueList.list = valueListOptions;
      }
      const utrParams: ProcessUtrParams = {
        combinedColumns,
        utr,
        valueListMap,
        extraData: { assuranceMap, initiativeMap },
        columnConfig,
        downloadScope,
        displayOptions,
      };
      const rows = this.tabularReportDataResolver.processUtr(utrParams);
      acc.push(...rows);
      return acc;
    }, []);

    return { combinedColumns, rows };
  }

  private getAvailableColumns({
    columns,
    surveyUnitConfigMap,
    displayOptions,
    utrvAssurances,
    utrTagMap,
    utr,
    tableConfigMap,
    userMap,
  }: ExtendedConvertDataParams) {
    const baseColumns = getBaseColumns({ utrvAssurances, utrTagMap, userMap });
    const answerColumns = this.getAnswerColumns({ utr, surveyUnitConfigMap, displayOptions, tableConfigMap });
    const unitColumn = this.getUnitColumn({ surveyUnitConfigMap, displayOptions, tableConfigMap });
    const numberScaleColumn = this.getNumberScaleColumn({ displayOptions, tableConfigMap });

    const allColumns: DataColumn[] = [...baseColumns, ...answerColumns, unitColumn, numberScaleColumn];
    return getOrderedColumns({ columns, allColumns, inputColumnPrefix: this.INPUT_COLUMN_PREFIX });
  }

  private getAnswerColumns({
    utr,
    surveyUnitConfigMap,
    displayOptions,
    tableConfigMap,
  }: Pick<
    ExtendedConvertDataParams,
    'surveyUnitConfigMap' | 'displayOptions' | 'utr' | 'tableConfigMap'
  >): DataColumn[] {
    const inputColumns = this.getInputColumns(utr);
    const defaultAnswerColumn = [{ code: 'input', name: 'Answer', type: '' }];

    return (inputColumns ?? defaultAnswerColumn).map((col, idx) => ({
      code: `${this.INPUT_COLUMN_PREFIX}${idx + 1}`,
      header: col.name,
      accessor: (utr) => {
        const utrv = utr.utrvs[0];
        if (!utrv) {
          return undefined;
        }

        const { unitsInRow, numberScalesInRow } = this.getMeasurementMatchedConfig({ utr, tableConfigMap });

        return this.tabularReportDataResolver.extractValue({
          utr: {
            ...utr,
            _currentRow: { ...utr._currentRow, column: inputColumns ? col : undefined },
          },
          utrv,
          unitConfig: surveyUnitConfigMap.get(utrv.surveyId.toString()),
          displayOptions,
          extractValueOnly: false,
          isMatchedUnit: ALLOWED_SIZES.includes(unitsInRow.length),
          isMatchedNumberScale:
            ALLOWED_SIZES.includes(numberScalesInRow.length) ||
            numberScalesInRow.every((s) => SINGLE_NUMBER_SCALE_VALUES.includes(s)),
        });
      },
    }));
  }

  private generateKeyForInputMap({
    utrv: { lastUpdated, status, _id },
    rowIndex = 0,
  }: {
    utrv: Pick<ReportUtrv, '_id' | 'lastUpdated' | 'status'>;
    rowIndex: number | undefined;
  }) {
    // must include lastUpdated & status in case history is included
    return `${_id.toString()}_${rowIndex}_${lastUpdated.getTime()}_${status}`;
  }

  private getMeasurementMatchedConfig({
    utr,
    tableConfigMap,
  }: {
    utr: CurrentRow<Partial<Pick<ConvertDataParams, 'initiativeMap' | 'assuranceMap'>>>;
  } & Pick<ExtendedConvertDataParams, 'tableConfigMap'>) {
    let currentConfig: TableConfig | undefined;
    const [utrv] = utr.utrvs;
    if (utrv && utr.valueType === UtrValueType.Table) {
      const uniqueKey = this.generateKeyForInputMap({
        utrv,
        rowIndex: utr._currentRow.tableRowIndex,
      });
      currentConfig = tableConfigMap.get(uniqueKey);
    }

    return currentConfig ?? { unitsInRow: [], numberScalesInRow: [] };
  }

  private getInputColumns(utr: CustomReportData) {
    if (utr.valueType === UtrValueType.Table) {
      return utr.valueValidation?.table?.columns;
    }

    if ([UtrValueType.TextValueList, UtrValueType.NumericValueList].includes(utr.valueType)) {
      const valueList = utr.valueValidation?.valueList;
      const options = (valueList?.list ?? valueList?.custom)?.map((op) => ({ ...op, type: ColumnType.Text }));
      if (options) {
        return utr.valueType === UtrValueType.NumericValueList
          ? [...options, { code: NUMERIC_VALUE_LIST_TOTAL_CODE, name: 'Total', type: ColumnType.Number }]
          : options;
      }
    }
  }

  private getUnitColumn({
    surveyUnitConfigMap,
    displayOptions,
    tableConfigMap,
  }: Pick<ConvertDataParams, 'surveyUnitConfigMap' | 'displayOptions'> &
    Pick<ExtendedConvertDataParams, 'tableConfigMap'>): DataColumn {
    return {
      code: 'unit',
      header: 'Unit',
      accessor: (utr) => {
        if (utr.valueType === UtrValueType.Table) {
          const { unitsInRow } = this.getMeasurementMatchedConfig({ utr, tableConfigMap });
          return unitsInRow.length === 1 ? unitsInRow[0] : undefined;
        }
        return getDownloadUnitCode({ utr, displayOptions, surveyUnitConfigMap });
      },
    };
  }

  private getNumberScaleColumn({
    displayOptions,
    tableConfigMap,
  }: Pick<ConvertDataParams, 'displayOptions'> & Pick<ExtendedConvertDataParams, 'tableConfigMap'>): DataColumn {
    return {
      code: 'numberScale',
      header: 'Number Scale',
      accessor: (utr) => {
        if (utr.valueType === UtrValueType.Table) {
          const { numberScalesInRow } = this.getMeasurementMatchedConfig({ utr, tableConfigMap });
          if (numberScalesInRow.length === 1) {
            return numberScalesInRow[0];
          }
          return;
        }
        return getDownloadNumberScaleCode({ utr, displayOptions });
      },
    };
  }
}

let instance: TabularReportGenerator;

export const getTabularReportGenerator = () => {
  if (!instance) {
    instance = new TabularReportGenerator(getBaseReportDownload(), getTabularReportDataResolver());
  }
  return instance;
};
