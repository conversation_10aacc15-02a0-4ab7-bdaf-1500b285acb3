import { ObjectId } from 'bson';
import { UtrValueType } from '../../models/public/universalTrackerType';
import { UniversalTrackerPlain } from '../../models/universalTracker';
import { UniversalTrackerValuePlain, UtrvAssuranceStatus, getAssuranceText } from '../../models/universalTrackerValue';
import { isDefined } from '../../util/type';
import { getCombinedUtrvPartialFields } from '../assurance/utils';
import { QuestionRow } from '../survey/transfer/DataTransform';
import { ActionList } from '../utr/constants';
import { CellData, ExportColumn } from './ReportDataResolver';
import { STATUSES_UI_MAP } from './constants';
import { ReportUtrv } from './reportTypes';
import { PartialFieldsUtrvAssurances } from '../assurance/model/Assurance';
import { MetricUnitManager, UnitTypes } from '../units/MetricUnitManager';
import { RowData, TableData } from '../../models/public/universalTrackerValueType';
import { DownloadDisplayOptions } from '../survey/scope/downloadScope';
import { isNumeric } from '../../util/number';
import { InputColumnRule, isInputColumn, Column, SurveyFilter, isAggregationGroupByColumn, AggregationGroupByRule } from '../../models/customReport';
import { getDisplayedTableUnitCode, getNumberScaleCode, getTableNumberScaleCode, getUnitCode, getUtrvTableProp } from '../utr/utrvUtil';
import { CurrentRowData } from './type';
import { isMultiRowTable } from '../utr/utrUtil';
import { isPercentageColumn } from '../../util/universal-trackers';
import { ALLOWED_SIZES, UnitConfig } from '../units/unitTypes';
import { checkMatchedNumberScale } from '../units/utils';
import { customDateFormat, DateFormat } from '../../util/date';
import { SurveyType } from '../../models/survey';
import { isNumericString } from '../../util/string';

export const getUtrvStatus = ({ status }: Pick<ReportUtrv, 'status'>) => {
  return STATUSES_UI_MAP[status as keyof typeof STATUSES_UI_MAP] || STATUSES_UI_MAP[ActionList.Verified];
};

export const getUtrvAssuranceStatus = (
  utrv: Pick<ReportUtrv, '_id' | 'status' | 'assuranceStatus'>,
  utr: Pick<UniversalTrackerPlain, 'valueType'> & { maxRows: number | undefined; columnCode: string | undefined },
  utrvAssurances: PartialFieldsUtrvAssurances
) => {
  const { assuranceStatus } = utrv;

  if (assuranceStatus === UtrvAssuranceStatus.Partial) {
    const { valueType, maxRows, columnCode } = utr;
    const combinedUtrvPartialFields = getCombinedUtrvPartialFields(utrvAssurances, utrv._id as ObjectId);
    const partialStatus = getAssuranceText(assuranceStatus);

    // when numericValueList or textValueList
    if ([UtrValueType.NumericValueList, UtrValueType.TextValueList].includes(valueType)) {
      const isAssuredField = combinedUtrvPartialFields.some((f) => f.code === columnCode);
      return isAssuredField ? 'Assured' : partialStatus;
    }

    // when singleRowTable
    if (valueType === UtrValueType.Table && maxRows === 1) {
      const isAssuredField = combinedUtrvPartialFields.some((f) => f.code === columnCode && f.rowIndex === 0);
      return isAssuredField ? 'Assured' : partialStatus;
    }

    // when multiRowTable
    return partialStatus;
  }

  return getAssuranceText(assuranceStatus);
};

export const getUtrvCombinedStatus = (
  utrv: Pick<ReportUtrv, '_id' | 'status' | 'assuranceStatus'>,
  utr: Pick<UniversalTrackerPlain, 'valueType'> & { maxRows: number | undefined; columnCode: string | undefined },
  utrvAssurances: PartialFieldsUtrvAssurances
) => {
  const { status, assuranceStatus } = utrv;

  return assuranceStatus && assuranceStatus !== UtrvAssuranceStatus.Created
    ? getUtrvAssuranceStatus(utrv, utr, utrvAssurances)
    : getUtrvStatus({ status });
};

export const shouldApplyDecimal = (rawValue: number | string | undefined, decimal: number | undefined): decimal is number => {
  if (isNaN(Number(rawValue)) || !isDefined(decimal)) {
    return false;
  }
  const decimalOfValue = String(rawValue).split('.')[1]?.length || 0;
  const missingDecimals = decimal - decimalOfValue;
  return missingDecimals > 0;
};

export const getDecimalNumericCellFormat = (decimal: number) => `0.${'0'.repeat(decimal)}`;

export const getCellValueWithFormat = (rawValue: number | string | undefined, decimal: number | undefined) => {
  if (isNaN(Number(rawValue))) {
    return rawValue;
  }

  return {
    value: rawValue, // must be number, text won't work with decimal format
    options: {
      type: 'n', // 'n' means number
      ...(shouldApplyDecimal(rawValue, decimal) ? { format: getDecimalNumericCellFormat(decimal) } : {}),
    },
  };
};

export const getCellValueForTableInputWithFormat = (rawValue: number | string | undefined, presetDecimal?: number) => {
  if (!isNumericString(rawValue)) {
    return rawValue;
  }

  const decimalOfValue = String(rawValue).split('.')[1]?.length || 0;
  const decimal = Math.max(decimalOfValue, presetDecimal || 0);
  return {
    value: rawValue, // must be number, text won't work with decimal format
    options: {
      type: 'n', // 'n' means number
      ...(decimal > 0 ? { format: getDecimalNumericCellFormat(decimal) } : {}),
    },
  };
};

export const getRawValue = (cellValue: CellData) => {
  if (cellValue && typeof cellValue === 'object' && 'value' in cellValue) {
    return cellValue.value;
  }
  return cellValue;
};

export const getFormattedValue = (rawValue: any, decimal: number | undefined) => {
  if (isNaN(Number(rawValue))) {
    return rawValue;
  }

  return shouldApplyDecimal(rawValue, decimal) ? Number(rawValue).toFixed(decimal) : rawValue;
};

export const getExportedValue = (row: Pick<QuestionRow, 'values' | 'value'> | { values?: undefined, value: string[]}) => {
  // Table transform populate row.values, the rest use row.value
  if (row.values) {
    return row.values.filter((value) => value !== undefined).join(', ');
  }
  return Array.isArray(row.value) ? row.value.join(', ') : row.value;
};

export const getDefaultDisplayOptions = (): DownloadDisplayOptions => {
  return { displayMetricOverrides: false, displayUserInput: false };
};

export const getConvertedValue = ({
  shouldConvert,
  rawValue,
  overrideProps,
  defaultProps,
}: {
  shouldConvert: boolean;
  rawValue: number | undefined;
  overrideProps: Pick<UniversalTrackerPlain, 'unitInput' | 'numberScaleInput'>;
  defaultProps: Pick<UniversalTrackerValuePlain, 'unit' | 'numberScale'>;
}) => {
  if (!shouldConvert || rawValue === undefined) {
    return rawValue;
  }

  const { unit, numberScale = 'single' } = defaultProps;
  const { unitInput, numberScaleInput } = overrideProps;
  let value = rawValue;

  if (numberScaleInput && numberScaleInput !== numberScale) {
    value = MetricUnitManager.convertUnit(value,numberScale, numberScaleInput);
  }

  if (unitInput && unitInput !== unit) {
    value = MetricUnitManager.convertUnit(value, unit, unitInput);
  }
  return value;
};

export const getColumnValue = ({
  col,
  colData,
  displayOptions = getDefaultDisplayOptions(),
}: {
  col: ExportColumn;
  colData: RowData;
  displayOptions: Pick<DownloadDisplayOptions, 'displayMetricOverrides' | 'displayUserInput'>;
}) => {
  const { value } = colData;
  if (value === undefined) {
    return '';
  }

  if (!isNumeric(value)) {
    return value;
  }

  const convertedValue = getConvertedValue({
    shouldConvert: Boolean(displayOptions.displayUserInput && displayOptions.displayMetricOverrides),
    rawValue: value,
    overrideProps: {
      unitInput: col.unitInput,
      numberScaleInput: col.numberScaleInput,
    },
    defaultProps: {
      unit: colData.unit,
      numberScale: colData.numberScale,
    },
  });

  return getFormattedValue(convertedValue, col.validation?.decimal);
};

export const checkIsHistoryIncluded = (columns: Column[]) => {
  return columns.some((column) => isInputColumn(column) && column.rule === InputColumnRule.All);
}

export const checkAggregationByGroupExcluded = (columns: Column[]) => {
  // Undefined rule is treated as excluded
  return columns.some((column) => isAggregationGroupByColumn(column) && column.rule !== AggregationGroupByRule.Include);
};

const isUnansweredTableColumn = (utr: CurrentRowData) => {
  return utr.utrvs?.[0]?.status === ActionList.Created;
}

// get unit code for table value type
export const getDownloadTableUnitCode = ({
  utr,
  surveyUnitConfigMap,
  displayOptions,
  ignoreMultiRowTable,
}: {
  utr: CurrentRowData;
  surveyUnitConfigMap: Map<string, UnitConfig>;
  displayOptions: DownloadDisplayOptions;
  ignoreMultiRowTable?: boolean;
}) => {
  // unanswered table should still display the unit of override is set
  if (isUnansweredTableColumn(utr) && utr._currentRow.column) {
    if (displayOptions.displayMetricOverrides && utr._currentRow.column.unitInput) {
      return utr._currentRow.column.unitInput;
    }
    if (isPercentageColumn(utr._currentRow.column)) {
      return '%';
    }
    return utr._currentRow.column.unit ?? '';
  }

  const params = getTableParams({ utr, displayOptions, ignoreMultiRowTable });
  if (!params) {
    return '';
  }
  if (params.column.unitType === UnitTypes.currency) {
    const unitConfig = surveyUnitConfigMap.get(utr.utrvs[0]?.surveyId.toString());
    // [GU-5448] unitConfig?.currency || column.unit, value input
    if (unitConfig?.currency) {
      return unitConfig.currency;
    }
  }
  return getDisplayedTableUnitCode(params.column, params.rowData, displayOptions);
};

// get unit code for other value types, except table: number, percentage, numericValueList
export const getDownloadUnitCode = ({
  utr,
  surveyUnitConfigMap,
  displayOptions,
}: {
  utr: CurrentRowData;
  surveyUnitConfigMap: Map<string, UnitConfig>;
  displayOptions: DownloadDisplayOptions;
}) => {
  if (utr.unitType === UnitTypes.currency) {
    const unitConfig = surveyUnitConfigMap.get(utr.utrvs[0]?.surveyId.toString());
    if (unitConfig?.currency) {
      return unitConfig.currency;
    }
  }

  if (utr.valueType === UtrValueType.Percentage) {
    return '%';
  }

  const unit = utr.unit || '';
  const unitInput = utr.unitInput || '';
  const [utrv] = utr.utrvs;
  if (!utrv) {
    return unit;
  }
  return getUnitCode(utrv, { unit, unitInput }, displayOptions) || '';
};

// get number scale code for table value type
export const getDownloadTableNumberScaleCode = ({
  utr,
  displayOptions,
  ignoreMultiRowTable,
}: {
  utr: CurrentRowData;
  displayOptions: DownloadDisplayOptions;
  ignoreMultiRowTable?: boolean;
}) => {
  // unanswered table should still display the unit of override is set
  if (isUnansweredTableColumn(utr) && utr._currentRow.column) {
    if (displayOptions.displayMetricOverrides && utr._currentRow.column.numberScaleInput) {
      return utr._currentRow.column.numberScaleInput;
    }
    return utr._currentRow.column.numberScale ?? '';
  }

  const params = getTableParams({ utr, displayOptions, ignoreMultiRowTable });
  return params ? getTableNumberScaleCode(params.column, params.rowData, displayOptions) : '';
};

// get unit code for other value types, except table: number, percentage, numericValueList
export const getDownloadNumberScaleCode = ({
  utr,
  displayOptions,
}: {
  utr: CurrentRowData;
  displayOptions: DownloadDisplayOptions;
}) => {
  const numberScale = utr.numberScale || '';
  const numberScaleInput = utr.numberScaleInput || '';

  const [utrv] = utr.utrvs;
  if (!utrv) {
    return numberScale;
  }
  return getNumberScaleCode(utrv, { numberScale, numberScaleInput }, displayOptions) || '';
};

const getTableParams = ({
  utr,
  displayOptions,
  ignoreMultiRowTable = false,
}: {
  utr: CurrentRowData;
  displayOptions: DownloadDisplayOptions;
  ignoreMultiRowTable?: boolean;
}) => {
  if (ignoreMultiRowTable && isMultiRowTable(utr)) {
    return undefined;
  }
  const { column, tableRowIndex = 0 } = utr._currentRow;

  if (!column) {
    /* istanbul ignore next */
    return undefined;
  }

  const [utrv] = utr.utrvs;

  if (!utrv) {
    return undefined;
  }

  const tableData = getUtrvTableProp(utrv, displayOptions.displayUserInput);
  const rowData = tableData?.[tableRowIndex]?.find((col) => col.code === column.code);
  return rowData ? { column, rowData } : undefined;
};

const getReportTypeLabel = (type: SurveyType) => {
  switch (type) {
    case SurveyType.Default:
      return 'Report';
    case SurveyType.Aggregation:
      return 'Combined report';
    case SurveyType.Materiality:
      return 'Materiality';
    case SurveyType.AutoAggregation:
      return 'Auto-aggregated report';
  }
};

export const getInitiativeReportHeader = (params: SurveyFilter & { name: string; surveyIncluded: boolean }) => {
  const { name, surveyIncluded, effectiveDate, period, type } = params;
  const reportType = getReportTypeLabel(type);
  if (!surveyIncluded) {
    return name;
  }
  return `${name} (${customDateFormat(effectiveDate, DateFormat.MonthYear)} ${period} ${reportType})`;
};

export const generateSurveyFilterKey = (params: SurveyFilter) => {
  const { effectiveDate, period, type } = params;
  return `${customDateFormat(effectiveDate)}-${period}-${type}`;
};

/**
 * Check if the table data is safe to use for multi-row table inputs.
 * Safe to use means that all rows in the table data have the same unit and number scale for each column.
 * @param {object} params Parameters for the function.
 * @param {TableData} params.tableData The table data to be checked.
 * @param {string[]} params.columnCodes The codes of the columns to be checked.
 * @returns {boolean} True if the table data is safe to use, false otherwise.
 */
export const checkIsTableDataSafeToUse = ({ tableData, columnCodes }: { tableData: TableData; columnCodes: string[] }): boolean => {
  if (columnCodes.length === 0) {
    // no columns, table columns config is invalid
    return false;
  }
  const tableColumns = tableData.flat();
  for (const code of columnCodes) {
    const matchedColumns = tableColumns.filter((column) => column.code === code);
    const isMatchedUnit = ALLOWED_SIZES.includes(new Set(matchedColumns.map((column) => column.unit)).size);
    const uniqueNumberScales = new Set(matchedColumns.map((column) => column.numberScale));

    if (!isMatchedUnit || !checkMatchedNumberScale(uniqueNumberScales)) {
      return false;
    }
  }
  return true;
};
