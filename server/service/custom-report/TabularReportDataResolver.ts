import { UtrValueType } from '../../models/public/universalTrackerType';
import { getUtrvTableProp } from '../utr/utrvUtil';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import { RecordRow } from './constants';
import { ExtractValueParams, getReportDataResolver, ProcessUtrParams, ReportDataResolver } from './ReportDataResolver';
import { ConvertDataParams } from './type';

export class TabularReportDataResolver {
  constructor(private logger: LoggerInterface, private reportDataResolver: ReportDataResolver) {}

  public processUtr(params: ProcessUtrParams): RecordRow[] {
    const { utr, columnConfig, downloadScope } = params;

    const dataPoints = this.reportDataResolver.getDataPoints({
      columnConfig,
      utr,
      downloadScope,
    });

    const rows = dataPoints.map((utrWithDataPoint) => {
      if (utr.valueType === UtrValueType.Table) {
        return this.processTable({ ...params, utr: utrWithDataPoint });
      }

      if ([UtrValueType.NumericValueList, UtrValueType.TextValueList].includes(utr.valueType as UtrValueType)) {
        return this.processComplexValueList({ ...params, utr: utrWithDataPoint });
      }

      // All the single row values
      const row = this.processRow({ ...params, utr: utrWithDataPoint });

      return [row];
    });

    return rows.flat();
  }

  private processTable({ utr, valueListMap, combinedColumns, extraData, displayOptions }: ProcessUtrParams) {
    const output: RecordRow[] = [];
    const utrv = utr.utrvs[0];
    const tableData = getUtrvTableProp(utrv, displayOptions?.displayUserInput);

    // unsupported
    if (!tableData || !Array.isArray(tableData)) {
      return [];
    }

    // unanswered
    if (tableData.length === 0) {
      const outputRow = this.processRow({ utr, valueListMap, combinedColumns, extraData });
      return [outputRow];
    }

    tableData.forEach((_row, tableRowIndex) => {
      const outputRow = this.processRow({ utr, valueListMap, combinedColumns, extraData, tableRowIndex });
      output.push(outputRow);
    });

    return output;
  }

  private processComplexValueList(params: ProcessUtrParams) {
    const { utr, combinedColumns, extraData, valueListMap } = params;
    return [
      combinedColumns.map(({ accessor }) => {
        return accessor({ ...utr, extraData, _currentRow: { valueListMap } });
      }),
    ];
  }

  private processRow(
    params: Pick<ProcessUtrParams, 'utr' | 'valueListMap' | 'combinedColumns' | 'extraData'> & {
      tableRowIndex?: number;
    }
  ) {
    const { utr, combinedColumns, valueListMap, extraData, tableRowIndex } = params;
    return combinedColumns.map(({ accessor }) => {
      return accessor({
        ...utr,
        _currentRow: { valueListMap, tableRowIndex },
        extraData,
      });
    });
  }

  public extractValue(params: ExtractValueParams) {
    return this.reportDataResolver.extractValue(params);
  }

  public getAllDataPoints(params: Pick<ConvertDataParams, 'data' | 'downloadScope'> & { isHistoryIncluded: boolean }) {
    return this.reportDataResolver.getAllDataPoints(params);
  }
}

let instance: TabularReportDataResolver;

export const getTabularReportDataResolver = () => {
  if (!instance) {
    instance = new TabularReportDataResolver(wwgLogger, getReportDataResolver());
  }
  return instance;
};
