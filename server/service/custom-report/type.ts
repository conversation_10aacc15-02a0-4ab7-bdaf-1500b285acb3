import { ObjectId } from 'bson';
import { Column, OrderingColumn } from '../../models/customReport';
import { ValueList as ValueListPlain } from '../../models/public/valueList';
import { SurveyModelPlain } from '../../models/survey';
import { PartialFieldsUtrvAssurances } from '../assurance/model/Assurance';
import { DownloadDisplayOptions, DownloadMultiScope, DownloadScopeData } from '../survey/scope/downloadScope';
import { UnitConfig } from '../units/unitTypes';
import { RecordRow } from './constants';
import { CustomReportData, DataResolverColumn } from './ReportDataResolver';
import { InitiativePlain } from '../../models/initiative';
import { UserPlain } from '../../models/user';
import { DocumentPlain } from '../../models/document';

export interface ReportDataGeneratorParams {
  surveys: SurveyModelPlain[];
  downloadScope: DownloadMultiScope;
  initiativeId: ObjectId;
  columns?: Column[];
  orderingColumns?: OrderingColumn[];
}

export interface ReportDataGeneratorResult {
  headers?: string[];
  records: RecordRow[];
  maxColumn?: number;
  utrvToEvidencesMap?: UtrvToEvidencesMap;
}

export interface ColumnConfig {
  isHistoryIncluded?: boolean;
  isAggregationByGroupExcluded?: boolean;
}

export interface ConvertDataParams {
  data: CustomReportData[];
  valueLists: ValueListPlain[];
  utrTagMap: Map<string, string[]>;
  assuranceMap: Map<string, string[] | undefined>;
  surveyUnitConfigMap: Map<string, UnitConfig>;
  utrvAssurances: PartialFieldsUtrvAssurances;
  columns: Column[];
  orderingColumns?: OrderingColumn[];
  displayOptions: DownloadDisplayOptions;
  downloadScope?: Pick<DownloadScopeData, 'statuses' | 'assuranceStatus'>;
  initiativeMap: Map<string, InitiativePlain & { subsidiaryHierarchy?: string }>;
  utrvToEvidencesMap?: UtrvToEvidencesMap;
  utrvToStakeholdersMap?: Map<string, { contributors: ReportStakeholder[]; verifiers: ReportStakeholder[] }>;
  userMap?: Map<string, UserPlain>;
}

export interface TableConfig {
  unitsInRow: (string | undefined)[];
  numberScalesInRow: (string | undefined)[];
}

export interface ExtendedConvertDataParams extends ConvertDataParams {
  utr: CustomReportData;
  tableConfigMap: Map<string, TableConfig>;
}

type ExtraData = Partial<Pick<ConvertDataParams, 'assuranceMap' | 'initiativeMap'> & { groupedByCodes?: string[] }>;
export type DataColumn = DataResolverColumn<ExtraData>;
export type OrderingColumnWithIndex = OrderingColumn & { index: number };
export type CurrentRowData = Parameters<DataColumn['accessor']>[0];
export type ReportStakeholder = Pick<UserPlain, '_id' | 'firstName' | 'surname'>;

export type UtrvToEvidencesMap = Map<
  string,
  {
    externalLinks: DocumentPlain[];
    internalLinks: DocumentPlain[];
    fileDocs: DocumentPlain[];
  }
>;
