/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { UniversalTrackerPlain } from '../../models/universalTracker';
import { UniversalTrackerValuePlain } from '../../models/universalTrackerValue';
import { KeysEnum } from '../../models/public/projectionUtils';
import { ObjectId } from 'bson';
import { SurveyType } from '../../models/survey';
import { DataPeriods } from '../../service/utr/constants';

export type BaseUtr = Pick<
  UniversalTrackerPlain,
  | '_id'
  | 'type'
  | 'typeCode'
  | 'valueType'
  | 'valueLabel'
  | 'numberScale'
  | 'unitType'
  | 'unit'
  | 'valueValidation'
  | 'name'
  | 'code'
  | 'alternatives'
  | 'tags'
  | 'instructions'
  | 'unitInput'
  | 'numberScaleInput'
  | 'typeTags'
  | 'valueAggregation'
  | 'valueListOrdered'
  | 'valueListTargets'
  | 'targetDirection'
  | 'created'
>;
export type ReportUtrv = Pick<
  UniversalTrackerValuePlain,
  | '_id'
  | 'value'
  | 'valueData'
  | 'valueType'
  | 'initiativeId'
  | 'status'
  | 'note'
  | 'isPrivate'
  | 'assuranceStatus'
  | 'unit'
  | 'numberScale'
  | 'effectiveDate'
  | 'lastUpdated'
  | 'history'
  | 'compositeData'
  | 'type'
  | 'universalTrackerId'
  | 'stakeholders'
  /**
   * ReportUtrv has been used for at least Simple report/Custom report template, Custom report download
   * surveyName is only used in Custom report template (default, transposed, tabular), should be available to Simple report as well but not used
   * surveyType & surveyPeriod is only available in Custom report download (initiative, survey,metrics comparison)
   */
> & {
  surveyId: ObjectId;
  surveyType?: SurveyType;
  surveyPeriod?: DataPeriods;
  surveyName?: string;
  /** userId of the current history utrv */
  historyUserId?: ObjectId;
};

export const reportUtrProjection: KeysEnum<BaseUtr> = {
  _id: 1,
  type: 1,
  unit: 1,
  unitType: 1,
  numberScale: 1,
  unitInput: 1,
  numberScaleInput: 1,
  valueLabel: 1,
  valueType: 1,
  valueValidation: 1,
  valueAggregation: 1,
  alternatives: 1,
  tags: 1,
  name: 1,
  code: 1,
  typeCode: 1,
  instructions: 1,
  typeTags: 1,
  valueListOrdered: 1,
  valueListTargets: 1,
  targetDirection: 1,
  created: 1
} as const;

// Generate projection for first in the array syntax
export const reportUtrArrayElemProjection = Object.entries(reportUtrProjection).reduce((acc, [key, v]) => {
  return {
    ...acc,
    [key]: { $arrayElemAt: [`$universalTracker.${key}`, 0] },
  };
}, {} as Record<keyof typeof reportUtrProjection, { $arrayElemAt: [string, 0] }>);

export const reportUtrvProjection: KeysEnum<Omit<ReportUtrv, 'historyUserId'>> = {
  _id: 1,
  value: 1,
  valueData: 1,
  valueType: 1,
  initiativeId: 1,
  status: 1,
  surveyId: 1,
  note: 1,
  isPrivate: 1,
  assuranceStatus: 1,
  unit: 1,
  numberScale: 1,
  effectiveDate: 1,
  surveyType: 1,
  surveyPeriod: 1,
  lastUpdated: 1,
  history: 1,
  compositeData: 1,
  surveyName: 1,
  type: 1,
  universalTrackerId: 1,
  stakeholders: 1
};
