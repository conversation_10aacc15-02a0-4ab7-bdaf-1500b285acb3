import config from '../../config';
import { Column, ColumnCode } from '../../models/customReport';
import { getFullName } from '../../models/user';
import { customDateFormat, DateFormat } from '../../util/date';
import { getTagTextByUtrId } from '../../util/metric-group';
import { PACK, QUESTION, SURVEY } from '../../util/terminology';
import { getCsvName } from '../assurance/csvContext';
import { DownloadMultiScope } from '../survey/scope/downloadScope';
import { getStandardName } from '../utr/standards';
import { generateMapping } from './dataResolverUtil';
import { CurrentRow } from './ReportDataResolver';
import { ConvertDataParams, DataColumn } from './type';
import { getUtrvAssuranceStatus, getUtrvCombinedStatus, getUtrvStatus } from './utils';

const DATE_COLUMN_CODES = ['effectiveDate', 'updatedDate'] satisfies ColumnCode[];
type DateColumnCode = (typeof DATE_COLUMN_CODES)[number];
export const DATE_COLUMN_FORMAT: Record<DateColumnCode, DateFormat> = {
  updatedDate: DateFormat.SortableSlash,
  effectiveDate: DateFormat.MonthYear,
};
export const isDateColumn = (columnCode: ColumnCode): columnCode is DateColumnCode =>
  DATE_COLUMN_CODES.includes(columnCode as DateColumnCode);

export const getBaseColumns = ({
  utrvAssurances,
  utrTagMap,
  userMap,
}: Pick<ConvertDataParams, 'utrvAssurances' | 'utrTagMap' | 'userMap'>): DataColumn[] => [
  {
    code: 'standard',
    header: `${SURVEY.CAPITALIZED_ADJECTIVE} ${PACK.CAPITALIZED_SINGULAR}`,
    accessor: (data) => getStandardName(data.type),
  },
  {
    code: 'code',
    header: 'Code',
    accessor: (data) => data.typeCode,
  },
  {
    code: 'mapping',
    header: 'Mapping',
    accessor: generateMapping,
  },
  {
    code: 'valueType',
    header: `${QUESTION.CAPITALIZED_SINGULAR} Type`,
    accessor: (data) => data.valueType,
  },
  {
    code: 'name',
    header: 'Title',
    accessor: (data) => data.name,
  },
  {
    code: 'valueLabel',
    header: QUESTION.CAPITALIZED_SINGULAR,
    accessor: (data) => data.valueLabel,
  },
  {
    code: 'columnLabel',
    header: `Sub-${QUESTION.SINGULAR}`,
    accessor: (data) => data._currentRow.column?.name,
  },
  {
    // Combination of status and assurance status.
    code: 'combinedStatus',
    header: 'Status',
    accessor: (data) => {
      const [utrv] = data.utrvs;
      if (!utrv) {
        return '';
      }

      return getUtrvCombinedStatus(
        utrv,
        {
          valueType: data.valueType,
          maxRows: data.maxRows,
          columnCode: data._currentRow.column?.code,
        },
        utrvAssurances
      );
    },
  },
  {
    code: 'note',
    header: 'Further Explanation / Notes',
    accessor: (utr) => {
      return utr.utrvs[0]?.note;
    },
  },
  {
    code: 'provenance',
    header: 'Provenance & Evidence',
    accessor: (utr) => {
      const { _id, utrvs } = utr;
      if (!utrvs[0]) {
        return undefined;
      }

      return `${config.email.publicHostname}/universal-tracker-provenance/${_id}/${utrvs[0]._id}`;
    },
  },
  {
    code: 'reporter',
    header: 'Reporter',
    accessor: (utr) => {
      const { utrvs } = utr;
      if (!utrvs[0] || !userMap) {
        return undefined;
      }

      // Determine which userId to use based on config
      const userId = utrvs[0].historyUserId;

      if (!userId) {
        return undefined;
      }

      const user = userMap.get(userId.toString());
      return user ? getFullName(user, undefined) : undefined;
    },
  },
  {
    code: 'tags',
    header: 'Tags',
    accessor: (utr) => {
      return getTagTextByUtrId(utrTagMap, utr._id);
    },
  },
  {
    code: 'assurance',
    header: 'Assurance',
    accessor: (utr) => {
      const utrv = utr.utrvs[0];
      if (!utrv) {
        return undefined;
      }
      return utr.extraData.assuranceMap?.get(utrv._id.toString())?.join(', ');
    },
  },
  {
    code: 'instructions',
    header: `${QUESTION.CAPITALIZED_SINGULAR} Instructions`,
    accessor: (utr) => {
      return utr.instructions;
    },
  },
  {
    code: 'status',
    header: 'Status',
    accessor: (data) => {
      const [utrv] = data.utrvs;
      if (!utrv) {
        return '';
      }

      return getUtrvStatus(utrv);
    },
  },
  {
    code: 'assuranceStatus',
    header: 'Assurance Status',
    accessor: (data) => {
      const [utrv] = data.utrvs;
      if (!utrv) {
        return '';
      }

      return getUtrvAssuranceStatus(
        utrv,
        {
          valueType: data.valueType,
          maxRows: data.maxRows,
          columnCode: data._currentRow.column?.code,
        },
        utrvAssurances
      );
    },
  },
  {
    code: 'updatedDate',
    header: 'Updated Date',
    accessor: (data) => {
      const [utrv] = data.utrvs;
      if (!utrv) {
        return '';
      }

      return customDateFormat(utrv.lastUpdated, DATE_COLUMN_FORMAT['updatedDate']);
    },
  },
  {
    code: 'effectiveDate',
    header: `${SURVEY.CAPITALIZED_ADJECTIVE} Date`,
    accessor: (data) => {
      const [utrv] = data.utrvs;
      if (!utrv) {
        return '';
      }

      return customDateFormat(utrv.effectiveDate, DATE_COLUMN_FORMAT['effectiveDate']);
    },
  },
  {
    code: 'subsidiaryCode',
    header: 'Subsidiary Code',
    accessor: (data) => {
      return getInitiative(data)?.code ?? '';
    },
  },
  {
    code: 'subsidiaryName',
    header: 'Subsidiary Name',
    accessor: (data) => {
      return getInitiative(data)?.name ?? '';
    },
  },
  {
    code: 'subsidiaryHierarchy',
    header: 'Subsidiary Hierarchy',
    accessor: (data) => {
      return getInitiative(data)?.subsidiaryHierarchy ?? '';
    },
  },
  {
    code: 'surveyName',
    header: `${SURVEY.CAPITALIZED_SINGULAR} Name`,
    accessor: (data) => {
      const [utrv] = data.utrvs;
      if (!utrv) {
        return '';
      }

      return (
        utrv.surveyName ||
        getCsvName({
          initiative: getInitiative(data),
          survey: {
            effectiveDate: utrv.effectiveDate,
            name: utrv.surveyName,
          },
          _id: utrv.surveyId,
        })
      );
    },
  },
];

const getInitiative = (data: CurrentRow<Partial<Pick<ConvertDataParams, 'assuranceMap' | 'initiativeMap'>>>) => {
  return data.extraData.initiativeMap?.get(data.utrvs[0]?.initiativeId.toString());
};

const SIMPLE_REPORT_COLUMNS: ColumnCode[] = [
  'standard',
  'code',
  'mapping',
  'valueType',
  'name',
  'valueLabel',
  'columnLabel',
  'unit',
  'numberScale',
  'combinedStatus',
  'input',
  'note',
  'tags',
  'assurance',
  'provenance',
];

export const DELEGATED_SURVEYS_COLUMNS: ColumnCode[] = [
  'effectiveDate',
  ...SIMPLE_REPORT_COLUMNS,
]

const EVIDENCE_COLUMNS: ColumnCode[] = ['evidenceFiles',
  'externalEvidenceLinks',
  'internalEvidenceLinks'];
const EXPLICIT_DELEGATION_COLUMNS: ColumnCode[] = [
  'explicitContributors',
  'explicitVerifiers'
]
export const FULL_EXPORT_REPORT_COLUMNS: ColumnCode[] = [
  ...SIMPLE_REPORT_COLUMNS,
  ...EVIDENCE_COLUMNS,
  ...EXPLICIT_DELEGATION_COLUMNS
];

export const hasEvidenceColumns = (columns: Column[] | undefined) => {
  return columns?.some(c => EVIDENCE_COLUMNS.includes(c.code));
}

export const hasExplicitDelegationColumns = (columns: Column[] | undefined) => {
  return columns?.some(c => EXPLICIT_DELEGATION_COLUMNS.includes(c.code));
}

export const hasReporterColumn = (columns: Column[] | undefined) => {
  return columns?.some(c => c.code === 'reporter');
}

export const getSimpleReportColumns = ({
  downloadScope,
  assuranceMap,
}: {
  downloadScope: DownloadMultiScope;
  assuranceMap: Map<string, string[]>;
}) => {
  return SIMPLE_REPORT_COLUMNS.filter((c) => {
    switch (c) {
      case 'tags': {
        return Boolean(downloadScope.displayTag);
      }
      case 'assurance': {
        return assuranceMap.size > 0;
      }
      default: {
        return true;
      }
    }
  }).map((code) => ({ code }));
};

export const getOrderedColumns = ({
  columns,
  allColumns,
  inputColumnPrefix,
}: {
  columns: Column[];
  allColumns: DataColumn[];
  inputColumnPrefix: string;
}) => {
  return columns.reduce((acc, c) => {
    if (c.code === 'input') {
      const inputColumns = allColumns.filter((col) => col.code.startsWith(inputColumnPrefix));
      acc.push(...inputColumns);
      return acc;
    }

    const column = allColumns.find((col) => col.code === c.code);
    if (column) {
      acc.push(column);
    }
    return acc;
  }, [] as DataColumn[]);
};
