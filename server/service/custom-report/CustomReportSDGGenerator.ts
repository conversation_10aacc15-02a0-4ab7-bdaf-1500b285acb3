import ScorecardFactory, { Scorecard } from '../scorecard/ScorecardFactory';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import sdgMap from '../../static/un-sdg';
import { BATCH_SIZE, DECIMAL_PLACES, InitiativeMapType, RecordRow, SurveyMapType } from './constants';
import { DateFormat, customDateFormat } from '../../util/date';
import { getBreadcrumbs } from '../../util/string';
import { SurveyType, isAggregatedSurvey } from '../../models/survey';

interface RowParams {
  goal: string;
  target: string | undefined;
  // Map for scorecards, can be initiatives scorecards map or surveys scorecards map
  scorecardsMap: Map<string, Scorecard>;
}

interface ColumnSetup {
  id: string;
  name: string;
  accessor: (row: RowParams) => string | number | undefined;
}

interface InitiativesSDGParams {
  initiatives: InitiativeMapType[];
}

interface DateComparisonSDGParams {
  surveys: SurveyMapType[];
}

export class CustomReportSDGGenerator {
  constructor(private logger: LoggerInterface) {}

  private getSDGColumns(): ColumnSetup[] {
    return [
      {
        id: 'goal',
        name: 'Goal',
        accessor: (row: RowParams) => row.goal,
      },
      {
        id: 'target',
        name: 'Target',
        accessor: (row: RowParams) => (row.target ? row.target.split('.').pop() : '-'),
      },
    ];
  }

  private prepareRowData({
    columns,
    scorecardsMap,
  }: {
    columns: ColumnSetup[];
    scorecardsMap: Map<string, Scorecard>;
  }) {
    return sdgMap.reduce<RecordRow[]>((acc, sdgGoal) => {
      const row = columns.map(({ accessor }) => {
        return accessor({
          goal: sdgGoal.code,
          target: undefined,
          scorecardsMap,
        });
      });
      acc.push(row);
      sdgGoal.targets.forEach((target) => {
        const targetRow = columns.map(({ accessor }) => {
          return accessor({
            goal: sdgGoal.code,
            target: target.code,
            scorecardsMap,
          });
        });
        acc.push(targetRow);
      });

      return acc;
    }, []);
  }

  private getDecimalValue(value: number | undefined) {
    return value ? value.toFixed(DECIMAL_PLACES) : '';
  }

  private getDisplaySurveyType(surveyType: SurveyType) {
    return isAggregatedSurvey(surveyType) ? 'Combined survey' : 'Survey';
  }

  private getDisplaySurveyName(survey: SurveyMapType) {
    return `${this.getDisplaySurveyType(survey.type)} - ${survey.period} ${customDateFormat(
      survey.effectiveDate,
      DateFormat.YearMonth
    )}`;
  }

  private async batchGetInitiativeScorecardsMap({ initiatives }: InitiativesSDGParams) {
    const initiativeScorecardsMap = new Map<string, Scorecard>();
    let promises: Promise<Scorecard>[] = [];

    for (const index in initiatives) {
      const initiative = initiatives[index];
      const isLastRun = Number(index) === initiatives.length - 1;
      promises.push(new ScorecardFactory().getByInitiative(initiative, { includeTargets: true }));

      if (promises.length % BATCH_SIZE === 0 || isLastRun) {
        const batchResults = await Promise.all(promises);
        batchResults.forEach((scoreCard) => {
          if (scoreCard.initiativeId) {
            initiativeScorecardsMap.set(scoreCard.initiativeId.toHexString(), scoreCard);
          }
        });
        if (!isLastRun) {
          this.logger.info(`Processed batch up to ${Number(index) + 1} initiative scorecards`);
        } else {
          this.logger.info(`Processed remaining batch, total ${initiatives.length} initiative scorecards`);
        }
        promises = [];
      }
    }

    return initiativeScorecardsMap;
  }

  /**
   * Retrieves the SDG data for subsidiary reports.
   *
   * @param initiatives - The list of initiatives.
   * @return The SDG data for export as a sheet, including columns, headers, and records.
   */
  public async getInitiativesSDGData({
    initiatives,
  }: InitiativesSDGParams): Promise<{ headers: string[]; records: RecordRow[] }> {
    const columns = this.getSDGColumns();

    const subsidiaryColumns: ColumnSetup[] = initiatives.map((initiative) => {
      const id = initiative._id.toHexString();
      return {
        id,
        name: getBreadcrumbs((initiative.parentNames || []).concat(initiative.name)),
        accessor: ({ goal, target, scorecardsMap }: RowParams) => {
          const initiativeScorecard = scorecardsMap.get(id);
          if (!initiativeScorecard) {
            return '';
          }
          const cardGoal = initiativeScorecard.goals.find(({ sdgCode }) => sdgCode === goal);
          if (!cardGoal) {
            return '';
          }

          if (!target) {
            return this.getDecimalValue(cardGoal.actual);
          }
          return this.getDecimalValue(cardGoal.targets.find(({ sdgCode }) => sdgCode === target)?.actual);
        },
      };
    });

    const combinedColumns: ColumnSetup[] = columns.concat(subsidiaryColumns);

    const initiativeScorecardsMap = await this.batchGetInitiativeScorecardsMap({ initiatives });

    const outputData = this.prepareRowData({
      columns: combinedColumns,
      scorecardsMap: initiativeScorecardsMap,
    });

    return {
      headers: combinedColumns.map((h) => h.name),
      records: outputData,
    };
  }

  private async batchGetSurveyScorecardsMap({ surveys }: DateComparisonSDGParams) {
    const surveyScorecardsMap = new Map<string, Scorecard>();
    let promises: Promise<Scorecard>[] = [];

    for (const index in surveys) {
      const survey = surveys[index];
      const isLastRun = Number(index) === surveys.length - 1;
      promises.push(new ScorecardFactory().getBySurveyId(survey._id));

      if (promises.length % BATCH_SIZE === 0 || isLastRun) {
        const batchResults = await Promise.all(promises);
        batchResults.forEach((scoreCard) => {
          if (scoreCard.surveyId) {
            surveyScorecardsMap.set(scoreCard.surveyId.toHexString(), scoreCard);
          }
        });
        if (!isLastRun) {
          this.logger.info(`Processed batch up to ${Number(index) + 1} survey scorecards`);
        } else {
          this.logger.info(`Processed remaining batch, total ${surveys.length} survey scorecards`);
        }
        promises = [];
      }
    }

    return surveyScorecardsMap;
  }

  /**
   * Retrieves the SDG data for date comparison reports.
   *
   * @param surveys - The list of surveys.
   * @return The SDG data for export as a sheet, including columns, headers, and records.
   */
  public async getDateComparisonSDGData({
    surveys,
  }: DateComparisonSDGParams): Promise<{ headers: string[]; records: RecordRow[] }> {
    const columns = this.getSDGColumns();

    const surveysColumns: ColumnSetup[] = surveys.map((survey) => {
      const id = survey._id.toHexString();
      return {
        id,
        name: this.getDisplaySurveyName(survey),
        accessor: ({ goal, target, scorecardsMap }: RowParams) => {
          const surveyScorecard = scorecardsMap.get(id);
          if (!surveyScorecard) {
            return '';
          }
          const cardGoal = surveyScorecard.goals.find(({ sdgCode }) => sdgCode === goal);
          if (!cardGoal) {
            return '';
          }

          if (!target) {
            return this.getDecimalValue(cardGoal.actual);
          }
          return this.getDecimalValue(cardGoal.targets.find(({ sdgCode }) => sdgCode === target)?.actual);
        },
      };
    });

    const combinedColumns: ColumnSetup[] = columns.concat(surveysColumns);

    const surveyScorecardsMap = await this.batchGetSurveyScorecardsMap({ surveys });

    const outputData = this.prepareRowData({
      columns: combinedColumns,
      scorecardsMap: surveyScorecardsMap,
    });

    return {
      headers: combinedColumns.map((h) => h.name),
      records: outputData,
    };
  }
}

let instance: CustomReportSDGGenerator;
export const getCustomReportSDGGenerator = () => {
  if (!instance) {
    instance = new CustomReportSDGGenerator(wwgLogger);
  }
  return instance;
};
