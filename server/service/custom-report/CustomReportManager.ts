/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import CustomReport, {
  CustomReportInitiatives,
  CustomReportInitiativesExtended,
  CustomReportModel,
  CustomReportPlain,
  CustomReportTemplate,
  isCustomReportTemplate,
  isInitiativesReport,
  MetricTypes,
} from '../../models/customReport';
import UniversalTracker, { UniversalTrackerPlain, UtrType } from "../../models/universalTracker";
import CalculationUniversalTrackerService from "../utr/CalculationUniversalTrackerService";
import { InitiativePlain } from "../../models/initiative";
import { ObjectId } from 'bson';
import { SurveyRepository } from '../../repository/SurveyRepository';
import { SurveyType } from '../../models/survey';
import { DataPeriods } from '../../service/utr/constants';

type CreateCustomReportData = Pick<CustomReportPlain, 'name' | 'description' | 'type' | 'survey' | 'config' | 'initiativeId'>

interface UpdateCustomReportData extends CreateCustomReportData {
  _id: string;
}

export class CustomReportManager {
  constructor(private customReportModel: typeof CustomReport, private surveyRepo: typeof SurveyRepository) {}

  public async getByInitiativeId(initiativeId: string) {
    return CustomReport.find({ initiativeId }).sort({ created: 'desc' }).lean().exec();
  }

  public async getById({ id, initiativeId }: { id: ObjectId; initiativeId: ObjectId }): Promise<CustomReportPlain> {
    const report = await this.customReportModel
      .findOne({ _id: id, initiativeId })
      .populate('universalTrackers')
      .orFail()
      .lean()
      .exec();
    if (isCustomReportTemplate(report)) {
      return this.getCustomReportTemplate(report);
    }
    if (isInitiativesReport(report)) {
      return this.getSubsidiaryComparisonReport(report);
    }

    return report;
  }

  public async getCustomReportTemplate(report: CustomReportTemplate): Promise<CustomReportTemplate> {
    const {
      survey: { initiativeIds, surveyFilters, ids = [] },
    } = report;

    // New template or old template but empty surveys don't need to convert data.
    if ((initiativeIds && surveyFilters) || !ids.length) {
      return report;
    }

    const surveys = await this.surveyRepo.findByIds(ids);

    return {
      ...report,
      survey: {
        ...report.survey,
        ids: undefined,
        initiativeIds: surveys.map(({ initiativeId }) => initiativeId),
        surveyFilters: surveys.map(({ effectiveDate, type, period }) => ({
          effectiveDate,
          type,
          period,
        })),
      },
    };
  }

  public async getSubsidiaryComparisonReport(reportModel: CustomReportInitiatives & {
    toObject?: () => CustomReportInitiatives
  }): Promise<CustomReportInitiativesExtended> {
    // In order to merge the properties the report must be converted to a plain object.
    const report = reportModel.toObject ? reportModel.toObject() : reportModel;

    const {
      survey: { surveyFilters, effectiveDate = '', type = SurveyType.Default, period = DataPeriods.Yearly },
    } = report;

    if (surveyFilters) {
      return report as CustomReportInitiativesExtended;
    }

    return {
      ...report,
      survey: {
        ...report.survey,
        surveyFilters: [{ effectiveDate: new Date(effectiveDate), type, period }],
        effectiveDate: undefined,
        type: undefined,
        period: undefined,
      },
    };
  }

  public async create(data: CreateCustomReportData) {
    const report = new CustomReport(data);
    return report.save();
  }

  public async createFromBubbles(data: CreateCustomReportData, initiative: InitiativePlain) {
    const USAGE_CODE = '4';

    const report = new CustomReport(data);

    initiative.linkedUniversalTrackers
      .filter((linkedUtr) => linkedUtr.usage.includes(USAGE_CODE))
      .map((linkedUtr) => new ObjectId(linkedUtr.universalTrackerId))
      .forEach((utrId) => {
        report.metrics.push({
          utrId,
          type: MetricTypes.Calculated,
        });
      });

    return report.save();
  }

  public async update(report: CustomReportModel, data: UpdateCustomReportData) {
    report.name = data.name ?? report.name;
    report.description = data.description ?? report.description;
    report.type = data.type ?? report.type;
    report.survey = data.survey ?? report.survey;
    report.config = data.config ?? report.config;
    report.lastUpdated = new Date();
    return report.save();
  }

  public async addMetric(report: CustomReportModel, type: MetricTypes, metric: UniversalTrackerPlain) {
    report.metrics.push({
      utrId: metric._id,
      type: type,
    });
    return report.save();
  }

  public async removeMetric(report: CustomReportModel, utrId: string, save = true) {
    const utr = await UniversalTracker.findById(utrId).exec();

    report.metrics = report.metrics.filter((metric) => String(metric.utrId) !== utrId);
    if (save) {
      await report.save();
    }

    if (utr?.type === UtrType.Calculation) {
      await CalculationUniversalTrackerService.deprecateUtr(utr, report.initiativeId);
    }

    return report;
  }
}

let instance: CustomReportManager;
export const getCustomReportManager = () => {
  if (!instance) {
    instance = new CustomReportManager(CustomReport, SurveyRepository);
  }

  return instance;
};
