/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { UniversalTrackerPlain } from '../../models/universalTracker';
import { getStandardName } from '../utr/standards';

export const generateMapping = (utr: Pick<UniversalTrackerPlain, 'type' | 'alternatives'>): string => {

  if (!utr.alternatives) {
    return ''
  }

  return Object.entries(utr.alternatives).reduce((acc, [code, alternative]) => {
    if (utr.type !== code) {
      acc.push(`${getStandardName(code)} ${alternative.typeCode ?? ''}`)
    }
    return acc;
  }, [] as string[]).join(', ')
}
