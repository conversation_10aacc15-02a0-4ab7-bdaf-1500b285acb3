import { ObjectId } from 'bson';
import { PipelineStage } from 'mongoose';
import { Column, OrderingColumn } from '../../models/customReport';
import Survey, { SurveyModelPlain } from '../../models/survey';
import { AssuranceRepository } from '../../repository/AssuranceRepository';
import { ValueListRepository } from '../../repository/ValueListRepository';
import { applyVisibilityFilter } from '../../repository/visibilityFilter';
import { naturalSort } from '../../util/string';
import { mergeUtrOverrides } from '../../util/universal-trackers';
import { getPreferredAltName, getPreferredTypeCode, getPreferredValueLabel } from '../assurance/csvContext';
import { AssuranceStatus } from '../assurance/model/Assurance';
import {
  getInitiativeUniversalTrackerService,
  InitiativeUniversalTrackerService,
} from '../initiative/InitiativeUniversalTrackerService';
import { CustomTagManager } from '../metric/CustomTagManager';
import { DownloadMultiScope, DownloadScope } from '../survey/scope/downloadScope';
import { UnitConfig } from '../units/unitTypes';
import { extractListId } from '../utr/utrUtil';
import { CustomReportData } from './ReportDataResolver';
import { reportUtrArrayElemProjection, ReportUtrv, reportUtrvProjection } from './reportTypes';
import { getInitiativeTreeService, InitiativeTreeService } from '../initiative/InitiativeTreeService';
import { combinedEvidence, getStakeholdersEvidence } from '../utr/utrvHistory';
import User, { UserPlain } from '../../models/user';
import { ReportStakeholder, UtrvToEvidencesMap } from './type';
import { UniversalTrackerValuePlain } from '../../models/universalTrackerValue';
import documentModel, { DocumentPlain, DocumentType } from '../../models/document';
import { hasEvidenceColumns, hasExplicitDelegationColumns, hasReporterColumn } from './columnUtils';
import { UserRepository } from '../../repository/UserRepository';

interface DownloadParams {
  surveys: SurveyModelPlain[];
  downloadScope: DownloadMultiScope;
  initiativeId: ObjectId;
  columns?: Column[];
  orderingColumns?: OrderingColumn[];
  surveyFiltersMap?: Map<string, PipelineStage[]>;
}

export class BaseReportDownload {
  constructor(
    private initiativeUtrService: InitiativeUniversalTrackerService,
    private initiativeTreeService: InitiativeTreeService
  ) {}

  public async getReportDataWithExtras({
    surveys,
    downloadScope,
    initiativeId,
    columns,
    orderingColumns,
    surveyFiltersMap,
  }: DownloadParams) {
    const surveyIds = surveys.map((survey) => survey._id);
    const utrvPipelineStages = surveyFiltersMap?.get(surveyIds[0].toString());
    // TODO: Refactor getSimpleReportData to take surveys.
    const reportData = (
      await Promise.all(
        surveys.map(async (survey) => this.getSimpleReportData(survey, downloadScope, utrvPipelineStages))
      )
    ).flat();

    const ids: ObjectId[] = [];
    reportData.forEach((utr) => {
      ids.push(...extractListId(utr.valueValidation));
    });

    const preferredTypes = downloadScope.scope.standards ?? [];

    if (!orderingColumns?.length) {
      reportData.sort(
        (a, b) =>
          naturalSort(getPreferredTypeCode(a, preferredTypes) ?? '', getPreferredTypeCode(b, preferredTypes) ?? '') ||
          naturalSort(getPreferredAltName(a, preferredTypes), getPreferredAltName(b, preferredTypes))
      );
    }

    const valueLists = await ValueListRepository.findByIds(ids);

    const utrIds = reportData.map((utr) => utr._id);

    const utrTagMap = await CustomTagManager.getUtrTagMap(initiativeId, utrIds);

    const utrOverridesMap = await this.initiativeUtrService.getUtrOverridesMap({
      initiativeId,
      utrIds,
    });

    const reportUtrvs = reportData.map(({ utrvs }) => utrvs).flat();

    const utrvIds = reportUtrvs.map((v) => v._id as ObjectId);
    const assuranceOrganizationMap = await AssuranceRepository.getSurveyAssuranceUtrvOrganizations({
      surveyIds,
      utrvIds,
    });
    const assuranceMap = new Map(assuranceOrganizationMap.map((utrv) => [String(utrv._id), utrv.names]));

    // Handle partial assurance fields
    const utrvAssurances = await AssuranceRepository.getSurveyAssuranceUtrvs(surveyIds, {
      status: { $in: [AssuranceStatus.Completed, AssuranceStatus.Partial] },
    });

    const reportDataWithAltCode = reportData.map((data) => {
      const initiativeUtr = utrOverridesMap.get(data._id.toString());
      return {
        ...data,
        typeCode: getPreferredTypeCode(data, preferredTypes),
        name: getPreferredAltName(data, preferredTypes),
        valueLabel: getPreferredValueLabel(data, preferredTypes),
        ...mergeUtrOverrides({ utr: data, initiativeUtr }),
      };
    });

    const surveyUnitConfigMap = new Map<string, UnitConfig>(
      surveys.map((survey) => [survey._id.toString(), survey.unitConfig])
    );

    const utrvToEvidencesMap = hasEvidenceColumns(columns) ? await this.getUtrvToEvidencesMap(reportUtrvs) : undefined;
    const utrvToStakeholdersMap = hasExplicitDelegationColumns(columns)
      ? await this.getUtrvToStakeholdersMap(reportUtrvs)
      : undefined;

    const userMap = hasReporterColumn(columns) ? await this.getUserMap(reportUtrvs) : undefined;

    return {
      reportDataWithAltCode,
      valueLists,
      utrTagMap,
      assuranceMap,
      surveyUnitConfigMap,
      utrvAssurances,
      initiativeMap: await this.initiativeTreeService.getFullTreeInitiativeMap(initiativeId),
      utrvToEvidencesMap,
      utrvToStakeholdersMap,
      userMap,
    };
  }

  private async getUtrvToEvidencesMap(utrvs: Pick<UniversalTrackerValuePlain, '_id' | 'history' | 'status'>[]) {
    const utrvToEvidenceIdsMap = new Map<string, ObjectId[]>(
      utrvs.map((utrv) => {
        const { updateHistory, verifyHistory } = getStakeholdersEvidence(utrv);
        const evidenceIds = combinedEvidence([updateHistory, verifyHistory]);
        return [utrv._id.toString(), evidenceIds];
      })
    );

    const docIds = Array.from(utrvToEvidenceIdsMap.values()).flat();
    const docs = await documentModel
      .find({ _id: { $in: docIds } })
      .lean()
      .exec();
    const utrvToEvidencesMap: UtrvToEvidencesMap = new Map(
      utrvs.map((utrv) => {
        const utrvId = utrv._id.toString() as string;
        const evidenceIds = utrvToEvidenceIdsMap.get(utrvId) ?? [];
        const { externalLinks, internalLinks, fileDocs } = docs.reduce(
          (acc, doc) => {
            if (evidenceIds.some((id) => id.equals(doc._id))) {
              if (doc.type === DocumentType.File) {
                acc.fileDocs.push(doc);
              } else {
                if (doc.public) {
                  acc.externalLinks.push(doc);
                } else {
                  acc.internalLinks.push(doc);
                }
              }
            }
            return acc;
          },
          {
            externalLinks: [] as DocumentPlain[],
            internalLinks: [] as DocumentPlain[],
            fileDocs: [] as DocumentPlain[],
          }
        );
        return [utrvId, { externalLinks, internalLinks, fileDocs }];
      })
    );

    return utrvToEvidencesMap;
  }

  private async getUtrvToStakeholdersMap(utrvs: ReportUtrv[]) {
    const stakeholderIds = utrvs.reduce((acc, utrv) => {
      const utrvStakeholders = [...(utrv.stakeholders?.stakeholder ?? []), ...(utrv.stakeholders?.verifier ?? [])];
      return [...acc, ...utrvStakeholders];
    }, [] as ObjectId[]);

    const users = await User.find({ _id: { $in: stakeholderIds } }, { _id: 1, firstName: 1, surname: 1 })
      .lean<ReportStakeholder[]>()
      .exec();

    const utrvToStakeholdersMap = new Map<
      string,
      { contributors: ReportStakeholder[]; verifiers: ReportStakeholder[] }
    >();
    utrvs.forEach((utrv) => {
      utrvToStakeholdersMap.set(utrv._id.toString(), {
        contributors: users.filter((u) => (utrv.stakeholders?.stakeholder ?? []).some((s) => s._id.equals(u._id))),
        verifiers: users.filter((u) => (utrv.stakeholders?.verifier ?? []).some((s) => s._id.equals(u._id))),
      });
    });
    return utrvToStakeholdersMap;
  }

  private async getUserMap(reportUtrvs: ReportUtrv[]) {
    const uniqueUserIds = Array.from(
      new Set(reportUtrvs.flatMap((utrv) => utrv.history.map((h) => h.userId.toString())))
    ).map((id) => new ObjectId(id));
    const uniqueUsers = await UserRepository.findByIds(uniqueUserIds);
    return new Map<string, UserPlain>(uniqueUsers.map((u) => [u._id.toString(), u]));
  }

  /**
   * Load all the data required to generate simple report
   */
  private async getSimpleReportData(
    survey: SurveyModelPlain,
    downloadScope: DownloadMultiScope,
    utrvPipelineStages?: PipelineStage[]
  ): Promise<CustomReportData[]> {
    const pipelineStagesOnOverride = downloadScope.displayMetricOverrides
      ? await this.initiativeUtrService.getPipelineStagesOnOverride(survey.initiativeId, 'utrvs')
      : undefined;

    const basePipeline: PipelineStage[] = [
      {
        $match: {
          _id: survey._id,
          initiativeId: new ObjectId(survey.initiativeId),
          deletedDate: { $exists: false },
        },
      },
      {
        $lookup: {
          from: 'universal-tracker-values',
          localField: 'visibleUtrvs',
          foreignField: '_id',
          as: 'utrvs',
        },
      },
      {
        $project: {
          initiativeId: 1,
          utrvs: {
            ...reportUtrvProjection,
            universalTrackerId: 1,
            surveyId: '$_id',
            surveyName: '$name',
          },
        },
      },
      {
        $unwind: { path: '$utrvs' },
      },
      ...(utrvPipelineStages ? utrvPipelineStages : []),
      ...(pipelineStagesOnOverride ? pipelineStagesOnOverride.initiativeUtrLookupAndUnwind : []),
      {
        $project: {
          initiativeId: 1,
          utrvs: {
            ...reportUtrvProjection,
            universalTrackerId: 1,
            note: {
              $reduce: {
                input: '$utrvs.history',
                initialValue: '',
                in: {
                  $cond: {
                    if: { $eq: ['$$this.action', 'updated'] },
                    then: '$$this.note',
                    else: '$$value',
                  },
                },
              },
            },
            ...(pipelineStagesOnOverride ? pipelineStagesOnOverride.initiativeUtrOverrides : {}),
          },
        },
      },
      {
        $group: {
          _id: '$utrvs.universalTrackerId',
          utrvs: { $push: '$utrvs' },
          maxRows: {
            $max: {
              $size: {
                $ifNull: ['$utrvs.valueData.table', []],
              },
            },
          },
        },
      },
      {
        $lookup: {
          from: 'universal-trackers',
          localField: '_id',
          foreignField: '_id',
          as: 'universalTracker',
        },
      },
      {
        $project: {
          ...reportUtrArrayElemProjection,
          maxRows: 1,
          // Need to add universalTrackerId for generateMultiScopeMatch to work
          universalTrackerId: { $arrayElemAt: [`$universalTracker._id`, 0] },
          utrvs: reportUtrvProjection,
        },
      },
    ];

    const $match = await DownloadScope.generateMultiScopeMatch({ ...downloadScope, sourceName: survey.sourceName });
    if ($match) {
      basePipeline.push({ $match });
    }
    if (downloadScope.visibilityStatus) {
      basePipeline.push({
        $match: applyVisibilityFilter({}, downloadScope.visibilityStatus, 'utrvs.'),
      });
    }
    if (downloadScope.statuses && downloadScope.statuses.length > 0) {
      basePipeline.push({
        $match: { 'utrvs.status': { $in: downloadScope.statuses } },
      });
    }

    if (downloadScope.assuranceStatus && downloadScope.assuranceStatus.length > 0) {
      basePipeline.push({
        $match: { 'utrvs.assuranceStatus': { $in: downloadScope.assuranceStatus } },
      });
    }

    return Survey.aggregate([
      ...basePipeline,
      {
        $sort: {
          name: 1,
        },
      },
    ]).exec();
  }
}

let instance: BaseReportDownload;

export const getBaseReportDownload = () => {
  if (!instance) {
    instance = new BaseReportDownload(getInitiativeUniversalTrackerService(), getInitiativeTreeService());
  }
  return instance;
};
