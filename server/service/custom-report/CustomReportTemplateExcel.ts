import XLSX from '@sheet/core';
import { getExcel } from '../file/Excel';
import { COLUMNS, DARK_GREY_COLOUR } from '../file/constants';

interface SheetCreateOptions<T = unknown> {
  sheets: { name: string; data: T[] }[];
  headers?: string[];
  isBoldHeader?: boolean;
}

interface CellStyle {
  [name: string]: any;
}

export class CustomReportTemplateExcel {
  constructor(private excel: ReturnType<typeof getExcel>) {}

  public async createCustomReportTemplateSheet({
    sheets,
    maxColumn = COLUMNS.length,
  }: {
    sheets: SheetCreateOptions<unknown[]>['sheets'];
    maxColumn?: number;
  }) {
    const book = await this.excel.createBook();
    const columns = this.generateSheetColumns(maxColumn);

    sheets.forEach(({ name, data }) => {
      const plainSheetData = this.excel.getPlainSheetData(data);
      const sheet = this.applyCellOptionsForCustomReportTemplate({
        sheet: this.excel.arrayToSheet(plainSheetData),
        data,
        columns,
      });

      plainSheetData.forEach((_row, idx) => {
        // header starts at index 0 or after a blank row []
        if (idx === 0 || (idx && plainSheetData[idx - 1].length === 0)) {
          const styles: CellStyle = {
            bold: true,
            alignment: { horizontal: 'left', vertical: 'top', wrapText: true },
            fgColor: { rgb: DARK_GREY_COLOUR },
          };
          columns.forEach((col) => {
            this.excel.applyStyles(sheet, { column: col, row: idx + 1 }, styles);
          });
        }
      });
      this.excel.addToBook(book, sheet, name);
    });

    return book;
  }

  /**
   * Specific logic for flow of Custom Report Template export
   * Loop through each cell to check if it has custom configs, no longer depends on inputColsIndex
   * could be replaced for applyCellOptions()
   */
  public applyCellOptionsForCustomReportTemplate({
    sheet,
    data,
    columns = COLUMNS,
  }: {
    sheet: XLSX.WorkSheet;
    data: unknown[][];
    columns?: string[];
  }) {
    if (!data.length) {
      return sheet;
    }

    data.forEach((row, rowIndex) => {
      columns.forEach((colText, colIndex) => {
        const cell = row[colIndex];
        // handle when cell has custom configs
        if (cell && typeof cell === 'object' && 'options' in cell) {
          const { options } = cell;
          const cellFormat = options && typeof options === 'object' && 'format' in options ? options.format : undefined;
          const cellPosition = `${colText}${rowIndex + 1}`;
          sheet[cellPosition].z = cellFormat;

          const cellType = options && typeof options === 'object' && 'type' in options ? options.type : undefined;
          sheet[cellPosition].t = cellType;
        }
      });
    });

    return sheet;
  }

  /**
   * Generate column letters dynamically, output: ['A', 'B', ..., 'Z', 'AA', 'AB', ..., 'AZ', 'ZA', ..., 'ZZ'];
   * As Custom Report Template (Tabular) export can exceed 26 columns of A-Z
   */
  private generateSheetColumns(length: number) {
    if (length <= COLUMNS.length) {
      return COLUMNS;
    }
    const result = [];

    for (let i = 0; i < length; i++) {
      let str = '';
      let remainder = i;

      while (remainder >= COLUMNS.length) {
        str = COLUMNS[remainder % COLUMNS.length] + str;
        remainder = Math.floor(remainder / COLUMNS.length) - 1;
      }

      str = COLUMNS[remainder] + str;
      result.push(str);
    }

    return result;
  }
}

let instance: CustomReportTemplateExcel;

export const getCustomReportTemplateExcel = () => {
  if (!instance) {
    instance = new CustomReportTemplateExcel(getExcel());
  }
  return instance;
};
