import { revertFormattedToDate } from '../../util/date';
import { Column, OrderingColumn } from '../../models/customReport';
import { UtrValueType } from '../../models/public/universalTrackerType';
import { OrderingDirection } from '../../types/ordering';
import { DATE_COLUMN_FORMAT, isDateColumn } from './columnUtils';
import { RecordRow } from './constants';
import {
  ConvertDataParams,
  DataColumn,
  OrderingColumnWithIndex,
  ReportDataGeneratorParams,
  ReportDataGeneratorResult,
} from './type';

const INPUT_COLUMN_PREFIX = 'input-';

/**
 * Create an abstract class to have 1 abstract function
 * to be implemented later by sub-classes: SimpleReportGenerator, TransposedReportGenerator, TabularReportGenerator
 */
export abstract class BaseReportGenerator {
  abstract getDownloadData(params: ReportDataGeneratorParams): Promise<ReportDataGeneratorResult>;

  public getHeaders({
    dataColumns,
    columns,
    inputColumnPrefix = INPUT_COLUMN_PREFIX,
  }: {
    dataColumns: DataColumn[];
    columns: Column[];
    inputColumnPrefix?: string;
  }) {
    const inputColumn = columns.find(({ code }) => code === 'input');

    return dataColumns.map((dataColumn) => {
      if (dataColumn.code.startsWith(inputColumnPrefix) && inputColumn?.header) {
        const inputIndex = dataColumn.code.slice(inputColumnPrefix.length);
        return `${inputColumn.header} ${inputIndex}`;
      }

      return columns.find((c) => c.code === dataColumn.code)?.header ?? dataColumn.header;
    });
  }

  public sortRecords({
    records,
    dataColumns,
    orderingColumns = [],
    data,
    inputColumnPrefix,
  }: {
    records: RecordRow[];
    dataColumns: DataColumn[];
    orderingColumns?: OrderingColumn[];
    inputColumnPrefix?: string;
  } & Pick<ConvertDataParams, 'data'>) {
    if (orderingColumns.length === 0) {
      return records;
    }

    const orderingColumnsWithIndexes = this.getOrderingColumnsWithIndexes({
      dataColumns,
      orderingColumns,
      inputColumnPrefix,
    });

    const columnLabelIndexes = this.getColumnLabelIndexes({ data, orderingColumns });

    return records.sort((a, b) => {
      for (const { code, direction, index } of orderingColumnsWithIndexes) {
        if (index === -1) {
          continue;
        }

        const aValue = String(a[index] ?? '');
        const bValue = String(b[index] ?? '');

        // Order inputs by the answer order (how they display on the metric answer page).
        if (this.isDefaultColumnLabelOrderingColumn({ code, direction })) {
          const aIndex = columnLabelIndexes.get(aValue) ?? 0;
          const bIndex = columnLabelIndexes.get(bValue) ?? 0;

          if (aIndex === bIndex) {
            continue;
          }

          return aIndex - bIndex;
        }

        const directionMultiplier = direction === OrderingDirection.Asc ? 1 : -1;

        if (isDateColumn(code)) {
          const aDate = revertFormattedToDate(aValue, DATE_COLUMN_FORMAT[code]);
          const bDate = revertFormattedToDate(bValue, DATE_COLUMN_FORMAT[code]);
          if (aDate.getTime() === bDate.getTime()) {
            continue;
          }
          return (aDate.getTime() - bDate.getTime()) * directionMultiplier;
        }

        const comparison = aValue.localeCompare(bValue);
        if (comparison === 0) {
          continue;
        }

        return directionMultiplier * comparison;
      }

      return 0;
    });
  }
  private getOrderingColumnsWithIndexes({
    dataColumns,
    orderingColumns,
    inputColumnPrefix = INPUT_COLUMN_PREFIX,
  }: {
    dataColumns: DataColumn[];
    orderingColumns: OrderingColumn[];
    inputColumnPrefix?: string;
  }) {
    return orderingColumns.reduce<OrderingColumnWithIndex[]>((orderingColumnsWithIndexes, orderingCol) => {
      if (orderingCol.code === 'input') {
        const inputColumns = dataColumns.reduce<OrderingColumnWithIndex[]>((inputColumns, col, index) => {
          if (col.code.startsWith(inputColumnPrefix)) {
            inputColumns.push({ ...orderingCol, index });
          }

          return inputColumns;
        }, []);

        orderingColumnsWithIndexes.push(...inputColumns);
        return orderingColumnsWithIndexes;
      }
      const index = dataColumns.findIndex((col) => {
        return col.code === orderingCol.code;
      });

      orderingColumnsWithIndexes.push({ ...orderingCol, index });
      return orderingColumnsWithIndexes;
    }, []);
  }

  /**
   * Returns a Map of column names to their order index.
   * This is used for sorting when the user chooses the default column label as the ordering column,
   * which means we need to sort by the order that the inputs appear on the metric answer page.
   */
  private getColumnLabelIndexes({
    data,
    orderingColumns,
  }: Pick<ConvertDataParams, 'data' | 'orderingColumns'>): Map<string, number> {
    const mightSortByDefaultColumnLabel = (orderingColumns || []).some(this.isDefaultColumnLabelOrderingColumn);
    return mightSortByDefaultColumnLabel
      ? new Map(
          data.reduce((iterable, utr) => {
            if (utr.valueType !== UtrValueType.Table || !utr.valueValidation?.table) {
              return iterable;
            }

            const columns = utr.valueValidation.table.columns;
            iterable.push(...columns.map(({ name }, index) => [name, index] as [string, number]));

            return iterable;
          }, [] as [string, number][])
        )
      : new Map([]);
  }

  private isDefaultColumnLabelOrderingColumn = ({ code, direction }: OrderingColumn) => {
    return code === 'columnLabel' && direction === OrderingDirection.Default;
  };
}
