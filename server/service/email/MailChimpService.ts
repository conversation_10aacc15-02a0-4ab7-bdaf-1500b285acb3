import { ProductCodes } from "../../models/customer";
import { OrganizationPartnerTypes } from "../../models/organization";
import config from "../../config";
import { AppConfig } from "../app/AppConfig";
import { SGXESGenome } from "../app/company-tracker/SGXESGenome";
import mailchimp, { ErrorResponse } from '@mailchimp/mailchimp_marketing';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import { UserPlain } from '../../models/user';
import { createHash } from 'crypto';
import { capitalize } from "../../util/string";

export enum MailChimpTags {
  Registered = 'Registered onto Platform',
  CtStandard = 'CT Standard',
  CtStarter = 'CT Starter',
  EsGenome = 'ESGenome',
  CtPro = 'CT Pro',
  CtEnterprise = 'CT Enterprise',
  Assurance = 'Assurance Portfolio',
  Portfolio = 'Portfolio Tracker',
  London = 'Environment - London',
  Singapore = 'Environment - Singapore',
  Development = 'Environment - Development',
  Archived = 'Archived in Platform'
}

// to remove a tag not in use anymore remove from MailChimpTags and add it here
// we need to be specific so we don't remove custom tags marketing added on members
export enum DeprecatedMailChimpTags {
  CtLight = 'CT Light',
}

export interface BatchListMemberOptions {
  skipMergeValidation: boolean;
}

export enum TagStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive'
}

export interface MemberTagPayload {
  name: string,
  status: TagStatus
}

export interface MemberData {
  email_address: string,
  id: string,
  tags: { name: string }[]
}

export enum BatchListMemberErrorCodes {
  contactExists = "ERROR_CONTACT_EXISTS",
  generic = "ERROR_GENERIC"
}

export interface RequestBaseTimeoutError extends Error {
  code: string
}

export const timeOutErrors = ['ECONNABORTED', 'ETIMEDOUT']

const maxBatchSize = 500

interface MailChimpServerProps {
  batchSize: number;
}

export class MailchimpService {
  private readonly batchSize
  private logger: LoggerInterface

  constructor({ batchSize = 500 }: MailChimpServerProps) {
    const key = config.email.mailChimpKey
    const server = config.email.mailChimpServer
    if (batchSize > maxBatchSize) {
      throw new Error(`Batchsize should be max ${maxBatchSize}`)
    }
    this.batchSize = batchSize

    if (!key) {
      throw new Error('No mailchimp key set')
    }
    if (key.indexOf('-') === -1) {
      throw new Error('Invalid key')
    }
    mailchimp.setConfig({
      apiKey: key,
      server: server
    });
    this.logger = wwgLogger;
  }

  private async batchListMembers(
    listId: string,
    body: { members: mailchimp.lists.BatchListMembersBodyMembersObject[] },
    opts?: BatchListMemberOptions,
  ) {
    return mailchimp.lists.batchListMembers(
      listId,
      body,
      opts
    );
  }

  public isTimeoutException(err: RequestBaseTimeoutError | Error): err is RequestBaseTimeoutError {
    return err && 'code' in err && timeOutErrors.includes(err.code)
  }

  public isErrorResponse(response: {} | ErrorResponse): response is ErrorResponse {
    return response && 'detail' in response
  }

  async getAudienceMembers(audienceId: string, count: number = 10, offset: number = 0) {
    return await mailchimp.lists.getListMembersInfo(audienceId, { count, offset });
  }

  async updateMemberTags(audienceId: string, hash: string, tags: MemberTagPayload[]) {
    return await mailchimp.lists.updateListMemberTags(audienceId, hash, { tags });
  }

  public async updateArchivedMailchimpTags(users: Pick<UserPlain, '_id' | 'email'>[]) {
    for (const user of users) {
      // According to mailchimp docs the member id is the md5 hash of the lowercase email
      // https://mailchimp.com/developer/marketing/api/list-member-tags/add-or-remove-member-tags/
      const memberId = createHash('md5').update(user.email.toLowerCase()).digest('hex').toString();

      const resp = await this.updateMemberTags(config.email.mailChimpAudienceId, memberId, [
        { name: MailChimpTags.Archived, status: TagStatus.ACTIVE },
      ]);

      if (this.isErrorResponse(resp)) {
        this.logger.error(resp.detail);
      }
    }
    this.logger.info(`Updated archived tags for users ${users.length}`, {
      userIds: users.map((u) => u._id.toString()),
    });
    return users;
  }

  /**
   * https://mailchimp.com/developer/marketing/api/list-merges/
   */
  async batchAddToAudience(audienceId: string, membersBatch: mailchimp.lists.BatchListMembersBodyMembersObject[]) {
    // chunk by mailchimp maxBatchSize
    const memberChunks = Array.from({ length: Math.ceil(membersBatch.length / this.batchSize) }, (v, i) =>
      membersBatch.slice(i * this.batchSize, (i * this.batchSize) + this.batchSize)
    );

    const batches = memberChunks.map(members =>
      this.batchListMembers(
        audienceId,
        {
          members
        }
      ))
    return await Promise.all(batches)
  }

  /**
   * Custom tags should be retained so only remove if is in the MailChimpTags list
   */
  public canRemoveTag(tagName: string, userRefreshedTags: string[]) {
    const notApplicableAnymore = !userRefreshedTags.includes(tagName) && Object.values<string>(MailChimpTags).includes(tagName)
    const isDeprecated = Object.values<string>(DeprecatedMailChimpTags).includes(tagName)
    return notApplicableAnymore || isDeprecated
  }

  /**
   *  to retrieve the correct environment we rely on the env file
   *  google cloud-scheduler calls api.g17.eco or api.sg.g17.eco and there is
   *  no specific domain config to determine the environment
   */
  private getEnvTag(appEnv?: string) {
    switch (appEnv) {
      case 'singapore':
        return MailChimpTags.Singapore;
      case 'production':
        return MailChimpTags.London;
      case 'development':
        return MailChimpTags.Development;
      default: {
        // Mailchimp will automatically create a tag if it doesn't exist
        return `Environment - ${capitalize(config.appEnv)}`;
      }
    }
  }

  public async getMailChimpTags(appConfig: AppConfig, organizationType?: string, appEnv?: string) {
    const tags: string[] = []

    const isTrackerPro = appConfig?.productCode === ProductCodes.CompanyTrackerPro
    const isTrackerEnterprise = appConfig?.productCode === ProductCodes.CompanyTrackerEnterprise
    const isEsgenome = appConfig?.code === SGXESGenome.code
    const isTrackerStandard = !isEsgenome && appConfig?.productCode === ProductCodes.CompanyTracker
    const isPortfolioTracker = appConfig?.productCode === ProductCodes.PortfolioTracker

    tags.push(MailChimpTags.Registered)
    tags.push(this.getEnvTag(appEnv))
    if (organizationType === OrganizationPartnerTypes.Assurer) {
      tags.push(MailChimpTags.Assurance)
    }

    if (appConfig.productCode === ProductCodes.CompanyTrackerStarter) {
      tags.push(MailChimpTags.CtStarter)
    }

    if (isTrackerStandard) {
      tags.push(MailChimpTags.CtStandard)
    }
    if (isEsgenome) {
      tags.push(MailChimpTags.EsGenome)
    }
    if (isTrackerPro) {
      tags.push(MailChimpTags.CtPro)
    }
    if (isTrackerEnterprise) {
      tags.push(MailChimpTags.CtEnterprise)
    }
    if (isPortfolioTracker) {
      tags.push(MailChimpTags.Portfolio)
    }
    return tags
  }

}
