/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import EmailTransaction, { EmailDeliveryType } from '../model/EmailTransaction';
import { FeedbackMessage } from './FeedbackHandler';
import { Logger } from 'winston';
import { wwgLogger } from '../../wwgLogger';
import {
  createOnboardingRepository,
  OnboardingRepository
} from '../../../repository/OnboardingRepository';
import {
  getOnboardingManager,
  OnboardingManager
} from '../../onboarding/OnboardingManager';


export class OnboardingFeedbackHandler {

  constructor(
    private onboardingRepo: OnboardingRepository,
    private onboardingManager: OnboardingManager,
    private logger: Logger,
  ) {
  }

  public async process(sesMessage: FeedbackMessage, emailLog: EmailTransaction) {
    if (emailLog.service !== 'onboarding_email_initial' || emailLog.type !== EmailDeliveryType.Bounce) {
      return;
    }

    const onboardingId = emailLog.data ? emailLog.data.onboardingId : undefined;
    if (!onboardingId) {
      this.logger.error(`Missing onboarding data for email transaction ${emailLog.id}`);
      return;
    }

    try {
      const onboarding = await this.onboardingRepo.mustFindById(onboardingId);
      await this.onboardingManager.remove(onboarding);
    } catch (e) {
      this.logger.error(e);
    }
  }
}

export const createOnboardingFeedbackHandler = () => {
  return new OnboardingFeedbackHandler(
    createOnboardingRepository(),
    getOnboardingManager(),
    wwgLogger,
  );
};
