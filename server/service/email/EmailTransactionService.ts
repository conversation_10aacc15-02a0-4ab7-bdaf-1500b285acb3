/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import EmailTransaction, { EmailDeliveryType } from '../email/model/EmailTransaction';
import Sequelize, { Attributes, FindOptions } from 'sequelize';

export const getFindAllByIdsFilters = (filters: {
  transactionIds: string[];
  service?: string;
  types?: EmailDeliveryType[];
}): FindOptions<Attributes<EmailTransaction>> => {
  const { transactionIds, service, types } = filters;
  return {
    where: {
      ...(service ? { service } : {}),
      externalId: {
        [Sequelize.Op.in]: transactionIds,
      },
      type: {
        [Sequelize.Op.in]: types,
      },
    },
    order: [['createdAt', 'DESC']],
  };
};

export class EmailTransactionService {
  public async findAll(filters: { userId: string; service?: string; types?: EmailDeliveryType[] }) {
    const { userId, service, types = Object.values(EmailDeliveryType) } = filters;
    const emails = await EmailTransaction.findAll({
      where: {
        userId: userId,
        ...(service ? { service } : {}),
        type: {
          [Sequelize.Op.in]: types,
        },
      },
      order: [['createdAt', 'DESC']],
    });
    return emails;
  }

  public async findAllByIds(filters: { transactionIds: string[]; service?: string; types?: EmailDeliveryType[] }) {
    const { transactionIds, service, types = Object.values(EmailDeliveryType) } = filters;
    return EmailTransaction.findAll(getFindAllByIdsFilters({ transactionIds, service, types }));
  }
}

let instance: EmailTransactionService;
export const getEmailTransactionService = () => {
  if (!instance) {
    instance = new EmailTransactionService();
  }
  return instance;
}
