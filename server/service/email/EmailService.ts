/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { SesEmailClient } from './SES/SesEmailClient';
import { SendEmailRequest, SES } from '@aws-sdk/client-ses';
import config from '../../config';

export interface MailerResponseInterface {
  isSuccess(): boolean;

  getRawResponse(): any;

  getId(): string;
}

export interface MailerInterface {
  send(message: MessageInterface): Promise<MailerResponseInterface>;

  getNewMessageInstance(): MessageInterface;
}

export type MessageObject = SendEmailRequest;

export interface MessageInterface {
  /**
   * Add a recipient
   */
  addTo(email: string, name: string, type?: string): MessageInterface;

  setReplyTo(email: string): MessageInterface;

  setBccAddress(bccAddress: string): MessageInterface;

  setFromEmail(fromEmail: string): MessageInterface;

  setHtml(html: string): MessageInterface;

  setText(text: string): MessageInterface;

  setSubject(subject: string): MessageInterface;

  setFromName(fromName: string): MessageInterface;

  getBccAddresses(): string[] | undefined;

  getFromEmail(): string;

  getHtml(): string;

  getSubject(): string;

  getText(): string;

  getTo(): string;

  getFromName(): string;

  toJSON(): MessageObject;
}

export enum EmailClientKind {
  SesClient,
}

export function createMailService(type: EmailClientKind = EmailClientKind.SesClient): MailerInterface {
  switch (type) {
    case EmailClientKind.SesClient:
      return createSesClient();
    default:
      throw new Error(`Unsupported email service type ${type}`);
  }
}

const createSesClient = () => {
  const { defaultSender, defaultSenderName } = config.email;
  const ses = new SES({
    credentials: {
      accessKeyId: process.env.SES_ACCESS_KEY as string,
      secretAccessKey: process.env.SES_SECRET_ACCESS_KEY as string,
    },
    apiVersion: '2010-12-01',
    region: 'eu-west-1',
  });
  return new SesEmailClient(ses, defaultSender, defaultSenderName);
};


let instance: MailerInterface;
export const getEmailService = () => {
  if (!instance) {
    instance = createMailService();
  }
  return instance;
}
