/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { getPgConnection } from '../../db/Db';
import { Model, DataTypes } from 'sequelize';

const db = getPgConnection();

export interface EmailTransactionAttributes {
  id?: string;
  externalId: string;
  userId: string;
  service: string;
  type?: string;
  subType?: string;
  data?: any;
  createdAt?: Date;
  updatedAt?: Date;
}

export enum EmailDeliveryType {
  Pending = 'pending',
  Delivery = 'delivery',
  Bounce = 'bounce',
  Complain = 'complain',
}

export enum EmailDeliverySubType {
  Permanent = 'permanent', // hard-bounce, will never be delivered successfully
  Transient = 'transient', // soft-bounce, might be delivered later
}

// https://sequelize.org/docs/v6/core-concepts/model-basics/#caveat-with-public-class-fields
class EmailTransaction extends Model {
  declare id?: string;
  declare externalId?: string;
  declare userId?: string;
  declare service?: string;
  declare type?: string;
  declare subType?: string;
  declare data?: any;
  declare createdAt?: Date;
  declare updatedAt?: Date;
}

export const tableName = 'EmailTransaction';
EmailTransaction.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4
    },
    externalId: { type: DataTypes.STRING },
    userId: { type: DataTypes.STRING },
    service: { type: DataTypes.STRING },
    type: {
      type: DataTypes.ENUM(
        EmailDeliveryType.Pending,
        EmailDeliveryType.Delivery,
        EmailDeliveryType.Bounce,
        EmailDeliveryType.Complain
      ),
      defaultValue: EmailDeliveryType.Pending
    },
    subType: { type: DataTypes.STRING },
    data: { type: DataTypes.JSON },
  },
  {
    sequelize: db,
    tableName: tableName,
    indexes: [
      {
        name: 'external_id_idx',
        fields: ['externalId']
      },
    ],
  },
);

if (process.env.NODE_ENV !== 'test' && !!process.env.USER_EVENTS_SYNC) {
  EmailTransaction.sync();
}
export default EmailTransaction;
