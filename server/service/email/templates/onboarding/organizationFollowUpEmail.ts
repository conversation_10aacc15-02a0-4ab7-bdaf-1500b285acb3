/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import baseTemplate, { renderContentBottom } from '../core/baseTemplate';
import { OnboardingInitialData } from './organizationInitial';
import { ObNotificationCode } from '../../../../models/onboarding';
import { EmailRenderer } from '../htmlGenerator';


export interface OnboardingEmailData extends OnboardingInitialData {
  action: ObNotificationCode;
}

type Renderer = EmailRenderer<OnboardingEmailData>;

interface RendererData {
  action: ObNotificationCode
}


const finalReminderRenderer = () => {
  return {
    subject: () => 'G17Eco - Member Registration Final reminder',
    topContent: ({ organizationName }: OnboardingEmailData) => {
      return `
      <p>You were recently invited by <strong>${organizationName}</strong> onto the G17Eco Platform to participate
      in their Assurance Tracker, as a member.</p>

      <p>This is a <strong>final</strong> reminder to start your registration process.
      Please click on the button below. We need to on board you as a member in the next 48 hours.</p>
      ${renderContentBottom()}
    `
    }
  }
}

const getFirstRenderer = function () {
  return {
    subject: () => 'G17Eco - Member Registration Reminder',
    topContent: ({ organizationName }: OnboardingEmailData) => {
      return `
      <p>You were recently delegated by <strong>${organizationName}</strong> as a member to their
      Assurance Tracker on the G17Eco platform.</p>

      <p>This is a kind reminder to please start your registration process.
      Please click on the button below. We need to on board you as a member in the next 48 hours.</p>
      ${renderContentBottom()}
    `
    }
  }
};

const getRenderer = (rendererData: RendererData): Renderer => {
  switch (rendererData.action) {
    case ObNotificationCode.ManualReminder:
    case ObNotificationCode.FirstReminder:
      return getFirstRenderer();
    case ObNotificationCode.FinalReminder:
      return finalReminderRenderer();
    default: {
      const action: never = rendererData.action;
      throw new Error(`Not supported renderer action ${action}`)
    }
  }
};

/**
 * Template for onboarding follow up actions
 */
export const organizationFollowUpEmail = (data: OnboardingEmailData) => {

  const renderer = getRenderer({
    action: data.action
  });

  const topContent = renderer.topContent(data);

  return {
    subject: renderer.subject(data),
    body: baseTemplate({
      user: {
        firstName: data.firstName,
      },
      link: {
        url: data.onboardingUrl,
        type: 'button',
        text: 'Join now',
      },
      domain: data.domain,
      domainConfig: data.domainConfig,
      appConfig: undefined,
      topContent,
    })
  }
};
