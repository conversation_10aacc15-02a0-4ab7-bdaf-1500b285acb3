/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import baseTemplate, { getBrandingTemplate, renderContentBottom } from '../core/baseTemplate';
import { EmailTemplate, OnboardingInitialData } from './initial';
import { ObNotificationCode } from '../../../../models/onboarding';
import { BrandingTemplate } from '../../../organization/domainConfig';
import { EmailRenderer } from '../htmlGenerator';


export interface OnboardingEmailData extends OnboardingInitialData {
  action: ObNotificationCode;
}


type Renderer = EmailRenderer<OnboardingEmailData>;

interface RendererData {
  template: EmailTemplate;
  branding: BrandingTemplate;
  action: ObNotificationCode
}


const finalReminderRenderer = (rendererData: RendererData) => {

  if (rendererData.branding === BrandingTemplate.SGX) {
    return {
      subject: () => 'SGX ESGenome - Registration Final reminder',
      topContent: ({ initiativeName }: OnboardingEmailData) => {
        return `
      <p>You were recently invited by <strong>${initiativeName}</strong> to participate
      in their sustainability reporting, on SGX ESGenome.</p>

      <p>Please click on the button below to register, within the next 48 hours.
      The registration link will expire after this time.</p>
      ${renderContentBottom()}
    `
      }
    }
  }

  return {
    subject: () => 'G17Eco - Member Registration Final reminder',
    topContent: ({ initiativeName }: OnboardingEmailData) => {
      return `
      <p>You were recently invited by <strong>${initiativeName}</strong> onto the G17Eco Platform to participate
      in their sustainability reporting, as a contributor, verifier or assurer.</p>

      <p>This is a <strong>final</strong> reminder to start your registration process.
      Please click on the button below. We need to on board you as a member in the next 48 hours.</p>
      ${renderContentBottom()}
    `
    }
  }
}

const getFirstRenderer = function (rendererData: RendererData) {

  if (rendererData.branding === BrandingTemplate.SGX) {
    return {
      subject: () => 'SGX ESGenome - Registration reminder',
      topContent: ({ initiativeName }: OnboardingEmailData) => {
        return `
      <p>You were recently added as a data contributor by <strong>${initiativeName}</strong>  to their
      sustainability reporting, on SGX ESGenome.</p>

      <p>Please click on the button below to register, within the next 48 hours.
      The registration link will expire after this time.</p>
      ${renderContentBottom()}
    `
      }
    }
  }

  return {
    subject: () => 'G17Eco - Member Registration Reminder',
    topContent: ({ initiativeName }: OnboardingEmailData) => {
      return `
      <p>You were recently delegated by <strong>${initiativeName}</strong> as a data contributor to their
      sustainability reporting on the G17Eco platform.</p>

      <p>This is a kind reminder to please start your registration process.
      Please click on the button below. We need to on board you as a member in the next 48 hours.</p>
      ${renderContentBottom()}
    `
    }
  }
};

const getRenderer = (rendererData: RendererData): Renderer => {
  switch (rendererData.action) {
    case ObNotificationCode.ManualReminder:
    case ObNotificationCode.FirstReminder:
      return getFirstRenderer(rendererData);
    case ObNotificationCode.FinalReminder:
      return finalReminderRenderer(rendererData);
    default: {
      const action: never = rendererData.action;
      throw new Error(`Not supported renderer action ${action}`)
    }
  }
};

/**
 * Template for onboarding follow up actions
 */
export const followUpEmail = (data: OnboardingEmailData) => {

  const renderer = getRenderer({
    template: data.emailTemplate ?? EmailTemplate.SurveyContributorVerifierAssurer,
    branding: getBrandingTemplate(data),
    action: data.action
  });

  const topContent = renderer.topContent(data);

  return {
    subject: renderer.subject(data),
    body: baseTemplate({
      user: {
        firstName: data.firstName,
      },
      link: {
        url: data.onboardingUrl,
        type: 'button',
        text: 'Join now',
      },
      domain: data.domain,
      domainConfig: data.domainConfig,
      appConfig: data.appConfig,
      topContent,
    })
  }
};
