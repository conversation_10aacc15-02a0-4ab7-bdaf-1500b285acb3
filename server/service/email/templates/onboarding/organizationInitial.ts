/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import baseTemplate, { renderContentBottom } from '../core/baseTemplate';
import { DomainConfig } from '../../../organization/domainConfig';
import { EmailRenderer } from '../htmlGenerator';
import config from '../../../../config';

export enum OrganizationEmailTemplate {
  User = 'user',
  Manager = 'manager',
  Guest = 'guest'
}

export interface OnboardingInitialData {
  firstName?: string;
  onboardingUrl: string;
  unsubscribeUrl: string;
  organizationName: string;
  emailTemplate?: OrganizationEmailTemplate;
  domain: string | undefined;
  domainConfig: DomainConfig | undefined;
}

export type TemplatesInterface = {
  [key in OrganizationEmailTemplate]: EmailRenderer<OnboardingInitialData>
};

const getTopContent = ({ organizationName, role }: { organizationName: string, role: string }) => {
  return `<p><strong>${organizationName}</strong> has invited you to join as an Assurance Tracker ${role} on the G17Eco Platform.</p>
  <p>For you to access the platform and begin assuring metrics, we need to onboard you as a member. Please click on the button below to register</p>`;
}

const getTemplates = (): TemplatesInterface => {
  return {
    [OrganizationEmailTemplate.Manager]: {
      subject: () => 'G17Eco Assurance Tracker Registration',
      topContent: ({ organizationName }) => getTopContent({ organizationName, role: 'administrator' }),
    },
    [OrganizationEmailTemplate.User]: {
      subject: () => 'G17Eco Assurance Tracker Registration',
      topContent: ({ organizationName }) => getTopContent({ organizationName, role: 'user' }),
    },
    [OrganizationEmailTemplate.Guest]: {
      subject: () => 'G17Eco Assurance Tracker Registration',
      topContent: ({ organizationName }) => getTopContent({ organizationName, role: 'restricted user' }),
    },
  };
}

export default (data: OnboardingInitialData) => {

  const template = data.emailTemplate ?? OrganizationEmailTemplate.Guest
  const renderer = getTemplates()[template];

  const topContent = renderer.topContent(data);

  return {
    subject: renderer.subject(data),
    body: baseTemplate({
      domain: data.domain,
      domainConfig: data.domainConfig,
      appConfig: undefined,
      user: {
        firstName: data.firstName,
      },
      link: {
        url: data.onboardingUrl,
        type: 'button',
        text: 'Join now',
      },
      logoUrl: config.assets.defaultLogo,
      topContent: `${topContent}${renderContentBottom()}`,
    })
  }
};
