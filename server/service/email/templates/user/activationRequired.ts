/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { getBrandingTemplate, renderContentBottom } from '../core/baseTemplate';
import { ActivationRequiredEmail } from '../../../user/UserEmailService';
import { BrandingTemplate } from '../../../organization/domainConfig';

/** Template that is send on self CTL onboarding to activate account */
export default (data: Pick<ActivationRequiredEmail, 'domainConfig' | 'domain' | 'appConfig'>) => {

  const branding = getBrandingTemplate(data);

  if (branding === BrandingTemplate.SGX) {
    return {
      subject: 'SGX ESGenome Disclosure Portal - activation required',
      topContent: (`<div class="text-secondary">
      <p>Welcome to SGX ESGenome.</p>
      <p>Please click on the button below to activate your account, and access the platform.</p>
      ${renderContentBottom()}
  </div>`)
    }
  }

  return {
    subject: 'G17Eco - activation required',
    topContent: (`<div class="text-secondary">
      <p>Thank you for registering. Please activate your account by clicking the button below.</p>
      ${renderContentBottom()}
  </div>`)
  }

};
