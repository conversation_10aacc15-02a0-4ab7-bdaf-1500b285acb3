/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { MessageInterface, MessageObject } from "../EmailService"

interface To {
  email: string,
  name: string,
}

class SesMessage implements MessageInterface {

  bbcAddresses: string[] = [];
  toAddresses: To[] = [];
  private fromName: string = '';
  private source: string = '';
  private subject: string = '';
  private fromEmail: string = '';
  private replyToAddresses?: string[];
  private html = { Data: '' };
  private text = { Data: '' };


  addTo(email: string, name: string) {
    const strippedName = String(name).replace(/undefined|,/g, '').trim();

    this.toAddresses.push({ email, name: strippedName });
    return this;
  }

  getBccAddresses(): string[] | undefined {
    return this.bbcAddresses;
  }

  getFromEmail(): string {
    return this.fromEmail;
  }

  getFromName(): string {
    return this.fromName;
  }

  getHtml(): string {
    return this.html ? this.html.Data : '';
  }

  getSubject(): string {
    return this.subject;
  }

  getText(): string {
    return this.text ? this.text.Data : '';
  }

  getTo(): string {
    return "";
  }

  setBccAddress(bccAddress: string): MessageInterface {
    this.bbcAddresses.push(bccAddress);
    return this;
  }

  setFromEmail(fromEmail: string): MessageInterface {
    this.fromEmail = fromEmail;
    this.source = fromEmail;
    return this;
  }

  setFromName(fromName: string): MessageInterface {
    this.fromName = fromName;
    return this;
  }

  setHtml(html: string): MessageInterface {
    this.html = {Data: html } ;
    return this;
  }

  setReplyTo(email: string): MessageInterface {
    if (!this.replyToAddresses) {
      this.replyToAddresses = [];
    }
    this.replyToAddresses.push(email);
    return this;
  }

  setSubject(subject: string): MessageInterface {
    this.subject = subject;
    return this;
  }

  setText(text: string): MessageInterface {
    this.text = { Data: text };
    return this;
  }

  private getFlatToArray(): string[] {
    const to = [];
    for (const address of this.toAddresses) {
      to.push(`${address.name} <${address.email}>`)
    }

    return to;
  }

  toJSON(): MessageObject {

    return {
      Source: this.getSource(),
      Destination: {
        ToAddresses: this.getFlatToArray(),
      },
      Message: {
        Subject: { Data: this.getSubject() },
        Body: {
          Html: this.html,
          Text: this.text,
        },
      },
      ReplyToAddresses: this.replyToAddresses,
    };
  }

  private getSource() {
    if (this.fromName) {
      return `${this.fromName} <${this.source}>`;
    }
    return this.source;
  }
}


export default SesMessage
