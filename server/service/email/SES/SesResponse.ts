/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { MailerResponseInterface } from "../EmailService"
import { SendEmailResponse } from '@aws-sdk/client-ses';

export default class SesResponse implements MailerResponseInterface {

  private readonly response: any;
  private readonly id: string = ''

  public constructor(response: SendEmailResponse) {
    this.response = response;
    this.id = response.MessageId || '';
  }

  public isSuccess() {
    return '' !== this.id;
  }

  public getRawResponse() {
    return this.response;
  }

  public getId() {
    return this.id;
  }
}
