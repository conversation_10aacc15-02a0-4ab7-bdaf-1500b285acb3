/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { SES } from '@aws-sdk/client-ses';
import { MailerInterface, MailerResponseInterface, MessageInterface } from "../EmailService"
import SesResponse from "./SesResponse"
import SesMessage from "./SesMessage"

export class SesEmailClient implements MailerInterface {

  private client: SES;

  /**
   * Default sender email (source)
   */
  private readonly defaultSender: string;

  private readonly defaultSenderName: string;

  public constructor(client: SES, defaultSender: string, defaultSenderName: string) {
    this.client = client;
    this.defaultSender = defaultSender;
    this.defaultSenderName = defaultSenderName;
  }

  public async send(message: MessageInterface, $async = false): Promise<MailerResponseInterface> {

    if (!message.getFromEmail()) {
      message.setFromEmail(this.defaultSender);
    }

    if (!message.getFromName()) {
      message.setFromName(this.defaultSenderName);
    }

    const data = await this.client.sendEmail(message.toJSON())
    return new SesResponse(data);
  }

  public getNewMessageInstance(): MessageInterface {
    return new SesMessage();
  }
}
