/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { DeleteMessageRequest, ReceiveMessageRequest, SQS, Message } from "@aws-sdk/client-sqs";
import EmailTransaction from './model/EmailTransaction';
import { FeedbackHandler } from './handler/FeedbackHandler';
import { QueueWorker } from '../queue/QueueWorker';
import { MissingJobDataException } from '../queue/MissingJobDataException';
import { wwgLogger } from "../wwgLogger";


class AwsSesWorker implements QueueWorker {

  constructor(
    private emailTransaction: typeof EmailTransaction,
    private client: SQS,
    private handlers: FeedbackHandler[]
  ) {
  }

  public async handle(message: Message) {

    const messageBody = JSON.parse(message.Body as string);
    if (!messageBody.Message) {
      throw new MissingJobDataException('aws-ses', 'Message');
    }

    const sesMessage = JSON.parse(messageBody.Message);
    const emailMessageId = String(sesMessage.mail.messageId);
    const emailLog = await this.emailTransaction.findOne(
      {where: {externalId: emailMessageId}}
    );

    if (!emailLog) {
      wwgLogger.warn(`AwsSesWorker: Unable to find message. This is normal if this is not the intended environment.`, { emailMessageId });
      return false;
    }

    await this.updateMailStatus(emailLog, sesMessage);
    this.handlers.forEach(h => h.process(sesMessage, emailLog));
    return true;
  }

  public getQueueUrl(queueName: string) {
    return this.client
      .getQueueUrl({QueueName: queueName})
      .then((result: any) => result.QueueUrl);
  }

  public receiveMessages(paramsReceiveMessage: ReceiveMessageRequest) {
    return this.client.receiveMessage(paramsReceiveMessage);
  }

  private updateMailStatus(emailTransaction: EmailTransaction, msg: any) {
    const type = msg.notificationType.toLowerCase();
    emailTransaction.type = type;
    if (type === 'bounce') {
      emailTransaction.subType = msg.bounce.bounceType.toLowerCase();
    }

    return emailTransaction.save();
  }

  public deleteMessage(params: DeleteMessageRequest) {
    return this.client.deleteMessage(params);
  }
}

export default AwsSesWorker;
