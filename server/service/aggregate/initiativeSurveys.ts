/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import { ObjectId } from 'bson';

interface SurveyResult<T = ObjectId> {
  _id: T;
  initiativeId: T
  effectiveDate: string;
}

interface InitiativeRow<T = ObjectId> {
  _id: T,
  parentId?: T
}

export interface SurveyRecursiveResult<T = ObjectId> {
  _id: T;
  initiatives: InitiativeRow<T>[],
  surveys: SurveyResult<T>[],
}

interface LatestSurveyResult<T = ObjectId> {
  _id: T;
  surveys: SurveyResult<T>[],
}

export enum AggregationStrategy {
  Latest = 'latest',
  Combined = 'combine',
}

function findSurveys(row: SurveyRecursiveResult, parentId: string, strategy: AggregationStrategy): SurveyResult[] {
  const children = row.initiatives.filter(initiative => parentId === String(initiative.parentId));
  return children.map(c => getInitiativeSurveyIds(row, c, strategy)).flat()
}

function matchStrategy(strategy: AggregationStrategy, surveys: SurveyResult[], survey: SurveyResult): SurveyResult[] {
  switch (strategy) {
    case AggregationStrategy.Latest:
      if (!surveys.some(s => s.effectiveDate > survey.effectiveDate)) {
        return [survey];
      }
      return surveys;
    case AggregationStrategy.Combined:
    default:
      return [...surveys, survey];
  }
}

const getInitiativeSurveyIds = (row: SurveyRecursiveResult, initiative: InitiativeRow, strategy: AggregationStrategy): SurveyResult[] => {
  const id = String(initiative._id);
  const surveys = row.surveys.reduce((a, s) => {
    if (id === String(s.initiativeId) && matchStrategy(strategy, a, s)) {
      return matchStrategy(strategy, a, s)
    }
    return a;
  }, <SurveyResult[]>[]);
  return surveys.length > 0 ? surveys : findSurveys(row, id, strategy);
};

export const getAggregateSurveyIds = (data: SurveyRecursiveResult[], strategy: AggregationStrategy): LatestSurveyResult[] => {
  return data.map(row => {
    const id = String(row._id);
    const initiative = row.initiatives.find(i => String(i._id) === id)

    return {
      _id: row._id,
      surveys: initiative ? getInitiativeSurveyIds(row, initiative, strategy) : [],
    };
  });
}
