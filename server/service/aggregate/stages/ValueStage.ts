/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */


type StageProcessingType = 'mathjs' | 'jsonata';

export interface ValueStage {
  input: {
    context: 'all' | 'utrv' | 'disaggregations';
    variable: string;
  },
  type: StageProcessingType;
  formula: string;
  output: string;
}

export interface ResultStage {
  input: { context: 'output' },
  type: StageProcessingType,
  formula: string;
  // Don't need to save it
  output: string;
  decimals?: number;
}

export type ValueStages = [...ValueStage[], ResultStage];
