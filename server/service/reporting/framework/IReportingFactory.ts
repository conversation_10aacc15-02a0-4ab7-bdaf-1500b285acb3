/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { ILexicalStateGenerator } from '../ILexicalStateGenerator';
import { IReportGenerator } from './IReportGenerator';
import { IReportDefinition } from './IReportDefinition';
import { IReportMapping } from './IReportMapping';

export interface IReportingFactory {
  getLexicalStateGenerator(): ILexicalStateGenerator;
  getReportGenerator(): IReportGenerator;
  getReportDefinition(): IReportDefinition;
  getReportMapping(): IReportMapping;
}
