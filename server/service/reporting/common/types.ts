export interface Languages {
  en: string;
}

export interface Labels {
  std: Languages;
  doc?: Languages;
  [k: `ns${number}`]: Languages | undefined;
}

/**
 * @example
 * {
 *   "esrs:DescriptionOfMaterialImpactsResultingFromMaterialityAssessmentExplanatory": {
 *     "labels": {
 *       "std": {
 *         "en": "Description of material impacts resulting from materiality assessment [text block]"
 *       }
 *     },
 *     "r": [
 *       [
 *         ["Name", "ESRS"],
 *         ["Number", "ESRS 2"],
 *         ["Paragraph", "48"],
 *         ["Subparagraph", "a"],
 *         ["Section", "SBM-3"]
 *       ],
 *       [
 *         ["Name", "ESRS"],
 *         ["Number", "E4"],
 *         ["Paragraph", "16"],
 *         ["Section", "SBM-3"]
 *       ]
 *     ],
 *     "t": true
 *   }
 * }
 */
export interface Concept {
  labels: Labels;
  /** d: "t" not sure what this means for now  */
  d?: 't' | 'e' | number;
  r?: ReferenceRow[];
  t?: boolean;
  en?: boolean;
}

type ReferenceItem = [label: string, value: string];
/**
 * @example
 *  [
 *    ["Name", "ESRS"],
 *    ["Number", "ESRS 2"],
 *    ["Paragraph", "48"],
 *    ["Subparagraph", "a"],
 *    ["Section", "SBM-3"]
 *  ]
 */
export type ReferenceRow = ReferenceItem[];

export interface DefinitionMapItem {
  label: string;
  technicalName: string;
  references?: ReferenceRow[];
}
