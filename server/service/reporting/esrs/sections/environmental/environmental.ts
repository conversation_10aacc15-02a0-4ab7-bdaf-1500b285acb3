/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { LexicalNode } from 'lexical/LexicalNode';
import { $createHeadingNode } from '@lexical/rich-text';
import { $createParagraphNode, $createTextNode } from 'lexical';
import { SectionData } from '../../../xhtml/csrdTypes';

export function buildEnvironmentalInformation(_sectionData: SectionData): LexicalNode[] {
  const sectionHeading = $createHeadingNode('h2');
  sectionHeading.append($createTextNode('🌿 2. Environmental Information'));

  const overviewParagraph = $createParagraphNode();
  overviewParagraph.append(
    $createTextNode(
      'This section outlines our environmental policies, objectives, and performance, focusing on areas such as climate change mitigation, resource efficiency, and pollution prevention.'
    )
  );

  const subHeading1 = $createHeadingNode('h3');
  subHeading1.append($createTextNode('2.1 Climate Change Mitigation'));

  const paragraph1 = $createParagraphNode();
  paragraph1.append(
    $createTextNode(
      'We are committed to reducing greenhouse gas emissions through energy efficiency measures, renewable energy adoption, and sustainable transportation solutions.'
    )
  );

  const subHeading2 = $createHeadingNode('h3');
  subHeading2.append($createTextNode('2.2 Resource Efficiency'));

  const paragraph2 = $createParagraphNode();
  paragraph2.append(
    $createTextNode(
      'Our resource efficiency initiatives include waste reduction programs, water conservation efforts, and the implementation of circular economy principles in our operations.'
    )
  );

  return [sectionHeading, overviewParagraph, subHeading1, paragraph1, subHeading2, paragraph2];
}
