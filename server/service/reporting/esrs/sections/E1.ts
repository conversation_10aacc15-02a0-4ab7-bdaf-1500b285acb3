import { $createTextNode } from 'lexical';
import { $createHeadingNode } from '@lexical/rich-text';
import { LexicalNode } from 'lexical/LexicalNode';
import { SectionData } from '../../xhtml/csrdTypes';
import { getContextualIxbrlNodes, MAJOR_SECTION } from '../utils';

export function buildE1Section(sectionData: SectionData): LexicalNode[] {
  const heading = $createHeadingNode('h1');
  heading.append($createTextNode('🌿 ESRS E1 – Climate Change'));

  const contextualIxbrlNodes = getContextualIxbrlNodes({
    ...sectionData,
    majorSection: MAJOR_SECTION.E1.code,
  });

  return [heading, ...contextualIxbrlNodes];
}
