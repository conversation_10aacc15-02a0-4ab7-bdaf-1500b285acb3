/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import { createHeadlessEditor } from '@lexical/headless';
import { $generateHtmlFromNodes } from '@lexical/html';
import { ListItemNode, ListNode } from '@lexical/list';
import { HeadingNode } from '@lexical/rich-text';
import { $getRoot, ParagraphNode, SerializedEditorState, type CreateEditorArgs } from 'lexical';
import { JSDOM } from 'jsdom';
import { excludeSoftDeleted } from '../../../repository/aggregations';
import { universalTrackerLookup } from '../../../repository/utrvAggregations';
import { ReportDocumentPlain } from '../../../models/reportDocument';
import Survey, { SurveyModelPlain } from '../../../models/survey';
import { UserPlain } from '../../../models/user';
import UniversalTrackerValue from '../../../models/universalTrackerValue';
import UtrExternalMapping from '../../../models/utrExternalMapping';
import UserError from '../../../error/UserError';
import { LoggerInterface, wwgLogger } from '../../wwgLogger';
import { GeneratorParameters, ReportSection, SectionData } from '../xhtml/csrdTypes';
import { XbrlTracker } from '../XbrlTracker';
import { ILexicalStateGenerator } from '../ILexicalStateGenerator';
import { IReportMapping } from '../framework/IReportMapping';
import { CsrdReportMapping } from './CsrdReportMapping';
import { IReportGenerator } from '../framework/IReportGenerator';

import { getCsrdReportGenerator } from './CsrdReportGenerator';
import { buildIntro } from './intro';
import { buildE1Section } from './sections/E1';
import { buildE2Section } from './sections/E2';
import { buildE3Section } from './sections/E3';
import { buildE4Section } from './sections/E4';
import { buildE5Section } from './sections/E5';
import { buildS1Section } from './sections/S1';
import { buildS2Section } from './sections/S2';
import { buildS3Section } from './sections/S3';
import { buildS4Section } from './sections/S4';
import { buildG1Section } from './sections/G1';
import { buildGeneralInformation } from './sections/general';
import { buildEnvironmentalInformation } from './sections/environmental/environmental';
import { buildTableOfContents } from './sections/tableOfContents';
import { EsrsFact, UtrvData, XBRLMapping } from './types';
import { IXBRLNode } from '../lexical/nodes/IXBRLNode';

export const editorConfig: CreateEditorArgs = {
  onError: (error: Error) => {
    wwgLogger.error(error);
  },
  theme: {
    ltr: 'ltr',
    rtl: 'rtl',
    placeholder: 'editor-placeholder',
    paragraph: 'editor-paragraph',
    list: {
      nested: {
        listitem: 'editor-nested-listitem',
      },
      ol: 'editor-list-ol',
      ul: 'editor-list-ul',
      listitem: 'editor-listitem',
    },
    text: {
      bold: 'editor-text-bold',
      italic: 'editor-text-italic',
      overflowed: 'editor-text-overflowed',
      underline: 'editor-text-underline',
    },
    ixbrlNode: 'ixbrl-plugin-node',
  },
  namespace: 'ReportEditor',
  nodes: [ListNode, ListItemNode, HeadingNode, IXBRLNode, ParagraphNode],
};

interface GenerateLexicalStateParams
  extends Pick<GeneratorParameters, 'initiative' | 'survey' | 'mapping' | 'utrCodeToUtrvMap'> {
  user: Pick<UserPlain, '_id'>;
  preview?: boolean;
}

export class CsrdLexicalStateGenerator implements ILexicalStateGenerator {
  constructor(
    private logger: LoggerInterface,
    private reportGenerator: IReportGenerator,
    private reportMapping: IReportMapping
  ) {}

  public async generateTemplateLexicalState(params: GenerateLexicalStateParams) {
    const { initiative, survey, mapping, utrCodeToUtrvMap } = params;

    this.logger.info(`Generating Lexical state for survey: ${survey._id}`, {
      surveyId: survey._id,
      initiativeId: survey.initiativeId,
    });

    const tracker = new XbrlTracker();
    const sectionData: SectionData = {
      initiative,
      mapping,
      utrCodeToUtrvMap,
      tracker,
    };

    // Use Lexical headless editor to build the state
    const editor = createHeadlessEditor(editorConfig);
    editor.update(
      () => {
        $getRoot().append(
          ...buildTableOfContents(sectionData),
          ...buildGeneralInformation(sectionData),
          ...buildIntro(sectionData),
          ...buildEnvironmentalInformation(sectionData),
          ...buildE1Section(sectionData),
          ...buildE2Section(sectionData),
          ...buildE3Section(sectionData),
          ...buildE4Section(sectionData),
          ...buildE5Section(sectionData),
          ...buildS1Section(sectionData),
          ...buildS2Section(sectionData),
          ...buildS3Section(sectionData),
          ...buildS4Section(sectionData),
          ...buildG1Section(sectionData)
        );
      },
      { discrete: true }
    );

    return editor.getEditorState().toJSON();
  }

  private async getUtrvData(survey: SurveyModelPlain) {
    const aggregations = [
      {
        $match: {
          _id: { $in: survey.visibleUtrvs },
          ...excludeSoftDeleted(),
        },
      },
      universalTrackerLookup,
      {
        $project: {
          _id: 1,
          value: 1,
          valueData: 1,
          status: 1,
          effectiveDate: 1,
          universalTracker: {
            $arrayElemAt: ['$universalTracker', 0],
          },
        },
      },
    ];
    return UniversalTrackerValue.aggregate<UtrvData>(aggregations).exec();
  }

  private async getGeneratorParameters(initiativeId: ObjectId, reportType: string) {
    // Get latest available survey
    const survey = await Survey.findOne({ initiativeId, deletedDate: { $exists: false } })
      .populate('initiative')
      .sort({ effectiveDate: -1, created: -1 })
      .lean<SurveyModelPlain>()
      .exec();

    if (!survey) {
      throw new UserError('No survey found for the initiative associated with this report document.');
    }

    if (!survey.initiative) {
      throw new UserError('Unable to fetch details of selected survey. If this continues please contact our support.');
    }

    const externalMapping = await UtrExternalMapping.find({ type: reportType }).lean();
    const externalXbrlMapping = externalMapping.reduce((acc, item) => {
      if (!acc[item.mappingCode]) {
        acc[item.mappingCode] = {
          factName: item.mappingCode as EsrsFact,
          utrCode: item.utrs[0].utrCode,
          valueListCode: item.utrs[0].valueListCode,
        };
      }
      return acc;
    }, {} as XBRLMapping);

    // Merge default mapping with external mapping
    const mapping = this.reportMapping.getMapping(externalXbrlMapping);

    const utrvData = await this.getUtrvData(survey);
    const utrCodeToUtrvMap = new Map<string, UtrvData>(utrvData.map((utrv) => [utrv.universalTracker.code, utrv]));

    return { initiative: survey.initiative, survey, mapping, utrvData, utrCodeToUtrvMap };
  }

  async getTemplate({
    initiativeId,
    user,
    reportType,
  }: {
    initiativeId: ObjectId;
    user: UserPlain;
    reportType: string;
  }): Promise<SerializedEditorState> {
    const generatorParams = await this.getGeneratorParameters(initiativeId, reportType);
    const state = await this.generateTemplateLexicalState({ ...generatorParams, user });
    return state;
  }

  /**
   * Groups a list of DOM nodes into structured sections based on heading elements.
   * @param nodes The list of child nodes from the document body.
   * @returns An array of structured ReportSection objects.
   */
  private parseAndGroupNodes(nodes: NodeListOf<ChildNode>): ReportSection[] {
    const sections: ReportSection[] = [];
    let currentSection: ReportSection | null = null;

    nodes.forEach((node) => {
      const isHeading = node.nodeType === node.ELEMENT_NODE && /^H[1-6]$/.test(node.nodeName);

      if (isHeading) {
        if (currentSection) {
          sections.push(currentSection);
        }
        currentSection = {
          header: { title: node.textContent || '' },
          children: [],
        };
      } else {
        if (!currentSection) {
          currentSection = {
            header: { title: 'Introduction' },
            children: [],
          };
        }
        const rawHtml = (node as Element).outerHTML || node.textContent || '';
        // Normalize the HTML to ensure it's valid XHTML.
        const content = rawHtml.replace(/<br>/g, '<br />');

        if (content.trim()) {
          currentSection.children.push({ type: 'html', content });
        }
      }
    });

    if (currentSection) {
      sections.push(currentSection);
    }

    return sections;
  }

  private buildReportSections(editorState: SerializedEditorState): ReportSection[] {
    const dom = new JSDOM();
    global.window = dom.window as unknown as Window & typeof globalThis;
    global.document = dom.window.document;

    const editor = createHeadlessEditor(editorConfig);
    const parsedEditorState = editor.parseEditorState(editorState);
    editor.setEditorState(parsedEditorState);

    let sections: ReportSection[] = [];

    editor.update(() => {
      const html = $generateHtmlFromNodes(editor);
      const reportDom = new JSDOM(html);
      const body = reportDom.window.document.body;
      sections = this.parseAndGroupNodes(body.childNodes);
    });

    return sections;
  }

  public async downloadReport({
    reportDocument,
    editorState,
    reportType,
  }: {
    reportDocument: ReportDocumentPlain;
    editorState: SerializedEditorState;
    reportType: string;
  }) {
    const params = await this.getGeneratorParameters(reportDocument.initiativeId, reportType);
    const sections = this.buildReportSections(editorState);
    return this.reportGenerator.generate({ ...params, sections, preview: true });
  }
}

let instance: CsrdLexicalStateGenerator;
export const getCsrdLexicalStateGenerator = () => {
  if (!instance) {
    instance = new CsrdLexicalStateGenerator(
      wwgLogger,
      getCsrdReportGenerator(),
      new CsrdReportMapping()
    );
  }
  return instance;
};
