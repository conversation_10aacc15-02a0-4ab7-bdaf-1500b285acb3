/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { IReportingFactory } from '../framework/IReportingFactory';
import { ILexicalStateGenerator } from '../ILexicalStateGenerator';
import { IReportGenerator } from '../framework/IReportGenerator';
import { IReportDefinition } from '../framework/IReportDefinition';
import { IReportMapping } from '../framework/IReportMapping';
import { getCsrdLexicalStateGenerator } from './CsrdLexicalStateGenerator';
import { getCsrdReportGenerator } from './CsrdReportGenerator';
import { CsrdReportDefinition } from './CsrdReportDefinition';
import { CsrdReportMapping } from './CsrdReportMapping';

export class CsrdReportingFactory implements IReportingFactory {
  getLexicalStateGenerator(): ILexicalStateGenerator {
    return getCsrdLexicalStateGenerator();
  }

  getReportGenerator(): IReportGenerator {
    return getCsrdReportGenerator();
  }

  getReportDefinition(): IReportDefinition {
    return new CsrdReportDefinition();
  }

  getReportMapping(): IReportMapping {
    return new CsrdReportMapping();
  }
}
