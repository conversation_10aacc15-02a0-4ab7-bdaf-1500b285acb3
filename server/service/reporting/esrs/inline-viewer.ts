/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import type { Concept, Languages } from '../common/types';
import type { FactsMap } from '../xhtml/types';
import type { EsrsFact } from './types';

export interface InlineViewerData {
  concepts: Record<string, Concept | undefined>;
  languages: Languages;
  facts: FactsMap<EsrsFact>;
  prefixes: Prefixes;
  roles: Roles;
  roleDefs: RoleDefs;
  rels: Rels;
  validation: unknown[];
}

export interface Rels {
  pres: Pres;
}

export interface Pres {
  [k: `ns${number}`]: EsrsNameSpace;
}

export interface EsrsNameSpace {
  [k: EsrsFact]: TextValue[];
}

export interface TextValue {
  t: string;
}

export interface RoleDefs {
  [k: `ns${number}`]: Languages;
}

export interface Roles {
  std: string;
  doc: string;
  calc: string;
  pres: string;
  'w-n': string;
  ns0: string;
  [k: string]: string;
}

export interface Prefixes {
  esrs_entry: string;
  esrs: string;
  e: string;
  iso4217: string;
  xbrli: string;
  utr: string;
}

export interface ESRSDefinitionRow {
  Level: number;
  Role: string;
  'Label en': string;
  'Additional Label': string;
  'Technical Name': string;
  Abstract: string;
  'Type name short': string;
  'Period type': string;
  Balance: string;
  'Substitution Group': string;
  References: string;
}
