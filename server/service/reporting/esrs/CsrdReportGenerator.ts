import { type LoggerInterface, wwgLogger } from '../../wwgLogger';
import { getXhtmlGenerator } from '../xhtml/XHTMLGenerator';
import type { GeneratorParameters, ReportSection, XbrlTable, XbrlTableData } from '../xhtml/csrdTypes';
import { e4DebugExtensions } from '../xhtml/e4-debug-extensions';
import { getCsvName } from '../../assurance/csvContext';
import { XbrlTracker } from '../XbrlTracker';
import { customDateFormat, DateFormat } from '../../../util/date';
import { ESRSDefinitions } from './ESRSDefinitions';
import type { IReportGenerator } from '../framework/IReportGenerator';

interface GenerateCSRDParams extends Pick<GeneratorParameters, 'initiative' | 'survey' | 'mapping'> {
  sections: ReportSection[];
  preview?: boolean;
}

interface SectionData extends Pick<GeneratorParameters, 'initiative' | 'mapping' | 'utrvData'> {
  tracker: XbrlTracker;
}

interface GenerateTableDataParams extends Pick<SectionData, 'utrvData' | 'mapping' | 'tracker'> {
  utrCode: string;
  facts: string[];
  getData: (factName: string, fallback?: string) => string | number;
}

export class CsrdReportGenerator implements IReportGenerator {
  constructor(private logger: LoggerInterface, private xhtmlGenerator: ReturnType<typeof getXhtmlGenerator>) {}

  public async generate(params: GenerateCSRDParams) {
    const { initiative, survey, preview, sections } = params;
    this.logger.info(`Generating CSRD for survey: ${survey._id}`, {
      surveyId: survey._id,
      initiativeId: survey.initiativeId,
    });

    const tracker = new XbrlTracker();

    const xmlString = await this.xhtmlGenerator.render({
      initiative,
      sections,
      effectiveDate: new Date(survey.effectiveDate),
      customScripts: preview
        ? e4DebugExtensions({
            facts: tracker.getDebugFacts(),
          })
        : '',
    });

    const filename = getCsvName({
      _id: survey._id,
      initiative,
      survey,
      type: 'ESRS',
    });

    return {
      filename: `${filename}${preview ? '-preview' : ''}.xhtml`,
      xmlString,
      tracker,
    };
  }

  private generateTableData = (params: GenerateTableDataParams): XbrlTable => {
    const { utrvData, getData, utrCode, facts, tracker } = params;

    const utrvs = utrvData.filter((utrv) => utrv.universalTracker.code === utrCode);
    const dates = utrvs.map((utrv) => customDateFormat(utrv.effectiveDate, DateFormat.Year));

    const tableColumns = [
      {
        id: 'waste-water-resource-type',
        name: '',
        accessor: 'columnName',
      },
      ...dates.map((date) => ({
        id: date,
        name: date,
        accessor: date,
      })),
    ];

    if (utrvs.length === 0) {
      return {
        type: 'table',
        columns: [],
        data: [],
      };
    }

    const data = facts.map((fact) => {
      return {
        columnName: ESRSDefinitions[fact]?.label ?? fact,
        ...dates.reduce((acc, date) => {
          // @TODO this will only be one date for now
          const value = getData(fact);
          acc[date] = tracker.addFactNonFraction(
            {
              unitRef: tracker.addUnitRef('m3', 'u-101'),
              name: fact,
              children: [{ type: 'inline', content: value }],
            },
            value
          );
          return acc;
        }, {} as XbrlTableData),
      } as XbrlTableData;
    });

    return {
      type: 'table',
      columns: tableColumns,
      data,
    };
  };
}

let instance: CsrdReportGenerator;
export const getCsrdReportGenerator = () => {
  if (!instance) {
    instance = new CsrdReportGenerator(wwgLogger, getXhtmlGenerator());
  }
  return instance;
};
