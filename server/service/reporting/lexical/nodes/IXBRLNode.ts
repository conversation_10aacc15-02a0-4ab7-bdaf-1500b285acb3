import { ElementNode, SerializedElementNode, EditorConfig, LexicalNode, DOMExportOutput } from 'lexical';
import { addClassNamesToElement } from '@lexical/utils';

export interface IXBRLNonNumericFact {
  tag: 'ix:nonNumeric';
  name: string;
  format?: string;
  factId: string;
  contextRef: string;
}

export interface IXBRLNonFractionFact {
  tag: 'ix:nonFraction';
  name: string;
  format?: string;
  factId: string;
  unitRef: string;
  contextRef: string;
  decimals?: number;
  scale?: number | string;
}

export type IXBRLNodeProps = IXBRLNonNumericFact | IXBRLNonFractionFact;

export interface IXBRLElementNodeSerialized extends SerializedElementNode {
  xbrl: IXBRLNodeProps;
}

export class IXBRLNode extends ElementNode {
  private readonly xbrl: IXBRLNodeProps;

  constructor(props: IXBRLNodeProps, key?: string) {
    super(key);
    this.xbrl = props;
  }

  static getType(): string {
    return 'ixbrl-element';
  }

  static clone(node: IXBRLNode): IXBRLNode {
    return new IXBRLNode(node.xbrl, node.__key);
  }

  createDOM(config: EditorConfig): HTMLElement {
    const element = document.createElement('span');
    addClassNamesToElement(element, 'ixbrl-element', config.theme.ixbrlNode);
    element.setAttribute('xbrlTag', this.xbrl.tag);
    element.setAttribute('contextRef', this.xbrl.contextRef);
    element.setAttribute('factId', this.xbrl.factId);
    element.setAttribute('title', this.xbrl.name);

    if (this.xbrl.tag === 'ix:nonFraction') {
      element.setAttribute('unitRef', this.xbrl.unitRef);
      if (this.xbrl.decimals) {
        element.setAttribute('decimals', this.xbrl.decimals.toString());
      }
      if (this.xbrl.scale) {
        element.setAttribute('scale', this.xbrl.scale.toString());
      }
    }
    return element;
  }

  updateDOM(): boolean {
    return false;
  }

  static importJSON(serializedNode: IXBRLElementNodeSerialized): IXBRLNode {
    const xbrlProps = serializedNode.xbrl;
    if (xbrlProps.tag === 'ix:nonFraction') {
      return new IXBRLNode({
        tag: xbrlProps.tag,
        name: xbrlProps.name,
        unitRef: xbrlProps.unitRef,
        contextRef: xbrlProps.contextRef,
        factId: xbrlProps.factId,
        decimals: xbrlProps.decimals,
        scale: xbrlProps.scale,
      });
    } else {
      return new IXBRLNode({
        tag: xbrlProps.tag,
        name: xbrlProps.name,
        contextRef: xbrlProps.contextRef,
        factId: xbrlProps.factId,
      });
    }
  }

  exportJSON(): IXBRLElementNodeSerialized {
    return {
      ...super.exportJSON(),
      xbrl: this.xbrl,
      type: this.getType(),
    };
  }

  getTagName(): string {
    return this.xbrl.tag;
  }

  getName(): string {
    return this.xbrl.name;
  }

  getXbrl(): IXBRLNodeProps {
    if (this.xbrl.tag === 'ix:nonFraction') {
      return this.xbrl ;
    }
    return this.xbrl ;
  }

  // Override to prevent text insertion at the end of IXBRL node
  canInsertTextAfter(): boolean {
    return false;
  }

  // Override to prevent text insertion at the beginning of IXBRL node
  canInsertTextBefore(): boolean {
    return false;
  }

  public canBeEmpty(): boolean {
    return false;
  }

  public isInline(): boolean {
    return true;
  }

  exportDOM(): DOMExportOutput {
    const element = document.createElementNS('http://www.xbrl.org/2013/inlineXBRL', this.xbrl.tag) as HTMLElement;
    element.setAttribute('name', this.xbrl.name);
    element.setAttribute('id', this.xbrl.factId);
    element.setAttribute('contextRef', this.xbrl.contextRef);

    if (this.xbrl.format) {
      element.setAttribute('format', this.xbrl.format);
    }

    if (this.xbrl.tag === 'ix:nonFraction') {
      element.setAttribute('unitRef', this.xbrl.unitRef);
      if (this.xbrl.decimals) {
        element.setAttribute('decimals', this.xbrl.decimals.toString());
      }
      if (this.xbrl.scale) {
        element.setAttribute('scale', this.xbrl.scale.toString());
      }
    }

    return { element };
  }
}

export function $createIxbrlNode(props: IXBRLNodeProps): IXBRLNode {
  return new IXBRLNode(props);
}

export function $isXbrlNode(node: LexicalNode | null | undefined): node is IXBRLNode {
  return node instanceof IXBRLNode || node?.getType() === 'ixbrl-element';
}
