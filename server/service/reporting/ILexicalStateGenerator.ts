/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import { SerializedEditorState } from 'lexical';
import { UserPlain } from './../../models/user';
import { ReportDocumentPlain } from './../../models/reportDocument';
import { XbrlTracker } from './XbrlTracker';

export interface ILexicalStateGenerator {
  generateTemplateLexicalState(params: any): Promise<SerializedEditorState>;
  getTemplate(params: { initiativeId: ObjectId; user: UserPlain; reportType: string }): Promise<SerializedEditorState>;
  downloadReport(params: {
    reportDocument: ReportDocumentPlain;
    editorState: SerializedEditorState;
    reportType: string;
  }): Promise<{ filename: string; xmlString: string; tracker: XbrlTracker }>;
}
