/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { LoggerInterface, wwgLogger } from '../wwgLogger';
import { IReportingFactory } from './framework/IReportingFactory';
import { CsrdReportingFactory } from './esrs/CsrdReportingFactory';
import { ILexicalStateGenerator } from './ILexicalStateGenerator';
import { IReportGenerator } from './framework/IReportGenerator';
import { IReportDefinition } from './framework/IReportDefinition';
import { IReportMapping } from './framework/IReportMapping';
import ContextError from '../../error/ContextError';
import { ReportDocumentType } from '../../models/reportDocument';

export class ReportingFactory implements IReportingFactory {
  constructor(private readonly logger: LoggerInterface) {}

  public getFactory(reportType: string): IReportingFactory {
    switch (reportType) {
      case ReportDocumentType.CSRD:
        return new CsrdReportingFactory();
      case ReportDocumentType.ISSB:
        // TODO: Implement and return IssbReportingFactory
        throw new ContextError('ISSB report type not yet implemented.');
      default:
        throw new ContextError(`Unknown report type: ${reportType}`);
    }
  }

  getLexicalStateGenerator(reportType: string = ReportDocumentType.CSRD): ILexicalStateGenerator {
    return this.getFactory(reportType).getLexicalStateGenerator();
  }

  getReportGenerator(reportType: string = ReportDocumentType.CSRD): IReportGenerator {
    return this.getFactory(reportType).getReportGenerator();
  }

  getReportDefinition(reportType: string = ReportDocumentType.CSRD): IReportDefinition {
    return this.getFactory(reportType).getReportDefinition();
  }

  getReportMapping(reportType: string = ReportDocumentType.CSRD): IReportMapping {
    return this.getFactory(reportType).getReportMapping();
  }
}

let instance: ReportingFactory;
export const getReportingFactory = () => {
  if (!instance) {
    instance = new ReportingFactory(wwgLogger);
  }
  return instance;
};
