/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { getReportingFactory } from './ReportingFactory';
import { ReportDocumentPlain } from '../../models/reportDocument';
import { SerializedEditorState } from 'lexical';

export class ReportingManager {
  constructor(private readonly reportingFactory: ReturnType<typeof getReportingFactory>) {}

  async downloadReport(reportDocument: ReportDocumentPlain, editorState: SerializedEditorState) {
    const lexicalStateGenerator = this.reportingFactory.getLexicalStateGenerator(reportDocument.type);
    return lexicalStateGenerator.downloadReport({ reportDocument, editorState, reportType: reportDocument.type });
  }
}

let instance: ReportingManager;
export const getReportingManager = () => {
  if (!instance) {
    instance = new ReportingManager(getReportingFactory());
  }
  return instance;
};
