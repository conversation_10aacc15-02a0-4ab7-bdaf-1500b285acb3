/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { CustomMetricRepository, InitiativeCustomMetric } from '../../repository/CustomMetricRepository';
import { ObjectId } from 'bson';
import JSZip from 'jszip';
import ContextError from '../../error/ContextError';
import fs from 'fs';
import UserError from '../../error/UserError';
import UniversalTracker, {
  UniversalTrackerModel,
  UniversalTrackerPlain,
  UtrType,
} from '../../models/universalTracker';
import { UtrValueType } from '../../models/public/universalTrackerType';
import type { Tags } from '../../types/universalTrackerValue';
import { blueprintDefaultUnitConfig, SupportedMeasureUnits } from '../units/unitTypes';
import { createCustomMetricDtoSchema } from '../../routes/validation-schemas/universal-trackers';
import { getFeatureUsageService } from '../organization/FeatureUsageService';
import { RootInitiativeData } from '../../repository/InitiativeRepository';
import { FeatureCode } from '@g17eco/core';
import { ActionList } from '../utr/constants';
import { RootInitiativeService, getRootInitiativeService } from '../organization/RootInitiativeService';
import Initiative from '../../models/initiative';
import { hasMinAndMax } from '../../util/universal-trackers';

export type CustomMetricUpdate = Pick<
  UniversalTrackerPlain,
  | 'name'
  | 'valueLabel'
  | 'instructions'
  | 'alternatives'
  | 'valueType'
  | 'typeCode'
  | 'unitType'
  | 'numberScale'
  | 'tags'
  | 'valueValidation'
>;

export interface CreateParams {
  rootInitiative: RootInitiativeData;
  data: Partial<UniversalTrackerPlain>;
  ownerId: ObjectId;
}

export class CustomMetricManager {
  constructor(
    private featureUsageService: ReturnType<typeof getFeatureUsageService>,
    private rootInitiativeService: RootInitiativeService
  ) {}

  public async getCustomMetricsByInitiativeId(initiativeId: string | undefined) {
    const metrics = await CustomMetricRepository.getInitiativeCustomMetrics(new ObjectId(initiativeId));
    const [created, used] = metrics.reduce(
      ([created, used], utr) => {
        return utr.universalTrackerValues.some((utrv) => utrv.status !== ActionList.Created)
          ? [created, [...used, utr]]
          : [[...created, utr], used];
      },
      [[] as InitiativeCustomMetric[], [] as InitiativeCustomMetric[]]
    );
    return { created, used };
  }

  public async createExportByInitiativeId(initiativeId: ObjectId) {
    const metrics = await CustomMetricRepository.dumpInitiativeCustomMetrics(initiativeId);
    const zipFile: JSZip = new JSZip();
    zipFile.file('custom-metrics.json', JSON.stringify(metrics));
    return zipFile;
  }

  public async importDumpToInitiativeId(initiativeId: ObjectId, file: Express.Multer.File) {
    const data = await fs.promises.readFile(file.path);
    const zipFile = await JSZip.loadAsync(data);
    const fileInZip = zipFile.file('custom-metrics.json');
    if (!fileInZip) {
      throw new ContextError('Invalid custom metric import file', {
        initiativeId,
        zipFile: {
          contents: Object.keys(zipFile.files),
        },
        file: {
          originalName: file.originalname,
          size: file.size,
        },
      });
    }

    const content = await fileInZip.async('string');
    const customMetrics = JSON.parse(content) as UniversalTrackerPlain[];
    const updatedCustomMetrics: (Omit<UniversalTrackerPlain, '_id'> & { _id?: undefined })[] = customMetrics.map(
      (cm) => ({
        ...cm,
        _id: undefined,
        ownerId: initiativeId,
      })
    );
    return CustomMetricRepository.importInitiativeCustomMetrics(updatedCustomMetrics);
  }

  private resetInvalidUnitTypeAndNumberScale(utr: UniversalTrackerPlain) {
    if (![UtrValueType.NumericValueList, UtrValueType.Number, UtrValueType.Percentage].includes(utr.valueType)) {
      return {
        ...utr,
        unitType: undefined,
        numberScale: undefined,
      }
    }
    return utr;
  }

  public async update({
    utrId,
    initiativeId,
    updates,
  }: {
    utrId: ObjectId;
    initiativeId: ObjectId;
    updates: CustomMetricUpdate;
  }) {
    const utr = await CustomMetricRepository.findOne({ _id: utrId, ownerId: initiativeId });

    const originalUtr = utr.toObject();

    const formattedUtr = this.resetInvalidUnitTypeAndNumberScale(originalUtr);

    await this.validateBeforeUpdate(formattedUtr, updates);

    if (this.canSetUnit(updates)) {
      utr.unitType = updates.unitType;
      utr.unit = updates.unitType ? blueprintDefaultUnitConfig[updates.unitType] : undefined;
    }

    if (this.canSetNumberScale(updates)) {
      utr.numberScale = updates.numberScale;
    }

    utr.name = updates.name;
    utr.valueLabel = updates.valueLabel;
    utr.instructions = updates.instructions;
    utr.alternatives = updates.alternatives;
    utr.valueType = updates.valueType as UtrValueType;
    utr.typeCode = updates.typeCode;
    utr.tags = { sdg: updates.tags?.sdg ?? [] } as Tags;
    utr.valueValidation = updates.valueValidation;
    await utr.save();

    return { originalUtr, utr };
  }

  private canSetUnit({ valueType }: Partial<Pick<UniversalTrackerPlain, 'valueType'>>) {
    return valueType === UtrValueType.Number;
  }

  private canSetNumberScale({
    valueType,
    valueValidation,
  }: Partial<Pick<UniversalTrackerPlain, 'valueType' | 'valueValidation'>>) {
    return (
      valueType === UtrValueType.Number || (valueType === UtrValueType.Percentage && !hasMinAndMax(valueValidation))
    );
  }

  private async validateBeforeUpdate(utr: Pick<UniversalTrackerPlain, 'valueType' | 'unitType' | '_id' | 'ownerId'>, updates: CustomMetricUpdate) {
    const isValueTypeChanged = utr.valueType !== updates.valueType;
    const isUnitTypeChanged = utr.unitType !== updates.unitType;

    if (!isValueTypeChanged && !isUnitTypeChanged) {
      return;
    }

    if (await CustomMetricRepository.checkIsAnswered(utr._id)) {
      throw new UserError(
        `${isValueTypeChanged ? 'Value type' : 'Value unit'} cannot be edited as metric has previously been answered.`,
        { utrId: utr._id, ownerId: utr.ownerId }
      );
    }
  }

  public async create({ rootInitiative, data, ownerId }: CreateParams): Promise<UniversalTrackerModel> {
    const usage = await this.featureUsageService.getUsage({
      rootInitiative,
      featureCode: FeatureCode.CustomMetrics,
    });

    if (usage.currentUsage >= usage.limit) {
      throw new UserError(`Custom metrics limit (${usage.currentUsage}/${usage.limit}) reached`, {
        rootInitiativeId: rootInitiative._id,
        usage,
        status: 400,
      });
    }

    let valueValidation = {};
    // Only support yes/no for now
    if (data.valueType === 'valueList') {
      valueValidation = {
        valueList: {
          type: 'custom',
          custom: [
            { code: 'yes', name: 'Yes' },
            { code: 'no', name: 'No' },
          ],
        },
      };
    }

    if (data.valueType === UtrValueType.Percentage && data.valueValidation) {
      const result = createCustomMetricDtoSchema.safeParse(data);
      if (result.success) {
        valueValidation = {
          ...valueValidation,
          min: Number(result.data.valueValidation.min),
          max: Number(result.data.valueValidation.max),
        };
      }
    }

    const canSetNumberScale = this.canSetNumberScale({ valueType: data.valueType, valueValidation });
    const canSetUnit = this.canSetUnit({ valueType: data.valueType });

    const utr = new UniversalTracker({
      code: String(new ObjectId()),
      name: data.name,
      valueLabel: data.valueLabel,
      alternatives: data.alternatives,
      instructions: data.instructions,
      type: UtrType.CustomKpi,
      typeCode: data.typeCode,
      valueType: data.valueType as UtrValueType,
      unitType: canSetUnit ? data.unitType : undefined,
      numberScale: canSetNumberScale ? data.numberScale : undefined,
      unit:
        canSetUnit && data.unitType ? blueprintDefaultUnitConfig[data.unitType as SupportedMeasureUnits] : undefined,
      tags: {
        sdg: data.tags?.sdg ?? [],
      },
      ownerId,
      valueValidation: valueValidation,
    });
    return utr.save();
  }

  public async getCustomMetricsUsageByInitiative(initiativeId: string | ObjectId | undefined) {
    const initiative = await Initiative.findById(initiativeId).orFail().exec();
    const rootInitiative = await this.rootInitiativeService.getOrganization(initiative);

    const organisationUsage = await this.featureUsageService.getUsage({
      rootInitiative,
      featureCode: FeatureCode.CustomMetrics,
    });

    const subsidiaryCurrentUsage = await UniversalTracker.find({ ownerId: initiativeId }).countDocuments();

    return {
      organisationCurrentUsage: organisationUsage.currentUsage,
      organisationLimit: organisationUsage.limit,
      subsidiaryCurrentUsage,
    };
  }
}

let instance: CustomMetricManager;
export const getCustomMetricManager = () => {
  if (!instance) {
    instance = new CustomMetricManager(getFeatureUsageService(), getRootInitiativeService());
  }

  return instance;
};
