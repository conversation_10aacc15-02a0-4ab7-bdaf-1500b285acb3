/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import { UserPlain } from '../../models/user';
import Initiative, {
  CompanyTrackerEnterpriseIds,
  CompanyTrackerProIds,
  CompanyTrackerStarterIds,
  InitiativePlain,
  LegacyPermissionGroups,
  PortfolioTrackerIds,
} from '../../models/initiative';
import { AppCode, ProductAppType } from '../app/AppConfig';
import { CustomerManager, getCustomerManager } from '../payment/CustomerManager';
import { materialityTrackerProductCodes } from '../../models/customer';
import Organization, { OrganizationPartnerTypes } from '../../models/organization';
import { isSGXESGenome } from '../../util/initiative';
import { KeysEnum } from '../../models/public/projectionUtils';

const CTPermissionGroups = [
  ...LegacyPermissionGroups,
  ...CompanyTrackerStarterIds,
  ...CompanyTrackerProIds,
  ...CompanyTrackerEnterpriseIds,
];

type AvailableInitiative = Pick<InitiativePlain, '_id' | 'permissionGroup' | 'appConfigCode'>;
const availableInitiativeFields: KeysEnum<AvailableInitiative> = {
  _id: 1,
  permissionGroup: 1,
  appConfigCode: 1,
};

export class UserAppPermissionService {
  constructor(private customerManager: CustomerManager) {}

  private async hasAccessMaterialityTracker(initiatives: AvailableInitiative[]): Promise<boolean> {
    if (initiatives.some((initiative) => initiative.appConfigCode === AppCode.MaterialityTracker)) {
      return true;
    }

    const subscriptionCustomers = await this.customerManager.getSubscriptionsByIds(initiatives.map((i) => i._id));
    const subscriptions = subscriptionCustomers.map((c) => c.subscriptions).flat();

    return Object.values(materialityTrackerProductCodes).some((productId) => {
      return this.customerManager.getValidSubscriptionByProduct(productId, subscriptions);
    });
  }

  private async hasAccessAssuranceTracker(user: Pick<UserPlain, '_id' | 'organizationId'>) {
    const organization = await Organization.findOne(
      {
        partnerTypes: OrganizationPartnerTypes.Assurer,
        $or: [
          { _id: user.organizationId },
          {
            permissions: {
              $elemMatch: {
                userId: user._id,
                permissions: { $exists: true, $not: { $size: 0 } },
              },
            },
          },
        ],
      },
      { _id: 1 }
    )
      .lean()
      .exec();
    return !!organization;
  }

  public async getAvailableApps(
    user: Pick<UserPlain, '_id' | 'permissions' | 'organizationId'>
  ): Promise<ProductAppType[]> {
    const permissionInitiativeIds = user.permissions.reduce((acc, permission) => {
      if (permission.permissions.length > 0) {
        acc.push(permission.initiativeId);
      }
      return acc;
    }, [] as ObjectId[]);

    const availableApps = new Set<ProductAppType>();

    const initiatives = await Initiative.find({ _id: { $in: permissionInitiativeIds } }, availableInitiativeFields)
      .lean<AvailableInitiative[]>()
      .exec();

    const { sgx, portfolioTracker, companyTracker } = initiatives.reduce(
      (acc, i) => {
        // Check Portfolio Tracker access
        if (i.permissionGroup && PortfolioTrackerIds.includes(i.permissionGroup)) {
          acc.portfolioTracker.push(i);
          return acc;
        }

        // Check SGXESGenome access
        if (i.permissionGroup && isSGXESGenome(i)) {
          acc.sgx.push(i);
          return acc;
        }

        // if permissionGroup is undefined then it's a FREE CT version
        if (!i.permissionGroup || CTPermissionGroups.includes(i.permissionGroup)) {
          acc.companyTracker.push(i);
          return acc;
        }

        acc.others.push(i);
        return acc;
      },
      {
        sgx: [] as AvailableInitiative[],
        portfolioTracker: [] as AvailableInitiative[],
        companyTracker: [] as AvailableInitiative[],
        others: [] as AvailableInitiative[],
      }
    );

    if (sgx.length > 0) {
      availableApps.add(ProductAppType.SGXESGenome);
    }

    if (portfolioTracker.length > 0) {
      availableApps.add(ProductAppType.PortfolioTracker);
    }

    if (companyTracker.length > 0) {
      availableApps.add(ProductAppType.CompanyTracker);

      // Check Materiality Tracker access
      const hasAccessMaterialityTracker = await this.hasAccessMaterialityTracker(companyTracker);
      if (hasAccessMaterialityTracker) {
        availableApps.add(ProductAppType.MaterialityTracker);
      }
    }

    // Check the access of Assurance Tracker
    const hasAccessAssuranceTracker = await this.hasAccessAssuranceTracker(user);
    if (hasAccessAssuranceTracker) {
      availableApps.add(ProductAppType.AssuranceTracker);
    }

    return Array.from(availableApps);
  }
}

let instance: UserAppPermissionService;
export const getUserAppPermissionService = () => {
  if (!instance) {
    instance = new UserAppPermissionService(getCustomerManager());
  }
  return instance;
};
