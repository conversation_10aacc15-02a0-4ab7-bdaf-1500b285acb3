/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { AuditEvent } from './AuditModels';
import { InitiativeAudit } from './events/Initiative';
import { SurveyAudit } from './events/survey';
import { UtrvAudit } from './events/utrv';
import { userAudit } from "./events/userAudit";

type EventKey = Pick<AuditEvent, 'service' | 'code'>;

const getKey = (v: EventKey)=> `${v.service}.${v.code}`;

const eventMap = new Map<string, AuditEvent>(
  [
    ...Object.values(InitiativeAudit),
    ...Object.values(SurveyAudit),
    ...Object.values(UtrvAudit),
    ...Object.values(userAudit),
  ].map(v => [getKey(v), v])
);

export const getEvent = (eventKey: EventKey) => eventMap.get(getKey(eventKey));
