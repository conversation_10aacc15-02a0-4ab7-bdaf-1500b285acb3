/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { PACK, SURVEY } from '../../../util/terminology';

export const SurveyAudit = {
  aggregateUpdate: {
    service: 'survey',
    code: 'aggregate_update',
    name: `Update Aggregated ${SURVEY.CAPITALIZED_SINGULAR}`,
  },
  /** @deprecated no longer used */
  publish: {
    service: 'survey',
    code: 'survey_publish',
    name: `Make ${SURVEY.CAPITALIZED_SINGULAR} Shared`,
  },
  /** @deprecated no longer used, leave for historical reasons */
  unpublish: {
    service: 'survey',
    code: 'survey_unpublish',
    name: `Make ${SURVEY.CAPITALIZED_SINGULAR} Private`,
  },
  create: {
    service: 'survey',
    code: 'survey_create',
    name: `${SURVEY.CAPITALIZED_SINGULAR} Create`,
  },
  disable: {
    service: 'survey',
    code: 'survey_disable',
    name: `${SURVEY.CAPITALIZED_SINGULAR} Disable`,
  },
  configurationUpdate: {
    service: 'survey',
    code: 'survey_configuration_update',
    name: `${SURVEY.CAPITALIZED_SINGULAR} Configuration Update`,
    description: `${SURVEY.CAPITALIZED_SINGULAR} %s config settings updated`,
  },
  scopeAdded: {
    service: 'survey',
    code: 'survey_scope_add',
    name: `${SURVEY.CAPITALIZED_SINGULAR} scope ${PACK.SINGULAR} add`,
    description: 'Scope added to %s',
  },
  scopeRemoved: {
    service: 'survey',
    code: 'survey_scope_remove',
    name: `${SURVEY.CAPITALIZED_SINGULAR} scope ${PACK.SINGULAR} removed`,
    description: 'Scope removed from %s',
  },
  complete: {
    service: 'survey',
    code: 'survey_complete',
    name: `Make ${SURVEY.CAPITALIZED_SINGULAR} Completed`,
  },
  uncomplete: {
    service: 'survey',
    code: 'survey_uncomplete',
    name: `Make ${SURVEY.CAPITALIZED_SINGULAR} Not Completed`,
  },
  aiAutoAnswerSurvey: {
    service: 'survey',
    code: 'ai_auto_answer_survey',
    name: 'AI Auto Answer Survey',
    description: 'AI Auto Answer Survey',
  },
};
