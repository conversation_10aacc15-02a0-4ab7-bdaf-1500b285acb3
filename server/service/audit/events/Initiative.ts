/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


export const InitiativeAudit = {
  profileUpdate: {
    service: 'initiative',
    code: 'profile_update',
    name: 'Update Reporting Level Profile information',
    description: 'Profile Update'
  },
  featuresUpdate: {
    service: 'initiative',
    code: 'features_update',
    name: 'Update Reporting Level features',
    description: 'Features Update'
  },
  parentIdUpdate: {
    service: 'initiative',
    code: 'parent_id_update',
    name: 'Update Reporting Level parent level',
    description: 'Update Reporting Level parent level'
  },
  archivedInitiative: {
    service: 'initiative',
    code: 'archived_initiative',
    name: 'Archive Reporting Level',
    description: 'Archive Reporting Level'
  },
  reactivatedInitiative: {
    service: 'initiative',
    code: 'reactivated_initiative',
    name: 'Reactivate Reporting Level',
    description: 'Reactivate Reporting Level'
  },
  userRemove: {
    service: 'initiative',
    code: 'user_remove',
    name: 'User Remove',
    description: 'Reporting Level - user removed',
  },
  userAdd: {
    service: 'initiative',
    code: 'user_add',
    name: 'User Add',
    description: 'Reporting Level - user added',
  },
  userPermissionUpdate: {
    service: 'initiative',
    code: 'user_permission_update',
    name: 'User Permission Update',
    description: 'Reporting Level - user permission updated',
  },
  dataShareRequest: {
    service: 'data_share',
    code: 'data_share_request',
    name: 'Data share request created',
    description: 'Data share - request was created',
  },
  dataShareAccept: {
    service: 'data_share',
    code: 'data_share_accept',
    name: 'Data share request accept',
    description: 'Data share - request was accepted',
  },
  dataShareRevoke: {
    service: 'data_share',
    code: 'data_share_revoke',
    name: 'Data share request revoke',
    description: 'Data share - request was revoked',
  },
  dataShareDelete: {
    service: 'data_share',
    code: 'data_share_delete',
    name: 'Data share request deleted',
    description: 'Data share - request was deleted',
  },
  dataShareUpdate: {
    service: 'data_share',
    code: 'data_share_update',
    name: 'Data share request updated',
    description: 'Data share - request was updated',
  },
  metricGroupCreated: {
    service: 'metric_group',
    code: 'metric_group_created',
    name: 'Metric group pack created',
    description: 'Metric group pack %s created',
  },
  metricGroupUpdated: {
    service: 'metric_group',
    code: 'metric_group_updated',
    name: 'Metric group pack config updated',
    description: 'Metric group pack %s updated',
  },
  metricGroupDeleted: {
    service: 'metric_group',
    code: 'metric_group_deleted',
    name: 'Metric group pack deleted',
    description: 'Metric group pack %s deleted',
  },
  metricGroupQuestionAdded: {
    service: 'metric_group',
    code: 'metric_group_question_added',
    name: 'Question added to Metric group pack',
    description: 'Question added to Metric group pack %s',
  },
  metricGroupQuestionRemoved: {
    service: 'metric_group',
    code: 'metric_group_question_removed',
    name: 'Question removed from Metric group pack',
    description: 'Question removed from Metric group pack %s',
  },
  custom_metric_updated: {
    service: 'custom_metric',
    code: 'custom_metric_question_updated',
    name: 'Custom question updated',
    description: 'Custom question %s updated',
  },
  custom_metric_deleted: {
    service: 'custom_metric',
    code: 'custom_metric_question_deleted',
    name: 'Custom question updated',
    description: 'Custom question %s deleted',
  },
  templateHistoryDeleted: {
    service: 'template_history',
    code: 'template_history_deleted',
    name: 'Template history deleted',
  },
  aiFeaturesAccepted: {
    service: 'initiative',
    code: 'ai_features_accepted',
    name: 'AI features accepted',
    description: 'AI Features accepted'
  }
} as const;
