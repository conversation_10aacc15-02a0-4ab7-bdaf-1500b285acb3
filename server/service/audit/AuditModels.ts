/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import { FeatureTag } from '@g17eco/core';
import { LEVEL } from '../event/Events';
import { ObjectId } from 'bson';
import { Actions } from '../action/Actions';

export interface UserAgent {
  rawUserAgent: string;
  os?: string;
  browser?: string;
}

export interface GeographicalContext {
  city?: string; // London
  state?: string; // England
  country?: string; // gb
  postalCode?: string | null; // ES1
  continentCode?: string; // AS
  geolocation?: {
    lat: number;
    lon: number;
  };
}

export enum ContinentCodes {
  Africa = 'AF',
  Asia = 'AS',
  Europe = 'EU',
  NorthAmerica = 'NA',
  Oceania = 'OC',
  SouthAmerica = 'SA',
  Antartica = 'AN',
}

export interface AuditClient {
  userAgent: UserAgent;
  // zone: string;
  device?: string;
  id?: string;
  ipAddress?: string;
  geographicalContext?: GeographicalContext | undefined;
}

export interface DebugContext {
  debugData: {
    requestId: string; // generated id
    requestUri: string; // path
    url: string; // path with query
    tag?: FeatureTag;
    action?: Actions;
  };
}

type DetailData = Record<string, unknown>;

export interface Transaction {
  type: 'WEB';
  id: string;
  detail?: DetailData;
}

export type ActorType =
  | 'UserApiKey'
  | 'User'
  | 'System'
  | 'Survey'
  | 'Initiative'
  | 'DataShare'
  | 'Portfolio'
  | 'UniversalTrackerValue'
  | 'UniversalTracker'
  | 'MetricGroup'
  | 'TemplateHistory';

// id	"00u271zmgySR9Qwgt5d7"
// type	"AppUser"
// alternateId	"<EMAIL>"
// displayName	"Kestutis Kasiulynas"
// detailEntry	null
export interface Actor<T = ObjectId | string, U = DetailData> {
  id: T;
  type: ActorType;
  alternateId?: string;
  displayName?: string;
  detailedEntry?: U;
}

export enum Operation {
  Access = 'access',
  Authentication = 'authentication',
  Create = 'create',
  Modify = 'modify',
  Remove = 'remove',
  Restore = 'restore',
  Transfer = 'transfer',
}

export interface Outcome {
  result: 'SUCCESS' | 'ERROR' | 'ALLOW';  // result	"ALLOW"
  reason?: string; // reason	"Sign-on policy evaluation resulted in ALLOW"
}

interface OutcomeAuditData {
  outcome: Outcome;
  event: string; // 'user_created' | 'registered' ...
  message: string; // "User single sign on to app"
  severity: LEVEL; // Standard Log - ERROR = 3,WARNING = 4,NOTICE = 5 etc.
  targets: Actor[];
}

export interface CreateAuditEntry extends OutcomeAuditData {
  // Could be combined into eventType = 'user.user_created' or 'user.created'
  service: string; // 'user' | 'onboarding'
  operation: Operation

  // Associate for specific org & level
  organizationId?: ObjectId; // Who it belongs to?

  // Specific reporting level
  initiativeId?: ObjectId | string;

  actor: Actor;

  client: AuditClient;
  transaction: Transaction;
  debugContext: DebugContext;
  eventDate: Date;
}

export interface AuditLogEntry extends CreateAuditEntry {
  _id: ObjectId;
  version: 0;
  initiativeId?: ObjectId;
  created: Date;
  actor: Actor<ObjectId>;
  targets: Actor<ObjectId>[];

  // Generated
  uuid: string; // This will probably be used later
}

export interface AuditLogExpanded extends CreateAuditEntry {
  auditEvent?: AuditEvent
}

export interface AuditEvent {
  service: string;
  code: string;

  // Human-readable generic name of the event
  name: string;
  description?: string;
  link?: string;
}
