/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import { Actor, ActorType, AuditLogExpanded } from './AuditModels';
import { ObjectId } from 'bson';
import User from '../../models/user';
import Initiative from '../../models/initiative';
import UniversalTrackerValue from '../../models/universalTrackerValue';
import MetricGroup from "../../models/metricGroup";
import UniversalTracker from "../../models/universalTracker";
import UserApiKey from "../../models/userApiKey";


type LookupData = { _id: ObjectId, displayName: string };
type LookupDataMap = Map<string, LookupData>;
type LookupResult = { type: string, data: LookupDataMap };

type LookupMap = Map<ActorType, Set<string>>;

export class AuditLookup {

  public async addLookupInfo(logs: AuditLogExpanded[]) {

    const lookUpMap = this.getLookupMap(logs)
    const lookupData = await this.getLookupData(lookUpMap);

    logs.forEach(l => {
      this.updateFromLookup(l.actor, lookupData);
      l.targets.forEach(target => this.updateFromLookup(target, lookupData));
    })
    return logs;
  }


  private async getUsers(ids: string[]): Promise<LookupDataMap> {
    const users = await User.find(
      { _id: { $in: ids.map(id => new ObjectId(id)) } },
      { firstName: 1, surname: 1 },
    ).lean().exec();

    const dataMap: LookupDataMap = new Map()

    users.forEach(user => {
      dataMap.set(String(user._id), {
        _id: user._id,
        displayName: this.getName(user),
      })
    })

    return dataMap
  }

  private async getInitiatives(ids: string[]): Promise<LookupDataMap> {
    const initiatives = await Initiative.find(
      { _id: { $in: ids.map(id => new ObjectId(id)) } },
      { name: 1 },
    ).lean().exec();

    const dataMap: LookupDataMap = new Map()
    initiatives.forEach(initiative => {
      dataMap.set(String(initiative._id), {
        _id: initiative._id,
        displayName: initiative.name,
      })
    })

    return dataMap
  }

  private async getUtrvs(ids: string[]): Promise<LookupDataMap> {
    const utrvs = await UniversalTrackerValue.find(
      { _id: { $in: ids.map(id => new ObjectId(id)) } },
      { name: 1, universalTrackerId: 1 },
    ).populate('universalTracker', 'name').lean().exec();

    const dataMap: LookupDataMap = new Map()
    utrvs.forEach(utrv => {
      dataMap.set(String(utrv._id), {
        _id: utrv._id,
        displayName: utrv.universalTracker?.name ?? String(utrv._id),
      })
    });

    return dataMap;
  }

  private getLookupMap(auditLogs: AuditLogExpanded[]): LookupMap {

    const lookupMap =  new Map() as LookupMap;

    // Extract all Actors and Targets
    auditLogs.forEach((auditLog) => {
      this.addLookup(auditLog.actor, lookupMap);
      auditLog.targets.forEach(target => this.addLookup(target, lookupMap));
    })

    return lookupMap;
  }

  private addLookup(actor: Actor, lookupMap: LookupMap) {
    const ids = lookupMap.get(actor.type);

    const id = String(actor.id);
    if (ids) {
      ids.add(id)
      return lookupMap;
    }

    const set = new Set<string>();
    set.add(id)
    return lookupMap.set(actor.type, set)
  }


  private getName(user: { firstName?: string, surname?: string }) {
    return `${user.firstName ?? ''} ${user.surname ?? ''}`.trim();
  }

  private async getLookupData(lookUpMap: LookupMap) {

    const promises: Promise<LookupResult>[] = [];
    for (const [type, ids] of lookUpMap) {
      promises.push(this.lookupByType(type, Array.from(ids)));
    }
    const data = await Promise.all(promises);

    const lookupData = new Map<string, LookupDataMap>()
    data.forEach(typeData => {
      lookupData.set(typeData.type, typeData.data)
    })

    return lookupData;
  }

  /**
   * Expand type with latest information, otherwise use existing data
   * This is only really needed for types that can change displayName property
   */
  private async lookupByType(type: ActorType, ids: string[]): Promise<LookupResult> {
    switch (type) {
      case 'User':
        return { type, data: await this.getUsers(ids) };
      case 'Portfolio':
        return { type, data: await this.getInitiatives(ids) };
      case 'Initiative':
        return { type, data: await this.getInitiatives(ids) };
      case 'UniversalTracker':
        return { type, data: await this.getUniversalTrackers(ids) };
      case 'UniversalTrackerValue':
        return { type, data: await this.getUtrvs(ids) };
      case 'MetricGroup':
        return { type, data: await this.getMetricGroups(ids) };
      case 'UserApiKey':
        return { type, data: await this.getUserApiKeys(ids) };
    }

    return { type, data: new Map() };
  }

  private updateFromLookup(actor: Actor, lookupData: Map<string, LookupDataMap>) {
    const data = lookupData.get(actor.type)
    if (data) {
      const actorData = data.get(String(actor.id));
      if (actorData) {
        actor.displayName = actorData.displayName;
      }
    }
  }

  private async getMetricGroups(ids: string[]): Promise<LookupDataMap> {
    const metricGroups = await MetricGroup.find(
      { _id: { $in: ids.map(id => new ObjectId(id)) } },
      { groupName: 1 },
    ).lean().exec();

    return new Map(metricGroups.map(metricGroup => [String(metricGroup._id), {
      _id: metricGroup._id,
      displayName: metricGroup.groupName,
    }]));
  }

  private async getUniversalTrackers(ids: string[]): Promise<LookupDataMap> {
    const utrs = await UniversalTracker.find(
      { _id: { $in: ids.map(id => new ObjectId(id)) } },
      { name: 1, code: 1, valueLabel: 1 },
    ).lean().exec();

    return new Map(utrs.map(utr => [String(utr._id), {
      _id: utr._id,
      code: utr.code,
      displayName: utr.name,
    }]));
  }

  private async getUserApiKeys(ids: string[]): Promise<LookupDataMap> {
    const userApiKeys = await UserApiKey.find(
      { _id: { $in: ids.map(id => new ObjectId(id)) } },
      { name: 1, shortToken: 1 },
    ).lean().exec();


    return new Map(userApiKeys.map((apiKey) => [
        String(apiKey._id),
        {
          _id: apiKey._id,
          displayName: apiKey.name ?? apiKey.shortToken,
        }
      ]
    ));
  }
}
