/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { AuditLogEntry, AuditLogExpanded, CreateAuditEntry } from './AuditModels';
import { LoggerInterface } from '../wwgLogger';
import { ObjectId } from 'bson';
import { getEvent } from './AuditEvents';

export interface AuditFilterOptions {
  fromDate?: Date;
  toDate?: Date;
  limit?: number;
}

export interface AuditStorage {
  save(auditLog: CreateAuditEntry): Promise<AuditLogEntry>;
  findForInitiatives(ids: ObjectId[], filters: AuditFilterOptions): Promise<AuditLogEntry[]>;
}

export class AuditService {
  constructor(private logger: LoggerInterface, private auditRepo: AuditStorage) {}

  public async add(auditEvent: CreateAuditEntry) {
    await this.auditRepo.save(auditEvent);

    // Need to save after with a shallow copy - as log update passed in object
    // @TODO mongoose SingleSet object can cause memory leak here...
    this.logger.info({ ...auditEvent });
  }

  public async getAuditForInitiative(
    initiativeId: ObjectId,
    filters: AuditFilterOptions
  ): Promise<AuditLogExpanded[]> {
    const logs = await this.auditRepo.findForInitiatives([initiativeId], filters);

    return logs.map((log) => ({
      ...log,
      auditEvent: getEvent({ code: log.event, service: log.service }),
    }));
  }
}
