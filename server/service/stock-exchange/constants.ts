import config from '../../config';

export interface Country {
  name: string;
  code: string;
}

export interface StockExchange {
  name?: string;
  code: string;
  countries?: Country[];
  logo?: string;
  countryCodes?: string[];
  popularCountryCodes?: string[];
}

export const LIST_OF_STOCK_EXCHANGES: StockExchange[] = [
  { name: 'New York Stock Exchange', code: 'NYSE', countries: [] },
  { name: 'NASDAQ', code: 'NASDAQ', countries: [] },
  { name: 'London Stock Exchange', code: 'LSE', countries: [] },
  { name: 'Tokyo Stock Exchange', code: 'TSE', countries: [] },
  { name: 'Shanghai Stock Exchange', code: 'SSE', countries: [] },
  { name: 'Hong Kong Stock Exchange', code: 'HKEX', countries: [] },
  { name: 'Euronext', code: 'ENX', countries: [] },
  { name: 'Toronto Stock Exchange', code: 'TSX', countries: [] },
  { name: 'Bombay Stock Exchange', code: 'BSE', countries: [] },
  { name: 'National Stock Exchange of India', code: 'NSE', countries: [] },
  { name: 'Deutsche Börse', code: 'DB1', countries: [] },
  { name: 'SIX Swiss Exchange', code: 'SIX', countries: [] },
  { name: 'Australian Securities Exchange', code: 'ASX', countries: [] },
  { name: 'Korea Exchange', code: 'KRX', countries: [] },
  { name: 'Johannesburg Stock Exchange', code: 'JSE', countries: [] },
  { name: 'B3 (Brasil Bolsa Balcão)', code: 'B3SA3', countries: [] },
  { name: 'Bolsas y Mercados Españoles', code: 'BME', countries: [] },
  {
    name: 'Singapore Exchange',
    code: 'SGX',
    countries: [],
    popularCountryCodes: ['SG'],
    logo: `${config.assets.cdn}/stock-exchanges/sgx.svg`,
  },
  { name: 'Taiwan Stock Exchange', code: 'TWSE', countries: [] },
  { name: 'Moscow Exchange', code: 'MOEX', countries: [] },
  { name: 'Japan Exchange Group', code: 'JPX', countries: [] },
  { name: 'Borsa Italiana', code: 'BIT', countries: [] },
  {
    name: 'Indonesia Stock Exchange',
    code: 'IDX',
    countries: [],
    popularCountryCodes: ['SG'],
    logo: `${config.assets.cdn}/stock-exchanges/idx.png`,
  },
  { name: 'Saudi Stock Exchange (Tadawul)', code: 'TADAWUL', countries: [] },
  { name: 'Qatar Stock Exchange', code: 'QE', countries: [] },
  { name: 'Tadawul All Share Index', code: 'TASI', countries: [] },
  { name: 'Dubai Financial Market', code: 'DFMGI', countries: [] },
  { name: 'Abu Dhabi Securities Exchange', code: 'ADX', countries: [] },
  { name: 'Warsaw Stock Exchange', code: 'WSE', countries: [] },
  {
    name: 'Bursa Malaysia',
    code: 'WSE',
    countries: [],
    popularCountryCodes: ['SG'],
    logo: `${config.assets.cdn}/stock-exchanges/bursa-malaysia.svg`,
  },
];
