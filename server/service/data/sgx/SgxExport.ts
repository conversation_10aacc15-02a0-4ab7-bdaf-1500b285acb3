/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import config from '../../../config';
import { SgxOptions } from './ExportTypes';
import { getSgxRepository, SgxRepository } from './SgxRepository';
import { writeFile } from 'fs/promises';
import path from 'path';
import { LoggerInterface, wwgLogger } from '../../wwgLogger';
import { ExportSet, Organization, RecordStatus, Survey, TransformedUtrv } from './sgxTypes';
import moment from 'moment';
import { generatedUUID } from '../../crypto/token';
import { mkDirByPathSync, removeTmpFolder } from '../../file/filesystem';
import { customDateFormat, DateFormat } from '../../../util/date';
import { getSFTPClient, SFTPClient } from '../../sftp/SftpClient';
import { ObjectId } from 'bson';
import { extractListId } from '../../utr/utrUtil';
import { ValueListRepository } from '../../../repository/ValueListRepository';
import { isJobFinished } from '../../jobs/status';
import ContextError from '../../../error/ContextError';
import {
  BackgroundJobModel,
  BackgroundJobPlain,
  isTaskSgxExport,
  JobStatus,
  TaskSgxExport,
  TaskStatus,
  TaskType
} from '../../../models/backgroundJob';
import { createLog } from '../../jobs/logMessage';
import UserError from '../../../error/UserError';
import { getSgxExportStorage, SgxExportStorage } from './SgxExportStorage';

interface ExportJobSetup {
  job: BackgroundJobModel;
  options: SgxOptions;
  lastJob: BackgroundJobModel | null;
}

type SurveyMap = Map<string, Survey<ObjectId, TransformedUtrv>>;

export class SgxExport {

  /** Used to create data file and find files from existing tasks **/
  private readonly datafileNamePart = `G17Eco-SGX_Part`;


  constructor(
    private logger: LoggerInterface,
    private repo: SgxRepository,
    private sftpClient: SFTPClient,
    private storage: SgxExportStorage,
  ) {
  }

  public async generate(customOptions: Partial<SgxOptions>) {

    this.logger.info("Starting SGX Export generation", customOptions)

    const lastJob = await this.repo.getLastJob();
    if (lastJob) {
      if (!isJobFinished(lastJob.status)) {
        throw new ContextError('Last Job have not yet completed', {
          jobId: lastJob.id,
          type: lastJob.type,
          created: lastJob.created,
          updated: lastJob.updated,
          jobStatus: lastJob.status,
          retryCount: lastJob.retryCount,
        })
      }

      if (!customOptions.previousRun) {
        customOptions.previousRun = new Date(lastJob.created)
      }
    }

    const options = this.mergeOptions(customOptions, lastJob);

    const currentDate = moment();
    const date = currentDate.format('YYYY-MM-DD');
    const job = await this.repo.createSgxJob({
        id: generatedUUID(),
        attributes: [
          { name: 'previousRun', value: options.previousRun.toISOString() }
        ],
        created: currentDate.toDate(),
        files: [],
        name: `${date} G17Eco - SGX Export`,
        status: JobStatus.Processing,
        type: 'full',
        updated: currentDate.toDate(),
      }
    );
    this.runJob({ job: job, options: options, lastJob: lastJob }).catch((e: Error) => {
      this.logger.error(e);
      job.logs.push(createLog(e.message));
      job.status = JobStatus.Error;
      job.save().catch((e) => this.logger.error(e))
    });

    return job;
  }

  /**
   * Run the job and send the data to SGX SFTP
   * @internal Should not be called directly, used only for tests.
   * It should be refactored to be blocking ,while all of the export logic should
   * be moving to background jobs scheduler
   */
  public async runJob(jobSetup: ExportJobSetup) {
    const { job } = jobSetup
    const archiveDir: string = await this.generateFiles(jobSetup);

    const { host, port, userId, privateKey, destinationPath } = config.sftp.sgx;
    if (!host || !port || !userId || !privateKey) {
      const skipMsg = "No SFTP details found. Could not send data to SGX SFTP";
      this.logger.error(new ContextError(skipMsg));
      await removeTmpFolder(archiveDir);
      job.logs.push(createLog(skipMsg))
      await job.save();
      return { archiveDir };
    }

    try {
      job.logs.push(createLog(`Writing file to google cloud storage for safekeeping`));
      await this.storage.uploadFolder(archiveDir);
      job.logs.push(createLog(`Completed writing file to google cloud storage for safekeeping`));
    } catch (e) {
      this.logger.error(e);
      job.logs.push(createLog(`Error writing file to google cloud storage: ${e.message}`));
    }

    try {
      job.logs.push(createLog(`Connecting to SGX ${host} SFTP`))
      await this.sftpClient.connect(host, Number(port), userId, privateKey)
      await this.sftpClient.uploadDir(archiveDir, destinationPath)
      await this.sftpClient.disconnect();
      job.logs.push(createLog(`Upload completed to SGX ${host} SFTP`))
    } catch (e) {
      const msg = `Error connecting to SGX SFTP: ${e.message}`;
      this.logger.error(new ContextError(msg));
      job.logs.push(createLog(msg));
      await job.save().catch((e) => this.logger.error(e))
      throw new UserError('Could not connect to SGX SFTP');
    } finally {
      await removeTmpFolder(archiveDir);
    }

    await job.save();

    this.logger.info("Sent report to SGX SFTP", { destinationPath, archiveDir });

    return { destinationPath, archiveDir };
  }

  private async generateFiles(jobSetup: ExportJobSetup) {
    const { job, options, lastJob } = jobSetup
    const initiatives = await this.repo.getRootInitiatives(options);

    this.logger.info(`Exporting ${initiatives.length} companies data to SGX`, {
      initiativeIds: initiatives.map(i => i._id.toHexString())
    })

    job.attributes.push({ name: 'companiesCount', value: String(initiatives.length) });

    const universalTrackerData = await this.repo.getSgxUniversalTrackers();
    job.logs.push(createLog(`Loaded UTR (${universalTrackerData.length}) data`))

    this.logger.info(`Starting loading export data`);
    const now = Date.now();
    const initialData = await this.repo.getExportData(options, initiatives, universalTrackerData);
    const duration = Date.now() - now;
    this.logger.info(`Finished loading export data: completed in ${duration}ms`, { duration });

    // Add removed or re-added surveys from previous export
    const data = await this.modifyOnPreviousExport(initialData, lastJob)

    job.logs.push(createLog(`Loaded Export Organization (${data.length}) data`))
    const batches = [data];

    // We know this must be here now
    const [exportTask] = job.tasks as [TaskSgxExport];

    const archiveDir = mkDirByPathSync(path.join(options.outputDir, job.name))

    const files: string[] = [];
    await Promise.all(batches.map(async (batch, index) => {
      const filename = `${customDateFormat(new Date(), DateFormat.DefaultDashes)}_${this.datafileNamePart}_${(index + 1)}.json`;
      const outputPath = path.join(archiveDir, filename);
      files.push(filename);
      return writeFile(outputPath, JSON.stringify(batch));
    }));

    // Modify data directly for it be correctly saved in db
    exportTask.data = { files, type: exportTask.data.type };
    exportTask.status = TaskStatus.Processing;
    job.status = JobStatus.Processing;
    job.logs.push(createLog('Start generating files'))
    await job.save();

    const date = moment(job.created).format('YYYY-MM-DD');

    const listIds: ObjectId[] = [];
    universalTrackerData.forEach(utr => {
      listIds.push(...extractListId(utr.valueValidation));
    });
    const valueLists = await ValueListRepository.findByIdsMin(listIds);

    const exportUtrs = await this.repo.convertToExportUtrs(universalTrackerData)
    const utrFileName = `${date}_G17Eco-SGX_UniversalTracker.json`;
    await writeFile(
      path.join(archiveDir, utrFileName),
      JSON.stringify(exportUtrs, null, 4)
    );
    exportTask.data.files.push(utrFileName);

    const valueListFilename = `${date}_G17Eco-SGX_ValueList.json`
    await writeFile(
      path.join(archiveDir, valueListFilename),
      JSON.stringify(valueLists, null, 4)
    );
    exportTask.data.files.push(valueListFilename);
    const metadataFilename = `${date}_G17Eco-SGX_Export.json`;
    exportTask.data.files.push(metadataFilename);

    job.logs.push({
      severity: 4,
      message: 'Completed generating files',
      created: new Date(),
      metadata: {
        files: exportTask.data.files
      }
    })
    await job.save();

    exportTask.status = TaskStatus.Completed;
    job.status = JobStatus.Completed;
    job.completedDate = new Date();

    const exportSet = this.getExportSetFromTask(job)

    await writeFile(
      path.join(archiveDir, metadataFilename),
      JSON.stringify(exportSet, null, 4)
    );

    const completeMsg = "Completed SGX Export generation";
    job.logs.push(createLog(completeMsg, exportSet))
    this.logger.info(completeMsg); // Temporarily remove exportSet to see if it is the cause of the memory sentry error

    return archiveDir;
  }

  private mergeOptions(options: Partial<SgxOptions>, lastJob?: { created: Date } | null): SgxOptions {

    const mergedOptions = {
      outputDir: '/tmp/sgx',
      previousRun: new Date('1970-01-01T00:00:00.000Z'),
      ...options,
    };

    if (lastJob?.created) {
      mergedOptions.previousRun = new Date(lastJob.created);
    }

    return mergedOptions
  }

  private getExportSetFromTask(job: BackgroundJobPlain): ExportSet {
    const [task] = job.tasks as [TaskSgxExport]
    return {
      id: task.id,
      attributes: job.attributes,
      created: job.created,
      files: task.data.files,
      name: task.name,
      status: job.status,
      type: task.data.type,
      updated: job.updated,
      completedDate: job.completedDate
    };
  }

  /**
   * Process the data from the previous export if available and add whatever is
   * missing, most likely no longer having the completedDate.
   */
  private async modifyOnPreviousExport(initialData: Organization[], lastJob: BackgroundJobModel | null) {

    if (!lastJob) {
      return initialData;
    }

    const task = lastJob.tasks.find(task => task.type === TaskType.SgxExport);
    if (!isTaskSgxExport(task)) {
      return initialData;
    }

    const dataFilenames = task.data.files.filter(name => name.includes(this.datafileNamePart));

    if (dataFilenames.length === 0) {
      return initialData;
    }

    // downloadAllFiles
    const previousFiles: Organization[][] = await Promise.all(dataFilenames.map(async (filename) => {
      const remotePath = path.join(lastJob.name, filename);
      const file = await this.storage.getFile(remotePath);
      const [exist] = await file.exists();
      if (!exist) {
        return [];
      }

      const [buffer] = await file.download();
      return JSON.parse(buffer.toString()) as Organization[];
    }));

    const previousExportMap = new Map<string, SurveyMap | undefined>();
    previousFiles.flat().forEach((org) => {
      org.initiatives.forEach(initiative => {
        const surveyMap: SurveyMap = new Map()

        initiative.surveys.forEach(survey => {
          // We don't to mark Deleted again, can safely skip
          if (survey.recordStatus !== RecordStatus.Deleted) {
            surveyMap.set(survey._id.toString(), survey)
          }
        })

        previousExportMap.set(initiative._id, surveyMap);
      });
    });

    return initialData.map((org) => {

      org.initiatives = org.initiatives.map(initiative => {
        const previousData = previousExportMap.get(initiative._id);

        initiative.surveys.forEach(survey => {

          const id = survey._id.toString();
          if (!previousData?.has(id) && survey.recordStatus !== RecordStatus.New) {
            // Must be marked as new, as it did not exist in previous export.
            // Most likely was marked as completed again.
            survey.recordStatus = RecordStatus.New;

            survey.universalTrackerValues = survey.universalTrackerValues.reduce((acc, utrv) => {

              if (utrv.recordStatus === RecordStatus.Deleted) {
                // Skip deleted, even if it's new survey being added.
                return acc;
              }

              // Private should still remain, others will reset to created
              if (RecordStatus.Private !== utrv.recordStatus) {
                utrv.recordStatus = RecordStatus.New;
              }
              acc.push(utrv);

              return acc;
            }, [] as typeof survey.universalTrackerValues);

            return;
          }

          previousData?.delete(id);
        });

        if (!previousData) {
          return initiative;
        }

        // Whatever is left must be deleted, push it with deleted record status
        for (const removedSurvey of previousData.values()) {
          // Modify in place, as we don't need the original data anymore
          // we are already potentially consuming a lot of memory
          removedSurvey.recordStatus = RecordStatus.Deleted;
          removedSurvey.universalTrackerValues.forEach(utrv => {
            utrv.recordStatus = RecordStatus.Deleted;
          });
          initiative.surveys.push(removedSurvey)
        }
        return initiative;
      })

      return org;
    });
  }
}

let instance: SgxExport;
export const getSgxExport = () => {
  if (!instance) {
    instance = new SgxExport(
      wwgLogger,
      getSgxRepository(),
      getSFTPClient(),
      getSgxExportStorage()
    );
  }
  return instance;
}
