/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { Storage, Bucket } from '@google-cloud/storage';
import { readdirSync } from 'fs';
import config from '../../../config';
import { getGoogleCloudCredentials } from "../../google-cloud/credentials";

interface Options {
  bucketName: string;
  folder: string;
}

export class SgxExportStorage {

  private readonly bucket: Bucket;
  private readonly folder: string;

  constructor(options: Options) {
    this.bucket = this.getStorage().bucket(options.bucketName);
    this.folder = options.folder;
  }

  private getStorage() {
    return new Storage(getGoogleCloudCredentials());
  }

  public async uploadFolder(sourcePath: string) {
    const folderName = sourcePath.split('/').pop();
    const environmentPath = config.appEnv;
    const filenames = readdirSync(sourcePath);
    filenames.forEach(filename => {
      this.bucket.upload(`${sourcePath}/${filename}`, {
        destination: `${this.folder}/${environmentPath}/${folderName}/${filename}`
      });
    });
  }

  public async getFile(filename: string) {
    return this.bucket.file(`${this.folder}/${config.appEnv}/${filename}`)
  }
}

let instance: SgxExportStorage;
export const getSgxExportStorage = () => {
  if (!instance) {
    const { bucketName, folder } = config.sgx.export;
    instance = new SgxExportStorage({ bucketName, folder });
  }
  return instance;
}
