/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { JobStatus } from '../../../models/backgroundJob';
import { KeysEnum } from '../../../models/public/projectionUtils';
import { ObjectId } from "bson";

export interface Attribute {
  name: string;
  value: string;
}

export interface ExportSet {
  id: string;
  name: string;
  type: 'full';
  attributes: Attribute[];
  files: string[];
  status: JobStatus;
  created: Date;
  updated: Date;
  completedDate?: Date;
}

export enum RecordStatus {
  New = 'new',
  Updated = 'updated',
  Deleted = 'deleted',
  Existing = 'existing',
  Private = 'private',
}

type ColumnValue = string | number | string[] | undefined;

// Table type { value: { valueListMulti: ['1' ] } } ...
export type TransformedTableValue = Record<string, ColumnValue> | undefined;

export interface SgxRowData<V extends TransformedTableValue | ColumnValue = ColumnValue> {
  code: string;
  value?: V;

  numberScale?: string;
  unit?: string;
}

type ValueListRecord = Record<string, undefined | number | string>;
type ValueDataDataDefault = string | string[] | ValueListRecord;

interface ValueData<T = ValueDataDataDefault, TableData = SgxRowData> {
  data?: T;
  // Already comes converted to SGX Format
  table?: {
    row: {
      column: TableData[]
    }[]
  };
  notApplicableType?: string;
  isImported?: boolean;
}

enum UtrValueType {
  // Stored in value property
  Number = 'number', // number
  Percentage = 'percentage', // number

  // Complex types stored in valueData.data (string | string[] | Record)
  Text = 'text', // string
  Date = 'date', // string
  ValueListMulti = 'valueListMulti', // string[]
  ValueList = 'valueList', // string
  NumericValueList = 'numericValueList', // Record<string, number>
  TextValueList = 'textValueList', // Record<string, string>

  // Stored in valueData.table property
  Table = 'table',
}

interface UniversalTrackerValue<
  T extends (string | ObjectId) = ObjectId,
  TableData extends SgxRowData<TransformedTableValue> | SgxRowData = SgxRowData,
> {
  _id: T;
  /** Reference to value metadata, such as name, description, value type etc. **/
  universalTrackerId: T;

  /** Reference to reporting level node **/
  initiativeId: T;

  /** Only verified values are exported **/
  status: 'created' | 'updated' | 'rejected' | 'verified';

  valueType?: UtrValueType;

  /** number or percentage valueType is stored here **/
  value?: number;

  /** Non-numeric complex answers are stored here **/
  valueData?: ValueData<Record<string, ValueDataDataDefault | undefined>, TableData> ;

  /** Code representing measure unit, based on type like time, area, mass, volume etc. **/
  unit?: string;
  numberScale?: string;

  /** Further explanation (note) for the answer **/
  note?: string;

  /** Reference to evidence document id **/
  evidence: string[];

  /** Represent end date of the period ('monthly' | 'quarterly' | 'yearly')  **/
  effectiveDate: Date;

  lastUpdated: Date;
  created: Date;

  /**  'new' | 'updated' | 'existing' | 'deleted' **/
  recordStatus: RecordStatus;
}

export type TransformedUtrv = UniversalTrackerValue<
  ObjectId,
  SgxRowData<TransformedTableValue>
>;


export interface Survey<
  T extends (string | ObjectId) = ObjectId,
  Utrv extends UniversalTrackerValue | TransformedUtrv = UniversalTrackerValue
> {
  _id: T;
  /** Represents end date of the period of the survey **/
  effectiveDate: string;
  period: 'monthly' | 'quarterly' | 'yearly';

  name?: string

  created: Date;
  initiativeId: T;

  /** 'new' | 'updated' | 'existing' | 'deleted' **/
  recordStatus: RecordStatus;

  universalTrackerValues: Utrv[];
}


interface Initiative {
  _id: string;
  name: string;
  parentId?: string;
  surveys: Survey<ObjectId, TransformedUtrv>[]
}

export interface Organization {

  _id: string,

  /** Name of the organization that can be changed **/
  name: string

  /** Represent unique mapping to company **/
  issuerName: string;

  created: Date;

  /**
   * should contain a status to identify it is new or updated
   * Since the last report was generated, created date must be after it.
   **/
  recordStatus: RecordStatus;

  /**
   * It's possible to have nested structure, even if the most common example is
   * a single initiative that represents organization
   **/
  initiatives: Initiative[];

}

export interface UniversalTracker {
  /** Unique Id **/
  _id: string;

  /** Unique code representing a question **/
  code: string;

  /** Represent a name of the question, shorter than valueLabel **/
  name: string;

  /** Question label that is displayed as title when answering question **/
  valueLabel: string;

  /**
   * Represent a type of data we expect to receive. Represented by UtrValueType enum
   */
  valueType: UtrValueType;

  /**
   * type that represents a standard code
   **/
  type: string;

  /** Code representing measure unit, based on unit type **/
  unit?: string;

  /**
   *  Contains validation logic such as min, max
   *  Or option list for valueList types
   *  or table configuration for table valueType
   *
   *  "valueValidation" : {
   *    "table" : {
   *      "columns" : [
   *          {
   *              "code" : "org_scale_employees_male",
   *              "name" : "i. Total Number of Employees - Male",
   *              "type" : "number",
   *              "shortName" : "i. Total Number of Employees",
   *          },
   *          {
   *              "code" : "total_employees",
   *              "name" : "Total Employees",
   *              "type" : "number",
   *          },
   *        ],
   *        "validation" : {
   *            "maxRows" : 1
   *        }
   *    }
   *  }
   **/
  valueValidation?: Record<string, object>;


  /** Connections to standards and frameworks, SDG **/
  connections: { type: string, name: string, code?: string }[];
}


// Projections
export const sgxUtrv: KeysEnum<UniversalTrackerValue> = {
  _id: 1,
  universalTrackerId: 1,
  initiativeId: 1,
  status: 1,
  valueType: 1,
  value: 1,
  valueData: 1,
  unit: 1,
  numberScale: 1,
  note: 1,
  evidence: 1,
  effectiveDate: 1,
  lastUpdated: 1,
  created: 1,
  recordStatus: 1,
}
