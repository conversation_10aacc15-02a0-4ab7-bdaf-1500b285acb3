/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import Initiative, { InitiativePlain, InitiativeTags, InitiativeTypes } from '../../../models/initiative';
import { RequesterCode } from '../../organization/domainConfig';
import { ActiveDataShare, DataShareRepository, getDataShareRepository } from '../../../repository/DataShareRepository';
import { KeysEnum } from '../../../models/commonProperties';
import Survey from '../../../models/survey';
import { SgxOptions } from './ExportTypes';
import {
  ExportSet,
  Organization,
  RecordStatus,
  SgxRowData,
  sgxUtrv,
  Survey as SgxSurvey,
  TransformedTableValue,
  TransformedUtrv
} from './sgxTypes';
import { ObjectId } from 'bson';
import { ActionList, DataPeriods } from '../../utr/constants';
import UniversalTracker, { UniversalTrackerPlain } from '../../../models/universalTracker';
import { getPreferredAlternative } from '../../assurance/csvContext';
import { getGroup } from '@g17eco/core';
import {
  BlueprintContribution,
  BlueprintContributions,
  getBluePrintContribution
} from '../../survey/BlueprintContribution';
import { DefaultBlueprintCode } from '../../../survey/blueprints';
import { sdgShortNameDefinition } from '../../../static/sdgShortNameDefinition';
import { PipelineStage } from 'mongoose';
import { projectToMap } from '../../../models/public/projectionUtils';
import BackgroundJob, { JobType, TaskSgxExport, TaskStatus, TaskType } from '../../../models/backgroundJob';
import { createLog } from '../../jobs/logMessage';
import { DataScope, DataScopeAccess } from '../../../models/dataShare';
import ContextError from '../../../error/ContextError';
import { getDataShareScope } from '../../share/dataShareUtil';
import { DownloadScope } from '../../survey/scope/downloadScope';
import { filterByFramework, filterByStandard } from '../../survey/scope/filterScope';
import { BlueprintRepositoryInterface, getBlueprintRepository } from "../../../repository/BlueprintRepository";
import { extractVisibleUtrCodes } from "../../../survey/surveyForms";
import { UtrValueType } from "../../../models/public/universalTrackerType";
import { LoggerInterface, wwgLogger } from '../../wwgLogger';
import { UnitTypes } from "../../units/MetricUnitManager";
import { batchArray } from "../../../util/batch";

type SgxInitiative = Pick<InitiativePlain, '_id' | 'name' | 'parentId' | 'code' | 'created' | 'metadata'>

type SgxInitiativeWithCombinedDataShare = SgxInitiative & {
  dataScope?: DataScope
}

const initiativeProject: KeysEnum<SgxInitiative, 1 | Record<string, 1>> = {
  _id: 1,
  code: 1,
  name: 1,
  parentId: 1,
  created: 1,
  metadata: 1,
}


export type SgxUniversalTracker = Pick<UniversalTrackerPlain, '_id'
  | 'code'
  | 'typeCode'
  | 'name'
  | 'valueLabel'
  | 'valueType'
  | 'type'
  | 'unit'
  | 'unitType'
  | 'numberScale'
  | 'alternatives'
  | 'typeTags'
  | 'tags'
  | 'valueValidation'>

type SgxBaseUtr = Omit<SgxUniversalTracker, 'alternatives' | 'tags' | 'typeTags'>;

interface SgxConnection {
  type: string;
  name: string;

  code?: string;
}

export interface SgxUniversalTrackerExtended extends SgxBaseUtr {
  connections: SgxConnection[];
}

const sgxUtrProject: KeysEnum<SgxUniversalTracker, 1> = {
  _id: 1,

  code: 1,
  typeCode: 1,

  name: 1,
  valueLabel: 1,
  valueType: 1,
  type: 1,
  unit: 1,
  unitType: 1,
  numberScale: 1,

  valueValidation: 1,
  /** For text selection, removed before export **/
  alternatives: 1,
  tags: 1,
  typeTags: 1,
}

type BatchScope = { utrIds: ObjectId[], initiativeIds: ObjectId[] };
type TableColumMapper = { [k: string]: string };

export class SgxRepository {

  private readonly sgxStandardCodes = ['sgx_metrics', 'sgx_extended'];
  private sdgMap: Map<string, string>;
  private blueprintCodes: string[] = [DefaultBlueprintCode];


  constructor(
    private dataShareRepo: DataShareRepository,
    private blueprintContribution: BlueprintContribution,
    private blueprintRepo: BlueprintRepositoryInterface,
    private logger: LoggerInterface,
  ) {
    this.sdgMap = new Map<string, string>();
    sdgShortNameDefinition.forEach(goal => {
      this.sdgMap.set(goal.code, `Goal ${goal.code} - ${goal.shortName}`);
      goal.targets.forEach(target => {
        this.sdgMap.set(target.code, `Target ${target.code} - ${target.shortName}`);
      })
    })
  }

  public getLastJob() {
    return BackgroundJob.findOne({
      type: JobType.Export,
      'tasks.type': TaskType.SgxExport
    }).sort({ created: -1 }).exec();
  }

  public createSgxJob(exportJob: ExportSet) {
    return BackgroundJob.create({
      name: exportJob.name,
      type: JobType.Export,
      attributes: exportJob.attributes,
      tasks: [
        {
          id: exportJob.id,
          name: exportJob.name,
          type: TaskType.SgxExport,
          status: TaskStatus.Pending,
          data: {
            type: exportJob.type,
            files: []
          }
        } as TaskSgxExport
      ],
      logs: [createLog('Starting SGX export')]
    })
  }

  public async getRootInitiatives(options: SgxOptions): Promise<SgxInitiativeWithCombinedDataShare[]> {

    const sgxPortfolio = await Initiative
      .findOne({ code: RequesterCode.SGX }, { _id: 1 })
      .orFail()
      .lean()
      .exec();

    // Any kind of data share
    const dataShareInitiatives = await this.dataShareRepo.findActiveDataShare({
      requesterId: sgxPortfolio._id
    })

    if (dataShareInitiatives.length === 0) {
      return [];
    }

    const initiativeDataShareMap = new Map<string, ActiveDataShare[]>()
    const idSet = new Set<string>();

    dataShareInitiatives.forEach(dataShare => {
      const initiativeId = dataShare.initiativeId.toHexString();
      idSet.add(initiativeId)

      const entry = initiativeDataShareMap.get(initiativeId)
      if (entry) {
        entry.push(dataShare)
        return
      }
      initiativeDataShareMap.set(initiativeId, [dataShare]);
    })

    const filter: Record<string, unknown> = {
      _id: { $in: Array.from(idSet).map(id => new ObjectId(id)) },
      tags: {
        $eq: InitiativeTags.Organization,
        $ne: InitiativeTags.StaffOrganization
      },
      type: InitiativeTypes.Initiative,
      'metadata.sgx_issuer_name': { $exists: true, $ne: '' },
    };

    if (options.initiativeIds && options.initiativeIds.length > 0) {
      filter._id = { $in: options.initiativeIds.map(id => new ObjectId(id)) }
    }

    const initiatives = await Initiative.find(filter, initiativeProject)
      .sort({ 'metadata.sgx_issuer_name': 1 })
      .lean()
      .exec();

    return initiatives.map(initiative => {
      const dataShares = initiativeDataShareMap.get(initiative._id.toString());

      if (!dataShares || dataShares.length === 0) {
        throw new ContextError(`Trying to export SGX data for initiative without data share`, {
          initiativeId: initiative._id,
          code: initiative.code,
        })
      }

      // Not combining with potential initiative dataShare as it require explicit dataShare
      return { ...initiative, dataScope: getDataShareScope(dataShares) }
    })
  }

  async getExportData(
    options: SgxOptions,
    initiatives: SgxInitiativeWithCombinedDataShare[],
    universalTrackerData: SgxUniversalTracker[],
  ): Promise<Organization[]> {

    const scopeBatches = this.createScopeBatches(universalTrackerData, initiatives);
    const batchSurveys: SgxSurvey[] = [];

    const maxInitiativePerBatch = 50;
    const now = Date.now();
    for (const [key, scopeBatch] of scopeBatches) {
      this.logger.info(`[SGX] Fetching scope key ${key}`, {
        scopeKey: key,
        utrCount: scopeBatch.utrIds.length,
        initiativeCount: scopeBatch.initiativeIds.length,
        initiativeIds: scopeBatch.initiativeIds.map(id => String(id)),
        utrIds: scopeBatch.utrIds.map(id => String(id)),
      })

      const batches = batchArray(scopeBatch.initiativeIds, maxInitiativePerBatch);
      for (const initiativeIds of batches) {
        const surveys = await this.fetchSurveys({ utrIds: scopeBatch.utrIds, initiativeIds }, options);
        batchSurveys.push(...surveys)
      }
    }

    const utrTableMap = new Map<string, TableColumMapper>()
    universalTrackerData.forEach(utr => {
      if (utr.valueType === UtrValueType.Table && utr.valueValidation?.table?.columns) {
        utrTableMap.set(utr._id.toString(), utr.valueValidation.table.columns.reduce((acc, col) => {
          acc[col.code] = col.type;
          return acc;
        }, {} as TableColumMapper));
      }
    })

    this.logger.info(`[SGX] Complete all scopes fetching in ${Date.now() - now}ms`, {
      duration: Date.now() - now,
    });


    return initiatives.map((initiative) => {

      return {
        _id: initiative._id.toHexString(),
        name: initiative.name,
        issuerName: initiative.metadata?.sgx_issuer_name ?? '',
        created: initiative.created,
        recordStatus: initiative.created > options.previousRun ? RecordStatus.New : RecordStatus.Existing,
        initiatives: [initiative].map(innerInitiative => {

          const id = innerInitiative._id.toHexString();
          return {
            _id: id,
            name: innerInitiative.name,
            parentId: innerInitiative.parentId ? String(innerInitiative.parentId) : undefined,
            surveys: batchSurveys.filter((s) => String(s.initiativeId) === id).map(survey => {

              survey.universalTrackerValues.forEach(utrv => {
                if (!utrv.valueData?.table?.row || utrv.valueData.table.row.length === 0) {
                  return;
                }
                const utrId = utrv.universalTrackerId.toString();
                const tableUtrv = utrTableMap.get(utrId)
                if (!tableUtrv) {
                  if (utrv.valueType === UtrValueType.Table) {
                    this.logger.error(new ContextError(`[SGX] Export utrv table is missing utr for table, skipping...`, {
                      utrId,
                      utrvId: utrv._id.toString(),
                    }))
                  }
                  return;
                }

                // Casting, avoiding re-creating data for memory usage
                const v = utrv as TransformedUtrv;
                if (!v.valueData?.table) {
                  return v; // As we convert the type, and losing the checks
                }

                v.valueData.table.row = utrv.valueData.table.row.map(row => {

                  const transformedColumns: SgxRowData<TransformedTableValue>[] = [];
                  row.column.forEach(column => {

                    const columnValueType = tableUtrv[column.code];
                    if (!columnValueType) {
                      this.logger.warn(new ContextError(`[SGX] Export utrv table column ${column.code} is missing definition file in utr, skipping...`, {
                        utrId,
                        utrvId: utrv._id.toString(),
                        columnCode: column.code,
                      }))
                      return;
                    }

                    transformedColumns.push({
                      ...column,
                      value: {
                        [columnValueType]: column.value ?? '',
                      }
                    })
                  })

                  return { column: transformedColumns };
                })
              })

              return survey as SgxSurvey<ObjectId, TransformedUtrv>
            })
          }
        })
      }
    })
  }

  private createScopeBatches(universalTrackerData: SgxUniversalTracker[], initiatives: SgxInitiativeWithCombinedDataShare[]) {
    const initialData = new Map<string, BatchScope>();
    const noRestriction: BatchScope = { utrIds: universalTrackerData.map(u => u._id), initiativeIds: [] };
    initialData.set('all', noRestriction);

    return initiatives.reduce((acc, initiative) => {

      const initiativeId = initiative._id;

      const scope = initiative.dataScope?.survey?.scope;
      if (!scope) {
        noRestriction.initiativeIds.push(initiativeId)
        return acc;
      }

      const { standards = [], frameworks = [] } = scope;
      if (standards.length === 0 && frameworks.length === 0) {
        noRestriction.initiativeIds.push(initiativeId)
        return acc;
      }

      // Ensure keys are sorted
      const key = (standards).concat(frameworks).sort().join('|')
      const entry = acc.get(key);
      if (entry) {
        entry.initiativeIds.push(initiativeId)
        return acc;
      }

      // Filter by standard and framework for scope batch
      const utrIds = universalTrackerData.filter(utr => {
        if (standards.length > 0 && filterByStandard(standards, utr.type, utr.typeTags, utr.alternatives)) {
          return true;
        }
        return frameworks.length > 0 ? filterByFramework(frameworks, utr.tags) : false;
      }).map(u => u._id);

      acc.set(key, { initiativeIds: [initiativeId], utrIds })
      return acc;
    }, initialData);
  }

  private async fetchSurveys({ utrIds, initiativeIds }: BatchScope, options: SgxOptions): Promise<SgxSurvey[]> {
    const previousRun = options.previousRun;

    const $match: Record<string, unknown> = {
      $expr: { $in: ["$_id", "$$ids"] },
      status: ActionList.Verified,
      $or: [
        { deletedDate: { $exists: false } },
        { deletedDate: { $gte: previousRun } }
      ],
    }

    if (utrIds.length > 0) {
      $match.universalTrackerId = { $in: utrIds }
    }

    const removeDataCondition = {
      $or: [
        { $eq: ["$$utrv.isPrivate", true] },
        { $ne: ["$$utrv.status", ActionList.Verified] }
      ]
    };


    const arrayToObj = (type: UtrValueType) => {
      return {
        case: { $eq: ["$$utrv.valueType", type] },
        then: {
          $arrayToObject: [
            [{ k: "$$utrv.valueType", v: "$$utrv.valueData.data" }]
          ]
        }
      }
    }

    const aggregate: PipelineStage[] = [
      {
        $match: {
          $and: [
            {
              $or: [
                { deletedDate: { $exists: false } },
                { deletedDate: { $gte: previousRun } }
              ]
            }
          ],
          period: { $eq: DataPeriods.Yearly },
          completedDate: { $exists: true },
          initiativeId: { $in: initiativeIds },
        }
      },
      {
        $lookup: {
          from: 'universal-tracker-values',
          let: {
            ids: { $concatArrays: ['$visibleUtrvs', '$disabledUtrvs'] },
          },
          pipeline: [
            {
              $match: $match
            }
          ],
          as: 'universalTrackerValues'
        }
      },
      {
        $project: {
          _id: 1,
          effectiveDate: 1,
          name: 1,
          period: 1,
          created: 1,
          initiativeId: 1,
          recordStatus: {
            $switch: {
              branches: [
                { case: { $gte: ["$deletedDate", previousRun] }, then: RecordStatus.Deleted },
                { case: { $gte: ["$created", previousRun] }, then: RecordStatus.New },
                { case: { $gte: ["$completedDate", previousRun] }, then: RecordStatus.Updated },
              ],
              default: RecordStatus.Existing
            },
          },
          universalTrackerValues: projectToMap({
            input: '$universalTrackerValues',
            projection: sgxUtrv,
            as: 'utrv',
            override: {
              recordStatus: {
                $switch: {
                  branches: [
                    { case: { $ne: ["$$utrv.status", ActionList.Verified] }, then: RecordStatus.Deleted },
                    { case: { $gte: ["$$utrv.deletedDate", previousRun] }, then: RecordStatus.Deleted },
                    { case: { $eq: ["$$utrv.isPrivate", true] }, then: RecordStatus.Private },
                    { case: { $gte: ["$$utrv.created", previousRun] }, then: RecordStatus.New },
                    { case: { $gte: ["$$utrv.lastUpdated", previousRun] }, then: RecordStatus.Updated },
                  ],
                  default: RecordStatus.Existing
                },
              },
              value: {
                $cond: [removeDataCondition, '$$REMOVE', '$$utrv.value'],
              },
              note: {
                $cond: [removeDataCondition, '$$REMOVE', '$$utrv.note'],
              },
              valueData: {
                $cond: [
                  removeDataCondition,
                  '$$REMOVE',
                  {
                    data: {
                      $switch: {
                        branches: [
                          {
                            case: {
                              // $ifNull treats undefined values and missing fields as null.
                              $eq: [{ $ifNull: ["$$utrv.valueData.data", null] }, null]
                            },
                            then: "$$REMOVE",
                          },
                          arrayToObj(UtrValueType.Text),
                          arrayToObj(UtrValueType.Date),
                          arrayToObj(UtrValueType.ValueList),
                          arrayToObj(UtrValueType.ValueListMulti),
                          arrayToObj(UtrValueType.TextValueList),
                          arrayToObj(UtrValueType.NumericValueList),
                        ],
                        default: '$$REMOVE'
                      },
                    },
                    isImported: "$$utrv.valueData.isImported",
                    notApplicableType: "$$utrv.valueData.notApplicableType",
                    table: {
                      $switch: {
                        branches: [
                          {
                            case: { $eq: ["$$utrv.valueType", UtrValueType.Table] },
                            then: {
                              $reduce: {
                                input: "$$utrv.valueData.table",
                                initialValue: { row: [] },
                                in: {
                                  row: { $concatArrays: ["$$value.row", [{ column: '$$this' }]] }
                                }
                              }
                            }
                          },
                        ],
                        default: '$$REMOVE'
                      },
                    }
                  }
                ],
              }
            }
          }),
        }
      }
    ];

    return Survey.aggregate(aggregate).exec();
  }

  public async convertToExportUtrs(utrs: SgxUniversalTracker[]): Promise<SgxUniversalTrackerExtended[]> {
    const contribution = await this.blueprintContribution.getContributions(DefaultBlueprintCode)

    return utrs.map(u => {

      if (u.valueValidation) {

        const valueList = u.valueValidation.valueList;
        if (valueList) {
          u.valueValidation.valueList = {
            type: valueList.type,
            listId: valueList.listId,
            allowCustomOptions: valueList.allowCustomOptions,
            custom: valueList.custom,
          }
        }

        if (u.valueValidation.table) {
          u.valueValidation.table = {
            validation: u.valueValidation.table.validation,
            columns: u.valueValidation.table.columns.map(c => {
              return {
                code: c.code,
                type: c.type,
                name: c.name,
                shortName: c.shortName,
                unitType: c.unitType,
                unit: c.unit,
                numberScale: c.numberScale,
                options: c.options,
                listId: c.listId,
                validation: c.validation
              }
            })
          }
        }
      }

      const { name, type, valueLabel, typeCode } = getPreferredAlternative(u, this.sgxStandardCodes)

      return {
        _id: u._id,
        code: u.code,
        // Override text with preferred types
        typeCode,
        name,
        type,
        valueLabel,
        valueType: u.valueType,
        // Do not want to send the currency (or could override to SGD)
        unit: u.unitType === UnitTypes.currency ? undefined : u.unit,
        unitType: u.unitType,
        numberScale: u.numberScale,
        valueValidation: u.valueValidation,
        connections: this.generateConnections(u, contribution)
      }
    })
  }

  /** Full utrs that needs to be stripped down before export **/
  public async getSgxUniversalTrackers(): Promise<SgxUniversalTracker[]> {
    // We only want to expose these for now
    const match = await DownloadScope.generateMultiScopeMatch({
      access: DataScopeAccess.Partial,
      scope: {
        standards: [
          'sgx_metrics',
          'sgx_extended',
          // 'tcfd_standard' // not yet ready
        ],
        frameworks: ['tcfd', 'ctl']
      }
    }, '') ?? {};

    const utrCodeSet = new Set<string>();
    for (const blueprintCode of this.blueprintCodes) {
      const blueprint = await this.blueprintRepo.getBlueprint(blueprintCode);
      if (blueprint) {
        extractVisibleUtrCodes(blueprint).forEach(code => utrCodeSet.add(code))
      }
    }

    return UniversalTracker.find({
      ...match,
      code: { $in: Array.from(utrCodeSet) },
    }, sgxUtrProject).lean().exec();
  }

  private generateConnections(u: SgxUniversalTracker, contributions: BlueprintContributions): SgxConnection[] {

    const connections: SgxConnection[] = [];

    this.blueprintContribution.convertToSDGCodes(contributions[u.code]).forEach((sdgCode) => {
      connections.push({ code: sdgCode, type: 'sdg', name: this.sdgMap.get(sdgCode) ?? sdgCode });
    })

    const standardTypes = new Map<string, SgxConnection>()
    if (u.alternatives) {
      Object.entries(u.alternatives).forEach(([type, alternative]) => {
        // Exclude sgxStandardCodes as those would have been updated to be the main type
        const group = getGroup('standards', type);
        if (group) {
          standardTypes.set(type, { type, name: group.name, code: alternative.typeCode })
        }
      })

      // We will override as SGX type, add the original type as connection
      if (!standardTypes.has(u.type)) {
        const group = getGroup('standards', u.type);
        if (group) {
          standardTypes.set(u.type, { type: u.type, name: group.name, code: u.typeCode })
        }
      }
    }

    connections.push(...Array.from(standardTypes.values()));

    if (u.tags) {
      Object.entries(u.tags).forEach(([type, tags]) => {
        if (tags.length !== 0) {
          const group = getGroup('frameworks', type);
          if (group) {
            const subgroups = (group.subgroups ?? []).filter(g => tags.includes(g.code))
            if (subgroups.length > 0) {
              subgroups.forEach(subGroup => {
                connections.push({ type, name: group.name, code: subGroup.code })
              })
            } else {
              // Probably should not happen unless subgroup was removed?
              connections.push({ type, name: group.name })
            }
          }
        }
      })
    }

    return connections;
  }
}

let instance: SgxRepository;
export const getSgxRepository = () => {
  if (!instance) {
    instance = new SgxRepository(
      getDataShareRepository(),
      getBluePrintContribution(),
      getBlueprintRepository(),
      wwgLogger,
    );
  }
  return instance;
}
