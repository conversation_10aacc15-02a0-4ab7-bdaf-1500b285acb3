/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { convertableToString, Parser, processors } from 'xml2js';
import SgxIssuer, { SgxIssuerModel, SgxIssuerPlain } from '../../../../models/sgxIssuer';
import fs from 'fs';
import ContextError from '../../../../error/ContextError';
import { snakeToCamel } from '../../../../util/string';
import { ObjectId } from "bson";


interface IssuerFile {
  root: {
    issuer: SgxIssuerModel[];
  }
}

export class IssuerSync {
  xmlParser: Parser;

  constructor() {
    this.xmlParser = new Parser({
      tagNameProcessors: [
        processors.normalize,
        snakeToCamel
      ],
      explicitArray: false,
    });
  }

  public async parseFile(filePath: string) {
    const content = await fs.promises.readFile(filePath);
    return this.parse(content)
  }

  public async parse(str: convertableToString): Promise<SgxIssuerPlain[]> {

    const data = await new Promise<IssuerFile>((resolve, reject) => {
      this.xmlParser.parseString(str, (err: Error | null, result: IssuerFile) => {
        err ? reject(err) : resolve(result)
      })
    });

    if (!Array.isArray(data?.root?.issuer)) {
      throw new ContextError(`Failed to parse issuer file`)
    }

    return data.root.issuer
  }

  public async sync(issuers: SgxIssuerPlain[]) {

    const all = await SgxIssuer.find({}, { issuerName: 1 }).lean().exec();

    const existingNames = new Map(all.map(issuer => [issuer.issuerName?.name ?? '', issuer._id]))

    // Track which issuer names we are updating, so we only do it once.
    const issuerNamesToUpdate = new Set<string>();
    const idsToRemove = new Set(all.map(i => i._id.toString()));

    const newEntries: unknown[] = [];

    const batchUpdates = [];
    for (const issuerUpdate of issuers) {
      const name = issuerUpdate.issuerName?.name ?? '';

      if (issuerNamesToUpdate.has(name)) {
        continue; // Must be processing a duplicate, will get removed later
      }

      const id = existingNames.get(name);
      if (!id) {
        newEntries.push(issuerUpdate);
        continue;
      }

      batchUpdates.push({
        replaceOne: {
          filter: { _id: id },
          replacement: issuerUpdate,
        }
      });

      issuerNamesToUpdate.add(name);
      idsToRemove.delete(String(id))
    }

    await SgxIssuer.create(newEntries);
    const updateResult = await SgxIssuer.bulkWrite(batchUpdates)


    const idsToDelete = Array.from(idsToRemove).map(id => new ObjectId(id));
    const r = await SgxIssuer.deleteMany({ _id: { $in: idsToDelete  } }).exec();

    return {
      existingCount: all.length,
      fileCount: issuers.length,
      created: newEntries.length,
      updated: updateResult.modifiedCount,
      deleted: r.deletedCount,
    };
  }
}

let instance: IssuerSync;
export const getIssuerSync = () => {
  if (!instance) {
    instance = new IssuerSync();
  }
  return instance;
}
