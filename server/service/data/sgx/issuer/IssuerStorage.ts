/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { Storage, Bucket } from '@google-cloud/storage';
import config from '../../../../config';
import { getGoogleCloudCredentials } from "../../../google-cloud/credentials";

interface Options {
  bucketName: string;
  filePath: string;
}

export class IssuerStorage {

  private readonly bucket: Bucket;
  private readonly filePath: string;
  private readonly baseUrl: string;


  constructor(options: Options) {
    this.filePath = options.filePath;
    this.baseUrl = config.googleCloud.storage.baseUrl
    this.bucket = this.getStorage().bucket(options.bucketName)
  }

  private getStorage() {
    return new Storage(getGoogleCloudCredentials());
  }

  /**
   * Download last issuer file from the storage
   */
  public async getIssuerFile(): Promise<Buffer> {
    const [fileBuffer] = await this.bucket.file(this.filePath).download();
    return fileBuffer
  }

  public async getFile() {
    return this.bucket.file(this.filePath);
  }

  public async uploadFile(filepath: string) {

    const file = await this.getFile();
    // Move to back up
    await file.copy(`${this.filePath}.previous`);

    return file.bucket.upload(filepath, {
      destination: this.filePath,
      metadata: {
        originalFile: filepath,
      }
    });
  }
}

let instance: IssuerStorage;
export const getIssuerStorage = () => {
  if (!instance) {
    const { bucketName, filePath } = config.sgx.issuer;
    instance = new IssuerStorage({ bucketName, filePath });
  }
  return instance;
}
