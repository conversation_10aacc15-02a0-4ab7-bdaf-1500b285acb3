/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { getIssuerSync, IssuerSync } from './IssuerSync';
import { getIssuerStorage, IssuerStorage } from './IssuerStorage';
import { LoggerInterface, wwgLogger } from '../../../wwgLogger';
import ContextError from '../../../../error/ContextError';
import { getIssuerFileUpdater, IssuerFileUpdater } from "./IssuerFileUpdater";
import { removeFile } from "../../../file/filesystem";

const supportedTypes = ['syncToLocal', 'syncSFTPtoRemote'] as const;
export type SupportedType = typeof supportedTypes[number];

export class SgxIssuerManager {

  constructor(
    private issuerSync: IssuerSync,
    private storage: IssuerStorage,
    private issuerFileUpdater: IssuerFileUpdater,
    private logger: LoggerInterface,
  ) {
  }

  processTask(taskData: { type: SupportedType }) {
    const type = taskData.type;
    switch (type) {
      case 'syncToLocal':
        return this.syncToLocal();
      case 'syncSFTPtoRemote':
        return this.syncSFTPtoRemote();
      default:
        throw new ContextError(`Unsupported issuer task type "${type}"`, {
          type,
          supportedTypes,
        })
    }
  }

  public async syncToLocal() {

    this.logger.info('[SGX] starting issuer sync process to local database')
    const file = await this.storage.getIssuerFile();

    this.logger.info('[SGX] Parsing issuer file')
    const entries = await this.issuerSync.parse(file)
    this.logger.info(`[SGX] Parsed issuer file, found ${entries.length} issuers`)

    const result = await this.issuerSync.sync(entries);
    this.logger.info(`[SGX] Completed sync to local`, result)

    return result;
  }

  private async syncSFTPtoRemote() {
    try {
      await this.issuerFileUpdater.connect();

      this.logger.info('[SGX] starting issuer SFTP sync process to remote storage')
      const result = await this.issuerFileUpdater.getLatestFile();
      const localPath = `/tmp/${result.name}`;
      const downloadResult = await this.issuerFileUpdater.getFile(result.remotePath, localPath)
      this.logger.info(`Downloaded latest available SGX issuer file to local path ${localPath}`, {
        downloadResult
      });

      const [file, metadata] = await this.storage.uploadFile(localPath);

      const combinedResult = {
        localPath,
        name: result.name,
        latestFile: result.latestFile,
        latestFileModified: result.modified,
        remoteFileName: file.name,
        metadata,
      };

      this.logger.info(`[SGX] Completed SFTP sync to remote`, combinedResult)
      // Clean local files
      removeFile(localPath);

      return combinedResult;
    } finally {
      await this.issuerFileUpdater.disconnect();
    }
  }
}

let instance: SgxIssuerManager;
export const getSgxIssuerManager = () => {
  if (!instance) {
    instance = new SgxIssuerManager(
      getIssuerSync(),
      getIssuerStorage(),
      getIssuerFileUpdater(),
      wwgLogger,
    );
  }
  return instance;
}
