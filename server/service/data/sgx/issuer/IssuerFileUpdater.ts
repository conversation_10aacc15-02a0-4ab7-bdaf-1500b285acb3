/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { getSFTPClient, SFTPClient } from "../../../sftp/SftpClient";
import config from "../../../../config";
import { LoggerInterface, wwgLogger } from "../../../wwgLogger";
import ContextError from "../../../../error/ContextError";
import { join } from 'path'

interface SFTPConnection {
  privateKey: string;
  port: string;
  host: string;
  userId: string
}

/**
 * Sync issuer file with local collection
 *
 * ## Cron - Issuer downloader
 * * Load credentials
 * * look up the issuer file in SFTP
 * * download to the server
 *
 * ## integrations/sgx/issuer
 * * upload process to Google Storage
 * * Check what we have in google bucket
 * * download from SGX what is missing
 * * figure out which the latest one, use that file to update the storage
 * * Sync issuer file with local collection
 * * SGX Export sss
 *
 * ## Check with IssuerName
 *
 * * Generate error in slack/email
 * * Manual inspection
 * * Skip the data upload.
 * * Allow to disable issuerName validation (for staging testing etc.)
 */
export class IssuerFileUpdater {

  /** SGX issuer inbox directory **/
  private readonly issuerFileDir = '/tmd_mffissuer/inbox';

  constructor(
    private logger: LoggerInterface,
    private sftpClient: SFTPClient,
  ) {
  }

  public async getLatestFile() {
    const files = await this.sftpClient.list(this.issuerFileDir)

    if (files.length === 0) {
      const { host, port } = config.sgx.issuer.sftp;
      throw new ContextError(`Failed to valid issuer files in the issuer directory ${this.issuerFileDir}`, {
        dir: this.issuerFileDir,
        host,
        port,
      })
    }

    // Sort files to ensure first one is the latest one
    files.sort((a, b) => b.name.localeCompare(a.name))

    // pattern MFF_ISR_FILE_20221026.xml, we have at least one file at this point
    const [latestFile] = files;

    const remotePath = join(this.issuerFileDir, latestFile.name);
    this.logger.info(`Found latest available SGX issuer file ${latestFile.name}`, { remotePath });

    return {
      latestFile,
      remotePath,
      name: latestFile.name,
      modified: new Date(latestFile.modifyTime),
    }
  }

  public async connect() {
    const sftp = config.sgx.issuer.sftp;
    if (!this.canConnect(sftp)) {
      throw new ContextError("No SFTP details found. Could not sync data from SGX SFTP", {
        host: sftp.host ?? '',
        port: sftp.port ?? '',
        userId: sftp.userId ?? '',
        privateKey: sftp.privateKey ? 'available' : 'missing',
      })
    }
    const { host, port, userId, privateKey } = sftp;

    await this.sftpClient.connect(host, Number(port), userId, privateKey)
  }


  public async disconnect() {
    await this.sftpClient.disconnect().catch(e => this.logger.error(e));
  }

  private canConnect(sftp: Partial<SFTPConnection>): sftp is SFTPConnection {
    return Boolean(sftp.host && sftp.port && sftp.privateKey && sftp.userId)
  }

  getFile(path: string, destination: string) {
    return this.sftpClient.fastGet(path, destination, { concurrency: 1 })
  }
}

let instance: IssuerFileUpdater;
export const getIssuerFileUpdater = () => {
  if (!instance) {
    instance = new IssuerFileUpdater(
      wwgLogger,
      getSFTPClient(),
    );
  }
  return instance;
}
