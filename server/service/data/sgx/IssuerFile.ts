/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


export interface Root {
  ISSUER: XmlIssuer[];
}

export interface XmlIssuer {
  ISSUER_NAME:                  IssuerName;
  IS_LISTED:                    YesNo;
  COUNTRY_OF_INCORPORATION:     string;
  DATE_OF_INCORPORATION:        Date;
  COMPANY_REG_NO:               string;
  PRINCIPAL_PLACE_OF_BUSINESS:  string;
  DESIGNATED_MARKET_MAKER_FLAG: YesNo;
  SUPER_PARENT_FLAG:            YesNo;
  CORPORATE_EMAIL_ADDRESS?:     string;
  WEBSITE?:                     string;
  BUSINESS_DESCRIPTION:         string;
  LOCAL_FOREIGN_FLAG:           YesNo;
  ISSUER_RELATED_PERSON:        IssuerRelated<PERSON>erson[];
  ISSUER_PARTICIPATING_ENTITY:  IssuerParticipatingEntity[];
  ISSUER_ADDRESS:               IssuerAddress[];
  ISSUER_COMMENT:               ISSUERCOMMENTElement[] | ISSUERCOMMENTElement;
  RECORD_STATUS:                string;
}

export enum YesNo {
  N = "N",
  Y = "Y",
}

export interface IssuerAddress {
  ISSUER_ADDRESS_TYPE: string;
  ISSUER_ADDRESS_1:    string;
  ISSUER_ADDRESS_2?:   string;
  ISSUER_ADDRESS_3?:   string;
  ISSUER_PHONE?:       string;
  ISSUER_FAX?:         string;
  ISSUER_ADDRESS_4?:   string;
}

export interface ISSUERCOMMENTElement {
  COMMENT_TYPE?: string;
  COMMENT_DATE:  Date;
  COMMENT_TIME:  string;
  COMMENT_TEXT?: string;
}

export interface IssuerName {
  LANGUAGE: string;
  NAME:     string;
}

export interface IssuerParticipatingEntity {
  ENTITY_TYPE:    string;
  ENTITY_NAME:    string;
  ENTITY_ADDRESS: EntityAddress;
}

export interface EntityAddress {
  ENTITY_ADDRESS_TYPE: string;
  ENTITY_ADDRESS_1?:   string;
  ENTITY_ADDRESS_2?:   string;
}

export interface IssuerRelatedPerson {
  RELATED_PERSON_NAME:        string;
  DESIGNATION?:               string;
  IS_BOARD_MEMBER?:           YesNo;
  RELATED_PERSON_RANKING?:    string;
  EFFECTIVE_START_DATE?:      Date;
  EFFECTIVE_END_DATE?:        Date;
  IS_AUDIT_COMMITTEE_MEMBER?: YesNo;
}
