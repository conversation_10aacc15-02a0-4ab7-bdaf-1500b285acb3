import { UserModel } from '../../models/user';
import Initiative, { InitiativeModel } from '../../models/initiative';
import { InitiativeTypes } from '../../models/initiative';
import MetricGroup, { MetricGroupModel } from '../../models/metricGroup';
import { getSimpleCountryByCode } from '../location/CountryService';
import { PortfolioService } from '../portfolio/PortfolioService';
import { LIST_OF_BANKS } from './constants';
import {
  DataScopeAccess,
  DataShareCreate,
  DataShareModel,
  DataShareUpdate,
  RequesterType,
  getScopeViews,
} from '../../models/dataShare';
import { DataShareService, getDataShareService } from '../share/DataShareService';
import { getRequesterService } from '../share/RequesterService';
import { Types } from 'mongoose';
import { MetricGroupManager } from '../metric/MetricGroupManager';
import { DataShareRepository, getDataShareRepository } from '../../repository/DataShareRepository';
import { getDataShareScope } from '../share/dataShareUtil';
import { SurveyManager, getSurveyManager } from '../survey/SurveyManager';
import { Actions } from '../action/Actions';
import { SurveyRepository } from '../../repository/SurveyRepository';
import { RequestScope } from '../survey/model/DelegationScope';
import { getDuration } from '../../util/date';
import { SurveyModelPlain } from '../../models/survey';
import { wwgLogger } from '../wwgLogger';
import { DataShareMapper } from '../referral/referralCodesMap';
import { ObjectId } from 'bson';
import { BankingCodeService } from "./BankingCodeService";
import { CustomScope } from "../../models/customScopeSchema";
import { Bank, BankType, Country, BankingSetting } from '../../types/banking';

// 5 seconds for updating a survey scopes
const ESTIMATION_SURVEY_UPDATING_TIME = 5;

interface AddAssignedMetrics {
  user: UserModel;
  initiativeId: Types.ObjectId;
  domain: string | undefined;
  bankCode: string;
}

interface SurveyUpdates {
  user: UserModel;
  domain: string | undefined;
  scopeGroups: RequestScope[];
  notCompletedSurveys: Pick<SurveyModelPlain, '_id'>[];
}

export class BankingService {
  constructor(
    private dataShareRepository: DataShareRepository,
    private surveyManager: SurveyManager,
    private dataShareService: DataShareService
  ) { }

  public static getCountries(codes: string[] | undefined): Country[] {
    return (codes ?? []).map(getSimpleCountryByCode).filter((item) => !!item) as Country[];
  }

  public static getListOfBanks(): Bank[] {
    return LIST_OF_BANKS.map((b) => ({
      ...b,
      countries: this.getCountries(b.countryCodes),
      ...(b.popularCountryCodes && { popularCountries: this.getCountries(b.popularCountryCodes) }),
    }));
  }

  public static getBanks(bankingSettings: BankingSetting[] | undefined): Bank[] {
    const manualBanks = (bankingSettings ?? []).filter((item) => item.type === BankType.ManualBank);
    const absCodes = (bankingSettings ?? [])
      .filter((item) => item.type !== BankType.ManualBank)
      .map((item) => item.code);
    return [...manualBanks, ...LIST_OF_BANKS.filter((b) => absCodes.includes(b.code))];
  }

  // Add the data-share between the company and the PT
  private async addDataShare(initiativeId: ObjectId, dataShare: DataShareMapper) {
    const portfolio = await Initiative.findOne({
      code: dataShare.requesterCode
    }).exec();
    if (!portfolio) {
      return;
    }

    const requesterId = portfolio._id;
    const requesterType = dataShare.requesterType;

    const activeDataShares = await this.dataShareRepository.findRawActiveDataShare({
      initiativeId,
      requesterId,
      requesterType,
    });

    if (!activeDataShares.length) {
      return this.createDataShare(initiativeId, portfolio, dataShare);
    }

    return this.updateDataShare(dataShare, activeDataShares);
  }

  private async createDataShare(initiativeId: ObjectId, portfolio: Pick<InitiativeModel, '_id'>, dataShare: DataShareMapper) {

    const requesterId = portfolio._id;
    const requesterType = dataShare.requesterType;

    const metricGroupIdsToAdd = dataShare.dataScope?.survey?.scope?.custom?.map(s => s._id) ?? [];
    const requester = await getRequesterService().getRequesterById({ requesterId, requesterType });

    const createDataShare: DataShareCreate = {
      title: `Data share request from ${requester.name}`,
      initiativeId,
      requesterId: requester._id,
      requesterType: requester.type,
      dataScope: {
        survey: {
          scope: {
            custom: metricGroupIdsToAdd,
          },
          views: getScopeViews(),
          access: DataScopeAccess.Partial
        },
      },
      acceptedDate: new Date(),
    };

    return this.dataShareService.createRequest(createDataShare);
  }

  private async updateDataShare(dataShare: DataShareMapper, activeDataShares: DataShareModel[]) {
    const dataScope = getDataShareScope(activeDataShares)?.survey?.scope;
    const metricGroupIdsToAdd = dataShare.dataScope?.survey?.scope?.custom?.map(s => s._id) ?? [];

    // ignore when assigned metrics not change
    if (dataScope?.custom) {
      const ids = new Set(dataScope.custom.map(String));
      if (metricGroupIdsToAdd.every(id => ids.has(String(id)))) {
        return true;
      }
    }

    for (const activeDataShare of activeDataShares) {
      if (!this.dataShareService.isRestricted(activeDataShare)) {
        const updateDataScope: DataShareUpdate['dataScope'] = {
          survey: {
            scope: {
              ...dataScope,
              custom: metricGroupIdsToAdd,
            },
            views: getScopeViews(),
          },
        };
        await this.dataShareService.updateRequest(activeDataShare, updateDataScope);
      }
    }
  }

  public async addWorkflow(initiativeId: string, bankCode: string) {
    const existingPortfolio = await Initiative.findOne({ code: bankCode, type: InitiativeTypes.Group });

    if (!existingPortfolio) {
      return { assignedMetricGroupIds: [] };
    }

    // add company to PT company (bank) with no-weight
    const { existingHolding } = await PortfolioService.getExistingHolding({
      portfolio: existingPortfolio,
      holdingId: initiativeId,
    });

    if (!existingHolding) {
      await PortfolioService.addHoldingToGroup({
        portfolio: existingPortfolio,
        holdingId: initiativeId,
        weight: 0,
      });
    }

    // Fetch all existing assigned metrics from PT company (bank)
    const bankAssignedMetricGroups: MetricGroupModel[] = await MetricGroup.find({ initiativeId: existingPortfolio._id });
    const bankCustomScope: CustomScope[] = bankAssignedMetricGroups.map((group) => ({
      scopeType: 'custom',
      code: group._id.toString(),
      required: true
    }));
    const bankDataShare = BankingCodeService.getBankDataShare(existingPortfolio.name, existingPortfolio.code, bankCustomScope);


    // Fetch all assigned metrics from referral config (which will include the ABS metrics)
    const referralConditions = await BankingCodeService.getPortfolioCodeConfig(existingPortfolio.code);
    const referralDataShare = referralConditions?.dataShareMappers ?? [];


    // Assign company to all the relevant assigned metric groups
    const scopesToAdd: CustomScope[] = [
      ...(referralConditions?.scope ?? []), // Will contain ABS scope
      ...bankCustomScope // Will contain Bank scope
    ].filter(s => s.scopeType === 'custom');
    const assignedMetricGroups = await MetricGroup.find({
      _id: { $in: scopesToAdd.map(scope => new ObjectId(scope.code)) } // For scopeType='custom', then code = metricGroupId
    });
    await MetricGroupManager.shareGroups(assignedMetricGroups, { _id: new ObjectId(initiativeId) });


    // Add the data-share between the company and the PT
    const dataSharesToAdd = [
      ...referralDataShare, // Will contain data-shares of ABS metrics to both Bank and ABS
      bankDataShare // Will contain data-shares of Bank metrics to only Bank
    ].reduce(this.consolidateDataShare, []);
    await Promise.all(dataSharesToAdd.map(dataShare => this.addDataShare(new ObjectId(initiativeId), dataShare)));


    return {
      portfolio: {
        _id: existingPortfolio._id,
        name: existingPortfolio.name,
      },
      assignedMetricGroupIds: assignedMetricGroups.map((group) => group._id),
    };
  }

  private consolidateDataShare(dataShares: DataShareMapper[], dataShare: DataShareMapper): DataShareMapper[] {
    const currentRequestor = dataShares.find(ds => ds.requesterCode === dataShare.requesterCode);
    if (!currentRequestor) {
      // Not in current array, so just append and return
      return [
        ...dataShares,
        dataShare
      ];
    }

    const uniqueCustomAssignedIds = new Set(currentRequestor.dataScope?.survey?.scope?.custom?.map(String) ?? []);
    dataShare.dataScope?.survey?.scope?.custom?.forEach(id => uniqueCustomAssignedIds.add(String(id)));

    if (!currentRequestor.dataScope) {
      currentRequestor.dataScope = {};
    }
    if (!currentRequestor.dataScope.survey) {
      currentRequestor.dataScope.survey = {};
    }
    if (!currentRequestor.dataScope.survey.scope) {
      currentRequestor.dataScope.survey.scope = {};
    }
    currentRequestor.dataScope.survey.scope.custom = Array.from(uniqueCustomAssignedIds.values()).map(id => new ObjectId(id));

    return dataShares;
  }

  public async removeWorkflow(initiativeId: string, bankCode: string) {
    const existingPortfolio = await Initiative.findOne(
      { code: bankCode, type: InitiativeTypes.Group },
      { _id: 1, code: 1, name: 1, initiativeGroupId: 1 }
    );

    if (!existingPortfolio) {
      return;
    }

    // delete data share
    const activeDataShares = await this.dataShareRepository.findRawActiveDataShare({
      initiativeId,
      requesterId: existingPortfolio._id,
      requesterType: RequesterType.Portfolio,
    });

    for (const dataShare of activeDataShares) {
      if (!this.dataShareService.isRestricted(dataShare)) {
        await this.dataShareService.deleteRequest(dataShare, false);
      }
    }

    // delete assigned metrics share
    const assignedMetrics: MetricGroupModel[] = await MetricGroup.find({ initiativeId: existingPortfolio._id });

    for (const metricGroup of assignedMetrics) {
      metricGroup.updated = new Date();
      metricGroup.share = metricGroup.share.filter((s) => String(s.initiativeId) !== initiativeId);
      await metricGroup.save();
    }

    return existingPortfolio;
  }

  private async startSurveyUpdates({ user, domain, scopeGroups, notCompletedSurveys }: SurveyUpdates) {
    for (const survey of notCompletedSurveys) {
      await this.surveyManager
        .updateSurveyScope({
          delegator: user,
          surveyId: survey._id,
          action: Actions.Add,
          scopeGroups: scopeGroups,
          domain,
        })
        .catch((e) => wwgLogger.error(e));
    }
  }

  public async addAssignedMetrics({ user, initiativeId, domain, bankCode }: AddAssignedMetrics) {
    const existingPortfolio = await Initiative.findOne({ code: bankCode, type: InitiativeTypes.Group }).orFail().exec();
    const assignedMetrics: MetricGroupModel[] = await MetricGroup.find({ initiativeId: existingPortfolio._id });

    if (!assignedMetrics.length) {
      return {
        message: `There are no assigned metrics from the bank.`,
      };
    }

    const notCompletedSurveys = await SurveyRepository.findSurveys(
      { initiativeId: initiativeId, completedDate: { $exists: false }, deletedDate: { $exists: false } },
      { _id: 1 }
    );

    if (!notCompletedSurveys.length) {
      return {
        message: `There are no surveys to update.`,
      };
    }

    const scopeGroups: RequestScope[] = assignedMetrics.map(({ _id: groupId }) => ({
      scopeType: 'custom',
      code: groupId.toString(),
    }));

    this.startSurveyUpdates({
      user,
      domain,
      scopeGroups,
      notCompletedSurveys,
    }).catch((e) => wwgLogger.error(e));

    return {
      message: `It could take around ${getDuration(
        notCompletedSurveys.length * ESTIMATION_SURVEY_UPDATING_TIME,
        'seconds'
      ).humanize()} to update surveys.`,
    };
  }
}

let instance: BankingService;
export const getBankingService = () => {
  if (!instance) {
    instance = new BankingService(getDataShareRepository(), getSurveyManager(), getDataShareService());
  }
  return instance;
};
