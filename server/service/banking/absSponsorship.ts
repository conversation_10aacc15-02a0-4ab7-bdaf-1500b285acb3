/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { RequesterCode } from "../organization/domainConfig";
import { getScopeViews, RequesterType, RestrictionType } from "../../models/dataShare";
import { ObjectId } from "bson";
import { ReferralShare } from "../referral/referralCodesMap";
import { CustomScope } from "../../models/customScopeSchema";
import config from "../../config";
import { wwgLogger } from "../wwgLogger";

/** @TODO referralCode migration - remove after transferring to sponsorship **/
export const absScope: CustomScope[] = config.referrals.abs.trim().split(',').reduce((acc, groupId) => {
  const id = groupId.trim();
  if (!id) {
    return acc;
  }
  if (!ObjectId.isValid(id)) {
    wwgLogger.error(`Failed to process invalid ABS referral custom scope, id: "${id}"`)
    return acc
  }
  return [...acc, { scopeType: 'custom', code: id, required: true }]

  // Ensure CTL is not mandatory
}, <CustomScope[]>[{ scopeType: 'frameworks', code: 'ctl', required: false }]);

/** @TODO referralCode migration - remove after transferring to sponsorship **/
export const absDataShare: ReferralShare = {
  title: 'Data Share ABS',
  requesterCode: RequesterCode.ABS,
  requesterType: RequesterType.Portfolio,
  restrictionType: RestrictionType.Subscription,
  dataScope: {
    survey: {
      scope: {
        custom: absScope.filter(s => s.scopeType === 'custom').map(scope => new ObjectId(scope.code)),
      },
      views: getScopeViews()
    }
  }
};
