/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { ObjectId } from "bson";
import {
  ReferralConditions,
  ReferralShare
} from "../referral/referralCodesMap";
import { CustomScope } from "../../models/customScopeSchema";
import { getScopeViews, RequesterType, RestrictionType } from "../../models/dataShare";
import { absDataShare, absScope } from "./absSponsorship";


const getBankDataShare = (name: string, requesterCode: string, assignedMetricScopes: CustomScope[]): ReferralShare => {
  return {
    title: `Data Share ${name}`,
    requesterCode: requesterCode,
    requesterType: RequesterType.Portfolio,
    restrictionType: RestrictionType.Subscription,
    dataScope: {
      survey: {
        scope: {
          custom: assignedMetricScopes.filter(s => s.scopeType === 'custom').map(scope => new ObjectId(scope.code)),
        },
        views: getScopeViews()
      }
    }
  };
}

/**
 * Map key is initiative code
 */
const portfolioCodesMap: Record<string, ReferralConditions | undefined> = {
  "dbs-bank-ltd-first-vice-chairman": {
    scope: absScope,
    dataShareMappers: [
      absDataShare, // Share ABS metrics with ABS
      getBankDataShare('DBS', 'dbs-bank-ltd-first-vice-chairman', absScope)  // Share ABS metrics with Bank
    ],
  },
  "ocbc-bank": {
    scope: absScope,
    dataShareMappers: [
      absDataShare, // Share ABS metrics with ABS
      getBankDataShare('OCBC', 'ocbc-bank', absScope)  // Share ABS metrics with Bank
    ],
  },
  "united-overseas-bank-limited-chairman": {
    scope: absScope,
    dataShareMappers: [
      absDataShare, // Share ABS metrics with ABS
      getBankDataShare('UOB', 'united-overseas-bank-limited-chairman', absScope)  // Share ABS metrics with Bank
    ],
  },
}

export class BankingCodeService {

  public static getBankDataShare = getBankDataShare;

  public static async getPortfolioCodeConfig(code: string) {
    return portfolioCodesMap[code];
  }

  /**
   * Loading extra details for search etc. that allow DBS to select ABS packs etc.
   * without explicitly sharing it, by using portfolio code config mapping
   */
  public static async getPortfolioMappingMetricGroupIds(portfolioCode: string) {
    const config = await this.getPortfolioCodeConfig(portfolioCode);
    const ids: ObjectId[] = [];
    if (!config || !config.dataShareMappers) {
      return ids;
    }

    config.dataShareMappers.forEach(scope => {
      scope.dataScope?.survey?.scope?.custom?.forEach(id => ids.push(id));
    });
    return ids;
  }

}
