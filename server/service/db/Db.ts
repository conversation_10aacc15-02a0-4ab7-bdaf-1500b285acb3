/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import config from '../../config';
import mongoose from 'mongoose';
import { Sequelize } from 'sequelize';

export const connectDb = () => {
// Database connection
  const { uri, debug, database, readPreference = '' } = config.database.mongodb;
  const connectionUri = uri + '/' + database + '?authSource=admin' + (readPreference ? `&readPreference=${readPreference}` : '');
  mongoose.set('debug', debug);
  mongoose.set('strictQuery', true);
  return mongoose.connect(connectionUri);
};

const createPgConnection = () => new Sequelize(
  process.env.PGDATABASE ?? '',
  process.env.PGUSER ?? '',
  process.env.PGPASSWORD,
  {
    host: process.env.PGHOST,
    dialect: 'postgres',
    pool: {
      max: 5,
      min: 1,
      acquire: 30000,
      idle: 10000
    },
    logging: false,
    port: process.env.PGPORT ? Number(process.env.PGPORT) : undefined,
  });

let pgConnection: Sequelize;
export const getPgConnection = () => {
  if (!pgConnection) {
    pgConnection = createPgConnection();
  }

  return pgConnection;
};
