/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import MetricGroup, { MetricGroupPlain, MetricGroupType } from '../../models/metricGroup';
import { Actions } from '../action/Actions';
import UniversalTracker, { UtrType } from '../../models/universalTracker';
import { AnyBulkWriteOperation } from 'mongoose';

interface CreateTagParams {
  initiativeId: string;
  groupName: string;
  userId: ObjectId;
}

interface UpdateTagUtrsParams {
  initiativeId: string;
  updatedTagIds: { [key in Actions]: string[] };
  utrIds: string[];
}

export class CustomTagManager {
  public static async getInitiativeCustomTags({
    initiativeId,
    filters,
  }: {
    initiativeId: string | ObjectId;
    filters?: { [key: string]: unknown };
  }) {
    const $match = {
      type: MetricGroupType.Tag,
      initiativeId: new ObjectId(initiativeId),
      ...filters,
    };
    return MetricGroup.find($match).sort({ groupName: 'asc' }).lean<MetricGroupPlain[]>().exec();
  }

  public static async getUtrTagMap(initiativeId: ObjectId | string, utrIds: ObjectId[]) {
    const tags = await this.getInitiativeCustomTags({ initiativeId, filters: { universalTrackers: { $in: utrIds } } });

    return utrIds.reduce((acc, utrId) => {
      const r = [...this.getUtrTagNames(tags, utrId)];
      acc.set(utrId.toString(), r);
      return acc;
    }, new Map<string, string[]>());
  }

  private static getUtrTagNames(tags: MetricGroupPlain[], utrId: ObjectId) {
    return tags.reduce((acc, tag) => {
      if (tag.universalTrackers.some((id) => id.equals(utrId))) {
        return acc.add(tag.groupName);
      }
      return acc;
    }, new Set<string>());
  }

  public static async createTag({ initiativeId, groupName, userId }: CreateTagParams) {
    const metricGroup = new MetricGroup({
      initiativeId,
      type: MetricGroupType.Tag,
      groupName,
      universalTrackers: [],
      updated: new Date(),
      createdBy: userId,
    });

    return metricGroup.save();
  }

  private static async getVariableUtrsFromCalculatedUtrIds(utrIds: ObjectId[]) {
    const calculatedUtrs = await UniversalTracker.find(
      { _id: { $in: utrIds }, type: UtrType.Calculation },
      { calculation: 1, code: 1 }
    )
      .lean()
      .exec();

    const calculatedUtrCodes = calculatedUtrs.reduce((acc, utr) => {
      const variables = Object.values(utr.calculation?.variables || {});
      variables.forEach((v) => {
        acc.push(v.code);
      });
      return acc;
    }, [] as string[]);

    const variableUtrs = await UniversalTracker.find({ code: { $in: calculatedUtrCodes } }, { _id: 1, code: 1 })
      .lean()
      .exec();

    return { calculatedUtrs, variableUtrs };
  }

  public static async getCalculatedReportTagMap(initiativeId: ObjectId | string, utrIds: ObjectId[]) {
    const { variableUtrs, calculatedUtrs } = await this.getVariableUtrsFromCalculatedUtrIds(utrIds);

    const combinedUtrIds = [...utrIds, ...variableUtrs.map((utr) => utr._id)];

    const tags = await this.getInitiativeCustomTags({
      initiativeId,
      filters: { universalTrackers: { $in: combinedUtrIds } },
    });

    const utrCodeToId = new Map(
      variableUtrs.map((utr) => {
        return [utr.code, String(utr._id)];
      })
    );

    const utrTapMap = combinedUtrIds.reduce((acc, utrId) => {
      const r = Array.from(this.getUtrTagNames(tags, utrId));
      acc.set(utrId.toString(), r);
      return acc;
    }, new Map<string, string[]>());

    calculatedUtrs.forEach((utr) => {
      // combine all tags from all calculation.variables
      const combinedVariableTags = new Set<string>();

      const variables = Object.values(utr.calculation?.variables || {});
      variables.forEach((v) => {
        const id = utrCodeToId.get(v.code);
        if (id) {
          utrTapMap.get(id)?.forEach((tag: string) => {
            combinedVariableTags.add(tag);
          });
        }
      });

      const utrId = utr._id.toString();
      utrTapMap.get(utrId)?.forEach((tag: string) => {
        combinedVariableTags.add(tag);
      });
      // update calculatedUtr's tags in utrTagMap from what we get from the original utr's combinedVariableTags
      utrTapMap.set(utrId, Array.from(combinedVariableTags));
    });

    return utrTapMap;
  }

  public static updateUtrsTags({ initiativeId, updatedTagIds, utrIds }: UpdateTagUtrsParams) {
    const updates: AnyBulkWriteOperation<MetricGroupPlain>[] = [];
    Object.values(Actions).forEach((action) => {
      const [operator, modifier] = action === Actions.Remove ? ['$pull', '$in'] : ['$addToSet', '$each'];
      if (updatedTagIds[action] && updatedTagIds[action].length) {
        updates.push({
          updateMany: {
            filter: {
              _id: { $in: updatedTagIds[action].map((id: string) => new ObjectId(id)) },
              initiativeId: new ObjectId(initiativeId),
            },
            update: {
              [operator]: {
                universalTrackers: {
                  [modifier]: utrIds.map((utrId) => new ObjectId(utrId)),
                },
              },
            },
          },
        });
      }
    });

    return MetricGroup.bulkWrite(updates);
  }
}
