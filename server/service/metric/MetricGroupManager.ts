/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { GroupType, UTrCustomGroup } from '../../survey/utrGroupConfigs';
import MetricGroup, {
  CustomMetricOrderType,
  CustomMetricsOrder,
  MetricGroupModel,
  MetricGroupPlain,
} from '../../models/metricGroup';
import { ObjectId } from 'bson';
import { UniversalTrackerRepository } from '../../repository/UniversalTrackerRepository';
import { InitiativePlain } from "../../models/initiative";
import { naturalSort } from '../../util/string';

export class MetricGroupManager {

  private static convertCustomToUtrGroup(g: MetricGroupPlain, utrMap: Map<string, string>, surveyInitiativeId: ObjectId) {
    const name = String(surveyInitiativeId) === String(g.initiativeId) ? g.groupName : `${g.initiative?.name} Assigned Metrics: ${g.groupName}`;

    return {
      type: GroupType.Group,
      groupName: name,
      utrCodes: g.universalTrackers.map(utrId => utrMap.get(String(utrId))) as string[],
      groupId: g._id,
      groupData: g.groupData,
      groupDate: g.updated,
    };
  }

  public static async getUtrCustomGroups(metricGroups: MetricGroupPlain[], surveyInitiativeId: ObjectId): Promise<UTrCustomGroup[]> {
    const utrIds = metricGroups.map(g => g.universalTrackers).flat();
    const utrs: { _id: ObjectId, code: string }[] = await UniversalTrackerRepository.find(
      { _id: { $in: utrIds } },
      { code: 1 },
      { lean: true },
    );

    const utrMap = new Map<string, string>()
    utrs.forEach(utr => {
      utrMap.set(String(utr._id), utr.code);
    });

    const getSortName = (a: MetricGroupPlain) => {
      // Put 'assigned' groups on top
      const prefix = String(surveyInitiativeId) !== String(a.initiativeId) ? '0' : '1';
      return `${prefix} ${a.groupName}`;
    }

    const sortFunc = (a: MetricGroupPlain, b: MetricGroupPlain) => naturalSort(getSortName(a), getSortName(b));

    return metricGroups.sort(sortFunc).map(g => MetricGroupManager.convertCustomToUtrGroup(g, utrMap, surveyInitiativeId))
  }

  public static async shareGroups(metricGroups: MetricGroupModel[], company: Pick<InitiativePlain, '_id'>) {
    for (const metricGroup of metricGroups) {
      metricGroup.updated = new Date();
      metricGroup.share = metricGroup.share.filter(s => String(s.initiativeId) !== String(company._id));
      metricGroup.share.push({ initiativeId: company._id, acceptedDate: new Date() });
      await metricGroup.save();
    }
  }

  public static async shareMultipleCompanies(portfolioId: string, metricGroupId: string, initiativeIds: string[]) {
    const metricGroup = await MetricGroup.findOne({ _id: metricGroupId, initiativeId: portfolioId }).orFail().exec();

    metricGroup.updated = new Date();

    const existingIds = new Set(metricGroup.share.map((s) => String(s.initiativeId)));

    initiativeIds.forEach((companyId) => {
      if (!existingIds.has(companyId)) {
        metricGroup.share.push({
          initiativeId: new ObjectId(companyId),
          acceptedDate: new Date(),
        });
      }
    });

    return metricGroup.save();
  }

  public static async updateMetricGroupUtrsOrder({
    initiativeId,
    groupId,
    orderType,
    direction,
    utrIds,
  }: CustomMetricsOrder & { initiativeId: ObjectId; groupId: ObjectId; utrIds?: string[] }) {
    const metricGroup = await MetricGroup.findOne({
      _id: groupId,
      initiativeId,
    })
      .orFail()
      .exec();

    if (orderType === CustomMetricOrderType.Custom) {
      metricGroup.metricsOrder = {
        orderType,
      };

      // Use utrIds to sort the current universalTrackers
      metricGroup.universalTrackers = utrIds
        ? metricGroup.universalTrackers.sort((a, b) => {
          const indexA = utrIds.indexOf(a.toString());
          const indexB = utrIds.indexOf(b.toString());
          // If an item is not in utrIds, assign it a high value to push it to the end
          return (indexA !== -1 ? indexA : Infinity) - (indexB !== -1 ? indexB : Infinity);
        })
        : metricGroup.universalTrackers;
      
      return metricGroup.save();
    }

    metricGroup.metricsOrder = {
      orderType,
      direction,
    };
    return metricGroup.save();
  }
}
