/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


export enum Actions {
  Add = 'add',
  Remove = 'remove',
}

export type OptionalAction = Actions | string;

export const isValidAction = (action: OptionalAction) => {
  return [Actions.Add, Actions.Remove].includes(action as Actions)
}

export const validateActionType = (action: OptionalAction): Actions => {
  if (isValidAction(action)) {
    return action as Actions;
  }

  throw new Error(`Provided not supported action "${action}"`);
}
