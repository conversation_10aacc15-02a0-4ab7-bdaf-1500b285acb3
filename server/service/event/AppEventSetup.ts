/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import ContextError from "../../error/ContextError";
import { getAppEventEmitter } from "./AppEventEmitter";
import { getGreenProjectEventListener } from "../integration/green-project/GreenProjectEventListener";
import { getToliEventListener } from "../app/ToliEventListener";
import { getGreenlyEventListener } from "../integration/greenly/GreenlyEventListener";


// Ensure we only call it once
let completed = false;
export const appEventSetup = () => {
  const emitter = getAppEventEmitter();
  if (completed) {
    throw new ContextError(`Trying to call event setup twice`)
  }

  emitter.addListener('permissionChange', getGreenProjectEventListener().onPermissionChange);
  emitter.addListener('permissionChange', getGreenlyEventListener().onPermissionChange);

  const toliListener = getToliEventListener();
  emitter.addListener('permissionChange', toliListener.onUserPermissionChange);
  emitter.addListener('appConfigChange', toliListener.onAppConfigChange);

  completed = true;
  return emitter;
}
