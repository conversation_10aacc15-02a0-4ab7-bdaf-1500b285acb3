/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import UserEvent, { UserEventsCreate } from './UserEvent';
import { wwgLogger } from '../wwgLogger';
import { UserPlain } from '../../models/user';


export type ExcludeCreate = Omit<UserEventsCreate, 'id' | 'level' | 'createdAt' | 'updatedAt'>;

export interface EventStorage {
  save: (event: ExcludeCreate) => Promise<boolean>;
  findByEmail: (email: string) => Promise<UserEvent[]>;
  matchEmailRecordsToUser: (user: UserPlain) => Promise<number>;
}

class UserEventRepository implements EventStorage {

  public async save(event: ExcludeCreate) {
    try {
      await UserEvent.create(event);
      return true;
    } catch (e) {
      wwgLogger.error(e.message);
      return false;
    }
  }

  public async findByEmail(email: string) {
    return UserEvent.findAll({ where: { email: email } });
  }

  public async matchEmailRecordsToUser(user: UserPlain) {

    if (!user.email) {
      throw new Error(`Trying to match invalid email ${user.email}`);
    }

    const userId = user._id.toString();

    return UserEvent.update(
      { userId: userId },
      { where: { email: user.email } }
    ).then((result) => result[0])
      .catch((e: Error) => {
        wwgLogger.error(e.message);
        return 0
      });
  }
}


export const createUserEventRepository = () => new UserEventRepository();
