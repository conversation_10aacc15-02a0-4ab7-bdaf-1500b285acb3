/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import EventEmitter from "node:events";
import { UserPermissions } from "../user/userPermissions";
import { Actions } from "../action/Actions";
import { InitiativePlain } from "../../models/initiative";
import { UserPlain } from "../../models/user";


type BaseContext = Record<string, unknown>;

export interface PermissionEventData<T extends BaseContext = BaseContext> extends UserPermissions {
  user: Pick<UserPlain, '_id' | 'permissions'>;
  action: Actions,
  context?: T;
}

interface AppConfigEventData {
  initiative: Pick<InitiativePlain, '_id' | 'appConfigCode' | 'tags'>;
  before: {
    appConfigCode?: string;
  },
  after: {
    appConfigCode?: string;
  }
}

// User Permissions
export type UserEventListener = (data: PermissionEventData[]) => void | Promise<unknown>;
export type AppConfigListener = (data: AppConfigEventData) => void | Promise<unknown>;


type LocalEventTypes = {
  permissionChange: Parameters<UserEventListener>
  appConfigChange: Parameters<AppConfigListener>
}

// Can extend later as needed?
export class AppEventEmitter extends EventEmitter<LocalEventTypes> {
}


let instance: AppEventEmitter;
export const getAppEventEmitter = () => {
  if (!instance) {
    instance = new AppEventEmitter();
  }
  return instance;
}
