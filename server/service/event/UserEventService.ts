/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { createUserEventRepository, EventStorage, ExcludeCreate } from './UserEventRepository';
import { UserPlain } from '../../models/user';
import { Logger } from 'winston';
import { wwgLogger } from '../wwgLogger';
import { OnboardingModelPlain } from '../../models/onboarding';
import { ONBOARDING, SURVEY, SurveyEvents, USER } from './Events';

export class UserEventService {

  constructor(private storage: EventStorage, private logger: Logger) {
  }

  public async emit(event: ExcludeCreate) {
    return this.storage.save(event);
  }

  /**
   * User have been created
   * @param onboarding
   * @param user
   */
  public async userOnboarded(onboarding: OnboardingModelPlain, user: UserPlain) {

    const matchedCount = await this.storage.matchEmailRecordsToUser(user);
    this.emit({
      ...this.createEvent(onboarding, ONBOARDING.events.userCreated, user),
      data: {
        message: `Created new onboarding user ${user.email}`,
        matchedEventCount: matchedCount,
      },
    }).catch(this.logger.error);

    return true;
  }

  public async rejectOnboarding(onboarding: OnboardingModelPlain, user?: UserPlain) {
    const commonDetails = this.createEvent(onboarding, ONBOARDING.events.reject, user);
    this.emit(commonDetails).catch(this.logger.error);
    return true;
  }

  public async removeOnboarding(onboarding: OnboardingModelPlain, user?: UserPlain) {
    const commonDetails = this.createEvent(onboarding, ONBOARDING.events.remove, user);
    this.emit(commonDetails).catch(this.logger.error);
    return true;
  }

  private prettyEventName(event: string) {
    const s = event.replace(/_/g, ' ');
    return s.charAt(0).toUpperCase() + s.slice(1);
  }

  private createEvent(onboarding: OnboardingModelPlain, event: string, user?: UserPlain) {

    const userEmail = user ? user.email : onboarding.user.email;
    const eventName = this.prettyEventName(event);

    return {
      userId: user ? user._id.toString() : undefined,
      email: userEmail,
      service: ONBOARDING.service,
      eventDate: new Date(),
      event: event,
      data: {
        message: `${eventName} onboarding ${onboarding._id} for user ${userEmail}`,
      },
    };
  }

  addEvent(user: UserPlain, event: string, data: object = {}) {
    this.emit({
      userId: String(user._id),
      email: user.email,
      service: USER.service,
      eventDate: new Date(),
      event: event,
      data: {
        message: `${(this.prettyEventName(event))} for user ${user.email} (${user._id})`,
        ...data
      },
    }).catch(this.logger.error);
    return true;
  }

  addSurveyEvent(user: UserPlain, event: SurveyEvents, data: object = {}) {
    this.emit({
      userId: String(user._id),
      email: user.email,
      service: SURVEY.service,
      eventDate: new Date(),
      event: event,
      data: {
        message: `${(this.prettyEventName(event))} for user ${user.email} (${user._id})`,
        ...data
      },
    }).catch(this.logger.error);
    return true;
  }
}

export const createUserEventService = () => new UserEventService(createUserEventRepository(), wwgLogger);


let instance: UserEventService;
export const getUserEventService = () => {
  if (!instance) {
    instance = createUserEventService();
  }
  return instance;
}
