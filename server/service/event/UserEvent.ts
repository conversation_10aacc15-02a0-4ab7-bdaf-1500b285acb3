/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import Sequelize, { Model, DataTypes, CreationOptional, InferAttributes, InferCreationAttributes } from 'sequelize';
import { getPgConnection } from '../db/Db';
import { LEVEL } from './Events';
import { wwgLogger } from '../wwgLogger';

const db = getPgConnection();

export type UserEventsCreate = InferCreationAttributes<UserEvent>;

class UserEvent extends Model<InferAttributes<UserEvent>, UserEventsCreate> {

  declare id: CreationOptional<number>;
  declare userId: string | undefined;
  declare email: string;
  declare service: string;
  declare event: string;
  declare data?: Record<string, unknown>;
  declare level: CreationOptional<number>;

  declare eventDate: CreationOptional<Date>;

  // createdAt can be undefined during creation
  declare createdAt: CreationOptional<Date>;
  // updatedAt can be undefined during creation
  declare updatedAt: CreationOptional<Date>;
}

UserEvent.init(
  {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    email: { type: DataTypes.STRING, allowNull: false },
    userId: { type: DataTypes.STRING },
    service: { type: DataTypes.STRING, allowNull: false, },
    data: { type: DataTypes.JSONB, allowNull: true, },
    level: { type: DataTypes.SMALLINT, allowNull: false, defaultValue: LEVEL.NOTICE },
    event: { type: DataTypes.STRING, allowNull: false, },
    eventDate: { type: DataTypes.DATE, defaultValue: Sequelize.NOW },
    createdAt: { type: DataTypes.DATE, defaultValue: Sequelize.NOW },
    updatedAt: { type: DataTypes.DATE, defaultValue: Sequelize.NOW },
  },
  {
    sequelize: db,
    tableName: 'user_events',
    indexes: [
      {
        name: 'user_id_idx',
        fields: ['userId']
      },
      {
        name: 'email_idx',
        fields: ['email']
      },
      {
        name: 'service_idx',
        fields: ['service']
      },
      {
        name: 'event_idx',
        fields: ['event']
      },
      {
        name: 'level_idx',
        fields: ['level']
      },
      {
        name: 'created_at_idx',
        fields: ['createdAt']
      },
    ],
  },
);

if (process.env.NODE_ENV !== 'test' && !!process.env.USER_EVENTS_SYNC) {
  UserEvent.sync().then(() => {
    wwgLogger.info(`Sync Completed for UserEvent model`)
  }).catch(e => wwgLogger.error(e));
}
export default UserEvent;
