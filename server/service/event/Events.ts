/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

export const ONBOARDING = {
  service: 'onboarding',
  events: {
    start: 'start_onboarding',
    accept: 'accept_onboarding',
    reject: 'reject_onboarding',
    remove: 'remove_onboarding',

    userCreated: 'user_created',
  }
};

export const USER = {
  service: 'user',
  events: {
    created: 'user_created',
    registered: 'registered',
    activationEmailSent: 'user_activation_email_sent',
    activated: 'user_activated',
    passwordChanged: 'user_password_changed',
    passwordReset: 'user_password_reset',
    loginSuccess: 'user_login_success',
    refreshToken: 'user_refresh_token',
    loginFail: 'user_login_fail',
    locked: 'user_locked',
    unlocked: 'user_unlocked',
    provisioned: 'user_provisioned',
  }
};


export enum SurveyEvents {
  Created= 'survey_created',
  Disabled = 'survey_disabled',
  Deleted = 'survey_deleted',
  Published = 'survey_published',
  Unpublished = 'survey_unpublished',
  BulkAction = 'bulk_action',
  Completed = 'survey_completed',
  Uncompleted = 'survey_uncompleted',
}
export const SURVEY = {
  service: 'survey',
  events: {
    created: SurveyEvents.Created,
    disabled: SurveyEvents.Disabled,
    published: SurveyEvents.Published,
    unpublished: SurveyEvents.Unpublished,
    completed: SurveyEvents.Completed,
    uncompleted: SurveyEvents.Uncompleted
  }
};


/**
 0	Emergency	emerg	panic. System is unusable
 1	Alert	alert	Action must be taken immediately
 2	Critical	critCritical conditions	Hard device errors.[8]
 3	Error	err	error	Error conditions
 4	Warning	warning	warn.	Warning conditions
 5	Notice	notice. Normal but significant conditions
 6	Informational	info.	Informational messages
 7	Debug	debug.	Debug-level messages
 */
export enum LEVEL {
  EMERGENCY = 0,
  ALERT = 1,
  CRITICAL = 2,
  ERROR = 3,
  WARNING = 4,
  NOTICE = 5,
  INFO = 6,
}

export const validLevels = [
  LEVEL.EMERGENCY,
  LEVEL.ALERT,
  LEVEL.CRITICAL,
  LEVEL.ERROR,
  LEVEL.WARNING,
  LEVEL.NOTICE,
  LEVEL.INFO,
]
