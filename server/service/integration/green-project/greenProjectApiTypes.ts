/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


type Frequency = 'quarterly' | 'annually' | 'monthly';

/**
 * @example
 *     {
 *         "gpt_company_id": "6229211b0dafa07b451e5b94",
 *         "partner_company_id": "",
 *         "gpt_company_name": "Example PortCo A",
 *         "frequency": "quarterly"
 *     },
 *     {
 *         "gpt_company_id": "66060ff53a713dda7d2849d5",
 *         "partner_company_id": "tobys_demo_company",
 *         "gpt_company_name": "Tobys Demo Company",
 *         "frequency": "quarterly"
 *     },
 *     // Why is this here?
 *     {
 *         "count": 3
 *     }
 *
 */
export interface CompanyListItem {
  gpt_company_id: string;
  /** Either empty  **/
  partner_company_id: string;
  gpt_company_name: string;
  frequency: Frequency;
}

type SurveyStatus = "IN_PROGRESS" | "COMPLETED" | "NOT_STARTED";
type GptSurveyData = {
  status: SurveyStatus,
  data_year: number
};

export interface GptCompany extends CompanyListItem {
  surveys: GptSurveyData[];
}

export interface Scope1 {
  stationary_combustion: number
  mobile_combustion: number
  fugitive_and_processed_emissions: number
  scope_1_total: number
}

export interface Scope2 {
  purchased_electricity: number
  thermal_use: number
  scope_2_total: number
}

export interface Scope3 {
  purchased_goods_and_services: number
  capital_goods: number
  fuel_and_energy_related_activities: number
  upstream_transportation_and_distribution: number
  waste: number
  business_travel: number
  employee_commuting: number
  upstream_leased_assets: number
  downstream_transportation_and_distribution: number
  processing_of_sold_products: number
  use_of_sold_products: number
  eol_of_sold_products: number
  downstream_leased_assets: number
  franchises: number
  investments: number
  scope_3_total: number
}

export interface EmissionData {
  business_unit: string
  scope1: Scope1
  scope2: Scope2
  scope3: Scope3
  is_total: boolean
  methodology: string
}

/**
 * @example
 * {
 *     "data_properties": {
 *         "partner_company_id": "5bb4ab10e90adc12160dddae",
 *         "status": "IN_PROGRESS",
 *         "last_updated": "2024-03-29T01:05:41.433Z"
 *     },
 *     "location-based_emissions": [
 *         {
 *             "default": {
 *                 "business_unit": "Default",
 *                 "scope1": {
 *                     "stationary_combustion": 33.866,
 *                     "mobile_combustion": 28.333,
 *                     "fugitive_and_processed_emissions": 43.92,
 *                     "scope_1_total": 106.119
 *                 },
 *                 "scope2": {
 *                     "purchased_electricity": 38.096,
 *                     "thermal_use": 0,
 *                     "scope_2_total": 38.096
 *                 },
 *                 "scope3": {
 *                     "purchased_goods_and_services": 5.026,
 *                     "capital_goods": 106.393,
 *                     "fuel_and_energy_related_activities": 0,
 *                     "upstream_transportation_and_distribution": 9.085,
 *                     "waste": 48.4,
 *                     "business_travel": 15.627,
 *                     "employee_commuting": 38.558,
 *                     "upstream_leased_assets": 0,
 *                     "downstream_transportation_and_distribution": 0,
 *                     "processing_of_sold_products": 0,
 *                     "use_of_sold_products": 0,
 *                     "eol_of_sold_products": 0,
 *                     "downstream_leased_assets": 0,
 *                     "franchises": 0,
 *                     "investments": 0,
 *                     "scope_3_total": 223.089
 *                 },
 *                 "is_total": false,
 *                 "methodology": "location"
 *             }
 *         },
 *         {
 *             "total": {
 *                 "business_unit": "Total",
 *                 "scope1": {
 *                     "stationary_combustion": 33.866,
 *                     "mobile_combustion": 28.333,
 *                     "fugitive_and_processed_emissions": 43.92,
 *                     "scope_1_total": 106.119
 *                 },
 *                 "scope2": {
 *                     "purchased_electricity": 38.096,
 *                     "thermal_use": 0,
 *                     "scope_2_total": 38.096
 *                 },
 *                 "scope3": {
 *                     "purchased_goods_and_services": 5.026,
 *                     "capital_goods": 106.393,
 *                     "fuel_and_energy_related_activities": 0,
 *                     "upstream_transportation_and_distribution": 9.085,
 *                     "waste": 48.4,
 *                     "business_travel": 15.627,
 *                     "employee_commuting": 38.558,
 *                     "upstream_leased_assets": 0,
 *                     "downstream_transportation_and_distribution": 0,
 *                     "processing_of_sold_products": 0,
 *                     "use_of_sold_products": 0,
 *                     "eol_of_sold_products": 0,
 *                     "downstream_leased_assets": 0,
 *                     "franchises": 0,
 *                     "investments": 0,
 *                     "scope_3_total": 223.089
 *                 },
 *                 "is_total": true,
 *                 "methodology": "location"
 *             }
 *         }
 *     ],
 *     "market-based_emissions": [
 *         {
 *             "default": {
 *                 "business_unit": "Default",
 *                 "scope1": {
 *                     "stationary_combustion": 33.866,
 *                     "mobile_combustion": 28.333,
 *                     "fugitive_and_processed_emissions": 43.92,
 *                     "scope_1_total": 106.119
 *                 },
 *                 "scope2": {
 *                     "purchased_electricity": 94.708,
 *                     "thermal_use": 0,
 *                     "scope_2_total": 94.708
 *                 },
 *                 "scope3": {
 *                     "purchased_goods_and_services": 5.026,
 *                     "capital_goods": 106.393,
 *                     "fuel_and_energy_related_activities": 0,
 *                     "upstream_transportation_and_distribution": 9.085,
 *                     "waste": 48.4,
 *                     "business_travel": 15.627,
 *                     "employee_commuting": 38.558,
 *                     "upstream_leased_assets": 0,
 *                     "downstream_transportation_and_distribution": 0,
 *                     "processing_of_sold_products": 0,
 *                     "use_of_sold_products": 0,
 *                     "eol_of_sold_products": 0,
 *                     "downstream_leased_assets": 0,
 *                     "franchises": 0,
 *                     "investments": 0,
 *                     "scope_3_total": 223.089
 *                 },
 *                 "is_total": false,
 *                 "methodology": "market"
 *             }
 *         },
 *         {
 *             "total": {
 *                 "business_unit": "Total",
 *                 "scope1": {
 *                     "stationary_combustion": 33.866,
 *                     "mobile_combustion": 28.333,
 *                     "fugitive_and_processed_emissions": 43.92,
 *                     "scope_1_total": 106.119
 *                 },
 *                 "scope2": {
 *                     "purchased_electricity": 94.708,
 *                     "thermal_use": 0,
 *                     "scope_2_total": 94.708
 *                 },
 *                 "scope3": {
 *                     "purchased_goods_and_services": 5.026,
 *                     "capital_goods": 106.393,
 *                     "fuel_and_energy_related_activities": 0,
 *                     "upstream_transportation_and_distribution": 9.085,
 *                     "waste": 48.4,
 *                     "business_travel": 15.627,
 *                     "employee_commuting": 38.558,
 *                     "upstream_leased_assets": 0,
 *                     "downstream_transportation_and_distribution": 0,
 *                     "processing_of_sold_products": 0,
 *                     "use_of_sold_products": 0,
 *                     "eol_of_sold_products": 0,
 *                     "downstream_leased_assets": 0,
 *                     "franchises": 0,
 *                     "investments": 0,
 *                     "scope_3_total": 223.089
 *                 },
 *                 "is_total": true,
 *                 "methodology": "market"
 *             }
 *         }
 *     ]
 * }
 */

type Emissions = { [k: string]: EmissionData}[];

export interface EmissionByBusinessUnit {
  data_properties: {
    partner_company_id: string,
    status: SurveyStatus,
    last_updated: string;
  },
  'location-based_emissions': Emissions,
  'market-based_emissions': Emissions
}

// Returns aggregated emissions totals for the specified year, across all business units
export interface AggregationEmissionTotals {
  data_properties: {
    partner_company_id: string,
    status: SurveyStatus,
    last_updated: string;
  },
  'business_unit_emissions': [{ total: EmissionData }],
}



// Seems like this should not be here, but current is.
type ListCount = { count: number };

export type ListResponse = (CompanyListItem | ListCount)[] ;
