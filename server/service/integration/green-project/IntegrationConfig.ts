/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { UtrValueType } from "../../../models/public/universalTrackerType";
import { industryOptions } from "./industry";
import type { IntegrationConfig } from "../IntegrationProvider";
import { UtrType } from '../../../models/universalTracker';

// Return new instance on every call
export const getIntegrationConfig = () => {
  return {
    type: 'external',
    requirements: {
      questions: [
        {
          _id: 'green-project-tech-registration-details',
          type: UtrType.Generated,
          code: 'green-project-tech-registration-details',
          valueLabel: 'Additional set up questions',
          name: 'Registration details',
          valueType: UtrValueType.Table,
          valueValidation: {
            table: {
              validation: {
                maxRows: 1,
              },
              columns: [
                {
                  code: 'green-project-tech-industry',
                  name: 'Industry',
                  type: 'valueList',
                  options: industryOptions.map((industry) => {
                    return ({ code: industry, name: industry });
                  }),
                  validation: { required: true },
                },
                {
                  code: 'green-project-tech-employees',
                  name: 'Number of employees',
                  type: 'number',
                  validation: { required: true },
                },
                {
                  code: 'green-project-tech-data-frequency',
                  name: 'Data frequency',
                  type: 'valueList',
                  options: ['Quarterly', 'Month', 'Yearly'].map((period) => ({ code: period, name: period })),
                  validation: { required: true },
                },
                {
                  code: 'green-project-tech-revenue',
                  name: 'Total Revenue',
                  type: 'number',
                  unitType: 'currency',
                  unit: 'USD',
                  numberScale: 'millions',
                },
              ]
            },

          }
        },
      ]
    }
  } satisfies IntegrationConfig;
};
