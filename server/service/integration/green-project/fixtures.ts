import { UtrValueType } from '../../../models/public/universalTrackerType';
import { UtrType } from '../../../models/universalTracker';
import { DataPeriods } from '../../utr/constants';
import { IntegrationData } from '../IntegrationProvider';

export const greenProjectTechUtrsData: IntegrationData['utrsData'] = [
  {
    integrationCode: 'green-project-tech',
    utr: {
      _id: 'green-project-tech-total-scope-1',
      type: UtrType.Generated,
      code: 'green-project-tech-total-scope-1',
      valueLabel: 'Total Scope 1 Emissions',
      name: 'Total Scope 1 Emissions',
      valueType: UtrValueType.Table,
      valueValidation: {
        table: {
          validation: {
            maxRows: 1,
          },
          columns: [
            {
              code: 'stationary_combustion',
              name: 'Stationary Combustion',
              type: 'number',
            },
            {
              code: 'mobile_combustion',
              name: 'Mobile Combustion',
              type: 'number',
            },
            {
              code: 'fugitive_and_processed_emissions',
              name: 'Fugitive and Processed Emissions',
              type: 'number',
            },
            {
              code: 'scope_1_total',
              name: 'Scope 1 Total',
              type: 'number',
            },
          ],
        },
      },
    },
    utrvs: [
      {
        universalTrackerId: 'green-project-tech-total-scope-1',
        utrCode: 'green-project-tech-total-scope-1',
        valueType: UtrValueType.Table,
        valueData: {
          table: [
            [
              {
                code: 'stationary_combustion',
                value: 10.899,
              },
              {
                code: 'mobile_combustion',
                value: 0,
              },
              {
                code: 'fugitive_and_processed_emissions',
                value: 0,
              },
              {
                code: 'scope_1_total',
                value: 10.899,
              },
            ],
          ],
        },
        sourceType: 'manual',
        effectiveDate: new Date('2023-12-31T23:59:59.999Z'),
        period: DataPeriods.Yearly,
        type: 'actual',
        status: 'verified',
      },
      {
        universalTrackerId: 'green-project-tech-total-scope-1',
        utrCode: 'green-project-tech-total-scope-1',
        valueType: UtrValueType.Table,
        valueData: {
          table: [
            [
              {
                code: 'stationary_combustion',
                value: 9.756,
              },
              {
                code: 'mobile_combustion',
                value: 2.202,
              },
              {
                code: 'fugitive_and_processed_emissions',
                value: 0,
              },
              {
                code: 'scope_1_total',
                value: 11.958,
              },
            ],
          ],
        },
        sourceType: 'manual',
        effectiveDate: new Date('2022-12-31T23:59:59.999Z'),
        period: DataPeriods.Yearly,
        type: 'actual',
        status: 'verified',
      },
    ],
  },
  {
    integrationCode: 'green-project-tech',
    utr: {
      _id: 'green-project-tech-total-scope-2',
      type: UtrType.Generated,
      code: 'green-project-tech-total-scope-2',
      valueLabel: 'Total Scope 2 Emissions',
      name: 'Total Scope 2 Emissions',
      valueType: UtrValueType.Table,
      valueValidation: {
        table: {
          validation: {
            maxRows: 1,
          },
          columns: [
            {
              code: 'purchased_electricity',
              name: 'Purchased Electricity',
              type: 'number',
            },
            {
              code: 'thermal_use',
              name: 'Thermal Use',
              type: 'number',
            },
            {
              code: 'scope_2_total',
              name: 'Scope 2 Total',
              type: 'number',
            },
          ],
        },
      },
    },
    utrvs: [
      {
        universalTrackerId: 'green-project-tech-total-scope-2',
        utrCode: 'green-project-tech-total-scope-2',
        valueType: UtrValueType.Table,
        valueData: {
          table: [
            [
              {
                code: 'purchased_electricity',
                value: 5.834,
              },
              {
                code: 'thermal_use',
                value: 3.867,
              },
              {
                code: 'scope_2_total',
                value: 9.701,
              },
            ],
          ],
        },
        sourceType: 'manual',
        effectiveDate: new Date('2022-12-31T23:59:59.999Z'),
        period: DataPeriods.Yearly,
        type: 'actual',
        status: 'verified',
      },
    ],
  },
  {
    integrationCode: 'green-project-tech',
    utr: {
      _id: 'green-project-tech-total-scope-3',
      type: UtrType.Generated,
      code: 'green-project-tech-total-scope-3',
      valueLabel: 'Total Scope 3 Emissions',
      name: 'Total Scope 3 Emissions',
      valueType: UtrValueType.Table,
      valueValidation: {
        table: {
          validation: {
            maxRows: 1,
          },
          columns: [
            {
              code: 'purchased_goods_and_services',
              name: 'Purchased Goods and Services',
              type: 'number',
            },
            {
              code: 'capital_goods',
              name: 'Capital Goods',
              type: 'number',
            },
            {
              code: 'fuel_and_energy_related_activities',
              name: 'Fuel and Energy Related Activities',
              type: 'number',
            },
            {
              code: 'upstream_transportation_and_distribution',
              name: 'Upstream Transportation and Distribution',
              type: 'number',
            },
            {
              code: 'waste',
              name: 'Waste',
              type: 'number',
            },
            {
              code: 'business_travel',
              name: 'Business Travel',
              type: 'number',
            },
            {
              code: 'employee_commuting',
              name: 'Employee Commuting',
              type: 'number',
            },
            {
              code: 'upstream_leased_assets',
              name: 'Upstream Leased Assets',
              type: 'number',
            },
            {
              code: 'downstream_transportation_and_distribution',
              name: 'Downstream Transportation and Distribution',
              type: 'number',
            },
            {
              code: 'processing_of_sold_products',
              name: 'Processing of Sold Products',
              type: 'number',
            },
            {
              code: 'use_of_sold_products',
              name: 'Use of Sold Products',
              type: 'number',
            },
            {
              code: 'eol_of_sold_products',
              name: 'EOL of Sold Products',
              type: 'number',
            },
            {
              code: 'downstream_leased_assets',
              name: 'Downstream Leased Assets',
              type: 'number',
            },
            {
              code: 'franchises',
              name: 'Franchises',
              type: 'number',
            },
            {
              code: 'investments',
              name: 'Investments',
              type: 'number',
            },
            {
              code: 'scope_3_total',
              name: 'Scope 3 Total',
              type: 'number',
            },
          ],
        },
      },
    },
    utrvs: [
      {
        universalTrackerId: 'green-project-tech-total-scope-3',
        utrCode: 'green-project-tech-total-scope-3',
        valueType: UtrValueType.Table,
        valueData: {
          table: [
            [
              {
                code: 'purchased_goods_and_services',
                value: 7.543,
              },
              {
                code: 'capital_goods',
                value: 2.542,
              },
              {
                code: 'fuel_and_energy_related_activities',
                value: 6.519,
              },
              {
                code: 'upstream_transportation_and_distribution',
                value: 12.885,
              },
              {
                code: 'waste',
                value: 8.459,
              },
              {
                code: 'business_travel',
                value: 6.541,
              },
              {
                code: 'employee_commuting',
                value: 4.007,
              },
              {
                code: 'upstream_leased_assets',
                value: 10.642,
              },
              {
                code: 'downstream_transportation_and_distribution',
                value: 11.958,
              },
              {
                code: 'processing_of_sold_products',
                value: 9.662,
              },
              {
                code: 'use_of_sold_products',
                value: 14.307,
              },
              {
                code: 'eol_of_sold_products',
                value: 18.651,
              },
              {
                code: 'downstream_leased_assets',
                value: 2.361,
              },
              {
                code: 'franchises',
                value: 6.928,
              },
              {
                code: 'investments',
                value: 17.851,
              },
              {
                code: 'scope_3_total',
                value: 140.856,
              },
            ],
          ],
        },
        sourceType: 'manual',
        effectiveDate: new Date('2022-12-31T23:59:59.999Z'),
        period: DataPeriods.Yearly,
        type: 'actual',
        status: 'verified',
      },
    ],
  },
];
