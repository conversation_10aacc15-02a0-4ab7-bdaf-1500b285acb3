/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { getEmailService } from "../../email/EmailService";
import config from "../../../config";
import DOMPurify from "isomorphic-dompurify";
import { SingleIdentityService } from "../api/SingleIdentityService";
import axios, { type AxiosInstance } from "axios";
import { AuthInterceptor } from "../api/AuthInterceptor";
import { AggregationEmissionTotals, EmissionByBusinessUnit, GptCompany, ListResponse } from "./greenProjectApiTypes";

interface SetupContextValue {
  code: string,
  name: string,
  value?: string | number | boolean,
  unit?: string
}

type Address = {
  street: string;
  city: string;
  state: string
  country: string;
  postalCode: string;
};

interface FacilityCreate {
  name: string;
  address: Address
  floorSpace: number;
  floorSpaceUnit: 'sqm' | 'sqft';
  ownerType: 'owner' | 'lessor' | 'lessee';
}

interface GreenProjectCreateData {

  user: {
    email: string
    jobTitle?: string;
    firstName: string;
    surname: string;
  }

  company: {
    _id: string;
    name: string
    logo: string | undefined;
    description?: string;
    address: Address
  }
  additionalContext: SetupContextValue[];

  facilities?: FacilityCreate[]
}

export class GreenProjectApi {

  constructor(
    private readonly emailService: ReturnType<typeof getEmailService>,
    private httpClient: AxiosInstance,
  ) {
  }

  public async listCompanies() {
    return this.httpClient.get<ListResponse>('/company/all').then(res => res.data);
  }

  public async getCompany(companyId: string) {
    return this.httpClient.get<GptCompany>(`/company/${companyId}`).then(res => res.data);
  }

  /**
   * Returns aggregated emissions totals for the year broken down by business unit
   */
  public async getEmissionsData(companyId: string, year: number) {
    return this.httpClient.get<EmissionByBusinessUnit>(`/company/${companyId}/emissions/${year}/`).then(res => res.data);
  }

  /**
   * Returns aggregated emissions totals for the specified year, across all business units
   */
  public async getEmissionsDataTotal(companyId: string, year: number) {
    return this.httpClient.get<AggregationEmissionTotals>(`/company/${companyId}/emissions/${year}/total`).then(res => res.data);
  }


  /**
   * Eventually this will be replaced by API call once enabled, now we rely on email
   */
  public async createConnection(createData: GreenProjectCreateData) {

    // Send data through email
    const { company, user, additionalContext } = createData;


    const msg = this.emailService.getNewMessageInstance();
    msg.addTo(config.integrations.greenProject.email, 'Green Project Tech');
    msg.setSubject('Green Project Connection Setup');

    const createRow = (label: string, value: string | undefined) => {
      return `<tr><td>${label}</td><td>${value || '-'}</td></tr>`;
    }


    const userDetails = [
      createRow('Job Title', user.jobTitle),
      createRow('First Name', user.firstName),
      createRow('Surname', user.surname),
      createRow('email', user.email),
    ]

    const companyDetails = [
      createRow('Id', company._id.toString()),
      createRow('Name', company.name),
      createRow('Logo', company.logo),
      createRow('Description', company.description),
    ];

    const context = additionalContext.reduce((acc, ctx) => {
      acc.push(createRow(ctx.name, [ctx.value, ctx.unit].filter(Boolean).join(' ')));
      return acc;
    }, [] as string[])

    const addressDetails = Object.entries(company.address).reduce((acc, [k, v]) => {
      acc.push(createRow(k, v));
      return acc;
    }, [] as string[])

    const html = (`
      <body>
        <h3>New G17Eco website integration request.</h3>
        <table border='1' width='100%'>
          <tr><td colspan="2">User</td></tr>
          ${userDetails.join('')}

          <tr><td colspan="2">Company</td></tr>
          ${companyDetails.join('')}

          <tr><td colspan="2">Address</td></tr>
          ${addressDetails.join('')}

          <tr><td colspan="2">Additional Data</td></tr>
          ${context.join('')}
        </table>
      </body>`
    );

    const sanitizedText = DOMPurify.sanitize(html);
    msg.setHtml(sanitizedText);

    const response = await this.emailService.send(msg);

    return {
      emailTransactionId: response.getId(),
      companyId: company._id.toString(),
    };
  }
}

let instance: GreenProjectApi;
export const getGreenProjectApi = () => {
  if (!instance) {
    const gptConfig = config.integrations.greenProject;
    const identityService = new SingleIdentityService({
      credentials: gptConfig.api.credentials,
      baseUrl: gptConfig.api.tokenUrl,
    });
    const httpClient = axios.create({ baseURL: gptConfig.api.baseUrl, timeout: gptConfig.api.timeoutMs });

    httpClient.interceptors.request.use(
      AuthInterceptor(identityService),
      error => Promise.reject(error),
    );

    instance = new GreenProjectApi(
      getEmailService(),
      httpClient,
    );
  }
  return instance;
}
