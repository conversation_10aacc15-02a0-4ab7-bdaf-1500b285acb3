/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { getGreenProjectApi, GreenProjectApi } from "./GreenProjectApi";
import { LoggerInterface, wwgLogger } from "../../wwgLogger";
import {
  GeneratedUtr,
  GeneratedUtrv,
  IntegrationConfig,
  IntegrationData,
  IntegrationDataParams,
  IntegrationProvider,
  IntegrationService,
  SetupData
} from "../IntegrationProvider";
import config from '../../../config';
import { getIntegrationConfig } from "./IntegrationConfig";
import { UtrValueType } from "../../../models/public/universalTrackerType";
import ContextError from "../../../error/ContextError";
import { getIntegrationRepository, IntegrationRepository } from "../IntegrationRepository";
import { ObjectId } from "bson";
import { DataPeriods } from "../../utr/constants";
import type { IntegrationConnectionPlain } from "../../../models/integrationConnection";
import { EmissionData, GptCompany } from "./greenProjectApiTypes";
import { getUTCEndOf } from "../../../util/date";
import { getIntegrationNotificationService } from "../IntegrationNotificationService";
import { getSingleSignOnManager, SingleSignOnManager } from "../SingleSignOnManager";
import { greenProjectTechUtrsData } from './fixtures';


export interface GreenProjectData {
  answers: SetupData['generatedAnswers'];
  address?: SetupData['address'];
  gptCompanyId?: string;
}

type GPTConnection = IntegrationConnectionPlain<GreenProjectData>

interface SurveyEmissionData {
  emissionData: EmissionData;
  effectiveDate: Date
  period: DataPeriods;
}

interface GenerateUtrvsParams {
  utr: GeneratedUtr;
  data: SurveyEmissionData[];
}

export class GreenProjectService implements IntegrationService {

  public readonly code = 'green-project-tech';

  public readonly oktaGroupId = config.integrations.greenProject.oktaGroupId;

  constructor(
    private readonly logger: LoggerInterface,
    private readonly api: GreenProjectApi,
    private readonly repo: IntegrationRepository,
    private readonly notificationService: ReturnType<typeof getIntegrationNotificationService>,
    private readonly ssoManager: SingleSignOnManager,
  ) {
  }

  public async getSetupConfig() {
    return {
      provider: this.getInfo(),
      integration: this.getConfig(),
    };
  }

  public async executeChecks() {
    this.logger.info(`Executing checks for ${this.code}`);

    const companies = await this.api.listCompanies();
    this.logger.info(`Companies found: ${companies.length}`, { companies });

    if (!companies.length) {
      return { connectionIds: [] };
    }

    const partnerIdToCompanyId = new Map<string, string>()
    const initiativeIds: ObjectId[] = [];

    companies.forEach((company) => {
      if ('partner_company_id' in company && ObjectId.isValid(company.partner_company_id)) {
        initiativeIds.push(new ObjectId(company.partner_company_id));
        partnerIdToCompanyId.set(company.partner_company_id, company.gpt_company_id)
      }
    });

    const connections = await this.repo.getConnectionsByProvider({
      integrationCode: this.code,
      initiativeId: { $in: initiativeIds },
      status: 'pending',
    });

    this.logger.info(`Pending connections found: ${connections.length}`, {
      providerCode: this.code,
      initiativeIds: connections.map(c => c.initiativeId)
    });

    for (const connection of connections) {
      connection.status = 'active';
      if (this.isValidConnection(connection)) {
        connection.data.gptCompanyId = partnerIdToCompanyId.get(connection.initiativeId.toString());
        connection.markModified('data');
        this.sendActive(connection);
      }

      await connection.save();
      this.logger.info(`Connection upgraded to active`, {
        providerCode: this.code,
        connectionId: connection._id,
        initiativeId: connection.initiativeId,
      });
    }

    this.enableSSOAccess({
      initiativeIds: connections.map(c => c.initiativeId),
    }).catch(this.logger.error);

    return {
      connectionIds: connections.map(c => c._id),
    };
  }

  private sendActive(connection: GPTConnection) {
    this.notificationService.sendIntegrationActive({
      provider: this.getInfo(),
      connection,
      notificationData: {
        recipients: [{ id: connection.createdBy.toString() }],
      }
    }).catch((e) => {
      this.logger.error(new ContextError(`Failed to send integration ${this.code} activation notification`, {
        cause: e,
        providerCode: this.code,
      }));
    });
  }

  private getConfig() {
    return getIntegrationConfig();
  }

  /** Hardcoded generated utrs, but could later come from another async storage **/
  public async getAvailableQuestions(): Promise<GeneratedUtr[]> {
    return greenProjectTechUtrsData.map(data => data.utr);
  }

  public async createSetup(setupData: SetupData) {

    const { rootInitiative, user, address } = setupData;
    this.logger.info(`Setting up Green Project ${rootInitiative.name}`);

    const setupConfig = this.getConfig();
    const additionalContext = this.getQuestionData(setupConfig, setupData);


    const response = await this.api.createConnection({
      company: {
        _id: rootInitiative._id.toString(),
        name: rootInitiative.name,
        logo: rootInitiative.profile,
        address: {
          street: address?.line1 ?? '',
          city: address?.city ?? rootInitiative.geoLocation ?? '',
          state: '',
          country: address?.country ?? rootInitiative.country ?? '',
          postalCode: address?.postcode ?? '',

        },
      },
      user,
      additionalContext,
    });

    this.logger.info(`Green Project setup completed for ${rootInitiative.name}`, response);
    return { success: true, data: response };
  }

  private getQuestionData(setupConfig: IntegrationConfig, setupData: SetupData) {
    return setupConfig.requirements.questions.map((question) => {
      const answer = setupData.generatedAnswers.find((answer) => answer.utrCode === question.code);
      if (answer) {

        if (question.valueType === UtrValueType.Number) {
          return { code: question.code, name: question.name, value: answer.value };
        }

        // Must be single row table
        if (question.valueType === UtrValueType.Table && question.valueValidation?.table?.validation?.maxRows === 1) {
          const tableColumns = question.valueValidation?.table?.columns ?? [];
          const row = answer.valueData?.table?.[0] ?? [];
          return tableColumns.map((column) => {
            const columnAnswer = row.find((cell) => cell.code === column.code);
            return {
              code: column.code,
              name: column.name,
              value: columnAnswer?.value,
              unit: columnAnswer?.unit,
            };
          })
        }

        throw new ContextError(`Not supported value type: ${question.valueType}`, { providerCode: setupData.providerCode });
      }

      return { code: question.code, name: question.code, value: undefined };
    }).flat();
  }

  public getInfo(): IntegrationProvider {
    return {
      code: this.code,
      name: 'Emissions Tracker',
      shortName: 'GPT',
      logo: `${config.assets.cdn}/carbon-calculators/Emissions_Tracker_logo.svg`,
      logoFooter: `${config.assets.cdn}/carbon-calculators/green-project-tech-logo.png`,
      color: '#3778FF11',
      icon: `${config.assets.cdn}/carbon-calculators/Emissions_Tracker_icon.svg`,
      link: 'https://www.greenprojecttech.com/',
      tags: [
        'All industries',
        'Emissions report',
        'Dashboards',
        'Scope 1, 2 & 3',
        'GHG Protocol aligned'
      ],
      description: 'Green Project Technologies is the first carbon accounting platform that was purpose built for the private markets and supply chains. They have a client base of over 400 companies in the US, UK, EU, Australia, Africa, and South America have been coined the "QuickBooks of ESG Accounting.',
      highlights: [
        '"Quickbooks" of carbon accounting',
        'Carbon accounting: Scope 1,2,3 emissions',
        'Dashboards and downloadable reports',
        'Carbon savings, targets & tracking',
        'Direct Data Integrations to 12k+ utility providers',
        'Portfolio and Supply Chain Management',
      ],
    };
  }

  /**
   * Should cache this, require separate cache or start storing in DB
   * @param dataLookup
   */
  public async generateIntegrationData(dataLookup: IntegrationDataParams): Promise<IntegrationData["utrsData"]> {

    const { utrCodes, connection, period } = dataLookup;
    if (!this.isValidConnection(connection) || !connection.data.gptCompanyId) {
      return [];
    }

    const now = Date.now();
    const baseContext = {
      initiativeId: connection.initiativeId.toString(),
      companyId: connection.data.gptCompanyId
    };

    this.logger.info(`Generating Green Project data for ${utrCodes.length} UTRs`, { utrCodes, ...baseContext });

    const gptCompany = await this.api.getCompany(connection.data.gptCompanyId).catch((error) => {
      throw new ContextError(`Failed to fetch Green Project company`, { ...baseContext, cause: error })
    });

    if (period && period !== gptCompany.frequency) {
      // Most of the time this will not match, for now we just log and ignore
      this.logger.info(`Green Project company frequency does not match requested period`, {
        ...baseContext,
        requestedPeriod: period,
        companyPeriod: gptCompany.frequency,
      });
    }

    const data = await this.processSurveys(gptCompany, dataLookup);
    this.logger.info(`Completed Green Project data in ${(Date.now() - now)}ms`, baseContext);

    return data;
  }

  private async processSurveys(gptCompany: GptCompany, { utrCodes, isCompleted, connection }: IntegrationDataParams) {

    const utrs = await this.getAvailableQuestions();

    const requiredUtrs = utrs.filter(utr => utrCodes.includes(utr.code));

    // Allow all data for now, might need to be restricted to completed only?
    // @TODO dateRange filters: How about years? 2022-2023 range?
    const surveys = gptCompany.surveys.filter(survey => {
      if (isCompleted && survey.status !== 'COMPLETED') {
        return false
      }
      // Should filter by date range
      return true;
    });


    if (surveys.length === 0) {
      return [];
    }

    const years = new Set(surveys.map(survey => survey.data_year));
    const allEmissionData = await Promise.all(Array.from(years).map(async (year) => {
      return {
        year,
        data: await this.api.getEmissionsDataTotal(gptCompany.gpt_company_id, year)
      }
    }));

    const data = allEmissionData.reduce((acc, surveyData) => {
      const businessUnitEmissions = surveyData.data.business_unit_emissions;
      const totalData = businessUnitEmissions.find(b => b.total?.is_total);
      if (!totalData?.total) {
        this.logger.error(new ContextError(`Failed to find total data for Green Project company`, {
          initiativeId: connection.initiativeId.toString(),
          data: businessUnitEmissions.map(unit => Object.keys(unit))
        }));
        return acc;
      }

      const effectiveDate = getUTCEndOf('year', new Date(`${surveyData.year}-12-15T15:00:00`));
      acc.push({ emissionData: totalData.total, effectiveDate, period: DataPeriods.Yearly })

      return acc;
    }, [] as SurveyEmissionData[]);

    return requiredUtrs.map(utr => {
      const utrvs = this.generateUtrvs({ utr, data });
      return { integrationCode: this.code, utr, utrvs };
    });
  }

  /**
   * Only UTR tables, that we can map to properties directly that much Table codes
   */
  private generateUtrvs({ utr, data }: GenerateUtrvsParams): GeneratedUtrv[] {

    const utrvs: GeneratedUtrv[] = [];

    for (const { emissionData, effectiveDate, period } of data) {
      const rowData = this.getTableRowValue(utr.code, emissionData);
      const data = Object.entries(rowData).map(([code, value]) => ({ code, value }));
      const table = data.length > 0 ? [data] : [];

      utrvs.push({
        universalTrackerId: utr.code,
        utrCode: utr.code,
        valueType: UtrValueType.Table,
        valueData: { table },
        sourceType: 'manual',
        effectiveDate,
        period,
        type: 'actual',
        status: 'verified',
      })
    }

    return utrvs;
  }

  private getTableRowValue(code: string, emissionData: EmissionData) {
    switch (code) {
      case 'green-project-tech-total-scope-1':
        return emissionData.scope1
      case 'green-project-tech-total-scope-2':
        return emissionData.scope2
      case 'green-project-tech-total-scope-3':
        return emissionData.scope3
      default:
        return {};
    }
  }

  /**
   * We are responsible to manage who can authenticate with SSO
   * For now we just enabled all user that are part of the initial connections.
   *
   * Will require us to manage follow up add/remove user changes and reflect
   * those in Okta Groups
   */
  public async enableSSOAccess({ initiativeIds }: { initiativeIds: ObjectId []}) {
    this.logger.info(`Enabling SSO access for ${initiativeIds.length} initiatives`, {
      initiativeIds: initiativeIds.map(String),
    });
    const results = await this.ssoManager.addUsersToGroup(this.oktaGroupId, initiativeIds);

    const { error, success } = results.reduce((acc, { status, userId }) => {
      acc[status].push(userId);
      return acc;
    }, { success: [], error: [] } as Record<typeof results[0]['status'], ObjectId[]>);

    this.logger.info(`SSO access enabled for ${success.length} users, failed ${error.length}`, {
      success: success.map(String),
      error: error.map(String),
    });

    return results;
  }

  private isValidConnection(connection: IntegrationConnectionPlain): connection is GPTConnection {
    if (connection.integrationCode !== this.code) {
      return false;
    }

    const { data } = connection;
    if (!data || typeof data !== 'object') {
      return false;
    }
    return 'answers' in data && Array.isArray(data.answers);
  }

  public async getExternalApp() {
    const info = this.getInfo();
    return {
      name: info.name,
      login: {
        url: config.integrations.greenProject.loginUrl,
        text: 'Login to Green Project',
      }
    }
  }
}

let instance: GreenProjectService;
export const getGreenProjectService = () => {
  if (!instance) {
    instance = new GreenProjectService(
      wwgLogger,
      getGreenProjectApi(),
      getIntegrationRepository(),
      getIntegrationNotificationService(),
      getSingleSignOnManager(),
    );
  }
  return instance;
}
