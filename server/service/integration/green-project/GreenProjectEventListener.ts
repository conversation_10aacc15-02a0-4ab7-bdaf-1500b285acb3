/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { UserEventListener } from "../../event/AppEventEmitter";
import { getGreenProjectService } from "./GreenProjectService";
import { getIntegrationRepository } from "../IntegrationRepository";
import { LoggerInterface, wwgLogger } from "../../wwgLogger";
import ContextError from "../../../error/ContextError";

export class GreenProjectEventListener {

  constructor(
    private logger: LoggerInterface,
    private repo: ReturnType<typeof getIntegrationRepository>,
    private greenProjectService: ReturnType<typeof getGreenProjectService>,
  ) {
  }

  public onPermissionChange: UserEventListener = async (data) => {
    const ids = data.map(event => event.initiativeId);

    const connections = await this.repo.getConnectionsByProvider({
      integrationCode: this.greenProjectService.code,
      initiativeId: { $in: ids },
      status: 'active',
    });

    const initiativeIds = connections.map(c => c.initiativeId);
    if (initiativeIds.length === 0) {
      return;
    }

    try {
      const result = await this.greenProjectService.enableSSOAccess({
        initiativeIds,
      })

      this.logger.info(`GreenProject applied change to ${result.length} users`, {
        initiativeIds,
        result,
      });
    } catch (e) {
      this.logger.error(new ContextError(`GreenProject failed to apply change`, {
        initiativeIds,
        cause: e,
      }));
    }
  }
}

let instance: GreenProjectEventListener;
export const getGreenProjectEventListener = () => {
  if (!instance) {
    instance = new GreenProjectEventListener(
      wwgLogger,
      getIntegrationRepository(),
      getGreenProjectService(),
    );
  }
  return instance;
}
