/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { getG<PERSON>ly<PERSON><PERSON>, Greenly<PERSON><PERSON> } from "./GreenlyApi";
import { LoggerInterface, wwgLogger } from "../../wwgLogger";
import {
  GeneratedUtr,
  GeneratedUtrv,
  IntegrationConfig,
  IntegrationData,
  IntegrationDataParams,
  IntegrationProvider,
  IntegrationService,
  SetupData
} from "../IntegrationProvider";
import config from '../../../config';
import { UtrValueType } from "../../../models/public/universalTrackerType";
import ContextError from "../../../error/ContextError";
import { getIntegrationRepository, IntegrationRepository } from "../IntegrationRepository";
import { ObjectId } from "bson";
import { DataPeriods } from "../../utr/constants";
import type { IntegrationConnectionPlain } from "../../../models/integrationConnection";
import { SurveyEmissionData } from "./greenlyTypes";
import { getUTCEndOf, subtractDate } from "../../../util/date";
import { getIntegrationNotificationService } from "../IntegrationNotificationService";
import { getSingleSignOnManager, SingleSignOnManager } from "../SingleSignOnManager";
import { getTableValues, greenlyTechUtrsData } from './fixtures';
import { UtrType } from "../../../models/universalTracker";
import { getQuestionData } from "../questionData";
import { IntegrationTaskCreate, IntegrationTaskStatus, IntegrationTaskType } from "../../../models/integrationTask";
import { ensureValidityStatus } from "./greenly-utils";
import { DashboardTemplateType, templateDashboard } from '../../insight-dashboard/template';
import { InsightDashboard } from '../../../models/insightDashboard';


export interface GreenlyData {
  answers: SetupData['generatedAnswers'];
  address?: SetupData['address'];
  externalId?: string;
}

type GreenlyConnection = IntegrationConnectionPlain<GreenlyData>;
type GreenlyConnectionCompleted = IntegrationConnectionPlain<Required<GreenlyData>>;

interface GenerateUtrvsParams {
  utr: GeneratedUtr;
  data: SurveyEmissionData[];
}

type GreenlyDataParams = IntegrationDataParams<GreenlyConnectionCompleted>;

export class GreenlyService implements IntegrationService {

  public readonly code = 'greenly';

  public readonly oktaGroupId = config.integrations.greenly.oktaGroupId;

  public readonly isEnabled = config.integrations.greenly.enabled;
  private readonly useMockData = config.integrations.greenly.useMockData;

  constructor(
    private readonly logger: LoggerInterface,
    private readonly api: GreenlyApi,
    private readonly repo: IntegrationRepository,
    private readonly notificationService: ReturnType<typeof getIntegrationNotificationService>,
    private readonly ssoManager: SingleSignOnManager,
    private insightDashboardModel: typeof InsightDashboard,
  ) {
  }

  public async getSetupConfig() {
    return {
      provider: this.getInfo(),
      integration: this.getConfig(),
    };
  }

  public async executeChecks() {
    this.logger.info(`Executing checks for ${this.code} service`);

    const companies = await this.api.listCompanies();
    this.logger.info(`Companies found: ${companies.length}`, { companies });

    if (!companies.length) {
      return { connectionIds: [] };
    }

    const initiativeIdToCompanyId = new Map<string, string>()
    const initiativeIds: ObjectId[] = [];

    companies.forEach((company) => {
      if (company.identifier?.value && ObjectId.isValid(company.identifier.value)) {
        initiativeIds.push(new ObjectId(company.identifier.value));
        initiativeIdToCompanyId.set(company.identifier.value, company.id)
      }
    });

    const connections = await this.repo.getConnectionsByProvider({
      integrationCode: this.code,
      initiativeId: { $in: initiativeIds },
      status: 'pending',
    });

    this.logger.info(`Pending connections found: ${connections.length}`, {
      providerCode: this.code,
      initiativeIds: connections.map(c => c.initiativeId)
    });

    for (const connection of connections) {
      connection.status = 'active';
      if (this.isValidConnection(connection)) {
        connection.data.externalId = initiativeIdToCompanyId.get(connection.initiativeId.toString());
        connection.markModified('data');
        this.sendActive(connection);
      }

      await connection.save();
      this.logger.info(`Connection upgraded to active`, {
        providerCode: this.code,
        connectionId: connection._id,
        initiativeId: connection.initiativeId,
      });

      try {
        const dashboard = await this.generateStaticDashboard({
          initiativeId: connection.initiativeId,
          creatorId: connection.createdBy,
        });
        this.logger.info(`Greenly dashboard generated`, {
          connectionId: connection._id,
          initiativeId: connection.initiativeId,
          dashboardId: dashboard._id,
        });
      } catch (error) {
        this.logger.error(
          new ContextError(`Failed to generate Greenly dashboard`, {
            cause: error,
            connectionId: connection._id,
            initiativeId: connection.initiativeId,
          })
        );
      }
    }

    this.enableSSOAccess({
      initiativeIds: connections.map((c) => c.initiativeId),
    }).catch(this.logger.error);

    return {
      connectionIds: connections.map((c) => c._id),
    };
  }

  private async generateStaticDashboard({ initiativeId, creatorId }: { initiativeId: ObjectId; creatorId: ObjectId }) {
    const dashboard = templateDashboard[DashboardTemplateType.Greenly];
    return this.insightDashboardModel.create({ ...dashboard, initiativeId, creatorId });
  }

  private sendActive(connection: GreenlyConnection) {
    this.notificationService.sendIntegrationActive({
      provider: this.getInfo(),
      connection,
      notificationData: {
        recipients: [{ id: connection.createdBy.toString() }],
      }
    }).catch((e) => {
      this.logger.error(new ContextError(`Failed to send integration ${this.code} activation notification`, {
        cause: e,
        providerCode: this.code,
      }));
    });
  }

  private getConfig() {
    return {
      type: 'external',
      requirements: {
        questions: [
          {
            _id: 'greenly-registration-details',
            type: UtrType.Generated,
            code: 'greenly-registration-details',
            valueLabel: 'Additional set up questions',
            name: 'Registration details',
            valueType: UtrValueType.Table,
            valueValidation: {
              table: {
                validation: {
                  maxRows: 1,
                },
                columns: [
                  {
                    code: 'greenly-employees',
                    name: 'Number of employees',
                    type: 'number',
                    validation: { required: true },
                  },
                  {
                    code: 'greenly-data-frequency',
                    name: 'Data frequency',
                    type: 'valueList',
                    options: ['Quarterly', 'Month', 'Yearly'].map((period) => ({ code: period, name: period })),
                    validation: { required: true },
                  },
                  {
                    code: 'greenly-revenue',
                    name: 'Total Revenue',
                    type: 'number',
                    unitType: 'currency',
                    unit: 'USD',
                    numberScale: 'millions',
                  },
                ]
              },

            }
          },
        ]
      }
    } satisfies IntegrationConfig;
  }

  /** Hardcoded generated utrs, but could later come from another async storage **/
  public async getAvailableQuestions(): Promise<GeneratedUtr[]> {
    return greenlyTechUtrsData.map(data => data.utr);
  }

  public async createSetup(setupData: SetupData) {
    const { rootInitiative, user, initiativeTree, integrationConnection } = setupData;
    this.logger.info(`Setting up Greenly ${rootInitiative.name}`, {
      initiativeId: rootInitiative._id,
      initiativeName: rootInitiative.name,
      integrationConnectionId: integrationConnection._id,
    });

    const additionalContext = getQuestionData(this.getConfig(), setupData);
    const year = new Date().getFullYear().toString();
    const language = 'en';
    const validityStatus = ensureValidityStatus(config.integrations.greenly.validityStatus);
    const type = 'standalone';

    const integrationTasks = await this.repo.getIntegrationTasks({
      integrationConnectionId: integrationConnection._id,
      initiativeIds: initiativeTree?.map(i => i._id) ?? [],
      type: IntegrationTaskType.SubConnection,
    });
    const existingMap = new Map(integrationTasks.map(t => [t.initiativeId.toString(), t]));

    const response = await this.api.createConnection({
      rootInitiative: {
        year,
        language,
        initiativeId: rootInitiative._id.toString(),
        name: rootInitiative.name,
        logo: rootInitiative.profile,
        industryLevel3: rootInitiative.industry?.icb2019.level3,
        validityStatus,
        parentInitiativeId: rootInitiative.parentId?.toString(),
        type,
        countryCode: rootInitiative.country,
      },
      user,
      additionalContext,
      initiativeTree: initiativeTree?.map(i => {
        const id = i._id.toString();
        return {
          initiativeId: id,
          name: i.name,
          logo: i.profile,
          year,
          language,
          industryLevel3: i.industry?.icb2019.level3,
          validityStatus,
          parentInitiativeId: i.parentId?.toString(),
          type,
          countryCode: i.country,
          externalId: existingMap.get(id)?.externalId,
        }
      }),
    });

    const activeDate = new Date();
    // Create sub connections for each child company
    const subTasksConnections = response.childrenCompanies.reduce((acc, childCompany) => {
      if (childCompany.identifier?.value && ObjectId.isValid(childCompany.identifier.value)) {
        acc.push({
          name: `Sub-Connection to ${childCompany.name}`,
          connectionId: integrationConnection._id,
          initiativeId: new ObjectId(childCompany.identifier.value),
          type: IntegrationTaskType.SubConnection,
          externalId: childCompany.id,
          status: IntegrationTaskStatus.Active,
          activeDate,
          // Nothing to store for now
          data: {},
        });
      }

      return acc;
    }, [] as IntegrationTaskCreate[]);

    const results = await this.repo.createSubConnection(subTasksConnections);
    this.logger.info(`Greenly ${results.length}/${subTasksConnections.length} sub connections created`, {
      integrationConnectionId: integrationConnection._id,
      initiativeId: rootInitiative._id,
      subTasksConnections: results.map(r => r._id),
    });

    this.logger.info(`Greenly setup completed for ${rootInitiative.name}`, response);
    return { success: true, data: response };
  }

  public getInfo(): IntegrationProvider {
    return {
      code: this.code,
      name: 'Greenly',
      shortName: 'Greenly',
      logo: `${config.assets.cdn}/carbon-calculators/greenly-logo.jpeg`,
      logoFooter: `${config.assets.cdn}/carbon-calculators/greenly-logo-v3.png`,
      color: '#3778FF11',
      icon: `${config.assets.cdn}/carbon-calculators/greenly-logo.jpeg`,
      link: 'https://greenly.earth/',
      tags: [
        'All industries',
        'Emissions report',
        'Dashboards',
        'Scope 1, 2 & 3',
        'GHG Protocol aligned'
      ],
      description: 'Greenly is a carbon accounting company that has supported 1,500+ companies measure, manage, and report on their carbon emissions. Through their starter plan (available for organisations with under 20 employees), you can easily get started. Greenly\'s full feature plans include dedicated support from a climate expert and account manager. The platform meets GHG Protocol standards, helping your organisation achieve relevant certifications and align with regulations.',
      highlights: [
        'All industries',
        'Scope 1,2,3 - spend based + activity based',
        'Dashboards & detailed GHG emissions report',
        'Final verification by a climate expert',
        'Account management support',
        'Reduction Plans included, SBTi support (higher plans only)'
      ],
    };
  }

  /**
   * Should cache this, require separate cache or start storing in DB
   * @param dataLookup
   */
  public async generateIntegrationData(dataLookup: IntegrationDataParams): Promise<IntegrationData["utrsData"]> {
    if (this.useMockData) {
      this.logger.info('Using mock data for Greenly');
      return greenlyTechUtrsData;
    }

    const { utrCodes, connection } = dataLookup;
    if (!this.isCompletedConnection(connection)) {
      this.logger.error(new ContextError(`Greenly connection ${connection._id} is not completed`, { connection }));
      return [];
    }

    const now = Date.now();
    const baseContext = {
      integrationCode: this.code,
      initiativeId: connection.initiativeId.toString(),
      companyId: connection.data.externalId
    };

    this.logger.info(`Generating Greenly data for ${utrCodes.length} UTRs`, { utrCodes, ...baseContext });

    const data = await this.processSurveys(connection.data.externalId, dataLookup as GreenlyDataParams);
    this.logger.info(`Completed Greenly data in ${(Date.now() - now)}ms`, baseContext);

    return data;
  }

  private async processSurveys(companyId: string, dataLookup: GreenlyDataParams) {
    const { utrCodes, connection, period = DataPeriods.Yearly } = dataLookup;

    const utrs = await this.getAvailableQuestions();
    const requiredUtrs = utrs.filter(utr => utrCodes.includes(utr.code));

    // Get years from date range
    const years = this.getYears(dataLookup);
    this.logger.info(`Fetching Greenly emissions data for ${years.length} years`, {
      years,
      requiredUtrs: requiredUtrs.map((utr) => utr.code),
    });

    const allEmissionData = await Promise.all(Array.from(years).map(async (year) => {
      return {
        year,
        data: await this.api.getEmissionsDataTotal(companyId, year).catch((e) => {
          this.logger.warn(new ContextError(`Failed to fetch Greenly emissions data for year ${year}`, {
            cause: e,
            initiativeId: connection.initiativeId.toString(),
          }));
          return [];
        })
      }
    }));

    const data = allEmissionData.reduce((acc, surveyData) => {
      const effectiveDate = getUTCEndOf('year', new Date(`${surveyData.year}-12-15T15:00:00`));
      if (surveyData.data.length) {
        acc.push({ emissionData: surveyData.data, effectiveDate, period })
      }

      return acc;
    }, [] as SurveyEmissionData[]);

    return requiredUtrs.map(utr => {
      const utrvs = this.generateUtrvs({ utr, data });
      return { integrationCode: this.code, utr, utrvs };
    });
  }

  /**
   * Get years from date range or fallback to 3 years
   */
  private getYears = (dataLookup: IntegrationDataParams<GreenlyConnectionCompleted>) => {
    const endDate = dataLookup.endDate ? new Date(dataLookup.endDate) : new Date();
    const startDate = dataLookup.startDate ? new Date(dataLookup.startDate) : subtractDate(endDate, 5, 'year');
    const startYear = startDate.getUTCFullYear();
    const endYear = endDate.getUTCFullYear();
    return Array.from({ length: endYear - startYear + 1 }, (_, i) => startYear + i);
  }

  /**
   * Only UTR tables, that we can map to properties directly that much Table codes
   */
  private generateUtrvs({ utr, data }: GenerateUtrvsParams): GeneratedUtrv[] {
    const utrvs: GeneratedUtrv[] = [];

    for (const { emissionData, effectiveDate, period } of data) {
      const columns = utr.valueValidation?.table?.columns;
      if (!columns) {
        this.logger.error(new ContextError(`Missing columns for generated UTR ${utr.code}`, {
          utrCode: utr.code,
          valueType: utr.valueType
        }));
        return [];
      }

      const rowData = getTableValues(columns, emissionData);
      const table = rowData.length > 0 ? [rowData] : [];

      utrvs.push({
        universalTrackerId: utr.code,
        utrCode: utr.code,
        valueType: UtrValueType.Table,
        valueData: { table },
        sourceType: 'manual',
        effectiveDate,
        period,
        type: 'actual',
        status: 'verified',
      })
    }

    return utrvs;
  }

  /**
   * We are responsible to manage who can authenticate with SSO
   * For now we just enabled all user that are part of the initial connections.
   */
  public async enableSSOAccess({ initiativeIds }: { initiativeIds: ObjectId[] }) {
    this.logger.info(`[Greenly] Enabling SSO access for ${initiativeIds.length} initiatives`, {
      integrationCode: this.code,
      initiativeIds: initiativeIds.map(String),
    });
    const results = await this.ssoManager.addUsersToGroup(this.oktaGroupId, initiativeIds);

    const { error, success } = results.reduce((acc, { status, userId }) => {
      acc[status].push(userId);
      return acc;
    }, { success: [], error: [] } as Record<typeof results[0]['status'], ObjectId[]>);

    this.logger.info(`[Greenly] SSO access enabled for ${success.length} users, failed ${error.length}`, {
      integrationCode: this.code,
      success: success.map(String),
      error: error.map(String),
    });

    return results;
  }

  private isValidConnection(connection: IntegrationConnectionPlain): connection is GreenlyConnection {
    if (connection.integrationCode !== this.code) {
      return false;
    }

    const { data } = connection;
    if (!data || typeof data !== 'object') {
      return false;
    }
    return 'answers' in data && Array.isArray(data.answers);
  }

  private isCompletedConnection(connection: IntegrationConnectionPlain): connection is GreenlyConnectionCompleted {
    if (connection.integrationCode !== this.code) {
      return false;
    }
    if (connection.status !== 'active') {
      return false;
    }

    const { data } = connection;
    if (!data || typeof data !== 'object') {
      return false;
    }
    return 'externalId' in data && !!data.externalId;
  }

  public async getExternalApp() {
    const info = this.getInfo();
    return {
      name: info.name,
      login: {
        url: config.integrations.greenly.loginUrl,
        text: 'Login to Greenly',
      }
    }
  }
}

let instance: GreenlyService;
export const getGreenlyService = () => {
  if (!instance) {
    instance = new GreenlyService(
      wwgLogger,
      getGreenlyApi(),
      getIntegrationRepository(),
      getIntegrationNotificationService(),
      getSingleSignOnManager(),
      InsightDashboard,
    );
  }
  return instance;
}
