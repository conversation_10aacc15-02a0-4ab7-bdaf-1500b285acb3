/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { UserEventListener } from "../../event/AppEventEmitter";
import { getGreenlyService } from "./GreenlyService";
import { getIntegrationRepository } from "../IntegrationRepository";
import { LoggerInterface, wwgLogger } from "../../wwgLogger";
import ContextError from "../../../error/ContextError";

export class GreenlyEventListener {

  constructor(
    private logger: LoggerInterface,
    private repo: ReturnType<typeof getIntegrationRepository>,
    private service: ReturnType<typeof getGreenlyService>,
  ) {
  }

  public onPermissionChange: UserEventListener = async (data) => {

    if (!this.service.isEnabled) {
      this.logger.debug(`${this.service.code} service is not enabled`);
      return;
    }

    const ids = data.map(event => event.initiativeId);
    if (ids.length === 0) {
      return;
    }

    const connections = await this.repo.getConnectionsByProvider({
      integrationCode: this.service.code,
      initiativeId: { $in: ids },
      status: 'active',
    });

    const initiativeIds = connections.map(c => c.initiativeId);
    if (initiativeIds.length === 0) {
      return;
    }

    try {
      const result = await this.service.enableSSOAccess({ initiativeIds })

      this.logger.info(`Greenly applied change to ${result.length} users`, {
        initiativeIds,
        result,
      });
    } catch (e) {
      this.logger.error(new ContextError(`Greenly failed to apply change`, {
        initiativeIds,
        cause: e,
      }));
    }
  }
}

let instance: GreenlyEventListener;
export const getGreenlyEventListener = () => {
  if (!instance) {
    instance = new GreenlyEventListener(
      wwgLogger,
      getIntegrationRepository(),
      getGreenlyService(),
    );
  }
  return instance;
}
