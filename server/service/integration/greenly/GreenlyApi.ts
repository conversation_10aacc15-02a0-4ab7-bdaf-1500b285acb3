/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import config from "../../../config";
import { SingleIdentityService } from "../api/SingleIdentityService";
import axios, { type AxiosInstance, isAxiosError } from "axios";
import { AuthInterceptor } from "../api/AuthInterceptor";
import {
  CreateGreenlyCompany,
  EntryEmissions,
  GreenlyCompany,
  GreenlyCreateData,
  OwnedCompaniesPayload,
  ServiceAccountMeReponse,
  GreenlyRegulatoryMethodology,
  CreateUser,
  GreenlyUserRole,
  OwnedCompanyResponse,
  SetupCompany,
} from "./greenlyTypes";
import ContextError from "../../../error/ContextError";
import { mapICB2019ToGreenlyIndustry, mapICB2019ToParentIndustry } from "./GreenlyIndustry";
import { LoggerInterface, wwgLogger } from "../../wwgLogger";
import { createGroupedBatches, MapOwnedCompaniesParams } from "./greenly-utils";


interface CreateUserData {
  user: GreenlyCreateData['user'];
  greenlyCompany: GreenlyCompany;
  childrenCompanies: GreenlyCompany[];
  rootInitiative: SetupCompany;
}

export class GreenlyApi {

  constructor(
    private readonly httpClient: AxiosInstance,
    private readonly logger: LoggerInterface,
  ) {
  }

  public async listCompanies() {
    return this.httpClient.get<ServiceAccountMeReponse>('/users/me', {
      params: {
        language: 'en',
      }
    }).then(res => res.data.companies);
  }

  public async getCompany(companyId: string): Promise<GreenlyCompany> {
    const companies = await this.listCompanies();
    const company = companies.find(c => c.id === companyId);
    if (!company) {
      throw new ContextError(`Company ${companyId} not found`, {
        companyId,
        availableIds: companies.map(c => c.id),
      });
    }
    return company;
  }

  private async createCompanies(companies: CreateGreenlyCompany[]): Promise<GreenlyCompany[]> {
    return this.httpClient.post('/companies', companies).then(res => res.data);
  }

  private async createUsers(companyId: string, users: CreateUser[]): Promise<string[]> {
    return this.httpClient.post(
      `/users`,
      { users },
      { headers: { 'Greenly-Company-Id': companyId } },
    ).then(res => res.data);
  }

  // Check if email is registered in Greenly (aka not available to create)
  private async isEmailRegistered(email: string): Promise<boolean> {
    try {
      const response = await this.httpClient.get(`/users/check-email`, { params: { email } });
      // 200 means it does not exist, 409 means it does
      if (response.status === 200) {
        return false;
      }
      if (response.status === 409) {
        return true;
      }

      throw new ContextError(`[GREENLY] Failed to check email ${email}`, {
        status: response.status,
        statusText: response.statusText,
        cause: response.data,
        debug: 'Unexpected status code',
      });

    } catch (e) {
      if (isAxiosError(e) && e.response?.status === 409) {
        return true;
      }
      throw e;
    }
  }

  /**
   * Invite a user to a company
   * Returns the IDs of the invited users
   */
  public async inviteUser(companyId: string, user: CreateUser): Promise<string[]> {

    const userToInvite = {
      jobTitle: user.jobTitle,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      email: user.email,
    };

    const response = await this.httpClient.post<{
      partialErrors: { errorDetail: string, errorLabel: string }[];
      idsInvited: string[];
      emailsInvited: string[];
    }>(
      `/companies/invite`,
      { usersToInvite: [userToInvite] },
      { headers: { 'Greenly-Company-Id': companyId } },
    );

    const data = response.data;

    const success = data.idsInvited.length > 0 || data.emailsInvited.length > 0;
    const successMsg = success ? 'success' : 'failed';
    const failedMsg = data.partialErrors.length > 0 ? '(with errors)' : '';

    this.logger.info(`[GREENLY] Inviting user ${user.email} to company ${companyId} ${successMsg} ${failedMsg}`, {
      userToInvite,
      companyId,
      idsInvited: data.idsInvited,
      emailsInvited: data.emailsInvited,
    });

    if (data.partialErrors.length > 0) {
      throw new ContextError(`[GREENLY] Failed to invite user to company ${companyId}`, {
        partialErrors: data.partialErrors,
        companyId,
      });
    }

    return data.idsInvited;
  }

  private async createOrInviteUser(company: GreenlyCompany, user: CreateUser): Promise<string[]> {
    const companyId = company.id;
    const emailExists = await this.isEmailRegistered(user.email);
    if (emailExists) {
      this.logger.warn(`[GREENLY] User already exists, inviting to company ${company.name}`, {
        email: user.email,
        greenlyCompanyId: companyId,
      });

      if (user.companyIds.length > 1) {
        this.logger.error(new ContextError(`[GREENLY] User invited with children companies, require manual intervention`, {
          email: user.email,
          greenlyCompanyId: companyId,
          otherCompanies: user.companyIds.filter(id => id !== companyId),
          debugMessage: `We only support inviting users to one company at a time. Reach out to Greenly support to fix this.`,
        }));
      }
      return this.inviteUser(companyId, user);
    }
    return this.createUsers(companyId, [user]);
  }

  /**
   * Add a user to a company
   * Returns 204 No Content
   */
  private async addUserToCompany(companyId: string, userId: string): Promise<void> {
    return this.httpClient.post(`/companies/${companyId}/users/${userId}`).then(res => res.data);
  }

  private async createOwnedCompanies(companyId: string, companies: OwnedCompaniesPayload): Promise<OwnedCompanyResponse> {
    return this.httpClient.post(`/companies/${companyId}/owned-companies`, companies).then(res => res.data);
  }

  private async createOrGetCompany(company: GreenlyCreateData['rootInitiative']): Promise<GreenlyCompany> {

    const initiativeId = company.initiativeId;
    const companies = await this.listCompanies();
    const existingCompany = companies.find(c => c.identifier?.value === initiativeId);
    if (existingCompany) {
      return existingCompany;
    }
    const countryId = company.countryCode || 'GB';

    const [greenlyCompany] = await this.createCompanies([{
      year: company.year,
      companyName: company.name,
      companyLogoUrl: company.logo,
      companyLanguage: company.language,
      countryId,
      headOfficeCountry: countryId,
      validityStatus: company.validityStatus,
      identifier: { type: 'operatorId', value: initiativeId },
      type: company.type,
      industry: mapICB2019ToGreenlyIndustry(company.industryLevel3),
      parentIndustry: mapICB2019ToParentIndustry(company.industryLevel3),
      organisationBoundaries: company.description,
      firstFiscalMonth: 'JANUARY',
      defaultRegulatoryMethodology: GreenlyRegulatoryMethodology.GHGProtocol,
      fiscalYearIsYearOfFirstMonth: true,
    }]);

    this.logger.info(`Greenly company created for ${company.name}`, {
      greenlyCompanyId: greenlyCompany.id,
    });

    return greenlyCompany;
  }

  private async createChildrenCompanies(company: GreenlyCompany, initiativeTree: GreenlyCreateData['initiativeTree']): Promise<GreenlyCompany[]> {

    if (!initiativeTree) {
      return [];
    }

    const existingCompanies = await this.listCompanies();
    // Initiative IDs we already attached on Greenly side
    const greenlyInitiativeIds = new Set(existingCompanies.map(c => c.identifier?.value));
    // Greenly IDs we are mapped to
    const greenlyCompanyIds = new Set(existingCompanies.map(c => c.id));

    // Ensure only missing ones are created.
    const newChildCompanies = initiativeTree.filter(i => {
      // Skip root initiative
      if (i.initiativeId === company.identifier?.value) {
        return false;
      }

      if (i.externalId && greenlyCompanyIds.has(i.externalId)) {
        return false;
      }
      return !greenlyInitiativeIds.has(i.initiativeId);
    });

    this.logger.info(`[GREENLY] Creating ${newChildCompanies.length} children companies for ${company.name}`, {
      initiativeIds: newChildCompanies.map(c => c.initiativeId),
      greenlyCompanyId: company.id,
    });

    if (newChildCompanies.length === 0) {
      return [];
    }

    return this.createCompanies(newChildCompanies.map(i => ({
      countryId: i.countryCode,
      headOfficeCountry: i.countryCode,
      validityStatus: i.validityStatus,
      identifier: { type: 'operatorId', value: i.initiativeId },
      type: i.type,
      industry: mapICB2019ToGreenlyIndustry(i.industryLevel3),
      parentIndustry: mapICB2019ToParentIndustry(i.industryLevel3),
      organisationBoundaries: i.description,
      firstFiscalMonth: 'JANUARY',
      defaultRegulatoryMethodology: GreenlyRegulatoryMethodology.GHGProtocol,
      fiscalYearIsYearOfFirstMonth: true,
      year: i.year,
      companyName: i.name,
      companyLogoUrl: i.logo,
      companyLanguage: i.language,
    })));
  }

  /**
   * Eventually this will be replaced by API call once enabled, now we rely on email
   */
  public async createConnection(createData: GreenlyCreateData) {

    this.logger.info(`[GREENLY] Creating connection for ${createData.rootInitiative.name}`, {
      initiativeId: createData.rootInitiative.initiativeId,
      initiativeName: createData.rootInitiative.name,
    });

    // Send data through email
    const { rootInitiative, user, initiativeTree } = createData;
    const greenlyCompany = await this.createOrGetCompany(rootInitiative);
    this.logger.info(`[GREENLY] Root company created for ${rootInitiative.name}`, {
      initiativeId: rootInitiative.initiativeId,
      greenlyCompanyId: greenlyCompany.id,
    });

    // Create all children companies
    const childrenCompanies = await this.createChildrenCompanies(greenlyCompany, initiativeTree);
    this.logger.info(`[GREENLY] ${childrenCompanies.length} children companies created for ${rootInitiative.name}`, {
      childrenCompanies: childrenCompanies.map(c => c.id),
    });

    // Create main user, but skip if it fails (like existing user on the platform)
    // We can still manually fix if needed.
    await this.createMainUser({ user, greenlyCompany, childrenCompanies, rootInitiative }).catch(e => {
      this.logger.error(new ContextError(`[GREENLY] Failed to create main user for ${rootInitiative.name}`, {
        initiativeId: rootInitiative.initiativeId,
        userId: user._id.toString(),
        greenlyCompanyId: greenlyCompany.id,
        cause: e,
      }));
    });

    this.mapOwnedCompanies({ greenlyCompany, childrenCompanies, rootInitiative, initiativeTree }).catch(e => {
      this.logger.error(new ContextError(`[GREENLY] Failed to map owned companies for ${rootInitiative.name}`, {
        initiativeId: rootInitiative.initiativeId,
        userId: user._id.toString(),
        greenlyCompanyId: greenlyCompany.id,
        cause: e,
      }));
    });

    return {
      greenlyCompany,
      childrenCompanies,
    };
  }

  private async createMainUser({ user, greenlyCompany, childrenCompanies, rootInitiative }: CreateUserData) {
    const userData = {
      email: user.email,
      firstName: user.firstName,
      lastName: user.surname,
      jobTitle: user.jobTitle,
      role: GreenlyUserRole.ProjectManager,
      companyIds: [greenlyCompany.id, ...childrenCompanies.map(c => c.id)],
    } satisfies CreateUser;

    const [greenlyUserIdOrEmail] = await this.createOrInviteUser(greenlyCompany, userData);
    this.logger.info(`[GREENLY] User created for ${rootInitiative.name}`, {
      userId: user._id.toString(),
      greenlyUserIdOrEmail,
      greenlyCompanyId: greenlyCompany.id,
    });

    if (!greenlyUserIdOrEmail) {
      throw new ContextError(`[GREENLY] Failed to create Greenly user for ${rootInitiative.name}`, {
        companyId: rootInitiative.initiativeId,
        userId: user._id.toString(),
        greenlyCompanyId: greenlyCompany.id,
      });
    }
  }

  private async mapOwnedCompanies(data: MapOwnedCompaniesParams) {

    const batches = createGroupedBatches(data);

    for (const { greenlyCompany, ownedCompanies } of batches) {
      this.logger.info(`[GREENLY] Creating owned companies for ${greenlyCompany.name ?? greenlyCompany.id}`, {
        greenlyCompanyId: greenlyCompany.id,
        ownedCompanies: ownedCompanies.map(c => c.ownedCompanyId),
      });

      const createdOwnedRelationship = await this.createOwnedCompanies(greenlyCompany.id, { ownedCompanies });

      this.logger.info(`[GREENLY] ${createdOwnedRelationship.numberOfCompanyAdded} owned companies associated for ${greenlyCompany.name}`, {
        ownedCompanies: createdOwnedRelationship.idsOfCompanyAdded,
        numberOfCompanyAdded: createdOwnedRelationship.numberOfCompanyAdded,
      });
    }
  }

  public async getEmissionsDataTotal(companyId: string, year: number, protocol = 'GHGProtocol'): Promise<EntryEmissions[]> {
    return this.httpClient
      .get<EntryEmissions[]>(`/reglementaryEntryEmissions`, {
        params: {
          reglementaryEntryEmissions: protocol,
          companyId,
          year,
        },
      })
      .then(res => res.data)
      .catch((e) => {
        throw new ContextError(`Failed to get emissions data for company ${companyId} and year ${year}`, {
          companyId,
          year,
          protocol,
          error: e.message,
          body: isAxiosError(e) ? e.response?.data : undefined,
        });
      });
  }
}

let instance: GreenlyApi;
export const getGreenlyApi = () => {
  if (!instance) {
    const { baseUrl, credentials, timeoutMs, tokenUrl } = config.integrations.greenly.api;
    const identityService = new SingleIdentityService({ credentials, baseUrl: tokenUrl });
    const httpClient = axios.create({ baseURL: baseUrl, timeout: timeoutMs });

    httpClient.interceptors.request.use(
      AuthInterceptor(identityService),
      (error) => {
        if (isAxiosError(error)) {
          throw new ContextError(`[GREENLY] API Error: ${error.config?.method ?? 'UNKNOWN'} ${error.config?.url ?? 'UNKNOWN'}`, {
            // Don't log error in production, it's too verbose, extra data is enough
            cause: config.isProduction ? undefined : error,
            url: error.config?.url,
            method: error.config?.method,
            statusCode: error.response?.status,
            statusText: error.response?.statusText,
            responseBody: error.response?.data,
          });
        }
        return Promise.reject(error);
      },
    );

    instance = new GreenlyApi(
      httpClient,
      wwgLogger,
    );
  }
  return instance;
}

