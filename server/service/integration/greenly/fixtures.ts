import { TableColumn, UtrValueType } from '../../../models/public/universalTrackerType';
import { UtrType } from '../../../models/universalTracker';
import { DataPeriods } from '../../utr/constants';
import { IntegrationData } from '../IntegrationProvider';
import { reglementaryEntries, reglementaryEntriesData } from "./reglementaryEntries";
import { tryCalculation } from "../../../rules/calculation/formula";
import { VariableMap } from "../../../rules/rule";
import { EntryEmissions } from "./greenlyTypes";


type Scope = 1 | 2 | 3;
const protocol = reglementaryEntries.GHGProtocol;
const getScopeEntries = (scope: Scope) => {
  const searchString = String(scope);
  return reglementaryEntries.GHGProtocol.filter((entry) => {
    return entry.regulatoryMethodologyCategory.startsWith(searchString);
  });
}

const getScopeColumns = (entries: typeof protocol, scope: Scope) => {
  return [
    ...entries.map(s => ({
      code: s.regulatoryMethodologyCategory,
      name: s.description.en,
      type: 'number',
      unit: 'kg',
      unitType: 'mass',
      numberScale: 'single',
    })),
    {
      code: `scope_${scope}_total`,
      name: `Scope ${scope} Total`,
      type: 'number',
      unit: 'kg',
      unitType: 'mass',
      numberScale: 'single',
      calculation: {
        formula: entries
          .map(s => `{${s.regulatoryMethodologyCategory}}`)
          .join(' + '),
      }
    },
  ];
}

const createColumns = (scope: Scope) => getScopeColumns(getScopeEntries(scope), scope);

const scopeOneColumns = createColumns(1);
const scopeTwoColumns = createColumns(2);
const scopeThreeColumns = createColumns(3);

export const getTableValues = (columns: TableColumn[], emissionData: EntryEmissions[]) => {
  const variables = emissionData
    .reduce((acc, entry) => {
      acc[entry.category] = entry.emissionsInKgCO2;
      return acc;
    }, {} as VariableMap);

  return columns.map(col => {

    if (col.calculation) {

      return {
        code: col.code,
        value: tryCalculation({ formula: col.calculation.formula, variables: variables }) as number | string,
        unit: col.unit,
        numberScale: col.numberScale,
      };


    }

    return {
      code: col.code,
      value: variables[col.code],
      unit: col.unit,
      numberScale: col.numberScale,
    };
  });
}

export const greenlyTechUtrsData: IntegrationData['utrsData'] = [
  {
    integrationCode: 'greenly',
    utr: {
      _id: 'greenly-total-scope-1',
      type: UtrType.Generated,
      code: 'greenly-total-scope-1',
      valueLabel: 'Total Scope 1 Emissions',
      name: 'Total Scope 1 Emissions',
      valueType: UtrValueType.Table,
      valueValidation: {
        table: {
          validation: {
            maxRows: 1,
          },
          columns: scopeOneColumns,
        },
      },
    },
    utrvs: [
      {
        universalTrackerId: 'greenly-total-scope-1',
        utrCode: 'greenly-total-scope-1',
        valueType: UtrValueType.Table,
        valueData: {
          table: [getTableValues(scopeOneColumns, reglementaryEntriesData)],
        },
        sourceType: 'manual',
        effectiveDate: new Date('2023-12-31T23:59:59.999Z'),
        period: DataPeriods.Yearly,
        type: 'actual',
        status: 'verified',
      },
    ],
  },
  {
    integrationCode: 'greenly',
    utr: {
      _id: 'greenly-total-scope-2',
      type: UtrType.Generated,
      code: 'greenly-total-scope-2',
      valueLabel: 'Total Scope 2 Emissions',
      name: 'Total Scope 2 Emissions',
      valueType: UtrValueType.Table,
      valueValidation: {
        table: {
          validation: {
            maxRows: 1,
          },
          columns: scopeTwoColumns,
        },
      },
    },
    utrvs: [
      {
        universalTrackerId: 'greenly-total-scope-2',
        utrCode: 'greenly-total-scope-2',
        valueType: UtrValueType.Table,
        valueData: {
          table: [getTableValues(scopeTwoColumns, reglementaryEntriesData)],
        },
        sourceType: 'manual',
        effectiveDate: new Date('2022-12-31T23:59:59.999Z'),
        period: DataPeriods.Yearly,
        type: 'actual',
        status: 'verified',
      },
    ],
  },
  {
    integrationCode: 'greenly',
    utr: {
      _id: 'greenly-total-scope-3',
      type: UtrType.Generated,
      code: 'greenly-total-scope-3',
      valueLabel: 'Total Scope 3 Emissions',
      name: 'Total Scope 3 Emissions',
      valueType: UtrValueType.Table,
      valueValidation: {
        table: {
          validation: {
            maxRows: 1,
          },
          columns: scopeThreeColumns,
        },
      },
    },
    utrvs: [
      {
        universalTrackerId: 'greenly-total-scope-3',
        utrCode: 'greenly-total-scope-3',
        valueType: UtrValueType.Table,
        valueData: {
          table: [getTableValues(scopeThreeColumns, reglementaryEntriesData)],
        },
        sourceType: 'manual',
        effectiveDate: new Date('2022-12-31T23:59:59.999Z'),
        period: DataPeriods.Yearly,
        type: 'actual',
        status: 'verified',
      },
    ],
  },
];
