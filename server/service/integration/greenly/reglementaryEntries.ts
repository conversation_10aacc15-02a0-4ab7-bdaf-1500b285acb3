/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

/**
 * Snapshot of the Greenly API for Reglementary Entries - GHG Protocol
 * @link: https://api.greenly.tech/api/v2/reglementary-entries
 */
export const reglementaryEntries = {
  "GHGProtocol": [
    {
      "regulatoryMethodologyCategory": "1.1",
      "description": {
        "fr": "Production d'électricité, de chaleur ou de vapeur",
        "en": "Generation of electricity, heat or steam"
      },
      "frenchOfficialCategory": 1,
      "scope": 1
    },
    {
      "regulatoryMethodologyCategory": "1.2",
      "description": {
        "fr": "Transport de matériaux, de produits, de déchets et d'employés",
        "en": "Transportation of materials, products, waste, and employees"
      },
      "frenchOfficialCategory": 1,
      "scope": 1
    },
    {
      "regulatoryMethodologyCategory": "1.3",
      "description": {
        "fr": "Transformation physique ou chimique",
        "en": "Physical or chemical processing"
      },
      "frenchOfficialCategory": 1,
      "scope": 1
    },
    {
      "regulatoryMethodologyCategory": "1.4",
      "description": {
        "fr": "Émissions fugitives",
        "en": "Fugitive emissions"
      },
      "frenchOfficialCategory": 1,
      "scope": 1
    },
    {
      "regulatoryMethodologyCategory": "2.1",
      "description": {
        "fr": "Émissions indirectes liées à l'électricité",
        "en": "Electricity related indirect emissions"
      },
      "frenchOfficialCategory": 2,
      "scope": 2
    },
    {
      "regulatoryMethodologyCategory": "2.2",
      "description": {
        "fr": "Émissions indirectes liées à la vapeur, au réseau de chaleur et de froid",
        "en": "Steam, heat and cooling related indirect emissions"
      },
      "frenchOfficialCategory": 2,
      "scope": 2
    },
    {
      "regulatoryMethodologyCategory": "3.1",
      "description": {
        "fr": "Achats de biens et de services",
        "en": "Purchased goods and services"
      },
      "frenchOfficialCategory": 4,
      "scope": 3
    },
    {
      "regulatoryMethodologyCategory": "3.2",
      "description": {
        "fr": "Biens immobilisés",
        "en": "Capital goods"
      },
      "frenchOfficialCategory": 4,
      "scope": 3
    },
    {
      "regulatoryMethodologyCategory": "3.3",
      "description": {
        "fr": "Activités liées aux combustibles et à l'énergie non incluses dans le scope 1 ou 2",
        "en": "Fuel- and energy- related activities not included in Scope 1 or Scope 2"
      },
      "frenchOfficialCategory": 4,
      "scope": 3
    },
    {
      "regulatoryMethodologyCategory": "3.4",
      "description": {
        "fr": "Transport de marchandises amont et distribution",
        "en": "Upstream transportation and distribution"
      },
      "frenchOfficialCategory": 3,
      "scope": 3
    },
    {
      "regulatoryMethodologyCategory": "3.5",
      "description": {
        "fr": "Déchets générés",
        "en": "Waste generated in operations"
      },
      "frenchOfficialCategory": 4,
      "scope": 3
    },
    {
      "regulatoryMethodologyCategory": "3.6",
      "description": {
        "fr": "Déplacements professionnels",
        "en": "Business travel"
      },
      "frenchOfficialCategory": 3,
      "scope": 3
    },
    {
      "regulatoryMethodologyCategory": "3.7",
      "description": {
        "fr": "Déplacements domicile-travail",
        "en": "Employee commuting"
      },
      "frenchOfficialCategory": 3,
      "scope": 3
    },
    {
      "regulatoryMethodologyCategory": "3.8",
      "description": {
        "fr": "Actifs en leasing amont",
        "en": "Upstream leased assets"
      },
      "frenchOfficialCategory": 4,
      "scope": 3
    },
    {
      "regulatoryMethodologyCategory": "3.9",
      "description": {
        "fr": "Transport de marchandises aval et distribution",
        "en": "Downstream transportation and distribution"
      },
      "frenchOfficialCategory": 3,
      "scope": 3
    },
    {
      "regulatoryMethodologyCategory": "3.10",
      "description": {
        "fr": "Transformation des produits vendus",
        "en": "Processing of sold products"
      },
      "frenchOfficialCategory": 5,
      "scope": 3
    },
    {
      "regulatoryMethodologyCategory": "3.11",
      "description": {
        "fr": "Utilisation des produits vendus",
        "en": "Use of sold products"
      },
      "frenchOfficialCategory": 5,
      "scope": 3
    },
    {
      "regulatoryMethodologyCategory": "3.12",
      "description": {
        "fr": "Fin de vie des produits vendus",
        "en": "End-of-life treatment of sold products"
      },
      "frenchOfficialCategory": 5,
      "scope": 3
    },
    {
      "regulatoryMethodologyCategory": "3.13",
      "description": {
        "fr": "Actifs en leasing aval",
        "en": "Downstream leased assets"
      },
      "frenchOfficialCategory": 5,
      "scope": 3
    },
    {
      "regulatoryMethodologyCategory": "3.14",
      "description": {
        "fr": "Franchises",
        "en": "Franchises"
      },
      "frenchOfficialCategory": 6,
      "scope": 3
    },
    {
      "regulatoryMethodologyCategory": "3.15",
      "description": {
        "fr": "Investissements",
        "en": "Investments"
      },
      "frenchOfficialCategory": 5,
      "scope": 3
    },
    {
      "regulatoryMethodologyCategory": "4.1",
      "description": {
        "fr": "Autres émissions - Émissions issues de la biomasse (sols et forêts)",
        "en": "Other emissions - Emissions from biomass (soil and forests)"
      },
      "frenchOfficialCategory": 1,
      "scope": 4
    },
    {
      "regulatoryMethodologyCategory": "4.2",
      "description": {
        "fr": "Autres émissions - Déplacements des visiteurs et des clients",
        "en": "Other emissions - Visitor and customer transport"
      },
      "frenchOfficialCategory": 3,
      "scope": 5
    },
    {
      "regulatoryMethodologyCategory": "4.3",
      "description": {
        "fr": "Autres émissions - Autres émissions indirectes",
        "en": "Other emissions - Other Indirect Emissions"
      },
      "frenchOfficialCategory": 6,
      "scope": 6
    }
  ]
}


/**
 * Snapshot of the Greenly API for Reglementary Entries - GHG Protocol
 * https://api.greenly.tech/api/v2/reglementaryEntryEmissions?
 *  year=2023&
 *  reportingMethodology=GHGProtocol&
 *  companyId=b834027b-e8b2-4cf2-9e5a-3cc552bd847f
 */
export const reglementaryEntriesData = [
  {
    "category": "1.1",
    "description": {
      "fr": "Émissions directes des sources fixes de combustion",
      "en": "Direct emissions from stationary combustion sources"
    },
    "emissionsInKgCO2": 138081.51,
    "kgCO2": 94366.78142430134,
    "kgCH4fossilAsCO2e": 8636.736605945713,
    "kgCH4biogenicAsCO2e": 3662.633185113783,
    "kgN2OAsCO2e": 31415.357673331255,
    "kgOtherAsCO2e": 0,
    "kgCO2Biogenic": 0,
    "frenchOfficialCategory": 1,
    "scope": 1
  },
  {
    "category": "1.2",
    "description": {
      "fr": "Émissions directes des sources mobiles de combustion",
      "en": "Direct emissions from mobile combustion sources"
    },
    "emissionsInKgCO2": 112288.75592099999,
    "kgCO2": 76969.94864503382,
    "kgCH4fossilAsCO2e": 10819.136871655226,
    "kgCH4biogenicAsCO2e": 2624.482063220042,
    "kgN2OAsCO2e": 21875.18761126369,
    "kgOtherAsCO2e": 0,
    "kgCO2Biogenic": 0,
    "frenchOfficialCategory": 1,
    "scope": 1
  },
  {
    "category": "1.3",
    "description": {
      "fr": "Émissions directes des procédés hors énergie",
      "en": "Direct emissions from physical or chemical processing (other than energy use)"
    },
    "emissionsInKgCO2": 0,
    "kgCO2": 0,
    "kgCH4fossilAsCO2e": 0,
    "kgCH4biogenicAsCO2e": 0,
    "kgN2OAsCO2e": 0,
    "kgOtherAsCO2e": 0,
    "kgCO2Biogenic": 0,
    "frenchOfficialCategory": 1,
    "scope": 1
  },
  {
    "category": "1.4",
    "description": {
      "fr": "Émissions directes fugitives",
      "en": "Direct fugitive emissions"
    },
    "emissionsInKgCO2": 73392.84224999997,
    "kgCO2": 0,
    "kgCH4fossilAsCO2e": 0,
    "kgCH4biogenicAsCO2e": 0,
    "kgN2OAsCO2e": 0,
    "kgOtherAsCO2e": 73392.84224999997,
    "kgCO2Biogenic": 0,
    "frenchOfficialCategory": 1,
    "scope": 1
  },
  {
    "category": "1.5",
    "description": {
      "fr": "Émissions issues de la biomasse (sols et forêts)",
      "en": "Emissions from biomass (soil and forests)"
    },
    "emissionsInKgCO2": 1450000,
    "kgCO2": 1450000,
    "kgCH4fossilAsCO2e": 0,
    "kgCH4biogenicAsCO2e": 0,
    "kgN2OAsCO2e": 0,
    "kgOtherAsCO2e": 0,
    "kgCO2Biogenic": 0,
    "frenchOfficialCategory": 1,
    "scope": 1
  },
  {
    "category": "2.1",
    "description": {
      "fr": "Émissions indirectes liées à la consommation d'électricité",
      "en": "Indirect emissions from electricity consumption"
    },
    "emissionsInKgCO2": 1071807.9572579998,
    "kgCO2": 910911.831856681,
    "kgCH4fossilAsCO2e": 56065.460701382355,
    "kgCH4biogenicAsCO2e": 53562.35344117982,
    "kgN2OAsCO2e": 51268.311257969566,
    "kgOtherAsCO2e": 0,
    "kgCO2Biogenic": 0,
    "frenchOfficialCategory": 2,
    "scope": 2
  },
  {
    "category": "2.2",
    "description": {
      "fr": "Émissions indirectes liées à la consommation d'énergie autre que l'électricité",
      "en": "Indirect emissions from energy consumption (other than electricity)"
    },
    "emissionsInKgCO2": 0,
    "kgCO2": 0,
    "kgCH4fossilAsCO2e": 0,
    "kgCH4biogenicAsCO2e": 0,
    "kgN2OAsCO2e": 0,
    "kgOtherAsCO2e": 0,
    "kgCO2Biogenic": 0,
    "frenchOfficialCategory": 2,
    "scope": 2
  },
  {
    "category": "3.1",
    "description": {
      "fr": "Transport de marchandise amont",
      "en": "Upstream transport"
    },
    "emissionsInKgCO2": 2002460.9329980933,
    "kgCO2": 1738541.78247708,
    "kgCH4fossilAsCO2e": 137155.5634920191,
    "kgCH4biogenicAsCO2e": 0,
    "kgN2OAsCO2e": 126763.5870289937,
    "kgOtherAsCO2e": 0,
    "kgCO2Biogenic": 0,
    "frenchOfficialCategory": 3,
    "scope": 3
  },
  {
    "category": "3.2",
    "description": {
      "fr": "Transport de marchandise aval",
      "en": "Downstream transport and distribution"
    },
    "emissionsInKgCO2": 157233.48449143345,
    "kgCO2": 136510.51957530458,
    "kgCH4fossilAsCO2e": 10769.472107976811,
    "kgCH4biogenicAsCO2e": 0,
    "kgN2OAsCO2e": 9953.492808151943,
    "kgOtherAsCO2e": 0,
    "kgCO2Biogenic": 0,
    "frenchOfficialCategory": 3,
    "scope": 3
  },
  {
    "category": "3.3",
    "description": {
      "fr": "Déplacements domicile-travail",
      "en": "Commuting"
    },
    "emissionsInKgCO2": 621542.1330102115,
    "kgCO2": 493356.5655461284,
    "kgCH4fossilAsCO2e": 40601.57814985109,
    "kgCH4biogenicAsCO2e": 5176.320614330178,
    "kgN2OAsCO2e": 70542.4949071907,
    "kgOtherAsCO2e": 11865.172225984872,
    "kgCO2Biogenic": 0,
    "frenchOfficialCategory": 3,
    "scope": 3
  },
  {
    "category": "3.4",
    "description": {
      "fr": "Déplacements des visiteurs et des clients",
      "en": "Visitor and customer transport"
    },
    "emissionsInKgCO2": 2.3792,
    "kgCO2": 2.0656276188502334,
    "kgCH4fossilAsCO2e": 0.16295974182709436,
    "kgCH4biogenicAsCO2e": 0,
    "kgN2OAsCO2e": 0.15061263932267135,
    "kgOtherAsCO2e": 0,
    "kgCO2Biogenic": 0,
    "frenchOfficialCategory": 3,
    "scope": 3
  },
  {
    "category": "3.5",
    "description": {
      "fr": "Déplacements professionnels",
      "en": "Business travel"
    },
    "emissionsInKgCO2": 297635.9359588572,
    "kgCO2": 266626.*********,
    "kgCH4fossilAsCO2e": 16115.019746734251,
    "kgCH4biogenicAsCO2e": 0,
    "kgN2OAsCO2e": 14894.020017335764,
    "kgOtherAsCO2e": 0,
    "kgCO2Biogenic": 0,
    "frenchOfficialCategory": 3,
    "scope": 3
  },
  {
    "category": "4.1",
    "description": {
      "fr": "Achats de biens",
      "en": "Purchases of goods"
    },
    "emissionsInKgCO2": 10306602.3575813,
    "kgCO2": 8855159.*********,
    "kgCH4fossilAsCO2e": 961541.0330990537,
    "kgCH4biogenicAsCO2e": 4700.814978758001,
    "kgN2OAsCO2e": 357633.5382911875,
    "kgOtherAsCO2e": 117372.***********,
    "kgCO2Biogenic": 0,
    "frenchOfficialCategory": 4,
    "scope": 3
  },
  {
    "category": "4.2",
    "description": {
      "fr": "Immobilisations de biens",
      "en": "Capital goods"
    },
    "emissionsInKgCO2": 3063703.2091142857,
    "kgCO2": 3063703.2091142857,
    "kgCH4fossilAsCO2e": 0,
    "kgCH4biogenicAsCO2e": 0,
    "kgN2OAsCO2e": 0,
    "kgOtherAsCO2e": 0,
    "kgCO2Biogenic": 0,
    "frenchOfficialCategory": 4,
    "scope": 3
  },
  {
    "category": "4.3",
    "description": {
      "fr": "Gestion des déchets",
      "en": "Waste management"
    },
    "emissionsInKgCO2": 433116.758,
    "kgCO2": 316721.27012481954,
    "kgCH4fossilAsCO2e": 33476.02246067765,
    "kgCH4biogenicAsCO2e": 0,
    "kgN2OAsCO2e": 82919.46541450312,
    "kgOtherAsCO2e": 0,
    "kgCO2Biogenic": 0,
    "frenchOfficialCategory": 4,
    "scope": 3
  },
  {
    "category": "4.4",
    "description": {
      "fr": "Actifs en leasing amont",
      "en": "Upstream leased assets"
    },
    "emissionsInKgCO2": 217814.54522857146,
    "kgCO2": 217814.54522857146,
    "kgCH4fossilAsCO2e": 0,
    "kgCH4biogenicAsCO2e": 0,
    "kgN2OAsCO2e": 0,
    "kgOtherAsCO2e": 0,
    "kgCO2Biogenic": 0,
    "frenchOfficialCategory": 4,
    "scope": 3
  },
  {
    "category": "4.5",
    "description": {
      "fr": "Achats de services",
      "en": "Purchases of services"
    },
    "emissionsInKgCO2": 72690.1863455,
    "kgCO2": 62916.99079134753,
    "kgCH4fossilAsCO2e": 6447.619528845848,
    "kgCH4biogenicAsCO2e": 0,
    "kgN2OAsCO2e": 2391.5071307669505,
    "kgOtherAsCO2e": 934.0688945396751,
    "kgCO2Biogenic": 0,
    "frenchOfficialCategory": 4,
    "scope": 3
  },
  {
    "category": "5.1",
    "description": {
      "fr": "Utilisation des produits vendus",
      "en": "Use of sold goods"
    },
    "emissionsInKgCO2": 0,
    "kgCO2": 0,
    "kgCH4fossilAsCO2e": 0,
    "kgCH4biogenicAsCO2e": 0,
    "kgN2OAsCO2e": 0,
    "kgOtherAsCO2e": 0,
    "kgCO2Biogenic": 0,
    "frenchOfficialCategory": 5,
    "scope": 3
  },
  {
    "category": "5.2",
    "description": {
      "fr": "Actifs en leasing aval",
      "en": "Downstream leased assets"
    },
    "emissionsInKgCO2": 0,
    "kgCO2": 0,
    "kgCH4fossilAsCO2e": 0,
    "kgCH4biogenicAsCO2e": 0,
    "kgN2OAsCO2e": 0,
    "kgOtherAsCO2e": 0,
    "kgCO2Biogenic": 0,
    "frenchOfficialCategory": 5,
    "scope": 3
  },
  {
    "category": "5.3",
    "description": {
      "fr": "Fin de vie des produits vendus",
      "en": "End-of-life treatment of sold products"
    },
    "emissionsInKgCO2": 185.072,
    "kgCO2": 135.33588304274434,
    "kgCH4fossilAsCO2e": 14.304397865950348,
    "kgCH4biogenicAsCO2e": 0,
    "kgN2OAsCO2e": 35.43171909130546,
    "kgOtherAsCO2e": 0,
    "kgCO2Biogenic": 0,
    "frenchOfficialCategory": 5,
    "scope": 3
  },
  {
    "category": "5.4",
    "description": {
      "fr": "Investissements",
      "en": "Investments"
    },
    "emissionsInKgCO2": 0,
    "kgCO2": 0,
    "kgCH4fossilAsCO2e": 0,
    "kgCH4biogenicAsCO2e": 0,
    "kgN2OAsCO2e": 0,
    "kgOtherAsCO2e": 0,
    "kgCO2Biogenic": 0,
    "frenchOfficialCategory": 5,
    "scope": 3
  },
  {
    "category": "6.1",
    "description": {
      "fr": "Autres émissions indirectes",
      "en": "Other indirect emissions"
    },
    "emissionsInKgCO2": 1060686.2267571045,
    "kgCO2": 775638.1685476425,
    "kgCH4fossilAsCO2e": 81981.4871966978,
    "kgCH4biogenicAsCO2e": 0,
    "kgN2OAsCO2e": 203066.5710127651,
    "kgOtherAsCO2e": 0,
    "kgCO2Biogenic": 0,
    "frenchOfficialCategory": 6,
    "scope": 3
  }
]
