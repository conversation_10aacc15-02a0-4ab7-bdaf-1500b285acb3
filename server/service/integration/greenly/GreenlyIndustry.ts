export enum GreenlyIndustry {
  AccountingFirm = 'AccountingFirm',
  AssetFundManagement = 'AssetFundManagement',
  AeronauticsIndustry = 'AeronauticsIndustry',
  AgricultureAndAgriculturalProduction = 'AgricultureAndAgriculturalProduction',
  Airports = 'Airports',
  AirTreatment = 'AirTreatment',
  AmateurSportsClub = 'AmateurSportsClub',
  ArtTradeAndTrading = 'ArtTradeAndTrading',
  AssociationsAndFoundations = 'AssociationsAndFoundations',
  AudiovisualProductionAndGraphicDesign = 'AudiovisualProductionAndGraphicDesign',
  AutomotiveRepairAndMaintenance = 'AutomotiveRepairAndMaintenance',
  BagsAndOtherFashionAccesories = 'BagsAndOtherFashionAccesories',
  BakeryAndPastry = 'BakeryAndPastry',
  BankingAndSavingsSoluition = 'BankingAndSavingsSoluition',
  BeautyAndWellnessInstitutes = 'BeautyAndWellnessInstitutes',
  BeddingAndHouseholdLinen = 'BeddingAndHouseholdLinen',
  Beekeeping = 'Beekeeping',
  Beverages = 'Beverages',
  BicycleAndScooterRental = 'BicycleAndScooterRental',
  BlogsAndWebsite = 'BlogsAndWebsite',
  BoilermakingAndIndustrialPiping = 'BoilermakingAndIndustrialPiping',
  BookingOfTravelAccomodationAndTouristActivities = 'BookingOfTravelAccomodationAndTouristActivities',
  BookshopsAndOnlineBookstores = 'BookshopsAndOnlineBookstores',
  Brokerage = 'Brokerage',
  BuildingMaterials = 'BuildingMaterials',
  BusinessNetworksAndFederationsAndAssociations = 'BusinessNetworksAndFederationsAndAssociations',
  Camping = 'Camping',
  CarbonOffsettingAndEcosystemRestoration = 'CarbonOffsettingAndEcosystemRestoration',
  CarpoolingPlatform = 'CarpoolingPlatform',
  CarRentalAndSale = 'CarRentalAndSale',
  CatetersAndFoodtrucks = 'CatetersAndFoodtrucks',
  CentralPurchasingAgency = 'CentralPurchasingAgency',
  CerealsAndGrainProducts = 'CerealsAndGrainProducts',
  CleaningService = 'CleaningService',
  CoachingAndPersonalSupport = 'CoachingAndPersonalSupport',
  CoffeeAndHerbalism = 'CoffeeAndHerbalism',
  ColivingManager = 'ColivingManager',
  CollectiveRestaurant = 'CollectiveRestaurant',
  CommunicationAndMarketingAgency = 'CommunicationAndMarketingAgency',
  ConstructionAndPublicWorks = 'ConstructionAndPublicWorks',
  ConsultingFirm = 'ConsultingFirm',
  CosmeticsCareAndPerfume = 'CosmeticsCareAndPerfume',
  CoursierAndPostalServices = 'CoursierAndPostalServices',
  CoworkingAreas = 'CoworkingAreas',
  DairyProduct = 'DairyProduct',
  DigitalCreationAgencyAndITServicesCompany = 'DigitalCreationAgencyAndITServicesCompany',
  DigitalMediaAndPaper = 'DigitalMediaAndPaper',
  DigitalSecurity = 'DigitalSecurity',
  DoItYourselfStores = 'DoItYourselfStores',
  EnergyProducer = 'EnergyProducer',
  EnergyStorage = 'EnergyStorage',
  EnergySuppliers = 'EnergySuppliers',
  EngineeringAndDesignOffice = 'EngineeringAndDesignOffice',
  EventAgency = 'EventAgency',
  EventEngineering = 'EventEngineering',
  ExtractionAndTransformationOfEnergyResources = 'ExtractionAndTransformationOfEnergyResources',
  FeedForAnimals = 'FeedForAnimals',
  Festivals = 'Festivals',
  FinancingPlatform = 'FinancingPlatform',
  FloristGardeCentreAndSeeds = 'FloristGardeCentreAndSeeds',
  FoodAndMarketPlace = 'FoodAndMarketPlace',
  FoodTrade = 'FoodTrade',
  FoodWholesaler = 'FoodWholesaler',
  Freelance = 'Freelance',
  FreightManagementPlatformAndSoftware = 'FreightManagementPlatformAndSoftware',
  FruitsAndVegetables = 'FruitsAndVegetables',
  FurnitureAndDecoration = 'FurnitureAndDecoration',
  GamesAndToys = 'GamesAndToys',
  GeneralConstructorAndPrimeConstructor = 'GeneralConstructorAndPrimeConstructor',
  GiftVouchersAndRestaurantVouchers = 'GiftVouchersAndRestaurantVouchers',
  GreenhousesAndVegetableGardens = 'GreenhousesAndVegetableGardens',
  GymAndFitnessRoom = 'GymAndFitnessRoom',
  HairdressersAndBarber = 'HairdressersAndBarber',
  HeadHunters = 'HeadHunters',
  HeavyAndUtilityVehicles = 'HeavyAndUtilityVehicles',
  HotelBnBAndInn = 'HotelBnBAndInn',
  HygieneProducts = 'HygieneProducts',
  ImportExportAndTrading = 'ImportExportAndTrading',
  IncubatorsAndAccelerators = 'IncubatorsAndAccelerators',
  IndustrialWasteManagement = 'IndustrialWasteManagement',
  InfantFeeding = 'InfantFeeding',
  InsectBreedingAndProduction = 'InsectBreedingAndProduction',
  Insurance = 'Insurance',
  InteriorAndExteriorDesign = 'InteriorAndExteriorDesign',
  ITServicesAndDigitalEcommerce = 'ITServicesAndDigitalEcommerce',
  JewelleryAndGoldsmithShop = 'JewelleryAndGoldsmithShop',
  JoineryAndFrames = 'JoineryAndFrames',
  LabelsAndRatingAgencies = 'LabelsAndRatingAgencies',
  LandscapingAndGardening = 'LandscapingAndGardening',
  LargeScaleDistribution = 'LargeScaleDistribution',
  LawFirm = 'LawFirm',
  LogisticsStorageOfGoods = 'LogisticsStorageOfGoods',
  Luxury = 'Luxury',
  ManagementCompanies = 'ManagementCompanies',
  ManagementOfVendingMachine = 'ManagementOfVendingMachine',
  ManufactureAndReconditionningOfElectricalAndElectronicEquipment = 'ManufactureAndReconditionningOfElectricalAndElectronicEquipment',
  ManufactureAndSaleOfCleaningProducts = 'ManufactureAndSaleOfCleaningProducts',
  MarketplaceAndEcommercePlatform = 'MarketplaceAndEcommercePlatform',
  Materials = 'Materials',
  MealsForSaleOnline = 'MealsForSaleOnline',
  MedicalAndWellnessDevices = 'MedicalAndWellnessDevices',
  MiscellaneousProducts = 'MiscellaneousProducts',
  MiscellaneousServices = 'MiscellaneousServices',
  MiscellaneousTrade = 'MiscellaneousTrade',
  Nursery = 'Nursery',
  OccupationalHealthAndSafetyService = 'OccupationalHealthAndSafetyService',
  Optician = 'Optician',
  OrganisationOfSeminarsAndCompanyTrips = 'OrganisationOfSeminarsAndCompanyTrips',
  OrganizerOfEventsForIndividuals = 'OrganizerOfEventsForIndividuals',
  OutdoorActivities = 'OutdoorActivities',
  ParkAndNaturalSite = 'ParkAndNaturalSite',
  PassengerTransport = 'PassengerTransport',
  PaymentSystemAndPlatform = 'PaymentSystemAndPlatform',
  PharmaceuticalIndustry = 'PharmaceuticalIndustry',
  PhotographersAndVideographers = 'PhotographersAndVideographers',
  PlatformSoftwareAndApplications = 'PlatformSoftwareAndApplications',
  Pneumatics = 'Pneumatics',
  PowerGenerationTechnologies = 'PowerGenerationTechnologies',
  PressRelationsAgencyAndAdvertisingAgencies = 'PressRelationsAgencyAndAdvertisingAgencies',
  PrintingAndPrintingService = 'PrintingAndPrintingService',
  ProcessedFoodProducts = 'ProcessedFoodProducts',
  ProfessionalEvents = 'ProfessionalEvents',
  ProfessionalSportsClub = 'ProfessionalSportsClub',
  PromotionalItemsAndCorporateGifts = 'PromotionalItemsAndCorporateGifts',
  PublicBankAndOtherFinancialInstitution = 'PublicBankAndOtherFinancialInstitution',
  PublicTransport = 'PublicTransport',
  PublishingHouse = 'PublishingHouse',
  ReadyToWearAndTextile = 'ReadyToWearAndTextile',
  RealEstateAgent = 'RealEstateAgent',
  RealEstateAndPropertyManagement = 'RealEstateAndPropertyManagement',
  RealEstateConsultancy = 'RealEstateConsultancy',
  RecruitmentAgenciesAndPlatforms = 'RecruitmentAgenciesAndPlatforms',
  RentalAndSaleOfBoats = 'RentalAndSaleOfBoats',
  RentalAndSaleOfScootersAndMotorcycles = 'RentalAndSaleOfScootersAndMotorcycles',
  RepairOfElectronicEquipment = 'RepairOfElectronicEquipment',
  ResearchCenter = 'ResearchCenter',
  Restaurant = 'Restaurant',
  SaleAndRentalOfElectricalAndElectronicEquipment = 'SaleAndRentalOfElectricalAndElectronicEquipment',
  SearchEngine = 'SearchEngine',
  SecurityGuardingAndConciergeServices = 'SecurityGuardingAndConciergeServices',
  ShowroomsAndTheatres = 'ShowroomsAndTheatres',
  SignageAndUrbanEquipement = 'SignageAndUrbanEquipement',
  SportsEvent = 'SportsEvent',
  StationeryAndPrintedObjects = 'StationeryAndPrintedObjects',
  TaxisVTCAndEventTransport = 'TaxisVTCAndEventTransport',
  Telecommunications = 'Telecommunications',
  ToolsAndIndustrialMachinery = 'ToolsAndIndustrialMachinery',
  TouristGuide = 'TouristGuide',
  TouristOffice = 'TouristOffice',
  TowedVehicles = 'TowedVehicles',
  TradeInSportsEquipments = 'TradeInSportsEquipments',
  TrainingAndAwareness = 'TrainingAndAwareness',
  TwoWheelersAndLightVehicles = 'TwoWheelersAndLightVehicles',
  UniversitiesAndColleges = 'UniversitiesAndColleges',
  UrbanAgriculture = 'UrbanAgriculture',
  VenusForReceptionsAndSeminars = 'VenusForReceptionsAndSeminars',
  VideoGameRoom = 'VideoGameRoom',
  VideoGames = 'VideoGames',
  VitaminsAndDietarySupplements = 'VitaminsAndDietarySupplements',
  WasteManagementAndReuse = 'WasteManagementAndReuse',
  WaterManagement = 'WaterManagement',
  WebAndCloudHosting = 'WebAndCloudHosting',
  WrappingAndPackaging = 'WrappingAndPackaging',
  PublicAdministration = 'PublicAdministration',
  LocalAuthority = 'LocalAuthority',
  SocialSecurityAdministration = 'SocialSecurityAdministration',
  PublicAdministrativeEstablishment = 'PublicAdministrativeEstablishment',
  PublicEstablishmentOfIndustrialAndCommercialNature = 'PublicEstablishmentOfIndustrialAndCommercialNature',
  TownHall = 'TownHall',
  Prefecture = 'Prefecture',
  Ministry = 'Ministry',
  Embassy = 'Embassy',
  Consulate = 'Consulate'
}

export enum ParentIndustry {
  FoodAndAgriculture = 'FoodAndAgriculture',
  ArtAndCulture = 'ArtAndCulture',
  AssociationsAndFederations = 'AssociationsAndFederations',
  AuditAndConsulting = 'AuditAndConsulting',
  AutomobilesAndVehicles = 'AutomobilesAndVehicles',
  BankingAndInsurance = 'BankingAndInsurance',
  TradeAndDistribution = 'TradeAndDistribution',
  CommunicationAndMarketing = 'CommunicationAndMarketing',
  CosmeticsAndWellBeing = 'CosmeticsAndWellBeing',
  DigitalAndSoftware = 'DigitalAndSoftware',
  PublishingAndMedia = 'PublishingAndMedia',
  Energy = 'Energy',
  EducationAndTraining = 'EducationAndTraining',
  CareAndMaintening = 'CareAndMaintening',
  EnvironmentAndWasteManagement = 'EnvironmentAndWasteManagement',
  Event = 'Event',
  Freelance = 'Freelance',
  RealEstate = 'RealEstate',
  PrintingAndPackaging = 'PrintingAndPackaging',
  IndustryAndEngineering = 'IndustryAndEngineering',
  ITAndTelecom = 'ITAndTelecom',
  GamesAndSports = 'GamesAndSports',
  HomeAndGardening = 'HomeAndGardening',
  ElectricalEquipment = 'ElectricalEquipment',
  Fashion = 'Fashion',
  FoodService = 'FoodService',
  Health = 'Health',
  PersonalAndProfessionalServices = 'PersonalAndProfessionalServices',
  TourismAndHotelBusiness = 'TourismAndHotelBusiness',
  TransportAndLogistics = 'TransportAndLogistics',
  PublicSector = 'PublicSector'
}

/**
 * Maps ICB2019 industry codes to GreenlyIndustry.
 *
 * Mapping Strategy:
 * 1. First attempts to map using Level 3 (Sector) codes for most precise mapping
 * 2. Falls back to Level 2 (Super Sector) if no Level 3 match is found
 * 3. Finally falls back to Level 1 (Industry) if no Level 2 match is found
 *
 * The mapping prioritizes specific Greenly industries where possible, but uses
 * broader categories when ICB2019 sectors encompass multiple Greenly industries.
 *
 * Example usage:
 * ```typescript
 * const industry = mapICB2019ToGreenlyIndustry('201030'); // Returns GreenlyIndustry.PharmaceuticalIndustry
 * ```
 */

// Map ICB2019 level 3 - Sector (3) to GreenlyIndustry
export const ICB2019ToGreenlyIndustry: { [key: string]: GreenlyIndustry } = {
  // Technology
  '101010': GreenlyIndustry.DigitalCreationAgencyAndITServicesCompany, // Software & Computer Services
  '101020': GreenlyIndustry.SaleAndRentalOfElectricalAndElectronicEquipment, // Technology Hardware & Equipment

  // Telecommunications
  '151010': GreenlyIndustry.Telecommunications, // Telecommunications Equipment
  '151020': GreenlyIndustry.Telecommunications, // Telecommunications Service Providers

  // Health Care
  '201010': GreenlyIndustry.MedicalAndWellnessDevices, // Health Care Providers
  '201020': GreenlyIndustry.MedicalAndWellnessDevices, // Medical Equipment & Services
  '201030': GreenlyIndustry.PharmaceuticalIndustry, // Pharmaceuticals, Biotechnology & Marijuana Producers

  // Financials
  '301010': GreenlyIndustry.BankingAndSavingsSoluition, // Banks
  '302010': GreenlyIndustry.FinancingPlatform, // Finance & Credit Services
  '302020': GreenlyIndustry.Brokerage, // Investment Banking & Brokerage Services
  '302030': GreenlyIndustry.RealEstateAndPropertyManagement, // Mortgage Real Estate Investment Trusts
  '302040': GreenlyIndustry.AssetFundManagement, // Equity Investment Instruments
  '302050': GreenlyIndustry.AssetFundManagement, // Nonequity Investment Instruments
  '303010': GreenlyIndustry.Insurance, // Life Insurance
  '303020': GreenlyIndustry.Insurance, // Non-life Insurance

  // Real Estate
  '351010': GreenlyIndustry.RealEstateAndPropertyManagement, // Real Estate Investment & Services
  '351020': GreenlyIndustry.RealEstateAndPropertyManagement, // Real Estate Investment Trusts

  // Consumer Discretionary
  '401010': GreenlyIndustry.HeavyAndUtilityVehicles, // Automobiles & Parts
  '402010': GreenlyIndustry.MiscellaneousServices, // Consumer Services
  '402020': GreenlyIndustry.FurnitureAndDecoration, // Household Goods & Home Construction
  '402030': GreenlyIndustry.GamesAndToys, // Leisure Goods
  '402040': GreenlyIndustry.Luxury, // Personal Goods
  '403010': GreenlyIndustry.DigitalMediaAndPaper, // Media
  '404010': GreenlyIndustry.LargeScaleDistribution, // Retailers
  '405010': GreenlyIndustry.HotelBnBAndInn, // Travel & Leisure

  // Consumer Staples
  '451010': GreenlyIndustry.Beverages, // Beverages
  '451020': GreenlyIndustry.ProcessedFoodProducts, // Food Producers
  '451030': GreenlyIndustry.MiscellaneousProducts, // Tobacco
  '452010': GreenlyIndustry.LargeScaleDistribution, // Personal Care, Drug & Grocery Stores

  // Industrials
  '501010': GreenlyIndustry.ConstructionAndPublicWorks, // Construction & Materials
  '502010': GreenlyIndustry.AeronauticsIndustry, // Aerospace & Defense
  '502020': GreenlyIndustry.ManufactureAndReconditionningOfElectricalAndElectronicEquipment, // Electronic & Electrical Equipment
  '502030': GreenlyIndustry.MiscellaneousProducts, // General Industrials
  '502040': GreenlyIndustry.EngineeringAndDesignOffice, // Industrial Engineering
  '502050': GreenlyIndustry.MiscellaneousServices, // Industrial Support Services
  '502060': GreenlyIndustry.LogisticsStorageOfGoods, // Industrial Transportation

  // Basic Materials
  '551010': GreenlyIndustry.Materials, // Industrial Materials
  '551020': GreenlyIndustry.Materials, // Industrial Metals & Mining
  '551030': GreenlyIndustry.Materials, // Precious Metals & Mining
  '552010': GreenlyIndustry.Materials, // Chemicals

  // Energy
  '601010': GreenlyIndustry.ExtractionAndTransformationOfEnergyResources, // Non-Renewable Energy
  '601020': GreenlyIndustry.EnergyProducer, // Renewable Energy

  // Utilities
  '651010': GreenlyIndustry.EnergySuppliers, // Electricity
  '651020': GreenlyIndustry.WaterManagement, // Gas, Water & Multi-utilities
  '651030': GreenlyIndustry.WasteManagementAndReuse, // Waste & Disposal Services
};

// Map ICB2019 level 2 - Super Sector (2) to GreenlyIndustry
export const ICB2019Level2ToGreenlyIndustry: { [key: string]: GreenlyIndustry } = {
  // Technology
  '1010': GreenlyIndustry.DigitalCreationAgencyAndITServicesCompany, // Technology

  // Telecommunications
  '1510': GreenlyIndustry.Telecommunications, // Telecommunications

  // Health Care
  '2010': GreenlyIndustry.MedicalAndWellnessDevices, // Health Care

  // Financials
  '3010': GreenlyIndustry.BankingAndSavingsSoluition, // Banks
  '3020': GreenlyIndustry.FinancingPlatform, // Financial Services
  '3030': GreenlyIndustry.Insurance, // Insurance

  // Real Estate
  '3510': GreenlyIndustry.RealEstateAndPropertyManagement, // Real Estate

  // Consumer Discretionary
  '4010': GreenlyIndustry.HeavyAndUtilityVehicles, // Automobiles & Parts
  '4020': GreenlyIndustry.MiscellaneousServices, // Consumer Products & Services
  '4030': GreenlyIndustry.DigitalMediaAndPaper, // Media
  '4040': GreenlyIndustry.LargeScaleDistribution, // Retail
  '4050': GreenlyIndustry.HotelBnBAndInn, // Travel & Leisure

  // Consumer Staples
  '4510': GreenlyIndustry.ProcessedFoodProducts, // Food, Beverage & Tobacco
  '4520': GreenlyIndustry.LargeScaleDistribution, // Personal Care, Drug & Grocery Stores

  // Industrials
  '5010': GreenlyIndustry.ConstructionAndPublicWorks, // Construction & Materials
  '5020': GreenlyIndustry.EngineeringAndDesignOffice, // Industrial Goods & Services

  // Basic Materials
  '5510': GreenlyIndustry.Materials, // Basic Resources
  '5520': GreenlyIndustry.Materials, // Chemicals

  // Energy
  '6010': GreenlyIndustry.EnergyProducer, // Energy

  // Utilities
  '6510': GreenlyIndustry.EnergySuppliers, // Utilities
};

// Map ICB2019 level 1 - Industry (1) to GreenlyIndustry
export const ICB2019Level1ToGreenlyIndustry: { [key: string]: GreenlyIndustry } = {
  '10': GreenlyIndustry.DigitalCreationAgencyAndITServicesCompany, // Technology
  '15': GreenlyIndustry.Telecommunications, // Telecommunications
  '20': GreenlyIndustry.MedicalAndWellnessDevices, // Health Care
  '30': GreenlyIndustry.BankingAndSavingsSoluition, // Financials
  '35': GreenlyIndustry.RealEstateAndPropertyManagement, // Real Estate
  '40': GreenlyIndustry.MiscellaneousServices, // Consumer Discretionary
  '45': GreenlyIndustry.ProcessedFoodProducts, // Consumer Staples
  '50': GreenlyIndustry.EngineeringAndDesignOffice, // Industrials
  '55': GreenlyIndustry.Materials, // Basic Materials
  '60': GreenlyIndustry.EnergyProducer, // Energy
  '65': GreenlyIndustry.EnergySuppliers, // Utilities
};

export const mapICB2019ToGreenlyIndustry = (icb2019: string | undefined): GreenlyIndustry | undefined => {
  if (!icb2019) {
    return undefined;
  }

  // Try Level 3 mapping first (most specific)
  const level3Code = icb2019.substring(0, 6);
  const level3Mapping = ICB2019ToGreenlyIndustry[level3Code];
  if (level3Mapping) {
    return level3Mapping;
  }

  // Try Level 2 mapping if Level 3 not found
  const level2Code = icb2019.substring(0, 4);
  const level2Mapping = ICB2019Level2ToGreenlyIndustry[level2Code];
  if (level2Mapping) {
    return level2Mapping;
  }

  // Try Level 1 mapping if Level 2 not found
  const level1Code = icb2019.substring(0, 2);
  return ICB2019Level1ToGreenlyIndustry[level1Code];
}

// Map ICB2019 level 3 - Sector (3) to ParentIndustry
export const ICB2019ToParentIndustry: { [key: string]: ParentIndustry } = {
  // Technology
  '101010': ParentIndustry.DigitalAndSoftware, // Software & Computer Services
  '101020': ParentIndustry.ElectricalEquipment, // Technology Hardware & Equipment

  // Telecommunications
  '151010': ParentIndustry.ITAndTelecom, // Telecommunications Equipment
  '151020': ParentIndustry.ITAndTelecom, // Telecommunications Service Providers

  // Health Care
  '201010': ParentIndustry.Health, // Health Care Providers
  '201020': ParentIndustry.Health, // Medical Equipment & Services
  '201030': ParentIndustry.Health, // Pharmaceuticals, Biotechnology & Marijuana Producers

  // Financials
  '301010': ParentIndustry.BankingAndInsurance, // Banks
  '302010': ParentIndustry.BankingAndInsurance, // Finance & Credit Services
  '302020': ParentIndustry.BankingAndInsurance, // Investment Banking & Brokerage Services
  '302030': ParentIndustry.RealEstate, // Mortgage Real Estate Investment Trusts
  '302040': ParentIndustry.BankingAndInsurance, // Equity Investment Instruments
  '302050': ParentIndustry.BankingAndInsurance, // Nonequity Investment Instruments
  '303010': ParentIndustry.BankingAndInsurance, // Life Insurance
  '303020': ParentIndustry.BankingAndInsurance, // Non-life Insurance

  // Real Estate
  '351010': ParentIndustry.RealEstate, // Real Estate Investment & Services
  '351020': ParentIndustry.RealEstate, // Real Estate Investment Trusts

  // Consumer Discretionary
  '401010': ParentIndustry.AutomobilesAndVehicles, // Automobiles & Parts
  '402010': ParentIndustry.PersonalAndProfessionalServices, // Consumer Services
  '402020': ParentIndustry.HomeAndGardening, // Household Goods & Home Construction
  '402030': ParentIndustry.GamesAndSports, // Leisure Goods
  '402040': ParentIndustry.Fashion, // Personal Goods
  '403010': ParentIndustry.PublishingAndMedia, // Media
  '404010': ParentIndustry.TradeAndDistribution, // Retailers
  '405010': ParentIndustry.TourismAndHotelBusiness, // Travel & Leisure

  // Consumer Staples
  '451010': ParentIndustry.FoodAndAgriculture, // Beverages
  '451020': ParentIndustry.FoodAndAgriculture, // Food Producers
  '451030': ParentIndustry.FoodAndAgriculture, // Tobacco
  '452010': ParentIndustry.TradeAndDistribution, // Personal Care, Drug & Grocery Stores

  // Industrials
  '501010': ParentIndustry.IndustryAndEngineering, // Construction & Materials
  '502010': ParentIndustry.IndustryAndEngineering, // Aerospace & Defense
  '502020': ParentIndustry.ElectricalEquipment, // Electronic & Electrical Equipment
  '502030': ParentIndustry.IndustryAndEngineering, // General Industrials
  '502040': ParentIndustry.IndustryAndEngineering, // Industrial Engineering
  '502050': ParentIndustry.PersonalAndProfessionalServices, // Industrial Support Services
  '502060': ParentIndustry.TransportAndLogistics, // Industrial Transportation

  // Basic Materials
  '551010': ParentIndustry.IndustryAndEngineering, // Industrial Materials
  '551020': ParentIndustry.IndustryAndEngineering, // Industrial Metals & Mining
  '551030': ParentIndustry.IndustryAndEngineering, // Precious Metals & Mining
  '552010': ParentIndustry.IndustryAndEngineering, // Chemicals

  // Energy
  '601010': ParentIndustry.Energy, // Non-Renewable Energy
  '601020': ParentIndustry.Energy, // Renewable Energy

  // Utilities
  '651010': ParentIndustry.Energy, // Electricity
  '651020': ParentIndustry.EnvironmentAndWasteManagement, // Gas, Water & Multi-utilities
  '651030': ParentIndustry.EnvironmentAndWasteManagement, // Waste & Disposal Services
};

// Map ICB2019 level 2 - Super Sector (2) to ParentIndustry
export const ICB2019Level2ToParentIndustry: { [key: string]: ParentIndustry } = {
  // Technology
  '1010': ParentIndustry.DigitalAndSoftware, // Technology

  // Telecommunications
  '1510': ParentIndustry.ITAndTelecom, // Telecommunications

  // Health Care
  '2010': ParentIndustry.Health, // Health Care

  // Financials
  '3010': ParentIndustry.BankingAndInsurance, // Banks
  '3020': ParentIndustry.BankingAndInsurance, // Financial Services
  '3030': ParentIndustry.BankingAndInsurance, // Insurance

  // Real Estate
  '3510': ParentIndustry.RealEstate, // Real Estate

  // Consumer Discretionary
  '4010': ParentIndustry.AutomobilesAndVehicles, // Automobiles & Parts
  '4020': ParentIndustry.PersonalAndProfessionalServices, // Consumer Products & Services
  '4030': ParentIndustry.PublishingAndMedia, // Media
  '4040': ParentIndustry.TradeAndDistribution, // Retail
  '4050': ParentIndustry.TourismAndHotelBusiness, // Travel & Leisure

  // Consumer Staples
  '4510': ParentIndustry.FoodAndAgriculture, // Food, Beverage & Tobacco
  '4520': ParentIndustry.TradeAndDistribution, // Personal Care, Drug & Grocery Stores

  // Industrials
  '5010': ParentIndustry.IndustryAndEngineering, // Construction & Materials
  '5020': ParentIndustry.IndustryAndEngineering, // Industrial Goods & Services

  // Basic Materials
  '5510': ParentIndustry.IndustryAndEngineering, // Basic Resources
  '5520': ParentIndustry.IndustryAndEngineering, // Chemicals

  // Energy
  '6010': ParentIndustry.Energy, // Energy

  // Utilities
  '6510': ParentIndustry.EnvironmentAndWasteManagement, // Utilities
};

// Map ICB2019 level 1 - Industry (1) to ParentIndustry
export const ICB2019Level1ToParentIndustry: { [key: string]: ParentIndustry } = {
  '10': ParentIndustry.DigitalAndSoftware, // Technology
  '15': ParentIndustry.ITAndTelecom, // Telecommunications
  '20': ParentIndustry.Health, // Health Care
  '30': ParentIndustry.BankingAndInsurance, // Financials
  '35': ParentIndustry.RealEstate, // Real Estate
  '40': ParentIndustry.PersonalAndProfessionalServices, // Consumer Discretionary
  '45': ParentIndustry.FoodAndAgriculture, // Consumer Staples
  '50': ParentIndustry.IndustryAndEngineering, // Industrials
  '55': ParentIndustry.IndustryAndEngineering, // Basic Materials
  '60': ParentIndustry.Energy, // Energy
  '65': ParentIndustry.EnvironmentAndWasteManagement, // Utilities
};

export const mapICB2019ToParentIndustry = (icb2019: string | undefined): ParentIndustry | undefined => {
  if (!icb2019) {
    return undefined;
  }

  // Try Level 3 mapping first (most specific)
  const level3Code = icb2019.substring(0, 6);
  const level3Mapping = ICB2019ToParentIndustry[level3Code];
  if (level3Mapping) {
    return level3Mapping;
  }

  // Try Level 2 mapping if Level 3 not found
  const level2Code = icb2019.substring(0, 4);
  const level2Mapping = ICB2019Level2ToParentIndustry[level2Code];
  if (level2Mapping) {
    return level2Mapping;
  }

  // Try Level 1 mapping if Level 2 not found
  const level1Code = icb2019.substring(0, 2);
  return ICB2019Level1ToParentIndustry[level1Code];
}
