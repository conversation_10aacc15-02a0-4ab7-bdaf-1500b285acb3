/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { getNotificationService } from "../notification/NotificationService";
import { IntegrationProvider } from "./IntegrationProvider";
import {
  CreateNotificationData,
  CustomAttributes,
  NotificationCategory,
  NotificationPage
} from "../notification/NotificationTypes";
import { UrlMapper } from "../url/UrlMapper";
import { IntegrationConnectionPlain } from "../../models/integrationConnection";
import { getRootInitiativeService } from "../organization/RootInitiativeService";
import Initiative from "../../models/initiative";

interface IntegrationActive {
  provider: IntegrationProvider;
  connection: IntegrationConnectionPlain;
  notificationData: Pick<CreateNotificationData, "recipients">;
}

export class IntegrationNotificationService {

  constructor(
    private notificationService: ReturnType<typeof getNotificationService>,
    private rootInitiativeService: ReturnType<typeof getRootInitiativeService>,
  ) {
  }


  public async sendIntegrationActive({ provider, connection, notificationData }: IntegrationActive) {

    const initiativeId = connection.initiativeId;
    const initiative = await Initiative.findById(initiativeId).lean().orFail().exec();
    const org = await this.rootInitiativeService.getOrganization(initiative);

    const customAttributes: CustomAttributes = {
      orgId: org?._id.toString(),
      appConfigCode: org?.appConfigCode,
      page: NotificationPage.IntegrationApp,
      domain: undefined,
      initiativeId: String(initiativeId),
      integrationCode: provider.code,
      integrationStatus: 'active',
    };

    const title = `${provider.name} setup have been completed`;
    await this.notificationService.createNotification({
      title: title,
      content:  'You can now start using the integration',
      category: NotificationCategory.Announcements,
      topic: `integration-${provider.code}`,
      customAttributes: customAttributes,
      actionUrl: UrlMapper.notificationUrl(customAttributes),
      recipients: notificationData.recipients,
      overrides: {
        email: {
          actionText: 'VIEW INTEGRATION',
        },
      },
    })
  }
}

let instance: IntegrationNotificationService;
export const getIntegrationNotificationService = () => {
  if (!instance) {
    instance = new IntegrationNotificationService(
      getNotificationService(),
      getRootInitiativeService(),
    );
  }
  return instance;
}
