/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { IntegrationConfig, SetupData } from "./IntegrationProvider";
import { UtrValueType } from "../../models/public/universalTrackerType";
import ContextError from "../../error/ContextError";

export const getQuestionData = (setupConfig: IntegrationConfig, setupData: SetupData) => {
  return setupConfig.requirements.questions.map((question) => {
    const answer = setupData.generatedAnswers.find((answer) => answer.utrCode === question.code);
    if (answer) {

      if (question.valueType === UtrValueType.Number) {
        return { code: question.code, name: question.name, value: answer.value };
      }

      // Must be single row table
      if (question.valueType === UtrValueType.Table && question.valueValidation?.table?.validation?.maxRows === 1) {
        const tableColumns = question.valueValidation?.table?.columns ?? [];
        const row = answer.valueData?.table?.[0] ?? [];
        return tableColumns.map((column) => {
          const columnAnswer = row.find((cell) => cell.code === column.code);
          return {
            code: column.code,
            name: column.name,
            value: columnAnswer?.value,
            unit: columnAnswer?.unit,
          };
        })
      }

      throw new ContextError(`Not supported value type: ${question.valueType}`, { providerCode: setupData.providerCode });
    }

    return { code: question.code, name: question.code, value: undefined };
  }).flat();
}
