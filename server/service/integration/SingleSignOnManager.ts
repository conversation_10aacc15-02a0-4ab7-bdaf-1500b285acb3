/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { ObjectId } from "bson";
import { getOktaManager } from "../user/OktaManager";
import { UserInitiativeRepository } from "../../repository/UserInitiativeRepository";
import { processInBatches } from "../../util/batch";
import { LoggerInterface, wwgLogger } from "../wwgLogger";
import ContextError from "../../error/ContextError";


interface GroupResult {
  groupId: string;
  userId: ObjectId;
  status: 'success' | 'error';
}

export interface SingleSignOnManager {
  addUsersToGroup(groupId: string, initiativeIds: ObjectId[]): Promise<GroupResult[]>;
}

export class OktaSSOManager implements SingleSignOnManager {

  constructor(
    private logger: LoggerInterface,
    private oktaManager: ReturnType<typeof getOktaManager>,
  ) {
  }

  public async addUsersToGroup(groupId: string, initiativeIds: ObjectId[]): Promise<GroupResult[]> {

    // Resolve user Ids
    const users = await UserInitiativeRepository.findOktaUserIds(initiativeIds);

    // Add users to group
    return processInBatches({
      data: users,
      processFn: async (user) => {
        const result: GroupResult = { groupId, userId: user._id, status: 'success' };

        return this.oktaManager.getUser(user.oktaUserId)
          .then((oktaUser) => oktaUser.addToGroup(groupId))
          .then(() => result)
          .catch((err: Error) => {
            this.logger.error(new ContextError(`Failed to add user ${user._id} to group ${groupId}`, {
              userId: user._id,
              groupId,
              cause: err,
            }));
            result.status = 'error';
            return result satisfies GroupResult;
          });
      },
      batchSize: 10,
      // Actual limit is 100 request per minute, but we want to be safe
      // for get user and addToGroup requests
      // lets make limit half of what we can process, not to impact other requests
      // Which means it take minimum 6 seconds to process 10 users
      batchRateLimit: { minWaitTime: 6, unit: 'second' },
      onBatchComplete: (results, count, total) => {
        this.logger.info(`Processed ${count} of ${total} users`, {
          userIds: results
        });
      }
    });
  }
}

let instance: SingleSignOnManager;
export const getSingleSignOnManager = (oktaManager?: ReturnType<typeof getOktaManager>) => {
  if (!instance) {
    instance = new OktaSSOManager(
      wwgLogger,
      oktaManager ?? getOktaManager(),
    );
  }
  return instance;
}



