/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


export interface BaseCredentials {
  clientId: string;
  clientSecret: string;
  grantType?: 'client_credentials';
}

export interface Credentials extends BaseCredentials {
  grantType: 'client_credentials';
  audience?: string;
}

export type OAuthParams = { token: string; } | { credentials: Credentials };

export interface AuthResponse {
  access_token: string;
  scope?: string;
  expires_in: number;
  token_type?: 'Bearer'
}

export interface TokenService {
  getToken: (params: OAuthParams | undefined) => Promise<string>;
  getAuthorizationValue: (params: OAuthParams | undefined) => Promise<string>;
}

