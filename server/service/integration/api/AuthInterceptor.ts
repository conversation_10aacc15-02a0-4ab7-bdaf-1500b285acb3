/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { InternalAxiosRequestConfig } from 'axios';
import { OAuthParams, TokenService } from "./TokenService";


interface AuthConfig extends InternalAxiosRequestConfig {
  authentication?: OAuthParams
}

export const AuthInterceptor = (tokenService: TokenService) => async (config: AuthConfig) => {
  const headerValue = await tokenService.getAuthorizationValue(config.authentication)
  config.headers.set('Authorization', headerValue);
  return config;
}
