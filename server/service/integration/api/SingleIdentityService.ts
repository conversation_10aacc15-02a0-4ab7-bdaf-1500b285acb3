/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { AuthResponse, Credentials, OAuthParams, TokenService } from "./TokenService";
import axios, { AxiosInstance } from "axios";

interface IdentityConfig {
  credentials: Credentials
  baseUrl: string,
  headerPrefix?: string;
}

export class SingleIdentityService<T extends AuthResponse = AuthResponse> implements TokenService {

  // In-memory cache, could be replaced by redis
  storage = { token: '', expiryTimestamp: 0 };

  private readonly httpClient: AxiosInstance;
  private readonly headerPrefix: string;

  constructor(
    private readonly config: IdentityConfig
  ) {

    this.headerPrefix = config.headerPrefix ?? 'Bearer';
    this.httpClient = axios.create({
      baseURL: this.config.baseUrl,
    })
  }

  private async getOAuthToken(credentials: Credentials) {
    const params = new URLSearchParams();
    params.append('grant_type', credentials.grantType ?? 'client_credentials');
    if (credentials.audience) {
      params.append('audience', credentials.audience);
    }
    return this.httpClient.post<T>(
      `${this.config.baseUrl}/oauth/token`,
      params,
      {
        auth: {
          username: credentials.clientId,
          password: credentials.clientSecret,
        },
      }).then(resp => resp.data)
  }

  public async getToken(params?: OAuthParams): Promise<string> {

    if (params && 'token' in params) {
      return params.token
    }

    const now = Date.now();

    if (this.storage.expiryTimestamp > now) {
      return this.storage.token;
    }

    const credentials = params?.credentials ?? this.config.credentials;
    const data = await this.getOAuthToken(credentials);

    this.storage = {
      token: data.access_token,
      expiryTimestamp: now + (data.expires_in * 1000),
    }

    return data.access_token;
  }

  public async getAuthorizationValue(params: OAuthParams | undefined): Promise<string> {
    return `${this.headerPrefix} ${await this.getToken(params)}`;
  }
}
