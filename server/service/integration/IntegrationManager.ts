/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import ContextError from "../../error/ContextError";
import { LoggerInterface, wwgLogger } from "../wwgLogger";
import { IntegrationCheck, IntegrationConnectionInfo, IntegrationData, IntegrationService, ProviderSetupInfo, SetupData } from "./IntegrationProvider";
import { getGreenProjectService } from "./green-project/GreenProjectService";
import { getIntegrationRepository } from "./IntegrationRepository";
import { ObjectId } from "bson";
import { IntegrationConnectionPlain } from "../../models/integrationConnection";
import { DataPeriods } from "../utr/constants";
import config from "../../config";
import { greenProjectTechUtrsData } from './green-project/fixtures';
import { getGreenlyService } from "./greenly/GreenlyService";

/**
 * Ideally all of these would be moved to separate API in the future.
 * Try to contain all the logic in the service folder
 */
export class IntegrationManager {

  constructor(
    private readonly logger: LoggerInterface,
    private readonly repo: ReturnType<typeof getIntegrationRepository>,
    private readonly integrationServices: IntegrationService[],
  ) {
  }

  public async executeChecks(providerCodes?: string[]) {

    const providers = providerCodes ? this.integrationServices.filter(p => providerCodes.includes(p.code)) : this.integrationServices;
    const codes = providers.map(p => p.code);

    this.logger.info(`Integrations - executing checks for ${providers.length} providers`, {
      providers: codes,
      providerCodes,
    });

    for (const provider of providers) {
      if (typeof provider.executeChecks === 'function') {
        await provider.executeChecks().catch((error) => {
          this.logger.warn(new ContextError(`Failed to execute checks for provider ${provider.code}`, {
            provider: provider.code,
            cause: error,
          }));
        });
      }
    }

    this.logger.info(`Integrations - Completed providers checks`, {
      providers: codes,
      providerCodes,
    });

    return {
      providers: codes,
    }
  }


  public mustGetProvider(code: string) {
    const provider = this.integrationServices.find((provider) => provider.code === code);
    if (!provider) {
      throw new ContextError(`Provider not found: ${code}`)
    }
    return provider;
  }

  public getProvider(code: string) {
    return this.integrationServices.find((provider) => provider.code === code);
  }

  public async allProviders() {
    return this.integrationServices.map(p => p.getInfo());
  }

  /**
   * Get the setup config for a given provider
   * Return information about the provider and the integration for initiative
   */
  public async getIntegration(integrationCheck: IntegrationCheck): Promise<ProviderSetupInfo> {

    this.logger.info(`Getting integration for ${integrationCheck.integrationCode}`, {
      initiativeId: integrationCheck.initiativeId,
    });

    const provider = this.mustGetProvider(integrationCheck.integrationCode);

    const [setupConfig, integrationConnection, externalApp] = await Promise.all([
      provider.getSetupConfig(),
      this.repo.getConnection(integrationCheck),
      provider.getExternalApp ? provider.getExternalApp() : undefined,
    ]);

    return {
      provider: setupConfig.provider,
      integration: setupConfig.integration,
      status: integrationConnection?.status || 'setup_required',
      externalApp,
    }
  }

  public async getUsedIntegration(integrationCheck: IntegrationCheck): Promise<IntegrationConnectionInfo> {

    this.logger.info(`Getting integration for ${integrationCheck.integrationCode}`, {
      initiativeId: integrationCheck.initiativeId,
    });

    const provider = this.mustGetProvider(integrationCheck.integrationCode);

    const [setupConfig, integrationConnection] = await Promise.all([
      provider.getSetupConfig(),
      this.repo.getConnectionWithUser(integrationCheck),
    ]);

    return {
      provider: setupConfig.provider,
      integration: setupConfig.integration,
      connection: integrationConnection,
    }
  }

  public async getActiveServices(initiativeId: ObjectId) {
    const connections = await this.repo.getInitiativeConnections(initiativeId);
    const providerCodes = connections.map(c => c.integrationCode);

    if (!config.integrations.greenProject.useMockData) {
      return this.integrationServices.filter(p => providerCodes.includes(p.code));
    }

    // Return all possible services as this is used with mock data.
    return this.integrationServices;
  }

  public async getActiveConnectionServices(initiativeId: ObjectId) {
    const connections = await this.repo.getInitiativeConnections(initiativeId);

    return connections.reduce((acc, connection) => {
      const integration = this.integrationServices.find(p => p.code === connection.integrationCode);

      if (integration) {
        acc.push({ connection, integration })
      }
      return acc;

    }, [] as { connection: IntegrationConnectionPlain, integration: IntegrationService }[])
  }

  public async getUsedProviders(initiativeId: ObjectId) {
    const connections = await this.repo.getConnectionsByProvider({
      initiativeId: initiativeId,
      status: { $in: ['pending', 'active'] },
    });

    return Promise.all(
      connections.map(async (connection) => {
        const provider = this.integrationServices.find((p) => p.code === connection.integrationCode);

        if (!provider) {
          return;
        }

        const externalApp = await provider.getExternalApp?.();
        return {
          ...provider.getInfo(),
          status: connection.status,
          login: connection.status === 'active' ? externalApp?.login : undefined,
        };
      })
    ).then((results) => results.filter(item => item !== undefined));
  }

  public async createIntegration(setupData: Omit<SetupData, 'integrationConnection'>) {
    const provider = this.mustGetProvider(setupData.providerCode);

    // Create a new connection
    const integrationConnection = await this.repo.createConnection({
      name: setupData.name,
      initiativeId: setupData.rootInitiative._id,
      integrationCode: setupData.providerCode,
      status: 'pending',
      createdBy: setupData.user._id,
      data: {
        answers: setupData.generatedAnswers,
        address: setupData.address,
      },
    });

    const response = await provider.createSetup({ ...setupData, integrationConnection });
    if (!response.success) {
      throw new ContextError('Failed to create setup', {
        initiativeId: setupData.rootInitiative._id,
        providerCode: setupData.providerCode,
        answers: setupData.generatedAnswers,
      })
    }


    const setupConfig = await provider.getSetupConfig();

    return {
      provider: setupConfig.provider,
      integration: setupConfig.integration,
      status: integrationConnection.status,
    }
  }

  private getInitialUtrsData() {
    if (!config.integrations.greenProject.useMockData) {
      return [];
    }
    // Hardcoded data for testing purpose
    return [
      ...greenProjectTechUtrsData,
    ];
  }

  async getData({
    initiativeId,
    providerUtrCodesMap,
    filters,
  }: {
    initiativeId: ObjectId;
    providerUtrCodesMap: Map<string, Set<string>>;
    filters: { period?: DataPeriods; dateRange?: { startDate?: string; endDate?: string } };
  }) {
    const integrationServices = await this.getActiveConnectionServices(initiativeId);

    const integrationData: IntegrationData = {
      // @TODO: Current integration logic does not apply filters, therefore the mock data will follow the same logic
      // Once it's implemented, should apply similar filter logic on the mock data
      utrsData: this.getInitialUtrsData(),
    };

    for (const [integrationCode, utrCodeSet] of providerUtrCodesMap) {
      const integrationConnection = integrationServices.find((service) => service.integration.code === integrationCode);
      if (!integrationConnection) {
        this.logger.error(new ContextError(`Integration service not found: ${integrationCode}`));
        continue;
      }

      const { integration, connection } = integrationConnection;

      const utrCodes = Array.from(utrCodeSet || []);
      const data = await integration.generateIntegrationData({
        connection,
        utrCodes,
        period: filters.period,
        startDate: filters.dateRange?.startDate,
        endDate: filters.dateRange?.endDate,
      });
      integrationData.utrsData.push(...data);
    }

    return integrationData;
  }
}

let instance: IntegrationManager;
export const getIntegrationManager = () => {
  if (!instance) {
    instance = new IntegrationManager(
      wwgLogger,
      getIntegrationRepository(),
      config.integrations.greenly.enabled ?
        [getGreenProjectService(), getGreenlyService()] :
        [getGreenProjectService()],
    );
  }
  return instance;
}
