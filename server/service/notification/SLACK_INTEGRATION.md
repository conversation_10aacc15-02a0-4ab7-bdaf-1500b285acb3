# Slack Integration for G17 Eco Notifications

This document explains how to set up and use the Slack notification integration in the G17 Eco platform.

## Overview

The Slack integration allows the platform to send notifications directly to Slack channels or users, providing real-time updates about surveys, assurance processes, data sharing, and other important events.

## Setup

### 1. Slack App Configuration

1. Create a Slack app at https://api.slack.com/apps
2. Add the following OAuth scopes to your bot:
   - `chat:write` - Send messages to channels
   - `chat:write.public` - Send messages to public channels
   - `users:read` - Read user information
   - `channels:read` - Read channel information

3. Install the app to your workspace
4. Copy the Bot User OAuth Token (starts with `xoxb-`)

### 2. Environment Variables

Set the following environment variables:

```bash
# Required
SLACK_BOT_TOKEN=xoxb-your-bot-token-here

# Channel configuration
SLACK_SIGNUP_CHANNEL_ID=CHANNEL_ID
```

## Message Format

Slack messages use rich formatting with:

- **Header block** with emoji and title
- **Content section** with markdown support
- **Action button** (if actionUrl provided)
- **Context footer** with category and topic info
- **Color coding** based on notification category

Example message structure:
```
🎉 Survey Completed

Your sustainability survey has been completed and is ready for review.

[View Details] (button)

Category: question_update | Topic: survey-completion
```

### Common Issues

1. **Bot token invalid**
   - Verify `SLACK_BOT_TOKEN` is correct
   - Check bot has required permissions
   - Ensure bot is installed in workspace

2. **Messages not appearing**
   - Check user has Slack notifications enabled
   - Verify channel exists and bot has access
   - Check logs for error messages

3. **Channel not found**
   - Ensure channels exist in Slack workspace
   - Bot must be invited to private channels
   - Use channel IDs instead of names if needed

### Logs

Check application logs for Slack-related messages:

```bash
# Success messages
"Slack message sent successfully"

# Error messages  
"Failed to send Slack notification to user"
"Could not send magic bell notification" # Generic notification errors
```