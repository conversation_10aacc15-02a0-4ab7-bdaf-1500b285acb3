/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import {
  CreateNotificationData,
  NotificationCategory,
  NotificationCategoryGroup,
  NotificationCategoryGroupsMap,
  NotificationDeliveryProvider,
  NotificationRepository,
  NotificationRole,
  NotificationRoleMap,
} from './NotificationTypes';
import { getMagicBell } from './delivery/MagicBell';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import { getNotificationStorage } from './NotificationStorage';
import { ObjectId } from 'bson';
import {
  getMergedPreferences,
  NotificationCategoryPreferences,
  NotificationPreferencesPlain,
  NotificationPreferencesUpdateRequest,
  NotificationServices,
} from './NotificationModels';
import NotificationPreferences from '../../models/notification-preferences';
import UserError from '../../error/UserError';
import { getEmailProvider } from './delivery/Email';
import { isUserManager, UserModel } from "../../models/user";
import { isOrganizationAssurer, OrganizationModel, } from "../../models/organization";
import { InitiativeRepository } from '../../repository/InitiativeRepository';
import { UserRoles } from '../user/userPermissions';
import { DomainConfigRepository, getDomainConfigRepository } from '../organization/DomainConfigRepository';
import { AppConfigService, getAppConfigService } from '../app/AppConfigService';
import { getRecipientsService, RecipientsService } from './delivery/Recipients';

type TargetConfig = boolean | { enabled: boolean }

interface Options {
  targets?: Record<string, TargetConfig>
  domain?: string;
}

export class NotificationService {

  constructor(
    private logger: LoggerInterface,
    private repo: NotificationRepository,
    private domainRepo: DomainConfigRepository,
    private appConfigService: AppConfigService,
    private deliveryProviders: NotificationDeliveryProvider[],
    private recipientsService: RecipientsService,
  ) {
  }

  public async createNotification(data: CreateNotificationData, options?: Options) {

    const mergedOptions = this.getOptions(options);
    const domainConfig = await this.domainRepo.getByDomain(mergedOptions.domain);
    const code = data.customAttributes?.appConfigCode;
    const appConfig = code ? await this.appConfigService.getByCode(code) : undefined;

    this.logger.info(`Sending new notification`, {
      category: data.category,
      topic: data.topic,
      customAttributes: data.customAttributes,
      options: mergedOptions,
    });

    const createdData = await this.repo.create(data);
    const recipients = await this.recipientsService.getRecipients(data);

    if (recipients.length === 0) {
      this.logger.info(`Skipping notifications to recipients`, recipients.map(r => r.id))
      return [];
    }

    this.logger.info(`Sending notifications to recipients`, recipients.map(r => r.id))
    const providerData = {
      ...data,
      recipients,
      domain: mergedOptions.domain,
      domainConfig,
      appConfig,
      _id: createdData._id
    };

    return Promise.all(this.deliveryProviders.map(async provider => {

      if (!this.isEnabled(provider, mergedOptions)) {
        return ({ provider: provider.getId(), status: 'skipped' })
      }
      return provider.create(providerData).catch(e => {
        this.logger.error(e);
        return ({ provider: provider.getId(), status: 'error', errors: [e] });
      });
    }))
  }

  private isEnabled(d: NotificationDeliveryProvider, options: Options) {
    if (!options.targets) {
      return true;
    }

    const target = options.targets[d.getId()];
    if (target === undefined) {
      return true;
    }

    if (typeof target === 'object') {
      return target.enabled
    }

    return target;
  }

  private getOptions(options?: Options): Options {

    const defaultOptions: Options = {
      targets: {
        email: true,
      },
    }

    if (options) {
      return {
        ...defaultOptions,
        ...options,
        targets: {
          ...defaultOptions.targets,
          ...options.targets,
        },
      }
    }

    return defaultOptions
  }

  private async getNotificationRole(user: UserModel, org: OrganizationModel | null): Promise<NotificationRole[]> {
    const roles: NotificationRole[] = [];

    if (isOrganizationAssurer(org)) {
      roles.push(NotificationRole.Assurer)
    }
    if (isUserManager(user)) {
      roles.push(NotificationRole.Admin)
    }

    if (await InitiativeRepository.hasPortfolioTracker(user, UserRoles.Manager)) {
      roles.push(NotificationRole.PortfolioAdmin)
    }

    return roles.length > 0 ? roles : [NotificationRole.User];
  }

  /**
   *  filters if NotificationCategory is not included in NotificationRole (assurer, user, admin) mapped by NotificationRoleMap
   *  note this only affects display of settings in the frontend
   *  for sending out notifications users are inherently filtered by the recipients queries
   */
  private filterPreferenceCategories = (roles: NotificationRole[], preferences: NotificationPreferencesPlain): NotificationPreferencesPlain => {

    // This will create plain version of preferences that can be safely updated
    const mergedPreferences = getMergedPreferences(preferences.userId, preferences);

    // Build up a list of all categories available
    const availableCategories = roles.reduce((acc, role) => {
      const roleCategories = NotificationRoleMap.get(role)
      if (roleCategories) {
        roleCategories.forEach(c => acc.add(c))
      }
      return acc;
    }, new Set<NotificationCategory>());

    const categories = mergedPreferences.magicBell.notification_preferences.categories
    const filtered: NotificationCategoryPreferences = {}
    Object.entries(categories).forEach(([k, v]) => {
      if (availableCategories.has(k as NotificationCategory)) {
        filtered[k] = v;
      }
    })
    mergedPreferences.magicBell.notification_preferences.categories = filtered

    return mergedPreferences
  }

  private mapNotificationCategoryGroups(preferences: NotificationPreferencesPlain): NotificationPreferencesPlain {
    const preferencesCategories = preferences.magicBell.notification_preferences.categories
    const mapped : NotificationCategoryPreferences = {}
    Object.entries(preferencesCategories).forEach(([k, v]) => {
      for (const [group, categories] of NotificationCategoryGroupsMap) {
        if (categories.includes(k as NotificationCategory) && v) {
          v.group = group;
        }
      }

      mapped[k] = v;
    })
    preferences.magicBell.notification_preferences.categories = mapped
    preferences.magicBell.notification_preferences.groups = Object.values(NotificationCategoryGroup)
    return preferences;
  }

  public async getPreferences(user: UserModel, org: OrganizationModel | null): Promise<NotificationPreferencesPlain> {
    const preferences = await this.getOrCreatePreferences(user._id);
    const roles = await this.getNotificationRole(user, org);
    const filtered = this.filterPreferenceCategories(roles, preferences);
    return this.mapNotificationCategoryGroups(filtered);
  }

  private async getOrCreatePreferences(userId: ObjectId) {
    const notificationPreferences = await this.repo.getUserSettings(userId);
    if (notificationPreferences) {
      return notificationPreferences;
    }
    const preferences = await NotificationPreferences.create({ userId });
    getMagicBell().setPreferences(userId, preferences.magicBell).catch(e => this.logger.error(e));
    return preferences
  }

  public async setPreferences(userId: string | ObjectId, data: NotificationPreferencesUpdateRequest) {
    if (!data.service || !data.category || !data.channel) {
      throw new UserError('Invalid request parameters');
    }
    const setting = data.setting !== undefined && Boolean(data.setting);

    if (data.service === NotificationServices.MagicBell) {
      const magicBell = getMagicBell();
      await magicBell.setPreference(userId, data.category, data.channel, setting);
    }
  }
}

let instance: NotificationService;
export const getNotificationService = () => {
  if (!instance) {
    instance = new NotificationService(
      wwgLogger,
      getNotificationStorage(),
      getDomainConfigRepository(),
      getAppConfigService(),
      [
        getMagicBell(),
        getEmailProvider(),
      ],
      getRecipientsService(),
    );
  }
  return instance;
}
