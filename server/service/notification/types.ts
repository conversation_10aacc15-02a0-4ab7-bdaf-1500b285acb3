/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

export enum MessageType {
  Survey = 'survey',
  Question = 'question',
  User = 'user',
  BulkSurvey = 'bulkSurvey',
}

export enum MessageFormField {
  UserIds = 'userIds',
  ContributorIds = 'contributorIds',
  VerifierIds = 'verifierIds',
  Subject = 'subject',
  Message = 'message',
  SendByEmail = 'sendByEmail',
}

export interface MessageFormFields {
  [MessageFormField.UserIds]: string[];
  [MessageFormField.ContributorIds]: string[];
  [MessageFormField.VerifierIds]: string[];
  [MessageFormField.Subject]: string;
  [MessageFormField.Message]: string;
  [MessageFormField.SendByEmail]: boolean;
}

export interface MessagePayload extends MessageFormFields {
  type: MessageType;
  surveyId?: string;
  utrvId?: string;
  initiativeId?: string;
}
