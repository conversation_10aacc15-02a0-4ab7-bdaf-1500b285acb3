/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { WebClient } from '@slack/web-api';
import { LoggerInterface, wwgLogger } from '../../wwgLogger';
import config from '../../../config';
import ContextError from '../../../error/ContextError';

export enum SlackNotificationCategory {
  Announcements = 'announcements',
}

export interface CreateNotificationData {
  title: string;
  content: string;
  category: SlackNotificationCategory;
  channel: string | undefined;
  topic?: string;
  actionUrl?: string;
  created?: Date;
}

export class SlackProvider {
  constructor(private logger: LoggerInterface, private slackClient: WebClient) {}

  public async create(createData: CreateNotificationData) {
    this.logger.info(`Sending slack notification`, {
      category: createData.category,
      topic: createData.topic,
      title: createData.title,
    });

    const message = this.formatSlackMessage(createData);
    const channel = createData.channel;

    if (!Object.values(SlackNotificationCategory).includes(createData.category) || !channel) {
      this.logger.info(`No channel for Slack notification`, {
        category: createData.category,
        topic: createData.topic,
        title: createData.title,
      });
      return;
    }

    try {
      const result = await this.slackClient.chat.postMessage({
        channel,
        ...message,
      });

      this.logger.info('Slack message sent successfully', {
        channel: result.channel,
        timestamp: result.ts,
        category: createData.category,
      });

      return result;
    } catch (error) {
      throw new ContextError('Failed to send Slack message', {
        channel,
        category: createData.category,
        cause: error,
      });
    }
  }

  private formatSlackMessage(data: CreateNotificationData) {
    const emoji = this.getCategoryEmoji(data.category);
    const color = this.getCategoryColor(data.category);

    return {
      text: `${emoji} ${data.title}`,
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: `${emoji} ${data.title}`,
            emoji: true,
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: data.content,
          },
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `Category: ${data.category} | Topic: ${data.topic}`,
            },
          ],
        },
      ],
      attachments: data.actionUrl
        ? [
            {
              color: color,
              fallback: `${data.title} - ${data.content}`,
            },
          ]
        : undefined,
    };
  }

  private getCategoryEmoji(category: SlackNotificationCategory): string {
    const emojiMap: Record<string, string> = {
      [SlackNotificationCategory.Announcements]: '📢',
    };

    return emojiMap[category] || '📬';
  }

  private getCategoryColor(category: SlackNotificationCategory): string {
    const colorMap: Record<string, string> = {
      [SlackNotificationCategory.Announcements]: '#ff6b6b',
    };

    return colorMap[category] || '#cccccc';
  }
}

let instance: SlackProvider;
export const getSlackProvider = () => {
  if (!instance) {
    instance = new SlackProvider(wwgLogger, new WebClient(config.notifications.slack.botToken));
  }
  return instance;
};
