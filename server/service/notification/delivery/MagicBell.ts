/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import MagicBellClient, { Notification } from '@magicbell/core';
import config from '../../../config';
import {
  NotificationCategory,
  NotificationDataExpanded,
  NotificationDeliveryProvider,
  NotificationRepository,
} from '../NotificationTypes';
import { ObjectId } from 'bson';
import { createHmac } from 'crypto';
import { LoggerInterface, wwgLogger } from '../../wwgLogger';
import axios, { isAxiosError } from 'axios';
import {
  MagicBellChannelApiV2,
  MagicBellPreferences,
  NotificationCategoryPreference,
  NotificationChannels,
  NotificationServices,
  getDefaultPreferences,
} from '../NotificationModels';
import { getNotificationStorage } from '../NotificationStorage';
import ContextError from "../../../error/ContextError";

MagicBellClient.configure(config.notifications.magicBell);

interface MagicBellUser {
  external_id: string
}

export class MagicBell implements NotificationDeliveryProvider {
  baseURL = 'https://api.magicbell.com';

  constructor(
    private logger: LoggerInterface,
    private repo: NotificationRepository,
  ) {
  }

  public getId() {
    return NotificationServices.MagicBell;
  }

  public async getStatus() {
    const { apiSecret, apiKey } = config.notifications.magicBell;
    const headers = {
      "X-MAGICBELL-API-KEY": apiKey,
      "X-MAGICBELL-API-SECRET": apiSecret
    };

    const service = {
      name: 'MagicBell',
      status: 'Not Connected',
      apiKey: `${apiKey.slice(0, 10)}...`,
      apiSecret: `${apiSecret.slice(0, 5)}...`,
      error: undefined as string | undefined,
    }

    try {
      // Check MagicBell endpoint for access, use fast request as it just for authentication
      await axios.get(`${this.baseURL}/users`, { headers, params: { per_page: 1 } });
      // If it doesn't throw error, it succeeded.
      service.status = 'Connected';
    } catch (e) {
      service.error = String(e.message);
    }

    return service;
  }

  public async create(createData: NotificationDataExpanded) {

    const notificationData: Parameters<typeof Notification.create>[0] = {
      category: createData.category,
      title: createData.title,
      content: createData.content,
      topic: createData.topic,
      action_url: createData.actionUrl,
      custom_attributes: createData.customAttributes,
      recipients: await this.getRecipients(createData),
    }

    if (notificationData.recipients.length === 0) {
      return; // Skip
    }

    this.logger.info("Sending magic bell notification", notificationData)
    return Notification.create(notificationData).catch((e) => {
      throw new ContextError('Could not send magic bell notification', {
        cause: e,
        notificationId: createData._id?.toString(),
      });
    });
  }

  public getAuthToken(userId: string | ObjectId): string {
    return createHmac('sha256', config.notifications.magicBell.apiSecret)
      .update(String(userId))
      .digest('base64')
  }

  private getAPIConfig = (userId: string) => {
    const token = this.getAuthToken(userId);
    return {
      headers: {
        'X-MAGICBELL-API-KEY': config.notifications.magicBell.apiKey,
        'X-MAGICBELL-USER-EXTERNAL-ID': userId,
        'X-MAGICBELL-USER-HMAC': token,
        // Accept-Version only apply to notification_preferences endpoint (only one we use)
        // to differentiate shape of preferences between Api versions.
        // Api-v1 => Eg. categories: { 'category': { email: true, in_app: false }, etc. }
        // Api-v2 => Eg. categories: [ { slug: 'category', channels: [ { slug: 'email', enabled: true }, { slug: 'in_app', enabled: false } ] }, etc. ]
        'Accept-Version': 'v2',
      },
    };
  }

  private _put(url: string, userId: string, data: any) {
    return axios.put(`${this.baseURL}/${url}`, data, this.getAPIConfig(userId));
  }

  public async setPreferences(userId: string | ObjectId, data: MagicBellPreferences) {
    try {
      const result = await this._put('notification_preferences', String(userId), this.formatPreferencesData(data));
      this.logger.info(`Magic bell preferences set for ${userId}`, { userId });
      return result;
    } catch (e) {
      throw new ContextError(`Could not set magic bell preferences for userId ${userId}`, {
        cause: e,
        userId,
        errors: this.getErrorData(e),
      });
    }
  }

  private getErrorData = (e: unknown) => {
    return isAxiosError(e) ? e.response?.data?.errors : undefined
  }

  public async setPreference(
    userId: string | ObjectId,
    category: string | string[],
    channel: NotificationChannels,
    isEnabled = false
  ) {
    const notificationPreferences = await this.repo.getUserSettings(userId);
    if (!notificationPreferences) {
      throw new Error(`Cannot update user preferences because could not find preferences for user ${userId}`);
    }

    if (!Object.values(NotificationChannels).includes(channel)) {
      throw new Error(`Invalid channel ${channel}`);
    }

    (Array.isArray(category) ? category : [category]).forEach((cat) => {
      if (!Object.values(NotificationCategory).includes(cat as NotificationCategory)) {
        throw new Error(`Invalid category ${cat}`);
      }

      const categories = notificationPreferences.magicBell.notification_preferences.categories;
      const categoryPreferences = categories[cat] ?? getDefaultPreferences();
      categoryPreferences[channel] = isEnabled;

      // Assign it back with update.
      categories[cat] = categoryPreferences;
    });

    notificationPreferences.markModified('magicBell');
    await notificationPreferences.save();
    await this.setPreferences(userId, notificationPreferences.magicBell);
  }

  private async getRecipients(createData: NotificationDataExpanded): Promise<MagicBellUser[]> {

    const forcedSend = createData.preferencesOverride?.in_app === true;

    const users = createData.recipients.reduce((a, r) => {
      // Could do some check here as well to ensure we should send out this

      const categories = r.settings.magicBell.notification_preferences.categories;
      // By default, is enabled, unless explicitly off
      if (forcedSend || categories[createData.category]?.in_app !== false) {
        a.set(r.id, { external_id: r.id });
      }

      return a;
    }, new Map<string, MagicBellUser>());

    return Array.from(users.values())
  }

  private getChannels(preference: NotificationCategoryPreference): MagicBellChannelApiV2[] {
    return Object.entries(preference).map(([key, value]) => ({
      slug: key,
      enabled: Boolean(value)
    }));
  }

  private getApiV2Preferences(data: MagicBellPreferences) {
    const categories = Object.entries(data.notification_preferences.categories).map(([key, preference]) => {
      if (!preference) {
        return {
          slug: key,
          channels: [],
        };
      }
      return {
        slug: key,
        channels: this.getChannels(preference),
      };
    });
    return {
      notification_preferences: {
        categories
      },
    };
  }

  public formatPreferencesData(data: MagicBellPreferences, version: 'v1' | 'v2' = 'v2') {
    switch(version) {
      case 'v2':
        return this.getApiV2Preferences(data);
      case 'v1':
      default:
        return data;
    }
  }
}

let instance: MagicBell;
export const getMagicBell = (repo: NotificationRepository = getNotificationStorage()) => {

  if (!instance) {
    instance = new MagicBell(
      wwgLogger,
      repo,
    );
  }
  return instance;
}
