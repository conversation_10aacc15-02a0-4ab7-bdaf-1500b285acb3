/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { CreateNotificationData, ExpandedRecipientData, NotificationRepository } from '../NotificationTypes';
import { LoggerInterface, wwgLogger } from '../../wwgLogger';
import { getNotificationStorage } from '../NotificationStorage';
import { getMergedPreferences } from '../NotificationModels';

export class RecipientsService {
  constructor(private logger: LoggerInterface, private repo: NotificationRepository) {}

  public async getRecipients(data: CreateNotificationData): Promise<ExpandedRecipientData[]> {
    const userIds = data.recipients.map((r) => r.id);
    const settings = await this.repo.getSettings(userIds);

    return data.recipients.reduce((a, r) => {
      const user = settings.find((s) => s._id.equals(r.id));

      if (user) {
        a.push({
          ...r,
          _id: user._id,
          email: user.email,
          firstName: user.firstName,
          surname: user.surname,
          settings: getMergedPreferences(user._id, user.notificationPreferences),
        });
      }

      return a;
    }, [] as ExpandedRecipientData[]);
  }
}

let instance: RecipientsService;
export const getRecipientsService = () => {
  if (!instance) {
    instance = new RecipientsService(wwgLogger, getNotificationStorage());
  }
  return instance;
};
