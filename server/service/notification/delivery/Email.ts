/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import {
  ExpandedRecipientData,
  NotificationCategory,
  NotificationDataExpanded,
  NotificationDeliveryProvider,
} from '../NotificationTypes';
import { LoggerInterface, wwgLogger } from '../../wwgLogger';
import { NotificationServices } from '../NotificationModels';
import {
  createMailService,
  MailerInterface,
  MessageInterface,
} from '../../email/EmailService';
import EmailTransaction from '../../email/model/EmailTransaction';
import { prepareNotificationEmail } from '../templates/email';
import NotificationSchedule from '../../../models/notification-schedule';
import ContextError from '../../../error/ContextError';


export class EmailProvider implements NotificationDeliveryProvider {

  constructor(
    private logger: LoggerInterface,
    private emailService: MailerInterface,
  ) {
  }

  public getId(): NotificationServices {
    return NotificationServices.Email;
  }

  public classifyDirectAndSummariesRecipients(
    recipients: ExpandedRecipientData[],
    category: NotificationCategory
  ) {
    // filter out users who have summaries email setting
    // this step will send direct email notification to only users who don't have email summary setting
    const { directRecipients, summariesRecipients } = recipients.reduce(
      (acc, r) => {
        const { settings } = r;
        if (settings.notificationSchedule?.isSummary && settings.magicBell.notification_preferences.categories[category]?.email) {
          acc.summariesRecipients.push(r);
        } else {
          acc.directRecipients.push(r);
        }
        return acc;
      },
      { directRecipients: [] as ExpandedRecipientData[], summariesRecipients: [] as ExpandedRecipientData[] }
    );

    return {
      directRecipients,
      summariesRecipients,
    };
  }

  public async accumulateNotificationSchedule(params: {
    recipientIds: ObjectId[];
    initiativeId: string;
    notificationId: ObjectId;
  }) {
    const { recipientIds, initiativeId, notificationId } = params;

    const listNotificationSchedules = await NotificationSchedule.find({ userId: { $in: recipientIds }, initiativeId, completedDate: { $exists: false } });

    // upsert notification schedules
    for (const recipientId of recipientIds) {
      const existed = listNotificationSchedules.find(item => item.userId.equals(recipientId));

      if (existed) {
        existed.notificationIds = [...existed.notificationIds, notificationId];
        await existed.save();
        continue;
      }

      await NotificationSchedule.create({ userId: recipientId, initiativeId, notificationIds: [notificationId] });
    }
    this.logger.info(`Accumulate notification schedule`, {
      recipientIds,
      initiativeId,
      notificationId,
    });
  }

  public async create(createData: NotificationDataExpanded) {

    const recipients = await this.getRecipients(createData);
    const notificationData: NotificationDataExpanded = {
      domain: createData.domain,
      appConfig: createData.appConfig,
      category: createData.category,
      title: createData.title,
      content: createData.content,
      topic: createData.topic,
      actionUrl: createData.actionUrl,
      customAttributes: createData.customAttributes,
      overrides: createData.overrides,
      recipients: recipients,
      preferencesOverride: createData.preferencesOverride
    }

    this.logger.info(`Sending ${this.getId()} notification`, {
      category: createData.category,
      topic: createData.topic,
      title: createData.title,
      recipientCount: recipients.length
    })

    const { directRecipients, summariesRecipients } = this.classifyDirectAndSummariesRecipients(recipients, createData.category);

    if (createData._id && createData.customAttributes?.initiativeId) {
      const initiativeId = createData.customAttributes.initiativeId;
      this.accumulateNotificationSchedule({
        recipientIds: summariesRecipients.map((r) => r._id),
        initiativeId,
        notificationId: createData._id,
      }).catch((e) =>
        this.logger.error(
          new ContextError('Failed to accumulate', {
            cause: e,
            notificationId: createData._id,
            initiativeId
          })
        )
      );
    }

    const sendingRecipients = createData.category === NotificationCategory.ScheduleEmailSummaries ? summariesRecipients : directRecipients;

    return sendingRecipients.map(u => {
      return this.sendToUser(notificationData, u).catch(e => this.logger.error(e));
    })
  }


  private sendToUser(data: NotificationDataExpanded, user: ExpandedRecipientData) {

    const { html, subject } = prepareNotificationEmail(data, user);

    const msg = this.emailService.getNewMessageInstance()
      .addTo(user.email, `${user.firstName ?? ''} ${user.surname ?? ''}`)
      .setHtml(html)
      .setSubject(subject);

    return this.send(msg, user._id);
  }

  private async getRecipients(createData: NotificationDataExpanded) {

    const forcedSend = createData.preferencesOverride?.email === true;

    const users = createData.recipients.reduce((a, r) => {
      // Could do some check here as well to ensure we should send out this

      const categories = r.settings.magicBell.notification_preferences.categories;
      // By default, is enabled, unless explicitly off
      if (forcedSend || categories?.[createData.category]?.email !== false) {
        a.set(r.id, r);
      }

      return a;
    }, new Map<string, ExpandedRecipientData>());

    return Array.from(users.values())
  }

  private async send(message: MessageInterface, userId: string | ObjectId, data?: any) {

    const emailResult = await this.emailService.send(message);
    EmailTransaction.create({
      externalId: emailResult.getId(),
      userId: userId.toString(),
      service: `notification`,
      data,
    }).catch(wwgLogger.error);

    return emailResult;
  }

}

let instance: EmailProvider;
export const getEmailProvider = () => {
  if (!instance) {
    instance = new EmailProvider(
      wwgLogger,
      createMailService(),
    );
  }
  return instance;
}
