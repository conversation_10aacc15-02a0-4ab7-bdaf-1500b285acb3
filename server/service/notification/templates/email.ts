/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import { NotificationDataExpanded, NotificationUser } from '../NotificationTypes';
import baseTemplate from '../../email/templates/core/baseTemplate';
import config from '../../../config';


type EmailRendererFn = (data: NotificationDataExpanded, user: NotificationUser) => {
  subject: string;
  html: string;
}

const getLink = (actionUrl = config.email.publicHostname, text = 'View') => {
  return { text, type: 'button', url: actionUrl };
};

function renderFooter() {
  return `
    <p>This is a notification from the G17Eco platform.</p>
    <p>You can manage the notification emails that you receive, by going to your inbox settings on the platform.</p>
  `;
}


const defaultTemplate: EmailRendererFn = (data, user) => {

  const { title, content, actionUrl, overrides } = data;
  const e = overrides?.email;

  const emailData = {
    domain: data.domain,
    domainConfig: data.domainConfig,
    appConfig: data.appConfig,
    link: getLink(actionUrl, e?.actionText),
    topContentHeader: '<br/>',
    topContent: `<div>
        <p>Hi there, </p>
        <p>${e?.title ?? title}</p>
        <p>${e?.content ?? content}</p>
      </div>
      `,
    bottomContent: e?.bottomContent ? `<div>${e?.bottomContent}</div>` : undefined,
    preFooter: renderFooter(),
    user,
  };

  return {
    html: baseTemplate(emailData),
    subject: e?.subject ?? `G17Eco - ${title}`
  }

}


export const prepareNotificationEmail = (data: NotificationDataExpanded, user: NotificationUser) => {

  switch (data.category) {
    default:
      return defaultTemplate(data, user);
  }
}
