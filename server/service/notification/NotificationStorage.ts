/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import {
  CreateNotificationData,
  NotificationModel,
  NotificationRepository,
} from './NotificationTypes';
import { ObjectId } from 'bson';
import Notification from '../../models/notification';
import NotificationPreferences from '../../models/notification-preferences';
import User from '../../models/user';

export class NotificationStorage implements NotificationRepository {
  public async create(data: CreateNotificationData): Promise<NotificationModel> {
    const createdData: Omit<NotificationModel, '_id'> = {
      ...data,
      recipients: data.recipients.map(r => {
        return {
          userId: new ObjectId(r.id),
        };
      })
    }

    return Notification.create(createdData);
  }

  public getSettings(userIds: string[]) {
    return User.find({
      _id: {
        $in: userIds.map(id => new ObjectId(id)),
      },
    }).populate('notificationPreferences').lean().exec();
  }

  public getUserSettings(userId: string | ObjectId) {
    return NotificationPreferences.findOne({ userId: new ObjectId(userId) }).exec();
  }
}

let instance: NotificationStorage;
export const getNotificationStorage = () => {
  if (!instance) {
    instance = new NotificationStorage();
  }
  return instance;
}
