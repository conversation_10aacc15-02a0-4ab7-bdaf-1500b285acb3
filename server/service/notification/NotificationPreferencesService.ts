import { ObjectId } from 'bson';
import NotificationPreferences from '../../models/notification-preferences';
import { NotificationPreferencesModel, NotificationSummariesUpdateRequest } from './NotificationModels';
import { NotificationCategory } from './NotificationTypes';

export class NotificationPreferencesService {
  public async setSummaries(userId: string | ObjectId, data: NotificationSummariesUpdateRequest) {
    const userPreferences = await NotificationPreferences.findOne({ userId: { $eq: userId } }).orFail();
    userPreferences.notificationSchedule = data;
    userPreferences.markModified('notificationSchedule');
    return userPreferences.save();
  }

  public hasEmailSummariesCategory(
    userPreferences: Pick<NotificationPreferencesModel, 'magicBell' | 'notificationSchedule'>,
    category: NotificationCategory
  ) {
    const {
      magicBell: { notification_preferences },
      notificationSchedule,
    } = userPreferences;

    if (!notificationSchedule || !notificationSchedule.isSummary) {
      return false;
    }

    if (!notification_preferences.categories[category] || !notification_preferences.categories[category].email) {
      return false;
    }

    return true;
  }
}

let instance: NotificationPreferencesService;
export const getNotificationPreferencesService = () => {
  if (!instance) {
    instance = new NotificationPreferencesService();
  }
  return instance;
};
