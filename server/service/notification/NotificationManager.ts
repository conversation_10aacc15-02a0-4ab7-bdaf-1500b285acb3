/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import { getNotificationService, NotificationService } from './NotificationService';
import { CustomAttributes, NotificationCategory, NotificationPage, RecipientData } from './NotificationTypes';
import UniversalTrackerValue, { UniversalTrackerValueModel, UtrvAssuranceStatus } from '../../models/universalTrackerValue';
import { ActionList } from '../utr/constants';
import { ObjectId } from 'bson';
import { customDateFormat, DateFormat, getCurrentDateStr, getFinancialEndDateDeadline } from '../../util/date';
import Survey, { BulkSurveysData, SurveyModel } from '../../models/survey';
import { Actions } from '../action/Actions';
import {
  AssurancePortfolioExpanded,
  AssurancePortfolioPlain,
  AssurancePortfolioStatus
} from '../assurance/model/AssurancePortfolio';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import {
  AssuranceAction,
  assuranceInProgressStatuses,
  UniversalTrackerValueAssuranceModel,
} from '../assurance/model/Assurance';
import { AssuranceRepository } from '../../repository/AssuranceRepository';
import { UrlMapper } from '../url/UrlMapper';
import { tryGetContext } from '../../middleware/audit/contextMiddleware';
import { getRootInitiativeService, RootInitiativeService } from '../organization/RootInitiativeService';
import { MessagePayload, MessageType } from './types';
import User, { UserModel, UserPlain } from '../../models/user';
import UniversalTracker, { UniversalTrackerModel, UniversalTrackerPlain } from '../../models/universalTracker';
import Initiative, { InitiativeWithFinancialEndDate } from '../../models/initiative';
import { InitiativeRepository, getInitiativeRepository, RootInitiativeData } from '../../repository/InitiativeRepository';
import { UserForUtrManager } from '../utr/UniversalTrackerActionManager';
import { PopulateOptions } from 'mongoose';
import { ObType, OnboardingModel } from "../../models/onboarding";
import { UserRoles } from "../user/userPermissions";
import { JobStatus, SurveyTemplateHistoryModelPlain } from '../../models/surveyTemplateHistory';
import DOMPurify from 'isomorphic-dompurify';
import { KeysEnum } from "../../models/public/projectionUtils";
import { BulkDelegateSurvey } from "../../repository/SurveyRepository";
import { SurveyRedirectManager } from '../survey/SurveyRedirectManager';
import { UserRepository } from '../../repository/UserRepository';
import { SURVEY } from '../../util/terminology';

interface GenerateReportNameParams {
  _id: string | ObjectId;
  initiative?: { name: string };
  effectiveDate?: string | Date;
}

interface DelegationNotificationRequest {
  survey: SurveyModel;
  questionCount: number;
  userId: string;
  action: Actions;
  utrv?: Pick<UniversalTrackerValueModel, 'universalTrackerId' | '_id'> | null;
  delegator: UserModel;
}

interface MultiSurveyDelegationNotification {
  surveys: BulkDelegateSurvey[];
  initiativeId: ObjectId;
  questionCount: number;
  userId: string;
  action: Actions;
  delegator: UserPlain,
}

interface UtrvNotificationRequest {
  utrv: UniversalTrackerValueModel,
  user: UserForUtrManager,
  commentId?: string | ObjectId,
}

interface UtrvNotificationCommentRequest extends UtrvNotificationRequest {
  text: string,
  filteredUserIds?: string[];
}

interface AssuranceUtrvRequest {
  assuranceUtrv: UniversalTrackerValueAssuranceModel,
  user: UserForUtrManager,
  utr: UniversalTrackerModel,
  org: RootInitiativeData
}

type AssurancePT = Pick<AssurancePortfolioPlain, '_id' | 'initiativeId' | 'status' | 'permissions' | 'surveyId'>;
const assurancePortfolioProjection: KeysEnum<AssurancePT> =  {
  _id: 1,
  initiativeId: 1,
  status: 1,
  permissions: 1,
  surveyId: 1,
}

interface UtrvMentionParams extends UtrvNotificationRequest {
  recipientIds: string[]
}

export class NotificationManager {
  constructor(
    private logger: LoggerInterface,
    private notificationService: NotificationService,
    private rootInitiativeService: RootInitiativeService,
  ) {
  }

  public async sendUtrvRejectedNotification({ utrv, user }: UtrvNotificationRequest) {

    if (utrv.status !== ActionList.Rejected) {
      return; // Skip
    }

    if (!utrv.compositeData?.surveyId) {
      return; // Nowhere to send the user. This may be rejecting a target on utr modal
    }

    if (!utrv.populated('initiative')) {
      await utrv.populate('initiative');
    }

    if (!utrv.initiative) {
      return;
    }

    const utrvId = String(utrv._id);
    const utrvContributors = utrv.stakeholders?.stakeholder.map((id) => ({ id: String(id) }));

    const initiativeContributors = await UserRepository.getUsersByInitiativeAndParents({ initiativeId: utrv.initiativeId, roles: [UserRoles.Contributor] });
    const permissionRecipients = initiativeContributors.reduce((acc, { _id }) => {
      const id = String(_id);
      // Ensure we do not include same user twice
      if (!(utrvContributors ?? []).some((stakeholder) => stakeholder.id === id)) {
        acc.push({ id });
      }
      return acc;
    }, [] as { id: string }[]);

    const org = await this.rootInitiativeService.getOrganization(utrv.initiative);
    const utr = await UniversalTracker.findOne({
      _id: utrv.universalTrackerId,
    })
      .orFail()
      .exec();

    const customAttributes: CustomAttributes = {
      orgId: org?._id.toString(),
      appConfigCode: org?.appConfigCode,
      page: NotificationPage.QuestionView,
      domain: tryGetContext()?.origin,
      initiativeId: String(utrv.initiativeId),
      surveyId: utrv.compositeData.surveyId.toString(),
      utrvId,
    };

    const reportName = this.generateReportName({
      _id: utrv._id,
      initiative: utrv.initiative,
      effectiveDate: utrv.effectiveDate,
    });
    const title = `<p class="mb-2">${this.getTypeCode({
      utr,
      fallback: 'a question',
    })} has been rejected by ${this.getFullName({
      user,
    })}</p>`;

    const notificationSetup = ({
      category,
      recipients,
    }: {
      category: NotificationCategory;
      recipients: { id: string }[];
    }) => ({
      title: 'An answer has been rejected',
      content: `${title}${reportName}`,
      category,
      topic: `question-${utrvId}`,
      customAttributes: customAttributes,
      actionUrl: UrlMapper.notificationUrl(customAttributes),
      recipients,
      overrides: {
        email: {
          subject: `G17Eco - Answer has been rejected`,
          title: title,
          content: reportName,
          actionText: 'VIEW QUESTION',
        },
      },
    });

    const notifications = [];
    if (utrvContributors && utrvContributors.length > 0) {
      notifications.push(
        this.notificationService.createNotification(
          notificationSetup({ category: NotificationCategory.QuestionReject, recipients: utrvContributors })
        )
      );
    }

    if (permissionRecipients.length > 0) {
      notifications.push(
        this.notificationService.createNotification(
          notificationSetup({
            category: NotificationCategory.QuestionRejectPermission,
            recipients: permissionRecipients,
          })
        )
      );
    }

    await Promise.all(notifications);
  }

  public async sendNoteNotification({ utrv, user }: UtrvNotificationRequest) {
    if (!utrv.stakeholders) {
      return; // Skip
    }

    if (!utrv.populated('initiative')) {
      await utrv.populate('initiative');
    }

    if (!utrv.initiative) {
      return;
    }

    const utrvId = String(utrv._id);
    const lastUserId = utrv.history[utrv.history.length - 1].userId
    const recipients = utrv.stakeholders.stakeholder.filter(id => String(id) !== String(lastUserId)).map(id => ({ id: String(id) }));

    const org = await this.rootInitiativeService.getOrganization(utrv.initiative);
    const utr = await UniversalTracker.findOne({
      _id: utrv.universalTrackerId,
    }).orFail().exec();

    const customAttributes: CustomAttributes = {
      orgId: org?._id.toString(),
      appConfigCode: org?.appConfigCode,
      page: NotificationPage.QuestionView,
      domain: tryGetContext()?.origin,
      initiativeId: String(utrv.initiativeId),
      surveyId: utrv.compositeData?.surveyId?.toString(),
      utrvId: utrvId,
    };

    const reportName = this.generateReportName({
      _id: utrv._id,
      initiative: utrv.initiative,
      effectiveDate: utrv.effectiveDate,
    });
    const title = `<p class="mb-2">${this.getTypeCode({
      utr,
      fallback: 'a question',
    })} has been updated by ${this.getFullName({
      user,
    })}</p>`;

    await this.notificationService.createNotification({
      title: 'Question notes update',
      content: `${title}${reportName}`,
      category: NotificationCategory.QuestionNoteUpdate,
      topic: `question-${utrvId}`,
      customAttributes: customAttributes,
      actionUrl: UrlMapper.notificationUrl(customAttributes),
      recipients,
      overrides: {
        email: {
          actionText: 'VIEW NOTES',
          title: title,
          content: reportName,
          subject: `G17Eco - New note waiting`,
        },
      },
    })
  }

  public async sendCommentNotification({ utrv, user, text, commentId, filteredUserIds }: UtrvNotificationCommentRequest) {
    const populatedPaths = ['initiative', 'universalTracker'].filter(path => !utrv.populated(path))
    if (populatedPaths.length > 0) {
      await utrv.populate(populatedPaths);
    }

    if (!utrv.initiative || !utrv.universalTracker) {
      return;
    }

    const utrvId = String(utrv._id);
    const currentUserId = String(user._id);

    if (!utrv.stakeholders || !utrv.compositeData?.surveyId) {
      return; // Skip
    }

    const recipients = [...utrv.stakeholders.stakeholder, ...utrv.stakeholders.verifier]
      .map(String)
      .filter(id => id !== currentUserId)
      .filter(id => filteredUserIds ? !filteredUserIds.includes(id) : true)
      .map(id => ({ id }));

    const org = await this.rootInitiativeService.getOrganization(utrv.initiative);
    const customAttributes: CustomAttributes = {
      orgId: org?._id.toString(),
      appConfigCode: org?.appConfigCode,
      page: NotificationPage.QuestionView,
      domain: tryGetContext()?.origin,
      initiativeId: String(utrv.initiativeId),
      surveyId: utrv.compositeData?.surveyId?.toString(),
      utrvId: utrvId,
      commentId: commentId ? String(commentId) : undefined,
    };

    const reportName = this.generateReportName({
      _id: utrv._id,
      initiative: utrv.initiative,
      effectiveDate: utrv.effectiveDate,
    });
    const title = `<p class="mb-2 comment-notification text-truncate">${this.getTypeCode({
      utr: utrv.universalTracker,
      fallback: 'a question',
    })} has received a new comment:</p>
    <p>${this.filterMentionsFromBody(text)}</p>`;

    await this.notificationService.createNotification({
      title: 'There is a new comment waiting',
      content: `${title}${reportName}`,
      category: NotificationCategory.QuestionComment,
      topic: `question-${utrvId}`,
      customAttributes: customAttributes,
      actionUrl: UrlMapper.notificationUrl(customAttributes),
      recipients,
      overrides: {
        email: {
          actionText: 'VIEW COMMENT',
          title: title,
          content: reportName,
          subject: `G17Eco - New comment waiting`,
        },
      },
    });

    const portfolios: AssurancePT[] = await AssuranceRepository.findActivePortfoliosByUtrvId(utrv._id, assurancePortfolioProjection);
    if (portfolios.length === 0) {
      return;
    }

    await Promise.all(
      portfolios.map(async (portfolio) => {
        const assurers = portfolio.permissions
          .map((permission) => ({ id: String(permission.userId) } ))
          .filter(({ id }) => id !== currentUserId && (filteredUserIds ? !filteredUserIds.includes(id) : true))

        const assuranceAttributes = {
          domain: tryGetContext()?.origin,
          appConfigCode: undefined,
          page: NotificationPage.AssurerPortfolioQuestion,
          initiativeId: String(portfolio.initiativeId),
          surveyId: portfolio.surveyId?.toString(),
          portfolioId: portfolio._id.toHexString(),
          utrvId: utrv._id.toHexString(),
        };
        await this.notificationService.createNotification({
          title: 'There is a new comment waiting',
          content: `${title}${reportName}`,
          category: NotificationCategory.QuestionComment,
          topic: `question-${utrvId}`,
          customAttributes: assuranceAttributes,
          actionUrl: UrlMapper.notificationUrl(assuranceAttributes),
          recipients: assurers,
          overrides: {
            email: {
              actionText: 'VIEW COMMENT',
              title: title,
              content: reportName,
              subject: `G17Eco - New comment waiting`,
            },
          },
        });
      })
    );
  }

  public async sendCommentMentionNotification({ utrv, user, commentId, recipientIds }: UtrvMentionParams) {

    if (!utrv.populated('initiative')) {
      await utrv.populate('initiative');
    }

    if (!utrv.initiative) {
      return;
    }

    const currentUserId = String(user._id);
    const filteredRecipientIds = recipientIds
      .filter(id => id !== currentUserId);

    const sentRecipients: string[] = [];

    // These includes completed and pending
    const assurancePortfolios: AssurancePT[] = await AssuranceRepository.findActivePortfoliosByUtrvId(
      utrv._id,
      assurancePortfolioProjection
    );

    if (assurancePortfolios) {
      for (const ap of assurancePortfolios) {
        const filteredApUserIds = ap.permissions.map(permission => String(permission.userId))
          // It's one of the mentioned userIds and it did not receive notification already
          .filter(id => recipientIds.includes(id) && !sentRecipients.includes(id));
        sentRecipients.push(...filteredApUserIds);

        await this._sendCommentMentionNotification({
          utrv,
          recipientIds: filteredApUserIds,
          portfolioId: ap._id,
          commentId,
        });
      }
    }

    const surveyRecipients = filteredRecipientIds.filter(id => !sentRecipients.includes(id));
    return this._sendCommentMentionNotification({
      utrv,
      recipientIds: surveyRecipients,
      commentId,
    });
  }

  private filterMentionsFromBody(text: string): string {
    // mentions format:   '@[William Medley](123123123123123123)'
    // outputs: ' William Medley '
    const mentionsRegex = /@\[([^\]]+)\]\([a-f0-9]+\)/g;
    return text.replace(mentionsRegex, ' $1 ');
  }

  private async _sendCommentMentionNotification({
    utrv,
    recipientIds,
    portfolioId,
    commentId,
  }: {
    utrv: UniversalTrackerValueModel;
    recipientIds: string[];
    portfolioId?: ObjectId;
    commentId?: string | ObjectId;
  }) {
    if (recipientIds.length === 0 || !utrv.initiative) {
      return;
    }
    const isAssurer = Boolean(portfolioId);

    const utrvId = String(utrv._id);
    const org = await this.rootInitiativeService.getOrganization(utrv.initiative);
    const customAttributes: CustomAttributes = {
      orgId: org?._id.toString(),
      appConfigCode: org?.appConfigCode,
      page: isAssurer ? NotificationPage.AssurerPortfolioQuestion : NotificationPage.QuestionView,
      domain: tryGetContext()?.origin,
      initiativeId: String(utrv.initiativeId),
      portfolioId: portfolioId ? String(portfolioId) : undefined,
      surveyId: utrv.compositeData?.surveyId?.toString(),
      utrvId: utrvId,
      commentId: commentId ? String(commentId) : undefined
    };

    const reportName = this.generateReportName({
      _id: utrv._id,
      initiative: utrv.initiative,
      effectiveDate: utrv.effectiveDate,
    });
    return this.notificationService.createNotification({
      title: `You have been mentioned in a comment`,
      content: reportName,
      category: NotificationCategory.QuestionComment,
      topic: `question-${utrvId}`,
      customAttributes: customAttributes,
      actionUrl: UrlMapper.notificationUrl(customAttributes),
      recipients: recipientIds.map(id => ({ id })),
      overrides: {
        email: {
          actionText: 'VIEW COMMENT',
          title: `<p>You have been mentioned in the comments for a question.</p>`,
          content: `<p>The question is part of the <strong>${reportName}</strong> survey.</p>`,
          subject: `G17Eco - New comment waiting`,
        },
      },
    });
  }

  public async sendUtrvUpdateNotification({ utrv, user }: UtrvNotificationRequest) {

    if (utrv.status !== ActionList.Updated) {
      return; // Skip
    }

    if (!utrv.populated('initiative')) {
      await utrv.populate('initiative');
    }

    if (!utrv.initiative) {
      return;
    }

    const utrvId = String(utrv._id);

    const utrvVerifiers = utrv.stakeholders?.verifier.map((id) => ({ id: String(id) }));
    const initiativeVerifiers = await UserRepository.getUsersByInitiativeAndParents({ initiativeId: utrv.initiativeId, roles: [UserRoles.Verifier] });
    const permissionRecipients = initiativeVerifiers.reduce((acc, { _id }) => {
      const id = String(_id);
      // Ensure we do not include same user twice
      if (!(utrvVerifiers ?? []).some((verifier) => verifier.id === id)) {
        acc.push({ id });
      }
      return acc;
    }, [] as { id: string }[]);

    const org = await this.rootInitiativeService.getOrganization(utrv.initiative);
    const utr = await UniversalTracker.findOne({
      _id: utrv.universalTrackerId,
    }).orFail().exec();

    const customAttributes: CustomAttributes = {
      orgId: org?._id.toString(),
      appConfigCode: org?.appConfigCode,
      domain: tryGetContext()?.origin,
      page: NotificationPage.QuestionView,
      initiativeId: String(utrv.initiativeId),
      surveyId: utrv.compositeData?.surveyId?.toString(),
      utrvId: utrvId,
    };

    const reportName = this.generateReportName({
      _id: utrv._id,
      initiative: utrv.initiative,
      effectiveDate: utrv.effectiveDate,
    });

    const title = `<p class="mb-2">${this.getTypeCode({
      utr,
      fallback: 'a question',
    })} has been updated by ${this.getFullName({
      user,
    })}</p>`;

    const notificationSetup = ({
      category,
      recipients,
    }: {
      category: NotificationCategory;
      recipients: { id: string }[];
    }) => ({
      title: 'Question has been updated',
      content: `${title}${reportName}`,
      category,
      topic: `question-${utrvId}`,
      customAttributes: customAttributes,
      actionUrl: UrlMapper.notificationUrl(customAttributes),
      recipients,
      overrides: {
        email: {
          subject: `G17Eco - Question has been updated`,
          title: title,
          content: reportName,
          actionText: 'GO TO PLATFORM',
        },
      },
    });

    const notifications = [];
    if (utrvVerifiers && utrvVerifiers.length > 0) {
      notifications.push(this.notificationService.createNotification(
        notificationSetup({ category: NotificationCategory.QuestionUpdate, recipients: utrvVerifiers })
      ));
    }

    if (permissionRecipients.length > 0) {
      notifications.push(this.notificationService.createNotification(
        notificationSetup({ category: NotificationCategory.QuestionUpdatePermission, recipients: permissionRecipients })
      ))
    }

    await Promise.all(notifications);
  }

  public async sendUtrvAssuredNotification({
    utrv,
    assurancePortfolio,
  }: {
    utrv: UniversalTrackerValueModel;
    assurancePortfolio: AssurancePortfolioExpanded;
  }) {
    if (!utrv.stakeholders) {
      return; // Skip
    }

    if (!utrv.populated('initiative')) {
      await utrv.populate('initiative');
    }

    if (!utrv.initiative) {
      return;
    }

    const assuranceOrgName = assurancePortfolio.organization?.name ?? 'Assurer';

    const utrvId = String(utrv._id);
    const recipients = [
      ...utrv.stakeholders.verifier.map((id) => String(id)),
      ...utrv.stakeholders.stakeholder.map((id) => String(id)),
    ].map((id) => ({ id }));

    const org = await this.rootInitiativeService.getOrganization(utrv.initiative);
    const utr = await UniversalTracker.findOne({
      _id: utrv.universalTrackerId,
    })
      .orFail()
      .exec();

    const customAttributes: CustomAttributes = {
      orgId: org?._id.toString(),
      appConfigCode: org?.appConfigCode,
      domain: tryGetContext()?.origin,
      page: NotificationPage.QuestionView,
      initiativeId: String(utrv.initiativeId),
      surveyId: utrv.compositeData?.surveyId?.toString(),
      utrvId: utrvId,
    };

    const reportName = this.generateReportName({
      _id: utrv._id,
      initiative: utrv.initiative,
      effectiveDate: utrv.effectiveDate,
    });
    const assuredText = utrv.assuranceStatus === UtrvAssuranceStatus.Partial ? 'partially assured' : 'assured';
    const title = `<p class="mb-2">${this.getTypeCode({
      utr,
      fallback: 'a question',
    })} has been ${assuredText} by ${assuranceOrgName}</p>`;

    await this.notificationService.createNotification({
      title: `Question has been ${assuredText}`,
      content: `${title}${reportName}`,
      category: NotificationCategory.QuestionAssured,
      topic: `question-${utrvId}`,
      customAttributes: customAttributes,
      actionUrl: UrlMapper.notificationUrl(customAttributes),
      recipients,
      overrides: {
        email: {
          subject: `G17Eco - Question has now been ${assuredText}`,
          title: title,
          content: reportName,
          actionText: 'GO TO PLATFORM',
        },
      },
    });
  }

  public async financialEndDateNotification() {
    const rootInitiatives: InitiativeWithFinancialEndDate[] = await getInitiativeRepository().getSGXRecipients();

    this.logger.info(`SGX - Found financial year end companies ${rootInitiatives.length}`)
    let notificationCount = 0;
    rootInitiatives.forEach(async (initiative) => {
      const deadline = getFinancialEndDateDeadline(initiative.notificationEndDate);
      if (!deadline) {
        return;
      }

      notificationCount++;

      const title = 'Deadline Alert: SGX Core';
      const deadlineDate = customDateFormat(initiative.notificationEndDate, DateFormat.DayMonthYear);
      const content = `Your mandatory SGX Core reporting for ${getCurrentDateStr(DateFormat.Year)} is due in ${deadline} (${deadlineDate})`;
      const org = await this.rootInitiativeService.getOrganization(initiative);
      const recipients = initiative.users.map((user) => ({ id: String(user._id) }));

      const customAttributes: CustomAttributes = {
        orgId: org._id.toString(),
        domain: undefined,
        appConfigCode: org.appConfigCode,
        page: NotificationPage.SurveyOverviewRedirect,
        initiativeId: initiative._id.toString(),
      };
      this.notificationService.createNotification({
        title,
        content,
        category: NotificationCategory.Announcements,
        customAttributes,
        recipients,
        topic: 'reporting_reminder_sgx',
        actionUrl: UrlMapper.notificationUrl(customAttributes),
        overrides: {
          email: {
            subject: `G17Eco - ${title}`,
            title,
            content,
            actionText: 'GO TO PLATFORM',
          },
        },
      });
    });

    this.logger.info(`SGX - Send financial year end notifications ${notificationCount}`)
  }

  public async sendUtrvDisputedNotification({
    utrv,
    assurancePortfolio,
    commentId,
  }: {
    utrv: UniversalTrackerValueModel;
    assurancePortfolio: AssurancePortfolioExpanded;
    commentId?: string | ObjectId;
  }) {
    if (!utrv.stakeholders) {
      return; // Skip
    }

    if (!utrv.populated('initiative')) {
      await utrv.populate('initiative');
    }

    if (!utrv.initiative) {
      return;
    }

    const populatePaths = ['organization', 'survey'].filter(path => !assurancePortfolio.populated(path))
    if (populatePaths.length > 0) {
      await assurancePortfolio.populate(populatePaths);
    }

    const assuranceOrgName = assurancePortfolio.organization?.name ?? 'Assurer';
    const surveyName = assurancePortfolio.survey?.name ?? '';

    const utrvId = String(utrv._id);
    const recipients = [
      ...utrv.stakeholders.verifier.map((id) => String(id)),
      ...utrv.stakeholders.stakeholder.map((id) => String(id)),
    ].map((id) => ({ id }));

    const org = await this.rootInitiativeService.getOrganization(utrv.initiative);
    const utr = await UniversalTracker.findOne({
      _id: utrv.universalTrackerId,
    })
      .orFail()
      .exec();

    const customAttributes: CustomAttributes = {
      orgId: org?._id.toString(),
      appConfigCode: org?.appConfigCode,
      domain: tryGetContext()?.origin,
      page: NotificationPage.QuestionView,
      initiativeId: String(utrv.initiativeId),
      surveyId: utrv.compositeData?.surveyId?.toString(),
      utrvId: utrvId,
      commentId: commentId ? String(commentId) : undefined,
    };

    const reportName = this.generateReportName({
      _id: utrv._id,
      initiative: utrv.initiative,
      effectiveDate: utrv.effectiveDate,
    });
    const title = `<p class="mb-2">${this.getTypeCode({
      utr,
      fallback: 'a question',
    })} has been disputed by ${assuranceOrgName}</p>${surveyName}`;

    await this.notificationService.createNotification({
      title: 'Question has been disputed by Assurer',
      content: `${title}${reportName}`,
      category: NotificationCategory.QuestionDisputed,
      topic: `question-${utrvId}`,
      customAttributes: customAttributes,
      actionUrl: UrlMapper.notificationUrl(customAttributes),
      recipients,
      overrides: {
        email: {
          subject: `G17Eco - Question has been disputed`,
          title: title,
          content: reportName,
          actionText: 'GO TO PLATFORM',
        },
      },
    });
  }

  public async sendBulkDelegation({
    survey,
    questionCount,
    userId,
    action,
    utrv,
    delegator,
  }: DelegationNotificationRequest) {
    const count = questionCount;
    // Skip on removal
    if (count === 0 || action === Actions.Remove) {
      return;
    }

    if (!survey.populated('initiative')) {
      await survey.populate('initiative');
    }
    const initiative = survey.initiative;
    if (!initiative) {
      this.logger.error(new Error(`Failed to find initiative for survey ${survey.initiativeId}`))
      return;
    }

    const id = survey._id.toString();
    const recipients = [{ id: String(userId) }];

    const org = await this.rootInitiativeService.getOrganization(initiative);
    const utr =
      count > 1 || !utrv
        ? null
        : await UniversalTracker.findById(utrv.universalTrackerId, { typeCode: 1 })
            .lean<Pick<UniversalTrackerPlain, 'typeCode'>>()
            .exec();

    const customAttributes: CustomAttributes = {
      orgId: org?._id.toString(),
      appConfigCode: org?.appConfigCode,
      domain: tryGetContext()?.origin,
      page: count > 1 ? SurveyRedirectManager.getPageByAppCode(org.appConfigCode) : NotificationPage.QuestionView,
      initiativeId: survey.initiativeId.toString(),
      surveyId: survey._id.toString(),
      filterByDelegationStatus: 'user-a',
      utrvId: utrv?._id.toString(),
    };

    const qText = `question${count === 1 ? '' : 's'}`;
    const hText = count === 1 ? 'has' : 'have';
    const title = `${count} ${qText} ${hText} been assigned to you`;
    const reportName = this.generateReportName({
      _id: survey._id,
      initiative: initiative,
      effectiveDate: survey.effectiveDate,
    });
    const emailTitle = `<p class="mb-2">${this.getTypeCode({
      utr,
      fallback: `${count} ${qText}`,
    })} ${hText} been assigned to you by ${this.getFullName({
      user: delegator,
    })}</p>`;

    await this.notificationService.createNotification({
      title,
      content: `${emailTitle}${reportName}`,
      category: NotificationCategory.QuestionDelegation,
      topic: `survey-${id}`,
      customAttributes: customAttributes,
      actionUrl: UrlMapper.notificationUrl(customAttributes),
      recipients,
      overrides: {
        email: {
          actionText: `VIEW YOUR ${qText.toUpperCase()}`,
          title: emailTitle,
          content: reportName,
          subject: `G17Eco - ${count} ${qText} assigned to you`,
        },
      },
    });
  }


  public async sendBulkSurveysDelegation(delegationRequest: MultiSurveyDelegationNotification) {

    const {
      surveys,
      questionCount,
      initiativeId,
      userId,
      action,
      delegator,
    } = delegationRequest;

    const count = questionCount;
    const [firstSurvey] = surveys;
    // Skip on removal
    if (count === 0 || action === Actions.Remove || !firstSurvey) {
      return;
    }


    const initiative = await Initiative.findById(initiativeId).exec();
    if (!initiative) {
      this.logger.error(new Error(`Failed to find initiative for survey ${initiativeId}`))
      return;
    }

    const recipients = [{ id: String(userId) }];

    const org = await this.rootInitiativeService.getOrganization(initiative);

    const customAttributes: CustomAttributes = {
      orgId: org?._id.toString(),
      appConfigCode: org?.appConfigCode,
      domain: tryGetContext()?.origin,
      page: NotificationPage.SurveyOverview,
      initiativeId: firstSurvey.initiativeId.toString(),
      surveyId: firstSurvey._id.toString(),
      filterByDelegationStatus: 'user-a',
    };

    const qText = `question${count === 1 ? '' : 's'}`;
    const hText = count === 1 ? 'has' : 'have';
    const title = `${count} ${qText} ${hText} been assigned to you`;

    const fullName = this.getFullName({ user: delegator });
    const surveyText = `${SURVEY.SINGULAR}${surveys.length === 1 ? '' : 's'}`;
    const emailTitle = `<p class="mb-2">${count} ${qText} ${hText} been assigned to you on ${surveys.length} ${surveyText} by ${fullName}</p>`;

    await this.notificationService.createNotification({
      title,
      content: `${emailTitle}`,
      category: NotificationCategory.QuestionDelegation,
      topic: `initiative-bulk-delegation-${initiativeId}`,
      customAttributes: customAttributes,
      actionUrl: UrlMapper.notificationUrl(customAttributes),
      recipients,
      overrides: {
        email: {
          actionText: `VIEW YOUR ${qText.toUpperCase()}`,
          title: emailTitle,
          content: '',
          subject: `G17Eco - ${count} ${qText} assigned to you`,
        },
      },
    });
  }

  public async sendSetupForAssurer(portfolio: AssurancePortfolioExpanded, userContactId: string) {

    if (portfolio.status !== AssurancePortfolioStatus.Created) {
      this.logger.error(`Trying to send notification for assurer when status is not created`, {
        status: portfolio.status,
        portfolioId: portfolio._id,
      })
      return; // Skip
    }

    const populatePaths: PopulateOptions[] = [
      { path: 'initiative' },
      { path: 'survey', select: ['name', 'effectiveDate'] }
    ].filter(({ path }) => !portfolio.populated(path));
    if (populatePaths.length > 0) {
      await portfolio.populate(populatePaths);
    }

    const initiative = portfolio.initiative;
    if (!initiative) {
      this.logger.error(`Failed to load initiative for assurance portfolio ${portfolio._id}`)
      return;
    }

    const survey = portfolio.survey;
    if (!survey) {
      this.logger.error(`Failed to load survey for assurance portfolio ${portfolio._id}`)
      return;
    }

    const portfolioId = String(portfolio._id);
    const recipients = [{ id: userContactId }];

    const customAttributes = {
      domain: tryGetContext()?.origin,
      appConfigCode: undefined,
      page: NotificationPage.AssurerDashboard,
      initiativeId: String(portfolio.initiativeId),
      surveyId: portfolio.surveyId?.toString(),
      portfolioId,
    };
    const surveyName = survey.name || customDateFormat(survey.effectiveDate, DateFormat.YearMonth);
    const content = `<p>
      ${initiative.name} is setting up a new Assurance portfolio, on the G17Eco platform. It is for the ${initiative.name}: ${surveyName} ${SURVEY.SINGULAR}. You will be informed when data has been sent for review.
    </p>`;

    await this.notificationService.createNotification({
      title: 'New Assurance portfolio is being set up',
      content: `${initiative.name}: ${surveyName}`,
      category: NotificationCategory.AssuranceStart,
      topic: `assurance-${portfolioId}`,
      customAttributes: customAttributes,
      actionUrl: UrlMapper.notificationUrl(customAttributes),
      recipients,
      overrides: {
        email: {
          subject: `G17Eco - Assurance set-up`,
          title: '',
          content,
          actionText: 'GO TO PLATFORM',
        },
      },
    })
  }

  public async sendReadyForAssurer(portfolio: AssurancePortfolioExpanded) {

    if (portfolio.status !== AssurancePortfolioStatus.Pending) {
      this.logger.error(`Trying to send notification for assurer when status is not pending`, {
        status: portfolio.status,
        portfolioId: portfolio._id,
      })
      return; // Skip
    }

    const populatePaths: PopulateOptions[] = [
      { path: 'initiative' },
      { path: 'survey', select: 'effectiveDate' }
    ].filter(({ path }) => !portfolio.populated(path));
    if (populatePaths.length > 0) {
      await portfolio.populate(populatePaths);
    }

    const initiative = portfolio.initiative;
    if (!initiative) {
      this.logger.error(`Failed to load initiative for assurance portfolio ${portfolio._id}`)
      return;
    }

    const portfolioId = String(portfolio._id);
    const recipients = portfolio.permissions.map(p => ({ id: String(p.userId) }));

    const customAttributes = {
      domain: tryGetContext()?.origin,
      appConfigCode: undefined,
      page: NotificationPage.AssurerPortfolio,
      initiativeId: String(portfolio.initiativeId),
      surveyId: portfolio.surveyId?.toString(),
      portfolioId,
    };

    const reportName = this.generateReportName({
      _id: portfolio._id,
      initiative: initiative,
      effectiveDate: portfolio.survey?.effectiveDate,
    });

    await this.notificationService.createNotification({
      title: 'New data sent through for assurance',
      content: reportName,
      category: NotificationCategory.AssuranceStart,
      topic: `assurance-${portfolioId}`,
      customAttributes: customAttributes,
      actionUrl: UrlMapper.notificationUrl(customAttributes),
      recipients,
      overrides: {
        email: {
          subject: `G17Eco - ${initiative.name} - New data to assure`,
          title: `<p>${initiative.name} sent through data for assurance, on the G17Eco platform.</p>`,
          content: `The data is part of the ${reportName} survey.`,
          actionText: 'GO TO PLATFORM',
        },
      },
    })
  }

  public async sendAssuranceQuestionUpdate({ utrv, user }: UtrvNotificationRequest) {
    const assuranceUtrvs = await AssuranceRepository.findByUtrvId(utrv._id, {
      status: { $in: assuranceInProgressStatuses },
    });

    if (assuranceUtrvs.length === 0) {
      return;
    }

    if (!utrv.populated('initiative')) {
      await utrv.populate('initiative');
    }

    if (!utrv.initiative) {
      return;
    }
    const utr = await UniversalTracker.findOne({
      _id: utrv.universalTrackerId,
    }).orFail().exec();

    const org = await this.rootInitiativeService.getOrganization(utrv.initiative);


    if (utrv.status === ActionList.Verified) {
      return Promise.all(assuranceUtrvs.map((assuranceUtrv) => {
        return this.sendAssuranceUtrvVerified({ assuranceUtrv, user, utr, org }).catch(e => {
          this.logger.error(e);
          return false;
        });
      }));
    }
    return Promise.all(assuranceUtrvs.map((assuranceUtrv) => {
      return this.sendAssuranceUtrvUpdated({ assuranceUtrv, user, utr, org }).catch(e => {
        this.logger.error(e);
        return false;
      });
    }));
  }

  private async sendAssuranceUtrvUpdated({ assuranceUtrv, user, utr }: AssuranceUtrvRequest) {
    if (!assuranceUtrv.portfolioId) {
      return false;
    }

    const portfolio = await AssuranceRepository.getAssuranceExpanded(assuranceUtrv.portfolioId);

    if (!portfolio || portfolio.status !== AssurancePortfolioStatus.Pending) {
      return; // Skip
    }

    const portfolioId = String(portfolio._id);
    const initiative = portfolio.initiative;
    const recipients = portfolio.permissions.map(p => ({ id: String(p.userId) }));

    const customAttributes = {
      domain: tryGetContext()?.origin,
      appConfigCode: undefined,
      page: NotificationPage.AssurerPortfolioQuestion,
      initiativeId: String(portfolio.initiativeId),
      surveyId: portfolio.surveyId?.toString(),
      portfolioId,
      utrvId: assuranceUtrv.utrvId.toHexString(),
    };

    const reportName = this.generateReportName({
      _id: portfolio._id,
      initiative: initiative,
      effectiveDate: portfolio.survey?.effectiveDate,
    });
    const title = `<p class="mb-2">${this.getTypeCode({
      utr,
      fallback: 'a question',
    })} has been updated by ${this.getFullName({
      user,
    })}</p>`;

    await this.notificationService.createNotification({
      title: 'Question data has been updated',
      content: `${title}${reportName}`,
      category: NotificationCategory.AssuranceQuestionUpdate,
      topic: `assurance-${portfolioId}-${assuranceUtrv._id}`,
      customAttributes: customAttributes,
      actionUrl: UrlMapper.notificationUrl(customAttributes),
      recipients,
      overrides: {
        email: {
          subject: `G17Eco - Question data has been updated`,
          title: title,
          content: reportName,
          actionText: 'GO TO PLATFORM',
        },
      },
    })
  }

  private async sendAssuranceUtrvVerified({ assuranceUtrv, user, utr, org }: AssuranceUtrvRequest) {
    if (!assuranceUtrv.portfolioId) {
      return false;
    }

    const portfolio = await AssuranceRepository.getAssuranceExpanded(assuranceUtrv.portfolioId);

    if (!portfolio || portfolio.status !== AssurancePortfolioStatus.Pending) {
      return; // Skip
    }

    const portfolioId = String(portfolio._id);
    const initiative = portfolio.initiative;
    const recipients = portfolio.permissions.map(p => ({ id: String(p.userId) }));

    const customAttributes = {
      domain: tryGetContext()?.origin,
      appConfigCode: undefined,
      page: NotificationPage.AssurerPortfolioQuestion,
      initiativeId: String(portfolio.initiativeId),
      surveyId: portfolio.surveyId?.toString(),
      portfolioId,
      utrvId: assuranceUtrv.utrvId.toHexString(),
    };

    const reportName = this.generateReportName({
      _id: portfolio._id,
      initiative: initiative,
      effectiveDate: portfolio.survey?.effectiveDate,
    });
    const title = `<p class="mb-2">${this.getTypeCode({
      utr,
      fallback: 'a question',
    })} has been verified by ${this.getFullName({
      user,
    })} at ${org.name} and is waiting for you</p>`;

    await this.notificationService.createNotification({
      title: 'Question has now been verified and is ready for you',
      content: `${title}${reportName}`,
      category: NotificationCategory.AssuranceQuestionUpdate,
      topic: `assurance-${portfolioId}-${assuranceUtrv._id}`,
      customAttributes: customAttributes,
      actionUrl: UrlMapper.notificationUrl(customAttributes),
      recipients,
      overrides: {
        email: {
          subject: `G17Eco - Question has now been verified and is ready for you`,
          title: title,
          content: reportName,
          actionText: 'GO TO PLATFORM',
        },
      },
    })
  }

  public async sendAssuranceCompleted(portfolio: AssurancePortfolioExpanded) {

    if (portfolio.status !== AssurancePortfolioStatus.Completed) {
      this.logger.error(`Trying to send notification for assurer when status is not completed`, {
        status: portfolio.status,
        portfolioId: portfolio._id,
      })
      return; // Skip
    }

    const populatePaths: PopulateOptions[] = [
      { path: 'initiative' },
      { path: 'survey', select: 'effectiveDate' },
      { path: 'organization', select: 'name' },
    ].filter(({ path }) => !portfolio.populated(path));
    if (populatePaths.length > 0) {
      await portfolio.populate(populatePaths);
    }

    const { initiative, organization, survey } = portfolio;
    if (!initiative || !organization || !survey) {
      this.logger.error(`Failed to load relation for assurance portfolio ${portfolio._id}`)
      return;
    }

    const portfolioId = String(portfolio._id);
    const reportName = this.generateReportName({
      _id: portfolio._id,
      initiative: initiative,
      effectiveDate: survey.effectiveDate,
    });

    const assurerName = organization.name;

    const org = await this.rootInitiativeService.getOrganization(initiative);
    const customAttributes: CustomAttributes = {
      orgId: org?._id.toString(),
      appConfigCode: org?.appConfigCode,
      domain: tryGetContext()?.origin,
      page: NotificationPage.SurveyAssurance,
      initiativeId: String(portfolio.initiativeId),
      surveyId: String(survey._id),
      portfolioId,
    };

    const recipientIds = new Set<string>();

    // Survey Admin & and the original creator of assurance portfolio
    survey.roles?.admin.forEach((id) => recipientIds.add(String(id)))
    const ownerId = portfolio.history.find(h => h.action === AssuranceAction.Created)?.userId;
    if (ownerId) {
      recipientIds.add(String(ownerId));
    }

    const recipients = Array.from(recipientIds).map(id => ({ id: String(id) }));

    await this.notificationService.createNotification({
      title: 'Assurance completed',
      content: reportName,
      category: NotificationCategory.AssuranceComplete,
      topic: `assurance-${portfolioId}`,
      customAttributes: customAttributes,
      actionUrl: UrlMapper.notificationUrl(customAttributes),
      recipients,
      overrides: {
        email: {
          subject: `G17Eco - Assurance completed`,
          title: `<p>Assurance from <strong>${assurerName}</strong> has been <strong>completed</strong> on the <strong>${reportName}</strong> ${SURVEY.SINGULAR}.</p>`,
          content: '',
          actionText: 'GO TO PLATFORM',
        },
      },
    })
  }

  public async sendJoinRequest(onboarding: OnboardingModel, context: { domain?: string }) {

    if (onboarding.type !== ObType.JoinRequest) {
      this.logger.error(`Trying to send JoinRequest for wrong onboarding type`, {
        obId: onboarding._id,
        type: onboarding.type ?? '',
      })
      return false;
    }

    if (!onboarding.populated('initiative')) {
      await onboarding.populate('initiative');
    }

    const initiative = onboarding.initiative;
    if (!initiative) {
      throw new Error('Trying to send a message with invalid initiativeId');
    }

    const recipients = (
      await User.find(
        {
          permissions: {
            $elemMatch: {
              initiativeId: initiative._id,
              permissions: { $in: [UserRoles.Owner, UserRoles.Manager] },
            },
          },
        },
        { _id: 1 }
      )
    ).map((user) => ({ id: String(user._id) }));

    const org = await this.rootInitiativeService.getOrganization(initiative);
    const initiativeId = String(onboarding.initiativeId);
    const customAttributes = {
      orgId: org._id.toString(),
      domain: context.domain,
      appConfigCode: org.appConfigCode,
      page: NotificationPage.ManageUsers,
      initiativeId: initiativeId,
      onboardingId: onboarding._id.toString(),
    };

    const content = `${onboarding.user.email} has requested to join your organisation. Visit the Manage Users page from Admin Settings.`;

    await this.notificationService.createNotification({
      title: 'New user requesting access',
      content: `${onboarding.user.email} has requested to join your organisation.`,
      category: NotificationCategory.Announcements,
      topic: `onboarding-${initiativeId}`,
      customAttributes: customAttributes,
      actionUrl: UrlMapper.notificationUrl(customAttributes),
      recipients,
      overrides: {
        email: {
          subject: `G17Eco - New user requesting access`,
          title: content,
          content: '',
          actionText: 'MANAGE USERS',
        },
      },
    })

  }

  private generateReportName(nameData: GenerateReportNameParams) {

    const { initiative, effectiveDate, _id } = nameData;

    const nameParts = [];
    if (initiative) {
      nameParts.push(`${initiative.name}:`);
    }

    if (effectiveDate) {
      nameParts.push(customDateFormat(effectiveDate, DateFormat.YearMonth));
    }

    // Fallback to id
    if (nameParts.length === 0) {
      nameParts.push('(' + String(_id) + ')');
    }

    // ~ Danshin Pharma: September 2021 report
    return nameParts.join(' ');
  }

  private getCategoryFromUserMessage(payload: MessagePayload): NotificationCategory {
    switch (payload.type) {
      case MessageType.Question:
        return NotificationCategory.UserToQuestionUsers;
      case MessageType.BulkSurvey:
      case MessageType.Survey:
        return NotificationCategory.UserToSurveyUsers;
      case MessageType.User:
      default:
        return NotificationCategory.UserToUsers;
    }
  }

  private getRecipientsFromUserMessage(payload: MessagePayload): RecipientData[] {
    switch (payload.type) {
      case MessageType.Question:
      case MessageType.Survey:
      case MessageType.BulkSurvey:
        return Array.from(new Set([...payload.contributorIds, ...payload.verifierIds])).map(id => ({ id }));
      case MessageType.User:
      default:
        return payload.userIds.map(id => ({ id }));
    }
  }

  private async getCustomAttributesFromUserMessage(payload: MessagePayload): Promise<CustomAttributes | undefined> {
    switch (payload.type) {
      case MessageType.Question: {
        const utrv = await UniversalTrackerValue.findById(payload.utrvId).populate('initiative').lean().exec();
        if (!utrv || !utrv.initiative) {
          throw new Error('Trying to send a message with invalid utrvId');
        }

        const org = await this.rootInitiativeService.getOrganization(utrv.initiative);

        return {
          page: NotificationPage.QuestionView,
          orgId: org?._id.toString(),
          appConfigCode: org?.appConfigCode,
          domain: tryGetContext()?.origin,
          initiativeId: String(utrv.initiativeId),
          surveyId: utrv.compositeData?.surveyId?.toString(),
          utrvId: payload.utrvId,
        };
      }
      case MessageType.Survey: {
        const survey = await Survey.findById(payload.surveyId).populate('initiative').lean().exec();
        if (!survey || !survey.initiative) {
          throw new Error('Trying to send a message with invalid surveyId');
        }

        const org = await this.rootInitiativeService.getOrganization(survey.initiative);

        return {
          page: NotificationPage.SurveyOverview,
          orgId: org?._id.toString(),
          appConfigCode: org?.appConfigCode,
          domain: tryGetContext()?.origin,
          initiativeId: String(survey.initiativeId),
          surveyId: payload.surveyId,
        };
      }
      case MessageType.BulkSurvey: {
        const initiative = await Initiative.findById(payload.initiativeId).exec();
        if (!initiative || !payload.initiativeId) {
          throw new Error('Trying to send a message with invalid initiativeId');
        }
        const org = await this.rootInitiativeService.getOrganization(initiative);
        return {
          page: NotificationPage.ReportView,
          orgId: org?._id.toString(),
          appConfigCode: org?.appConfigCode,
          domain: tryGetContext()?.origin,
          initiativeId: String(payload.initiativeId),
          surveyId: payload.surveyId,
        };
      }
      default:
        return undefined;
    }
  }

  public async sendUserGeneratedNotification(fromUserId: string, payload: MessagePayload) {
    const category = this.getCategoryFromUserMessage(payload);
    const customAttributes = await this.getCustomAttributesFromUserMessage(payload);
    const stripHtmlConfig = { ALLOWED_TAGS: ['#text'] };
    const sanitizedMessage = DOMPurify.sanitize(payload.message, stripHtmlConfig);
    const sanitizedSubject = DOMPurify.sanitize(payload.subject, stripHtmlConfig);

    await this.notificationService.createNotification({
      title: sanitizedSubject,
      content: sanitizedMessage,
      category: category,
      topic: `${category}-${fromUserId}`,
      customAttributes: customAttributes,
      actionUrl: customAttributes ? UrlMapper.notificationUrl(customAttributes) : undefined,
      recipients: this.getRecipientsFromUserMessage(payload),
      preferencesOverride: {
        email: payload.sendByEmail === true ? true : false,
        in_app: true,
        mobile_push: false,
        web_push: false
      }
    })
  }

  public async sendBulkSurveysGeneratingNotification(
    fromUserId: ObjectId,
    data: Pick<BulkSurveysData, 'initiativeId' | 'templateId'>,
    historyId: ObjectId
  ) {
    const [org] = await InitiativeRepository.getRootInitiativesForIds([data.initiativeId]);
    const customAttributes: CustomAttributes = {
      page: NotificationPage.TemplateHistory,
      orgId: org?._id.toString(),
      appConfigCode: org?.appConfigCode,
      initiativeId: String(data.initiativeId),
      domain: tryGetContext()?.origin,
      templateId: String(data.templateId),
      templateHistoryId: String(historyId),
    };
    await this.notificationService.createNotification({
      title: `${SURVEY.CAPITALIZED_SINGULAR} creation is processing`,
      content: `Click here to view the progress of bulk ${SURVEY.SINGULAR} creation`,
      category: NotificationCategory.BulkSurveys,
      topic: `bulk-surveys-${String(data.templateId)}`,
      customAttributes,
      actionUrl: UrlMapper.notificationUrl(customAttributes),
      recipients: [{ id: String(fromUserId) }],
    });
  }

  public async sendBulkSurveysGeneratedNotification(
    fromUserId: ObjectId,
    data: Pick<BulkSurveysData, 'initiativeId' | 'templateId'>,
    history: SurveyTemplateHistoryModelPlain
  ) {
    const failure = history.results.filter((result) => result.status === JobStatus.Error);
    const appendText = failure.length > 0 ? `(${SURVEY.CAPITALIZED_PLURAL} failed - ${failure.length})` : '';
    const [org] = await InitiativeRepository.getRootInitiativesForIds([data.initiativeId]);

    const customAttributes: CustomAttributes = {
      page: NotificationPage.TemplateHistory,
      orgId: org?._id.toString(),
      appConfigCode: org?.appConfigCode,
      initiativeId: String(data.initiativeId),
      domain: tryGetContext()?.origin,
      templateId: String(data.templateId),
      templateHistoryId: String(history._id),
    };

    await this.notificationService.createNotification({
      title: `${SURVEY.CAPITALIZED_SINGULAR} creation has been completed`,
      content: `Click here to view creation results ${appendText}`,
      category: NotificationCategory.BulkSurveys,
      topic: `bulk-surveys-${String(data.templateId)}`,
      customAttributes,
      actionUrl: UrlMapper.notificationUrl(customAttributes),
      recipients: [{ id: String(fromUserId) }],
    });
  }

  private getFullName({ user, fallback = 'a user' }: { user: Pick<UserModel, 'firstName' | 'surname'> | undefined; fallback?: string }) {
    return user ? `${user.firstName} ${user.surname}` : fallback;
  }

  private getTypeCode({
    utr,
    fallback = '',
  }: {
    utr: Pick<UniversalTrackerPlain, 'typeCode'> | null;
    fallback?: string;
  }) {
    return utr?.typeCode ? utr.typeCode : fallback;
  }
}

let instance: NotificationManager;
export const getNotificationManager = () => {
  if (!instance) {
    instance = new NotificationManager(
      wwgLogger,
      getNotificationService(),
      getRootInitiativeService(),
    );
  }
  return instance;
}
