/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import type { InitiativePlain } from '../../models/initiative';
import Initiative from '../../models/initiative';
import { MaterialityAssessmentUtrCodes } from '../../routes/validation-schemas/materiality-assessment';
import { customDateFormat } from '../../util/date';
import { assessmentTypeLabelMap } from '../materiality-assessment/constants';
import { getAddressText } from '../../util/initiative';
import { getCountryByCode } from '../location/CountryService';
import type { SlackProvider } from './delivery/Slack';
import { getSlackProvider, SlackNotificationCategory } from './delivery/Slack';
import type { CreateAssessmentParams } from '../materiality-assessment/types';
import ValueList from '../../models/valueList';
import config from '../../config';
import type { ObjectId } from 'bson';

export interface OnboardingMTNotificationParams {
  user: CreateAssessmentParams['user'];
  context?: CreateAssessmentParams['context'];
  metadata?: CreateAssessmentParams['metadata'];
  initiativeId: ObjectId;
  type: OnboardingMaterialityTrackerType;
}

export enum OnboardingMaterialityTrackerType {
  NewOnboarding = 'New onboarding',
  CTIntegration = 'CT integration',
}

export class SlackNotificationService {
  constructor(private slackProvider: SlackProvider) {}

  private async getContextValueLists(context: CreateAssessmentParams['context']) {
    const valueListCodes = Object.keys(context);
    const valueLists = await ValueList.find({ code: { $in: valueListCodes } })
      .lean()
      .exec();
    return new Map(valueLists.map((v) => [v.code, v.options]));
  }

  private async getOnboardingMaterialityTrackerMessage(
    params: Omit<OnboardingMTNotificationParams, 'initiativeId'> & {
      initiative: InitiativePlain;
    }
  ): Promise<string> {
    const { user, context, metadata, initiative, type } = params;
    const messageParts: string[] = [];

    messageParts.push('A new client has completed the Materiality Tracker onboarding process.');

    messageParts.push(
      '\n*User Details:*',
      `- *Name:* ${user.firstName} ${user.surname}`,
      `- *Email:* ${user.email}`
    );

    messageParts.push(
      '\n*Company Details:*',
      `- *Name:* ${initiative.name}`,
      `- *Location:* ${initiative.country ? getCountryByCode(initiative.country)?.name ?? '-' : '-'}`,
      `- *Address:* ${getAddressText(initiative.address)}`
    );

    if (metadata || context) {
      messageParts.push('\n*Assessment Details:*');
      
      if (metadata) {
        messageParts.push(
          `- *Date:* ${customDateFormat(metadata.effectiveDate) || '-'}`,
          `- *Type:* ${assessmentTypeLabelMap[metadata.assessmentType] || '-'}`
        );
      }

      if (context) {
        const valueListsMap = await this.getContextValueLists(context);
        const getContextValue = (valueListCode: string, optionCode: string) => {
          const valueListOptions = valueListsMap.get(valueListCode);
          const option = valueListOptions?.find((o) => o.code === optionCode);
          return option?.name ?? optionCode;
        };

        const contextDetails: [string, MaterialityAssessmentUtrCodes][] = [
          ['Industry', MaterialityAssessmentUtrCodes.Sector],
          ['Operating time', MaterialityAssessmentUtrCodes.OperationTime],
          ['Number of staff', MaterialityAssessmentUtrCodes.NumStaff],
          ['Annual sales', MaterialityAssessmentUtrCodes.AnnualSales],
          ['Total liabilities', MaterialityAssessmentUtrCodes.CapitalEmployed],
        ];

        contextDetails.forEach(([label, code]) => {
          messageParts.push(
            `- *${label}:* ${getContextValue(code, context[code])}`
          );
        });
      }
    }

    messageParts.push(`\n*Onboarding Type:* ${type}`);

    return messageParts.join('\n');
  }

  public async sendMaterialityOnboardingNotification(request: OnboardingMTNotificationParams) {
    const { initiativeId, user, context, metadata, type } = request;

    const initiative = await Initiative.findById(initiativeId).lean().orFail().exec();
    const content = await this.getOnboardingMaterialityTrackerMessage({
      user,
      context,
      metadata,
      initiative,
      type,
    });

    await this.slackProvider.create({
      title: 'New Materiality Tracker Onboarding',
      content: content,
      category: SlackNotificationCategory.Announcements,
      topic: `onboarding-${initiativeId}`,
      channel: config.notifications.slack.channels.signup,
    });
  }
}

let instance: SlackNotificationService;
export const getSlackNotificationService = () => {
  if (!instance) {
    instance = new SlackNotificationService(getSlackProvider());
  }
  return instance;
};
