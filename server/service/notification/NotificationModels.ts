/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { HydratedDocument, Types } from 'mongoose';
import {
  NotificationCategory,
  NotificationCategoryGroup,
  NotificationRole,
  NotificationRoleMap,
} from './NotificationTypes';
import { TimePeriod } from '../../util/date';

export enum NotificationServices {
  MagicBell = 'magicBell',
  Email = 'email',
}
export enum NotificationChannels {
  Email = 'email',
  InApp = 'in_app',
  MobilePush = 'mobile_push',
  WebPush = 'web_push'
}

export interface NotificationCategoryPreference {
  email: boolean,
  in_app: boolean,
  mobile_push: boolean,
  web_push: boolean,
  group?: NotificationCategoryGroup,
}

export interface NotificationCategoryPreferences {
  [key: string]: NotificationCategoryPreference | undefined;
}

export interface MagicBellPreferences {
  notification_preferences: {
    categories: NotificationCategoryPreferences,
    groups?: NotificationCategoryGroup[]
  }
}



export type MagicBellChannelApiV2 = {
  slug: string;
  enabled: boolean;
}

export type MagicBellCategoryApiV2 = {
  slug: string;
  channels: MagicBellChannelApiV2[];
}

export interface MagicBellPreferencesApiV2 {
  notification_preferences: {
    categories: MagicBellCategoryApiV2[]
  }
}

interface NotificationSchedule {
  isSummary: boolean;
  period: TimePeriod;
}

export interface NotificationPreferencesPlain<T = Types.ObjectId> {
  userId: T;
  magicBell: MagicBellPreferences;
  notificationSchedule?: NotificationSchedule;
  created?: Date;
}

export type NotificationPreferencesModel = HydratedDocument<NotificationPreferencesPlain>;

export interface NotificationPreferencesUpdateRequest {
  service: NotificationServices;
  category: string | string[];
  channel: NotificationChannels;
  setting: boolean;
}

export interface NotificationSummariesUpdateRequest {
  period: TimePeriod;
  isSummary: boolean;
}

export const getDefaultPreferences = (): NotificationCategoryPreference => ({
  email: true,
  in_app: true,
  mobile_push: false,
  web_push: false,
});

export const getDefaultPreferencesAssurer = (): NotificationCategoryPreference => ({
  email: true,
  in_app: true,
  mobile_push: false,
  web_push: false,
});

export const getDefaultCategories = () => Object.values(NotificationCategory).reduce((a, c) => {
  a[c] = getDefaultPreferences();
  // if category is within assurer notifications e.g. assurance_start, use different defaults
  const assurerCategories = NotificationRoleMap.get(NotificationRole.Assurer)
  if(assurerCategories && assurerCategories.includes(c)) {
    a[c] = getDefaultPreferencesAssurer();
  }
  return a;
}, {} as NotificationCategoryPreferences);

export const getDefaultMagicBell = () => ({
  notification_preferences: {
    categories: {
      ...getDefaultCategories(),
    },
  }
});

export const getDefaultUserSettings = (userId: Types.ObjectId): NotificationPreferencesPlain => {
  return {
    userId,
    magicBell: getDefaultMagicBell()
  }
}

export const getMergedPreferences = (
  userId: Types.ObjectId,
  preferences?: NotificationPreferencesModel | NotificationPreferencesPlain,
): NotificationPreferencesPlain => {

  const plain = preferences && 'toObject' in preferences ? preferences.toObject() : preferences;
  const mergedPreferences = plain ?? getDefaultUserSettings(userId)

  const current = mergedPreferences.magicBell.notification_preferences.categories;
  const categories = getDefaultCategories();

  // Merge based on default categories
  Object.entries(current).forEach(([k, v]) => {
    if (categories[k]) {
      categories[k] = v;
    }
  })
  mergedPreferences.magicBell.notification_preferences.categories = categories

  return mergedPreferences
}
