import { LoggerInterface, wwgLogger } from '../wwgLogger';
import { ObjectId } from 'bson';
import {
  NotificationCategory,
  NotificationCategoryGroup,
  notificationCategoryGroupNameMap,
  NotificationCategoryGroupsMap,
  NotificationModel,
} from './NotificationTypes';
import NotificationSchedule, { NotificationScheduleExtended } from '../../models/notification-schedule';
import { PipelineStage } from 'mongoose';
import { customDateFormat, DateFormat, getTimeRangeByPeriod, TimePeriod } from '../../util/date';
import Notification from '../../models/notification';
import { InitiativePlain } from '../../models/initiative';
import { EmailProvider, getEmailProvider } from './delivery/Email';
import { AppConfigService, getAppConfigService } from '../app/AppConfigService';
import { getRecipientsService, RecipientsService } from './delivery/Recipients';
import { UrlMapper } from '../url/UrlMapper';

const CRON_LIMIT = 50;
const CATEGORY_NOTIFICATION_LIMIT = 20;

/**
 * This service mainly include:
 * - handling summaries email notification
 */
export class EmailScheduleService {
  constructor(
    private logger: LoggerInterface,
    private emailProvider: EmailProvider,
    private appConfigService: AppConfigService,
    private recipientsService: RecipientsService
  ) {}

  private getMapNotificationCategoryGroup(notifications: NotificationModel[]) {
    const categoryGroupMap = new Map<NotificationCategoryGroup, NotificationModel[]>();
    notifications.forEach((n) => {
      for (const [group, categories] of NotificationCategoryGroupsMap) {
        if (categories.includes(n.category)) {
          const existed = categoryGroupMap.get(group) ?? [];

          // Limit 20 items per category, so the summaries email will not have a long list of notifications
          if (existed.length < CATEGORY_NOTIFICATION_LIMIT) {
            categoryGroupMap.set(group, [...existed, n]);
          }
        }
      }
    });
    return categoryGroupMap;
  }

  private renderNotificationContent(notification: NotificationModel) {
    if (!notification.customAttributes) {
      return '<br />';
    }

    const actionUrl = UrlMapper.notificationUrl(notification.customAttributes);
    return `
      <tr>
        <td align="left" style="font-family: sailec, sans-serif; color: #6a6c70; padding-top: 16px; padding-bottom: 16px;">
          <p style="font-size: 11px; line-height: 17px; font-weight: normal; font-style: normal; margin: 0;">
            ${customDateFormat(notification.created, DateFormat.DayMonth)}
          </p>
          <div style="font-size: 16px; line-height: 24px; font-weight: normal; font-style: normal;">
            ${notification.content}
          </div>
        </td>
        <td align="right" style="padding-top: 16px; padding-bottom: 16px;">
          <a href="${actionUrl}" style="font-family: sailec, sans-serif; font-size: 11px !important; line-height: 11px !important; font-weight: 500; font-style: normal; text-decoration: none; color: #6a6c70;" target="_blank">
            view
          </a>
        </td>
      </tr>
    `;
  }

  private renderCategoryGroupContent(categoryGroup: NotificationCategoryGroup) {
    return `
    <tr style="border-top: 1px solid #6a6c70;">
      <td align="left" style="font-family: sailec, sans-serif; font-size: 16px; line-height: 24px; font-weight: normal; text-transform: uppercase; color: #6a6c70; padding-top: 16px; padding-bottom: 16px;">
        ${notificationCategoryGroupNameMap[categoryGroup]}
      </td>
      <td></td>
    </tr>`;
  }

  private renderSummariesContent(initiative: InitiativePlain, notifications: NotificationModel[]) {
    const initiativeName = `
      <tr>
        <td align="left" style="font-family: sailec, sans-serif; font-size: 19px; line-height: 24px; font-weight: 500; font-style: normal; text-transform: uppercase; color: #6a6c70; padding-bottom: 24px;">${initiative.name}</td>
        <td></td>
      </tr>
    `;

    const notificationCategoryMap = this.getMapNotificationCategoryGroup(notifications);

    let summariesContent = initiativeName;

    for (const [group, items] of notificationCategoryMap) {
      let contentGroup = '';

      // Add category group
      contentGroup = contentGroup.concat(this.renderCategoryGroupContent(group));

      // Add notifications details
      items.forEach((item) => (contentGroup = contentGroup.concat(this.renderNotificationContent(item))));

      summariesContent = summariesContent.concat(`
        <tr><td style="text-align: center;"><hr style="width: 100px; display: inline-block; border-color: #e6e9ed;"></hr></td></tr>
        <table style="width: 100%">
          <tbody style="width: 100%">${contentGroup}</tbody>
        </table>
      `);
    }

    return summariesContent;
  }

  public async sendSummary(params: {
    userId: ObjectId;
    initiative: InitiativePlain;
    processNotifications: NotificationModel[];
    period: TimePeriod;
  }) {
    const { userId, initiative, processNotifications, period } = params;

    const code = initiative.appConfigCode;
    const appConfig = code ? await this.appConfigService.getByCode(code) : undefined;

    const bottomEmailContent = this.renderSummariesContent(initiative, processNotifications);

    const createSummariesData = {
      title: 'Weeky email summaries',
      content: `Here is your summaries of G17Eco notifications for the past 1 ${period}.`,
      category: NotificationCategory.ScheduleEmailSummaries,
      recipients: [{ id: userId.toHexString() }],
      domain: undefined,
      appConfig,
      overrides: {
        email: {
          title: '',
          actionText: 'GO TO PLATFORM',
          bottomContent: bottomEmailContent,
        },
      },
    };
    const recipientsData = await this.recipientsService.getRecipients(createSummariesData);

    return this.emailProvider.create({ ...createSummariesData, recipients: recipientsData });
  }

  private async postSendSummary(schedule: NotificationScheduleExtended) {
    return NotificationSchedule.findOneAndUpdate({ _id: schedule._id }, { completedDate: new Date() });
  }

  // This will send summaries email group by user and initiative
  public async processNotificationSchedules(schedules: NotificationScheduleExtended[]) {
    for (const schedule of schedules) {
      const { userId, notificationIds, initiative, notificationPreferences } = schedule;

      // Those process notification will either be outdated or be not processed when user toggle off summary,
      // So to prevent re-run this process, we will mark it as completed
      // TODO: Improvement: to prevent losing these notification,
      // should send these schedule notifications by the end of the week, or directly send them when user toggle off summary
      if (!notificationPreferences?.notificationSchedule?.isSummary) {
        await this.postSendSummary(schedule);
        continue;
      }

      const { startTime, endTime } = getTimeRangeByPeriod(notificationPreferences.notificationSchedule.period);
      const pipeline: PipelineStage[] = [
        {
          $match: {
            _id: { $in: notificationIds },
            created: {
              $gte: startTime,
              $lte: endTime,
            },
          },
        },
        { $sort: { created: 1 } },
        // group by utrv to get the latest notification
        // this prevents sending out the outdated notifications
        {
          $group: {
            _id: '$topic',
            notificationId: { $last: '$_id' },
            title: { $last: '$title' },
            content: { $last: '$content' },
            category: { $last: '$category' },
            customAttributes: { $last: '$customAttributes' },
            actionUrl: { $last: '$actionUrl' },
            created: { $last: '$created' },
          },
        },
        {
          $project: {
            _id: '$notificationId',
            title: 1,
            content: 1,
            category: 1,
            topic: '$_id',
            customAttributes: 1,
            actionUrl: 1,
            created: 1,
          },
        },
        {
          $sort: { created: -1 }
        }
      ];
      // Only process notifications within period and the latest update ones
      const processNotifications = await Notification.aggregate<NotificationModel>(pipeline).exec();

      await this.sendSummary({
        userId,
        initiative,
        processNotifications,
        period: notificationPreferences.notificationSchedule.period,
      });

      await this.postSendSummary(schedule);

      this.logger.info('A summaries notification email has been sent', {
        userId,
        initiativeId: initiative._id.toHexString(),
        processNotifications: processNotifications.map(n => n._id.toHexString()),
      });
    }
  }

  /**
   * This process trigger periodically (hourly, daily, weekly, monthly, yearly)
   * to send out a summarized email
   */
  public async process(limit = CRON_LIMIT) {
    const pipeline: PipelineStage[] = [
      {
        $match: {
          completedDate: { $exists: false },
          initiativeId: { $exists: true }
        },
      },
      { $limit: limit },
      {
        $lookup: {
          from: 'notification-preferences',
          localField: 'userId',
          foreignField: 'userId',
          as: 'notificationPreferences',
        },
      },
      {
        $unwind: {
          path: '$notificationPreferences',
        },
      },
      {
        $lookup: {
          from: 'initiatives',
          localField: 'initiativeId',
          foreignField: '_id',
          as: 'initiative',
        },
      },
      {
        $unwind: {
          path: '$initiative',
        },
      },
    ];

    const schedules = await NotificationSchedule.aggregate<NotificationScheduleExtended>(pipeline).exec();
    await this.processNotificationSchedules(schedules);
  }
}

let instance: EmailScheduleService;
export const getEmailScheduleService = () => {
  if (!instance) {
    instance = new EmailScheduleService(
      wwgLogger,
      getEmailProvider(),
      getAppConfigService(),
      getRecipientsService()
    );
  }
  return instance;
};
