/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { BackgroundJobPlain, JobType } from '../../models/backgroundJob';

export type JobResult = Partial<Pick<BackgroundJobPlain, '_id'>> &
  Pick<BackgroundJobPlain, 'status' | 'completedDate' | 'logs'> & {
    /**
     * Allow next handler to pick up the job and continue working on the same one
     */
    passToNextHandler?: boolean;
  };

export interface BackgroundWorker {
  /**
   * First job handler will covert it
   */
  canHandle(jobType: JobType): boolean;

  /**
   * Process Job and return a result.
   */
  process(jobId: string, options: { retry?: boolean }): Promise<JobResult>;
}
