/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import ContextError from '../../error/ContextError';
import { BackgroundJobModel, JobType } from '../../models/backgroundJob';
import { <PERSON><PERSON>or<PERSON>, JobResult } from '../background-process/types';
import { LoggerInterface } from '../wwgLogger';
import { BackgroundBaseWorkflow } from './BackgroundBaseWorkflow';

/**
 * Workers are always triggered in Google Cloud Job runs environments
 * allow as to do heavy work without affecting normal API server operations.
 */
export class BackgroundBaseWorker<JobModel extends BackgroundJobModel, T extends BackgroundBaseWorkflow<JobModel>>
  implements BackgroundWorker
{
  constructor(private logger: LoggerInterface, protected workflow: T) {}

  public canHandle(jobType: JobType): boolean {
    return this.workflow.canHandle(jobType);
  }

  public async process(jobId: string, options: { retry: boolean }): Promise<JobResult> {
    this.logger.info(`Start processing, jobId: ${jobId || '-'}`);
    const job = jobId ? await this.workflow.findJob(jobId) : await this.workflow.findPendingJob();

    if (!this.workflow.canHandle(job.type)) {
      throw new ContextError(`Received invalid job ${job._id} type ${job.type}`, {
        jobId: job._id,
        type: job.type,
        created: job.created,
      });
    }

    // Progress job to the next stage.
    return this.workflow.progressJob(job as JobModel);
  }
}
