/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { JobRunClient } from './JobRunClient';
import { Background<PERSON>obPlain } from '../../models/backgroundJob';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import { execFile } from 'node:child_process';

export class LocalRunClient implements JobRunClient {
  constructor(private logger: LoggerInterface) {}

  public async runJob(job: Pick<BackgroundJobPlain, '_id' | 'type'>) {
    const { _id: jobId, type: jobType } = job;
    const childProcess = execFile('node', ['./dist/scripts/background.js', jobType, jobId.toHexString()], (error) => {
      if (error) {
        this.logger.error(error);
      }
    });

    const result = { pid: childProcess.pid, jobId: jobId.toHexString(), jobType };
    this.logger.info('Running local background job', result);
    childProcess.stdout?.on('data', (data) => this.logger.info(data));
    childProcess.stderr?.on('data', (errorData) => this.logger.error(errorData));

    return new Promise((resolve, reject) => {
      childProcess.on('exit', (code) => {
        if (code === 0) {
          resolve(result);
        } else {
          reject(new Error(`Job failed with code ${code}`));
        }
      });
    });
  }
}

let instance: LocalRunClient;
export const getLocalRunClient = () => {
  if (!instance) {
    instance = new LocalRunClient(wwgLogger);
  }
  return instance;
};
