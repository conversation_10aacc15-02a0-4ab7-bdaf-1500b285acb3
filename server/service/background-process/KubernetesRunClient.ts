/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import config from "../../config";
import ContextError from "../../error/ContextError";
import { JobRunClient } from "./JobRunClient";
import { BackgroundJobPlain } from "../../models/backgroundJob";
import { BatchV1Api, KubeConfig } from "@kubernetes/client-node";
import { LoggerInterface, wwgLogger } from "../wwgLogger";
import { V1Job } from "@kubernetes/client-node/dist/gen/model/v1Job";
import { generateRandomToken } from "../crypto/token";

export class KubernetesRunClient implements JobRunClient {

  constructor(
    private logger: LoggerInterface,
  ) {
  }

  public async runJob(job: Pick<BackgroundJobPlain, '_id' | 'type'>) {

    const { type: jobType, _id: jobId } = job;

    const { templateName, namespace } = config.kubernetes.backgroundJob;
    const jobArgs = [jobType, jobId.toString()];

    const kc = new KubeConfig();
    kc.loadFromCluster();
    const k8sBatchApi = kc.makeApiClient(BatchV1Api);

    try {
      const jobTemplate = await k8sBatchApi.readNamespacedJob(templateName, namespace);
      const v1Job = jobTemplate.body;
      const jobSpec = v1Job.spec;

      if (!jobSpec?.template.spec) {
        throw new ContextError('Invalid job template', {
          templateName,
          namespace,
        });
      }

      const suffix = '-' + Date.now().toString();
      const type = jobType.replaceAll('_', '-');
      const name = `job-${type}-${jobId.toString()}`;
      // 63-character limit on label
      const maxNameLength = 63;
      const newTemplateName = name.slice(0, maxNameLength - suffix.length) + suffix;

      // Create a new job instance based on the template
      const jobInstance: V1Job = {
        ...v1Job,
        apiVersion: 'batch/v1',
        kind: 'Job',
        metadata: {
          name: newTemplateName,
          labels: {
            'g17ecoJobType': type,
            'g17ecoJobId': jobId.toString(),
          }
        },
        spec: {
          ...jobSpec,
          // Clear requirements
          ttlSecondsAfterFinished: 3600, // Remove the job after 1 hour
          selector: undefined,
          template: {
            ...jobSpec.template,
            metadata: {
              ...jobSpec.template.metadata,
              labels: {
                name: newTemplateName,
                'batch.kubernetes.io/job-name': newTemplateName,
              },
            },
            spec: {
              ...jobSpec.template.spec,
              containers: jobSpec.template.spec.containers.map(container => ({
                ...container,
                command: ["/usr/bin/dumb-init", "node", "scripts/background.js"],
                args: jobArgs,
              })),
            },
          },
        },
      };

      console.log(jobInstance)
      // Create the job
      const response = await k8sBatchApi.createNamespacedJob(namespace, jobInstance);
      this.logger.info('Job created:', response.body);
      return response;
    } catch (err) {
      this.logger.error(new ContextError(`Error running ${jobType} job`, {
        cause: err,
        jobType,
        jobId,
      }));
    }
  }
}

let instance: KubernetesRunClient;
export const getKubernetesRunClient = () => {
  if (!instance) {
    instance = new KubernetesRunClient(
      wwgLogger,
    );
  }
  return instance;
}
