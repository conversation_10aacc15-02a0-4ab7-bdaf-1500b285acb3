/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { Background<PERSON>ob<PERSON>lain } from "../../models/backgroundJob";
import config from "../../config";
import { JobsClient } from "@google-cloud/run";
import { getGoogleCloudCredentials } from "../google-cloud/credentials";
import { JobRunClient } from "./JobRunClient";

export class GoogleRunClient implements JobRunClient {

  constructor(
    private runClientGoogle: JobsClient,
    private jobName: string,
  ) {
  }

  public async runJob(job: Pick<BackgroundJobPlain, '_id' | 'type'>) {
    const { type: jobType, _id: jobId } = job;

    const { projectId, cloudRun } = config.googleCloud;
    const fullName = `projects/${projectId}/locations/${cloudRun.job.region}/jobs/${this.jobName}`;

    // Run request
    const [operation] = await this.runClientGoogle.runJob({
      name: fullName,
      overrides: {
        containerOverrides: [
          {
            // cmd:node args= scripts/background.js jobType jobId
            args: ['scripts/background.js', jobType, jobId.toString()],
          }
        ],
      }
    });
    const [response] = await operation.promise();

    return response;
  }
}

let instance: GoogleRunClient;
export const getGoogleRunClient = () => {
  if (!instance) {
    instance = new GoogleRunClient(
      new JobsClient(getGoogleCloudCredentials()),
      config.googleCloud.cloudRun.job.name,
    );
  }
  return instance;
}
