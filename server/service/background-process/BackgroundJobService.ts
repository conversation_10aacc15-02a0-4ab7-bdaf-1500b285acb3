/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

// Imports the Run library
import config from '../../config';
import { ObjectId } from 'bson';
import BackgroundJob, {
  BackgroundJobModel,
  BackgroundJobPlain,
  MAX_RETRY_COUNT,
  supportedJobTypes,
} from '../../models/backgroundJob';
import ContextError from '../../error/ContextError';
import { finalStatuses } from '../jobs';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import { UserPlain } from '../../models/user';
import { JobRunClient } from './JobRunClient';
import { getKubernetesRunClient } from './KubernetesRunClient';
import { getGoogleRunClient } from './GoogleRunClient';
import { getLocalRunClient } from './LocalRunClient';
import { BackgroundBaseWorkflow } from './BackgroundBaseWorkflow';
import UserError from '../../error/UserError';

/**
 * Implement background job service using
 * GCP Cloud Run Jobs service
 */
export class BackgroundJobService {
  constructor(private logger: LoggerInterface, private runClient: JobRunClient) {}

  /**
   *  Run job through cloud run job service
   *  Note that currently we are not able to override the arguments
   *  therefore the jobId is not used, but once that becomes available in client
   *  library, we will be able to run specified job by overriding arguments passed
   *  on runJob library command to include jobId as second argument
   *  {
   *    cmd: 'node'
   *    args: ['scripts/background.js', jobType, jobId]
   *  }
   * Allow to run the job from background job model.
   * Not yet working as expected until overrides are supported
   */
  public runFromJob(job: Pick<BackgroundJobPlain, '_id' | 'type'>) {
    if (!supportedJobTypes.includes(job.type)) {
      throw new ContextError(`Not supported run using job type "${job.type}"`, {
        jobId: job._id,
        jobType: job.type,
      });
    }

    return this.runClient.runJob(job);
  }

  public async runById(jobId: string | ObjectId, user: Pick<UserPlain, '_id'>) {
    this.logger.info(`Trigger background job ${jobId}`, {
      userId: user._id,
      jobId,
    });

    const job = await BackgroundJob.findOne(
      {
        _id: new ObjectId(jobId),
        status: { $nin: finalStatuses },
      },
      {
        type: 1,
        status: 1,
      }
    )
      .orFail()
      .exec();

    // Do not actually want to wait for response, just trigger
    this.runFromJob(job).catch((e) => {
      this.logger.error(
        new ContextError(`Failed to complete job run by id`, {
          cause: e,
          jobId: job._id.toString(),
          jobType: job.type,
          jobStatus: job.status,
        })
      );
    });

    return {
      jobId: job._id.toString(),
      jobType: job.type,
      jobStatus: job.status,
    };
  }

  public async resetAndRunJob(job: BackgroundJobModel, user: UserPlain): Promise<BackgroundJobModel> {
    // check if job retry count exceed job max count then throw error
    const maxRetryCount = job.maxRetryCount ?? MAX_RETRY_COUNT;
    if (job.retryCount >= maxRetryCount) {
      throw new UserError(`Job has reached max retry count ${job.retryCount}/${maxRetryCount}`, {
        maxRetryCount,
        currentRetryCount: job.retryCount,
        jobId: job._id,
      });
    }
    // Reset the job status
    await BackgroundBaseWorkflow.resetJob(job, user);

    this.logger.warn(`Job ${job._id} reset by ${user.email}`, {
      jobId: job._id,
      userId: user._id,
    });

    // Increment retry count and save
    job.retryCount = (job.retryCount || 0) + 1;
    await job.save();
    await this.runById(job._id, user);
    return job;
  }
}

const getClient = () => {
  if (config.localJobRunEnv) {
    return getLocalRunClient();
  }
  return config.containerEnv === "kubernetes" ? getKubernetesRunClient() : getGoogleRunClient();
}

let instance: BackgroundJobService;
export const getBackgroundJobService = () => {
  if (!instance) {
    instance = new BackgroundJobService(
      wwgLogger,
      getClient(),
    );
  }
  return instance;
}
