import BackgroundJob, { BackgroundJobModel, JobStatus, JobType } from "../../../models/backgroundJob";
import { <PERSON><PERSON><PERSON><PERSON>, JobResult } from "../types";
import { getSurveyTemplateJobManager, SurveyTemplateJobManager } from "../../survey-template/SurveyTemplateJobManager";
import { ObjectId } from "bson";
import { createLogEntry } from "../../jobs/logMessage";
import { LoggerInterface, wwgLogger } from "../../wwgLogger";

export class BulkSurveyCreateWorker implements BackgroundWorker {

  private readonly jobType = JobType.BulkSurveyCreate;

  constructor(
    private logger: LoggerInterface,
    private jobManager: SurveyTemplateJobManager
  ) {
  }

  public canHandle(jobType: JobType): boolean {
    return jobType === this.jobType;
  }

  public async process(jobId: string): Promise<JobResult> {
    const job = await this.loadJob(jobId);
    if (!job) {
      this.logger.info(`No job available for ${this.jobType}, skipping.`)
      return {
        status: JobStatus.Pending,
        logs: [createLogEntry(`No job found. JobId ${jobId}`)]
      }
    }

    return this.jobManager.runJob(job)
  }

  private async loadJob(jobId: string): Promise<BackgroundJobModel | null> {
    if (!jobId) {
      return BackgroundJob.findOne({
        type: JobType.BulkSurveyCreate,
        status: JobStatus.Pending
      }).sort({ priority: 1, created: -1 }).exec()
    }

    return BackgroundJob.findOne({
      _id: new ObjectId(jobId),
      type: JobType.BulkSurveyCreate,
      status: JobStatus.Pending,
    }).exec();
  }
}

let instance: BulkSurveyCreateWorker;
export const getBulkSurveyCreateWorker = () => {
  if (!instance) {
    instance = new BulkSurveyCreateWorker(
      wwgLogger,
      getSurveyTemplateJobManager(),
    );
  }
  return instance;
}
