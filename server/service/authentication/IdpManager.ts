/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import { LoggerInterface, wwgLogger } from '../wwgLogger';
import config from '../../config';

export interface IdentityProvider {
  code: string;
  name: string,
  logo?: string;
  idp: string,
}


interface IdpConfig {
  domain?: string,
  orgCode?: string
}


type IdpMap = Map<string, IdentityProvider>;

export class IdpManager {

  mapping: IdpMap

  constructor(
    private logger: LoggerInterface,
    idpMapping: string
  ) {
    this.mapping = this.mapFromMappingString(idpMapping);
  }

  private getLogoUrl(logo?: string) {
    return logo ? `${config.assets.cdn}/sso/logos/${logo}` : undefined;
  }

  private mapFromMappingString(mapping: string): IdpMap {

    const map = new Map();

    const items = mapping.split(',');
    this.logger.info(`Loading IdP configuration from mapping string`, items)

    items.forEach((idpItem) => {
      const [code, idp, name, logo] = idpItem.split(':')
        .map(prop => prop.trim())
        .filter(Boolean);

      const idpConfig: IdentityProvider = {
        code,
        idp,
        name,
        logo: this.getLogoUrl(logo),
      };

      if (!code || !idp || !name) {
        this.logger.error(`Missing required mapping properties`, idpConfig)
        return;
      }
      map.set(code, idpConfig);
    })

    return map;
  }

  public getMapping() {
    return this.mapping;
  }

  public async getIdpConfig(options: IdpConfig): Promise<IdentityProvider[]> {
    const map = [options.domain, options.orgCode].reduce((acc, code) => {
      if (code) {
        const orgIdp = this.mapping.get(code);
        if (orgIdp) {
          acc.set(orgIdp.code, orgIdp)
        }
      }
      return acc;
    }, new Map());

    return Array.from(map.values());
  }
}

let instance: IdpManager;
export const getIdpManager = () => {
  if (!instance) {
    instance = new IdpManager(
      wwgLogger,
      config.authentication.idpMapping,
    );
  }
  return instance;
}
