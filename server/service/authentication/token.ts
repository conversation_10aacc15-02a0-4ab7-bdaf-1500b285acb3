/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import config from '../../config';
import { JwtPayload, sign, verify, SignOptions } from 'jsonwebtoken';

const { secret, emailAccessTokenExpire } = config.jwt;

export const createToken = (
  payload: string | object | Buffer,
  expireTime: SignOptions['expiresIn'] = emailAccessTokenExpire
) => {
  return sign(payload, secret, { expiresIn: expireTime });
};

export const decodeToken = async (token: string): Promise<JwtPayload | string | undefined> => {
  return new Promise((resolve, reject) => {
    verify(token, secret, (err, decoded) => {
      err ? reject(err) : resolve(decoded);
    });
  });
};
