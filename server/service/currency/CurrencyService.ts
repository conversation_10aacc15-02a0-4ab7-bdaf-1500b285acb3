/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { LoggerInterface, wwgLogger } from "../wwgLogger";
import ContextError from "../../error/ContextError";

export class CurrencyService {

  /** Three-letter ISO currency code, in lowercase. */
  private readonly codeToSymbol: Record<string, string | undefined> = {
    gbp: '£',
    usd: '$',
    sdg: 'S$',
    eur: '€'
  }

  constructor(private logger: LoggerInterface) {
  }

  public toSymbol(currencyCode: string): string {
    const symbol = this.codeToSymbol[currencyCode.toLowerCase()];

    if (symbol) {
      return symbol
    }

    this.logger.error(new ContextError(`Failed to resolve ${currencyCode}`, {
      debugMessage: 'Need to add symbol to mapping table',
      currencyCode,
    }));

    return currencyCode;
  }
}

let instance: CurrencyService;
export const getCurrencyService = () => {
  if (!instance) {
    instance = new CurrencyService(
      wwgLogger,
    );
  }
  return instance;
}
