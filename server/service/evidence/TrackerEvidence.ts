/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import EvidenceUploader, { UploadRequest } from './EvidenceUploader';
import Document, {
  DocumentModel,
  LinkDocument,
  DocumentType,
} from '../../models/document';
import { ObjectId } from 'bson';
import { ExistingEvidence } from '../utr/model/actionRequest';
import { isFalsyOrEmpty } from '../../util/array';
import { AnyBulkWriteOperation } from 'mongoose';

export default class TrackerEvidence {
  constructor(protected uploader: EvidenceUploader, protected type: string) {}

  public async processUpload({
    ownerId,
    userId,
    files,
    filesDescriptions = [],
  }: Omit<UploadRequest, 'data' | 'type'> & { files: any }) {
    if (typeof files === 'object') {
      files = Array.from(Object.keys(files), (k) => files[k]);
    }

    if (!Array.isArray(files) || files.length <= 0) {
      return [];
    }

    return this.uploader.uploadFiles(
      {
        userId,
        ownerId,
        type: this.type,
        data: {},
        filesDescriptions,
      },
      files
    );
  }

  public async addLinkDocuments(userId: string | ObjectId, links?: LinkDocument[]): Promise<DocumentModel[]> {
    if (!Array.isArray(links) || links.length === 0) {
      return [];
    }

    const data = links.map((link) => ({
      title: link.title,
      description: link.description,
      metadata: { name: link.title },
      path: link.link,
      public: link.public,
      type: DocumentType.Link,
      userId: userId,
    }));

    return Document.create(data);
  }

  public async addEvidenceDescription(existingEvidence?: ExistingEvidence[]) {
    if (isFalsyOrEmpty(existingEvidence)) {
      return;
    }

    const updates: AnyBulkWriteOperation<ExistingEvidence>[] = existingEvidence.map(({ _id, description }) => ({
      updateOne: {
        filter: { _id },
        update: description ? { $set: { description: description } } : { $unset: { description: '' } },
      },
    }));

    return Document.bulkWrite(updates);
  }
}

