/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import TrackerEvidence from "./TrackerEvidence"
import { createEvidenceUploader } from "./EvidenceUploader"

export enum SupportedEvidenceTypes {
  UtrvBundle = 'universal_tracker_value_bundle',
  SurveyBundle = 'survey_bundle',
}

const uploaderMap = new Map<SupportedEvidenceTypes, TrackerEvidence>();
const uploader = createEvidenceUploader();

export const getUploaderType = (type: SupportedEvidenceTypes) => {
  if (uploaderMap.has(type)) {
    return uploaderMap.get(type);
  }

  uploaderMap.set(type, new TrackerEvidence(uploader, type));
  return uploaderMap.get(type);
}
