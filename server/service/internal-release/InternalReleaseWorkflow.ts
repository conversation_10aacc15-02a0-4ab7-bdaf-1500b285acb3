/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { LoggerInterface, wwgLogger } from '../wwgLogger';
import BackgroundJob, {
  BackgroundJobModel,
  CreateJob,
  JobStatus,
  JobType,
  Task,
  TaskStatus,
  TaskType,
} from '../../models/backgroundJob';
import ContextError from '../../error/ContextError';
import { BackgroundBaseWorkflow, TaskResult } from '../background-process/BackgroundBaseWorkflow';
import {
  InternalRelease,
  ReleaseAction,
  ReleaseApprovalStatus,
  ReleaseWorkflowCreate,
  SupportedJobModel,
  SupportedTask,
  TaskInternalReleaseApply,
  TaskInternalReleaseApprovalRequest,
} from './types';
import { createLogEntry } from '../jobs';
import { generatedUUID } from '../crypto/token';
import { createInternalReleaseService } from './InternalReleaseService';
import { UserPlain } from '../../models/user';

export class InternalReleaseWorkflow extends BackgroundBaseWorkflow<SupportedJobModel> {
  constructor(
    protected logger: LoggerInterface,
    protected jobType: JobType.InternalRelease,
  ) {
    super();
  }

  protected async processTask(job: SupportedJobModel, task: SupportedTask): Promise<TaskResult<SupportedJobModel>> {
    const releaseService = createInternalReleaseService(task.data.storage);

    const result: TaskResult<SupportedJobModel> = {
      job,
      executeNextTask: false,
    };

    const taskId = task.id;
    task.status = TaskStatus.Processing;
    job.markModified('tasks');
    await job.save();

    try {
      switch (task.type) {
        case TaskType.InternalReleasePlan:
          wwgLogger.info(`Planning release ${task.data.release.code}`, { jobId: job._id.toString() });
          job.logs.push(createLogEntry(`Planning release ${task.data.release.code}`));
          await releaseService.plan(job, task);
          result.executeNextTask = true;
          break;
        case TaskType.InternalReleaseApprovalRequest: {
          wwgLogger.info(`Requesting approval for release ${task.data.release.code}`, { jobId: job._id.toString() });
          job.logs.push(createLogEntry(`Requesting approval for release ${task.data.release.code}`));
          const approval = await releaseService.approvalRequest(job, task);
          result.executeNextTask = approval.executeNextTask;
          break;
        }
        case TaskType.InternalReleaseApply: {
          wwgLogger.info(`Applying release ${task.data.release.code}`, { jobId: job._id.toString() });
          job.logs.push(createLogEntry(`Applying release ${task.data.release.code}`));
          const apply = await releaseService.apply(job, task);
          result.executeNextTask = apply.executeNextTask;
          job.status = JobStatus.Completed;
          break;
        }
        default: {
          job.logs.push(createLogEntry('Found task that cannot be handled in job'));
          throw new ContextError('Found task that cannot be handled in job', {
            jobId: job._id.toString(),
            taskId: taskId.toString(),
          });
        }
      }
    } catch (error) {
      task.status = TaskStatus.Error;
      job.status = JobStatus.Error;
      job.logs.push(
        createLogEntry(`Failed task ${task.name}. Error: ${error.message}`, { metadata: { cause: error } })
      );
      wwgLogger.error(error);
    }

    job.markModified('tasks');
    await job.save();

    return result;
  }

  public async resetStatus(job: SupportedJobModel) {
    job.status = JobStatus.Pending;

    for (const task of job.tasks) {
      task.status = TaskStatus.Pending;
    }

    job.markModified('tasks');
    return job.save();
  }

  async updateApproval(job: SupportedJobModel, status: ReleaseApprovalStatus, user: UserPlain) {
    const task = job.tasks.find((t) => t.type === TaskType.InternalReleaseApprovalRequest);
    if (!task) {
      throw new ContextError('Missing approval task', {
        jobId: job._id.toString(),
      });
    }

    if (task.status !== TaskStatus.Pending) {
      throw new ContextError('Approval task is not pending', {
        jobId: job._id.toString(),
        taskId: task.id.toString(),
      });
    }

    if ('approval' in task.data) {
      task.data.approval.status = status;
      task.data.approval.userId = user._id.toString();
    }
    job.markModified('tasks');
    return job.save();
  }

  public async createJob(workflow: ReleaseWorkflowCreate, user: UserPlain): Promise<BackgroundJobModel> {
    const key = this.getIdempotencyKey({
      id: workflow.release._id,
    });

    const tasks: Task<InternalRelease>[] = [
      {
        // Task to plan the release
        id: generatedUUID(),
        type: TaskType.InternalReleasePlan,
        name: `Plan release for ${workflow.release.code}`,
        status: TaskStatus.Pending,
        data: {
          userId: user._id.toString(),
          ...workflow,
        },
      },
    ];

    const isPlan = workflow.action === ReleaseAction.Plan;

    if (isPlan) {
      const approvalTask: TaskInternalReleaseApprovalRequest = {
        // Task to request approval for the release
        id: generatedUUID(),
        type: TaskType.InternalReleaseApprovalRequest,
        name: `Approval request for ${workflow.release.code}`,
        status: TaskStatus.Pending,
        data: {
          userId: user._id.toString(),
          ...workflow,
          approval: {
            created: new Date().toISOString(),
            status: ReleaseApprovalStatus.Pending,
          },
        },
      };
      tasks.push(approvalTask);

      // This task will be executed only if the approval is approved
      const applyTask: TaskInternalReleaseApply = {
        // Task to apply the release
        id: generatedUUID(),
        type: TaskType.InternalReleaseApply,
        name: `Apply release for ${workflow.release.code}`,
        status: TaskStatus.Pending,
        data: {
          userId: user._id.toString(),
          ...workflow,
        },
      };
      tasks.push(applyTask);
    }

    const createData: CreateJob = {
      idempotencyKey: key,
      type: this.jobType,
      name: `${isPlan ? 'Plan and Apply' : 'Preview'} internal release ${workflow.release.code}`,
      tasks: tasks,
      logs: [createLogEntry('Starting internal release workflow')],
    };

    return BackgroundJob.create(createData);
  }
}

let instance: InternalReleaseWorkflow;
export const getInternalReleaseWorkflow = () => {
  if (!instance) {
    instance = new InternalReleaseWorkflow(wwgLogger, JobType.InternalRelease);
  }
  return instance;
};
