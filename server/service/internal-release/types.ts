import { HydratedDocument } from 'mongoose';
import { BackgroundJobPlain, JobType, Task, TaskType } from '../../models/backgroundJob';
import { MaterialityMetricPlain } from '../../models/materialityMetric';
import { UniversalTrackerPlain } from '../../models/universalTracker';
import { TableColumn, ValueTable, ValueValidation } from '../../models/public/universalTrackerType';
import { ValueList } from '../../models/public/valueList';
import { InitiativePlain } from '../../models/initiative';

export enum ReleaseCollection {
  ValueList = 'value-list',
  UniversalTrackers = 'universal-trackers',
  MaterialTopics = 'material-topics',
  MaterialityMetrics = 'materiality-metrics',
  UtrExternalMappings = 'utr-external-mappings',
  UniversalTrackerConnections = 'universal-tracker-connections',
  CalculationGroups = 'calculation-groups',
}

export enum ReleaseScope {
  All = 'all',
  ById = 'by-id',
  ByScope = 'by-scope',
}

export enum ReleaseAction {
  Preview = 'preview',
  Plan = 'plan',
}

export enum ReleaseApprovalStatus {
  Pending = 'pending',
  Approved = 'approved',
  Rejected = 'rejected',
}

export type InternalRelease = {
  userId?: string;
  storage?: {
    projectId: string;
    bucket: string;
  },
  release: {
    _id: string;
    code: string;
    path: string;
    scope: ReleaseScopeConfig[];
  };
  action: ReleaseAction;
  returnUrl: string;
};

export type InternalReleaseApproval = InternalRelease & {
  approval: {
    created: string;
    status: ReleaseApprovalStatus;
    userId?: string;
  };
};

export enum InternalReleaseNotificationStatus {
  Planning = 'planning',
  Planned = 'planned',
  ApprovalRquested = 'approvalRequested',
  Applying = 'applying',
  Applied = 'applied',
  Errored = 'errored',
}

export interface RunResultsFile {
  filename: string;
  code: string;
  changes?: number;
  insertions?: number;
  deletions?: number;
}

export interface RunResults {
  scope: string;
  files: RunResultsFile[];
  added: number;
  changed: number;
  unchanged: number;
}

export interface InternalReleaseNotification {
  jobId: string;
  taskId: string;
  status: InternalReleaseNotificationStatus;
  results?: RunResults[];
}

export type TaskInternalReleasePlan = Task<InternalRelease, TaskType.InternalReleasePlan>;
export type TaskInternalReleaseApply = Task<InternalRelease, TaskType.InternalReleaseApply>;
export type TaskInternalReleaseApprovalRequest = Task<InternalReleaseApproval, TaskType.InternalReleaseApprovalRequest>;

export type SupportedTask = TaskInternalReleasePlan | TaskInternalReleaseApprovalRequest | TaskInternalReleaseApply;
export type SupportedJobPlain = BackgroundJobPlain<SupportedTask[]> & { type: JobType.GenerateReport };
export type SupportedJobModel = HydratedDocument<SupportedJobPlain>;

export interface ReleaseScopeConfig {
  collectionName: ReleaseCollection;
  scope: ReleaseScope;
}

export type ReleaseWorkflowCreate = InternalRelease;

export type ImportMaterialityMetric = MaterialityMetricPlain;

type ColumnWithValueListCode = TableColumn & {
  valueListCode?: string;
};

export interface ImportUniversalTracker extends UniversalTrackerPlain {
  valueValidation?: {
    min?: number;
    max?: number;
    valueList: ValueValidation['valueList'] & {
      valueListCode?: string;
    };
    table: ValueTable<ColumnWithValueListCode>;
  };
  ownerCode?: string;
  owner?: Pick<InitiativePlain, '_id' | 'code'>
}

export type ExportUniversalTrackerPlain = ImportUniversalTracker & UniversalTrackerPlain & {
  valueListOptions?: Pick<ValueList, '_id' | 'code'> | null;
  tableColumnValueListOptions?: Pick<ValueList, '_id' | 'code'>[];
}

export enum PostReleaseProcessorType {
  Materiality = 'Materiality',
  // Add other processor types here in the future
}

export interface PostReleaseProcessor {
  canProcess(release: InternalRelease['release']): boolean;
  process(release: InternalRelease['release']): Promise<void>;
}
