/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { LoggerInterface, wwgLogger } from '../wwgLogger';
import {
  ExportUniversalTrackerPlain,
  ImportMaterialityMetric,
  ImportUniversalTracker,
  ReleaseCollection,
} from './types';
import ContextError from '../../error/ContextError';
import MaterialityMetric from '../../models/materialityMetric';
import ValueList, { ValueListModel } from '../../models/valueList';
import UniversalTracker, { UtrType } from '../../models/universalTracker';
import { standards } from '@g17eco/core';
import MaterialTopic, { MaterialTopicPlain } from '../../models/materialTopics';
import Initiative from '../../models/initiative';
import UtrExternalMapping, { UtrExternalMappingPlain } from '../../models/utrExternalMapping';
import {
  ImportUniversalTrackerConnection,
  UniversalTrackerConnection,
  UniversalTrackerConnectionPlain,
} from '../../models/universalTrackerConnection';
import { CalculationGroupPlain, CalculationGroup } from '../../models/calculationGroup';
import { ObjectId } from 'bson';

const projection = { _id: 0, __v: 0, created: 0 };

export class InternalReleaseRepository {
  constructor(protected logger: LoggerInterface = wwgLogger) {}

  async dump(collectionName: ReleaseCollection, codes: string[]) {
    switch (collectionName) {
      case ReleaseCollection.MaterialTopics:
        return this.dumpMaterialTopics(codes);
      case ReleaseCollection.MaterialityMetrics:
        return this.dumpMaterialityMetrics(codes);
      case ReleaseCollection.UniversalTrackers:
        return this.dumpUniversalTrackers(codes);
      case ReleaseCollection.ValueList:
        return this.dumpValueList(codes);
      case ReleaseCollection.UtrExternalMappings:
        return this.dumpUtrExternalMapping(codes);
      case ReleaseCollection.CalculationGroups:
        return this.dumpCalculationGroups(codes);
      case ReleaseCollection.UniversalTrackerConnections:
        return this.dumpUtrConnections(codes);
      default:
        throw new ContextError(`Collection not supported`, {
          collectionName,
        });
    }
  }

  async import(collectionName: ReleaseCollection, data: object) {
    switch (collectionName) {
      case ReleaseCollection.MaterialTopics:
        return this.importMaterialTopics(data as MaterialTopicPlain<string>);
      case ReleaseCollection.MaterialityMetrics:
        return this.imporMaterialityMetrics(data);
      case ReleaseCollection.UniversalTrackers:
        return this.importUniversalTrackers(data);
      case ReleaseCollection.ValueList:
        return this.importValueList(data);
      case ReleaseCollection.UtrExternalMappings:
        return this.importUtrExternalMapping(data);
      case ReleaseCollection.CalculationGroups:
        return this.importCalculationGroups(data);
      case ReleaseCollection.UniversalTrackerConnections:
        return this.importUtrConnections(data);
      default:
        throw new ContextError(`Collection not supported`, {
          collectionName,
        });
    }
  }

  // IMPORT FUNCTIONS

  private async importMaterialTopics(data: Partial<MaterialTopicPlain<string>>) {
    if (!data.code) {
      throw new ContextError('Missing code', {
        name: data.name,
        code: data.code,
      });
    }

    await MaterialTopic.findOneAndUpdate({ code: data.code }, { $set: data }, { upsert: true });
  }

  private async imporMaterialityMetrics(data: Partial<ImportMaterialityMetric>) {
    if (!data.code || !data.utrCode || !data.valueListCode) {
      throw new ContextError('Invalid import object', {
        utrCode: data.utrCode,
        code: data.code,
        valueListCode: data.valueListCode,
      });
    }
    const utr = await UniversalTracker.findOne({ code: data.utrCode });
    if (!utr) {
      throw new ContextError('Invalid UTR code', {
        utrCode: data.utrCode,
        code: data.code,
        valueListCode: data.valueListCode,
      });
    }
    const valueList = await ValueList.findOne({ code: data.valueListCode });
    if (!valueList) {
      throw new ContextError('Invalid Value List code', {
        utrCode: data.utrCode,
        code: data.code,
        valueListCode: data.valueListCode,
      });
    }

    await MaterialityMetric.findOneAndUpdate(
      { code: data.code },
      {
        $set: data,
      },
      { upsert: true }
    );
  }

  private getAllowedUtrTypes(): (UtrType | keyof typeof standards)[] {
    return [
      UtrType.Kpi,
      // UtrType.Calculation,
      UtrType.CustomKpi,
      UtrType.SdgComponent,
      ...Object.keys(standards),
    ];
  }

  private isValidUniversalTracker(data: Partial<ImportUniversalTracker>) {
    const validTypes = this.getAllowedUtrTypes();
    if (!validTypes.includes(String(data.type))) {
      wwgLogger.warn('Trying to import UTR of disallowed utr.type', {
        type: data.type,
        data,
      });
      return false;
    }
    return true;
  }

  private async importUniversalTrackers(data: Partial<ImportUniversalTracker>) {
    if (!data.code) {
      throw new ContextError('Invalid import object', {
        code: data.code,
      });
    }
    const isValid = this.isValidUniversalTracker(data);
    if (!isValid) {
      return;
    }

    const valueListCodes = new Set([
      data.valueValidation?.valueList?.valueListCode,
      ...(data.valueValidation?.table?.columns.map((c) => c.valueListCode) || []),
    ]);

    const valueLists = await ValueList.find({
      code: { $in: Array.from(valueListCodes) },
    });

    if (data.valueValidation?.valueList?.valueListCode) {
      const vl = valueLists.find((v) => v.code === data.valueValidation?.valueList?.valueListCode);
      if (!vl) {
        throw new ContextError('Missing Value List', { code: data.code });
      }
      data.valueValidation.valueList.listId = vl._id;
      delete data.valueValidation.valueList.valueListCode;
    }

    if (data.valueValidation?.table?.columns) {
      data.valueValidation.table.columns.forEach((c) => {
        if (!c.valueListCode) {
          return;
        }
        const vl = valueLists.find((v) => v.code === c.valueListCode);
        if (!vl) {
          throw new ContextError('Missing Value List for table column', {
            code: data.code,
            valueListCode: c.valueListCode,
            column: c.code,
          });
        }
        c.listId = vl._id;
        delete c.valueListCode;
      });
    }

    if (data.ownerCode) {
      const owner = await Initiative.findOne({
        code: data.ownerCode,
      }).lean();
      if (!owner) {
        throw new ContextError('Missing owner', { code: data.code });
      }
      data.ownerId = owner._id;
      delete data.ownerCode;
    }

    return UniversalTracker.findOneAndReplace({ code: data.code }, data, { upsert: true }).exec();
  }

  private async importValueList(data: Partial<ValueListModel>) {
    if (!data.code) {
      throw new ContextError('Invalid import object', { code: data.code });
    }
    await ValueList.findOneAndUpdate({ code: data.code }, { $set: data }, { upsert: true });
  }

  private async importUtrExternalMapping(data: Partial<UtrExternalMappingPlain<string>>) {
    if (!data.code) {
      throw new ContextError('Missing code', {
        type: data.type,
        code: data.code,
      });
    }
    return UtrExternalMapping.findOneAndUpdate({ code: data.code }, { $set: data }, { upsert: true });
  }

  private async importUtrConnections(data: Partial<ImportUniversalTrackerConnection>) {
    if (!data.code) {
      throw new ContextError('Missing code', {
        data,
      });
    }
    const calculationGroupCodes = new Set(data.calculations?.map(({ calculationGroupCode }) => calculationGroupCode));
    const calculationGroups = await CalculationGroup.find(
      { code: { $in: Array.from(calculationGroupCodes) } },
      { _id: 1, code: 1 }
    );
    const codeToIdCalculationGroupMap = new Map(calculationGroups.map(({ _id, code }) => [code, _id]));

    data.calculations?.forEach((calculation) => {
      const existedId = codeToIdCalculationGroupMap.get(calculation.calculationGroupCode);
      if (existedId) {
        calculation.calculationGroupId = existedId;
      } else {
        this.logger.error(
          new ContextError('Not found a calculation group', { groupCode: calculation.calculationGroupCode })
        );
      }
    });
    return UniversalTrackerConnection.findOneAndUpdate({ code: data.code }, { $set: data }, { upsert: true });
  }

  private async importCalculationGroups(data: Partial<CalculationGroupPlain<string>>) {
    if (!data.code) {
      throw new ContextError('Missing code', {
        data,
      });
    }
    return CalculationGroup.findOneAndUpdate({ code: data.code }, { $set: data }, { upsert: true });
  }

  // DUMP FUNCTIONS
  private async dumpMaterialTopics(codes: string[]) {
    return MaterialTopic.find({ code: { $in: codes } }, projection)
      .sort({ code: 1 })
      .lean();
  }

  private async dumpMaterialityMetrics(codes: string[]) {
    return MaterialityMetric.find({ code: { $in: codes } }, projection)
      .sort({ code: 1 })
      .lean();
  }

  private async dumpUtrExternalMapping(codes: string[]) {
    return UtrExternalMapping.find({ code: { $in: codes } }, projection)
      .sort({ code: 1 })
      .lean();
  }

  private async dumpCalculationGroups(codes: string[]) {
    return CalculationGroup.find({ code: { $in: codes } }, projection)
      .sort({ code: 1 })
      .lean();
  }

  private async dumpUtrConnections(codes: string[]): Promise<ImportUniversalTrackerConnection[]> {
    const docs = await UniversalTrackerConnection.find({ code: { $in: codes } }, projection)
      .sort({ code: 1 })
      .lean<UniversalTrackerConnectionPlain[]>();
    const calculationGroupIdsSet = new Set(
      docs.map((doc) => doc.calculations.map(({ calculationGroupId }) => calculationGroupId.toString())).flat()
    );
    const uniqueCalculationGroupIds = Array.from(calculationGroupIdsSet).map((id) => new ObjectId(id));
    const calculationGroups = await CalculationGroup.find(
      { _id: { $in: uniqueCalculationGroupIds } },
      { _id: 1, code: 1 }
    ).lean();
    const idToCodeCalculationGroupMap = new Map(calculationGroups.map(({ _id, code }) => [_id.toString(), code]));
    return docs.map((doc) => {
      const importConnection: ImportUniversalTrackerConnection = {
        ...doc,
        calculations: doc.calculations.map((calculation) => {
          const existedCode = idToCodeCalculationGroupMap.get(calculation.calculationGroupId.toString());
          if (!existedCode) {
            throw new ContextError('Not found calculation group', {
              groupId: calculation.calculationGroupId,
              utrConnectionId: doc._id,
            });
          }
          return { calculationGroupCode: existedCode };
        }),
      };
      return importConnection;
    });
  }

  private async dumpUniversalTrackers(codes: string[]) {
    const match = { code: { $in: codes } };
    const docs = await UniversalTracker.find(match, {
      ...projection,
      'alternatives._id': 0,
    })
      .populate('valueListOptions', { code: 1 })
      .populate('tableColumnValueListOptions', { code: 1 })
      .populate('owner', { code: 1 })
      .sort({ code: 1 })
      .lean<ExportUniversalTrackerPlain[]>();
    return docs.map((doc) => {
      if (doc.valueValidation) {
        if (doc.valueListOptions) {
          doc.valueValidation.valueList.valueListCode = doc.valueListOptions?.code;
          delete doc.valueValidation.valueList.listId;
        }

        if (doc.tableColumnValueListOptions) {
          doc.valueValidation.table?.columns.forEach((c) => {
            if (!c.listId) {
              return;
            }
            const list = doc.tableColumnValueListOptions?.find((l) => l._id.toString() === c.listId?.toString());
            if (!list) {
              wwgLogger.error(`Missing Value List for UTR ${doc.code}`);
              return;
            }
            c.valueListCode = list.code;
            delete c.listId;
          });
        }
      }
      if (doc.owner) {
        doc.ownerCode = doc.owner.code;
        delete doc.ownerId;
      }
      delete doc.owner;
      delete doc.valueListOptions;
      delete doc.tableColumnValueListOptions;
      return doc;
    });
  }

  private async dumpValueList(codes: string[]) {
    return ValueList.find({ code: { $in: codes } }, projection)
      .sort({ code: 1 })
      .lean();
  }
}

export const getInternalReleaseRepository = () => {
  return new InternalReleaseRepository(wwgLogger);
};
