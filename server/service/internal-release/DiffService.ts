/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import fs from 'fs/promises';
import { createReadStream } from 'fs';
import simpleGit from 'simple-git';
import readline from 'readline/promises';
import { LoggerInterface } from '../wwgLogger';
import isEqual from 'lodash/isEqual';
import { RunResults, RunResultsFile } from './types';

enum OnChangeFnAction {
  Added = 'added',
  Unchanged = 'unchanged',
  Changed = 'changed',
}

interface OnChangeFn {
  action: OnChangeFnAction;
  code: string;
  leftJSON: object | null;
  rightJSON: object | null;
}

export class DiffService {
  constructor(private logger: LoggerInterface) {}

  async isSame(filenameA: string, filenameB: string) {
    const git = simpleGit();
    const diffSummary = await git.diffSummary([filenameB, filenameA]);
    const updates = diffSummary.changed + diffSummary.insertions + diffSummary.deletions;
    return updates === 0;
  }

  async getChangedCodes(filenameA: string, filenameB: string) {
    const changedCodes: string[] = [];
    const onChanges = async (change: OnChangeFn) => {
      if ([OnChangeFnAction.Changed, OnChangeFnAction.Added].includes(change.action)) {
        changedCodes.push(change.code);
      }
    };

    await this.fileStepper(filenameA, filenameB, onChanges);
    return changedCodes;
  }

  async compare(
    workingFolder: string,
    inputFilenameA: string,
    inputFilenameB: string
  ): Promise<Omit<RunResults, 'scope'>> {
    await fs.mkdir(workingFolder, { recursive: true });
    return this.process(workingFolder, inputFilenameA, inputFilenameB);
  }

  private async process(
    outputFolder: string,
    filenameA: string,
    filenameB: string
  ): Promise<Omit<RunResults, 'scope'> & { workingFolder: string }> {
    const results: { files: RunResults['files']; added: string[]; changed: string[]; unchanged: string[] } = {
      files: [],
      added: [],
      changed: [],
      unchanged: [],
    };

    const onChanges = async ({ action, code, leftJSON, rightJSON }: OnChangeFn) => {
      switch (action) {
        case OnChangeFnAction.Added: {
          results.added.push(code);
          results.files.push(await this.createDiffFile(outputFolder, code, leftJSON, rightJSON));
          break;
        }
        case OnChangeFnAction.Unchanged: {
          results.unchanged.push(code);
          break;
        }
        case OnChangeFnAction.Changed: {
          results.changed.push(code);
          results.files.push(await this.createDiffFile(outputFolder, code, leftJSON, rightJSON));
          break;
        }
      }
    };

    await this.fileStepper(filenameA, filenameB, onChanges);

    return {
      workingFolder: outputFolder,
      files: results.files,
      added: results.added.length,
      changed: results.changed.length,
      unchanged: results.unchanged.length,
    };
  }

  private async fileStepper(filenameA: string, filenameB: string, onChanges: (change: OnChangeFn) => Promise<void>) {
    const changedCodes: string[] = [];

    const streamA = readline.createInterface({
      input: createReadStream(filenameA),
      crlfDelay: Infinity,
    });
    const streamB = readline.createInterface({
      input: createReadStream(filenameB),
      crlfDelay: Infinity,
    });

    const FileAIterator = streamA[Symbol.asyncIterator]();
    const FileBIterator = streamB[Symbol.asyncIterator]();

    const getNextALine = async () => FileAIterator.next();
    const getNextBLine = async () => FileBIterator.next();

    let lineA = await getNextALine();
    let lineB = await getNextBLine();

    let totalLines = 0;
    const maxLines = 10000; // Prevent infinite loop
    while (!lineA.done || !lineB.done) {
      totalLines++;
      if (totalLines > maxLines) {
        break;
      }

      const lineAValue = lineA.value ? (lineA.value as string) : null;
      const lineBValue = lineB.value ? (lineB.value as string) : null;

      const lineAJSON = lineAValue ? (JSON.parse(lineAValue) as { code?: string }) : null;
      const lineBJSON = lineBValue ? (JSON.parse(lineBValue) as { code?: string }) : null;

      const codeA = lineAJSON?.code;
      const codeB = lineBJSON?.code;

      if (codeA === undefined) {
        // If we reached the end of the remote file, then we are done
        break;
      }

      if (codeB === undefined) {
        // If there is not more lines on local, then we want to write everything as a diff (new items)
        await onChanges({ action: OnChangeFnAction.Added, code: codeA, leftJSON: lineAJSON, rightJSON: null });
        lineA = await getNextALine();
        continue;
      }

      if (codeA < codeB) {
        // Missing line on local, so we want to write everything as a diff (new items)
        await onChanges({ action: OnChangeFnAction.Added, code: codeA, leftJSON: lineAJSON, rightJSON: null });
        lineA = await getNextALine();
        continue;
      }

      if (codeA > codeB) {
        lineB = await getNextBLine();
        continue;
      }

      const isSame = isEqual(lineAJSON, lineBJSON) as boolean;
      if (!isSame) {
        await onChanges({ action: OnChangeFnAction.Changed, code: codeA, leftJSON: lineAJSON, rightJSON: lineBJSON });
      } else {
        await onChanges({ action: OnChangeFnAction.Unchanged, code: codeA, leftJSON: lineAJSON, rightJSON: lineBJSON });
      }

      lineA = await getNextALine();
      lineB = await getNextBLine();
    }

    return changedCodes;
  }

  private recursiveSort(object: any): object {
    if (Array.isArray(object)) {
      return object.map((item) => this.recursiveSort(item));
    }
    if (typeof object !== 'object' || object === null) {
      return object;
    }
    if (object instanceof Date) {
      return object;
    }

    const keys = Object.keys(object as object);
    keys.sort();
    const newObject: any = {};
    for (let i = 0; i < keys.length; i++) {
      newObject[keys[i]] = this.recursiveSort(object[keys[i]]);
    }
    return newObject;
  }

  private createString(json: object): string {
    const reorderedData = this.recursiveSort(json);
    try {
      return JSON.stringify(reorderedData, null, 4) + '\n';
    }
    catch(err) {
      this.logger.error('DiffService.createString: Failed convert json to string', {
        reorderedData,
        cause: err
      });
      return '';
    }
  }

  private async createDiffFile(
    workingFolder: string,
    code: string,
    lineAJSON: null | object,
    lineBJSON: null | object
  ): Promise<RunResultsFile> {
    const safeCode = code.replace(/\//g, '-');

    const filenameA = `${workingFolder}/${safeCode}-a.json`;
    const filenameB = `${workingFolder}/${safeCode}-b.json`;
    const diffFilename = `${safeCode}.diff`;
    const diffFilepath = `${workingFolder}/${safeCode}.diff`;

    const write = [];
    if (lineAJSON === null) {
      // Just touch
      write.push(fs.open(filenameA, 'w'));
    } else {
      write.push(fs.writeFile(filenameA, this.createString(lineAJSON)));
    }
    if (lineBJSON === null) {
      // Just touch
      write.push(fs.open(filenameB, 'w'));
    } else {
      write.push(fs.writeFile(filenameB, this.createString(lineBJSON)));
    }
    const handles = await Promise.all(write);

    const git = simpleGit();
    const diff: string = await git.diff([filenameB, filenameA]);
    const {
      files: [file],
    } = await git.diffSummary([filenameB, filenameA]);
    const fileChanges = file.binary === false ? file : undefined;
    await fs.writeFile(diffFilepath, diff);

    await Promise.all(handles.map((handle) => handle?.close())).catch((err) =>
      this.logger.error(`Failed to delete ${filenameA}:`, err)
    );

    await fs.unlink(filenameA).catch((err) => this.logger.error(`Failed to delete ${filenameA}:`, err));
    await fs.unlink(filenameB).catch((err) => this.logger.error(`Failed to delete ${filenameB}:`, err));

    return {
      filename: diffFilename,
      code,
      changes: fileChanges?.changes,
      insertions: fileChanges?.insertions,
      deletions: fileChanges?.deletions,
    };
  }
}
