/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { LoggerInterface, wwgLogger } from '../wwgLogger';
import GoogleStorage from '../storage/googleStorage';
import {
  InternalRelease,
  InternalReleaseNotification,
  InternalReleaseNotificationStatus,
  ReleaseApprovalStatus,
  ReleaseCollection,
  ReleaseScopeConfig,
  RunResults,
  SupportedJobModel,
  SupportedTask,
  TaskInternalReleaseApply,
  TaskInternalReleaseApprovalRequest,
  TaskInternalReleasePlan,
} from './types';
import { PostReleaseProcessor } from './types';
import fs from 'fs/promises';
import { createReadStream } from 'fs';
import JSZip from 'jszip';
import { InternalReleaseRepository, getInternalReleaseRepository } from './InternalReleaseRepository';
import ContextError from '../../error/ContextError';
import readline from 'readline/promises';
import { TaskResult } from '../background-process/BackgroundBaseWorkflow';
import axios from 'axios';
import { JobStatus, TaskStatus } from '../../models/backgroundJob';
import { DiffService } from './DiffService';
import { LEVEL } from '../event/Events';
import { createLog } from '../jobs';
import { getGoogleCloudReleasesCredentials } from '../google-cloud/credentials';
import config from '../../config';
import { getMaterialityPostReleaseProcessor } from './MaterialityPostReleaseProcessor';

export class InternalReleaseService {
  constructor(
    private logger: LoggerInterface,
    private googleStorage: GoogleStorage,
    private repository: InternalReleaseRepository,
    private diffService: DiffService,
    private postReleaseProcessors: PostReleaseProcessor[]
  ) {}
  private workspaceDir: string = '';
  private outputDir: string = '';
  private remoteDir: string = '';

  private async hasStateChanged(job: SupportedJobModel, task: TaskInternalReleaseApply) {
    const { release } = task.data;
    for (const scope of release.scope) {
      const localFilename = this.getLocalFilename(scope.collectionName);
      const localAltFilename = this.getLocalAltFilename(scope.collectionName);
      await this.extractLocalState(scope, localAltFilename);
      const isSame = await this.diffService.isSame(localAltFilename, localFilename);
      if (!isSame) {
        job.status = JobStatus.Error; // Fail both task and job as this is not recoverable
        task.status = TaskStatus.Error;
        job.logs.push(
          createLog(`Local state for ${scope.collectionName} has changed. Must do another plan.`, {
            severity: LEVEL.ERROR,
          })
        );
        return true;
      }
    }
    return false;
  }

  private async applyScope(job: SupportedJobModel, scope: ReleaseScopeConfig) {
    job.logs.push(createLog(`Applying ${scope.collectionName} (${scope.scope})`));
    const localFilename = this.getLocalFilename(scope.collectionName);
    const remoteFilename = this.getRemoteFilename(scope.collectionName);
    const changedCodes = await this.diffService.getChangedCodes(remoteFilename, localFilename);

    const readInterface = readline.createInterface({
      input: createReadStream(remoteFilename),
      output: process.stdout,
      terminal: false,
    });

    let count = 0;
    for await (const line of readInterface) {
      const item = JSON.parse(line) as { code?: string };
      if (item.code && changedCodes.includes(item.code)) {
        count += 1;
        await this.repository.import(scope.collectionName, item);
        const pcComplete = count/changedCodes.length * 100;
        if (pcComplete % 5 === 0) {
          console.debug(`Imported ${pcComplete}%`);
        }
      }
    }
    job.logs.push(createLog(`Applied ${count} on ${scope.collectionName} (${scope.scope})`));
    console.debug(`Import Complete.`);
  }

  async apply(job: SupportedJobModel, task: TaskInternalReleaseApply): Promise<TaskResult<SupportedJobModel>> {
    const { release } = task.data;
    await this.notifyOwner(job, task, InternalReleaseNotificationStatus.Applying);

    try {
      await this.prepareWorkspace(job, release);
      await this.downloadAndExtractFiles(`${job._id.toString()}.zip`, false);

      const hasStateChanged = await this.hasStateChanged(job, task);
      if (hasStateChanged) {
        job.logs.push(createLog('Local states do not match. Aborting.'));
        await job.save();
        throw new ContextError(`Aborting release ${task.data.release.code} as states dont match.`,
          {
            jobId: job._id.toString(),
            taskId: task.id.toString(),
          }
        );
      }

      job.logs.push(createLog('Local states match. Will try to apply.'));
      await job.save();

      this.logger.info(`States matched. Applying release ${task.data.release.code} as states match.`, {
        jobId: job._id.toString(),
        taskId: task.id.toString(),
        scope: release.scope,
      });

      // Apply in order
      const orderedScopes = [
        ReleaseCollection.ValueList,
        ReleaseCollection.UniversalTrackers,
        ReleaseCollection.MaterialTopics,
        ReleaseCollection.MaterialityMetrics,
        ReleaseCollection.UtrExternalMappings,
        ReleaseCollection.CalculationGroups,
        ReleaseCollection.UniversalTrackerConnections,
      ];
      for (const orderedScope of orderedScopes) {
        const scopeConfig = release.scope.find((s) => s.collectionName === orderedScope);
        if (scopeConfig) {
          await this.applyScope(job, scopeConfig);
        }
      }

      // Run all registered post-release processors
      this.logger.info(`Starting post-release processing for release ${release.code}.`);
      for (const processor of this.postReleaseProcessors) {
        if (processor.canProcess(release)) {
          try {
            await processor.process(release);
          } catch (error) {
            // Log error from processor but continue with other processors
            this.logger.error(
              new ContextError(`Post-release processor failed during apply step`, {
                processorName: processor.constructor.name,
                releaseCode: release.code,
                jobId: job._id.toString(),
                taskId: task.id.toString(),
                cause: error,
              })
            );
          }
        }
      }
      this.logger.info(`Finished post-release processing for release ${release.code}.`);

      task.status = TaskStatus.Completed;

      await this.notifyOwner(job, task, InternalReleaseNotificationStatus.Applied);
      await this.cleanUp(release);

      return {
        job,
        executeNextTask: false,
      };
    } catch (error) {
      this.logger.error(new ContextError(`Failed to apply release ${task.data.release.code}.`, {
        jobId: job._id.toString(),
        taskId: task.id.toString(),
        cause: error,
      }));
      await this.notifyOwner(job, task, InternalReleaseNotificationStatus.Errored);
      await this.cleanUp(release);
      throw error;
    }
  }

  async plan(job: SupportedJobModel, task: TaskInternalReleasePlan) {
    const { release } = task.data;
    await this.notifyOwner(job, task, InternalReleaseNotificationStatus.Planning);

    try {
      await this.prepareWorkspace(job, release);
      await this.downloadAndExtractFiles(release.path, true);

      const scopeResults: RunResults[] = [];

      for (const scope of release.scope) {
        const localFilename = this.getLocalFilename(scope.collectionName);
        await this.extractLocalState(scope, localFilename);
        const results = await this.createDiffs(job, scope);
        scopeResults.push({
          scope: scope.collectionName,
          ...results,
        });
      }
      this.logger.info(`Finished planning ${task.data.release.code}. Preparing to upload ZIP.`, {
        jobId: job._id.toString(),
        taskId: task.id.toString(),
      });
      await this.uploadZipped(job);
      this.logger.info(`Zip uploaded for ${task.data.release.code}.`, {
        jobId: job._id.toString(),
        taskId: task.id.toString(),
      });

      task.status = TaskStatus.Completed;

      await this.notifyOwner(job, task, InternalReleaseNotificationStatus.Planned, scopeResults);
      this.logger.info(`Origin API notified for ${task.data.release.code}.`, {
        jobId: job._id.toString(),
        taskId: task.id.toString(),
      });
      await this.cleanUp(release);

      return {
        success: true,
      };
    } catch (error) {
      this.logger.error(new ContextError(`Failed to plan release ${task.data.release.code}.`, {
        jobId: job._id.toString(),
        taskId: task.id.toString(),
        cause: error,
      }));
      await this.notifyOwner(job, task, InternalReleaseNotificationStatus.Errored);
      await this.cleanUp(release);
      throw error;
    }
  }

  private async getFiles(dir: string, accFiles: string[]) {
    const dirFiles = await fs.readdir(dir);
    for (const file of dirFiles) {
      const filePath = `${dir}/${file}`;
      const stat = await fs.stat(filePath);
      if (stat.isDirectory()) {
        await this.getFiles(filePath, accFiles);
      } else {
        accFiles.push(filePath);
      }
    }

    return accFiles;
  }

  private async uploadZipped(job: SupportedJobModel) {
    const zip = new JSZip();
    const files = await this.getFiles(this.outputDir, []);
    for (const file of files) {
      const filename = file.replace(`${this.outputDir}/`, '');
      zip.file(filename, await fs.readFile(file));
    }

    const contents = await zip.generateAsync({ type: 'nodebuffer' });
    const path = `${this.remoteDir}/${job._id.toString()}.zip`;
    return this.googleStorage.streamBufferUpload({ path, contents });
  }

  private async createDiffs(job: SupportedJobModel, scope: ReleaseScopeConfig): Promise<Omit<RunResults, 'scope'>> {
    const localFilename = this.getLocalFilename(scope.collectionName);
    const remoteFilename = this.getRemoteFilename(scope.collectionName);
    const summary = await this.diffService.compare(
      `${this.outputDir}/${scope.collectionName}`,
      remoteFilename,
      localFilename
    );
    const metaFilename = `${this.outputDir}/${scope.collectionName}-meta.json`;
    try {
      const jsonString = JSON.stringify(summary, null, 4);
      await fs.writeFile(metaFilename, jsonString);
    }
    catch(err) {
      this.logger.error('Failed to write meta file', {
        filename: metaFilename,
        summary,
        cause: err
      });
    }
    return summary;
  }

  private async notifyOwner(
    job: SupportedJobModel,
    task: SupportedTask,
    status: InternalReleaseNotificationStatus,
    results?: RunResults[]
  ) {
    if (!task.data.returnUrl) {
      throw new ContextError('Missing return url', {
        jobId: job._id.toString(),
        taskId: task.id.toString(),
      });
    }

    const notification: InternalReleaseNotification = {
      jobId: job._id.toString(),
      taskId: task.id.toString(),
      status: status,
      results,
    };

    try {
      await axios.post(task.data.returnUrl, notification);
    } catch (err) {
      this.logger.error(new ContextError(`Failed to notify owner of status ${status}, but continuing anyway...`, {
        jobId: job._id.toString(),
        taskId: task.id.toString(),
        notificationStatus: status,
        debugMessage: err.message
      }));
    }
  }

  private async cleanUp(release: InternalRelease['release']) {
    // Rebuild here just in case to make sure we are not deleting anything unexpected
    const workspaceDir = `/tmp/releases/${release.code}`;
    return fs.rm(workspaceDir, { recursive: true, force: true });
  }

  async approvalRequest(
    job: SupportedJobModel,
    task: TaskInternalReleaseApprovalRequest
  ): Promise<TaskResult<SupportedJobModel>> {
    switch (task.data.approval?.status) {
      case ReleaseApprovalStatus.Approved:
        task.status = TaskStatus.Completed;
        return {
          job,
          executeNextTask: true,
        };
      case ReleaseApprovalStatus.Rejected:
        task.status = TaskStatus.Completed;
        return {
          job,
          executeNextTask: false,
        };
      case ReleaseApprovalStatus.Pending:
        await this.notifyOwner(job, task, InternalReleaseNotificationStatus.ApprovalRquested);
        task.status = TaskStatus.Pending;
        return {
          job,
          executeNextTask: false,
        };
      default:
        return {
          job,
          executeNextTask: false,
        };
    }
  }

  private async getCodes(scope: ReleaseScopeConfig): Promise<string[]> {
    const codes: string[] = [];
    const rl = readline.createInterface({
      input: createReadStream(this.getRemoteFilename(scope.collectionName)),
      output: process.stdout,
      terminal: false,
    });

    for await (const line of rl) {
      const item = JSON.parse(line) as { code?: string };
      if (item.code) {
        codes.push(item.code);
      }
    }
    return codes;
  }

  private getLocalFilename(collectionName: ReleaseCollection | string, inclPath = true) {
    const filename = `${collectionName}-local.json`;
    return inclPath ? `${this.outputDir}/${filename}` : filename;
  }

  private getLocalAltFilename(collectionName: ReleaseCollection | string, inclPath = true) {
    const filename = `${collectionName}-local-alt.json`;
    return inclPath ? `${this.outputDir}/${filename}` : filename;
  }

  private getRemoteFilename(collectionName: ReleaseCollection | string, inclPath = true) {
    const filename = `${collectionName}-remote.json`;
    return inclPath ? `${this.outputDir}/${filename}` : filename;
  }

  private async extractLocalState(scope: ReleaseScopeConfig, filename: string) {
    const codes = await this.getCodes(scope);
    const items = await this.repository.dump(scope.collectionName, codes);

    // Touch file, in case no results from dump will result in empty file
    const handler = await fs.open(filename, 'w');
    for (const item of items) {
      try {
      await fs.appendFile(filename, `${JSON.stringify(item)}\n`);
      } catch(err) {
        this.logger.error('InternalReleaseService.extractLocalState: Failed convert json to string', {
          item,
          cause: err
        });
      }
    }
    return handler.close();
  }

  private async prepareWorkspace(job: SupportedJobModel, release: InternalRelease['release']) {
    this.remoteDir = `${release.code}`;
    this.workspaceDir = `/tmp/releases/${this.remoteDir}`;
    this.outputDir = `${this.workspaceDir}/${job._id.toString()}`;
    try {
      await fs.mkdir(this.outputDir, { recursive: true });
    } catch (error) {
      throw new ContextError('Failed to prepare workspace', {
        cause: error,
      });
    }
  }

  private async downloadAndExtractFiles(filename: string, renameFiles = true) {
    try {
      this.logger.info(`Downloading file ${filename}.`);
      await this.googleStorage.downloadFile(`${this.remoteDir}/${filename}`, `${this.workspaceDir}/${filename}`);
      this.logger.info(`File ${filename} downloaded from cloud.`);
      await this.extractFiles(filename, renameFiles);
      this.logger.info(`File ${filename} extracted.`);
    } catch (error) {
      throw new ContextError(`Failed to download release file: ${error.message ?? ''}`, {
        remoteFilepath: `${this.remoteDir}/${filename}`,
        cause: error,
      });
    }
  }

  private async extractFiles(zipFilepath: string, renameFiles = true) {
    const data = await fs.readFile(`${this.workspaceDir}/${zipFilepath}`);
    const zip = new JSZip();
    const contents = await zip.loadAsync(data);
    for (const contentsFilename of Object.keys(contents.files)) {
      const file = zip.file(contentsFilename);
      if (!file) {
        continue;
      }

      const [filename, extension] = contentsFilename.split('.');
      if (extension !== 'json') {
        continue;
      }
      const newFilename = renameFiles ? this.getRemoteFilename(filename) : `${this.outputDir}/${contentsFilename}`;
      await file.async('nodebuffer').then((content) => {
        return fs.writeFile(newFilename, content);
      });
    }
  }
}

export const createInternalReleaseService = (storage?: InternalRelease['storage']) => {
  return new InternalReleaseService(
    wwgLogger,
    GoogleStorage.create(storage?.bucket ?? config.g17ecoReleases.storage.bucket, getGoogleCloudReleasesCredentials(storage?.projectId)),
    getInternalReleaseRepository(),
    new DiffService(wwgLogger),
    [getMaterialityPostReleaseProcessor()]
  );
};
