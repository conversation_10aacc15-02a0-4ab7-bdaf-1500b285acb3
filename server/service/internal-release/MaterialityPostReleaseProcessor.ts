
import MaterialityMetric, {
  MaterialityAssessmentType,
  MaterialityMetricWithUtrPlain,
} from '../../models/materialityMetric';
import MaterialTopic, { MaterialTopicPlainWithId } from '../../models/materialTopics';
import { AssessmentInputs, MaterialityAssessmentScope, MetricConfiguration } from '../materiality-assessment/types';
import { MaterialityLookupCalculator } from '../materiality-assessment/assessments/MaterialityLookupCalculator';
import { MaterialitySizeMultipliers } from '../materiality-assessment/assessments/MaterialityLookupCalculator';
import { roundTo } from '../../util/number';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import { InternalRelease, ReleaseCollection } from './types';
import { PostReleaseProcessor } from './types';
import { FINANCIAL_ASSESSMENT_WEIGHTS, IMPACT_ASSESSMENT_WEIGHTS } from '../materiality-assessment/constants';
import ContextError from '../../error/ContextError';

export class MaterialityPostReleaseProcessor implements PostReleaseProcessor {
  constructor(private logger: LoggerInterface) {}

  public canProcess(release: InternalRelease['release']) {
    const isIncluded = release.scope.some((scopeItem) =>
      [ReleaseCollection.MaterialTopics, ReleaseCollection.MaterialityMetrics].includes(scopeItem.collectionName)
    );
    if (!isIncluded) {
      this.logger.info(
        'Skipping post-process as MaterialTopics or MaterialityMetrics are not included for this release.',
        { releaseCode: release.code }
      );
    }
    return isIncluded;
  }

  public async process(release: InternalRelease['release']) {
    this.logger.info('Starting post-process for materiality.', {
      releaseCode: release.code,
    });

    const { metrics, filteredTopicsByMetrics } = await this.getFilteredMaterialityData();
    if (filteredTopicsByMetrics.length === 0) {
      this.logger.info('No topics found that match metrics. Skipping further processing.');
      return;
    }

    const availableScopes = Object.values(MaterialityAssessmentScope);
    const metricsByTypeMap = this.initializeMetricsByTypeMap(metrics, availableScopes);

    for (const topic of filteredTopicsByMetrics) {
      try {
        await this.calculateScoresAndUpdateTopic(topic, metricsByTypeMap);
      } catch (error) {
        this.logger.error(
          new ContextError('Failed to process topic', {
            topicCode: topic.code,
            releaseCode: release.code,
            cause: error,
          })
        );
        // Decide if we want to continue with other topics or re-throw to stop all post-processing
        // For now, it logs the error and continues with the next topic.
      }
    }
    this.logger.info('Finished post-process for materiality.');
  }

  private async getFilteredMaterialityData(): Promise<{
    metrics: MaterialityMetricWithUtrPlain[];
    filteredTopicsByMetrics: MaterialTopicPlainWithId[];
  }> {
    const metrics = await MaterialityMetric.find()
      .populate('utr')
      .lean<MaterialityMetricWithUtrPlain[]>()
      .exec();
    const topics = await MaterialTopic.find().lean<MaterialTopicPlainWithId[]>().exec();
    const filteredTopicsByMetrics = topics.filter((topic) => {
      return metrics.some((metric) =>
        metric.options.some((option) => option.scores.some((score) => score.materialityCode === topic.code))
      );
    });
    return { metrics, filteredTopicsByMetrics };
  }

  private initializeMetricsByTypeMap(
    metrics: MaterialityMetricWithUtrPlain[],
    availableScopes: MaterialityAssessmentScope[]
  ): Map<MaterialityAssessmentType, MetricConfiguration> {
    const metricsByTypeMap = new Map<MaterialityAssessmentType, MetricConfiguration>();

    metricsByTypeMap.set(
      MaterialityAssessmentType.Financial,
      this.configureMetricsForType(
        MaterialityAssessmentType.Financial,
        metrics,
        availableScopes,
        FINANCIAL_ASSESSMENT_WEIGHTS
      )
    );

    metricsByTypeMap.set(
      MaterialityAssessmentType.Impact,
      this.configureMetricsForType(
        MaterialityAssessmentType.Impact,
        metrics,
        availableScopes,
        IMPACT_ASSESSMENT_WEIGHTS
      )
    );

    return metricsByTypeMap;
  }

  private configureMetricsForType(
    assessmentType: MaterialityAssessmentType,
    allMetrics: MaterialityMetricWithUtrPlain[],
    availableScopes: MaterialityAssessmentScope[],
    weights: MaterialitySizeMultipliers
  ): MetricConfiguration {
    const typeSpecificMetrics = allMetrics.filter((m) => m.type === assessmentType);
    const scopeMetrics = availableScopes.reduce((acc, scope) => {
      acc.set(
        scope,
        typeSpecificMetrics.filter((m) => m.tags?.includes(scope))
      );
      return acc;
    }, new Map<MaterialityAssessmentScope, MaterialityMetricWithUtrPlain[]>());

    return {
      scopeMetrics,
      weights,
    };
  }

  private buildAssessmentInputs(
    topicCode: string,
    metricsInScope: MaterialityMetricWithUtrPlain[]
  ): AssessmentInputs {
    const assessmentInputs: AssessmentInputs = new Map();
    metricsInScope.forEach((metric) => {
      const optionScores = metric.options.reduce((acc, option) => {
        const scores = option.scores.filter((score) => score.materialityCode === topicCode);
        if (scores.length === 0) {
          return acc;
        }
        if (scores.length > 1) {
          this.logger.warn('Multiple scores found for a single option, using the first one.', {
            topicCode,
            metricUtrCode: metric.utrCode,
            optionCode: option.optionCode,
          });
        }
        acc.push({ optionCode: option.optionCode, score: scores[0].score });
        return acc;
      }, [] as { optionCode: string; score: number }[]);

      const answersPerMetric = metric.utr?.valueValidation?.max ?? 1;
      const sortedScores = optionScores.sort((a, b) => b.score - a.score).slice(0, answersPerMetric);
      if (sortedScores.length > 0) {
        assessmentInputs.set(
          metric.utrCode,
          sortedScores.map((scoreOutput) => scoreOutput.optionCode)
        );
      }
    });
    return assessmentInputs;
  }

  private async calculateScoresAndUpdateTopic(
    topic: MaterialTopicPlainWithId,
    metricsByTypeMap: Map<MaterialityAssessmentType, MetricConfiguration>
  ) {
    const topicCode = topic.code;
    const scopeScores = [];
    for (const [assessmentType, { scopeMetrics, weights }] of metricsByTypeMap.entries()) {
      for (const [scope, metricsInScope] of scopeMetrics.entries()) {
        const assessmentInputs = this.buildAssessmentInputs(topicCode, metricsInScope);
        const calculator = new MaterialityLookupCalculator(assessmentInputs, metricsInScope, weights);
        const result = await calculator.getResult();
        const maxScore = roundTo(result.scores[topicCode] ?? 0);
        if (maxScore === 0 && assessmentInputs.size > 0) {
          this.logger.error(
            new ContextError('Failed to calculate max score for topic in scope', {
              topicCode,
              scope,
              assessmentInputs: Object.fromEntries(assessmentInputs.entries()),
            })
          );
        }
        scopeScores.push({ scope, type: assessmentType, maxScore, referenceCount: assessmentInputs.size });
      }
    }
    await MaterialTopic.findByIdAndUpdate(topic._id, { scopeScores });
    this.logger.info('Calculated scope scores for topic', {
      topicCode,
      scopeScores,
    });
  }
}

let instance: MaterialityPostReleaseProcessor;

export const getMaterialityPostReleaseProcessor = (): MaterialityPostReleaseProcessor => {
  if (!instance) {
    instance = new MaterialityPostReleaseProcessor(wwgLogger);
  }
  return instance;
};
