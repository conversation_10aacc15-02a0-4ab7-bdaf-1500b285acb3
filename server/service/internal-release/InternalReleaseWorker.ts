
/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { BackgroundBaseWorker } from '../background-process/BackgroundBaseWorker';
import { wwgLogger } from '../wwgLogger';
import { InternalReleaseWorkflow, getInternalReleaseWorkflow } from './InternalReleaseWorkflow';
import { JobResult } from '../background-process/types';
import { SupportedJobModel } from './types';

/**
 * Workers are always triggered in Google Cloud Job runs environments
 * allow as to do heavy work without affecting normal API server operations.
 */
export class InternalReleaseWorker extends BackgroundBaseWorker<SupportedJobModel, InternalReleaseWorkflow> {
  public async process(jobId: string, options: { retry: boolean }): Promise<JobResult> {
    const result = await super.process(jobId, options);

    if (!result._id) {
      return result;
    }

    // if (result.status !== JobStatus.Completed) {
      // const job = await this.workflow.findById(result._id);
      // await this.workflow.resetStatus(job as SupportedJobModel);
    // }
    return result;
  }
}

let instance: InternalReleaseWorker;
export const getInternalReleaseWorker = () => {
  if (!instance) {
    instance = new InternalReleaseWorker(wwgLogger, getInternalReleaseWorkflow());
  }
  return instance;
};
