/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import { UniversalTrackerPlain } from '../../../models/universalTracker';
import {
  type UniversalTrackerValueModel,
  type UniversalTrackerValueAggregated,
  type UniversalTrackerValuePlain,
} from '../../../models/universalTrackerValue';
import { wwgLogger } from '../../wwgLogger';
import UserError from '../../../error/UserError';
import UniversalTrackerActionManager from '../UniversalTrackerActionManager';
import { getSurveyAggregator } from '../../../service/survey/SurveyAggregator';
import { SurveyRepository } from '../../../repository/SurveyRepository';
import { SourceItemType } from '../../../models/survey';
import type { Blueprint } from '../../../repository/BlueprintRepository';
import { UtrvFilter } from '../../../types/universalTrackerValue';
import { AggregationErrorMessages } from '../../../error/ErrorMessages';

export type PreviewUtrv = Pick<
  UniversalTrackerValuePlain,
  'effectiveDate' | 'valueData' | 'value' | 'status' | 'unit' | 'numberScale' | 'note' | 'valueAggregation' | 'sourceItems'
>;

interface RefreshResult {
  current: PreviewUtrv;
  next: PreviewUtrv;
}

export class AggregationPreviewManager {
  constructor(
    private readonly logger: typeof wwgLogger,
    private readonly surveyAggregator: ReturnType<typeof getSurveyAggregator>,
  ) {}

  public async previewAggregation(params: {
    utr: UniversalTrackerPlain,
    originalUtrv: UniversalTrackerValueModel,
  }): Promise<RefreshResult> {
    const aggregatedUtr = await this.createAggregatedUtr(params.utr, params.originalUtrv);
    const nextValue = this.createNextValue(aggregatedUtr, params.originalUtrv);
    return {
      current: this.getCurrentValue(params.originalUtrv),
      next: nextValue,
    };
  }

  public async updateAggregation(params: {
    utr: UniversalTrackerPlain,
    originalUtrv: UniversalTrackerValueModel,
    userId: ObjectId,
  }): Promise<RefreshResult> {
    const aggregatedUtr = await this.createAggregatedUtr(params.utr, params.originalUtrv);
    const nextValue = this.createNextValue(aggregatedUtr, params.originalUtrv);
    params.originalUtrv.valueAggregation = aggregatedUtr.valueAggregation;
    // Update same way createUtrv does in SurveyAggregateProcess
    params.originalUtrv.valueType = aggregatedUtr.valueType;
    params.originalUtrv.sourceItems = aggregatedUtr.sourceItems;

    UniversalTrackerActionManager.hydrateUpdate({
      utrv: params.originalUtrv,
      userId: params.userId,
      value: nextValue.value,
      note: nextValue.note,
      valueData: nextValue.valueData,
    });
    UniversalTrackerActionManager.hydrateVerify(params.originalUtrv, params.userId);
    await params.originalUtrv.save();
    return {
      current: this.getCurrentValue(params.originalUtrv),
      next: nextValue,
    };
  }

  private async createAggregatedUtr(utr: UniversalTrackerPlain, originalUtrv: UniversalTrackerValueModel): Promise<UniversalTrackerValueAggregated> {
    const utrvId = originalUtrv._id.toString() as string;
    const surveyId = originalUtrv.compositeData?.surveyId;
    if (!surveyId) {
      throw new UserError(AggregationErrorMessages.NoDisaggregatedData, {
        utrvId: utrvId,
        compositeData: originalUtrv.compositeData,
      });
    }
    const survey = await SurveyRepository.findByIdWithInitiative(surveyId, { sourceItems: 1, initiativeId: 1, initiative: 1, filters: 1 });
    if (!survey?.initiative) {
      throw new UserError(AggregationErrorMessages.NoDisaggregatedData, {
        utrvId,
        surveyId,
        debugMessage: 'Survey not found',
      });
    }
    const surveyIds = survey.sourceItems?.reduce<ObjectId[]>((acc, item) => {
      if (item.type === SourceItemType.Survey) {
        acc.push(item.sourceId);
      }
      return acc;
    }, [] as ObjectId[]);
    if (!surveyIds?.length) {
      throw new UserError(AggregationErrorMessages.NoDisaggregatedData, {
        utrvId,
        debugMessage: 'No survey source items found',
      });
    }
    const surveys = await this.surveyAggregator.getSurveysForAggregation(surveyIds);
    if (surveys.length === 0) {
      throw new UserError(AggregationErrorMessages.NoDisaggregatedData, {
        utrvId,
        debugMessage: 'No surveys found',
      });
    }
    const blueprint: Pick<Blueprint, 'forms'> = {
      forms: [{
        utrGroupConfig: { groupName: utr.code, utrCodes: [utr.code] },
      }],
    };
    this.logger.info('createAggregatedUtr', {
      utrId: utr._id.toString(),
      utrCode: utr.code,
      surveyId: surveyId.toString(),
      initiativeId: survey.initiative._id.toString(),
      surveys: surveys.map((survey) => ({
        _id: survey._id.toString(),
        initiativeId: survey.initiativeId.toString(),
      })),
    });
    const utrvStatuses = this.surveyAggregator.convertToUtrvStatuses(survey.filters?.utrv ?? UtrvFilter.Verified);
    const cloneData = await this.surveyAggregator.getAggregatedData({
      expandedBlueprint: blueprint,
      surveysToAggregate: surveys,
      initiative: survey.initiative,
      utrvStatuses,
    });
    const [aggregatedUtrv] = cloneData;
    if (!aggregatedUtrv || cloneData.length > 1) {
      throw new UserError(AggregationErrorMessages.NoDisaggregatedData, {
        utrvId,
        aggregatedUtrvCount: cloneData.length,
      });
    }
    return aggregatedUtrv;
  }

  private createNextValue(aggregatedUtr: UniversalTrackerValueAggregated, originalUtrv: UniversalTrackerValueModel): PreviewUtrv {
    return {
      effectiveDate: aggregatedUtr.effectiveDate,
      valueData: aggregatedUtr.valueData,
      value: aggregatedUtr.value,
      note: aggregatedUtr.note,
      status: originalUtrv.status,
      unit: originalUtrv.unit,
      numberScale: originalUtrv.numberScale,
      valueAggregation: aggregatedUtr.valueAggregation,
      sourceItems: aggregatedUtr.sourceItems,
    };
  }

  private getCurrentValue(originalUtrv: UniversalTrackerValueModel): PreviewUtrv {
    return {
      effectiveDate: originalUtrv.effectiveDate,
      status: originalUtrv.status,
      valueData: originalUtrv.valueData,
      value: originalUtrv.value,
      note: originalUtrv.note,
      unit: originalUtrv.unit,
      numberScale: originalUtrv.numberScale,
      valueAggregation: originalUtrv.valueAggregation,
      sourceItems: originalUtrv.sourceItems,
    };
  }
}

let instance: AggregationPreviewManager;
export const getAggregationPreviewManager = () => {
  if (!instance) {
    instance = new AggregationPreviewManager(wwgLogger, getSurveyAggregator());
  }
  return instance;
};
