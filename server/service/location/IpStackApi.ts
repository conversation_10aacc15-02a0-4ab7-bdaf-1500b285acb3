/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import axios from 'axios';
import config from '../../config';
import { wwgLogger } from '../wwgLogger';
import { IpLocation } from '../../models/universalTrackerValue';
import ContextError from "../../error/ContextError";

export interface Language {
  code: string;
  name: string;
  native: string;
}

export interface Location {
  geoname_id?: any;
  capital: string;
  languages: Language[];
  country_flag: string;
  country_flag_emoji: string;
  country_flag_emoji_unicode: string;
  calling_code: string;
  is_eu: boolean;
}

export interface TimeZone {
  id: string;
  current_time: Date;
  gmt_offset: number;
  code: string;
  is_daylight_saving: boolean;
}

export interface Currency {
  code: string;
  name: string;
  plural: string;
  symbol: string;
  symbol_native: string;
}

export interface Connection {
  asn: number;
  isp: string;
}

interface IpStackErrorResponse {
  success: boolean;
  error: {
    code: number,
    type: string,
    info: string,
  };
}

export interface IpStackSuccessResponse {
  ip: string;
  type: string;
  continent_code: string;
  continent_name: string;
  country_code: string;
  country_name: string;
  region_code: string;
  region_name: string;
  city: string;
  zip: string;
  latitude: number;
  longitude: number;
  location: Location;

  // For paid response
  time_zone: TimeZone;
  currency: Currency;
  connection: Connection;
}


const apiHostname = 'http://api.ipstack.com';
type IpStackResponse = IpStackSuccessResponse | IpStackErrorResponse;
const isErrorResponse = (resp: IpStackResponse): resp is IpStackErrorResponse => {
  return 'success' in resp && !resp.success;
}

export interface ExtendedIpLocation extends IpLocation {
  latitude: number;
  longitude: number;
  city: string; // Bexley
  state: string; // England
  country: string; // GB
  postalCode: string | null; // SE18
  continentCode: string; // 'AF' | 'AS' | 'EU' | 'NA' | 'OC' | 'SA' | 'AN'
}

export type IpStackResult = IpLocation | ExtendedIpLocation;

export const getInfo = (ip: string): Promise<IpStackResult> => axios.get<IpStackResponse>(
  `${apiHostname}/${ip}`, {
    params: {
      access_key: config.service.ipStack.accessKey
    }
  }
).then(({ data }): IpLocation | ExtendedIpLocation => {

  if (isErrorResponse(data)) {
    wwgLogger.error(new ContextError('Failed to get IP Stack response', { error: data.error }));
    return { ip };
  }

  return ({
    ip: data.ip,
    latitude: data.latitude,
    longitude: data.longitude,
    city: data.city,
    state: data.region_name,
    country: data.country_code,
    postalCode: data.zip,
    continentCode: data.continent_code,
  }) as ExtendedIpLocation;
}).catch(e => {
  wwgLogger.error(e);
  return { ip };
});

