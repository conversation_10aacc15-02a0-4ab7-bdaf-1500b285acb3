import { IpStackResult, getInfo, ExtendedIpLocation } from './IpStackApi';
import config from '../../config';
import { NodeCache } from '@cacheable/node-cache';

const shortTTL = 60 * 60; // 1 hour in seconds
const ipCache = new NodeCache();

export const isExtendedLocation = (location: IpStackResult): location is ExtendedIpLocation => {
  return 'postalCode' in location;
};

export const getLocationInfo = async (ip?: string): Promise<IpStackResult> => {
  if (!ip) {
    return { ip: '' };
  }

  const cached = ipCache.get<IpStackResult>(ip);
  if (cached) {
    return cached;
  }

  const isLocalhost = ip === '127.0.0.1' || ip === '::1' || ip === 'localhost';
  if (isLocalhost) {
    return { ip };
  }

  const locationData = await getInfo(ip);
  const ttl = locationData.latitude ? config.service.ipStack.cache.TTL : shortTTL;

  ipCache.set(ip, locationData, ttl);

  return locationData;
};
