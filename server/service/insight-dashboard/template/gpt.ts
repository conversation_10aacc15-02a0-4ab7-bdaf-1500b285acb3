import {
  ChartSubType,
  getDefaultFilters,
  InsightDashboardItemType,
  InsightDashboardType,
  StaticDashboard,
  TextSubType,
  TimeFrameType,
} from '../../../models/insightDashboard';
import { ObjectId } from 'bson';
import { config } from './common';

const EMISSIONS_OVER_TIME_TEXT = {
  gridSize: {
    x: 0,
    y: 0,
    w: 12,
    h: 3,
    minW: 4,
    minH: 3,
  },
  type: InsightDashboardItemType.Text,
  text: 'Emissions over time',
  editorState: {
    root: {
      children: [
        {
          children: [
            {
              detail: 0,
              format: 0,
              mode: 'normal',
              style: '',
              text: 'Emissions over time',
              type: 'text',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'heading',
          version: 1,
          tag: 'h1',
        },
      ],
      direction: 'ltr',
      format: '',
      indent: 0,
      type: 'root',
      version: 1,
    },
  },
  subType: TextSubType.Free,
  _id: new ObjectId(),
};

const EMISSIONS_OVER_TIME = {
  title: 'Emissions over time',
  variables: {
    a: {
      code: 'green-project-tech-total-scope-1',
      valueListCode: 'scope_1_total',
      integrationCode: 'green-project-tech',
    },
    b: {
      code: 'green-project-tech-total-scope-2',
      valueListCode: 'scope_2_total',
      integrationCode: 'green-project-tech',
    },
    c: {
      code: 'green-project-tech-total-scope-3',
      valueListCode: 'scope_3_total',
      integrationCode: 'green-project-tech',
    },
  },
  gridSize: {
    x: 0,
    y: 3,
    w: 12,
    h: 17,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.Line,
  calculation: {
    type: 'formula',
    values: [
      {
        name: 'Scope 1 Total',
        formula: '{a}',
      },
      {
        name: 'Scope 2 Total',
        formula: '{b}',
      },
      {
        name: 'Scope 3 Total',
        formula: '{c}',
      },
    ],
  },
  _id: new ObjectId(),
};

const SPACE_1 = {
  gridSize: {
    x: 0,
    y: 56,
    w: 12,
    h: 5,
    minW: 2,
    maxW: 12,
    minH: 1,
  },
  type: InsightDashboardItemType.Space,
  _id: new ObjectId(),
};

const SCOPE_1_TOTAL_TEXT = {
  gridSize: {
    x: 0,
    y: 25,
    w: 12,
    h: 3,
    minW: 4,
    minH: 3,
  },
  type: InsightDashboardItemType.Text,
  text: 'Scope 1:',
  editorState: {
    root: {
      children: [
        {
          children: [
            {
              detail: 0,
              format: 0,
              mode: 'normal',
              style: '',
              text: 'Scope 1:',
              type: 'text',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'heading',
          version: 1,
          tag: 'h1',
        },
      ],
      direction: 'ltr',
      format: '',
      indent: 0,
      type: 'root',
      version: 1,
    },
  },
  subType: TextSubType.Free,
  _id: new ObjectId(),
};

const SCOPE_1_TOTAL = {
  title: 'Scope 1 Total',
  variables: {
    a: {
      code: 'green-project-tech-total-scope-1',
      valueListCode: 'scope_1_total',
      integrationCode: 'green-project-tech',
    },
  },
  gridSize: {
    x: 0,
    y: 28,
    w: 12,
    h: 15,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.SparkLine,
  _id: new ObjectId(),
};

const COMBUSTION = {
  title: 'Combustion',
  variables: {
    a: {
      code: 'green-project-tech-total-scope-1',
      valueListCode: 'stationary_combustion',
      integrationCode: 'green-project-tech',
    },
    b: {
      code: 'green-project-tech-total-scope-1',
      valueListCode: 'mobile_combustion',
      integrationCode: 'green-project-tech',
    },
  },
  gridSize: {
    x: 0,
    y: 43,
    w: 6,
    h: 13,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.Column,
  calculation: {
    type: 'formula',
    values: [
      {
        name: 'Stationary Combustion',
        formula: '{a}',
      },
      {
        name: 'Mobile Combustion',
        formula: '{b}',
      },
    ],
  },
  _id: new ObjectId(),
};

const FUGITIVE_AND_PROCESSED_EMISSIONS = {
  title: 'Fugitive and Processed Emissions',
  variables: {
    a: {
      code: 'green-project-tech-total-scope-1',
      valueListCode: 'fugitive_and_processed_emissions',
      integrationCode: 'green-project-tech',
    },
  },
  gridSize: {
    x: 6,
    y: 43,
    w: 6,
    h: 13,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.SingleValue,
  _id: new ObjectId(),
};

const SPACE_2 = {
  gridSize: {
    x: 0,
    y: 92,
    w: 12,
    h: 5,
    minW: 2,
    maxW: 12,
    minH: 1,
  },
  type: InsightDashboardItemType.Space,
  _id: new ObjectId(),
};

const SCOPE_2_TOTAL_TEXT = {
  gridSize: {
    x: 0,
    y: 61,
    w: 12,
    h: 3,
    minW: 4,
    minH: 3,
  },
  type: InsightDashboardItemType.Text,
  text: 'Scope 2:',
  editorState: {
    root: {
      children: [
        {
          children: [
            {
              detail: 0,
              format: 0,
              mode: 'normal',
              style: '',
              text: 'Scope 2:',
              type: 'text',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'heading',
          version: 1,
          tag: 'h1',
        },
      ],
      direction: 'ltr',
      format: '',
      indent: 0,
      type: 'root',
      version: 1,
    },
  },
  subType: TextSubType.Free,
  _id: new ObjectId(),
};

const SCOPE_2_TOTAL = {
  title: 'Scope 2 total',
  variables: {
    a: {
      code: 'green-project-tech-total-scope-2',
      valueListCode: 'scope_2_total',
      integrationCode: 'green-project-tech',
    },
  },
  gridSize: {
    x: 0,
    y: 64,
    w: 12,
    h: 16,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.SparkLine,
  _id: new ObjectId(),
};

const PURCHASED_ELECTRICITY = {
  title: 'Purchased Electricity',
  variables: {
    a: {
      code: 'green-project-tech-total-scope-2',
      valueListCode: 'purchased_electricity',
      integrationCode: 'green-project-tech',
    },
  },
  gridSize: {
    x: 0,
    y: 80,
    w: 6,
    h: 12,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.SingleValue,
  _id: new ObjectId(),
};

const THERMAL_USE = {
  title: 'Thermal Use',
  variables: {
    a: {
      code: 'green-project-tech-total-scope-2',
      valueListCode: 'thermal_use',
      integrationCode: 'green-project-tech',
    },
  },
  gridSize: {
    x: 6,
    y: 80,
    w: 6,
    h: 12,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.SingleValue,
  _id: new ObjectId(),
};

const SPACE_3 = {
  gridSize: {
    x: 0,
    y: 20,
    w: 12,
    h: 5,
    minW: 2,
    maxW: 12,
    minH: 1,
  },
  type: InsightDashboardItemType.Space,
  _id: new ObjectId(),
};

const SCOPE_3_TOTAL_TEXT = {
  gridSize: {
    x: 0,
    y: 97,
    w: 12,
    h: 3,
    minW: 4,
    minH: 3,
  },
  type: InsightDashboardItemType.Text,
  text: 'Scope 3:',
  editorState: {
    root: {
      children: [
        {
          children: [
            {
              detail: 0,
              format: 0,
              mode: 'normal',
              style: '',
              text: 'Scope 3:',
              type: 'text',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'heading',
          version: 1,
          tag: 'h1',
        },
      ],
      direction: 'ltr',
      format: '',
      indent: 0,
      type: 'root',
      version: 1,
    },
  },
  subType: TextSubType.Free,
  _id: new ObjectId(),
};

const SCOPE_3_TOTAL = {
  title: 'Scope 3 Total',
  variables: {
    a: {
      code: 'green-project-tech-total-scope-3',
      valueListCode: 'scope_3_total',
      integrationCode: 'green-project-tech',
    },
  },
  gridSize: {
    x: 0,
    y: 100,
    w: 12,
    h: 15,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.SparkLine,
  _id: new ObjectId(),
};

const GOODS = {
  title: 'Goods',
  variables: {
    a: {
      code: 'green-project-tech-total-scope-3',
      valueListCode: 'purchased_goods_and_services',
      integrationCode: 'green-project-tech',
    },
    b: {
      code: 'green-project-tech-total-scope-3',
      valueListCode: 'capital_goods',
      integrationCode: 'green-project-tech',
    },
  },
  gridSize: {
    x: 0,
    y: 115,
    w: 4,
    h: 12,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.Column,
  calculation: {
    type: 'formula',
    values: [
      {
        name: 'Purchased Goods and Services',
        formula: '{a}',
      },
      {
        name: 'Capital Goods',
        formula: '{b}',
      },
    ],
  },
  _id: new ObjectId(),
};

const FUEL_AND_ENERGY_RELATED_ACTIVITIES = {
  title: 'Fuel and energy related activities',
  variables: {
    a: {
      code: 'green-project-tech-total-scope-3',
      valueListCode: 'fuel_and_energy_related_activities',
      integrationCode: 'green-project-tech',
    },
  },
  gridSize: {
    x: 4,
    y: 115,
    w: 4,
    h: 12,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.SparkLine,
  _id: new ObjectId(),
};

const TRANSPORTATION_AND_DISTRIBUTION = {
  title: 'Transportation and Distribution',
  variables: {
    a: {
      code: 'green-project-tech-total-scope-3',
      valueListCode: 'upstream_transportation_and_distribution',
      integrationCode: 'green-project-tech',
    },
    b: {
      code: 'green-project-tech-total-scope-3',
      valueListCode: 'downstream_transportation_and_distribution',
      integrationCode: 'green-project-tech',
    },
  },
  gridSize: {
    x: 8,
    y: 115,
    w: 4,
    h: 12,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.Column,
  calculation: {
    type: 'formula',
    values: [
      {
        name: 'Upstream Transportation and Distribution',
        formula: '{a}',
      },
      {
        name: 'Downstream Transportation and Distribution',
        formula: '{b}',
      },
    ],
  },
  _id: new ObjectId(),
};

const TRAVEL = {
  title: 'Travel',
  variables: {
    a: {
      code: 'green-project-tech-total-scope-3',
      valueListCode: 'employee_commuting',
      integrationCode: 'green-project-tech',
    },
    b: {
      code: 'green-project-tech-total-scope-3',
      valueListCode: 'business_travel',
      integrationCode: 'green-project-tech',
    },
  },
  gridSize: {
    x: 0,
    y: 127,
    w: 4,
    h: 12,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.Column,
  calculation: {
    type: 'formula',
    values: [
      {
        name: 'Employee Commuting',
        formula: '{a}',
      },
      {
        name: 'Business Travel',
        formula: '{b}',
      },
    ],
  },
  _id: new ObjectId(),
};

const WASTE = {
  title: 'Waste',
  variables: {
    a: {
      code: 'green-project-tech-total-scope-3',
      valueListCode: 'waste',
      integrationCode: 'green-project-tech',
    },
  },
  gridSize: {
    x: 4,
    y: 127,
    w: 4,
    h: 12,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.SingleValue,
  _id: new ObjectId(),
};

const PRODUCTS = {
  title: 'Products',
  variables: {
    a: {
      code: 'green-project-tech-total-scope-3',
      valueListCode: 'processing_of_sold_products',
      integrationCode: 'green-project-tech',
    },
    b: {
      code: 'green-project-tech-total-scope-3',
      valueListCode: 'use_of_sold_products',
      integrationCode: 'green-project-tech',
    },
    c: {
      code: 'green-project-tech-total-scope-3',
      valueListCode: 'eol_of_sold_products',
      integrationCode: 'green-project-tech',
    },
  },
  gridSize: {
    x: 8,
    y: 127,
    w: 4,
    h: 12,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.Column,
  calculation: {
    type: 'formula',
    values: [
      {
        name: 'Processing of Sold Products',
        formula: '{a}',
      },
      {
        name: 'Use of Sold Products',
        formula: '{b}',
      },
      {
        name: 'EOL of Sold Products',
        formula: '{c}',
      },
    ],
  },
  _id: new ObjectId(),
};

const LEASED_ASSETS = {
  title: 'Leased assets',
  variables: {
    a: {
      code: 'green-project-tech-total-scope-3',
      valueListCode: 'upstream_leased_assets',
      integrationCode: 'green-project-tech',
    },
    b: {
      code: 'green-project-tech-total-scope-3',
      valueListCode: 'downstream_leased_assets',
      integrationCode: 'green-project-tech',
    },
  },
  gridSize: {
    x: 0,
    y: 139,
    w: 4,
    h: 12,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.Column,
  calculation: {
    type: 'formula',
    values: [
      {
        name: 'Upstream Leased Assets',
        formula: '{a}',
      },
      {
        name: 'Downstream Leased Assets',
        formula: '{b}',
      },
    ],
  },
  _id: new ObjectId(),
};

const FRANCHISES = {
  title: 'Franchises',
  variables: {
    a: {
      code: 'green-project-tech-total-scope-3',
      valueListCode: 'franchises',
      integrationCode: 'green-project-tech',
    },
  },
  gridSize: {
    x: 4,
    y: 139,
    w: 4,
    h: 12,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.SparkLine,
  _id: new ObjectId(),
};

const INVESTMENTS = {
  title: 'Investments',
  variables: {
    a: {
      code: 'green-project-tech-total-scope-3',
      valueListCode: 'investments',
      integrationCode: 'green-project-tech',
    },
  },
  gridSize: {
    x: 8,
    y: 139,
    w: 4,
    h: 12,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.SparkLine,
  _id: new ObjectId(),
};

export const gpt: StaticDashboard = {
  title: 'Emissions Tracker Template',
  type: InsightDashboardType.GPTTemplate,
  filters: {
    ...getDefaultFilters(),
    timeFrame: {
      type: TimeFrameType.AllTime,
    },
    baselinesTargets: {
      enabled: true,
    },
    shareWithSubsidiaries: {
      enabled: true,
    },
  },
  items: [
    EMISSIONS_OVER_TIME_TEXT,
    EMISSIONS_OVER_TIME,
    SPACE_1,
    SCOPE_1_TOTAL_TEXT,
    SCOPE_1_TOTAL,
    COMBUSTION,
    FUGITIVE_AND_PROCESSED_EMISSIONS,
    SPACE_2,
    SCOPE_2_TOTAL_TEXT,
    SCOPE_2_TOTAL,
    PURCHASED_ELECTRICITY,
    THERMAL_USE,
    SPACE_3,
    SCOPE_3_TOTAL_TEXT,
    SCOPE_3_TOTAL,
    GOODS,
    FUEL_AND_ENERGY_RELATED_ACTIVITIES,
    TRANSPORTATION_AND_DISTRIBUTION,
    TRAVEL,
    WASTE,
    PRODUCTS,
    LEASED_ASSETS,
    FRANCHISES,
    INVESTMENTS,
  ].map(item => ({ ...item, config })),
};
