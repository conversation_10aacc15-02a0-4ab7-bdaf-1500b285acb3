import { ObjectId } from 'bson';
import {
  CalculationType,
  ChartSubType,
  getDefaultFilters,
  InsightDashboardItemType,
  InsightDashboardType,
  StaticDashboard,
  TextSubType,
  TimeFrameType,
} from '../../../models/insightDashboard';
import serverConfig from '../../../config';
import { config } from './common';

const OVERVIEW_TEXT = {
  gridSize: {
    x: 0,
    y: 0,
    w: 12,
    h: 4,
    minW: 4,
    minH: 3,
  },
  type: InsightDashboardItemType.Text,
  text: 'Overview',
  editorState: {
    root: {
      children: [
        {
          children: [
            {
              detail: 0,
              format: 0,
              mode: 'normal',
              style: '',
              text: 'Overview',
              type: 'text',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'heading',
          version: 1,
          tag: 'h1',
        },
      ],
      direction: 'ltr',
      format: '',
      indent: 0,
      type: 'root',
      version: 1,
    },
  },
  subType: TextSubType.Free,
  _id: new ObjectId(),
};

const EMISSIONS_BY_SCOPE = {
  title: 'Emissions by scope',
  variables: {
    a: {
      code: 'greenly-total-scope-1',
      valueListCode: 'scope_1_total',
      integrationCode: 'greenly',
    },
    b: {
      code: 'greenly-total-scope-2',
      valueListCode: 'scope_2_total',
      integrationCode: 'greenly',
    },
    c: {
      code: 'greenly-total-scope-3',
      valueListCode: 'scope_3_total',
      integrationCode: 'greenly',
    },
  },
  gridSize: {
    x: 0,
    y: 4,
    w: 12,
    h: 17,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.FullPie,
  calculation: {
    type: CalculationType.Formula,
    values: [
      {
        name: 'Scope 1 Total',
        formula: '{a}',
      },
      {
        name: 'Scope 2 Total',
        formula: '{b}',
      },
      {
        name: 'Scope 3 Total',
        formula: '{c}',
      },
    ],
  },
  _id: new ObjectId(),
  icon: `${serverConfig.assets.cdn}/carbon-calculators/greenly-logo.jpeg`,
};

const SCOPE_1_TOTAL = {
  title: 'Scope 1 Total',
  variables: {
    a: {
      code: 'greenly-total-scope-1',
      valueListCode: 'scope_1_total',
      integrationCode: 'greenly',
    },
  },
  gridSize: {
    x: 0,
    y: 21,
    w: 4,
    h: 12,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.SingleValue,
  _id: new ObjectId(),
  icon: `${serverConfig.assets.cdn}/carbon-calculators/greenly-logo.jpeg`,
};

const SCOPE_2 = {
  title: 'Scope 2',
  variables: {
    a: {
      code: 'greenly-total-scope-2',
      valueListCode: 'scope_2_total',
      integrationCode: 'greenly',
    },
  },
  gridSize: {
    x: 4,
    y: 21,
    w: 4,
    h: 12,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.SingleValue,
  _id: new ObjectId(),
  icon: `${serverConfig.assets.cdn}/carbon-calculators/greenly-logo.jpeg`,
};

const SCOPE_3 = {
  title: 'Scope 3',
  variables: {
    a: {
      code: 'greenly-total-scope-3',
      valueListCode: 'scope_3_total',
      integrationCode: 'greenly',
    },
  },
  gridSize: {
    x: 8,
    y: 21,
    w: 4,
    h: 12,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.SingleValue,
  _id: new ObjectId(),
  icon: `${serverConfig.assets.cdn}/carbon-calculators/greenly-logo.jpeg`,
};

const SPACE_1 = {
  gridSize: {
    x: 0,
    y: 33,
    w: 12,
    h: 3,
    minW: 2,
    maxW: 12,
    minH: 1,
  },
  type: InsightDashboardItemType.Space,
  _id: new ObjectId(),
};

const SCOPE_1_TEXT = {
  gridSize: {
    x: 0,
    y: 36,
    w: 12,
    h: 4,
    minW: 4,
    minH: 3,
  },
  type: InsightDashboardItemType.Text,
  text: 'Scope 1',
  editorState: {
    root: {
      children: [
        {
          children: [
            {
              detail: 0,
              format: 0,
              mode: 'normal',
              style: '',
              text: 'Scope 1',
              type: 'text',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'heading',
          version: 1,
          tag: 'h1',
        },
      ],
      direction: 'ltr',
      format: '',
      indent: 0,
      type: 'root',
      version: 1,
    },
  },
  subType: TextSubType.Free,
  _id: new ObjectId(),
};

const SCOPE_1_BREAKDOWN = {
  title: 'Breakdown',
  variables: {
    a: {
      code: 'greenly-total-scope-1',
      valueListCode: '1.1',
      integrationCode: 'greenly',
    },
    b: {
      code: 'greenly-total-scope-1',
      valueListCode: '1.2',
      integrationCode: 'greenly',
    },
    c: {
      code: 'greenly-total-scope-1',
      valueListCode: '1.3',
      integrationCode: 'greenly',
    },
    d: {
      code: 'greenly-total-scope-1',
      valueListCode: '1.4',
      integrationCode: 'greenly',
    },
  },
  gridSize: {
    x: 0,
    y: 40,
    w: 6,
    h: 21,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.Pie,
  calculation: {
    type: CalculationType.Formula,
    values: [
      {
        name: 'Generation of electricity, heat or steam',
        formula: '{a}',
      },
      {
        name: 'Transportation of materials, products, waste, and employees',
        formula: '{b}',
      },
      {
        name: 'Physical or chemical processing',
        formula: '{c}',
      },
      {
        name: 'Fugitive emissions',
        formula: '{d}',
      },
    ],
  },
  _id: new ObjectId(),
  icon: `${serverConfig.assets.cdn}/carbon-calculators/greenly-logo.jpeg`,
};

const SPACE_2 = {
  gridSize: {
    x: 0,
    y: 61,
    w: 12,
    h: 3,
    minW: 2,
    maxW: 12,
    minH: 1,
  },
  type: InsightDashboardItemType.Space,
  _id: new ObjectId(),
};

const SCOPE_2_TEXT = {
  gridSize: {
    x: 0,
    y: 64,
    w: 12,
    h: 4,
    minW: 4,
    minH: 3,
  },
  type: InsightDashboardItemType.Text,
  text: 'Scope 2',
  editorState: {
    root: {
      children: [
        {
          children: [
            {
              detail: 0,
              format: 0,
              mode: 'normal',
              style: '',
              text: 'Scope 2',
              type: 'text',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'heading',
          version: 1,
          tag: 'h1',
        },
      ],
      direction: 'ltr',
      format: '',
      indent: 0,
      type: 'root',
      version: 1,
    },
  },
  subType: TextSubType.Free,
  _id: new ObjectId(),
};

const SCOPE_2_BREAKDOWN = {
  title: 'Breakdown',
  variables: {
    a: {
      code: 'greenly-total-scope-2',
      valueListCode: '2.1',
      integrationCode: 'greenly',
    },
    b: {
      code: 'greenly-total-scope-2',
      valueListCode: '2.2',
      integrationCode: 'greenly',
    },
  },
  gridSize: {
    x: 0,
    y: 68,
    w: 6,
    h: 21,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.Pie,
  calculation: {
    type: CalculationType.Formula,
    values: [
      {
        name: 'Electricity related indirect emissions',
        formula: '{a}',
      },
      {
        name: 'Steam, heat and cooling related indirect emissions',
        formula: '{b}',
      },
    ],
  },
  _id: new ObjectId(),
  icon: `${serverConfig.assets.cdn}/carbon-calculators/greenly-logo.jpeg`,
};

const SPACE_3 = {
  gridSize: {
    x: 0,
    y: 89,
    w: 12,
    h: 3,
    minW: 2,
    maxW: 12,
    minH: 1,
  },
  type: InsightDashboardItemType.Space,
  _id: new ObjectId(),
};

const SCOPE_3_TEXT = {
  gridSize: {
    x: 0,
    y: 92,
    w: 12,
    h: 4,
    minW: 4,
    minH: 3,
  },
  type: InsightDashboardItemType.Text,
  text: 'Scope 3',
  editorState: {
    root: {
      children: [
        {
          children: [
            {
              detail: 0,
              format: 0,
              mode: 'normal',
              style: '',
              text: 'Scope 3',
              type: 'text',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'heading',
          version: 1,
          tag: 'h1',
        },
      ],
      direction: 'ltr',
      format: '',
      indent: 0,
      type: 'root',
      version: 1,
    },
  },
  subType: TextSubType.Free,
  _id: new ObjectId(),
};

const SCOPE_3_BREAKDOWN = {
  title: 'Breakdown',
  variables: {
    a: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.1',
      integrationCode: 'greenly',
    },
    b: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.2',
      integrationCode: 'greenly',
    },
    c: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.3',
      integrationCode: 'greenly',
    },
    d: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.4',
      integrationCode: 'greenly',
    },
    e: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.5',
      integrationCode: 'greenly',
    },
    f: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.6',
      integrationCode: 'greenly',
    },
    g: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.7',
      integrationCode: 'greenly',
    },
    h: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.8',
      integrationCode: 'greenly',
    },
    i: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.9',
      integrationCode: 'greenly',
    },
    j: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.10',
      integrationCode: 'greenly',
    },
    k: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.11',
      integrationCode: 'greenly',
    },
    l: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.12',
      integrationCode: 'greenly',
    },
    m: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.13',
      integrationCode: 'greenly',
    },
    n: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.14',
      integrationCode: 'greenly',
    },
    o: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.15',
      integrationCode: 'greenly',
    },
  },
  gridSize: {
    x: 0,
    y: 96,
    w: 6,
    h: 21,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.Pie,
  calculation: {
    type: CalculationType.Formula,
    values: [
      {
        name: 'Purchased goods and services',
        formula: '{a}',
      },
      {
        name: 'Capital goods',
        formula: '{b}',
      },
      {
        name: 'Fuel- and energy- related activities not included in Scope 1 or Scope 2',
        formula: '{c}',
      },
      {
        name: 'Upstream transportation and distribution',
        formula: '{d}',
      },
      {
        name: 'Waste generated in operations',
        formula: '{e}',
      },
      {
        name: 'Business travel',
        formula: '{f}',
      },
      {
        name: 'Employee commuting',
        formula: '{g}',
      },
      {
        name: 'Upstream leased assets',
        formula: '{h}',
      },
      {
        name: 'Downstream transportation and distribution',
        formula: '{i}',
      },
      {
        name: 'Processing of sold products',
        formula: '{j}',
      },
      {
        name: 'Use of sold products',
        formula: '{k}',
      },
      {
        name: 'End-of-life treatment of sold products',
        formula: '{l}',
      },
      {
        name: 'Downstream leased assets',
        formula: '{m}',
      },
      {
        name: 'Franchises',
        formula: '{n}',
      },
      {
        name: 'Investments',
        formula: '{o}',
      },
    ],
  },
  _id: new ObjectId(),
  icon: `${serverConfig.assets.cdn}/carbon-calculators/greenly-logo.jpeg`,
};

const SCOPE_1_TIMELINE = {
  title: 'Timeline',
  variables: {
    a: {
      code: 'greenly-total-scope-1',
      valueListCode: '1.1',
      integrationCode: 'greenly',
    },
    b: {
      code: 'greenly-total-scope-1',
      valueListCode: '1.2',
      integrationCode: 'greenly',
    },
    c: {
      code: 'greenly-total-scope-1',
      valueListCode: '1.3',
      integrationCode: 'greenly',
    },
    d: {
      code: 'greenly-total-scope-1',
      valueListCode: '1.4',
      integrationCode: 'greenly',
    },
  },
  gridSize: {
    x: 6,
    y: 40,
    w: 6,
    h: 21,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.Line,
  calculation: {
    type: CalculationType.Formula,
    values: [
      {
        name: 'Generation of electricity, heat or steam',
        formula: '{a}',
      },
      {
        name: 'Transportation of materials, products, waste, and employees',
        formula: '{b}',
      },
      {
        name: 'Physical or chemical processing',
        formula: '{c}',
      },
      {
        name: 'Fugitive emissions',
        formula: '{d}',
      },
    ],
  },
  _id: new ObjectId(),
  icon: `${serverConfig.assets.cdn}/carbon-calculators/greenly-logo.jpeg`,
};

const SCOPE_2_TIMELINE = {
  title: 'Timeline',
  variables: {
    a: {
      code: 'greenly-total-scope-2',
      valueListCode: '2.1',
      integrationCode: 'greenly',
    },
    b: {
      code: 'greenly-total-scope-2',
      valueListCode: '2.2',
      integrationCode: 'greenly',
    },
  },
  gridSize: {
    x: 6,
    y: 68,
    w: 6,
    h: 21,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.Line,
  calculation: {
    type: CalculationType.Formula,
    values: [
      {
        name: 'Electricity related indirect emissions',
        formula: '{a}',
      },
      {
        name: 'Steam, heat and cooling related indirect emissions',
        formula: '{b}',
      },
    ],
  },
  _id: new ObjectId(),
  icon: `${serverConfig.assets.cdn}/carbon-calculators/greenly-logo.jpeg`,
};

const SCOPE_3_TIMELINE = {
  title: 'Timeline',
  variables: {
    a: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.1',
      integrationCode: 'greenly',
    },
    b: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.2',
      integrationCode: 'greenly',
    },
    c: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.3',
      integrationCode: 'greenly',
    },
    d: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.4',
      integrationCode: 'greenly',
    },
    e: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.5',
      integrationCode: 'greenly',
    },
    f: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.6',
      integrationCode: 'greenly',
    },
    g: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.7',
      integrationCode: 'greenly',
    },
    h: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.8',
      integrationCode: 'greenly',
    },
    i: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.9',
      integrationCode: 'greenly',
    },
    j: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.10',
      integrationCode: 'greenly',
    },
    k: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.11',
      integrationCode: 'greenly',
    },
    l: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.12',
      integrationCode: 'greenly',
    },
    m: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.13',
      integrationCode: 'greenly',
    },
    n: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.14',
      integrationCode: 'greenly',
    },
    o: {
      code: 'greenly-total-scope-3',
      valueListCode: '3.15',
      integrationCode: 'greenly',
    },
  },
  gridSize: {
    x: 6,
    y: 96,
    w: 6,
    h: 21,
    minW: 4,
    minH: 12,
  },
  type: InsightDashboardItemType.Integration,
  subType: ChartSubType.Line,
  calculation: {
    type: CalculationType.Formula,
    values: [
      {
        name: 'Purchased goods and services',
        formula: '{a}',
      },
      {
        name: 'Capital goods',
        formula: '{b}',
      },
      {
        name: 'Fuel- and energy- related activities not included in Scope 1 or Scope 2',
        formula: '{c}',
      },
      {
        name: 'Upstream transportation and distribution',
        formula: '{d}',
      },
      {
        name: 'Waste generated in operations',
        formula: '{e}',
      },
      {
        name: 'Business travel',
        formula: '{f}',
      },
      {
        name: 'Employee commuting',
        formula: '{g}',
      },
      {
        name: 'Upstream leased assets',
        formula: '{h}',
      },
      {
        name: 'Downstream transportation and distribution',
        formula: '{i}',
      },
      {
        name: 'Processing of sold products',
        formula: '{j}',
      },
      {
        name: 'Use of sold products',
        formula: '{k}',
      },
      {
        name: 'End-of-life treatment of sold products',
        formula: '{l}',
      },
      {
        name: 'Downstream leased assets',
        formula: '{m}',
      },
      {
        name: 'Franchises',
        formula: '{n}',
      },
      {
        name: 'Investments',
        formula: '{o}',
      },
    ],
  },
  _id: new ObjectId(),
  icon: `${serverConfig.assets.cdn}/carbon-calculators/greenly-logo.jpeg`,
};

export const greenly: StaticDashboard = {
  title: 'Emissions',
  type: InsightDashboardType.GreenlyTemplate,
  filters: {
    ...getDefaultFilters(),
    timeFrame: {
      type: TimeFrameType.AllTime,
    },
    baselinesTargets: {
      enabled: true,
    },
    shareWithSubsidiaries: {
      enabled: true,
    },
  },
  items: [
    OVERVIEW_TEXT,
    EMISSIONS_BY_SCOPE,
    SCOPE_1_TOTAL,
    SCOPE_2,
    SCOPE_3,
    SPACE_1,
    SCOPE_1_TEXT,
    SCOPE_1_BREAKDOWN,
    SCOPE_1_TIMELINE,
    SPACE_2,
    SCOPE_2_TEXT,
    SCOPE_2_BREAKDOWN,
    SCOPE_2_TIMELINE,
    SPACE_3,
    SCOPE_3_TEXT,
    SCOPE_3_BREAKDOWN,
    SCOPE_3_TIMELINE,
  ].map((item) => ({ ...item, config })),
};
