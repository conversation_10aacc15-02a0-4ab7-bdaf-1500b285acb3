import { StaticDashboard } from '../../../models/insightDashboard';
import { gpt } from './gpt';
import { greenly } from './greenly';
import { wfn } from './wfn';

export enum DashboardTemplateType {
  WFN = 'wfn',
  GPT = 'gpt',
  Greenly = 'greenly',
}

export const templateDashboard: Record<DashboardTemplateType, StaticDashboard | undefined> = {
  [DashboardTemplateType.WFN]: wfn,
  [DashboardTemplateType.GPT]: gpt,
  [DashboardTemplateType.Greenly]: greenly,
};
