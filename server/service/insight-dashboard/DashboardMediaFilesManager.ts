import { FileStorageInterface, getStorage } from '../storage/fileStorage';
import Document, { DocumentOwnerSubType, DocumentOwnerType, DocumentPlain } from '../../models/document';
import { ObjectId } from 'bson';
import { FilterQuery } from 'mongoose';
import ContextError from '../../error/ContextError';
import { InsightDashboard, InsightDashboardItemType } from '../../models/insightDashboard';
import { CategorizedDocument, DashboardDocumentCleanupParams } from './utils';

const ALLOWED_MEDIA_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'video/mp4'];
const UPLOAD_PATH_PREFIX = 'dashboard';
const OWNER_TYPE = DocumentOwnerType.InsightDashboard;
const OWNER_SUB_TYPE = DocumentOwnerSubType.InsightDashboardItem;

export class DashboardMediaFilesManager {
  constructor(private storage: FileStorageInterface) {}
  public async uploadFiles({
    files,
    userId,
    dashboardId,
  }: {
    files: Express.Multer.File[];
    userId: ObjectId;
    dashboardId: ObjectId;
  }) {
    const result = await Promise.allSettled(
      files.map(async (file) => {
        if (!ALLOWED_MEDIA_TYPES.includes(file.mimetype)) {
          throw new ContextError(`Do not support file type ${file.mimetype} for file ${file.originalname}`, {
            dashboardId,
            userId,
          });
        }

        const extension = this.storage.getExtensionFromMimeType(file.mimetype);

        const isPublic = false;
        const model = new Document({
          title: file.originalname,
          ownerType: OWNER_TYPE,
          ownerId: dashboardId,
          ownerSubType: OWNER_SUB_TYPE,
          metadata: {
            name: file.originalname,
            mimetype: file.mimetype,
            extension,
          },
          size: file.size,
          userId,
          public: isPublic,
        });

        const uploadPath = `${UPLOAD_PATH_PREFIX}/${dashboardId.toString()}/${model.id}.${extension}`;

        const uploadResult = await this.storage.upload(file.path, uploadPath, file.mimetype, isPublic);

        model.path = uploadResult.path;
        await model.save();

        return { documentId: model._id, name: file.originalname, itemId: file.fieldname };
      })
    );
    return result;
  }

  public async deleteFiles(documentIds: ObjectId[], dashboardId: ObjectId) {
    const filter = { _id: { $in: documentIds }, ownerId: dashboardId, ownerType: OWNER_TYPE };
    return this.deleteFilesByFilter(filter);
  }

  private async categorizeDashboardDocuments({
    dashboardId,
    initiativeId,
    deletedDashboardDocumentIds,
  }: DashboardDocumentCleanupParams) {
    const documentIdMatchCondition = {
      'items.files.documentId': {
        $in: deletedDashboardDocumentIds.map((id) => new ObjectId(id)),
      },
    };
    const [result] = await InsightDashboard.aggregate<CategorizedDocument>([
      {
        $match: {
          _id: { $ne: dashboardId },
          initiativeId,
          'items.type': InsightDashboardItemType.Media,
          ...documentIdMatchCondition,
        },
      },
      { $unwind: '$items' },
      { $unwind: '$items.files' },
      // Retain the individual items in the overall document that match
      {
        $match: {
          ...documentIdMatchCondition,
        },
      },
      // Group the documents by their documentId and collect the owner
      {
        $group: {
          _id: { $toString: '$items.files.documentId' },
          ownerId: { $first: '$_id' },
        },
      },
      // Categorize toReOwn and toDelete arrays
      {
        $group: {
          _id: null,
          documentsToReOwn: {
            $push: { _id: '$_id', ownerId: '$ownerId' },
          },
          documentsToDelete: {
            $push: '$_id',
          },
        },
      },
      // Calculate documentsToDelete difference
      {
        $project: {
          documentsToReOwn: 1,
          documentsToDelete: {
            $setDifference: [deletedDashboardDocumentIds, '$documentsToDelete'],
          },
        },
      },
    ]);

    return (
      result ?? {
        documentsToDelete: deletedDashboardDocumentIds,
        documentsToReOwn: [],
      }
    );
  }

  private async reassignDocumentOwners(documentsToReOwn: { _id: string; ownerId: ObjectId }[]) {
    const bulkOperations = documentsToReOwn.map(({ _id, ownerId }) => ({
      updateOne: {
        filter: { _id: new ObjectId(_id) },
        update: { $set: { ownerId } },
      },
    }));

    return Document.bulkWrite(bulkOperations);
  }

  public async deleteFilesAndReassignOwnership({
    dashboardId,
    initiativeId,
    deletedDashboardDocumentIds,
  }: DashboardDocumentCleanupParams) {
    const { documentsToDelete, documentsToReOwn } = await this.categorizeDashboardDocuments({
      dashboardId,
      initiativeId,
      deletedDashboardDocumentIds,
    });
    if (documentsToReOwn.length > 0) {
      await this.reassignDocumentOwners(documentsToReOwn);
    }

    const filter = { _id: { $in: documentsToDelete.map((id) => new ObjectId(id)) }, ownerType: OWNER_TYPE };
    const result = await this.deleteFilesByFilter(filter);
    return result;
  }

  private async deleteFilesByFilter(filter: FilterQuery<DocumentPlain>) {
    const documents = await Document.find(filter, { path: 1 }).lean().exec();
    const paths = documents.map((d) => d.path);

    const result = await Promise.allSettled(paths.map((path) => this.storage.remove(path)));

    await Document.deleteMany(filter);

    return result;
  }
}

let instance: DashboardMediaFilesManager;
export const getDashboardMediaFilesManager = () => {
  if (!instance) {
    instance = new DashboardMediaFilesManager(getStorage());
  }

  return instance;
};
