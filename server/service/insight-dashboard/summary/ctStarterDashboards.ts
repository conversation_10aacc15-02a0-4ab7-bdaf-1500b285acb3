/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */


import {
  CalculationType,
  ChartSubType,
  getDefaultFilters,
  InsightDashboardItemType,
  InsightDashboardType,
  StaticDashboard,
  ValueRole
} from '../../../models/insightDashboard';
import { ObjectId } from 'bson';
import { employeeGenderDiversity405Pie } from "../calculations";

const greenGasEmissions = {
  title: 'Greenhouse gas emissions',
  variables: {
    a: {
      code: 'gri/2020/305-1/a',
    },
    b: {
      code: 'gri/2020/305-2/a',
    },
    c: {
      code: 'gri/2020/305-2/b',
    },
    d: {
      code: 'gri/2020/305-3/a',
    },
  },
  type: InsightDashboardItemType.Chart,
  subType: ChartSubType.SingleValue,
  calculation: {
    type: CalculationType.Formula,
    values: [
      {
        name: 'Greenhouse gas emissions',
        formula: '{a} + {b} + {c} + {d}',
      },
    ],
  },
  _id: new ObjectId('65632c9d42371dcec5019cbd'),
};

export const ctStarterDashboards: Record<string, StaticDashboard> = {
  [InsightDashboardType.Overview]: {
    title: 'Overview',
    type: InsightDashboardType.Overview,
    filters: getDefaultFilters(),
    items: [
      {
        ...greenGasEmissions,
        gridSize: {
          x: 0,
          y: 0,
          w: 4,
          h: 12,
          minW: 4,
          minH: 12,
        },
        isDefault: true,
      },
      {
        ...employeeGenderDiversity405Pie,
        gridSize: {
          x: 4,
          y: 0,
          w: 4,
          h: 12,
          minW: 4,
          minH: 12,
        },
        isDefault: true,
      },
      {
        title: 'Anti-corruption training',
        variables: {
          a: {
            code: 'gri/2020/205-2/d',
            valueListCode: 'anti_corruption_training_number',
          },
          b: {
            code: 'gri/2020/205-2/d',
            valueListCode: 'board_members_region',
          },
        },
        gridSize: {
          x: 8,
          y: 0,
          w: 4,
          h: 12,
          minW: 4,
          minH: 12,
        },
        type: InsightDashboardItemType.Chart,
        subType: ChartSubType.SingleValue,
        calculation: {
          type: CalculationType.Formula,
          values: [
            {
              name: 'Value',
              formula: '100 * {a} / {b}',
              decimalPlaces: 1,
            },
          ],
        },
        unitText: '%',
        note: {
          prefix: 'of board',
        },
        isDefault: true,
        _id: new ObjectId('65632c6c9c8245d864bf310f'),
      },
    ],
  },
  [InsightDashboardType.Environmental]: {
    title: 'Environmental',
    type: InsightDashboardType.Environmental,
    filters: getDefaultFilters(),
    items: [
      {
        ...greenGasEmissions,
        gridSize: {
          x: 0,
          y: 3,
          w: 4,
          h: 12,
          minW: 4,
          minH: 12,
        },
      },
      {
        gridSize: {
          x: 0,
          y: 0,
          w: 12,
          h: 3,
          minW: 4,
          minH: 3,
        },
        type: InsightDashboardItemType.Headline,
        text: 'Highlights',
        subType: ChartSubType.Line,
        _id: new ObjectId('65632c9d42371dcec5019cc9')
      },
      {
        title: 'Total energy consumption',
        variables: {
          a: {
            code: 'gri/2020/302-1/e',
            groupCode: 'gri',
          },
        },
        gridSize: {
          x: 8,
          y: 3,
          w: 4,
          h: 12,
          minW: 4,
          minH: 12,
        },
        type: InsightDashboardItemType.Chart,
        subType: ChartSubType.SingleValue,
        calculation: {
          type: CalculationType.Formula,
          values: [
            {
              name: 'Total energy consumption',
              formula: '{a}',
            },
          ],
        },
        _id: new ObjectId('65632c9d42371dcec5019cbe'),
      },
      {
        gridSize: {
          x: 0,
          y: 15,
          w: 12,
          h: 3,
          minW: 4,
          minH: 3,
        },
        type: InsightDashboardItemType.Headline,
        text: 'Emissions',
        subType: ChartSubType.Line,
        _id: new ObjectId('65632c9d42371dcec5019cc8'),
      },
      {
        title: '% emissions by scope',
        variables: {
          a: {
            code: 'gri/2020/305-1/a',
          },
          b: {
            code: 'gri/2020/305-2/a',
          },
          c: {
            code: 'gri/2020/305-2/b',
          },
          d: {
            code: 'gri/2020/305-3/a',
          },
        },
        gridSize: {
          x: 4,
          y: 3,
          w: 4,
          h: 12,
          minW: 4,
          minH: 12,
        },
        type: InsightDashboardItemType.Chart,
        subType: ChartSubType.Pie,
        calculation: {
          type: CalculationType.Formula,
          values: [
            {
              name: 'Scope 1',
              formula: '{a}',
            },
            {
              name: 'Scope 2',
              formula: '{b} + {c}',
            },
            {
              name: 'Scope 3',
              formula: '{d}',
            },
          ],
        },
        _id: new ObjectId('65632c9d42371dcec5019cbf'),
      },
      {
        title: 'Total emissions over time',
        variables: {
          a: {
            code: 'gri/2020/305-1/a',
          },
          b: {
            code: 'gri/2020/305-2/a',
          },
          c: {
            code: 'gri/2020/305-2/b',
          },
          d: {
            code: 'gri/2020/305-3/a',
          },
        },
        gridSize: {
          x: 0,
          y: 18,
          w: 6,
          h: 17,
          minW: 4,
          minH: 12,
        },
        type: InsightDashboardItemType.Chart,
        subType: ChartSubType.Line,
        calculation: {
          type: CalculationType.Sum,
          values: [
            {
              name: 'Emissions',
            },
          ],
        },
        _id: new ObjectId('65632c9d42371dcec5019cc0'),
      },
      {
        title: '% emissions by scope',
        variables: {
          a: {
            code: 'gri/2020/305-1/a',
          },
          b: {
            code: 'gri/2020/305-2/a',
          },
          c: {
            code: 'gri/2020/305-2/b',
          },
          d: {
            code: 'gri/2020/305-3/a',
          },
        },
        gridSize: {
          x: 6,
          y: 18,
          w: 6,
          h: 17,
          minW: 4,
          minH: 12,
        },
        type: InsightDashboardItemType.Chart,
        subType: ChartSubType.Line,
        calculation: {
          type: CalculationType.Percentage,
          values: [
            {
              name: 'Scope 1',
              formula: '{a}',
            },
            {
              name: 'Scope 2',
              formula: '{b} + {c}',
            },
            {
              name: 'Scope 3',
              formula: '{d}',
            },
          ],
        },
        _id: new ObjectId('65632c9d42371dcec5019cc1')
      },
      {
        gridSize: {
          x: 0,
          y: 35,
          w: 12,
          h: 3,
          minW: 4,
          minH: 3,
        },
        type: InsightDashboardItemType.Headline,
        text: 'Energy use',
        subType: ChartSubType.Line,
        _id: new ObjectId('65632c9d42371dcec5019cc2')
      },
      {
        title: 'Total energy consumption',
        variables: {
          a: {
            code: 'gri/2020/302-1/e',
            groupCode: 'gri',
          },
        },
        gridSize: {
          x: 0,
          y: 38,
          w: 6,
          h: 17,
          minW: 4,
          minH: 12,
        },
        type: InsightDashboardItemType.Chart,
        subType: ChartSubType.Line,
        calculation: {
          type: CalculationType.Sum,
          values: [
            {
              name: 'Consumption',
            },
          ],
        },
        _id: new ObjectId('65632c9d42371dcec5019cc3'),
      },
      {
        title: 'Energy intensity ratio',
        variables: {
          a: {
            code: 'gri/2020/302-3/a',
            groupCode: 'gri',
          },
        },
        gridSize: {
          x: 6,
          y: 38,
          w: 6,
          h: 17,
          minW: 4,
          minH: 12,
        },
        type: InsightDashboardItemType.Chart,
        subType: ChartSubType.Column,
        calculation: {
          type: CalculationType.Formula,
          values: [
            {
              name: 'Intensity',
              formula: '{a}',
            },
            {
              name: 'Annotation',
              formula: '{a}',
              role: ValueRole.Annotation,
            },
          ],
        },
        _id: new ObjectId('65632c9d42371dcec5019cc4')
      },
      {
        gridSize: {
          x: 0,
          y: 55,
          w: 12,
          h: 3,
          minW: 4,
          minH: 3,
        },
        type: InsightDashboardItemType.Headline,
        text: 'Water & Waste',
        _id: new ObjectId('65632c9d42371dcec5019cc5')
      },
      {
        title: 'Total water consumption',
        variables: {
          a: {
            code: 'gri/2020/303-5/a',
          },
        },
        gridSize: {
          x: 0,
          y: 58,
          w: 6,
          h: 17,
          minW: 4,
          minH: 12,
        },
        type: InsightDashboardItemType.Chart,
        subType: ChartSubType.Line,
        calculation: {
          type: CalculationType.Formula,
          values: [
            {
              name: 'Consumption',
              formula: '{a}',
            },
          ],
        },
        _id: new ObjectId('65632c9d42371dcec5019cc6')
      },
      {
        title: 'Hazardous and non-hazardous',
        variables: {
          a: {
            code: 'gri/2020/306-3/a',
            valueListCode: 'hazardous_waste',
          },
          b: {
            code: 'gri/2020/306-3/a',
            valueListCode: 'non_hazardous',
          },
        },
        gridSize: {
          x: 6,
          y: 58,
          w: 6,
          h: 17,
          minW: 4,
          minH: 12,
        },
        type: InsightDashboardItemType.Chart,
        subType: ChartSubType.Column,
        calculation: {
          type: CalculationType.Formula,
          values: [
            {
              name: 'Hazardous',
              formula: '{a}',
            },
            {
              name: 'Hazardous',
              formula: '{a}',
              role: ValueRole.Annotation,
            },
            {
              name: 'Non-Hazardous',
              formula: '{b}',
            },
            {
              name: 'Hazardous',
              formula: '{b}',
              role: ValueRole.Annotation,
            },
          ],
        },
        unitText: 'Metric Tonnes',
        _id: new ObjectId('65632c9d42371dcec5019cc7'),
      },
    ],
  },
  [InsightDashboardType.Social]: {
    title: 'Custom Social',
    type: InsightDashboardType.Social,
    filters: getDefaultFilters(),
    items: [
      {
        gridSize: {
          x: 0,
          y: 0,
          w: 12,
          h: 3,
          minW: 4,
          minH: 3,
        },
        type: InsightDashboardItemType.Headline,
        text: 'Highlights',
        subType: ChartSubType.Line,
        _id: new ObjectId('65632c9d42371dcec5019cca')
      },
      {
        ...employeeGenderDiversity405Pie,
        gridSize: {
          x: 0,
          y: 3,
          w: 4,
          h: 12,
          minW: 4,
          minH: 12,
        },
      },
      {
        title: 'Suppliers screened using social criteria',
        variables: {
          a: {
            code: 'gri/2020/414-1',
            groupCode: 'gri',
          },
        },
        gridSize: {
          x: 4,
          y: 3,
          w: 4,
          h: 12,
          minW: 4,
          minH: 12,
        },
        type: InsightDashboardItemType.Chart,
        subType: ChartSubType.SingleValue,
        unitText: '%',
        note: {
          prefix: 'of new suppliers',
        },
        calculation: {
          type: CalculationType.Formula,
          values: [
            {
              name: 'Value',
              formula: '{a}',
            },
          ],
        },
        _id: new ObjectId('65632c9d42371dcec5019ccc')
      },
      {
        title: '% healthcare benefits covering families',
        variables: {
          a: {
            code: 'gri/2020/403-1/b',
            valueListCode: 'pc_family_covered',
            groupCode: 'gri',
          },
        },
        gridSize: {
          x: 8,
          y: 3,
          w: 4,
          h: 12,
          minW: 4,
          minH: 12,
        },
        type: InsightDashboardItemType.Chart,
        subType: ChartSubType.SingleValue,
        calculation: {
          type: CalculationType.Formula,
          values: [
            {
              name: 'Value',
              formula: '{a}',
              decimalPlaces: 2,
            },
          ],
        },
        unitText: '%',
        _id: new ObjectId('65632c9d42371dcec5019ccd')
      },
      {
        gridSize: {
          x: 0,
          y: 15,
          w: 12,
          h: 3,
          minW: 4,
          minH: 3,
        },
        type: InsightDashboardItemType.Headline,
        text: 'Gender',
        subType: ChartSubType.Line,
        _id: new ObjectId('65632c9d42371dcec5019cce')
      },
      {
        title: 'Average female pay v male pay',
        variables: {
          a: {
            code: 'gri/2020/405-2/a',
            valueListCode: 'denominator_male',
          },
          b: {
            code: 'gri/2020/405-2/a',
            valueListCode: 'numerator_female',
          },
        },
        gridSize: {
          x: 0,
          y: 18,
          w: 6,
          h: 17,
          minW: 4,
          minH: 12,
        },
        type: InsightDashboardItemType.Chart,
        subType: ChartSubType.Column,
        calculation: {
          type: CalculationType.Formula,
          values: [
            {
              name: 'Male',
              formula: '{a}',
            },
            {
              name: 'Male',
              formula: '{a}',
              role: ValueRole.Annotation,
            },
            {
              name: 'Female',
              formula: '{b}',
            },
            {
              name: 'Female',
              formula: '{b}',
              role: ValueRole.Annotation,
            },
          ],
        },
        unitText: 'USD Millions',
        _id: new ObjectId('65632c9d42371dcec5019ccf')
      },
      {
        title: 'Gender: average hours of training',
        variables: {
          a: {
            code: 'gri/2020/404-1/a',
            valueListCode: 'male',
          },
          b: {
            code: 'gri/2020/404-1/a',
            valueListCode: 'female',
          },
        },
        gridSize: {
          x: 6,
          y: 18,
          w: 6,
          h: 17,
          minW: 4,
          minH: 12,
        },
        type: InsightDashboardItemType.Chart,
        subType: ChartSubType.Column,
        calculation: {
          type: CalculationType.Formula,
          values: [
            {
              name: 'Male',
              formula: '{a}',
            },
            {
              name: 'Male',
              formula: '{a}',
              role: ValueRole.Annotation,
            },
            {
              name: 'Female',
              formula: '{b}',
            },
            {
              name: 'Female',
              formula: '{b}',
              role: ValueRole.Annotation,
            },
          ],
        },
        unitText: 'Hours',
        _id: new ObjectId('65632c9d42371dcec5019cd0')
      },
      {
        gridSize: {
          x: 0,
          y: 52,
          w: 12,
          h: 3,
          minW: 4,
          minH: 3,
        },
        type: InsightDashboardItemType.Headline,
        text: 'Health',
        subType: ChartSubType.Line,
        _id: new ObjectId('65632c9d42371dcec5019cd1')
      },
      {
        title: 'Workplace injuries',
        variables: {
          a: {
            code: 'gri/2020/403-9/a',
            valueListCode: 'injuries_company_employees_number_fatalities',
          },
          b: {
            code: 'gri/2020/403-9/a',
            valueListCode: 'injuries_company_employees_high-consequence_work-related_injuries_number',
          },
          c: {
            code: 'gri/2020/403-9/a',
            valueListCode: 'injuries_company_employees_recordable_injuries_number',
          },
          d: {
            code: 'gri/2020/403-10/a',
            valueListCode: 'health_ill_health',
          },
        },
        gridSize: {
          x: 6,
          y: 55,
          w: 12,
          h: 17,
          minW: 4,
          minH: 12,
        },
        type: InsightDashboardItemType.Table,
        subType: ChartSubType.Table,
        calculation: {
          type: CalculationType.Formula,
          values: [
            { name: 'Fatalities', formula: '{a}' },
            { name: 'High consequence injuries', formula: '{b}' },
            { name: 'Injuries', formula: '{c}' },
            { name: 'Ill health', formula: '{d}' },
          ],
        },
        _id: new ObjectId('65632c9d42371dcec5019cd2')
      },
    ],
  },
  [InsightDashboardType.Governance]: {
    title: 'Custom Governance',
    type: InsightDashboardType.Governance,
    filters: getDefaultFilters(),
    items: [
      {
        title: '% of board with anti-corruption training',
        variables: {
          a: {
            code: 'gri/2020/205-2/d',
            valueListCode: 'board_members_region',
          },
          b: {
            code: 'gri/2020/205-2/d',
            valueListCode: 'anti_corruption_training_number',
          },
        },
        gridSize: {
          x: 0,
          y: 3,
          w: 4,
          h: 12,
          minW: 4,
          minH: 12,
        },
        type: InsightDashboardItemType.Chart,
        subType: ChartSubType.Column,
        calculation: {
          type: CalculationType.Formula,
          values: [
            {
              name: 'Board with Anti-Corruption Training',
              formula: '100 * {b} / {a}',
            },
          ],
        },
        unitText: '%',
        _id: new ObjectId('65632c9d42371dcec5019cd3'),
      },
      {
        title: 'Board diversity',
        variables: {
          a: {
            code: 'gri/2020/405-1/a',
            valueListCode: 'male',
          },
          b: {
            code: 'gri/2020/405-1/a',
            valueListCode: 'female',
          },
        },
        gridSize: {
          x: 4,
          y: 3,
          w: 4,
          h: 12,
          minW: 4,
          minH: 12,
        },
        type: InsightDashboardItemType.Chart,
        subType: ChartSubType.PieWithLegend,
        calculation: {
          type: CalculationType.Formula,
          values: [
            {
              name: 'Male',
              formula: '{a}',
            },
            {
              name: 'Female',
              formula: '{b}',
            },
          ],
        },
        _id: new ObjectId('65632c9d42371dcec5019cd4')
      },
      {
        title: '% women on the board',
        variables: {
          a: {
            code: 'gri/2020/405-1/a',
            valueListCode: 'female',
          },
        },
        gridSize: {
          x: 8,
          y: 3,
          w: 4,
          h: 12,
          minW: 4,
          minH: 12,
        },
        type: InsightDashboardItemType.Chart,
        subType: ChartSubType.SingleValue,
        calculation: {
          type: CalculationType.Formula,
          values: [
            {
              name: '% women on the board',
              formula: '{a}',
            },
          ],
        },
        unitText: '%',
        _id: new ObjectId('65632c9d42371dcec5019cd5')
      },
      {
        gridSize: {
          x: 0,
          y: 15,
          w: 12,
          h: 3,
          minW: 4,
          minH: 3,
        },
        type: InsightDashboardItemType.Headline,
        text: 'Diversity',
        subType: ChartSubType.Line,
        _id: new ObjectId('65632c9d42371dcec5019cd6')
      },
      {
        title: 'Board diversity: gender split',
        variables: {
          a: {
            code: 'gri/2020/405-1/a',
            valueListCode: 'male',
          },
          b: {
            code: 'gri/2020/405-1/a',
            valueListCode: 'female',
          },
        },
        gridSize: {
          x: 0,
          y: 35,
          w: 12,
          h: 17,
          minW: 4,
          minH: 12,
        },
        type: InsightDashboardItemType.Chart,
        subType: ChartSubType.Column,
        calculation: {
          type: CalculationType.Formula,
          values: [
            {
              name: 'Male',
              formula: '{a}',
            },
            {
              name: 'Male',
              formula: '{a}',
              role: ValueRole.Annotation,
            },
            {
              name: 'Female',
              formula: '{b}',
            },
            {
              name: 'Female',
              formula: '{b}',
              role: ValueRole.Annotation,
            },
          ],
        },
        unitText: '%',
        _id: new ObjectId('65632c9d42371dcec5019cd7')
      },
    ],
  },
};
