import { InsightDashboard, InsightDashboardItem, InsightDashboardType } from '../../../models/insightDashboard';
import { ObjectId } from 'bson';
import { overview } from './overview';
import UserError from '../../../error/UserError';
import { getOrdinate } from '../utils';
import { SummaryDashboardService, getSummaryDashboardService } from './SummaryDashboardService';

class OverviewDashboardService {
  constructor(private summaryDashboardService: SummaryDashboardService) {}

  public async addItem({
    initiativeId,
    creatorId,
    item,
  }: {
    initiativeId: ObjectId;
    creatorId: ObjectId;
    item: InsightDashboardItem;
  }) {
    const dashboard = await this.summaryDashboardService.findOrCreate({
      initiativeId,
      creatorId,
      type: InsightDashboardType.Overview,
    });

    const isExisted = dashboard.items.some(({ _id }) => _id.equals(item._id));
    if (isExisted) {
      throw new UserError('This chart already has been added');
    }

    dashboard.items = [...dashboard.items, item];

    return dashboard.save();
  }

  public async removeItem({ initiativeId, itemId }: { initiativeId: ObjectId; itemId: ObjectId }) {
    const dashboard = await InsightDashboard.findOne({ initiativeId, type: InsightDashboardType.Overview })
      .orFail(new UserError('Overview dashboard not found', { initiativeId }))
      .exec();

    const filteredItems = dashboard.toObject().items.filter(({ _id }: { _id: ObjectId }) => !_id.equals(itemId));
    dashboard.items = this.recalculateItemsOrdinate(filteredItems);

    return dashboard.save();
  }

  private recalculateItemsOrdinate(items: InsightDashboardItem[]) {
    return items.reduce((calculatedItems, item) => {
      const ordinate = getOrdinate(item.gridSize.w, [
        ...(overview.items as InsightDashboardItem[]),
        ...calculatedItems,
      ]);
      const calculatedItem = { ...item, gridSize: { ...item.gridSize, ...ordinate } };
      calculatedItems.push(calculatedItem);

      return calculatedItems;
    }, [] as InsightDashboardItem[]);
  }
}

let instance: OverviewDashboardService;
export const getOverviewDashboardService = () => {
  if (!instance) {
    instance = new OverviewDashboardService(getSummaryDashboardService());
  }

  return instance;
};
