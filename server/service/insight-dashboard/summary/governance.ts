import { ObjectId } from 'bson';
import {
  CalculationType,
  ChartSubType,
  InsightDashboardItemType,
  InsightDashboardType,
  StaticDashboard,
  ValueRole,
  getDefaultFilters,
} from '../../../models/insightDashboard';

export const governance = {
  title: 'Governance',
  type: InsightDashboardType.Governance,
  filters: getDefaultFilters(),
  items: [
    {
      title: 'Employee compensation',
      variables: {
        a: {
          code: 'gri/2020/102-38',
          valueListCode: 'denominator_value',
        },
        b: {
          code: 'gri/2020/102-38',
          valueListCode: 'numerator_value',
        },
      },
      gridSize: {
        x: 0,
        y: 3,
        w: 4,
        h: 12,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Bar,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Highest paid',
            formula: '{a}',
            options: {
              style: '#2871C2',
              tooltip: {
                formula: 'Highest paid',
              },
            },
          },
          {
            name: 'Employee median',
            formula: '{b}',
            options: {
              style: '#32D2D9',
              tooltip: {
                formula: 'Employee median',
              },
            },
          },
        ],
        headers: [
          {
            name: 'Gender',
          },
          {
            name: 'Employees',
          },
          {
            role: ValueRole.Annotation,
          },
          {
            role: ValueRole.Style,
          },
        ],
      },
      note: {
        prefix: 'Highest paid earns',
        value: '{a} / {b}',
        valueUnit: 'times',
        postfix: 'more than Employees',
      },
      _id: new ObjectId('650184d82f6863ea713abe4a'),
    },
    {
      title: 'Board diversity',
      variables: {
        a: {
          code: 'gri/2020/405-1/a',
          valueListCode: 'male',
        },
        b: {
          code: 'gri/2020/405-1/a',
          valueListCode: 'female',
        },
      },
      gridSize: {
        x: 4,
        y: 3,
        w: 4,
        h: 12,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.PieWithLegend,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Male',
            formula: '{a}',
          },
          {
            name: 'Female',
            formula: '{b}',
          },
        ],
      },
      _id: new ObjectId('650188da2f6863ea713abf29'),
    },
    {
      title: '% women on the board',
      variables: {
        a: {
          code: 'gri/2020/405-1/a',
          valueListCode: 'female',
        },
      },
      gridSize: {
        x: 8,
        y: 3,
        w: 4,
        h: 12,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.SingleValue,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: '% women on the board',
            formula: '{a}',
          },
        ],
      },
      unitText: '%',
      _id: new ObjectId('65018e196adb10f824662e42'),
    },
    {
      gridSize: {
        x: 0,
        y: 15,
        w: 12,
        h: 3,
        minW: 4,
        minH: 3,
      },
      type: InsightDashboardItemType.Headline,
      text: 'Diversity and compensation',
      _id: new ObjectId('65018f716adb10f824662f7a'),
    },
    {
      title: 'Employee compensation v highest paid',
      variables: {
        a: {
          code: 'gri/2020/102-38',
          valueListCode: 'denominator_value',
        },
        b: {
          code: 'gri/2020/102-38',
          valueListCode: 'numerator_value',
        },
      },
      gridSize: {
        x: 0,
        y: 18,
        w: 6,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Line,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Highest paid',
            formula: '{a}',
          },
          {
            name: 'Average pay',
            formula: '{b}',
          },
        ],
      },
      unitText: 'USD Millions',
      _id: new ObjectId('65018f866adb10f824662fc9'),
    },
    {
      title: 'Senior staff % bonuses based on ESG targets',
      variables: {
        a: {
          code: 'survey/sdg/4.7/pc-bonuses-based-on-ees-topics',
        },
      },
      gridSize: {
        x: 6,
        y: 18,
        w: 6,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Column,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: '% of bonuses',
            formula: '{a}',
          },
        ],
      },
      _id: new ObjectId('650191496adb10f8246630d2'),
    },
    {
      title: 'Board diversity: gender split',
      variables: {
        a: {
          code: 'gri/2020/405-1/a',
          valueListCode: 'male',
        },
        b: {
          code: 'gri/2020/405-1/a',
          valueListCode: 'female',
        },
      },
      gridSize: {
        x: 0,
        y: 35,
        w: 12,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Column,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Male',
            formula: '{a}',
          },
          {
            name: 'Male',
            formula: '{a}',
            role: ValueRole.Annotation,
          },
          {
            name: 'Female',
            formula: '{b}',
          },
          {
            name: 'Female',
            formula: '{b}',
            role: ValueRole.Annotation,
          },
        ],
      },
      unitText: '%',
      _id: new ObjectId('6501922d6adb10f82466321b'),
    },
    {
      gridSize: {
        x: 0,
        y: 52,
        w: 12,
        h: 3,
        minW: 4,
        minH: 3,
      },
      type: InsightDashboardItemType.Headline,
      text: 'Corruption training and regulations',
      _id: new ObjectId('650194146adb10f82466347e'),
    },
    {
      title: '% of board with anti-corruption training',
      variables: {
        a: {
          code: 'gri/2020/205-2/d',
          valueListCode: 'board_members_region',
        },
        b: {
          code: 'gri/2020/205-2/d',
          valueListCode: 'anti_corruption_training_number',
        },
      },
      gridSize: {
        x: 0,
        y: 55,
        w: 6,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Column,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Board with Anti-Corruption Training',
            formula: '100 * {b} / {a}',
          },
        ],
      },
      unitText: '%',
      _id: new ObjectId('650194236adb10f8246634c6'),
    },
    {
      title: '% operations in breach of financial regulation',
      variables: {
        a: {
          code: 'survey/sdg/10.5/pc-operations-break-financial-regulations',
        },
      },
      gridSize: {
        x: 6,
        y: 55,
        w: 6,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Column,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: '% Operations in Breach',
            formula: '{a}',
          },
        ],
      },
      _id: new ObjectId('6501943a6adb10f824663553'),
    },
    {
      gridSize: {
        x: 0,
        y: 72,
        w: 12,
        h: 3,
        minW: 4,
        minH: 3,
      },
      type: InsightDashboardItemType.Headline,
      text: 'Policies and best practice',
      subType: ChartSubType.Line,
      _id: new ObjectId('6503c12ea0682c560d61e1e9'),
    },
    {
      title: '',
      variables: {
        a: {
          code: 'survey/sdg/10.3/discrimination-policy',
        },
        b: {
          code: 'survey/sdg/3.4/pc-investment-inform-public-risks-product',
        },
        c: {
          code: 'survey/sdg/16.4/illicit-flows',
        },
        d: {
          code: 'survey/generic/does-company-report-sustainable-development-risks',
        },
        e: {
          code: 'survey/sdg/4.7/pc-bonuses-based-on-ees-topics',
        },
        f: {
          code: 'survey/sdg/10.7/human-trafficking',
        },
      },
      gridSize: {
        x: 0,
        y: 75,
        w: 12,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Table,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Diversity policy',
            formula: "compare({a}, 'eq', 1)",
          },
          {
            name: 'Sustainability reporting',
            formula: "compare({d}, 'eq', 1)",
          },
          {
            name: 'Mental Health policy',
            formula: "compare({b}, 'eq', 100)",
          },
          {
            name: 'Bonuses linked to Sustainability',
            formula: "compare({e}, 'gt', 0)",
          },
          {
            name: 'Bribery/Corruption policy',
            formula: "compare({c}, 'eq', 1)",
          },
          {
            name: 'Human Trafficking Supplier policy',
            formula: "compare({f}, 'eq', 1)",
          },
        ],
        headers: [
          {
            name: 'Policy/Best practice',
          },
          {
            name: 'Status',
          },
        ],
      },
      _id: new ObjectId('6503c3a5a0682c560d61e29f'),
    },
    {
      gridSize: {
        x: 0,
        y: 0,
        w: 12,
        h: 3,
        minW: 4,
        minH: 3,
      },
      type: InsightDashboardItemType.Headline,
      text: 'Highlights',
      _id: new ObjectId('6507fdf0a8125b0fe1ef0815'),
    },
  ],
} satisfies StaticDashboard;
