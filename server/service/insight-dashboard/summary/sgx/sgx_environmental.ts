import { ObjectId } from 'bson';
import {
  CalculationType,
  ChartSubType,
  InsightDashboardItemType,
  InsightDashboardType,
  StaticDashboard,
  ValueRole,
  getDefaultFilters,
} from '../../../../models/insightDashboard';

export const sgxEnvironmental = {
  title: 'SGX Environmental',
  type: InsightDashboardType.SgxEnvironmental,
  filters: getDefaultFilters(),
  items: [
    {
      gridSize: {
        x: 0,
        y: 0,
        w: 12,
        h: 3,
        minW: 4,
        minH: 3,
      },
      type: InsightDashboardItemType.Headline,
      text: 'Highlights',
      _id: new ObjectId('65717ebb2b8d682aa1e8d4b8'),
    },
    {
      title: 'Absolute GHG emissions by scope (%)',
      variables: {
        a: {
          code: 'gri/2020/305-1/a',
        },
        b: {
          code: 'gri/2020/305-2/a',
        },
        c: {
          code: 'gri/2020/305-2/b',
        },
        d: {
          code: 'gri/2020/305-3/a',
        },
      },
      gridSize: {
        x: 0,
        y: 3,
        w: 4,
        h: 12,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Pie,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Scope 1',
            formula: '{a}',
          },
          {
            name: 'Scope 2',
            formula: '{b} + {c}',
          },
          {
            name: 'Scope 3',
            formula: '{d}',
          },
        ],
      },
      _id: new ObjectId('65717eec2b8d682aa1e8d4da'),
    },
    {
      title: 'Total energy consumption',
      variables: {
        a: {
          code: 'gri/2020/302-1/e',
        },
      },
      gridSize: {
        x: 4,
        y: 3,
        w: 4,
        h: 12,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.SingleValue,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Total energy consumption',
            formula: '{a}',
          },
        ],
      },
      _id: new ObjectId('6571825b2b8d682aa1e8da2d'),
    },
    {
      title: 'Total water consumption',
      variables: {
        a: {
          code: 'gri/2020/303-5/a',
        },
      },
      gridSize: {
        x: 8,
        y: 3,
        w: 4,
        h: 12,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.SingleValue,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Total water consumption',
            formula: '{a}',
          },
        ],
      },
      _id: new ObjectId('657182712b8d682aa1e8da50'),
    },
    {
      gridSize: {
        x: 0,
        y: 15,
        w: 12,
        h: 3,
        minW: 4,
        minH: 3,
      },
      type: InsightDashboardItemType.Headline,
      text: 'Greenhouse gas emissions',
      _id: new ObjectId('657183f52b8d682aa1e8db04'),
    },
    {
      title: 'Total absolute emissions over time',
      variables: {
        a: {
          code: 'gri/2020/305-1/a',
        },
        b: {
          code: 'gri/2020/305-2/a',
        },
        c: {
          code: 'gri/2020/305-2/b',
        },
        d: {
          code: 'gri/2020/305-3/a',
        },
      },
      gridSize: {
        x: 0,
        y: 18,
        w: 6,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Line,
      calculation: {
        type: CalculationType.Sum,
        values: [
          {
            name: 'Emissions',
          },
        ],
      },
      _id: new ObjectId('657184a62b8d682aa1e8db64'),
    },
    {
      title: 'Absolute emissions by scope over time',
      variables: {
        a: {
          code: 'gri/2020/305-1/a',
        },
        b: {
          code: 'gri/2020/305-2/a',
        },
        c: {
          code: 'gri/2020/305-2/b',
        },
        d: {
          code: 'gri/2020/305-3/a',
        },
      },
      gridSize: {
        x: 6,
        y: 18,
        w: 6,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Stacked,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Scope 1',
            formula: '{a}',
          },
          {
            name: 'Scope 1',
            formula: '{a}',
            role: ValueRole.Annotation,
          },
          {
            name: 'Scope 2',
            formula: '{b}+{c}',
          },
          {
            name: 'Scope 2',
            formula: '{b}+{c}',
            role: ValueRole.Annotation,
          },
          {
            name: 'Scope 3',
            formula: '{d}',
          },
          {
            name: 'Scope 3',
            formula: '{d}',
            role: ValueRole.Annotation,
          },
        ],
      },
      _id: new ObjectId('657185652b8d682aa1e8dc43'),
    },
    {
      gridSize: {
        x: 0,
        y: 35,
        w: 12,
        h: 3,
        minW: 4,
        minH: 3,
      },
      type: InsightDashboardItemType.Headline,
      text: 'Energy and water consumption',
      _id: new ObjectId('6572d296241361940b8eb41f'),
    },
    {
      title: 'Total energy consumption over time',
      variables: {
        a: {
          code: 'gri/2020/302-1/e',
        },
      },
      gridSize: {
        x: 0,
        y: 38,
        w: 6,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Line,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Consumption',
            formula: '{a}',
          },
        ],
      },
      _id: new ObjectId('6572d2ae241361940b8eb460'),
    },
    {
      title: 'Total water consumption over time',
      variables: {
        a: {
          code: 'gri/2020/303-5/a',
        },
      },
      gridSize: {
        x: 6,
        y: 38,
        w: 6,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Line,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Consumption',
            formula: '{a}',
          },
        ],
      },
      _id: new ObjectId('6572d731241361940b8eb5d9'),
    },
    {
      gridSize: {
        x: 0,
        y: 55,
        w: 12,
        h: 3,
        minW: 4,
        minH: 3,
      },
      type: InsightDashboardItemType.Headline,
      text: 'Waste',
      _id: new ObjectId('6572d7cf241361940b8eb690'),
    },
    {
      title: 'Hazardous and non-hazardous waste',
      variables: {
        a: {
          code: 'gri/2020/306-3/a',
          valueListCode: 'hazardous_waste',
        },
        b: {
          code: 'gri/2020/306-3/a',
          valueListCode: 'non_hazardous',
        },
      },
      gridSize: {
        x: 0,
        y: 58,
        w: 12,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Stacked,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Hazardous',
            formula: '{a}',
          },
          {
            name: 'Hazardous',
            formula: '{a}',
            role: ValueRole.Annotation,
          },
          {
            name: 'Non-Hazardous',
            formula: '{b}',
          },
          {
            name: 'Hazardous',
            formula: '{b}',
            role: ValueRole.Annotation,
          },
        ],
      },
      unitText: 'Metric Tonnes',
      _id: new ObjectId('6572d7e8241361940b8eb6da'),
    },
  ],
} satisfies StaticDashboard;
