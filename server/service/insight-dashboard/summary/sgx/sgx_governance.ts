import { ObjectId } from 'bson';
import {
  CalculationType,
  ChartSubType,
  InsightDashboardItemType,
  InsightDashboardType,
  StaticDashboard,
  ValueRole,
  getDefaultFilters,
} from '../../../../models/insightDashboard';

export const sgxGovernance = {
  title: 'SGX Governance',
  filters: getDefaultFilters(),
  type: InsightDashboardType.SgxGovernance,
  items: [
    {
      gridSize: {
        x: 0,
        y: 0,
        w: 12,
        h: 3,
        minW: 4,
        minH: 3,
      },
      type: InsightDashboardItemType.Headline,
      text: 'Highlights',
      subType: ChartSubType.Line,
      _id: new ObjectId('65771a4cdb0d673ae640b5fd'),
    },
    {
      title: 'Board diversity',
      variables: {
        a: {
          code: 'gri/2020/405-1/a',
          valueListCode: 'male',
        },
        b: {
          code: 'gri/2020/405-1/a',
          valueListCode: 'female',
        },
      },
      gridSize: {
        x: 0,
        y: 3,
        w: 4,
        h: 12,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.PieWithLegend,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Male',
            formula: '{a}',
          },
          {
            name: 'Female',
            formula: '{b}',
          },
        ],
      },
      _id: new ObjectId('65771b27db0d673ae640b62d'),
    },
    {
      title: '% of independent board directors',
      variables: {
        a: {
          code: 'sgx-custom-42',
          valueListCode: 'sgx42-percentage',
        },
      },
      gridSize: {
        x: 4,
        y: 3,
        w: 4,
        h: 12,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.SingleValue,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: '% of independent board directors',
            formula: '{a}',
          },
        ],
      },
      _id: new ObjectId('65771bf5db0d673ae640b728'),
    },
    {
      title: 'No. of confirmed corruption incidents',
      variables: {
        a: {
          code: 'gri/2020/205-3/a',
          valueListCode: 'corruption_incidents_number',
        },
      },
      gridSize: {
        x: 8,
        y: 3,
        w: 4,
        h: 12,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.SingleValue,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'No. of confirmed corruption incidents',
            formula: '{a}',
          },
        ],
      },
      _id: new ObjectId('65771cc5db0d673ae640b7e7'),
    },
    {
      title: 'Board diversity: gender split',
      variables: {
        a: {
          code: 'gri/2020/405-1/a',
          valueListCode: 'male',
        },
        b: {
          code: 'gri/2020/405-1/a',
          valueListCode: 'female',
        },
      },
      gridSize: {
        x: 0,
        y: 15,
        w: 12,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Column,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Male',
            formula: '{a}',
          },
          {
            name: 'Male',
            formula: '{a}',
            role: ValueRole.Annotation,
          },
          {
            name: 'Female',
            formula: '{b}',
          },
          {
            name: 'Female',
            formula: '{b}',
            role: ValueRole.Annotation,
          },
        ],
      },
      unitText: '%',
      _id: new ObjectId('65771d83db0d673ae640b94d'),
    },
    {
      title: 'Total volunteering hours',
      variables: {
        a: {
          code: 'survey/generic/volunteering-hours',
        },
      },
      gridSize: {
        x: 0,
        y: 32,
        w: 12,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Line,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Total volunteering hours',
            formula: '{a}',
          },
        ],
      },
      _id: new ObjectId('65771e88db0d673ae640bada'),
    },
    {
      title: 'Total charitable donations',
      variables: {
        a: {
          code: 'unctad/2020/a.3.2',
          valueListCode: 'total_charity',
        },
      },
      gridSize: {
        x: 0,
        y: 49,
        w: 12,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Line,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Total charitable donations',
            formula: '{a}',
          },
        ],
      },
      _id: new ObjectId('65772117db0d673ae640bee0'),
    },
  ],
} satisfies StaticDashboard;
