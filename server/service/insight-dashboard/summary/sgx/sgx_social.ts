import { ObjectId } from 'bson';
import {
  CalculationType,
  ChartSubType,
  getDefaultFilters,
  InsightDashboardItemType,
  InsightDashboardType,
  StaticDashboard, Value,
  ValueRole,
} from '../../../../models/insightDashboard';
import { employeeGenderDiversity405Pie, femaleCalculationTotal, maleCalculationTotal } from "../../calculations";

const maleTotalTurnover401Stage = {
  name: 'Male',
  stages: [
    {
      input: { context: 'utrv', variable: 'a' },
      formula: '$sum(data[gender.value="male1"].($number(total_turnover.value)))',
      type: 'jsonata',
      output: 'maleTotal',
    },
    {
      input: { context: 'output' },
      type: 'jsonata',
      formula: 'maleTotal',
      output: 'result',
      decimals: 2,
    },
  ],
} satisfies Value;

const femaleTotalTurnover401Stage = {
  name: 'Female',
  stages: [
    {
      input: { context: 'utrv', variable: 'a' },
      formula: '$sum(data[gender.value="females1"].($number(total_turnover.value)))',
      type: 'jsonata',
      output: 'femaleTotal',
    },
    {
      input: { context: 'output' },
      type: 'jsonata',
      formula: 'femaleTotal',
      output: 'result',
      decimals: 2,
    },
  ]
} satisfies Value;

export const sgxSocial = {
  title: 'SGX Social',
  type: InsightDashboardType.SgxSocial,
  filters: getDefaultFilters(),
  items: [
    {
      gridSize: {
        x: 0,
        y: 0,
        w: 12,
        h: 3,
        minW: 4,
        minH: 3,
      },
      type: InsightDashboardItemType.Headline,
      text: 'Highlights',
      _id: new ObjectId('6572dc511f441c3154dab904'),
    },
    {
      ...employeeGenderDiversity405Pie,
      gridSize: {
        x: 0,
        y: 3,
        w: 4,
        h: 12,
        minW: 4,
        minH: 12,
      },
    },
    {
      title: 'Employee age diversity',
      variables: {
        a: {
          code: 'gri/2020/405-1/b',
          valueListCode: 'under_30',
        },
        b: {
          code: 'gri/2020/405-1/b',
          valueListCode: 'age_30_50',
        },
        c: {
          code: 'gri/2020/405-1/b',
          valueListCode: 'over_50',
        },
      },
      gridSize: {
        x: 4,
        y: 3,
        w: 4,
        h: 12,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Pie,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Under 30',
            formula: '{a}',
          },
          {
            name: '30 to 50',
            formula: '{b}',
          },
          {
            name: 'Over 50',
            formula: '{c}',
          },
        ],
      },
      _id: new ObjectId('6572dd781f441c3154daba66'),
    },
    {
      title: 'Average training hours by gender',
      variables: {
        a: {
          code: 'gri/2020/404-1/a',
          valueListCode: 'male',
        },
        b: {
          code: 'gri/2020/404-1/a',
          valueListCode: 'female',
        },
      },
      gridSize: {
        x: 8,
        y: 3,
        w: 4,
        h: 12,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Bar,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Male',
            formula: '{a}',
            options: {
              style: '#2871C2',
              tooltip: {
                formula: 'Male | {a}',
              },
            },
          },
          {
            name: 'Female',
            formula: '{b}',
            options: {
              style: '#32D2D9',
              tooltip: {
                formula: 'Female | {b}',
              },
            },
          },
        ],
        headers: [
          {
            name: 'Gender',
          },
          {
            name: 'Employees',
          },
          {
            role: ValueRole.Annotation,
          },
          {
            role: ValueRole.Style,
          },
        ],
      },
      _id: new ObjectId('6572ded61f441c3154dabb30'),
    },
    {
      title: 'Total employee turnover',
      variables: {
        a: {
          code: 'gri/2020/401-1/b',
          valueListCode: 'total_turnover',
        },
      },
      gridSize: {
        x: 0,
        y: 15,
        w: 4,
        h: 12,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.SingleValue,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Total employee turnover',
            formula: '{a}',
          },
        ],
      },
      _id: new ObjectId('6572dfd71f441c3154dabc04'),
    },
    {
      title: 'Employee turnover by gender',
      variables: {
        a: { code: 'gri/2020/401-1/b' },
      },
      gridSize: {
        x: 4,
        y: 15,
        w: 4,
        h: 12,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Pie,
      isDefault: true,
      calculation: {
        type: CalculationType.Stages,
        values: [
          maleTotalTurnover401Stage,
          femaleTotalTurnover401Stage,
        ]
      },
      _id: new ObjectId('6572e0711f441c3154dabce2'),
    },
    {
      title: 'Employee turnover by age',
      variables: {
        a: {
          code: 'gri/2020/401-1/b',
          valueListCode: 'under_30',
        },
        b: {
          code: 'gri/2020/401-1/b',
          valueListCode: 'age_30_50',
        },
        c: {
          code: 'gri/2020/401-1/b',
          valueListCode: 'over_50',
        },
      },
      gridSize: {
        x: 8,
        y: 15,
        w: 4,
        h: 12,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Pie,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Under 30',
            formula: '{a}',
          },
          {
            name: '30 to 50',
            formula: '{b}',
          },
          {
            name: 'Over 50',
            formula: '{c}',
          },
        ],
      },
      _id: new ObjectId('6572e70b1f441c3154dabdf5'),
    },
    {
      gridSize: {
        x: 0,
        y: 27,
        w: 12,
        h: 3,
        minW: 4,
        minH: 3,
      },
      type: InsightDashboardItemType.Headline,
      text: 'Diversity',
      _id: new ObjectId('6572e8321f441c3154dabed6'),
    },
    {
      title: 'Gender diversity over time',
      variables: {
        a: { code: 'gri/2020/405-1/b' },
      },
      gridSize: {
        x: 0,
        y: 30,
        w: 6,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Stacked,
      isDefault: true,
      calculation: {
        type: CalculationType.Stages,
        values: [
          {
            name: 'Male',
            stages: maleCalculationTotal
          },
          {
            name: 'Male',
            stages: [
              {
                input: { context: 'output' },
                type: 'jsonata',
                formula: 'malePc',
                output: 'malePc',
                decimals: 2,
              },
            ],
            role: ValueRole.Annotation,
          },
          {
            name: 'Female',
            stages: femaleCalculationTotal,
          },
          {
            name: 'Female',
            stages: [
              {
                input: { context: 'output' },
                type: 'jsonata',
                formula: 'femalePc',
                output: 'femalePc',
                decimals: 2,
              },
            ],
            role: ValueRole.Annotation,
          },
        ],
      },
      _id: new ObjectId('6572e84d1f441c3154dabf57'),
    },
    {
      title: 'Age diversity over time',
      variables: {
        a: {
          code: 'gri/2020/405-1/b',
          valueListCode: 'under_30',
        },
        b: {
          code: 'gri/2020/405-1/b',
          valueListCode: 'age_30_50',
        },
        c: {
          code: 'gri/2020/405-1/b',
          valueListCode: 'over_50',
        },
      },
      gridSize: {
        x: 6,
        y: 30,
        w: 6,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Stacked,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Under 30',
            formula: '{a}',
          },
          {
            name: 'Under 30',
            formula: '{a}',
            role: ValueRole.Annotation,
          },
          {
            name: '30 to 50',
            formula: '{b}',
          },
          {
            name: '30 to 50',
            formula: '{b}',
            role: ValueRole.Annotation,
          },
          {
            name: 'Over 50',
            formula: '{c}',
          },
          {
            name: 'Over 50',
            formula: '{c}',
            role: ValueRole.Annotation,
          },
        ],
      },
      _id: new ObjectId('6572e9691f441c3154dac05d'),
    },
    {
      gridSize: {
        x: 0,
        y: 47,
        w: 12,
        h: 3,
        minW: 4,
        minH: 3,
      },
      type: InsightDashboardItemType.Headline,
      text: 'Employee turnover',
      _id: new ObjectId('6572f2981f441c3154dac15a'),
    },
    {
      title: 'Employee turnover by gender',
      variables: {
        a: { code: 'gri/2020/401-1/b' },
      },
      gridSize: {
        x: 0,
        y: 50,
        w: 6,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Stacked,
      // Using stages, disable add functionality
      isDefault: true,
      calculation: {
        type: CalculationType.Stages,
        headers: [
          { name: 'Male', },
          { name: 'Female', },
          { role: ValueRole.Annotation, },
          { role: ValueRole.Style, },
        ],
        values: [
          maleTotalTurnover401Stage,
          {
            name: 'Male',
            stages: [
              {
                input: { context: 'output' },
                type: 'jsonata',
                // Re-use previous calculated data
                formula: 'maleTotal',
                output: 'result',
                decimals: 2,
              }
            ],
            role: ValueRole.Annotation,
          },
          femaleTotalTurnover401Stage,
          {
            name: 'Female',
            stages: [
              {
                input: { context: 'output' },
                type: 'jsonata',
                // Re-use previous calculated data
                formula: 'femaleTotal',
                output: 'result',
                decimals: 2,
              },
            ],
            role: ValueRole.Annotation,
          },
        ],
      },
      _id: new ObjectId('6572f2dd1f441c3154dac1aa'),
    },
    {
      title: 'Employee turnover by age group',
      variables: {
        a: {
          code: 'gri/2020/401-1/b',
          valueListCode: 'under_30',
        },
        b: {
          code: 'gri/2020/401-1/b',
          valueListCode: 'age_30_50',
        },
        c: {
          code: 'gri/2020/401-1/b',
          valueListCode: 'over_50',
        },
      },
      gridSize: {
        x: 6,
        y: 50,
        w: 6,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Stacked,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Under 30',
            formula: '{a}',
          },
          {
            name: 'Under 30',
            formula: '{a}',
            role: ValueRole.Annotation,
          },
          {
            name: '30 to 50',
            formula: '{b}',
          },
          {
            name: '30 to 50',
            formula: '{b}',
            role: ValueRole.Annotation,
          },
          {
            name: 'Over 50',
            formula: '{c}',
          },
          {
            name: 'Over 50',
            formula: '{c}',
            role: ValueRole.Annotation,
          },
        ],
      },
      _id: new ObjectId('6572f2f11f441c3154dac1ee'),
    },
  ],
} satisfies StaticDashboard;
