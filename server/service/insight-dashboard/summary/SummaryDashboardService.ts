import { ObjectId } from 'bson';
import { InsightDashboard, InsightDashboardModel, InsightDashboardPlain } from '../../../models/insightDashboard';
import { InitiativePlain } from '../../../models/initiative';
import { DashboardItemManager, getDashboardItemManager } from '../DashboardItemManager';
import {
  getDashboardByAppConfigCode,
  getDefaultSummaryDashboard,
  PreloadQueryOptions,
  StaticDashboardType
} from '../utils';
import { UserPlain } from "../../../models/user";

interface FindOrCreateData {
  initiativeId: ObjectId;
  creatorId: ObjectId;
  type: StaticDashboardType;
}

interface GetSummaryDashboardParams {
  user: Pick<UserPlain, '_id'>,
  type: StaticDashboardType;
  initiative: InitiativePlain;
  filters: PreloadQueryOptions;
}

export class SummaryDashboardService {
  constructor(private dashboardItemManager: DashboardItemManager) {
  }

  public async findOrCreate({ initiativeId, creatorId, type }: FindOrCreateData) {
    const dashboard = await InsightDashboard.findOne({ initiativeId, type }).exec();
    if (dashboard) {
      return dashboard;
    }

    const summaryDashboard = getDefaultSummaryDashboard(type);

    return InsightDashboard.create({
      creatorId,
      initiativeId,
      type,
      title: summaryDashboard.title,
      filters: summaryDashboard.filters,
    });
  }

  public async getSummaryDashboard({ user, type, initiative, filters }: GetSummaryDashboardParams) {
    const dashboard = getDashboardByAppConfigCode({
      type: type,
      initiativeId: initiative._id,
      userId: user._id,
      appConfigCode: initiative.appConfigCode
    });

    const summary = await InsightDashboard.findOne({ initiativeId: initiative._id, type: type }).lean().exec() as InsightDashboardPlain;

    const summaryDashboard = summary
      ? { ...summary, items: [...dashboard.items, ...summary.items] }
      : dashboard;

    return this.dashboardItemManager.populateData({
      dashboard: summaryDashboard,
      initiative,
      filters
    });
  }

  public async updateSummaryDashboard(
    dashboard: InsightDashboardModel,
    updateProps: Pick<InsightDashboardPlain, 'filters'>
  ) {
    const changes = { filters: updateProps.filters };
    dashboard.set(changes);
    return dashboard.save();
  }
}

let instance: SummaryDashboardService;
export const getSummaryDashboardService = () => {
  if (!instance) {
    instance = new SummaryDashboardService(getDashboardItemManager());
  }

  return instance;
};
