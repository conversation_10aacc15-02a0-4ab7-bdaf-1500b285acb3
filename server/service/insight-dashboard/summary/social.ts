import { ObjectId } from 'bson';
import {
  CalculationType,
  ChartSubType,
  InsightDashboardItemType,
  InsightDashboardType,
  StaticDashboard,
  ValueRole,
  getDefaultFilters,
} from '../../../models/insightDashboard';

export const social = {
  title: 'Social',
  type: InsightDashboardType.Social,
  filters: getDefaultFilters(),
  items: [
    {
      gridSize: {
        x: 0,
        y: 0,
        w: 12,
        h: 3,
        minW: 4,
        minH: 3,
      },
      type: InsightDashboardItemType.Headline,
      text: 'Highlights',
      _id: new ObjectId('64fe6f492833ec2d2838a1be'),
    },
    {
      title: 'Employee numbers',
      variables: {
        a: {
          code: 'gri/2020/102-7/a',
          valueListCode: 'org_scale_employees_male',
        },
        b: {
          code: 'gri/2020/102-7/a',
          valueListCode: 'org_scale_employees_female',
        },
      },
      gridSize: {
        x: 0,
        y: 3,
        w: 4,
        h: 12,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Bar,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Male',
            formula: '{a}',
            options: {
              tooltip: {
                formula: 'Male | {a}',
              },
              style: '#2871C2',
            },
          },
          {
            name: 'Female',
            formula: '{b}',
            options: {
              tooltip: {
                formula: 'Female | {b}',
              },
              style: '#32D2D9',
            },
          },
        ],
        headers: [
          {
            name: 'Gender',
          },
          {
            name: 'Employees',
          },
          {
            role: ValueRole.Annotation,
          },
          {
            role: ValueRole.Style,
          },
        ],
      },
      _id: new ObjectId('64fe6f792833ec2d2838a1e5'),
    },
    {
      title: 'Employee gender split',
      variables: {
        a: {
          code: 'gri/2020/102-7/a',
          valueListCode: 'org_scale_employees_male',
        },
        b: {
          code: 'gri/2020/102-7/a',
          valueListCode: 'org_scale_employees_female',
        },
      },
      gridSize: {
        x: 4,
        y: 3,
        w: 4,
        h: 12,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Pie,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Male',
            formula: '{a}',
          },
          {
            name: 'Female',
            formula: '{b}',
          },
        ],
      },
      _id: new ObjectId('64fe76fa64479aa67009c800'),
    },
    {
      title: 'Employee turnover ratio',
      variables: {
        a: {
          code: 'survey/sdg/5.1/ratio-employee-turnover',
          valueListCode: 'ratio_turnover',
          groupCode: 'ctl',
          subGroupCode: 'ctl-social',
        },
      },
      gridSize: {
        x: 8,
        y: 3,
        w: 4,
        h: 12,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.SingleValue,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Value',
            formula: '{a}',
            decimalPlaces: 2,
          },
        ],
      },
      note: {
        prefix: 'Ratio of employee turnover rate for women to men',
      },
      _id: new ObjectId('64fe78af64479aa67009c946'),
    },
    {
      gridSize: {
        x: 0,
        y: 15,
        w: 12,
        h: 3,
        minW: 4,
        minH: 3,
      },
      type: InsightDashboardItemType.Headline,
      text: 'Gender',
      _id: new ObjectId('64fe80ffc83c724c7b33c837'),
    },
    {
      title: 'Average female pay v male pay',
      variables: {
        a: {
          code: 'gri/2020/405-2/a',
          valueListCode: 'denominator_male',
        },
        b: {
          code: 'gri/2020/405-2/a',
          valueListCode: 'numerator_female',
        },
      },
      gridSize: {
        x: 0,
        y: 18,
        w: 6,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Column,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Male',
            formula: '{a}',
          },
          {
            name: 'Male',
            formula: '{a}',
            role: ValueRole.Annotation,
          },
          {
            name: 'Female',
            formula: '{b}',
          },
          {
            name: 'Female',
            formula: '{b}',
            role: ValueRole.Annotation,
          },
        ],
      },
      unitText: 'USD Millions',
      _id: new ObjectId('64fe817cc83c724c7b33c892'),
    },
    {
      title: 'Gender: average hours of training',
      variables: {
        a: {
          code: 'gri/2020/404-1/a',
          valueListCode: 'male',
        },
        b: {
          code: 'gri/2020/404-1/a',
          valueListCode: 'female',
        },
      },
      gridSize: {
        x: 6,
        y: 18,
        w: 6,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Column,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Male',
            formula: '{a}',
          },
          {
            name: 'Male',
            formula: '{a}',
            role: ValueRole.Annotation,
          },
          {
            name: 'Female',
            formula: '{b}',
          },
          {
            name: 'Female',
            formula: '{b}',
            role: ValueRole.Annotation,
          },
        ],
      },
      unitText: 'Hours',
      _id: new ObjectId('64fe848dc83c724c7b33cc03'),
    },
    {
      title: 'Employee gender split',
      variables: {
        a: {
          code: 'gri/2020/102-7/a',
          valueListCode: 'org_scale_employees_male',
        },
        b: {
          code: 'gri/2020/102-7/a',
          valueListCode: 'org_scale_employees_female',
        },
      },
      gridSize: {
        x: 0,
        y: 35,
        w: 6,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Column,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Male',
            formula: '{a}',
          },
          {
            name: 'Male',
            formula: '{a}',
            role: ValueRole.Annotation,
          },
          {
            name: 'Female',
            formula: '{b}',
          },
          {
            name: 'Female',
            formula: '{b}',
            role: ValueRole.Annotation,
          },
        ],
      },
      _id: new ObjectId('64fe8ad8c83c724c7b33cf68'),
    },
    {
      title: 'Turnover by gender',
      variables: {
        a: {
          code: 'survey/sdg/5.1/ratio-employee-turnover',
          valueListCode: 'ratio_turnover',
        },
      },
      gridSize: {
        x: 6,
        y: 35,
        w: 6,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Line,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Ratio',
            formula: '{a}',
          },
        ],
      },
      _id: new ObjectId('64fe8e2bc83c724c7b33d0e7'),
    },
    {
      gridSize: {
        x: 0,
        y: 52,
        w: 12,
        h: 3,
        minW: 4,
        minH: 3,
      },
      type: InsightDashboardItemType.Headline,
      text: 'Wages and health',
      _id: new ObjectId('64feb9204d19ffe06a22bc01'),
    },
    {
      title: '% workforce paid the national living wage',
      variables: {
        a: {
          code: 'survey/generic/pc-company-workforce-paid-national-living-wage',
        },
      },
      gridSize: {
        x: 0,
        y: 55,
        w: 6,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Column,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Avg Male Pay',
            formula: '{a}',
          },
          {
            name: 'Avg Male Pay',
            formula: '{a}',
            role: ValueRole.Annotation,
          },
        ],
      },
      _id: new ObjectId('64feb9424d19ffe06a22bc56'),
    },
    {
      title: 'Expenditure on employee H&S as a % of revenue',
      variables: {
        a: {
          code: 'unctad/2020/c.3.1',
          valueListCode: 'percent_expenditure_hs',
        },
      },
      gridSize: {
        x: 6,
        y: 55,
        w: 6,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Line,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Expenditure Percentage',
            formula: '{a}',
          },
        ],
      },
      _id: new ObjectId('64feba7c4d19ffe06a22bf3e'),
    },
  ],
} satisfies StaticDashboard;
