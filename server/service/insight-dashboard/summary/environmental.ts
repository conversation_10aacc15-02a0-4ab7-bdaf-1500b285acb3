import { ObjectId } from 'bson';
import {
  CalculationType,
  ChartSubType,
  InsightDashboardItemType,
  InsightDashboardType,
  StaticDashboard,
  ValueRole,
  getDefaultFilters,
} from '../../../models/insightDashboard';

export const environmental = {
  title: 'Environmental',
  type: InsightDashboardType.Environmental,
  filters: getDefaultFilters(),
  items: [
    {
      title: 'Greenhouse gas emissions',
      variables: {
        a: {
          code: 'gri/2020/305-1/a',
          groupCode: 'ctl',
          subGroupCode: 'ctl-environment',
        },
        b: {
          code: 'gri/2020/305-2/a',
        },
        c: {
          code: 'gri/2020/305-2/b',
        },
        d: {
          code: 'gri/2020/305-3/a',
        },
      },
      gridSize: {
        x: 0,
        y: 3,
        w: 4,
        h: 12,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.SingleValue,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Greenhouse gas emissions',
            formula: '{a} + {b} + {c} + {d}',
          },
        ],
      },
      _id: new ObjectId('64e72492b9e37cb093acc3cc'),
    },
    {
      gridSize: {
        x: 0,
        y: 0,
        w: 12,
        h: 3,
        minW: 4,
        minH: 3,
      },
      type: InsightDashboardItemType.Headline,
      text: 'Highlights',
      _id: new ObjectId('64e724c7b9e37cb093acc40f'),
    },
    {
      title: 'Green investment',
      variables: {
        a: {
          code: 'unctad/2020/a.3.1',
          valueListCode: 'pc_envt_exp',
          groupCode: 'ctl',
          subGroupCode: 'ctl-environment',
        },
      },
      gridSize: {
        x: 8,
        y: 3,
        w: 4,
        h: 12,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.SingleValue,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Green investment',
            formula: '{a}',
          },
        ],
      },
      unitText: '%',
      note: {
        prefix: 'of capex',
      },
      _id: new ObjectId('64cb2929230ca514bbff02bd'),
    },
    {
      gridSize: {
        x: 0,
        y: 15,
        w: 12,
        h: 3,
        minW: 4,
        minH: 3,
      },
      type: InsightDashboardItemType.Headline,
      text: 'Emissions',
      _id: new ObjectId('64ec7949e5dd71c7aa2de075'),
    },
    {
      title: '% emissions by scope',
      variables: {
        a: {
          code: 'gri/2020/305-1/a',
        },
        b: {
          code: 'gri/2020/305-2/a',
        },
        c: {
          code: 'gri/2020/305-2/b',
        },
        d: {
          code: 'gri/2020/305-3/a',
        },
      },
      gridSize: {
        x: 4,
        y: 3,
        w: 4,
        h: 12,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Pie,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Scope 1',
            formula: '{a}',
          },
          {
            name: 'Scope 2',
            formula: '{b} + {c}',
          },
          {
            name: 'Scope 3',
            formula: '{d}',
          },
        ],
      },
      _id: new ObjectId('64ec8236e5dd71c7aa2df848'),
    },
    {
      title: 'Total emissions over time',
      variables: {
        a: {
          code: 'gri/2020/305-1/a',
        },
        b: {
          code: 'gri/2020/305-2/a',
        },
        c: {
          code: 'gri/2020/305-2/b',
        },
        d: {
          code: 'gri/2020/305-3/a',
        },
      },
      gridSize: {
        x: 0,
        y: 18,
        w: 6,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Line,
      calculation: {
        type: CalculationType.Sum,
        values: [
          {
            name: 'Emissions',
          },
        ],
      },
      _id: new ObjectId('64efef1422e378210c8bdc34'),
    },
    {
      title: '% emissions by scope',
      variables: {
        a: {
          code: 'gri/2020/305-1/a',
        },
        b: {
          code: 'gri/2020/305-2/a',
        },
        c: {
          code: 'gri/2020/305-2/b',
        },
        d: {
          code: 'gri/2020/305-3/a',
        },
      },
      gridSize: {
        x: 6,
        y: 18,
        w: 6,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Line,
      calculation: {
        type: CalculationType.Percentage,
        values: [
          {
            name: 'Scope 1',
            formula: '{a}',
          },
          {
            name: 'Scope 2',
            formula: '{b} + {c}',
          },
          {
            name: 'Scope 3',
            formula: '{d}',
          },
        ],
      },
      _id: new ObjectId('64f040e8e757b54c28ac8c27'),
    },
    {
      gridSize: {
        x: 0,
        y: 35,
        w: 12,
        h: 3,
        minW: 4,
        minH: 3,
      },
      type: InsightDashboardItemType.Headline,
      text: 'Energy use',
      _id: new ObjectId('64f04c8db45fd29379c9dd45'),
    },
    {
      title: 'Paris agreement energy target: progress',
      variables: {
        a: {
          code: 'survey/generic/paris-aligned-targets',
          valueListCode: 'production_target',
        },
      },
      gridSize: {
        x: 0,
        y: 38,
        w: 6,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Line,
      calculation: {
        type: CalculationType.Sum,
        values: [
          {
            name: 'Aligned',
          },
        ],
      },
      unitText: '%',
      _id: new ObjectId('64f04dfcb45fd29379c9ddf3'),
    },
    {
      title: '% capex spent on sustainable energy',
      variables: {
        a: {
          code: 'survey/sdg/11.1/pc-investment-energy-renewable-energy',
        },
      },
      gridSize: {
        x: 6,
        y: 38,
        w: 6,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Column,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Aligned',
            formula: '{a}',
          },
          {
            name: 'Annotation',
            formula: '{a}',
            role: ValueRole.Annotation,
          },
        ],
      },
      _id: new ObjectId('64f06b44f0b9b749e5e244d5'),
    },
    {
      gridSize: {
        x: 0,
        y: 55,
        w: 12,
        h: 3,
        minW: 4,
        minH: 3,
      },
      type: InsightDashboardItemType.Headline,
      text: 'Waste',
      _id: new ObjectId('64f073468c5542f7b6948f67'),
    },
    {
      title: '% plastic waste recycled',
      variables: {
        a: {
          code: 'survey/sdg/14.1/pc-plastic-waste-recycled',
          valueListCode: 'plastic_recycled',
        },
        b: {
          code: 'survey/sdg/14.1/pc-plastic-waste-recycled',
          valueListCode: 'plastic_waste',
        },
      },
      gridSize: {
        x: 0,
        y: 58,
        w: 6,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Line,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Recycled',
            formula: '100 * {a} / {b}',
          },
        ],
      },
      unitText: '%',
      _id: new ObjectId('64f93ee93924b2162123ed33'),
    },
    {
      title: 'Hazardous and non-hazardous',
      variables: {
        a: {
          code: 'gri/2020/306-3/a',
          valueListCode: 'hazardous_waste',
        },
        b: {
          code: 'gri/2020/306-3/a',
          valueListCode: 'non_hazardous',
        },
      },
      gridSize: {
        x: 6,
        y: 58,
        w: 6,
        h: 17,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.Column,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Hazardous',
            formula: '{a}',
          },
          {
            name: 'Hazardous',
            formula: '{a}',
            role: ValueRole.Annotation,
          },
          {
            name: 'Non-Hazardous',
            formula: '{b}',
          },
          {
            name: 'Hazardous',
            formula: '{b}',
            role: ValueRole.Annotation,
          },
        ],
      },
      unitText: 'Metric Tonnes',
      _id: new ObjectId('64f97d7106dfb19e17bb429b'),
    },
  ],
} satisfies StaticDashboard;
