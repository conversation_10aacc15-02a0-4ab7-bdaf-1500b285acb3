import { ObjectId } from 'bson';
import {
  CalculationType,
  ChartSubType,
  InsightDashboardItemType,
  InsightDashboardType,
  StaticDashboard,
  getDefaultFilters,
} from '../../../models/insightDashboard';
import { employeeGenderDiversity405Pie } from "../calculations";

export const overview = {
  title: 'Overview',
  type: InsightDashboardType.Overview,
  filters: getDefaultFilters(),
  items: [
    {
      title: 'Greenhouse gas emissions',
      variables: {
        a: {
          code: 'gri/2020/305-1/a',
        },
        b: {
          code: 'gri/2020/305-2/a',
        },
        c: {
          code: 'gri/2020/305-2/b',
        },
        d: {
          code: 'gri/2020/305-3/a',
        },
      },
      gridSize: {
        x: 0,
        y: 0,
        w: 4,
        h: 12,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.SingleValue,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Greenhouse gas emissions',
            formula: '{a} + {b} + {c} + {d}',
          },
        ],
      },
      _id: new ObjectId('64e72492b9e37cb093acc3cc'),
      isDefault: true,
    },
    {
      ...employeeGenderDiversity405Pie,
      gridSize: {
        x: 4,
        y: 0,
        w: 4,
        h: 12,
        minW: 4,
        minH: 12,
      },
      _id: new ObjectId('6508195fbf4772ad4bcef68f'),
      isDefault: true,
    },
    {
      title: 'Anti-corruption policy communicated to',
      variables: {
        a: {
          code: 'gri/2020/205-2/b',
          valueListCode: 'number',
        },
        b: {
          code: 'gri/2020/205-2/b',
          valueListCode: 'total_number_employees',
        },
      },
      gridSize: {
        x: 8,
        y: 0,
        w: 4,
        h: 12,
        minW: 4,
        minH: 12,
      },
      type: InsightDashboardItemType.Chart,
      subType: ChartSubType.SingleValue,
      calculation: {
        type: CalculationType.Formula,
        values: [
          {
            name: 'Value',
            formula: '100 * {a} / {b}',
            decimalPlaces: 1,
          },
        ],
      },
      unitText: '%',
      note: {
        prefix: 'of employees',
      },
      _id: new ObjectId('6508196ebf4772ad4bcef6cb'),
      isDefault: true,
    },
  ],
} satisfies StaticDashboard;
