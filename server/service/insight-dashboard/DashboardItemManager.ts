import { ObjectId } from 'bson';
import { defaultLayout } from './constants';
import {
  ExtendedDashboard,
  GridSize,
  InsightDashboardItem,
  InsightDashboardItemCreate,
  InsightDashboardItemType,
  InsightDashboardModel,
  InsightDashboardPlain,
  InsightDashboardType,
  SurveyFilter,
  UtrVariables,
} from '../../models/insightDashboard';
import UserError from '../../error/UserError';
import ScorecardFactory from '../scorecard/ScorecardFactory';
import { InitiativePlain } from '../../models/initiative';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import { getUtrvFiltersFromDashboardFilters, PreloadQueryOptions, shouldLoadScorecard } from './utils';
import { DocumentService, getDocumentService } from '../file/DocumentService';
import { getIntegrationManager } from '../integration/IntegrationManager';
import ContextError from '../../error/ContextError';
import { IntegrationData } from '../integration/IntegrationProvider';
import { UniversalTrackerRepository } from '../../repository/UniversalTrackerRepository';
import { isDefaultFetching } from '../../util/insight-dashboard';
import {
  getUniversalTrackerHistoricalDataManager,
  UniversalTrackerHistoricalDataManager,
} from '../utr/historical-data/UniversalTrackerHistoricalDataManager';

const MAXIMUM_LENGTH = 10;
const ALPHABET = 'abcdefghijklmnopqrstuvwxyz';

export type PopulateDataParams = {
  dashboard: InsightDashboardPlain;
  initiative: InitiativePlain | undefined;
  filters: PreloadQueryOptions;
};

type DashboardItems = Pick<InsightDashboardPlain, 'items'>;

export class DashboardItemManager {
  constructor(
    private readonly logger: LoggerInterface,
    private documentService: DocumentService,
    private integrationManager: ReturnType<typeof getIntegrationManager>,
    private historicalDataManager: UniversalTrackerHistoricalDataManager
  ) {}

  private getDefaultGridLayout(items: InsightDashboardItem[], item: InsightDashboardItemCreate): GridSize {
    let y = defaultLayout.y;
    if (item.gridSize.y ?? items.length > 0) {
      const max = items.reduce((acc, current) => {
        return acc.gridSize.y > current.gridSize.y ? acc : current;
      }, items[0]);
      y = max.gridSize.y + max.gridSize.h;
    }
    return {
      ...defaultLayout,
      x: item.gridSize.x || defaultLayout.x,
      y,
      w: item.gridSize.w || defaultLayout.w,
      h: item.gridSize.h || defaultLayout.h,
      minW: item.gridSize.minW || defaultLayout.minW,
      minH: item.gridSize.minH || defaultLayout.minH,
      ...(Boolean(item.gridSize.maxH) && { maxH: item.gridSize.maxH }),
    };
  }

  private async transformVariables(variables: InsightDashboardItemCreate['utrVariables']) {
    if (!variables) {
      return {};
    }

    if (variables.length > MAXIMUM_LENGTH) {
      throw new UserError('Maximum number of variable items reached');
    }
    const keys = ALPHABET.split('');

    return variables.reduce((acc, variable, index) => {
      acc[keys[index]] = {
        code: variable.code,
        valueListCode: variable.valueListCode,
        groupCode: variable.groupCode,
        subGroupCode: variable.subGroupCode,
      };
      return acc;
    }, {} as UtrVariables);
  }

  public async addDashboardItem({
    dashboard,
    item,
  }: {
    dashboard: InsightDashboardModel;
    item: InsightDashboardItemCreate;
  }) {
    const defaultGridLayout = this.getDefaultGridLayout(dashboard.items, item);

    // utrVariables is to support old logic, will be convert to item.variables in the future refactor
    const variables = item.utrVariables ? await this.transformVariables(item.utrVariables) : item.variables;

    const addItem: InsightDashboardItem = {
      _id: new ObjectId(),
      title: item.title,
      gridSize: defaultGridLayout,
      variables,
      type: item.type,
      text: item.text,
      subType: item.subType,
      icon: item.icon,
      calculation: item.calculation,
      config: item.config,
    };
    dashboard.items = [...dashboard.items, addItem];
    await dashboard.save();
    return addItem;
  }

  /**
   * Populate data based on the items and filters
   */
  public async populateData({ dashboard, initiative, filters }: PopulateDataParams) {
    const [utrsData, dashboardWithFiles, scorecard, integrationsData] = await Promise.all([
      this.getUtrData(dashboard, initiative?._id, filters, dashboard.filters.initiativeIds),
      this.populateFiles(dashboard),
      this.getScorecard(dashboard, initiative),
      // This will modify integration items in place by attaching logo
      this.getIntegrationsData(dashboard, filters).catch((error) => {
        this.logger.error(
          new ContextError('Failed to get integrations data', {
            dashboardId: dashboard._id,
            initiativeId: dashboard.initiativeId,
            cause: error,
          })
        );
        return undefined;
      }),
    ]);

    return {
      _id: dashboard._id,
      type: dashboard.type ?? InsightDashboardType.Custom,
      creatorId: dashboard.creatorId,
      initiativeId: dashboard.initiativeId,
      title: dashboard.title,
      filters: dashboard.filters,
      items: dashboardWithFiles.items,
      share: dashboard.share,
      utrsData,
      scorecard,
      integrationsData,
    } satisfies ExtendedDashboard;
  }

  private async getUtrData(
    dashboard: InsightDashboardPlain,
    initiativeId: ObjectId | string | undefined,
    filters: PreloadQueryOptions,
    initiativeIds?: ObjectId[]
  ) {
    const utrCodes = Array.from(
      dashboard.items.reduce((acc, item) => {
        if (item.variables) {
          Object.values(item.variables).forEach((variable) => acc.add(variable.code));
        }

        return acc;
      }, new Set<string>())
    );

    if (utrCodes.length === 0) {
      return [];
    }

    const universalTrackers = await UniversalTrackerRepository.populateValueValidationByCodes(utrCodes);
    if (universalTrackers.length === 0) {
      return [];
    }

    const utrIds = universalTrackers.map((utr) => String(utr._id));
    const utrvFilters = getUtrvFiltersFromDashboardFilters({ filters: dashboard.filters, additionalFilters: filters });

    const now = Date.now();
    const dashboardId = dashboard._id.toString();
    const inputInitiativeId = initiativeId || dashboard.initiativeId;

    // Load the utr data based on the number of subsidiaries
    // initiativeIds only contains the initiativeId of the current dashboard then it is a default fetch
    // otherwise it is a disaggregation data fetch
    if (!initiativeIds || isDefaultFetching({ initiativeIds, initiativeId: dashboard.initiativeId })) {
      const utrsData = await this.historicalDataManager.getUtrsHistoricalData({
        initiativeId: inputInitiativeId,
        utrIds,
        utrvFilters,
      });
      const totalTime = Date.now() - now;
      wwgLogger.info(`Processed dashboard ${dashboardId} loading. Took ${totalTime}ms`, {
        totalTime,
        dashboardId: dashboardId,
        initiativeId: inputInitiativeId.toString(),
        ...utrvFilters,
        utrCount: utrIds.length,
      });
      return utrsData;
    }

    const utrsData = await this.historicalDataManager.getSubsidiariesUtrsData({
      initiativeId: inputInitiativeId,
      initiativeIds,
      utrIds,
      utrvFilters,
    });
    const totalTime = Date.now() - now;
    wwgLogger.info(`Processed aggregated dashboard ${dashboardId} loading. Took ${totalTime}ms`, {
      totalTime,
      dashboardId,
      initiativeId: inputInitiativeId,
      initiativeIds: initiativeIds.map((id) => id.toString()),
      ...utrvFilters,
      utrCount: utrIds.length,
    });
      return utrsData;
  }

  private async getScorecard(dashboard: InsightDashboardPlain, initiative: InitiativePlain | undefined) {
    if (!initiative || !shouldLoadScorecard(dashboard)) {
      return undefined;
    }

    const isCompletedData = dashboard.filters.survey === SurveyFilter.Completed;
    const scorecardFactory = new ScorecardFactory();

    return {
      scorecard: await scorecardFactory.getByInitiative(initiative, { isCompletedData }),
      initiative,
    };
  }

  public async populateFiles<T extends DashboardItems = DashboardItems>(dashboard: T): Promise<T> {
    const { items } = dashboard;

    const documentIds = items.reduce((documentIds, item) => {
      if (item.type !== InsightDashboardItemType.Media) {
        return documentIds;
      }

      return [...documentIds, ...(item.files?.map((file) => file.documentId) || [])];
    }, [] as ObjectId[]);

    const documentsMap = await this.documentService.getDocumentsMapByIds(documentIds);

    const itemsWithFiles = items.map((item) => {
      if (item.type !== InsightDashboardItemType.Media) {
        return item;
      }

      return {
        ...item,
        files: item.files?.map((file) => {
          const document = documentsMap.get(file.documentId.toString());
          return {
            ...file,
            url: document?.url ?? '',
            type: document?.metadata?.mimetype ?? '',
            name: document?.metadata?.name ?? '',
          };
        }),
      };
    });

    return { ...dashboard, items: itemsWithFiles };
  }

  /**
   * Will override items logo property if needed
   */
  private async getIntegrationsData(
    dashboard: InsightDashboardPlain,
    filters: PreloadQueryOptions
  ): Promise<IntegrationData | undefined> {
    const integrationItems = dashboard.items.filter((item) => item.type === InsightDashboardItemType.Integration);

    const providerLookup = this.createProviderMap(integrationItems);
    if (providerLookup.size === 0) {
      return undefined;
    }

    // This might need to be top level root initiative id?
    const initiativeId = dashboard.initiativeId;

    const integrationData = this.integrationManager.getData({
      initiativeId,
      providerUtrCodesMap: providerLookup,
      filters,
    });

    return integrationData;
  }

  /**
   * This does modify icon property to avoid doing multiple loops.
   * That might be something we should change without sacrificing readability or performance
   */
  private createProviderMap(integrationItems: InsightDashboardItem[]) {
    const providerLookup = new Map<string, Set<string>>();

    integrationItems.forEach((item) => {
      if (!item.variables) {
        return;
      }

      Object.values(item.variables).forEach((variable) => {
        if (variable.integrationCode) {
          // populate icon from Integration provider
          const provider = this.integrationManager.getProvider(variable.integrationCode);
          item.icon = provider?.getInfo()?.icon;

          const providerUtrCodes = providerLookup.get(variable.integrationCode);
          if (providerUtrCodes) {
            providerUtrCodes.add(variable.code);
          } else {
            providerLookup.set(variable.integrationCode, new Set([variable.code]));
          }
        }
      });
    });

    return providerLookup;
  }
}

let instance: DashboardItemManager;
export const getDashboardItemManager = () => {
  if (!instance) {
    instance = new DashboardItemManager(
      wwgLogger,
      getDocumentService(),
      getIntegrationManager(),
      getUniversalTrackerHistoricalDataManager(),
    );
  }

  return instance;
};
