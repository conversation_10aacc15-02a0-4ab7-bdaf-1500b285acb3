/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { ValueStages } from "../aggregate/stages/ValueStage";
import { CalculationType, ChartSubType, InsightDashboardItemType } from "../../models/insightDashboard";
import { ObjectId } from "bson";

export const maleCalculationTotal = [
  {
    input: { context: 'utrv', variable: 'a' },
    formula: '$sum(data.($number(employees_per_category.value) / 100 * $number(male.value)))',
    type: 'jsonata',
    output: 'maleTotal',
  },
  {
    input: { context: 'utrv', variable: 'a' },
    formula: '$sum(data.($number(employees_per_category.value)))',
    type: 'jsonata',
    output: 'combinedTotal',
  },
  {
    input: { context: 'output' },
    type: 'jsonata',
    formula: 'maleTotal / combinedTotal * 100',
    output: 'malePc',
    decimals: 2,
  },
] satisfies ValueStages;

export const femaleCalculationTotal = [
  {
    input: { context: 'utrv', variable: 'a' },
    formula: '$sum(data.($number(employees_per_category.value) / 100 * $number(female.value)))',
    type: 'jsonata',
    output: 'femaleTotal',
  },
  {
    input: { context: 'utrv', variable: 'a' },
    formula: '$sum(data.($number(employees_per_category.value)))',
    type: 'jsonata',
    output: 'femaleCombinedTotal',
  },
  {
    input: { context: 'output' },
    type: 'jsonata',
    formula: 'femaleTotal / femaleCombinedTotal * 100',
    output: 'femalePc',
    decimals: 2,
  },
] satisfies ValueStages;

export const employeeGenderDiversity405Pie = {
  title: 'Employee gender diversity',
  variables: {
    a: { code: 'gri/2020/405-1/b' },
  },
  type: InsightDashboardItemType.Chart,
  subType: ChartSubType.Pie,
  // Using stages, disable add functionality
  isDefault: true,
  calculation: {
    type: CalculationType.Stages,
    values: [
      {
        name: 'Male',
        stages: maleCalculationTotal
      },
      {
        name: 'Female',
        stages: femaleCalculationTotal
      },
    ],
  },
  _id: new ObjectId('65632c6c9c8245d864bf310e'),
};
