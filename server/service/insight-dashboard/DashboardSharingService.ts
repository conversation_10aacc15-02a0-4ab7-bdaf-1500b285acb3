import { ObjectId } from 'bson';
import { InitiativeRepository } from '../../repository/InitiativeRepository';
import PermissionDeniedError from '../../error/PermissionDeniedError';
import {
  INSIGHT_TEMPLATE_DASHBOARD_TYPES,
  InsightDashboard,
  InsightDashboardModel,
  InsightDashboardPlain,
  SHARING_TOKEN_LENGTH,
  SharingItem,
} from '../../models/insightDashboard';
import { generateRandomToken } from '../crypto/token';
import { getDashboardItemManager } from './DashboardItemManager';
import { InitiativePlainWithRoot } from '../../models/initiative';
import { getSummaryDashboardItems, isESGDashboard, PreloadQueryOptions, shouldLoadInitiative } from './utils';
import { getDashboardTemplatesService } from './DashboardTemplatesService';

type SafeInitiativeFields = Pick<
  InitiativePlainWithRoot,
  '_id' | 'profile' | 'name' | 'sectorText' | 'industryText' | 'description' | 'missionStatement'
>;

type GetShareDashboard = { dashboardId: string; filters: PreloadQueryOptions } & Pick<SharingItem, 'token'>;

export class DashboardSharingService {
  constructor(
    private dashboardItemManager: ReturnType<typeof getDashboardItemManager>,
    private dashboardTemplatesService: ReturnType<typeof getDashboardTemplatesService>
  ) {}
  public findSharedDashboard({ dashboardId, token }: Pick<GetShareDashboard, 'dashboardId' | 'token'>) {
    return InsightDashboard.findOne(
      {
        _id: dashboardId,
        share: { $elemMatch: { token, enabled: true } },
      },
      { share: 0 }
    )
      .orFail(new PermissionDeniedError('This dashboard is private or not found'))
      .lean<InsightDashboardPlain>()
      .exec();
  }

  public async getSharedDashboard({ dashboardId, token, filters }: GetShareDashboard) {
    const dashboard = await this.findSharedDashboard({ dashboardId, token });

    if (isESGDashboard(dashboard.type)) {
      dashboard.items = getSummaryDashboardItems(dashboard.type);
    }

    const initiative = shouldLoadInitiative(dashboard)
      ? await InitiativeRepository.getInitiativeById(dashboard.initiativeId)
      : undefined;

    const params = { dashboard, initiative, filters };
    return {
      dashboard: INSIGHT_TEMPLATE_DASHBOARD_TYPES.includes(dashboard.type)
        ? await this.dashboardTemplatesService.populateDashboardData(params)
        : await this.dashboardItemManager.populateData(params),
      initiative: this.toSafeInitiativeFields(dashboard, initiative),
    };
  }

  async shareDashboard({ dashboard, enabled }: { dashboard: InsightDashboardModel } & Pick<SharingItem, 'enabled'>) {
    const sharingItem = dashboard.share?.[0] ?? this.createSharingItem({ enabled });

    sharingItem.enabled = enabled;
    dashboard.share = [sharingItem]; // currently, only 1 item but to be expanded later.
    await dashboard.save();

    return dashboard.share;
  }

  private createSharingItem({ enabled }: Pick<SharingItem, 'enabled'>): SharingItem {
    return {
      enabled,
      token: generateRandomToken(SHARING_TOKEN_LENGTH),
    };
  }

  public async getSharedParentDashboardsById(initiativeId: ObjectId) {
    const [initiative] = await InitiativeRepository.getAllParentsById(initiativeId);

    if (!initiative || !initiative.parents.length) {
      return [];
    }

    const parentIds = initiative.parents.map((parent) => parent._id);

    const sharedDashboards = await InsightDashboard.find({
      initiativeId: { $in: parentIds },
      'filters.shareWithSubsidiaries.enabled': { $eq: true },
    })
      .lean()
      .exec();

    return sharedDashboards;
  }

  private toSafeInitiativeFields(
    dashboard: Pick<InsightDashboardPlain, 'filters'>,
    initiative: SafeInitiativeFields | undefined
  ) {
    if (dashboard.filters.initiativeInfo?.enabled && initiative) {
      return {
        _id: initiative._id,
        profile: initiative.profile,
        name: initiative.name,
        sectorText: initiative.sectorText,
        industryText: initiative.industryText,
        description: initiative.description,
        missionStatement: initiative.missionStatement,
      } satisfies SafeInitiativeFields;
    }
    return undefined;
  }
}

let instance: DashboardSharingService;
export const getDashboardSharingService = () => {
  if (!instance) {
    instance = new DashboardSharingService(getDashboardItemManager(), getDashboardTemplatesService());
  }

  return instance;
};
