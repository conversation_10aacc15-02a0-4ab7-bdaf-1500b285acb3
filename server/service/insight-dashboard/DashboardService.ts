import { ObjectId } from 'bson';
import { Filters, InsightDashboard, InsightDashboardItem, InsightDashboardItemType, InsightDashboardModel } from '../../models/insightDashboard';
import { UserModel } from '../../models/user';
import { DashboardMediaFilesManager, getDashboardMediaFilesManager } from './DashboardMediaFilesManager';
import { getResultsFromPromiseAllSettled } from '../../util/promise';
import { Logger } from 'winston';
import { wwgLogger } from '../wwgLogger';
import UserError from '../../error/UserError';
import ContextError from '../../error/ContextError';
import { DashboardDocumentCleanupParams, isSDGContributionChartType } from '../../service/insight-dashboard/utils';

export class DashboardService {
  constructor(
    private insightDashboardModel: typeof InsightDashboard,
    private dashboardMediaFilesManager: DashboardMediaFilesManager,
    private logger: Logger
  ) {}
  duplicate(
    dashboard: Pick<InsightDashboardModel, 'initiativeId' | 'title' | 'filters' | 'items'>,
    creatorId: UserModel['_id']
  ) {
    const { initiativeId, title, filters, items } = dashboard;
    return this.insightDashboardModel.create({
      creatorId,
      initiativeId,
      title: `(copy) ${title}`,
      filters,
      items,
    });
  }

  async update(dashboard: InsightDashboardModel, updates: {
    title: string;
    filters: Partial<Pick<Filters, "sdgContribution">>;
    items?: InsightDashboardItem<string>[];
    deletedDocumentIds?: string[];
  }) {
    const { title, filters, items = [], deletedDocumentIds = [] } = updates;
    const dashboardDocumentIds = dashboard.items
      .flatMap((item) => item.files)
      .map((file) => file?.documentId?.toString())
      .filter(Boolean);

    const invalidDocumentIds = deletedDocumentIds.filter((id) => !dashboardDocumentIds.includes(id));

    if (invalidDocumentIds.length > 0) {
      throw new ContextError('Invalid document IDs', {
        invalidDocumentIds,
        dashboardDocumentIds,
      });
    }
    const changes = { title, ...this.processSDGContributionSetting({ dashboard, filters, items }) };
    dashboard.set(changes);
    await dashboard.save();

    this.cleanDocumentsAfterUpdating({
      dashboardId: dashboard._id,
      initiativeId: dashboard.initiativeId,
      deletedDashboardDocumentIds: deletedDocumentIds,
    }).catch(this.logger.error);

    return dashboard;
  }

  private processSDGContributionSetting({
    dashboard,
    filters,
    items,
  }: {
    dashboard: InsightDashboardModel;
    filters: Partial<Pick<Filters, 'sdgContribution'>>;
    items: InsightDashboardItem<string>[];
  }) {
    const isPreviousEnabled = dashboard.filters.sdgContribution?.enabled;
    const isNowEnabled = filters.sdgContribution?.enabled;

    if (!isPreviousEnabled && !isNowEnabled) {
      return { filters, items };
    }

    // toggle sdgContribution chart on
    if (!isPreviousEnabled && isNowEnabled) {
      const sdgContributionChartItems = items.filter((item) => isSDGContributionChartType(item));
      if (sdgContributionChartItems.length === 0) {
        throw new ContextError('Missing 1 SDG Contribution chart item');
      }
      if (sdgContributionChartItems.length > 1) {
        throw new ContextError('Creating more than 1 SDG Contribution chart item is not allowed');
      }
    }

    let updatedItems = items;
    // when sdgContribution is toggled off, remove the sdgContribution chart item
    if (isPreviousEnabled && !isNowEnabled) {
      updatedItems = items.filter((item) => !isSDGContributionChartType(item));
    }
    // when remove the sdgContribution chart item, toggle sdgContribution off
    const existingSDGContributionItem = dashboard.items.some((item) => isSDGContributionChartType(item));
    const isNowRemoved = items.every((item) => !isSDGContributionChartType(item));

    if (existingSDGContributionItem && isNowRemoved && filters.sdgContribution) {
      filters.sdgContribution.enabled = false;
    }
    return { filters, items: updatedItems };
  }

  private async cleanDocumentsAfterUpdating({
    dashboardId,
    initiativeId,
    deletedDashboardDocumentIds,
  }: DashboardDocumentCleanupParams) {
    if (!deletedDashboardDocumentIds.length) {
      return;
    }

    const results = await this.dashboardMediaFilesManager.deleteFilesAndReassignOwnership({
      dashboardId,
      initiativeId,
      deletedDashboardDocumentIds,
    });
    const { fulfilled, rejected } = getResultsFromPromiseAllSettled(results);

    this.logger.info(`Delete files when updating dashboard ${dashboardId}`, fulfilled);
    if (rejected.length > 0) {
      this.logger.error(`Cannot delete files when updating dashboard ${dashboardId}`, rejected);
    }
  }

  async delete(dashboardId: ObjectId) {
    const result = await InsightDashboard.findOneAndDelete({ _id: dashboardId }).orFail().lean().exec();

    const deletedDashboardDocumentIds = result.items.reduce<string[]>((acc, { type, files = [] }) => {
      if (type === InsightDashboardItemType.Media) {
        acc.push(...files.map(({ documentId }) => documentId.toString()));
      }
      return acc;
    }, []);

    this.logger.warn(`Cleaning up deleted dashboard ${dashboardId} documents`, { deletedDashboardDocumentIds });
    this.cleanDocumentsAfterDeleting({
      dashboardId,
      initiativeId: result.initiativeId,
      deletedDashboardDocumentIds,
    }).catch(this.logger.error);

    return result;
  }

  private async cleanDocumentsAfterDeleting({
    dashboardId,
    initiativeId,
    deletedDashboardDocumentIds,
  }: DashboardDocumentCleanupParams) {
    const results = await this.dashboardMediaFilesManager.deleteFilesAndReassignOwnership({
      dashboardId,
      initiativeId,
      deletedDashboardDocumentIds,
    });
    const { fulfilled, rejected } = getResultsFromPromiseAllSettled(results);

    this.logger.info(`Delete files when deleting dashboard ${dashboardId}`, fulfilled);
    if (rejected.length > 0) {
      this.logger.error(`Cannot delete files when deleting dashboard ${dashboardId}`, rejected);
    }
  }

  async uploadFiles(
    dashboardId: ObjectId,
    userId: ObjectId,
    files:
      | Express.Multer.File[]
      | {
          [fieldname: string]: Express.Multer.File[];
        }
      | undefined
  ) {
    if (!Array.isArray(files) || files.length === 0) {
      throw new UserError('There are no file to be uploaded');
    }

    const results = await this.dashboardMediaFilesManager.uploadFiles({
      files,
      userId,
      dashboardId,
    });

    const { fulfilled, rejected } = getResultsFromPromiseAllSettled(results);

    if (rejected.length > 0) {
      wwgLogger.error(`Cannot upload files for dashboard ${dashboardId}`, rejected);
    }

    return fulfilled;
  }
}

let instance: DashboardService;
export const getDashboardService = () => {
  if (!instance) {
    const dashboardMediaFilesManager = getDashboardMediaFilesManager();
    instance = new DashboardService(InsightDashboard, dashboardMediaFilesManager, wwgLogger);
  }

  return instance;
};
