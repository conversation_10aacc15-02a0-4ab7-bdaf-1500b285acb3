import { InsightsData, PortfolioRepository, SubmissionInsightsQuery, SubmissionInsightsView, UtrvInsightData } from '../../repository/PortfolioRepository';
import { RequesterType } from '../../models/dataShare';
import { InitiativePlain } from '../../models/initiative';
import { ActiveDataShare, DataShareRepository, getDataShareRepository } from '../../repository/DataShareRepository';
import { ExtendedInitiativePlain, PortfolioService } from './PortfolioService';
import { ObjectId } from 'bson';
import { PortfolioPackUsageService, ScopePackRowData } from './PortfolioPackUsageService';
import { RequestScope } from '../survey/model/DelegationScope';
import { SurveyScope } from '../survey/SurveyScope';
import { DelegationScope } from '../../models/survey';
import { getPreferredTypeCode } from '../assurance/csvContext';

interface DisplayUtrValues {
  utrvs?: UtrvInsightData[];
  name: string;
  initiativeId?: string;
}

export class PortfolioSubmissionInsightsService {
  constructor(private dataShareRepo: DataShareRepository) {}

  public async getPacksWithCounts({
    portfolio,
    startDate,
    endDate,
    activeDataShares,
  }: {
    portfolio: InitiativePlain;
    startDate: Date | undefined;
    endDate: Date | undefined;
    activeDataShares: ActiveDataShare[];
  }) {
    // Empty means all scopes
    const scope = SurveyScope.createEmpty<string>();
    const customMetricGroups = await PortfolioService.getPortfolioMetricGroups(portfolio, scope.custom);
    const companiesByScope = await PortfolioService.getSharedScopesCompanies({
      requestedScope: { scope },
      activeDataShares,
    });

    const scopePackData: ScopePackRowData[] = [];
    for (const grouping of Object.values(companiesByScope)) {
      const packs = await PortfolioPackUsageService.getScopeBreakdown({
        initiativeIds: grouping.initiativeIds,
        validatedScope: grouping.validatedScope,
        customMetricGroups,
        startDate: startDate?.toString(),
        endDate: endDate?.toString(),
      });
      scopePackData.push(...packs.packs);
    }

    const packs = scopePackData.reduce((acc, currentValue) => {
      const current = acc.get(currentValue.code);
      if (current) {
        current.surveyCount += currentValue.surveyCount;
      } else {
        acc.set(currentValue.code, currentValue);
      }
      return acc;
    }, new Map<string, ScopePackRowData>());

    return Array.from(packs.values());
  }

  public async getMostUsedPack({
    portfolio,
    startDate,
    endDate,
    activeDataShares,
  }: {
    portfolio: InitiativePlain;
    startDate: Date | undefined;
    endDate: Date | undefined;
    activeDataShares: ActiveDataShare[];
  }): Promise<RequestScope> {
    const packsWithCounts = await this.getPacksWithCounts({
      portfolio,
      startDate,
      endDate,
      activeDataShares,
    });
    if (packsWithCounts.length <= 0) {
      // Default is SDG Impact
      return { code: 'ctl', scopeType: 'frameworks' as keyof DelegationScope };
    }
    const sortedPacks = packsWithCounts.sort((a, b) => {
      if (a.surveyCount !== b.surveyCount) {
        return b.surveyCount - a.surveyCount;
      }
      if (a.code < b.code) {
        return -1;
      }
      if (a.code > b.code) {
        return 1;
      }
      return 0;
    });
    return {
      code: sortedPacks[0].code,
      scopeType: sortedPacks[0].scopeType,
    };
  }

  public getDisplayUtrValues = ({
    companies,
    view,
    utrValues,
  }: {
    companies: Map<string, ExtendedInitiativePlain>;
    view: SubmissionInsightsView;
    utrValues: InsightsData[];
  }): DisplayUtrValues[] => {
    if (view === SubmissionInsightsView.Company) {
      return Array.from(companies.values()).map((company) => {
        const companyId = String(company._id);
        const utrvValue = utrValues.find((utrv) => company._id.equals(utrv._id));
        const otherProps = {
          name: PortfolioService.getDisplayNameByView(view, companyId, companies),
          initiativeId: companyId,
          ...PortfolioService.getIndustryClassificationsText(view, companyId, companies),
        };

        return utrvValue
          ? {
              ...otherProps,
              utrvs: utrvValue.utrvs,
            }
          : {
              ...otherProps,
              utrvs: []
            };
      });
    }

    return utrValues.map(({ _id, ...utr }) => ({
      ...utr,
      name: PortfolioService.getDisplayNameByView(view, _id, companies),
    }));
  };

  public async getSubmissionInsightsData({
    portfolio,
    filters,
    maxRecursion,
  }: {
    portfolio: InitiativePlain;
    filters: SubmissionInsightsQuery<Date>;
    maxRecursion: number;
  }) {
    const { view, isCompleted, startDate, endDate, scopeGroup } = filters as SubmissionInsightsQuery<Date>;
    const companies = await PortfolioService.getRecursiveInitiatives(portfolio, maxRecursion);

    const activeDataShares = await this.dataShareRepo.findActiveDataShare({
      requesterType: RequesterType.Portfolio,
      requesterId: portfolio._id,
      initiativeId: {
        $in: Array.from(companies.keys()).map((id) => new ObjectId(id)),
      },
    });
    activeDataShares.forEach((share) => {
      const company = companies.get(String(share.initiativeId));
      if (company) {
        company.requestedDataShares = company.requestedDataShares ? [...company.requestedDataShares, share] : [share];
      }
    });

    const selectedScope =
      scopeGroup ??
      (await this.getMostUsedPack({
        portfolio,
        startDate,
        endDate,
        activeDataShares,
      }));

    const scope = SurveyScope.fromScopeGroups(selectedScope);
    const customMetricGroups = await PortfolioService.getPortfolioMetricGroups(portfolio, scope.custom);

    const dataAccessibleCompaniesByScope = await PortfolioService.getSharedScopesCompanies({
      requestedScope: { scope },
      activeDataShares,
    });

    // @TODO [DATA-SHARE] It only takes ids, but does not care about data-share scope directly...
    const initiativeIds = Object.values(dataAccessibleCompaniesByScope).reduce((acc, cur) => {
      acc.push(...cur.initiativeIds);
      return acc;
    }, [] as ObjectId[]);
    const utrs = await PortfolioRepository.getSubmissionInsightsUtrs(
      scope,
      customMetricGroups.map((g) => g._id.toString())
    );
    const utrIds = utrs.map((utr) => utr._id);

    // @TODO [DATA-SHARE] not limiting returned data,
    // can we display this without data-share restrictions? As it only totals?
    const utrValues = await PortfolioRepository.getSubmissionInsightsData({
      view,
      isCompleted,
      scope,
      initiativeIds,
      startDate,
      endDate,
      utrIds,
    });

    return {
      scopeGroup: selectedScope,
      companies: Array.from(companies.values()),
      utrs: utrs.map((utr) => ({
        _id: utr._id,
        alternatives: utr.alternatives ?? {},
        type: utr.type,
        name: utr.name,
        typeCode: getPreferredTypeCode(utr, [selectedScope.code]),
        unit: utr.unit,
      })),
      utrValues: this.getDisplayUtrValues({ companies, view, utrValues }),
    };
  }
}

let instance: PortfolioSubmissionInsightsService;
export const getPortfolioSubmissionInsightsService = () => {
  if (!instance) {
    instance = new PortfolioSubmissionInsightsService(
      getDataShareRepository(),
    );
  }
  return instance;
}
