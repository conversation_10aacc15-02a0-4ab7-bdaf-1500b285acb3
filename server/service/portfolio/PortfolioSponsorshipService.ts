/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import Initiative, { InitiativeModel, InitiativeWithCustomer } from '../../models/initiative';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import { PortfolioCompany } from './PortfolioService';
import { ProductCodes } from '../../models/customer';
import { getSponsorshipService, SponsoredInitiative } from '../referral/SponsorshipService';
import { AppConfigService, getAppConfigService } from '../app/AppConfigService';
import { KeysEnum } from '../../models/commonProperties';
import ContextError from '../../error/ContextError';
import SponsorshipConfig, { SponsorshipConfigPlain, SponsorType } from '../../models/sponsorshipConfig';

type SubscriptionInitiative = Pick<InitiativeModel, '_id' | 'name' | 'permissionGroup' | 'appConfigCode' | 'customer'>;
type SubscriptionInitiativeWithCustomer = Pick<InitiativeWithCustomer, '_id' | 'name' | 'permissionGroup' | 'appConfigCode' | 'customer'>;

const subscriptionInitiativeWithCustomerProjection: KeysEnum<SubscriptionInitiativeWithCustomer> = {
  _id: 1,
  name: 1,
  customer: 1,
  permissionGroup: 1,
  appConfigCode: 1
}

const isSubscriptionInitiativeWithCustomer = (initiative: SubscriptionInitiative | null): initiative is SubscriptionInitiativeWithCustomer => {
  return Boolean(initiative?.customer);
}

type SponsorChangeParams = {
  portfolio: Pick<PortfolioCompany, '_id' | 'code' | 'name'>,
  initiativeId: string,
  referralCode: string,
};

type SponsorshipConfigParams = {
  portfolio: Pick<PortfolioCompany, '_id' | 'code'>;
  referralCode: string;
}

type UpdateSponsorshipConfigParams = SponsorshipConfigParams & { data: Partial<SponsorshipConfigPlain> }

/**
 * Wrapper class for PT specific interaction with sponsorship
 *
 * All core logic should be implemented through sponsorship service
 * that is not aware of PT and works with sponsor abstract logic.
 */
export class PortfolioSponsorshipService {

  /**
   * Only these product codes can be sponsored right now.
   */
  private readonly supportedProductCodes: ProductCodes[] = [
    ProductCodes.CompanyTracker,
    ProductCodes.SGXESGenome
  ]

  constructor(
    private logger: LoggerInterface,
    private appConfigService: AppConfigService,
    private sponsorshipService: ReturnType<typeof getSponsorshipService>,
  ) {
  }

  public async getReferralCodes(portfolioTracker: Pick<PortfolioCompany, 'code'>) {
    const codes = await this.sponsorshipService.getSponsorReferralCodes(portfolioTracker.code);
    return codes.sort();
  }

  public async getSponsoredCompanies(portfolioTracker: Pick<PortfolioCompany, 'code'>, promoCode: string): Promise<SponsoredInitiative[]> {
    const sponsoredInitiatives = await this.sponsorshipService.getSponsoredCompanies({
      sponsor: portfolioTracker,
      referralCode: promoCode,
    });

    return Promise.all(sponsoredInitiatives.map(async i => ({
      ...i,
      canBeSponsored: await this.canBeSponsored(i)
    })));
  }

  private async canBeSponsored(initiative: Pick<InitiativeWithCustomer, 'permissionGroup' | 'appConfigCode'>): Promise<boolean> {
    const appConfig = await this.appConfigService.getByOrganization(initiative);
    if (!appConfig?.productCode) {
      return false;
    }
    return this.supportedProductCodes.includes(appConfig.productCode);
  }

  private async getProductCode(initiative: Pick<InitiativeWithCustomer, '_id' | 'customer' | 'permissionGroup' | 'appConfigCode'>): Promise<ProductCodes> {
    const appConfig = await this.appConfigService.getByOrganization(initiative);
    const productCode = appConfig?.productCode;
    if (!productCode) {
      throw new ContextError(`Cannot sponsor company because we could not resolve appConfig.productCode`, {
        initiative: initiative._id.toString(),
        appConfig
      });
    }
    const canBeSponsored = await this.canBeSponsored(initiative);
    if (!canBeSponsored) {
      throw new ContextError(`Cannot sponsor company because they have restricted productCode`, {
        initiative: initiative._id.toString(),
        productCode,
        appConfig
      });
    }
    return productCode;
  }

  public async sponsorCompany(params: SponsorChangeParams) {
    const { portfolio, initiativeId, referralCode } = params;

    this.logger.info(`[PT] ${portfolio.name} add sponsorship to ${initiativeId} `, {
      portfolioId: portfolio._id,
      initiativeId,
      referralCode,
    });

    const initiative = await Initiative.findById(initiativeId, subscriptionInitiativeWithCustomerProjection).exec();
    if (!isSubscriptionInitiativeWithCustomer(initiative)) {
      throw new ContextError(`Cannot sponsor company: could not find valid customer in initiative.`, {
        initiativeId
      });
    }

    const productCode = await this.getProductCode(initiative);
    const { stripeSub, promotion } = await this.sponsorshipService.legacySponsorCompany({
      initiative: initiative,
      referralCode: referralCode,
      productCode,
      sponsor: portfolio,
    });

    return { referralCode, subscriptionId: stripeSub.id, promotionId: promotion.id };
  }

  public async unSponsorCompany(params: SponsorChangeParams) {
    const { initiativeId, portfolio, referralCode } = params;

    this.logger.info(`[PT] ${portfolio.name} remove sponsorship to ${initiativeId}`, {
      portfolioId: portfolio._id,
      initiativeId,
      referralCode,
    });

    const initiative = await Initiative.findById(initiativeId, subscriptionInitiativeWithCustomerProjection).exec();
    if (!isSubscriptionInitiativeWithCustomer(initiative)) {
      throw new ContextError(`Cannot unsponsor company: could not find valid customer in initiative.`, {
        initiativeId,
      });
    }

    const productCode = await this.getProductCode(initiative);
    const { subscription } = await this.sponsorshipService.legacyRemoveSponsorCompany({
      initiative,
      referralCode,
      productCode,
      sponsor: portfolio,
    });

    return { referralCode, subscriptionId: subscription.id };
  }

  public async getSponsorshipConfig(params: SponsorshipConfigParams) {
    const { portfolio, referralCode } = params;
    return SponsorshipConfig.findOne({
      sponsorCode: portfolio.code,
      sponsorType: SponsorType.Portfolio,
      'referrals.code': { $eq: referralCode },
    }).exec();
  }

  public async updateSponsorshipConfig(params: UpdateSponsorshipConfigParams) {
    const { portfolio, referralCode, data } = params;
    return SponsorshipConfig.findOneAndUpdate(
      { sponsorCode: portfolio.code, sponsorType: SponsorType.Portfolio, 'referrals.code': { $eq: referralCode } },
      data
    )
      .orFail()
      .exec();
  }
}

let instance: PortfolioSponsorshipService;
export const getPortfolioSponsorshipService = () => {
  if (!instance) {
    instance = new PortfolioSponsorshipService(
      wwgLogger,
      getAppConfigService(),
      getSponsorshipService(),
    );
  }
  return instance;
}
