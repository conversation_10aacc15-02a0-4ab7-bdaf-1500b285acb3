import { AppSetting, AppSetting<PERSON>ey, AppSettingPlain } from '../../models/app-setting';

export class AppSettingsService {
  constructor(private appSettingModel: typeof AppSetting) {}

  public async getAppSettingsByKey(key: AppSettingKey) {
    return this.appSettingModel.findOne({ key }).lean<AppSettingPlain>().exec();
  }

  public async getAppSettingsByKeys(keys: AppSettingKey[]) {
    return this.appSettingModel
      .find({
        key: { $in: keys },
      })
      .lean<AppSettingPlain[]>()
      .exec();
  }

  public async create({ key, value }: { key: AppSettingKey; value: string }) {
    return this.appSettingModel.create({ key, value });
  }
}

let instance: AppSettingsService | undefined;
export const getAppSettingsService = () => {
  if (!instance) {
    instance = new AppSettingsService(AppSetting);
  }
  return instance;
};
