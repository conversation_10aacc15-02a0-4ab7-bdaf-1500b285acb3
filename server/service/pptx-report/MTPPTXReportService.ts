import { ObjectId } from 'bson';
import ContextError from '../../error/ContextError';
import { TaskType } from '../../models/backgroundJob';
import { SurveyModelPlain } from '../../models/survey';
import { AssessmentType } from '../../types/materiality-assessment';
import { BackgroundJobService, getBackgroundJobService } from '../background-process/BackgroundJobService';
import { SupportedJobPlain } from '../materiality-assessment/background-job/types';
import { MaterialityAssessmentService } from '../materiality-assessment/MaterialityAssessmentService';
import { AssessmentResultType } from '../materiality-assessment/types';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import { PPTXReportingService } from './PPTXReportService';
import { MTPPTXTemplateScheme, PPTXTemplateName, TemplateContextInput } from './types';

export interface MaterialityReportContextInput {
  userId: ObjectId;
  initiativeId: ObjectId;
  survey: Pick<SurveyModelPlain, '_id' | 'assessmentType' | 'effectiveDate'>;
  assessmentJob: SupportedJobPlain;
}

export class MTPPTXReportService extends PPTXReportingService {
  constructor(logger: LoggerInterface, backgroundJobService: BackgroundJobService) {
    super(logger, backgroundJobService);
  }

  public getMTReportJobContext(params: MaterialityReportContextInput) {
    const { survey, assessmentJob, userId, initiativeId } = params;
    const assessmentType: AssessmentType = survey.assessmentType ?? AssessmentType.FinancialMateriality;
    const surveyId = survey._id;
    const materialityAssessmentService = new MaterialityAssessmentService(surveyId);
    const orderedFinancialData =
      materialityAssessmentService.getOrderedFinancialAssessmentData(assessmentJob?.tasks[0].data) ?? [];

    if (assessmentType === AssessmentType.FinancialMateriality) {
      const context: TemplateContextInput = {
        templateName: PPTXTemplateName.MT,
        templateScheme: MTPPTXTemplateScheme.FinancialReport,
        type: TaskType.GenerateMaterialityTrackerFinancialReport,
        userId,
        initiativeId,
        surveyId,
        financialAssessmentDataMin: orderedFinancialData,
        scoreJobId: assessmentJob._id,
        metadata: {
          effectiveDate: survey.effectiveDate,
        },
      };
      return context;
    }

    if (assessmentType === AssessmentType.DoubleMateriality) {
      const hasCustomOrder = !!assessmentJob.tasks[0].data.config?.orderedTopics.length;
      const assessmentDataMin = assessmentJob.tasks[0].data.result
        ? {
            [AssessmentResultType.Financial]: orderedFinancialData,
            [AssessmentResultType.Impact]: assessmentJob.tasks[0].data.result[AssessmentResultType.Impact],
          }
        : {
            [AssessmentResultType.Financial]: [],
            [AssessmentResultType.Impact]: [],
          };

      const context: TemplateContextInput = {
        templateName: PPTXTemplateName.MT,
        templateScheme: MTPPTXTemplateScheme.DoubleMateriality,
        type: TaskType.GenerateMaterialityTrackerDoubleMaterialityReport,
        userId,
        initiativeId,
        surveyId,
        assessmentDataMin,
        scoreJobId: assessmentJob._id,
        metadata: {
          effectiveDate: survey.effectiveDate,
          hasCustomOrder,
        },
      };
      return context;
    }

    throw new ContextError('Unsupported assessment type', { assessmentType, surveyId });
  }
}

let instance: MTPPTXReportService;

export const getMTPPTXReportService = () => {
  if (!instance) {
    instance = new MTPPTXReportService(wwgLogger, getBackgroundJobService());
  }
  return instance;
};
