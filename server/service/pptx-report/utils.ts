import { isNumericString } from '../../util/string';

export const generateMapKey = (utrCode: string, reportOffset: number = 0) => {
  return `${utrCode}_SURVEY_${reportOffset}`;
};

export const sum = (
  a: number | string | undefined,
  b: number | string | undefined,
  fallback: number | '' = ''
): number | '' => {
  if (!isNumericString(a) && !isNumericString(b)) {
    return fallback;
  }
  const aNum = Number(a);
  const bNum = Number(b);
  return aNum + bNum;
};

/**
 * Image url returned from upload function has this format:
 * `{baseUrl}/{file.bucket.name}/{file.name}?{file.metadata.md5Hash}`
 * where md5Hash can contain "/" characters, therefore we split on "?" first and then on "/"
 */
export const extractImageFromUrl = (imageUrl: string) => imageUrl.split('?').shift()?.split('/')?.pop();
