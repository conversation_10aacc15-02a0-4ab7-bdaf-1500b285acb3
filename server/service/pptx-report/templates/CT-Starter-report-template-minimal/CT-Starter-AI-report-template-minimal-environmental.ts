/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { UtrValueType } from '../../../../models/public/universalTrackerType';
import { PPTXTemplateLayoutItem } from '../PPTXTemplateInterface';
import { SlideSection } from '../builders/AISlideRecommendationBuilder';
import { PPTXTemplateBuilder } from '../builders/PPTXTemplateBuilder';

export const getPPTXConfigEnvironmentalAI = (builder: PPTXTemplateBuilder): PPTXTemplateLayoutItem[] => [
  {
    // Environmental
    slideId: 17,
  },
  ...builder.getSlidesRecommendation(SlideSection.Environmental).map(
    (slide): PPTXTemplateLayoutItem => ({
      slideId: slide.slideId,
      appendix: async () => slide.utrs.map(({ utrCode }) => utrCode),
      textReplacements: [
        [
          'HEADING',
          {
            text: async () => slide.category,
          },
        ],
        [
          'COLUMN_1_TITLE',
          {
            text: async () => slide.heading,
          },
        ],
        [
          'COLUMN_1_BODY',
          {
            text: async () => {
              const utrs = await Promise.all(
                slide.utrs.map(async ({ utrCode }) => {
                  const utr = await builder.createUTRBuilder(utrCode).getUtr();
                  switch (utr?.valueType) {
                    case UtrValueType.Table:
                      return {
                        name: utr.valueLabel,
                        value: await builder.createUTRTableBuilder(utrCode).toJSONString(),
                        previousValue: await builder.createUTRTableBuilder(utrCode).periodOffset(-1).toJSONString(),
                      };
                    case UtrValueType.Number:
                      return {
                        name: utr.valueLabel,
                        value: await builder.createUTRBuilder(utrCode).getInputSimpleNumericAnswer(),
                        previousValue: await builder
                          .createUTRBuilder(utrCode)
                          .periodOffset(-1)
                          .getInputSimpleNumericAnswer(),
                      };
                    case UtrValueType.NumericValueList:
                      return {
                        name: utr.valueLabel,
                        value: await builder.createUTRBuilder(utrCode).getComplexValueListData(),
                        previousValue: await builder
                          .createUTRBuilder(utrCode)
                          .periodOffset(-1)
                          .getComplexValueListData(),
                      };
                    default:
                      return {};
                  }
                })
              );

              let aiBuilder = builder
                .createAIBuilder()
                .ask([
                  `Create a clear narrative explaining a list of metrics: ${JSON.stringify(utrs)}`,
                  'Each object will have the following structure:',
                  '{ "name": The title of the metric, "value": "The value the user reported for the current reporting period", "previousValue": The value the user reported for the prior reporting period, for comparison }',
                  'Focus on data insights and creating a summary of key economic indicators.',
                ])
                .and('Use straightforward business language.')
                .and('Be concise and factual.');

              slide.utrs.forEach(({ utrCode }) => {
                aiBuilder = aiBuilder.addFurtherExplanation(utrCode).addTarget(utrCode);
              });

              return aiBuilder
                .and("Don't say what you can't do.")
                .bePositive()
                .and('Reference the data supplied, explaining the figures and what the figures mean.')
                .and('Focus on data insights. Include year-on-year comparisons where available.')
                .and(
                  "There is no need to explain if you don't have any corresponding data, just note that there was no data available."
                )
                .max(150)
                .exec();
            },
          },
        ],
        [
          'CHART_TITLE',
          {
            text: async () => slide.chartTitle,
          },
        ],
        [
          'UNIT_NUMBERSCALE',
          {
            text: async () => '',
          },
        ],
      ],
      chartReplacements: [
        [
          'CHART',
          {
            chartData: async () => {
              const getData = async (
                periodOffset = 0
              ): Promise<{ values: (number | string | null)[]; series?: { label: string }[] }> => {
                const isAllSimpleNumeric = slide.utrs.every(({ valueType }) =>
                  [UtrValueType.Number, UtrValueType.Percentage].includes(valueType)
                );
                if (isAllSimpleNumeric) {
                  const data = await Promise.all(
                    slide.utrs.map(async ({ utrCode }) => {
                      const value = await builder.createUTRBuilder(utrCode).periodOffset(periodOffset).getAsNumber();
                      return { label: utrCode, value };
                    })
                  );
                  return { values: data.map(({ value }) => value), series: data.map(({ label }) => ({ label })) };
                }

                if (slide.utrs.length !== 1) {
                  return { values: [''] };
                }

                if (slide.utrs[0].valueType === UtrValueType.Table) {
                  return { values: [''] };
                }

                if (slide.utrs[0].valueType === UtrValueType.NumericValueList) {
                  const data = await builder
                    .createUTRBuilder(slide.utrs[0].utrCode)
                    .periodOffset(periodOffset)
                    .getComplexValueListData();

                  if (!Object.keys(data).length) {
                    return { values: [''] };
                  }

                  return Object.entries(data).reduce(
                    (acc, [key, value]) => {
                      acc.series.push({ label: key });
                      acc.values.push(Number(value));
                      return acc;
                    },
                    { values: [] as (number | null)[], series: [] as { label: string }[] }
                  );
                }
                return { values: [''] };
              };
              // TODO: [GU-6287] This is for spike/testing purposes, will need to cleanup getData()
              const { values, series } = await getData();
              const { values: prevValues, series: prevSeries } = await getData(-1);

              return {
                series: series ??
                  prevSeries ?? [
                    {
                      label: slide.chartTitle,
                    },
                  ],
                categories: [
                  {
                    label: String(await builder.getYear(-1)),
                    values: prevValues,
                  },
                  {
                    label: String(await builder.getYear()),
                    values: values,
                  },
                ],
              };
            },
          },
        ],
      ],
    })
  ),
];
