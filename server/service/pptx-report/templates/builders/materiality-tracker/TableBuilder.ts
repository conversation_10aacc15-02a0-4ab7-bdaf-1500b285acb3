/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { AssessmentData } from '../../../../materiality-assessment/types';
import { MaterialityBoundary, MaterialPillar } from '../../../../../models/materialTopics';
import { capitalize, truncate } from '../../../../../util/string';
import { DEFAULT_TABLE_CELL_VALUE, MAX_TOPIC_LETTERS } from '../../../constants';
import { createArrayOfNumbers } from '../../../../../util/array';

const SCORE_MAX_ROWS = 40;

export class TableBuilder {
  private applyMaxDecimals: number | undefined;
  private boundaryValues = Object.values(MaterialityBoundary);
  private materialPillars = Object.values(MaterialPillar);
  // TODO: Table should be cached so that it doesn't need to be regenerated every time the slides is repeated for the same table

  public constructor(private hydratedTopics: AssessmentData[]) {}

  // Add this GETTER for testing purposes
  public getApplyMaxDecimals(): number | undefined {
    return this.applyMaxDecimals;
  }

  public maxDecimals(precision: number | undefined): TableBuilder {
    this.applyMaxDecimals = precision;
    return this;
  }

  public applyDecimals(value: number): number {
    if (this.applyMaxDecimals !== undefined) {
      return Number(value?.toFixed(this.applyMaxDecimals));
    }
    return value;
  }

  public toNum(value: any): number {
    const v = Number(value);
    return Number.isNaN(v) ? 0 : v;
  }

  public getScoresTableRowCount(): number {
    // Top topics by score
    const table = this.hydratedTopics.toSorted((a, b) => b.score - a.score).slice(0, SCORE_MAX_ROWS);
    return table.length;
  }

  public async getScoresTable(): Promise<{ values: (string | number)[] }[]> {
    const table = this.hydratedTopics.toSorted((a, b) => b.score - a.score).slice(0, SCORE_MAX_ROWS);
    return table.map((row) => ({
      values: [row.name || row.code, this.applyDecimals(this.toNum(row.score))],
    }));
  }

  private getDefinitionWithScore(score: string | number, description: string | undefined) {
    const formatedScore = this.applyDecimals(this.toNum(score));
    if (!description) {
      return `Score: ${formatedScore}`;
    }
    return `Score: ${formatedScore} - ${description}`;
  }

  public async getDefinitionsTable({
    startOffset,
    endOffset,
  }: {
    startOffset: number;
    endOffset: number;
  }): Promise<{ values: (string | number)[] }[]> {
    const table = this.hydratedTopics.slice(startOffset, endOffset);
    return table.map((row) => ({
      values: [row.name || row.code, this.getDefinitionWithScore(row.relativeScore ?? 0, row.description)],
    }));
  }

  public async getBoundariesTable(numOfTopics: number): Promise<{ values: (string | number)[] }[]> {
    const topTopics = this.hydratedTopics.slice(0, numOfTopics);

    const table: { values: (string | number)[] }[] = [];

    topTopics.forEach((topic) => {
      if (topic) {
        const boundaryValues = this.boundaryValues.map((v) => {
          if (topic.categories?.boundary?.includes(v)) {
            return '●';
          }
          return '○';
        });
        table.push({ values: [truncate(topic.name || topic.code, MAX_TOPIC_LETTERS, true), ...boundaryValues] });
        return;
      }
      table.push({
        values: [
          DEFAULT_TABLE_CELL_VALUE,
          DEFAULT_TABLE_CELL_VALUE,
          DEFAULT_TABLE_CELL_VALUE,
          DEFAULT_TABLE_CELL_VALUE,
          DEFAULT_TABLE_CELL_VALUE,
          DEFAULT_TABLE_CELL_VALUE,
          DEFAULT_TABLE_CELL_VALUE,
          DEFAULT_TABLE_CELL_VALUE,
        ],
      });
    });
    // Expected shape: { values: [topic, boundaries[0], boundaries[1], ...boundaries[6]] }
    // Example output: ['topic #1', '●', '○', '●', ...]
    return table;
  }

  /** @deprecated */
  public async getSummaryFindingsTable(): Promise<{ values: (string | number)[] }[]> {
    // Topics with material pillars sorted by score
    const topics = this.hydratedTopics
      .filter((topic) => Boolean(topic.categories?.materialPillar?.length))
      .sort((a, b) => b.score - a.score);

    return this.materialPillars.map((pillar) => {
      // Standards and frameworks and sdgs must not be duplicated
      const uniqueTopics = new Set();
      const uniqueStandardsCodes = new Set();
      const uniqueSDGCodes = new Set();

      // Use the top 3 of each pillar
      topics
        .filter((t) => t.categories?.materialPillar?.includes(pillar))
        .slice(0, 3)
        .forEach((topic, index) => {
          uniqueTopics.add(`${index + 1}. ${topic.name || topic.code}`);
          topic.utrMapping?.forEach((utr) => uniqueStandardsCodes.add(utr.code));
          topic.categories?.sdg?.forEach((code) => uniqueSDGCodes.add(code));
        });
      // Expected shape: { values: [pillar, topic, standards, sdgs] }
      // Example output: ['People', '1.social\n2.partnership\n3.planet', 'gri/102', '1, 1.1']
      return {
        values: [
          capitalize(pillar + '\n'),
          Array.from(uniqueTopics).join('\n'),
          Array.from(uniqueStandardsCodes).join(', '),
          Array.from(uniqueSDGCodes).join(', '),
        ],
      };
    });
  }

  /** @deprecated */
  public async getBoundariesByPillarsTable(): Promise<{ values: (string | number)[] }[]> {
    const topics = this.hydratedTopics
      .filter((topic) => Boolean(topic.categories?.materialPillar?.length))
      .sort((a, b) => b.score - a.score);

    const table: { values: (string | number)[] }[] = [];

    this.materialPillars.map((pillar) => {
      // Use the top 3 of each pillar
      const topTopics = topics.filter((t) => t.categories?.materialPillar?.includes(pillar)).slice(0, 3);
      createArrayOfNumbers(0, 2).forEach((index) => {
        const topic = topTopics?.[index];
        if (topic) {
          const boundaryValues = this.boundaryValues.map((v) => {
            if (topic.categories?.boundary?.includes(v)) {
              return '✓';
            }
            return DEFAULT_TABLE_CELL_VALUE;
          });
          table.push({ values: [capitalize(pillar), topic.name || topic.code, ...boundaryValues] });
          return;
        }
        table.push({
          values: [
            capitalize(pillar),
            DEFAULT_TABLE_CELL_VALUE,
            DEFAULT_TABLE_CELL_VALUE,
            DEFAULT_TABLE_CELL_VALUE,
            DEFAULT_TABLE_CELL_VALUE,
            DEFAULT_TABLE_CELL_VALUE,
            DEFAULT_TABLE_CELL_VALUE,
            DEFAULT_TABLE_CELL_VALUE,
            DEFAULT_TABLE_CELL_VALUE,
          ],
        });
      });
    });
    // Expected shape: { values: [pillar, topic, boundaries[0], boundaries[1], ...boundaries[6]] }
    // Example output: ['People', 'topic #1', '✓', DEFAULT_TABLE_CELL_VALUE, '✓', ...]
    return table;
  }

  /** @deprecated */
  public async getTopTopicsByBoundariesTable(): Promise<{ values: (string | number)[] }[]> {
    // Topics with material pillars sorted by score
    const topics = this.hydratedTopics
      .filter((topic) => Boolean(topic.categories?.boundary?.length))
      .sort((a, b) => b.score - a.score);

    const topicsByBoundariesMap = this.boundaryValues.reduce<{ [key in MaterialityBoundary]: string[] }>(
      (acc, boundary) => {
        acc[boundary] = topics
          .filter((topic) => topic.categories?.boundary?.includes(boundary))
          .map((topic) => topic.name || topic.code)
          .slice(0, 10);
        return acc;
      },
      {
        [MaterialityBoundary.Leadership]: [],
        [MaterialityBoundary.ResearchAndDevelopment]: [],
        [MaterialityBoundary.SupplyChain]: [],
        [MaterialityBoundary.ProductAndServices]: [],
        [MaterialityBoundary.Distribution]: [],
        [MaterialityBoundary.Communities]: [],
        [MaterialityBoundary.Experiences]: [],
      }
    );

    // top 10 material topics per boundary
    const table: { values: (string | number)[] }[] = [];
    for (let i = 0; i < 10; i++) {
      table.push({
        values: [
          topicsByBoundariesMap[MaterialityBoundary.Leadership][i] || DEFAULT_TABLE_CELL_VALUE,
          topicsByBoundariesMap[MaterialityBoundary.ResearchAndDevelopment][i] || DEFAULT_TABLE_CELL_VALUE,
          topicsByBoundariesMap[MaterialityBoundary.SupplyChain][i] || DEFAULT_TABLE_CELL_VALUE,
          topicsByBoundariesMap[MaterialityBoundary.ProductAndServices][i] || DEFAULT_TABLE_CELL_VALUE,
          topicsByBoundariesMap[MaterialityBoundary.Distribution][i] || DEFAULT_TABLE_CELL_VALUE,
          topicsByBoundariesMap[MaterialityBoundary.Communities][i] || DEFAULT_TABLE_CELL_VALUE,
          topicsByBoundariesMap[MaterialityBoundary.Experiences][i] || DEFAULT_TABLE_CELL_VALUE,
        ],
      });
    }
    return table;
  }

  /** @deprecated */
  public async getAppendixDescriptionTable(): Promise<{ values: (string | number)[] }[]> {
    const topics = this.hydratedTopics
      .filter((topic) => Boolean(topic.categories?.materialPillar?.length))
      .sort((a, b) => b.score - a.score);

    const table: { values: (string | number)[] }[] = [];

    this.materialPillars.map((pillar) => {
      // Use the top 4 of each pillar
      const topTopics = topics.filter((t) => t.categories?.materialPillar?.includes(pillar)).slice(0, 4);
      createArrayOfNumbers(0, 3).forEach((index) => {
        const topic = topTopics?.[index];
        if (topic) {
          table.push({
            values: [capitalize(pillar), topic.name || topic.code, topic.description || DEFAULT_TABLE_CELL_VALUE],
          });
          return;
        }
        table.push({
          values: [capitalize(pillar), DEFAULT_TABLE_CELL_VALUE, DEFAULT_TABLE_CELL_VALUE],
        });
      });
    });
    // Expected shape: { values: [pillar, topic, description] }
    // Example output: ['People', 'topic #1', 'topic #1 description']
    return table;
  }

  /** @deprecated */
  public async getAppendixActionTable(): Promise<{ values: (string | number)[] }[]> {
    const topics = this.hydratedTopics
      .filter((topic) => Boolean(topic.categories?.materialPillar?.length))
      .sort((a, b) => b.score - a.score);

    const table: { values: (string | number)[] }[] = [];

    this.materialPillars.map((pillar) => {
      // Use the top 4 of each pillar
      const topTopics = topics.filter((t) => t.categories?.materialPillar?.includes(pillar)).slice(0, 4);
      createArrayOfNumbers(0, 3).forEach((index) => {
        const topic = topTopics?.[index];
        if (topic) {
          table.push({
            values: [capitalize(pillar), topic.name || topic.code, topic.action || DEFAULT_TABLE_CELL_VALUE],
          });
          return;
        }
        table.push({
          values: [capitalize(pillar), DEFAULT_TABLE_CELL_VALUE, DEFAULT_TABLE_CELL_VALUE],
        });
      });
    });
    // Expected shape: { values: [pillar, topic, description] }
    // Example output: ['People', 'topic #1', 'topic #1 description']
    return table;
  }
}
