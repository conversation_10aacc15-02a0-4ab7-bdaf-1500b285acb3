/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import ContextError from '../../../../../error/ContextError';
import Initiative, { InitiativePlain } from '../../../../../models/initiative';
import { KeysEnum } from '../../../../../models/commonProperties';
import { ObjectId } from 'bson';
import { AssessmentData, MaterialityAssessmentScope } from '../../../../materiality-assessment/types';
import { TableBuilder } from './TableBuilder';
import { PPTXTemplateReplacement, TableDataRow, TOCEntry } from '../../PPTXTemplateInterface';
import UniversalTracker from '../../../../../models/universalTracker';
import UniversalTrackerValue from '../../../../../models/universalTrackerValue';
import ValueList from '../../../../../models/valueList';
import { ESGCategory, MaterialPillar } from '../../../../../models/materialTopics';
import { capitalize, truncate } from '../../../../../util/string';
import { topicLengthMap } from '../../../../materiality-assessment/constants';

const MAX_TOPIC_LETTERS = 50;
const MAX_SUGGESTED_TOPIC = 2;

type BuilderInitiative = Pick<InitiativePlain, '_id' | 'industry' | 'name' | 'profile'>;
export type RelevantTopic = {
  name: string;
  relativeScore: number;
  category: ESGCategory | undefined;
  pillars: MaterialPillar[] | undefined;
  priority: number;
};

export type ExtendedAssessmentData<T extends AssessmentData = AssessmentData> = T & {
  priority: number;
};

export abstract class BaseBuilder<T extends AssessmentData = AssessmentData, K extends RelevantTopic = RelevantTopic> {
  private initiative: null | BuilderInitiative = null;
  protected tableBuilder: null | TableBuilder = null;
  private tocStore: Map<string, TOCEntry> = new Map();
  protected data: ExtendedAssessmentData<T>[] = [];
  protected suggestedTopicsByPillarsMap: Map<string, RelevantTopic[]> = new Map();

  abstract getRelevantTopTopics(): K[];

  constructor(
    protected initiativeId: ObjectId,
    protected assessmentData: T[],
    protected surveyId: ObjectId,
    protected sizeScope: MaterialityAssessmentScope,
  ) {
    this.data = this.assessmentData
      .map((data, index) => ({
        ...data,
        priority: index + 1,
      }))
      .filter((topic) => Boolean(topic.categories?.esg?.length) && topic.relativeScore);
    this.suggestedTopicsByPillarsMap = new Map(
      Object.values(MaterialPillar).map<[string, RelevantTopic[]]>((pillar) => [
        pillar,
        this.data
          .slice(this.getTopicsLength())
          .filter((topic) => topic.categories?.materialPillar?.includes(pillar))
          .slice(0, MAX_SUGGESTED_TOPIC)
          .map((topic) => ({
            name: topic ? truncate(topic.name || topic.code, MAX_TOPIC_LETTERS, true) : 'N/A',
            relativeScore: topic.relativeScore ?? 0,
            category: topic.categories?.esg?.[0],
            pillars: topic.categories?.materialPillar,
            priority: topic.priority,
          })),
      ])
    );
  }

  protected async getSuggestedContent({ pillar, initiativeName }: { pillar: MaterialPillar; initiativeName: string }) {
    const suggested = this.suggestedTopicsByPillarsMap.get(pillar);
    const pillarName = capitalize(pillar);
    if (!suggested || suggested.length === 0) {
      return `Based on your assessment results, no highly relevant topics have been categorised under the ${pillarName} pillar for ${initiativeName}.`;
    }
    const firstSuggestion = suggested[0];
    const secondSuggestion = suggested[1];
    return `Based on your assessment results, no highly relevant topics have been categorised under the ${pillarName} pillar for ${initiativeName}.
      ${
        firstSuggestion
          ? `To enhance your organisation's holistic approach, you may consider incorporating ${
              firstSuggestion.name
            }, which ranked ${firstSuggestion.priority} with a score of ${firstSuggestion.relativeScore ?? ''}%.`
          : ''
      }
      ${
        secondSuggestion
          ? ` Alternatively, ${secondSuggestion.name}, ranked ${secondSuggestion.priority} with a score of ${secondSuggestion.relativeScore}%, could also provide valuable insights into this area.`
          : ''
      }`;
  }

  public getTopicsLength() {
    return topicLengthMap[this.sizeScope];
  }

  public async getInitiative() {
    if (this.initiative === null) {
      const initiativeProjection: KeysEnum<BuilderInitiative> = {
        _id: 1,
        industry: 1,
        name: 1,
        profile: 1,
      };

      this.initiative = await Initiative.findById(this.initiativeId, initiativeProjection).lean();
      if (!this.initiative) {
        throw new ContextError('Could not load initiative to generate PPTX report', {
          context,
        });
      }
    }
    return this.initiative;
  }

  public async getSectorName(): Promise<null | string> {
    const utr = await UniversalTracker.findOne({ code: 'materiality-2024/sector' }).lean().exec();
    const valueListId = utr?.valueValidation?.valueList?.listId;

    if (!utr || !valueListId) {
      return null;
    }

    const valueList = await ValueList.findOne({ _id: valueListId }).lean().exec();
    const sectorUtrv = await UniversalTrackerValue.findOne({
      universalTrackerId: utr._id,
      'compositeData.surveyId': this.surveyId,
    })
      .lean()
      .exec();

    if (!sectorUtrv || !valueList) {
      return null;
    }
    return valueList.options.find((option) => option.code === sectorUtrv.valueData?.data)?.name ?? null;
  }

  public async getCompanyName(): Promise<string> {
    const initiative = await this.getInitiative();
    return initiative.name;
  }

  protected async createTableBuilder() {
    return new TableBuilder(this.data);
  }

  public addTOCEntry(row: TOCEntry) {
    const current = this.tocStore.get(row.key);
    if (current) {
      // Entry has already been added
      // Table of contents only care about the start page of a section
      return;
    }
    this.tocStore.set(row.key, row);
  }

  public getTOCTable(): TableDataRow[] {
    const tocSize = this.tocStore.size;
    if (!tocSize) {
      return [];
    }
    // Table of contents is page 2, so pageNumber >= 2 will need to add 1 to accumulate for the TOC page
    return Array.from(this.tocStore.values()).map((row) => ({
      values: [row.title, row.pageNumber >= 2 ? row.pageNumber + 1 : row.pageNumber],
    }));
  }

  public async getTableBuilder() {
    if (this.tableBuilder === null) {
      this.tableBuilder = await this.createTableBuilder();
    }
    return this.tableBuilder;
  }

  public getPillarsTextReplacement(initiativeName: string): PPTXTemplateReplacement[] {
    const topics = this.getRelevantTopTopics();
    const pillarMap = topics.reduce<Record<MaterialPillar, string[]>>(
      (acc, topic) => {
        if (topic.pillars) {
          topic.pillars.forEach((pillar) => {
            acc[pillar].push(topic.name);
          });
        }
        return acc;
      },
      {
        [MaterialPillar.People]: [],
        [MaterialPillar.Partnership]: [],
        [MaterialPillar.Prosperity]: [],
        [MaterialPillar.Planet]: [],
        [MaterialPillar.Principle]: [],
      }
    );
    const replacements: PPTXTemplateReplacement[] = [];
    Object.values(MaterialPillar).forEach((pillar) => {
      const topics = pillarMap[pillar];
      if (topics.length) {
        replacements.push([
          pillar,
          {
            text: async () =>
              topics.map((topic, index) => `${index + 1}. ${truncate(topic, MAX_TOPIC_LETTERS, true)}`).join('\n'),
          },
        ]);
      } else {
        // Missing data then use custom message
        replacements.push([pillar, { text: async () => this.getSuggestedContent({ pillar, initiativeName }) }]);
      }
    });

    return replacements;
  }
}
