import { ObjectId } from 'bson';
import {
  AssessmentData,
  AssessmentResult,
  AssessmentResultType,
  DoubleMaterialityAssessmentData,
  MaterialityAssessmentScope,
} from '../../../../materiality-assessment/types';
import { truncate } from '../../../../../util/string';
import { MAX_TOPIC_LETTERS } from '../../../constants';
import { mapToDoubleMaterialityData } from '../../../../../util/materiality-assessment';
import { BaseBuilder, ExtendedAssessmentData, RelevantTopic } from './BaseBuilder';

type DoubleMaterialityRelevantTopic = RelevantTopic &
  Pick<DoubleMaterialityAssessmentData, 'nonFinancialRelativeScore' | 'financialRelativeScore'>;

export class DoubleMaterialityBuilder extends BaseBuilder<
  DoubleMaterialityAssessmentData,
  DoubleMaterialityRelevantTopic
> {
  constructor(
    protected initiativeId: ObjectId,
    protected assessmentResult: AssessmentResult<AssessmentData>,
    protected surveyId: ObjectId,
    protected sizeScope: MaterialityAssessmentScope,
    protected hasCustomOrder: boolean
  ) {
    const data = mapToDoubleMaterialityData({
      financialData: assessmentResult[AssessmentResultType.Financial],
      nonFinancialData: assessmentResult[AssessmentResultType.Impact],
      hasCustomOrder,
    });
    super(initiativeId, data, surveyId, sizeScope);
  }

  private mapToRelevantTopTopics(
    topic: ExtendedAssessmentData<DoubleMaterialityAssessmentData>
  ): DoubleMaterialityRelevantTopic {
    return {
      name: topic ? truncate(topic.name || topic.code, MAX_TOPIC_LETTERS, true) : 'N/A',
      relativeScore: topic.relativeScore ?? 0,
      category: topic.categories?.esg?.[0],
      pillars: topic.categories?.materialPillar,
      priority: topic.priority,
      financialRelativeScore: topic.financialRelativeScore,
      nonFinancialRelativeScore: topic.nonFinancialRelativeScore,
    };
  }

  public getRelevantTopTopics() {
    return this.data.slice(0, this.getTopicsLength()).map((topic) => this.mapToRelevantTopTopics(topic));
  }
}
