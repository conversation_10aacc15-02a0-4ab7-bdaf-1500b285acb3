/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import { AssessmentData, MaterialityAssessmentScope } from '../../../../materiality-assessment/types';
import { BaseBuilder, ExtendedAssessmentData } from './BaseBuilder';
import { truncate } from '../../../../../util/string';
import { MAX_TOPIC_LETTERS } from '../../../constants';
import { roundTo } from '../../../../../util/number';

export class FinancialBuilder extends BaseBuilder {
  constructor(
    protected initiativeId: ObjectId,
    protected financialAssessmentData: AssessmentData[],
    protected surveyId: ObjectId,
    protected sizeScope: MaterialityAssessmentScope,
    protected maxScore: number
  ) {
    // Backward compatibility, old financial reports don't have relative score
    const data: AssessmentData[] = financialAssessmentData.map((topic) => {
      return topic.relativeScore
        ? topic
        : {
            ...topic,
            relativeScore: roundTo((topic.score / maxScore) * 100),
          };
    });
    super(initiativeId, data, surveyId, sizeScope);
  }

  private mapToRelevantTopTopics(topic: ExtendedAssessmentData) {
    return {
      name: topic ? truncate(topic.name || topic.code, MAX_TOPIC_LETTERS, true) : 'N/A',
      relativeScore: topic.relativeScore ?? 0,
      category: topic.categories?.esg?.[0],
      pillars: topic.categories?.materialPillar,
      priority: topic.priority,
    };
  }

  public getRelevantTopTopics() {
    return this.data.slice(0, this.getTopicsLength()).map((topic) => this.mapToRelevantTopTopics(topic));
  }
}
