/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

export interface CreateContactData {
  email: string;
  externalId?: string;
  title?: string;
  lastName?: string;
  firstName?: string;
}

interface CommonCrmOnboardingData {
  type: string;
  externalId: string;
  contactId: string;
}

export interface CreateCrmOnboardingData extends CommonCrmOnboardingData {
  token: string;
  onboardingUrl: string;
  unsubscribeUrl: string;
  initiativeName: string;
  appConfigCode: string | undefined;
}

export interface CreateCrmOrganizationOnboardingData
  extends Omit<CreateCrmOnboardingData, 'initiativeName' | 'appConfigCode'> {
  organizationName: string;
}

export interface CloseCrmOnboardingData extends CommonCrmOnboardingData {
  status: string;
}
