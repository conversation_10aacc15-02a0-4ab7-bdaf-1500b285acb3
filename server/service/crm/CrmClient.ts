/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { CreateCrmOnboardingData, CloseCrmOnboardingData, CreateCrmOrganizationOnboardingData } from './contact';
import { OnboardingModel } from '../../models/onboarding';

export interface CrmClient {

  createOnboarding(data: CreateCrmOnboardingData | CreateCrmOrganizationOnboardingData): Promise<{}>;

  closeOnboarding(data: CloseCrmOnboardingData): Promise<{}>;

  linkContact(model: OnboardingModel): Promise<string>;
}

export enum OnboardingOutcome {
  Expired = 'Expired',
  Completed = 'Completed',
  Pending = 'Pending',
  Rejected = 'Rejected',
  Deleted = 'Deleted',
}


export enum ItemTypes {
  Onboarding = 'onboarding',
}
