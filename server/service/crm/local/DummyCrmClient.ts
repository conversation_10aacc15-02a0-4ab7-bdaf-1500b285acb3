/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { CrmClient } from '../CrmClient';
import { CloseCrmOnboardingData, CreateCrmOnboardingData, CreateCrmOrganizationOnboardingData } from '../contact';
import { OnboardingModel } from '../../../models/onboarding';

/**
 * Dummy CRM client that currently does nothing.
 * As we no longer require external CRM atm the moment.
 */
export class DummyCrmClient implements CrmClient {
  closeOnboarding(data: CloseCrmOnboardingData): Promise<{}> {
    return Promise.resolve({});
  }

  createOnboarding(data: CreateCrmOnboardingData | CreateCrmOrganizationOnboardingData): Promise<{}> {
    return Promise.resolve({});
  }

  linkContact(model: OnboardingModel): Promise<string> {
    return Promise.resolve('');
  }
}

let instance: DummyCrmClient;
export const getDummyCrmClient = () => {
  if (!instance) {
    instance = new DummyCrmClient();
  }
  return instance;
}
