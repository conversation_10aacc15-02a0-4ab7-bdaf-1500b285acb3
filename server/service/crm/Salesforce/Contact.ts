/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

export enum ContactFields {
  UserId = 'External_Id__c',
  UserEmail = 'Canonical_Email__c'
}

export interface CreateContactData {
  Email: string;
  Canonical_Email__c: string;
  External_Id__c?: string;
  Title?: string;
  LastName?: string;
  FirstName?: string;
}

export interface MinContact extends CreateContactData {
  Id: string;
}

export interface Contract extends MinContact {
  IsDeleted?: boolean;
  MasterRecordId?: null;
  AccountId?: null;
  Salutation?: null;
  Name: string;
  OtherStreet?: null;
  OtherCity?: null;
  OtherState?: null;
  OtherPostalCode?: null;
  OtherCountry?: null;
  OtherLatitude?: null;
  OtherLongitude?: null;
  OtherGeocodeAccuracy?: null;
  OtherAddress?: null;
  MailingStreet?: null;
  MailingCity?: null;
  MailingState?: null;
  MailingPostalCode?: null;
  MailingCountry?: null;
  MailingLatitude?: null;
  MailingLongitude?: null;
  MailingGeocodeAccuracy?: null;
  MailingAddress?: null;
  Phone?: null;
  Fax?: null;
  MobilePhone?: null;
  HomePhone?: null;
  OtherPhone?: null;
  AssistantPhone?: null;
  ReportsToId?: null;
  Department?: null;
  AssistantName?: null;
  LeadSource?: null;
  Birthdate?: null;
  Description?: null;
  OwnerId: string;
  CreatedDate: string;
  CreatedById: string;
  LastModifiedDate: string;
  LastModifiedById: string;
  SystemModstamp: string;
  LastActivityDate?: null;
  LastCURequestDate?: null;
  LastCUUpdateDate?: null;
  LastViewedDate: string;
  LastReferencedDate: string;
  EmailBouncedReason?: null;
  EmailBouncedDate?: null;
  IsEmailBounced: boolean;
  PhotoUrl: string;
  Jigsaw?: null;
  JigsawContactId?: null;
  CleanStatus: string;
  Level__c?: null;
  Languages__c?: null;
  }
