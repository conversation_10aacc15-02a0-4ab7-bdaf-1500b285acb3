/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { Account, Notification, SalesforceSoap } from './types';
import config from '../../../config';
import { LoggerInterface, wwgLogger } from '../../wwgLogger';

export class OutboundMessage {

  constructor(private logger: LoggerInterface = wwgLogger) {
  }

  public isNotifications(data: unknown): data is SalesforceSoap {

    if (typeof data === 'object') {
      if (data !== null) {
        const rootObject = data as Record<string, any>;

        return rootObject
          ?.['soapenv:Envelope']
          ?.['soapenv:Body']
          ?.['notifications']
          ?.OrganizationId === config.crm.salesforce.organizationId
      }
    }

    return false;
  }

  public processNotification(msg: unknown): Notification[] {
    if (!this.isNotifications(msg)) {
      this.logger.error(`Received not valid notification: "${JSON.stringify(msg)}"`)
      return [];
    }

    const notificationData = msg['soapenv:Envelope']['soapenv:Body'].notifications;
    return Array.isArray(notificationData.Notification) ?
      notificationData.Notification :
      [notificationData.Notification];
  }

  isAccountNotification(n: Notification): n is Notification<Account> {
    if (typeof n.sObject === 'object') {
      if ('sf:Customer_Journey__c' in n.sObject && n.sObject['sf:Customer_Journey__c']) {
        return true;
      }
    }
    return false;
  }

  public toAccountMessage(n: Notification<Account>) {
    const account = n.sObject as Account;
    return {
      accountId: n.Id,
      status: account['sf:Account_Status__c'],
      initiativeId: account['sf:G17Eco_ID__c'],
      journeyStatus: account['sf:Customer_Journey__c'],
    }
  }
}


