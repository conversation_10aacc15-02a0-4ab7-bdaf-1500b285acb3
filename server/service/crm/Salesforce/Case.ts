/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

export enum CaseFields {
}

export enum CaseTypes {
  Onboarding = 'onboarding',
}

export enum CaseStatus {
  Closed = 'Closed',
  New = 'New',
  Working = 'Working',
  Escalated = 'Escalated',
}

export enum CaseOutcome {
  Expired = 'Expired',
  Completed = 'Completed',
  Pending = 'Pending',
  Rejected = 'Rejected',
  Deleted = 'Deleted',
}

export interface CreateCaseData {
  ContactId: string;
  AccountId?: string;
  Type: CaseTypes;
  Onboarding_Id__c: string;
  Outcome__c?: CaseOutcome;
}

export interface OnboardingCaseCreate extends CreateCaseData {
  Token__c: string;
  Onboarding_Url__c: string;
  Unsubscribe_Url__c: string;
  Initiative_Name__c: string;
}

export interface MinCase extends CreateCaseData {
  Id?: string;
  CaseNumber: string;
  Status: string;
  CreatedDate: Date;
}

export interface Case extends MinCase {
  IsDeleted: boolean;
  AssetId?: any;
  ParentId?: any;
  SuppliedName?: any;
  SuppliedEmail?: any;
  SuppliedPhone?: any;
  SuppliedCompany?: any;
  Reason?: any;
  Origin: string;
  Subject?: any;
  Priority: string;
  Description?: any;
  IsClosed: boolean;
  ClosedDate?: any;
  IsEscalated: boolean;
  OwnerId: string;
  CreatedById: string;
  LastModifiedDate: Date;
  LastModifiedById: string;
  SystemModstamp: Date;
  ContactPhone?: any;
  ContactMobile?: any;
  ContactEmail: string;
  ContactFax?: any;
  LastViewedDate: Date;
  LastReferencedDate: Date;
  EngineeringReqNumber__c?: any;
  SLAViolation__c?: any;
  Product__c?: any;
  PotentialLiability__c?: any;
}
