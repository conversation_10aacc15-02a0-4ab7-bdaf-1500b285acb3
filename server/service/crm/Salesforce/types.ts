/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


export interface Account {
  'sf:Id': string;
  'sf:Customer_Journey__c': string;
  'sf:Account_Status__c'?: string;
  'sf:G17Eco_ID__c'?: string;
  [k: string]: string | undefined;
}

export interface Notification<T extends object = { [k: string]: string | unknown }> {
  Id: string;
  sObject: T;
}

export interface Notifications {
  OrganizationId: string;
  ActionId: string;
  SessionId: string;
  EnterpriseUrl: string;
  PartnerUrl: string;
  Notification: Notification | Notification[];
}

export interface SoapenvBody {
  notifications: Notifications;
}

export interface SoapenvEnvelope {
  'soapenv:Body': SoapenvBody;
}

export interface SalesforceSoap {
  'soapenv:Envelope': SoapenvEnvelope;
}
