import Survey, { Scope, SurveyGroupsData } from '../../models/survey';
import { BulkDelegateSurvey, BulkDelegateSurveyQuery, SurveyRepository } from '../../repository/SurveyRepository';
import { wwgLogger } from '../wwgLogger';
import { ObjectId } from 'bson';
import { Blueprint, getBlueprintRepository, SurveyForm } from '../../repository/BlueprintRepository';
import { extractVisibleUtrCodes } from '../../survey/surveyForms';
import { Blueprints, DefaultBlueprintCode } from '../../survey/blueprints';
import { BlueprintContributions, getBluePrintContribution } from '../survey/BlueprintContribution';
import UniversalTracker, { UniversalTrackerPlain } from '../../models/universalTracker';
import { getMaterialityCode } from '../survey/scope/materiality';
import MetricGroup from '../../models/metricGroup';
import { filterByFramework, filterBySdgCodes, filterByStandard } from '../survey/scope/filterScope';
import Initiative, { InitiativePlain } from '../../models/initiative';
import { getValueListIds } from '../utr/utrUtil';
import { ValueListRepository } from '../../repository/ValueListRepository';
import { SurveyCalculator } from '../survey/SurveyCalculator';
import { InitiativeRepository } from '../../repository/InitiativeRepository';
import { SurveyScope } from "../survey/SurveyScope";
import { initiativeSurveyMin, SurveyModelMinimalInitiative } from "../../repository/projections";
import { UserPlain } from "../../models/user";
import { UniversalTrackerValueDelegation } from "../delegation/UniversalTrackerValueDelegation";
import { SurveyUserRoles } from "../survey/SurveyUsers";
import { StakeholderGroup } from "../../models/stakeholderGroup";
import { Actions } from "../action/Actions";
import PermissionDeniedError from "../../error/PermissionDeniedError";
import { MetricGroupManager } from "../metric/MetricGroupManager";
import { getNotificationManager } from "../notification/NotificationManager";
import { VisibleStakeholders } from '../survey/VisibleStakeholders';

export interface UserBulkDelegationServiceParams extends BulkDelegateSurveyQuery {
  delegator: UserPlain
  user: Pick<UserPlain, '_id'>;
  initiativeId: string;
  blueprintCode?: Blueprints;
}

export interface UserBulkDelegationParams extends UserBulkDelegationServiceParams {
  stakeholderUtrIds: string[];
  verifierUtrIds: string[];
}

type CombinedScopesType = Partial<Scope>;

const blueprintRepo = getBlueprintRepository();
const notificationManager = getNotificationManager();

interface DelegationParams {
  userIds: ObjectId[];
  surveyIds: ObjectId[];
  stakeholderUtrIds: ObjectId[];
  verifierUtrIds: ObjectId[];
  action: Actions;
}

interface BulkDelegationResult {
  userIds: ObjectId[],
  roles: string[];
  count: number;
}

type DelegationBatch = { roles: (keyof StakeholderGroup)[], utrIds: ObjectId[] };

export class UserBulkDelegationService {
  public static async getDelegateQuestions(params: UserBulkDelegationServiceParams): Promise<SurveyGroupsData> {
    const { initiativeId, blueprintCode = DefaultBlueprintCode } = params;


    const children = await InitiativeRepository.getAllChildrenById(initiativeId);
    this.validateInitiativeIds(params, children, initiativeId);

    const surveys = await SurveyRepository.getBulkDelegationSurveys({
      datePeriods: params.datePeriods,
      initiativeIds: params.initiativeIds,
    });
    const combinedScopes = this.getCombinedScopes(surveys) ?? {};
    const scope = { ...SurveyScope.createEmpty(), ...combinedScopes };

    const initiativeObjectId = new ObjectId(initiativeId);


    // Fetch only in scope groups
    const customMetricGroups = await InitiativeRepository.getRecursiveInitiativeKpiGroups({
      initiativeId,
      children,
      groupIds: scope.custom,
    });
    const kpi = await MetricGroupManager.getUtrCustomGroups(customMetricGroups, initiativeObjectId);
    const groups: SurveyForm[] = kpi.map(g => ({ utrGroupConfig: g }));
    // Re-add custom utrGroups, that we know are in scope
    const blueprint = await blueprintRepo.findExpandedCopy(blueprintCode);
    blueprint.forms.unshift(...groups);
    const expandedBlueprint = await blueprintRepo.getExpandedBlueprint(blueprint);

    const initiative = await Initiative.findById(initiativeId, initiativeSurveyMin).orFail().lean().exec();
    const contributions = await getBluePrintContribution().getContributions(blueprintCode);

    const utrs: UniversalTrackerPlain[] = await this.getUtrsByScopesMatch(
     {
      combinedScope: scope,
      initiative,
      blueprint: expandedBlueprint,
      contribution: contributions,
     }
    );

    const questionGroups = await SurveyCalculator.createUtrGroupQuestions(
      {
        fragmentUniversalTracker: utrs,
        list: await ValueListRepository.findByIds(getValueListIds(utrs))
      },
      expandedBlueprint.forms
    );

    return {
      customMetricGroups,
      contributions,
      questionGroups,
      scope: scope,
      fragmentUniversalTrackerValues: [],
      initiativeId: initiative._id,
      initiatives: [initiative]
    };
  }

  private static validateInitiativeIds(
    params: UserBulkDelegationServiceParams,
    children: Pick<InitiativePlain, '_id'>[],
    initiativeId: string,
  ) {
    const validIds = new Set(children.map(c => c._id.toString()));
    const idsNotValid = params.initiativeIds.filter(id => !validIds.has(id));

    if (idsNotValid.length > 0) {
      throw new PermissionDeniedError(`You cannot delegate to ${idsNotValid.length} subsidiaries due to missing access`, {}, {
        debugMessage: `User tried use bulk delegation for initiativeIds that are not withing the initiative child tree`,
        user: { _id: params.delegator._id.toString() },
        initiativeId,
        notValidIds: idsNotValid.slice(0, 100).map(String), // Limit send over ids
      })
    }
  }

  public static async executeDelegate(params: UserBulkDelegationParams): Promise<BulkDelegationResult[]> {
    const { initiativeId, user, stakeholderUtrIds, verifierUtrIds, delegator } = params;

    const children = await InitiativeRepository.getAllChildrenById(initiativeId);
    this.validateInitiativeIds(params, children, initiativeId);

    const surveys = await SurveyRepository.getBulkDelegationSurveys({
      datePeriods: params.datePeriods,
      initiativeIds: params.initiativeIds,
    });

    const action = Actions.Add;
    // Sort by the latest for notifications
    const surveyIds = surveys
      .sort((a, b) => a.effectiveDate < b.effectiveDate ? 1 : a.effectiveDate > b.effectiveDate ? -1 : 0)
      .map(s => s._id);
    const results = await UserBulkDelegationService.triggerUserDelegation({
      userIds: [user._id],
      surveyIds: surveyIds,
      stakeholderUtrIds: stakeholderUtrIds.map(id => new ObjectId(id)),
      verifierUtrIds: verifierUtrIds.map(id => new ObjectId(id)),
      action: action,
    });

    // Add the user to visibleStakeholders of the surveys
    const surveyModels = await Survey.find({ _id: { $in: surveyIds } }).orFail().exec();
    for (const survey of surveyModels) {
      VisibleStakeholders.updateUserId(survey, [user._id], action).catch(wwgLogger.error);
    }

    notificationManager.sendBulkSurveysDelegation({
      surveys,
      initiativeId: new ObjectId(initiativeId),
      questionCount: results.reduce((acc, cur) => acc + cur.count, 0),
      userId: user._id.toString(),
      action,
      delegator,
    }).catch(e => wwgLogger.error(e));

    return results;
  }

  public static async triggerUserDelegation(params: DelegationParams): Promise<BulkDelegationResult[]> {

    const { userIds, surveyIds, action } = params;
    const batches = this.generateDelegationBatches(params);

    const results: BulkDelegationResult[] = [];
    for (const batch of batches) {
      const r = await UniversalTrackerValueDelegation.applySurveyUtrsDelegations({
        userIds,
        surveyIds,
        utrIds: batch.utrIds,
        roles: batch.roles,
        action,
      });
      results.push({ userIds, roles: batch.roles, count: r.modifiedCount });
    }

    wwgLogger.info(`Applying user delegation action ${Actions.Add}`, {
      batches: batches.map(b => ({ roles: b.roles, utrIdCount: b.utrIds.length })),
      results: results.map(r => ({ ...r, userIds: r.userIds.map(String) })),
    });

    return results;
  }

  private static generateDelegationBatches(params: Pick<DelegationParams, 'stakeholderUtrIds' | 'verifierUtrIds'>) {
    const stakeholderIds = new Set(params.stakeholderUtrIds.map(String));
    const verifierIds = new Set(params.verifierUtrIds.map(String));
    const utrIdSet = new Set([...stakeholderIds, ...verifierIds]);

    const both: ObjectId[] = [];
    const stakeholderOnly: ObjectId[] = [];
    const verifierOnly: ObjectId[] = [];

    utrIdSet.forEach(id => {
      const inStakeholder = stakeholderIds.has(id);
      const inVerifier = verifierIds.has(id);
      if (inStakeholder && inVerifier) {
        both.push(new ObjectId(id))
      } else if (inStakeholder) {
        stakeholderOnly.push(new ObjectId(id))
      } else if (inVerifier) {
        verifierOnly.push(new ObjectId(id))
      }
    })

    return [
      { roles: [SurveyUserRoles.Stakeholder, SurveyUserRoles.Verifier], utrIds: both },
      { roles: [SurveyUserRoles.Stakeholder], utrIds: stakeholderOnly },
      { roles: [SurveyUserRoles.Verifier], utrIds: verifierOnly },
    ] as DelegationBatch[];
  }

  private static getCombinedScopes(surveys: BulkDelegateSurvey[]) {
    // Only the types we support at the moment
    const combined: { [k: string]: Set<string> | undefined } = {
      sdg: new Set<string>(),
      materiality: new Set<string>(),
      standards: new Set<string>(),
      frameworks: new Set<string>(),
      custom: new Set<string>(),
    };

    for (const survey of surveys) {
      const scope = survey.scope ?? {};
      Object.entries(scope).forEach(([k, scopeTags]) => {
        const set = combined[k];
        if (Array.isArray(scopeTags) && set) {
          scopeTags.forEach((value) => {
            set.add(String(value));
          });
        }
      });
    }

    const surveyScope: CombinedScopesType = {};

    Object.entries(combined).forEach(([k, scopeTags]) => {
      if (scopeTags && scopeTags.size > 0) {
        switch (k) {
          case 'custom':
            surveyScope.custom = Array.from(scopeTags).map((id) => new ObjectId(id));
            return;
          case 'standards':
          case 'frameworks':
          case 'sdg':
            surveyScope[k] = Array.from(scopeTags);
            return;
          default:
            wwgLogger.error(`Received not supported survey scope key ${k}`);
        }
      }
    });

    if (Object.keys(surveyScope).length === 0) {
      return;
    }

    return surveyScope;
  }

  public static async getUtrsByScopesMatch({
    combinedScope,
    initiative,
    blueprint,
    contribution,
  }: {
    combinedScope: Scope;
    initiative?: SurveyModelMinimalInitiative;
    blueprint: Blueprint;
    contribution: BlueprintContributions;
  }) {

    const sdgCodes = initiative ? getMaterialityCode(initiative, combinedScope.materiality) : [];
    const sdgs = [...combinedScope.sdg, ...sdgCodes].map((code) => `sdg/${code}`);

    const groups = combinedScope.custom.length > 0
        ? await MetricGroup.find({ _id: { $in: combinedScope.custom } }, { _id: 1, universalTrackers: 1 })
            .lean()
            .exec()
        : [];

    const metricGroupUtrIds = groups.reduce((acc, group) => {
      group.universalTrackers.forEach(id => acc.add(id.toString()));
      return acc;
    }, new Set<string>());

    // Blueprint already contain utrCodes for metricGroups in scope
    const utrCodes = extractVisibleUtrCodes(blueprint);

    const utrs = await UniversalTracker.find(
      { code: { $in: utrCodes }, },
      {
        _id: 1,
        code: 1,
        name: 1,
        valueLabel: 1,
        typeCode: 1,
        type: 1,
        typeTags: 1,
        alternatives: 1,
        tags: 1,
        valueValidation: 1,
        valueType: 1,
        unit: 1,
        unitType: 1,
        numberScale: 1,
      }
    ).lean().exec();

    // Find utr match combined scopes
    return utrs.filter(
      (utr) =>
        filterByStandard(combinedScope.standards ?? [], utr.type, utr.typeTags, utr.alternatives) ||
        filterByFramework(combinedScope.frameworks ?? [], utr.tags) ||
        filterBySdgCodes(contribution, sdgs, utr.code) ||
        metricGroupUtrIds.has(utr._id.toString())
    ) as UniversalTrackerPlain[];
  }
}
