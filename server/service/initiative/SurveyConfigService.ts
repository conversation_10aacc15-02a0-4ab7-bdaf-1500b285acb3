import { InitiativeRepository } from '../../repository/InitiativeRepository';
import Initiative, { InitiativePlain } from '../../models/initiative';
import InitiativeSettings, { InitiativeSettingsModel, SurveyConfig } from '../../models/initiativeSettings';
import { Types } from 'mongoose';
import { ObjectId } from 'bson';
import { SurveyImporter } from '../survey/SurveyImporter';
import { getInitiativeTreeService } from './InitiativeTreeService';
import { UnitConfig } from '../units/unitTypes';

export const getDefaultConfig = (initiative: Pick<InitiativePlain, 'permissionGroup' | 'country'>): SurveyConfig => ({
  subsidiariesEnforced: false,
  verificationRequired: true,
  evidenceRequired: true,
  noteRequired: false,
  isPrivate: false,
  unitConfig: SurveyImporter.createUnitConfig(initiative),
});

export class SurveyConfigService {
  static async findByInitiative(initiativeId: string) {
    const initiative = await Initiative.findById(initiativeId).orFail().exec();

    // Get parents until root organization only because we previously had all companies live under "companies" > ABC | DEFG | etc…  > organization (initiative with org tag).
    const parents = await InitiativeRepository.getSortedParentsUntilRootOrganization(initiative);

    const initiativeSettings = await InitiativeSettings.find({
      initiativeId: { $in: [...parents.map(({ _id }) => _id), initiative._id] },
    })
      .lean()
      .exec();

    const initiativeSettingsMap = new Map<string, InitiativeSettingsModel>(
      initiativeSettings.map((s) => [String(s.initiativeId), s])
    );

    return SurveyConfigService.getInitiativeSurveyConfig({ initiative, parents, initiativeSettingsMap });
  }

  static async getInitiativeUnitConfigMap(initiativeIds: (string | ObjectId)[]) {
    const initiativesWithParentsMap = await getInitiativeTreeService().getInitiativesWithParentsByIds(initiativeIds);
    const uniqueIds = new Set<string>(
      Array.from(initiativesWithParentsMap.values()).flatMap(({ initiative, parents }) => [
        initiative._id.toString(),
        ...parents.map(({ _id }) => _id.toString()),
      ])
    );

    const initiativeSettings =
      uniqueIds.size === 0
        ? []
        : await InitiativeSettings.find({ initiativeId: { $in: [...uniqueIds].map((id) => new ObjectId(id)) } })
            .lean()
            .exec();

    const initiativeSettingsMap = new Map<string, InitiativeSettingsModel>(
      initiativeSettings.map((s) => [String(s.initiativeId), s])
    );

    const initiativeUnitConfigMap = new Map<string, UnitConfig>();
    initiativesWithParentsMap.forEach(({ initiative, parents }, initiativeId) => {
      const { unitConfig } = SurveyConfigService.getInitiativeSurveyConfig({
        initiative,
        parents,
        initiativeSettingsMap,
      });
      initiativeUnitConfigMap.set(initiativeId, unitConfig);
    });

    return initiativeUnitConfigMap;
  }

  private static getInitiativeSurveyConfig({
    initiative,
    parents,
    initiativeSettingsMap,
  }: {
    initiative: InitiativePlain;
    parents: InitiativePlain[];
    initiativeSettingsMap: Map<string, InitiativeSettingsModel>;
  }) {
    const getSurveyConfig = ({ _id }: { _id: Types.ObjectId }): SurveyConfig =>
      initiativeSettingsMap.get(String(_id))?.surveyConfig || getDefaultConfig(initiative);

    const enforcedParent = parents.find((parent) => getSurveyConfig(parent)?.subsidiariesEnforced);
    let surveyConfig = getSurveyConfig(enforcedParent ? enforcedParent : initiative);

    if (parents.length) {
      surveyConfig = SurveyConfigService.useRootCurrencyUnit(surveyConfig, getSurveyConfig(parents[0]));
    }

    const result = { ...surveyConfig, isEnforced: !!enforcedParent };
    return result;
  }

  static useRootCurrencyUnit<T = SurveyConfig>(config: T & Pick<SurveyConfig, 'unitConfig'>, rootConfig: SurveyConfig) {
    return { ...config, unitConfig: { ...config.unitConfig, currency: rootConfig.unitConfig.currency } };
  }

  static async update(initiativeId: string, surveyConfig: SurveyConfig) {
    return InitiativeSettings.findOneAndUpdate(
      { initiativeId: new ObjectId(initiativeId) },
      { surveyConfig },
      { upsert: true, new: true }
    ).exec();
  }
}
