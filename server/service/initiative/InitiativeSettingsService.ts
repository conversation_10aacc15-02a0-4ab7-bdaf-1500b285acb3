import { FeatureDetails, FeatureTag, getFeatureDetails } from '@g17eco/core';
import InitiativeSettings, { FeatureSetting, FeatureSettingType } from '../../models/initiativeSettings';
import { ObjectId } from 'bson';
import DocumentModel, { insightDocumentProjection } from '../../models/document';
import { addDocumentUrl } from '../storage/fileStorage';
import { DocumentByType, DocumentMediaType, InsightDocument } from '../../types/document';

interface UpdateFeature extends Pick<FeatureDetails, 'active' | 'disabled' | 'disabledReason'> {
  tag: FeatureTag;
}

export class InitiativeSettingsService {
  constructor(
    private documentModel: typeof DocumentModel,
    private initiativeSettingsModel: typeof InitiativeSettings
  ) {}

  public async updateFeatures(initiativeId: string, updateData: UpdateFeature[]) {
    const featuresSetting = updateData.map(({ tag, active, disabled, disabledReason }) => {
      const details = getFeatureDetails(tag);
      return {
        ...details,
        active,
        disabled,
        disabledReason,
        name: disabled ? `${details.name} (Disabled)` : details.name,
      };
    });

    return InitiativeSettings.findOneAndUpdate(
      { initiativeId: new ObjectId(initiativeId) },
      { features: featuresSetting },
      { upsert: true, new: true }
    ).exec();
  }

  public async getFeaturesSetting(initiativeId: ObjectId): Promise<FeatureSetting[]> {
    const setting = await InitiativeSettings.findOne({ initiativeId }, { features: 1 }).lean().exec();
    if (!setting || !setting.features) {
      return [];
    }
    return setting.features.map((feature) => ({ ...feature, type: FeatureSettingType.InitiativeSetting }));
  }

  private async transformDocuments(documents: InsightDocument[]) {
    const { documents: documentsWithURLs } = await addDocumentUrl({ documents });
    return (documentsWithURLs as InsightDocument[]).reduce<DocumentByType>(
      (acc, document) => {
        const mediaType = document.metadata?.mimetype?.split('/');
        switch (mediaType?.[0]) {
          case 'video':
            acc[DocumentMediaType.Video].push(document);
            break;
          case 'image':
            acc[DocumentMediaType.Image].push(document);
            break;
          default:
            acc[DocumentMediaType.File].push(document);
        }
        return acc;
      },
      { [DocumentMediaType.Image]: [], [DocumentMediaType.Video]: [], [DocumentMediaType.File]: [] }
    );
  }

  public async getDisplayDocumentsMap(initiativeId: ObjectId) {
    const settings = await this.initiativeSettingsModel
      .findOne({ initiativeId }, { displayDocuments: 1 })
      .populate('documents', insightDocumentProjection)
      .lean<{ documents: InsightDocument[] }>()
      .exec();
    if (!settings) {
      return { [DocumentMediaType.Image]: [], [DocumentMediaType.Video]: [], [DocumentMediaType.File]: [] };
    }
    return this.transformDocuments(settings.documents);
  }

  public async handleUpdateDisplayDocuments(
    initiativeId: ObjectId,
    data: { addedIds?: ObjectId[]; removedIds?: ObjectId[] }
  ) {
    const { addedIds, removedIds } = data;
    if (!addedIds && !removedIds) {
      return [];
    }

    const validDocuments = await this.documentModel
      .find({ _id: { $in: addedIds }, ownerId: initiativeId }, { _id: 1 })
      .lean<{ _id: ObjectId }[]>()
      .exec();

    const settings = await this.initiativeSettingsModel
      .findOneAndUpdate(
        { initiativeId },
        [
          {
            $set: {
              displayDocuments: {
                $setUnion: [
                  {
                    $filter: {
                      input: {
                        $ifNull: ['$displayDocuments', []], // ensure array on upsert
                      },
                      as: 'doc',
                      cond: {
                        $not: {
                          $in: ['$$doc.documentId', removedIds],
                        },
                      },
                    },
                  },
                  // Aggregation pipeline bypasses schema defaults and hooks during an upsert so need to add defaults
                  validDocuments.map((doc) => ({ documentId: doc._id, added: new Date() })),
                ],
              },
            },
          },
        ],
        {
          new: true,
          upsert: true,
        }
      )
      .lean()
      .exec();
    return settings.displayDocuments;
  }
}

let instance: InitiativeSettingsService;
export const getInitiativeSettingsService = () => {
  if (!instance) {
    instance = new InitiativeSettingsService(DocumentModel, InitiativeSettings);
  }

  return instance;
};
