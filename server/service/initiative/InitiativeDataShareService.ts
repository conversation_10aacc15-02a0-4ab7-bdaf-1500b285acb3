import Initiative, { InitiativeDataShare, InitiativeModel, RequestReviewMethod } from '../../models/initiative';
import { ObjectId } from 'bson';
import {
  DataShareModel,
  DataShareWithRequester,
  getShareStatus,
} from '../../models/dataShare';
import { DataShareRepository, getDataShareRepository } from '../../repository/DataShareRepository';
import { RequesterService, getRequesterService } from '../share/RequesterService';
import { DataShareService, getDataShareService } from '../share/DataShareService';
import { Actor, AuditEvent } from '../audit/AuditModels';
import { AuthenticatedRequest } from '../../http/AuthRouter';
import { AuditLogger, getAuditLogger } from '../audit/AuditLogger';
import { InitiativeAudit } from '../audit/events/Initiative';
import { wwgLogger } from '../wwgLogger';
import { SurveyRepository } from '../../repository/SurveyRepository';
import { SurveyScope } from '../survey/SurveyScope';

const getShareTarget = (dataShare: DataShareModel): Actor => ({ id: dataShare._id, type: 'DataShare' })

interface UpdateDataShareTemplate {
  req: AuthenticatedRequest;
  initiativeId: string;
  updateDataShare: InitiativeDataShare;
}

interface LogUpdateTemplate {
  req: AuthenticatedRequest;
  initiative: InitiativeModel;
  dataShares: DataShareModel[];
  auditEvent: AuditEvent;
}

export class InitiativeDataShareService {
  constructor(
    private dataShareRepo: DataShareRepository,
    private requesterService: RequesterService,
    private dataShareService: DataShareService,
    private auditLogger: AuditLogger
  ) {}

  public async getDataShare(initiativeId: ObjectId | string): Promise<InitiativeDataShare | undefined> {
    const initiative = await Initiative.findById(initiativeId, { dataShare: 1 }).orFail().exec();
    return initiative.dataShare;
  }

  public async getDataSharesWithRequesters(initiativeId: string) {
    const dataShares = await this.dataShareRepo.find({ initiativeId });
    const requesters = await this.requesterService.getRequestersByIds(dataShares);

    // Only return with requesters
    const sharesWithRequesters = dataShares.reduce((acc, dataShare) => {
      const requester = requesters.find((r) => dataShare.requesterId.equals(r.requesterId));
      if (requester) {
        acc.push({ ...dataShare, status: getShareStatus(dataShare), requester });
      }
      return acc;
    }, [] as DataShareWithRequester[]);

    return sharesWithRequesters;
  }

  private async logUpdateTemplate({req, initiative, dataShares, auditEvent}: LogUpdateTemplate) {
    const shareTargets = dataShares.map(getShareTarget);
    this.auditLogger
      .fromRequest(req as AuthenticatedRequest, {
        initiativeId: initiative._id,
        auditEvent: auditEvent,
        targets: shareTargets,
      })
      .catch(wwgLogger.error);

    for (const dataShare of dataShares) {
      this.auditLogger
      .createSystem({
        req: req,
        initiativeId: await this.requesterService.getRequesterInitiativeId(dataShare),
        actor: this.auditLogger.initiativeTarget(initiative),
        auditEvent: auditEvent,
        targets: [getShareTarget(dataShare)],
      })
      .catch(wwgLogger.error);
    }
  }

  public async updateDataShareTemplate({ req, initiativeId, updateDataShare }: UpdateDataShareTemplate) {
    const initiative = await Initiative.findById(initiativeId, { dataShare: 1 }).orFail().exec();
    const currentDataShare = initiative.dataShare;

    // accept pending data shares when 'automate'
    if (updateDataShare.requestReview === RequestReviewMethod.Automate) {
      const pendingDataShares = (await this.dataShareRepo.findRaw({
        initiativeId,
        ...this.dataShareRepo.getPendingFilter(),
        ...this.dataShareRepo.excludeRestrictedFilter()
      }));

      for (let dataShare of pendingDataShares) {
        if (currentDataShare?.dataScope) {
          dataShare = this.dataShareService.replaceDataScope(dataShare, currentDataShare.dataScope);
        }
        await this.dataShareService.acceptRequestThenDeleteExisting(dataShare);
      }
      await this.logUpdateTemplate({req, initiative, dataShares: pendingDataShares, auditEvent: InitiativeAudit.dataShareAccept});
    }

    // revoke current active data shares when 'no access'
    if (updateDataShare.requestReview === RequestReviewMethod.NoAccess) {
      const dataShares = (await this.dataShareRepo.findRaw({
        initiativeId,
        ...this.dataShareRepo.getActiveFilter(),
        ...this.dataShareRepo.excludeRestrictedFilter()
      }));

      for (const dataShare of dataShares) {
        await this.dataShareService.revokeRequest(dataShare);
      }
      await this.logUpdateTemplate({req, initiative, dataShares, auditEvent: InitiativeAudit.dataShareRevoke});
    }

    // restore active data shares when 'review' and 'automate'
    if (currentDataShare?.requestReview === RequestReviewMethod.NoAccess && (updateDataShare.requestReview === RequestReviewMethod.Automate || updateDataShare.requestReview === RequestReviewMethod.Review)) {
      const revokedDataShares = (await this.dataShareRepo.findRaw({
        initiativeId,
        ...this.dataShareRepo.getRevokedFilter(),
        ...this.dataShareRepo.excludeRestrictedFilter()
      }));

      for (let dataShare of revokedDataShares) {
        await this.dataShareService.undoRevokeRequest(dataShare);
      }
      await this.logUpdateTemplate({req, initiative, dataShares: revokedDataShares, auditEvent: InitiativeAudit.dataShareAccept});
    }

    initiative.dataShare = updateDataShare;
    return initiative.save();
  }

  // This is for getting a combined scope of all surveys of initiatives
  public async getCombinedScope(initiativeId: string | ObjectId | ObjectId[] | string[]) {
    const initiativeIds = Array.isArray(initiativeId)
      ? initiativeId.map((id) => new ObjectId(id))
      : [new ObjectId(initiativeId)];

    const surveys = await SurveyRepository.findSurveys(
      { initiativeId: { $in: initiativeIds }, deletedDate: { $exists: false } },
      { scope: 1 }
    );
    return SurveyScope.mergeScopes(surveys);
  }
}

let instance: InitiativeDataShareService;
export const getInitiativeDataShareService = () => {
  if (!instance) {
    instance = new InitiativeDataShareService(
      getDataShareRepository(),
      getRequesterService(),
      getDataShareService(),
      getAuditLogger()
    );
  }
  return instance;
};
