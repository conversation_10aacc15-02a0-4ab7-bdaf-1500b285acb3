import { ObjectId } from 'bson';
import Initiative, { InitiativePlain } from '../../models/initiative';
import { InitiativeRepository, RootInitiativeData } from '../../repository/InitiativeRepository';
import { valueChainCategories } from '../../util/valueChain';
import ArchivedInitiative from '../../models/archivedInitiative';
import { ArchivedInitiativeRepository } from '../../repository/ArchivedInitiativeRepository';
import { FeatureCode } from '@g17eco/core';
import { FeatureUsageService, getFeatureUsageService } from '../organization/FeatureUsageService';
import { SurveyRepository } from '../../repository/SurveyRepository';
import {
  getRootOrganizationStatsService,
  RootOrganizationStatsService,
} from './RootOrganizationStatsService';
import { UserRepository } from '../../repository/UserRepository';
import { getBreadcrumbs } from '../../util/string';
import ContextError from '../../error/ContextError';

type InitiativeTreeNode = Pick<InitiativePlain, '_id' | 'parentId'>;

type InitiativeTreeMap = Map<string, InitiativePlain & { subsidiaryHierarchy: string, parentNames: string[] }>;

export class InitiativeTreeService {
  constructor(
    private rootOrganizationStatsService: RootOrganizationStatsService,
    private featureUsageService: FeatureUsageService
  ) {}

  private async getAllChildrenByInitiativeId(initiativeId: string) {
    const id = new ObjectId(initiativeId);
    const initiative = await Initiative.findById(id).orFail().lean().exec();

    const initiatives = await InitiativeRepository.getAllChildrenById(id, {
      tags: { $nin: valueChainCategories },
    });

    const activeInitiativeIds = initiatives.map((initiative) => initiative._id);

    const [activeSubsidiaries, archivedSubsidiaries] = await this.rootOrganizationStatsService.get(activeInitiativeIds);
    const [owners, featuresUsage] = await Promise.all([
      UserRepository.findInitiativeOwners(initiative._id),
      this.getRootInitiativeFeaturesUsage(initiative, initiatives),
    ]);

    return {
      initiatives: activeSubsidiaries,
      archivedInitiatives: archivedSubsidiaries,
      rootInitiative: { ...initiative, owners, featuresUsage, isActive: true },
    };
  }

  private async getRootInitiativeFeaturesUsage(initiative: RootInitiativeData, subsidiaries: InitiativePlain[]) {
    const subsidiaryIds = subsidiaries.map((initiative) => initiative._id);

    const [users, reportingLevels, customMetrics] = await Promise.all(
      [FeatureCode.Users, FeatureCode.ReportingLevels, FeatureCode.CustomMetrics].map((featureCode) =>
        this.featureUsageService.getUsage({
          rootInitiative: initiative,
          featureCode,
        })
      )
    );

    const surveys = {
      currentUsage: await SurveyRepository.countSurveys(subsidiaryIds),
    };

    return { users, reportingLevels, customMetrics, surveys };
  }

  private async getAllArchivedChildrenByInitiativeId(initiativeId: string) {
    const initiative = await ArchivedInitiative.findById(new ObjectId(initiativeId)).orFail().lean().exec();
    const initiatives = await ArchivedInitiativeRepository.getAllChildrenById(initiative._id, {
      tags: { $nin: valueChainCategories },
    });

    const archivedInitiatives = initiatives.map((initiative) => ({ ...initiative, isActive: false }));

    return { initiatives: archivedInitiatives, rootInitiative: { ...initiative, isActive: false } };
  }

  public async getAllChildren({ initiativeId, isActive }: { initiativeId: string; isActive: boolean }) {
    if (isActive) {
      return this.getAllChildrenByInitiativeId(initiativeId);
    }
    return this.getAllArchivedChildrenByInitiativeId(initiativeId);
  }

  public getAllChildrenFromTreeNodes(treeInitiatives: InitiativeTreeNode[], initiativeIds: ObjectId[]) {
    const hasFindingNodes = treeInitiatives.some((initiative) => initiativeIds.some((id) => id.equals(initiative._id)));

    if (!hasFindingNodes) {
      return [];
    }

    const childrenNodesSet = new Set(initiativeIds.map((i) => i.toHexString()));
    let narrowDownList = treeInitiatives;

    const getRecursiveNodes = (nodeIds: ObjectId[]) => {
      if (!nodeIds.length) {
        return;
      }

      const { children, others } = narrowDownList.reduce(
        (acc, cur) => {
          if (nodeIds.some((id) => cur.parentId?.equals(id))) {
            acc.children.push(cur);
          } else {
            acc.others.push(cur);
          }

          return acc;
        },
        { children: [] as InitiativeTreeNode[], others: [] as InitiativeTreeNode[] }
      );

      narrowDownList = others;
      children.forEach((i) => {
        childrenNodesSet.add(i._id.toHexString());
      });

      return getRecursiveNodes(children.map((i) => i._id));
    };

    getRecursiveNodes(initiativeIds);
    return Array.from(childrenNodesSet);
  }

  public async getFullTreeInitiativeMap(initiativeId: ObjectId) {
    const fullTree = await InitiativeRepository.getFullTree(initiativeId.toString());

    const initiativeMap: InitiativeTreeMap = new Map(
      fullTree.map((initiative) => [
        initiative._id.toString(),
        { ...initiative, subsidiaryHierarchy: '', parentNames: [] },
      ])
    );

    fullTree.forEach((initiative) => {
      const parentNames = this.buildParentHierarchy(initiativeMap, initiative._id.toString()).map(
        (parent) => parent.name
      );
      initiativeMap.get(initiative._id.toString())!.parentNames = parentNames;
      initiativeMap.get(initiative._id.toString())!.subsidiaryHierarchy = getBreadcrumbs([
        ...parentNames,
        initiative.name,
      ]);
    });

    return initiativeMap;
  }


public async getInitiativesWithParentsByIds(initiativeIds: (string | ObjectId)[]) {
  const rootInitiativeWithChildren = await InitiativeRepository.getOrganizationWithChildren(initiativeIds[0]);
  const initiativeMap = new Map<string, InitiativePlain>(
    rootInitiativeWithChildren.map((initiative) => [initiative._id.toHexString(), initiative])
  );

  const result = new Map<string, { initiative: InitiativePlain; parents: InitiativePlain[] }>();

  initiativeIds.forEach((initiativeId) => {
    const initiativeIdString = String(initiativeId);
    const initiative = initiativeMap.get(initiativeIdString);
    if (!initiative) {
      throw new ContextError('Initiative not found in the tree', { initiativeId });
    }

    result.set(initiativeIdString, {
      initiative,
      parents: this.buildParentHierarchy(initiativeMap, initiativeIdString),
    });
  });

  return result;
}
  // output must be in order: [root -> parent -> child -> directParentOfInitiative]
  public buildParentHierarchy<T extends Partial<InitiativePlain> = InitiativePlain>(
    initiativeMap: Map<string, T>,
    initiativeId: string
  ) {
    const parents: T[] = [];
    let currentParentId = initiativeMap.get(initiativeId)?.parentId?.toString();

    while (currentParentId && initiativeMap.has(currentParentId)) {
      const parent = initiativeMap.get(currentParentId)!;
      parents.unshift(parent);
      currentParentId = parent.parentId?.toString();
    }

    return parents;
  }
}

let instance: InitiativeTreeService;
export const getInitiativeTreeService = (): InitiativeTreeService => {
  if (!instance) {
    instance = new InitiativeTreeService(getRootOrganizationStatsService(), getFeatureUsageService());
  }
  return instance;
};
