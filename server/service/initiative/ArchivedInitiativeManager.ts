import { InitiativeRepository } from '../../repository/InitiativeRepository';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import Initiative, { InitiativeTags } from '../../models/initiative';
import ArchivedInitiative, {
  ArchiveSubsidiaryData,
  ArchiveSubsidiaryInput,
  ArchiveSubsidiaryRequest,
} from '../../models/archivedInitiative';
import ArchivedSurvey from '../../models/archivedSurvey';
import Survey, { SurveyModel } from '../../models/survey';
import UniversalTrackerValue from '../../models/universalTrackerValue';
import ArchivedUniversalTrackerValue from '../../models/archivedUniversalTrackerValue';
import { ObjectId } from 'bson';
import User, { UserModel, UserPlain } from '../../models/user';
import { getOnboardingRepository, OnboardingRepository, statusCondition, } from '../../repository/OnboardingRepository';
import { getOnboardingManager, OnboardingManager } from '../onboarding/OnboardingManager';
import { UserPermissions, UserRoles } from '../user/userPermissions';
import { ArchivedInitiativeRepository } from '../../repository/ArchivedInitiativeRepository';
import { AnyBulkWriteOperation } from 'mongoose';
import UserError from '../../error/UserError';
import ContextError from "../../error/ContextError";
import { MailchimpService } from '../email/MailChimpService';

export class ArchivedInitiativeManager {
  constructor(
    private onboardingRepo: OnboardingRepository,
    private logger: LoggerInterface,
    private onboardingManager: OnboardingManager,
    private mailchimpService: MailchimpService,
  ) {}

  private async handleArchive({ archivedId, user }: { archivedId: ObjectId; user: UserModel }) {
    this.logger.info(`Starting archive process for initiative ${archivedId}`);
    const children = await InitiativeRepository.getAllChildrenById(archivedId, undefined, { _id: 1 });
    const archivedIds = children.map((initiative) => initiative._id);
    const rootArchivedInitiative = children.find((initiative) => initiative._id.equals(archivedId));

    if (archivedIds.length <= 0 || !rootArchivedInitiative) {
      this.logger.info(`Nothing to archive for initiative ${archivedId}`, {
        archivedCount: archivedIds.length,
        rootArchivedInitiativeId: rootArchivedInitiative?._id,
      });
      return [];
    }

    await this.handlePendingOnboarding({ archivedIds, user });

    await Initiative.aggregate([
      {
        $match: {
          _id: { $in: archivedIds },
        },
      },
      {
        $merge: { into: 'archived-initiatives' },
      },
    ]).exec();
    await Initiative.deleteMany({ _id: { $in: archivedIds } }).exec();
    return archivedIds;
  }

  /**
   * Permissions must be checked before calling this function
   */
  public async archive({
    archivedId,
    reassignedUserIds,
    removedUserIds,
    reassignedInitiativeId,
    user,
  }: ArchiveSubsidiaryInput) {
    const started = Date.now();
    const archivedIds = await this.handleArchive({ archivedId, user });
    // @TODO: Should be moved to separate class in order to test these as well
    this.archiveSurveysAndUtrvs(archivedIds);
    this.applyUserChanges({ reassignedUserIds, removedUserIds, archivedIds, reassignedInitiativeId });

    this.logger.info(`Completed archive process for initiative ${archivedId}`, {
      duration: Date.now() - started,
    });
    return archivedIds;
  }

  private async archiveSurveysAndUtrvs(archiveIds: ObjectId[]) {
    const archivedSurveys = await Survey.find({ initiativeId: { $in: archiveIds } }).exec();
    // Create a map of initiativeId to surveys to process by each initiative
    // Each initiative can contain huge amount of utrvs
    // Splitting them by initiative will avoid inserting too many utrv documents at a time
    const surveysByInitiativesMap: Map<string, SurveyModel[]> = archivedSurveys.reduce((acc, survey) => {
      const initiativeId = survey.initiativeId.toString();
      const surveys = acc.get(initiativeId);
      if (surveys) {
        surveys.push(survey);
      } else {
        acc.set(initiativeId, [survey]);
      }
      return acc;
    }, new Map<string, SurveyModel[]>());

    for (const [initiativeIdString, surveys] of surveysByInitiativesMap) {
      const initiativeId = new ObjectId(initiativeIdString);
      this.logger.info(`Starting archive process for surveys of initiative ${initiativeId}`);
      await Survey.aggregate([
        {
          $match: {
            initiativeId,
          },
        },
        {
          $merge: { into: 'archived-surveys' },
        },
      ]);

      this.logger.info(`Starting reactivate process for utrvs of initiative ${initiativeId}`);
      await UniversalTrackerValue.aggregate([
        {
          $match: {
            initiativeId,
          },
        },
        {
          $merge: { into: 'archived-utrvs' },
        },
      ]);

      this.logger.info(`Starting cleanup for surveys and utrvs of initiative ${initiativeId}`);
      const result = await UniversalTrackerValue.deleteMany({ initiativeId }).exec();
      await Survey.deleteMany({ initiativeId }).exec();
      this.logger.info('Archived surveys and its utrvs', {
        surveyIds: surveys.map((survey) => survey._id.toString()),
        numberOfUtrvs: result.deletedCount,
        initiativeId: initiativeIdString,
      });
    }
  }

  private async applyUserChanges({
    reassignedUserIds,
    removedUserIds,
    archivedIds,
    reassignedInitiativeId,
  }: ArchiveSubsidiaryData<ObjectId>) {
    const combinedIds = [...reassignedUserIds, ...removedUserIds];
    if (combinedIds.length <= 0) {
      return;
    }
    const users = await User.find({ _id: { $in: combinedIds } }).lean().exec();
    const updates: AnyBulkWriteOperation[] = [];
    users
      .forEach((user) => {
        // Make sure reassignedUserId will not override existing permissions of that user in the reassigned initiative
        if (reassignedInitiativeId && reassignedUserIds.some((userId) => userId.equals(user._id))) {
          const filteredPermissions: UserPermissions[] = user.permissions.filter(({ initiativeId }) => {
            return archivedIds.every((id) => !initiativeId.equals(id));
          });

          if (user.permissions.every(({ initiativeId }) => !initiativeId.equals(reassignedInitiativeId))) {
            // add reassignedInitiativeId if we don't have it already
            // reassigned users will be downgraded to restricted user role to avoid conflicts
            // @TODO [PERMISSIONS] Should be using userPermissionService to emit events
            filteredPermissions.push({ initiativeId: reassignedInitiativeId, permissions: [UserRoles.User] });
          }

          updates.push({
            updateOne: {
              filter: { _id: user._id, 'permissions.initiativeId': { $in: archivedIds } },
              update: {
                $set: {
                  permissions: filteredPermissions,
                },
              },
            },
          });
          return;
        }

        if (removedUserIds.some((userId) => userId.equals(user._id.toString()))) {
          updates.push({
            updateOne: {
              filter: { _id: user._id, 'permissions.initiativeId': { $in: archivedIds } },
              update: { $pull: { permissions: { initiativeId: { $in: archivedIds } } } },
            },
          });
        }
      });

    if (updates.length > 0) {
      await User.bulkWrite(updates);
      this.logger.info(`Applied user changes for ${updates.length} users`, {
        reassignedInitiativeId: reassignedInitiativeId?.toString(),
        reassignedUserIds: reassignedUserIds.map((id) => id.toString()),
        removedUserIds: removedUserIds.map((id) => id.toString()),
      });
    }
  }

  private async handlePendingOnboarding({
    archivedIds,
    user,
  }: {
    archivedIds: ObjectId[];
    user: UserModel;
  }) {
    const onboardings = await this.onboardingRepo.find({
      initiativeId: { $in: archivedIds },
      status: statusCondition,
    });
    if (onboardings.length <= 0) {
      return;
    }

    for (const onboarding of onboardings) {
      await this.onboardingManager.remove(onboarding);
    }
    this.logger.info('Removed pending onboarding', {
      onboardings: onboardings.map((o) => o._id.toString()),
      archivedIds: archivedIds.map((id) => id.toString()),
      userId: user._id.toString(),
    });
  }

  public convertInput(
    input: ArchiveSubsidiaryRequest & { archivedId: string; user: UserModel }
  ): ArchiveSubsidiaryInput {
    return {
      archivedId: new ObjectId(input.archivedId),
      reassignedUserIds: input.reassignedUserIds.map((id) => new ObjectId(id)),
      removedUserIds: input.removedUserIds.map((id) => new ObjectId(id)),
      reassignedInitiativeId: new ObjectId(input.reassignedInitiativeId),
      user: input.user,
    };
  }

  public async reactivate({
    archivedInitiativeId,
    reportingParentId,
  }: {
    archivedInitiativeId: ObjectId;
    reportingParentId?: ObjectId;
  }) {
    const started = Date.now();
    this.logger.info(`Starting reactivate process for initiative ${archivedInitiativeId}`);
    const children = await ArchivedInitiativeRepository.getAllChildrenById(archivedInitiativeId, undefined, { _id: 1 });
    const archivedInitiativeIds = children.map((initiative) => initiative._id);

    if (archivedInitiativeIds.length <= 0) {
      this.logger.info(`Nothing to reactivate for initiative ${archivedInitiativeId}`, {
        archivedInitiativeId,
      });
      return [];
    }

    if (reportingParentId) {
      // Apply new parent id
      await ArchivedInitiative.findOneAndUpdate({ _id: archivedInitiativeId }, { parentId: reportingParentId })
        .orFail()
        .exec();
      this.logger.info(`Successfully applied new parent id for initiative ${archivedInitiativeId}`, {
        reportingParentId,
      });
    }

    await ArchivedInitiative.aggregate([
      {
        $match: {
          _id: { $in: archivedInitiativeIds },
        },
      },
      {
        $merge: { into: 'initiatives' },
      },
    ]).exec();

    await ArchivedInitiative.deleteMany({ _id: { $in: archivedInitiativeIds } })
      .orFail()
      .exec();

    this.reactivateSurveysAndUtrvs(archivedInitiativeIds);

    this.logger.info(`Completed reactivate process for initiative ${archivedInitiativeId}`, {
      duration: Date.now() - started,
    });

    return archivedInitiativeIds;
  }

  private async reactivateSurveysAndUtrvs(reactivatedIds: ObjectId[]) {
    const reactivatingSurveys = await ArchivedSurvey.find({ initiativeId: { $in: reactivatedIds } }).exec();
    const surveysByInitiativesMap: Map<string, SurveyModel[]> = reactivatingSurveys.reduce((acc, survey) => {
      const initiativeId = survey.initiativeId.toString();
      const surveys = acc.get(initiativeId);
      if (surveys) {
        surveys.push(survey);
      } else {
        acc.set(initiativeId, [survey]);
      }
      return acc;
    }, new Map<string, SurveyModel[]>());

    for (const [initiativeIdString, surveys] of surveysByInitiativesMap) {
      const initiativeId = new ObjectId(initiativeIdString);
      this.logger.info(`Starting reactivate process for surveys of initiative ${initiativeId}`);
      await ArchivedSurvey.aggregate([
        {
          $match: {
            initiativeId,
          },
        },
        {
          $merge: { into: 'surveys' },
        },
      ]);
      this.logger.info(`Starting reactivate process for utrvs of initiative ${initiativeId}`);
      await ArchivedUniversalTrackerValue.aggregate([
        {
          $match: {
            initiativeId,
          },
        },
        {
          $merge: { into: 'universal-tracker-values' },
        },
      ]);

      this.logger.info(`Starting cleanup for surveys and utrvs of initiative ${initiativeId}`);
      const result = await ArchivedUniversalTrackerValue.deleteMany({ initiativeId }).orFail().exec();
      await ArchivedSurvey.deleteMany({ initiativeId }).orFail().exec();
      this.logger.info('Reactivated surveys and its utrvs', {
        surveyIds: surveys.map((survey) => survey._id.toString()),
        numberOfUtrvs: result.deletedCount,
        initiativeId: initiativeIdString,
      });
    }
  }

  public async archiveCompany({ archivedId, user }: Pick<ArchiveSubsidiaryInput, 'archivedId' | 'user'>) {
    const started = Date.now();
    const archivedIds = await this.handleArchive({ archivedId, user });

    Promise.allSettled([
      this.archiveSurveysAndUtrvs(archivedIds),
      this.removedArchivedInitiativesPermissions(archivedIds),
    ]).then(([survey, permissions]) => {
      this.logger.info(`Completed all archive process for initiative ${archivedId}`, {
        duration: Date.now() - started,
        user: user._id.toString(),
        outcome: {
          survey: survey.status,
          permissions: permissions.status,
        }
      });
    }).catch((e) => this.logger.error(new ContextError('Error in archiveCompany', {
      cause: e,
      archivedId,
      userId: user._id.toString()
    })));

    this.logger.info(`Completed archive process for initiative ${archivedId}`, {
      duration: Date.now() - started,
      user: user._id.toString(),
    });

    return archivedIds;
  }

  /**
   * Remove all permissions of users with archived initiatives from the same initiative tree.
   *
   * @param {ObjectId[]} archivedIds - archived initiative ids from the same tree
   * @return {Promise<void>}
   */
  public async removedArchivedInitiativesPermissions(archivedIds: ObjectId[]): Promise<void> {
    const users = await User.find({ 'permissions.initiativeId': { $in: archivedIds } })
      .lean()
      .exec();
    const updates = users
      .map((user) => ({
          updateOne: {
            filter: { _id: user._id, 'permissions.initiativeId': { $in: archivedIds } },
            update: { $pull: { permissions: { initiativeId: { $in: archivedIds } } } },
          }
        })
      );

    if (updates.length > 0) {
      await User.bulkWrite(updates);
      this.logger.info(`Applied user changes for ${updates.length} users`, {
        removedUserIds: users.map((user) => user._id.toString()),
      });

      const updatedUserIds = updates.map((update) => update.updateOne.filter._id);
      const updatedUsers = await User.find({ _id: { $in: updatedUserIds }, permissions: { $eq: [] } }, { _id: 1, email: 1})
        .lean<Pick<UserPlain, '_id' | 'email'>[]>()
        .exec();

      this.mailchimpService.updateArchivedMailchimpTags(updatedUsers).catch(this.logger.error);
    }
  }

  /**
   * A function that retrieves available organizations based on initiativeIds and isArchive flag.
   *
   * @param {Object} param - Object containing initiativeIds as string[] and isArchive as boolean
   * @return {Promise<{ _id: ObjectId }[]>} Array of eligible organization ids
   */
  public async getEligibleOrganizations({
    initiativeIds,
    isArchive,
  }: {
    initiativeIds: string[];
    isArchive: boolean;
  }): Promise<{ _id: ObjectId }[]> {
    if (isArchive) {
      return Initiative.find(
        {
          _id: { $in: initiativeIds.map((id) => new ObjectId(id)) },
          tags: { $in: [InitiativeTags.Organization] },
        },
        { _id: 1 }
      )
        .lean<{ _id: ObjectId }[]>()
        .exec();
    }

    return ArchivedInitiative.find(
      {
        _id: { $in: initiativeIds.map((id) => new ObjectId(id)) },
        tags: { $in: [InitiativeTags.Organization] },
      },
      { _id: 1 }
    )
      .lean<{ _id: ObjectId }[]>()
      .exec();
  }

  public async hasActiveStatus(initiativeId: string) {
    const activeCount = await Initiative.findById(new ObjectId(initiativeId)).countDocuments().exec();
    if (activeCount > 0) {
      return true;
    }

    const archivedCount = await ArchivedInitiative.findById(new ObjectId(initiativeId)).countDocuments().exec();
    if (archivedCount > 0) {
      return false;
    }

    throw new UserError(`Company not found ${initiativeId}`);
  }
}

let instance: ArchivedInitiativeManager;
export const getArchivedInitiativeManager = () => {
  if (!instance) {
    instance = new ArchivedInitiativeManager(
      getOnboardingRepository(),
      wwgLogger,
      getOnboardingManager(),
      new MailchimpService({ batchSize: 500 }),
    );
  }
  return instance;
};
