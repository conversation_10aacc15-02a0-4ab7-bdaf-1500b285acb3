/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import Initiative, {
  CreateInitiativeData,
  DEFAULT_USAGE,
  DisplaySettings,
  InitiativeModel,
  InitiativePlain,
  InitiativeRating,
  InitiativeTags,
  InitiativeTypes,
  betaFeatureTags,
  usageOptions,
} from '../../models/initiative';
import { getRatingsRepository } from '../../repository/RatingsRepository';
import InitiativeGroup from '../../models/initiativeGroup';
import { generateRandomToken } from '../crypto/token';
import { getCustomerManager } from '../payment/CustomerManager';
import UserError from '../../error/UserError';
import { wwgLogger } from '../wwgLogger';
import { DataShareScopeView } from '../../models/dataShare';
import { getDataShareRepository } from '../../repository/DataShareRepository';
import { PortfolioService } from '../portfolio/PortfolioService';
import { ListcoService } from '../sgx/ListcoService.ts';
import { customDateFormat, DateFormat } from '../../util/date';
import { getSurveyExcel } from '../survey/transfer/SurveyExcel';
import { createReportingCompanyCode } from '../../util/string';
import { getInitiativeRepository, InitiativeRepository } from '../../repository/InitiativeRepository';
import { ObjectId } from 'bson';
import { getRootInitiativeService } from '../organization/RootInitiativeService';
import { FeatureDetails, getFeatureDetails } from '@g17eco/core';

const rootInitiativeService = getRootInitiativeService();

type InitiativeUpdateData = Partial<CreateInitiativeData> & { createInitiativeGroup?: boolean };

export type InitiativeUpdateProps = Pick<
  InitiativePlain,
  | 'name'
  | 'parentId'
  | 'industry'
  | 'financialEndDate'
  | 'description'
  | 'missionStatement'
  | 'geoLocation'
  | 'profile'
  | 'files'
>;

export class InitiativeManager {
  public static create(data: CreateInitiativeData) {
    const i = new Initiative(data);
    return i.save();
  }

  public static createMany(data: CreateInitiativeData[]) {
    return Initiative.create(data)
  }

  public static async createChildInitiative(body: any) {
    if (!body.parentInitiativeCode) {
      throw new Error(`Initiative requires 'parentInitiativeCode'. Aborting.`);
    }

    if (!body.initiativeName) {
      throw new Error(`Initiative requires 'initiativeName'. Aborting.`);
    }

    const parentInitiative = await Initiative.findOne({ code: body.parentInitiativeCode }).exec();
    if (!parentInitiative) {
      throw new Error(`Invalid 'parentInitiativeCode'. Aborting.`);
    }

    const initiative = new Initiative();
    initiative.code = body.initiativeCode;
    initiative.name = body.initiativeName;
    initiative.parentId = parentInitiative._id;
    await initiative.save();

    return initiative;
  }

  public static async createChildInitiativeWithUsage(data: { name: string; parentId: string; userId: ObjectId }) {
    const parentInitiative = await getInitiativeRepository().mustFindById(data.parentId);
    const createData: CreateInitiativeData = {
      code: createReportingCompanyCode(data.name),
      name: data.name,
      parentId: parentInitiative._id,
      usage: DEFAULT_USAGE, // required for tree
    };
    const created = await InitiativeManager.create(createData);

    const maxNestedOptions = 15;
    const [initiative] = await InitiativeRepository.getAllParentsById(created._id, { _id: 1 });
    const parentIds = initiative.parents.map((i) => i._id.toString());
    if (parentIds.length >= maxNestedOptions) {
      wwgLogger.warn('16th level initiative created', {
        initiativeId: created._id,
        parentIds,
        userId: data.userId,
      });
    }

    return created;
  }

  public static async updateRatings(initiative: InitiativeModel, ratings: InitiativeRating[]) {
    if (!Array.isArray(ratings)) {
      throw new Error('Ratings must be array');
    }

    const codes = ratings.map((r) => r.code);
    const ratingAgencies = await getRatingsRepository().ratingAgencies();
    const ratingCodes = ratingAgencies.map(r => r.code);

    // Resolve rating codes
    for (const c of codes) {
      if (!ratingCodes.includes(c)) {
        throw new Error(`Failed to import rating agency with code ${c}`);
      }
    }
    initiative.ratings = ratings;

    return initiative.save();
  }

  public static async editRating(initiative: InitiativeModel, update: InitiativeRating) {
    if (initiative.ratings) {
      if (update._id) {
        initiative.ratings = initiative.ratings.map(r => String(update._id) === String(r._id) ? update : r);
      } else {
        // No id, must fall back to legacy code
        initiative.ratings = initiative.ratings.map(r => {
          if (update.code === r.code) {
            r.rating = update.rating;
            r.link = update.link;
            r.linkText = update.linkText;
            r.date = update.date;
          }
          return r;
        });
      }
    }
    return initiative.save();
  }

  public static async deleteRating(initiative: InitiativeModel, code: string) {
    if (initiative.ratings) {
      initiative.ratings = initiative.ratings.filter(r => r.code !== code);
    }
    return initiative.save();
  }

  public static async deleteRatingById(initiative: InitiativeModel, id: string) {
    if (initiative.ratings) {
      initiative.ratings = initiative.ratings.filter(r => String(r._id) !== id);
    }
    return initiative.save();
  }

  public static async importRatings(code: string, ratings: InitiativeRating[]) {

    const initiative = await Initiative.findOne({ code }).exec();
    if (!initiative) {
      throw new Error(`Failed to find initiative by code: "${code}"`);
    }
    return InitiativeManager.updateRatings(initiative, ratings);
  }

  public static async createFromRequest(data: CreateInitiativeData): Promise<InitiativeModel> {
    const initiative = await InitiativeManager.create(data);
    return InitiativeManager.handleInitiativeGroup(initiative, data);
  }

  private static getToken(existingToken?: string) {
    if (existingToken) {
      return existingToken;
    }
    return generateRandomToken(12);
  }

  public static async updateSettings(initiativeId: string, data: Partial<DisplaySettings>) {
    const obj = await Initiative.findById(initiativeId).orFail().exec();
    if (data.sdgContributionChart) {
      data.sdgContributionChart.token = InitiativeManager.getToken(obj.displaySettings?.sdgContributionChart?.token);
    }
    const displaySettings = (obj.toObject()).displaySettings ?? {};
    const mergedDisplaySettings = { ...displaySettings, ...data };
    obj.set('displaySettings', mergedDisplaySettings);

    return obj.save();
  }

  public static async hasDataShare(initiativeId: string, scopeViews: DataShareScopeView[]): Promise<boolean> {
    const obj = await Initiative.findById(initiativeId).orFail().exec();
    if (!obj) {
      throw new UserError(`There was an error updating setting.`);
    }
    const dataScopeViews = obj.dataShare?.dataScope?.survey?.views ?? [];
    if (dataScopeViews.length === 0) {
      return false;
    }
    return scopeViews.some(scopeView => dataScopeViews.includes(scopeView));
  }

  public static async adminUpdate(initiativeId: string, data: any) {
    const obj = await Initiative.findById(initiativeId).orFail().exec();
    if (Object.hasOwnProperty.call(data, 'name') && !data.name) {
      throw new Error('Reporting level name cannot be empty');
    }

    if (data.industry && obj.industry) {
      // Merge frameworks
      const frameworks = Object.keys(data.industry);
      for (const f of frameworks) {
        for (const level in data.industry[f]) {
          if (!obj.industry[f]) {
            obj.industry[f] = {
              level1: '',
              level2: '',
              level3: '',
              level4: ''
            };
          }
          obj.industry[f][level] = data.industry[f][level];
        }
      }
      delete (data.industry);
    }
    obj.set(data);
    if (obj.parentId === null) {
      obj.parentId = undefined;
      // Reset this before replace, since the admin clears this field instead of submitting 'undefined' (which is technically not valid JSON)
    }
    obj.usage = obj.usage.filter((v: string) => usageOptions.includes(v))
    const initiative = await obj.save();
    return InitiativeManager.handleInitiativeGroup(initiative, data);
  }

  private static getWhiteListUpdateData(data: { [k: string]: any }) {
    const allowedProps: (keyof InitiativeUpdateProps)[] = [
      'name',
      'parentId',
      'industry',
      'financialEndDate',
      'description',
      'missionStatement',
      'geoLocation',
      'profile',
      'files',
    ];
    const whitelistData: Partial<InitiativeUpdateProps> = {};
    for (const key in data) {
      if (allowedProps.includes(key as keyof InitiativeUpdateProps)) {
        if (key === 'parentId') {
          whitelistData[key] = new ObjectId(data[key]);
          continue;
        }
        whitelistData[key as keyof InitiativeUpdateProps] = data[key];
        continue;
      }
      const msg = `Unexpected property passed: ${key}`;
      wwgLogger.error(msg);
      throw new UserError(msg);
    }
    return whitelistData;
  }

  public static async update(initiativeId: string, rawData: any) {
    const data = this.getWhiteListUpdateData(rawData);
    const obj = await Initiative.findById(initiativeId).orFail().exec();

    if (Object.hasOwnProperty.call(data, 'name') && !data.name) {
      throw new Error('Reporting level name cannot be empty');
    }

    if (data.industry && obj.industry) {
      // Merge frameworks
      const frameworks = Object.keys(data.industry);
      for (const f of frameworks) {
        for (const level in data.industry[f]) {
          if (!obj.industry[f]) {
            obj.industry[f] = {
              level1: '',
              level2: '',
              level3: '',
              level4: '',
            };
          }
          obj.industry[f][level] = data.industry[f][level];
        }
      }
      delete data.industry;
    }

    obj.set(data);

    if (obj.parentId === null) {
      obj.parentId = undefined;
      // Reset this before replace, since the admin clears this field
      // instead of submitting 'undefined' (which is technically not valid JSON)
    }
    obj.usage = obj.usage.filter((v: string) => usageOptions.includes(v));
    const initiative = await obj.save();
    // Since this is not admin update, then initiative group can be ignored as it is not allowed to change
    return initiative;
  }

  public static async handleInitiativeGroup(initiative: InitiativeModel, data: InitiativeUpdateData) {

    if (initiative.type !== InitiativeTypes.Group || initiative.initiativeGroupId) {
      return initiative;
    }

    if (data.createInitiativeGroup) {
      const group = await InitiativeGroup.create({
        code: `${initiative.code.trimEnd()}/initiative-group`,
        name: `${initiative.name} Group`,
      });
      initiative.initiativeGroupId = group._id;
      return initiative.save();
    }
    return initiative;
  }

  public static async softDelete(initiative: InitiativeModel) {

    if (initiative.deletedDate) {
      return;
    }

    initiative.deletedDate = new Date();

    // To try and 'hide' the original initiative, reomve the Organization tag
    initiative.tags = initiative?.tags?.filter(tag => tag !== InitiativeTags.Organization) ?? [];

    // change name so it's visually obvious it's deleted
    initiative.name = `${initiative.name} (Soft-deleted)`;

    // remove referrer code so it doesn't issues with PT
    initiative.referrals = undefined;

    // remove from any portfolio trackers
    await PortfolioService.removeCompanyFromAllPortfolios(initiative);

    // delete data shares
    const dataShareRepository = getDataShareRepository();
    await dataShareRepository.deleteAll(initiative);

    // deletes the Stripe user, which also removes all active subscriptions
    const customerManager = getCustomerManager();
    await customerManager.delete(initiative);

    await initiative.save();
  }

  public static async bulkUpdateFinancialEndDate() {
    const initiatives = await Initiative.find({
      financialEndDate: null,
      'metadata.sgx_issuer_name': { $ne: null },
    }).exec();

    for (const initiative of initiatives) {
      initiative.financialEndDate = ListcoService.getFinancialEndDateByIssuerName(initiative.metadata?.sgx_issuer_name);
    }

    return Initiative.bulkSave(initiatives);
  }

  public static async downloadInitiativeSubsidiaries(parentInitiative: InitiativeModel, initiatives: InitiativePlain[]) {
    const header = ['Subsidiary Name', 'ID', 'Code'];
    const data: unknown[][] = [];
    initiatives.forEach((initiative: any) => {
      data.push([initiative.name, initiative._id.toString(), initiative.code]);
    });
    const date = customDateFormat(new Date(), DateFormat.DefaultDashes);
    const fileName = `${parentInitiative.name} Subsidiaries ${date}.xlsx`;
    const workBook = await getSurveyExcel().createSheetFromArray({
      sheets: [
        {
          name: 'Subsidiaries',
          data: [header, ...data],
        },
      ],
    });
    return { fileName, workBook };
  }

  public static async getDefaultAndConfigFeatures(initiativeId: ObjectId | string, domain: string | undefined) {
    const initiative = await Initiative.findById(new ObjectId(initiativeId)).orFail().exec();
    const config = await rootInitiativeService.getConfig(initiative, { domain });

    return {
      configFeatures: config.features,
      defaultFeatures: config.defaultFeatures,
    };
  }

  private static getBetaTags = (features: FeatureDetails[]) => {
    const betaFeatureByTags = betaFeatureTags.map((tag) => ({ tag, feature: getFeatureDetails(tag) }));

    return betaFeatureByTags
      .filter(({ feature }) => features.some((f) => f.code === feature.code))
      .map(({ tag }) => tag);
  };

  public static async getDefaultAndConfigBetaFeatureTags(initiativeId: ObjectId | string) {
    const initiative = await Initiative.findById(new ObjectId(initiativeId)).orFail().lean().exec();
    const rootOrg = await rootInitiativeService.getOrganization(initiative);

    const { features: configFeatures, defaultFeatures } = await rootInitiativeService.getConfig(rootOrg, {});

    const configTags = this.getBetaTags(configFeatures);
    const defaultTags = this.getBetaTags(defaultFeatures);

    return {
      configTags,
      defaultTags,
    };
  }
}
