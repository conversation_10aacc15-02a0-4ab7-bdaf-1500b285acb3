/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { StatusStats } from "../InitiativeDataAggregator";
import { DelegationStats, DelegationStatsLegacySupport } from "../statsTypes";

interface SumDelegationStats {
  status: StatusStats;
  isContributor: boolean;
  delegationStats: DelegationStats;
  isVerifier: boolean;
}

export function sumDelegationStats({ status, isContributor, delegationStats, isVerifier }: SumDelegationStats) {
  const { created, updated, verified, rejected } = status
  const total = created + updated + verified + rejected;

  if (isContributor) {
    delegationStats.contributor += total;
    delegationStats.updated += updated;
    delegationStats.created += created;
    delegationStats.contributorRejected += rejected;
  }

  if (isVerifier) {
    delegationStats.verifier += total;
    delegationStats.verified += verified;
    delegationStats.verifierRejected += rejected;
  }

  return delegationStats;
}

interface ClearCountsParams {
  isContributor: boolean;
  isVerifier: boolean;
  utrvStats: DelegationStatsLegacySupport | undefined;
  id: string;
}

export function clearCounts({ isContributor, isVerifier, utrvStats, id }: ClearCountsParams) {
  const initiativeCount = utrvStats?.initiativeCounts[id]
  if (initiativeCount) {
    // It will be replaced by initiative level
    if (isContributor && isVerifier) {
      delete utrvStats.initiativeCounts[id];
    } else if (isContributor) {
      utrvStats.initiativeCounts[id].contributor = 0;
      utrvStats.initiativeCounts[id].contributorRejected = 0;
      utrvStats.initiativeCounts[id].updated = 0;
      utrvStats.initiativeCounts[id].created = 0;

    } else if (isVerifier) {
      utrvStats.initiativeCounts[id].verifier = 0;
      utrvStats.initiativeCounts[id].verified = 0;
      utrvStats.initiativeCounts[id].verifierRejected = 0;
    }
  }
}
