/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import BackgroundJob, { C<PERSON><PERSON><PERSON>, JobStatus, JobType, TaskStatus, TaskType } from '../../../models/backgroundJob';
import ContextError from '../../../error/ContextError';
import { JobInfo } from '../../background-process/BackgroundBaseWorkflow';
import {
  CopiedFile,
  CreatedJob,
  EvidenceFile,
  FolderStructure,
  GeneratedReports,
  InitiativeProcessContext,
  SupportedJobPlain,
  SurveyBundleInfo,
  TaskExportSetup,
  TaskExportZip,
  TaskInitiativeProcess,
  UploadParams,
  WorkflowCreate,
} from './types';
import { ObjectId } from 'bson';
import { getCurrentDateStr } from '../../../util/date';
import { createLogEntry } from '../../../service/jobs';
import {
  BackgroundJobService,
  getBackgroundJobService,
} from '../../../service/background-process/BackgroundJobService';
import { LoggerInterface, wwgLogger } from '../../../service/wwgLogger';
import { generatedUUID } from '../../../service/crypto/token';
import { InitiativeRepository } from '../../../repository/InitiativeRepository';
import { InitiativePlain } from '../../../models/initiative';
import { getInitiativeTreeService, InitiativeTreeService } from '../InitiativeTreeService';
import { DownloadMultiScope } from '../../../service/survey/scope/downloadScope';
import { SurveyWithInitiative } from '../../../models/survey';
import { DataScopeAccess } from '../../../models/dataShare';
import { VisibilityStatus } from '../../../service/survey/scope/visibilityStatus';
import { ActionList } from '../../../service/utr/constants';
import { getSimpleReportGenerator, SimpleReportGenerator } from '../../../service/custom-report/SimpleReportGenerator';
import { getSurveyExcel, SurveyExcel } from '../../../service/survey/transfer/SurveyExcel';
import { FileParserType } from '../../../service/survey/transfer/parserTypes';
import { writeCsvFile } from '../../../service/file/writer/CsvFileWriter';
import { RecordRow } from '../../../service/custom-report/constants';
import { mkDirByPathSync } from '../../../service/file/filesystem';
import { FULL_EXPORT_REPORT_COLUMNS } from '../../../service/custom-report/columnUtils';
import { writeFile } from '@sheet/core';
import { Excel, getExcel } from '../../../service/file/Excel';
import { DocumentPlain } from '../../../models/document';
import { FileStorageInterface, getStorage } from '../../../service/storage/fileStorage';
import UniversalTrackerValue, { UniversalTrackerValueExtended } from '../../../models/universalTrackerValue';
import { UniversalTrackerPlain } from '../../../models/universalTracker';
import { mimeType } from '../../../service/file/constants';
import { getReportName } from '../../../util/survey';
import { getBundleInstructions, normalizeNamePath } from './utils';
import { writeFileSync } from 'fs';
import path from 'path';
import { CompressionLevel, getZipStats, zipFolder } from '../../../service/file/archive';
import { UtrvToEvidencesMap } from '../../../service/custom-report/type';

const fullDownloadScope: DownloadMultiScope = {
  scope: {},
  access: DataScopeAccess.Full,
  visibilityStatus: VisibilityStatus.Include,
  statuses: [ActionList.Updated, ActionList.Verified, ActionList.Rejected],
  displayUserInput: true,
  displayMetricOverrides: true,
  displayTag: true,
};

type FolderInitiative = Pick<InitiativePlain, '_id' | 'name' | 'parentId'>;

export class InitiativeExportService {
  constructor(
    private logger: LoggerInterface,
    private bgJobService: BackgroundJobService,
    private initiativeTreeService: InitiativeTreeService,
    private reportGenerator: SimpleReportGenerator,
    private surveyExcel: SurveyExcel,
    private excel: Excel,
    private storage: FileStorageInterface
  ) {}

  private async getFolderStructure(rootInitiativeId: ObjectId): Promise<FolderStructure> {
    const projection = {
      _id: 1,
      name: 1,
      parentId: 1,
    };
    const rootInitiative = await InitiativeRepository.mustFindById(rootInitiativeId, projection);
    const children = await InitiativeRepository.getAllChildrenById(rootInitiativeId, undefined, projection);
    const idToInitiativeMap = new Map<string, FolderInitiative>(
      [rootInitiative, ...children].map((i) => [i._id.toString(), i])
    );

    const structure: FolderStructure = {};
    for (const [initiativeId, data] of idToInitiativeMap.entries()) {
      const parents = this.initiativeTreeService.buildParentHierarchy(idToInitiativeMap, initiativeId);
      const folderPath = [...parents, data]
        .map((p) => {
          return p._id.equals(rootInitiativeId)
            ? `[${normalizeNamePath(rootInitiative.name)}] Bundle`
            : normalizeNamePath(p.name);
        })
        .join('/');
      structure[initiativeId] = { _id: data._id, name: data.name, path: folderPath };
    }

    return structure;
  }

  private async getSetupTask({ taskId, initiativeId }: JobInfo & { initiativeId: ObjectId }): Promise<TaskExportSetup> {
    const folderStructure = await this.getFolderStructure(initiativeId);
    return {
      id: taskId,
      name: 'Set up folder structure for exporting',
      type: TaskType.ExportInitiativeSetup,
      status: TaskStatus.Pending,
      data: {
        initiativeId,
        folderStructure,
      },
    };
  }

  /**
   * Workflow eventually will contain 3 main tasks
   *  1. Create initiatives folder structure
   *  2. Process through each intitiative, generate export files and upload to its folder location
   *  3. Download root folder, zipped and re-upload
   */
  public async createJob(workflow: WorkflowCreate): Promise<CreatedJob> {
    const { initiativeId, userId, idempotencyKey } = workflow;

    const taskId = generatedUUID();
    const jobId = new ObjectId();

    const setupTask = await this.getSetupTask({ jobId, taskId, initiativeId });

    const createData: CreateJob = {
      _id: jobId,
      idempotencyKey,
      type: JobType.ExportInitiativeDataFull,
      name: `${getCurrentDateStr()} Export Full Initiative Data`,
      tasks: [setupTask],
      logs: [createLogEntry('Starting export data workflow')],
      userId,
      initiativeId,
    };
    const job = await BackgroundJob.create(createData);

    this.bgJobService.runFromJob(job).catch((e) => {
      this.logger.error(
        new ContextError(`Failed to complete background job type ${job.type}`, {
          debugMessage: `Job "${job.name}" failed to complete`,
          jobId: job._id,
          jobType: job.type,
          initiativeId: job.initiativeId,
          cause: e,
        })
      );
    });

    return {
      jobId: job._id.toString(),
      status: job.status,
    };
  }

  public getInitiativeProcessTask(context: InitiativeProcessContext): TaskInitiativeProcess {
    const taskId = generatedUUID();
    return {
      id: taskId,
      name: `Generate initiative export data: ${context.name}`,
      type: TaskType.ExportInitiativeProcess,
      status: TaskStatus.Pending,
      data: context,
    };
  }

  private getFileName(survey: SurveyWithInitiative) {
    const fileName = normalizeNamePath(getReportName(survey));
    return {
      xlsxName: `${fileName}.${FileParserType.Xlsx}`,
      csvName: `${fileName}.${FileParserType.Csv}`,
    };
  }

  private async generateReportFiles({
    reportPath,
    remoteReportPath,
    survey,
    headers,
    records,
  }: {
    reportPath: string;
    remoteReportPath: string;
    survey: SurveyWithInitiative;
    headers: string[];
    records: RecordRow[];
  }): Promise<GeneratedReports> {
    const { csvName, xlsxName } = this.getFileName(survey);
    const csvPath = `${reportPath}/${csvName}`;
    const xlsxPath = `${reportPath}/${xlsxName}`;

    // Write csv file
    await writeCsvFile({ path: csvPath, records: this.excel.getPlainSheetData(records), header: headers });

    // Write xlsx file
    const workbook = await this.surveyExcel.createSimpleReportSheet({
      headers,
      sheets: [{ name: 'Data Report', data: [headers ?? [], ...records] }],
      isBoldHeader: true,
    });
    await writeFile(workbook, xlsxPath, { type: 'file', cellStyles: true });

    // Upload reports
    const csvResponse = await this.storage.uploadFile({
      source: csvPath,
      destination: path.join(remoteReportPath, csvName),
      contentType: mimeType.csv,
    });
    const xlsxResponse = await this.storage.uploadFile({
      source: xlsxPath,
      destination: path.join(remoteReportPath, xlsxName),
      contentType: mimeType.xlsx,
    });

    return {
      csv: {
        name: csvName,
        path: csvResponse.path,
        url: csvResponse.url,
      },
      xlsx: {
        name: xlsxName,
        path: xlsxResponse.path,
        url: xlsxResponse.url,
      },
    };
  }

  private async revertFailedCopies(failedFiles: { source: string; destination: string; size: number }[]) {
    // This will revert files which are still failed after retry process of copying
    for (const file of failedFiles) {
      try {
        await this.storage.remove(file.destination);
      } catch (error) {
        wwgLogger.error(`Failed to revert file: ${file.destination}`, error);
      }
    }
  }

  private async copyRemoteFiles(fileDocs: DocumentPlain[], destination = ''): Promise<CopiedFile[]> {
    if (!destination) {
      return [];
    }

    const copiedFiles: CopiedFile[] = [];
    const failedFiles: { source: string; destination: string; size: number }[] = [];

    // Process each file sequentially
    for (const d of fileDocs) {
      const fileName = d.metadata ? d.metadata.name : String(d._id);
      const destFilePath = path.join(destination, fileName);

      try {
        const result = await this.storage.copyFile({ source: d.path, destination: destFilePath });
        copiedFiles.push({ ...result, name: fileName, size: d.size ?? 0 });
      } catch (error) {
        wwgLogger.error(`Failed to copy file: ${d.path} -> ${destFilePath}`, error);
        failedFiles.push({ source: d.path, destination: destFilePath, size: d.size ?? 0 });
      }
    }

    wwgLogger.info('Completed copying files', { copiedFiles, failedFiles, destination });

    // Revert failed files
    if (failedFiles.length > 0) {
      await this.revertFailedCopies(failedFiles);
    }

    return copiedFiles;
  }

  private async processEvidenceFiles({
    remoteEvidencePath,
    survey,
    utrvToEvidencesMap,
  }: {
    remoteEvidencePath: string;
    survey: SurveyWithInitiative;
    utrvToEvidencesMap: UtrvToEvidencesMap | undefined;
  }): Promise<SurveyBundleInfo['evidences']> {
    if (!utrvToEvidencesMap) {
      this.logger.info('Not exist any evidence in this report', { surveyId: survey._id.toString() });
      return { files: [], totalSize: 0 };
    }

    const utrvIds = Array.from(utrvToEvidencesMap.keys());
    const utrvs = await UniversalTrackerValue.find({ _id: { $in: utrvIds } })
      .populate('universalTracker')
      .lean<UniversalTrackerValueExtended[]>()
      .exec();

    if (utrvs.length === 0) {
      this.logger.info('Not exist any evidence in this report', { surveyId: survey._id.toString() });
      return { files: [], totalSize: 0 };
    }

    const utrvToUtrMap = new Map<string, UniversalTrackerPlain>(
      utrvs.map((utrv) => [utrv._id.toString(), utrv.universalTracker])
    );

    const utrvEvidenceRequests: Promise<EvidenceFile[]>[] = Array.from(utrvToEvidencesMap.entries())
      .filter(([_, { fileDocs }]) => fileDocs.length > 0)
      .map(async ([utrvId, { fileDocs }]) => {
        try {
          // remote utrv evidence directory
          const name = utrvToUtrMap.get(utrvId)?.name ?? utrvId;
          const utrvEvidenceDir = path.join(remoteEvidencePath, normalizeNamePath(name));

          const uploadedFiles = await this.copyRemoteFiles(fileDocs, utrvEvidenceDir);
          return uploadedFiles.map((file) => ({ ...file, utrvId }));
        } catch (error) {
          this.logger.info(`Failed to process evidence for utrvId: ${utrvId}`, error);
          return []; // Return empty array to avoid stopping execution
        }
      });

    // Handle both fulfilled & rejected promises
    const settledResults = await Promise.allSettled(utrvEvidenceRequests);

    // Extract successful results
    const utrvEvidences = settledResults
      .filter((result) => result.status === 'fulfilled')
      .flatMap((result) => result.value);

    const totalSize = utrvEvidences.reduce((acc, file: EvidenceFile) => acc + file.size, 0);

    return { files: utrvEvidences.flat(), totalSize };
  }

  /**
   * @param remotePath - jobs/{jobId}/[Root initiative] Bundle/Subsidiary 1/...
   * @param localPath - /tmp/{initiativeId}
   */
  public async createSurveyBundle({
    localPath,
    remotePath,
    survey,
  }: UploadParams & {
    survey: SurveyWithInitiative;
  }) {
    const {
      headers = [],
      records,
      utrvToEvidencesMap,
    } = await this.reportGenerator.getDownloadData({
      surveys: [survey],
      downloadScope: fullDownloadScope,
      initiativeId: survey.initiativeId,
      columns: FULL_EXPORT_REPORT_COLUMNS.map((code) => ({ code })),
    });

    const reportName = normalizeNamePath(getReportName(survey));
    const reportPath = path.join(localPath, reportName);
    await mkDirByPathSync(reportPath);

    const remoteReportPath = path.join(remotePath, 'Reports', reportName);
    const generatedReports = await this.generateReportFiles({ reportPath, remoteReportPath, survey, headers, records });

    const remoteEvidencePath = path.join(remoteReportPath, 'evidence');
    const evidenceDetails = await this.processEvidenceFiles({ remoteEvidencePath, survey, utrvToEvidencesMap });

    return {
      reports: generatedReports,
      evidences: evidenceDetails,
    };
  }

  public getZipTask(): TaskExportZip {
    const taskId = generatedUUID();
    return {
      id: taskId,
      name: `Zip export data of the initiative`,
      type: TaskType.ExportInitiativeZip,
      status: TaskStatus.Pending,
      data: {},
    };
  }

  private writeReadme({ localPath, initiativeName }: { localPath: string; initiativeName: string }) {
    const readmeFile = {
      content: getBundleInstructions(initiativeName),
      name: 'README.txt',
    };
    const filePath = path.join(localPath, readmeFile.name);
    writeFileSync(filePath, readmeFile.content, 'utf8');
  }

  /**
   * @param remotePath: jobs/{jobId}
   * @param localPath: /tmp/{jobId}
   */
  public async uploadBundleZip({ remotePath, localPath, initiativeName }: UploadParams & { initiativeName: string }) {
    const { totalSize } = await this.storage.downloadFolder({ remotePath, localPath, replacePath: remotePath });

    const bundlePath = path.join(localPath, `[${initiativeName}] Bundle`);
    const zipName = `[${initiativeName}] Bundle.zip`;
    const zipPath = path.join(localPath, zipName);

    this.writeReadme({ localPath: bundlePath, initiativeName });

    await zipFolder({ sourceFolder: bundlePath, zipFilePath: zipPath, compressionLevel: CompressionLevel.Maximum });

    const { size: zipSize } = getZipStats(zipPath);

    const { path: uploadedPath, url: uploadedUrl } = await this.storage.uploadFile({
      source: zipPath,
      destination: path.join(remotePath, zipName),
      contentType: mimeType.zip,
    });

    return {
      name: zipName,
      path: uploadedPath,
      url: uploadedUrl,
      zipSize,
      totalSize,
    };
  }

  public async getSignedUrl(jobId: ObjectId) {
    const job = await BackgroundJob.findOne({ _id: jobId, status: JobStatus.Completed })
      .lean<SupportedJobPlain>()
      .exec();

    if (!job) {
      throw new ContextError('Job not found', { jobId });
    }

    const tasks = job.tasks;
    const zipTask = tasks.find((t) => t.type === TaskType.ExportInitiativeZip);
    const path = zipTask?.data.path as string;

    if (!zipTask || !path) {
      throw new ContextError('Bundle url has not generated', { jobId, taskId: zipTask?.id });
    }

    const [url] = await this.storage.getSignedUrl(path);
    return url;
  }
}

let instance: InitiativeExportService;
export const getInitiativeExportService = () => {
  if (!instance) {
    instance = new InitiativeExportService(
      wwgLogger,
      getBackgroundJobService(),
      getInitiativeTreeService(),
      getSimpleReportGenerator(),
      getSurveyExcel(),
      getExcel(),
      getStorage()
    );
  }
  return instance;
};
