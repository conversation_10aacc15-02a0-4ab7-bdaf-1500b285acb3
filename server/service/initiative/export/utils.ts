/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

export const getBundleInstructions = (initiativeName: string) => `
This download contains a complete data bundle for ${initiativeName}. The structure is as follows:

Subsidiary Data
Each subsidiary has its own folder, which includes:

1. Reports Folder

A .csv file containing the report data
A .xlsx file containing the report data
Individual folders with supporting evidence for survey questions
Child Companies

2. Separate folders for each child company under the subsidiary

Copyright © ${(new Date()).getFullYear()} G17Eco
`;

export const normalizeNamePath = (name: string) => {
  // Remove control characters (ASCII codes 0-31)
  let safeName = '';
  for (let i = 0; i < name.length; i++) {
    const charCode = name.charCodeAt(i);
    if (charCode >= 32) {
      safeName += name[i];
    }
  }

  // Remove invalid filename characters
  safeName = safeName.replace(/[<>:"/\\|?*]/g, '').trim();

  // Replace multiple spaces with an underscore (but keep single spaces)
  safeName = safeName.replace(/ {2,}/g, '_');

  // If the name becomes empty, assign a default name
  if (!safeName) safeName = '_unnamed';

  // Reserved Windows file names
  const reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'];

  // If the name is reserved, prefix it with an underscore
  if (reservedNames.includes(safeName.toUpperCase())) {
    safeName = `_${safeName}`;
  }

  return safeName;
};