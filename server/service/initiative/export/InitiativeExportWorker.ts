/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { BackgroundWorker, JobResult } from '../../../service/background-process/types';
import { JobType } from '../../../models/backgroundJob';
import { LoggerInterface, wwgLogger } from '../../wwgLogger';
import { getInitiativeExportWorkflow, InitiativeExportWorkflow } from './InitiativeExportWorkflow';
import { SupportedJobModel } from './types';
import ContextError from '../../../error/ContextError';

/**
 * Workers are always triggered in Google Cloud Job runs environments
 * allow as to do heavy work without affecting normal API server operations.
 */
export class InitiativeExportWorker implements BackgroundWorker {
  constructor(private logger: LoggerInterface, protected workflow: InitiativeExportWorkflow) {}

  public canHandle(jobType: JobType): boolean {
    return jobType === JobType.ExportInitiativeDataFull;
  }

  public async process(jobId: string): Promise<JobResult> {
    this.logger.info(`Start processing, jobId: ${jobId || '-'}`);
    const job = jobId ? await this.workflow.findJob(jobId) : await this.workflow.findPendingJob();

    if (!this.workflow.canHandle(job.type)) {
      throw new ContextError(`Received invalid job ${job._id} type ${job.type}`, {
        jobId: job._id,
        type: job.type,
        created: job.created,
      });
    }

    // Progress job to the next stage, allow to retry at failed task
    return this.workflow.progressJob(job as SupportedJobModel, { retry: true });
  }
}

let instance: InitiativeExportWorker;
export const getInitiativeExportWorker = () => {
  if (!instance) {
    instance = new InitiativeExportWorker(wwgLogger, getInitiativeExportWorkflow());
  }
  return instance;
};
