import { HydratedDocument } from 'mongoose';
import { BackgroundJobPlain, JobStatus, JobType, Task, TaskType } from '../../../models/backgroundJob';
import { ObjectId } from 'bson';
import { UserPlain } from '../../../models/user';

type InitiativeSetupContext = {
  initiativeId: ObjectId;
  folderStructure: FolderStructure;
};
export interface TaskExportSetup extends Task<InitiativeSetupContext> {
  type: TaskType.ExportInitiativeSetup;
}
export type InitiativeProcessContext = {
  initiativeId: ObjectId;
  name: string;
  uploadFolder: string;
};
export interface TaskInitiativeProcess extends Task<InitiativeProcessContext> {
  type: TaskType.ExportInitiativeProcess;
}

export type ZipContext = {
  name: string;
  path: string;
  url: string;
  totalSize: number;
  zipSize: number;
};
export interface TaskExportZip extends Task<ZipContext | Record<string, unknown>> {
  type: TaskType.ExportInitiativeZip;
}
export type InitiativeExportTask = TaskExportSetup | TaskInitiativeProcess | TaskExportZip;
export type SupportedJobPlain = Omit<BackgroundJobPlain<InitiativeExportTask[]>, 'initiativeId'> & {
  type: JobType.ExportInitiativeDataFull;
  initiativeId: ObjectId;
};
export type SupportedJobModel = HydratedDocument<SupportedJobPlain>;

export interface WorkflowCreate {
  initiativeId: ObjectId;
  userId: ObjectId;
  idempotencyKey?: string;
}

export interface CreatedJob {
  jobId: string;
  status: JobStatus;
}

export type FolderStructure = {
  [initiativeId: string]: {
    _id: ObjectId;
    name: string;
    path: string;
  };
};

type FileInfo = {
  name: string;
  path: string;
  url: string;
};

export type GeneratedReports = {
  csv: FileInfo;
  xlsx: FileInfo;
};

export type EvidenceFile = {
  utrvId: string;
  size: number;
  name: string;
  path: string;
};

export type CopiedFile = Pick<EvidenceFile, 'name' | 'path' | 'size'>;

export type SurveyBundleInfo = {
  reports: GeneratedReports;
  evidences: {
    files: EvidenceFile[];
    totalSize: number;
  };
};

export type UploadParams = {
  localPath: string;
  remotePath: string;
}

export interface NotificationParams {
  job: SupportedJobModel;
  user: Pick<UserPlain, '_id' | 'email'>;
  title: string;
  content: string;
}