/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { ObjectId } from "bson";
import { StatusStats } from "./InitiativeDataAggregator";
import { StatsUser } from "../../models/user";

export interface DelegationStats {
  /** All statuses for the contributor role */
  contributor: number;
  /** Only updated for contributor role */
  updated: number;
  created: number;
  contributorRejected: number;

  /** All statuses for the verifier role */
  verifier: number;
  /** Only verified for verifier role */
  verified: number;
  verifierRejected: number;
}

export interface DelegationStatsInitiativeSupport extends DelegationStats {
  initiativeCounts: Record<string, DelegationStats>
}

export interface DelegationStatsLegacySupport extends DelegationStatsInitiativeSupport {
  /** Legacy counting */
  contributorCounts: StatusStats;
  verifierCounts: StatusStats;
}

export interface ConnectedUtrvStats {
  _id: ObjectId;
  id: string;
  parentIdString?: string;
  childIds: string[];
  status: StatusStats;
  parent?: ConnectedUtrvStats
}

export interface UserWithDelegation extends StatsUser<ObjectId> {
  _id: ObjectId;
  /** @deprecated should use utrvContributor */
  contributor?: StatusStats;
  /** @deprecated should use utrvVerifier */
  verifier?: StatusStats;
  /**
   * Ensures users from child reporting levels still appear but are disabled.
   * Used to prevent delegation on these users while maintaining visibility.
   */
  delegateDisabled?: boolean;
}

export interface UserDelegationStats extends UserWithDelegation {
  /** New options, replace frontend calculation using contributor/verifier **/
  utrvDelegation: DelegationStats;

  /**
   * Combined options that include utrv and initiative roles
   * Key different that it accounts for shared counts between direct delegation
   * and initiative delegation using initiative roles.
   */
  totalDelegation: DelegationStats;
}
