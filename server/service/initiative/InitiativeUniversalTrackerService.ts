import { ObjectId } from 'bson';
import InitiativeUniversalTracker, {
  InitiativeUniversalTrackerPlain,
  InitiativeUtrMin,
  initiativeUtrMinProjection,
  UtrvConfigValue,
} from '../../models/initiativeUniversalTracker';
import { TableColumn, UtrValueType, Variation } from '../../models/public/universalTrackerType';
import UniversalTracker, { UniversalTrackerPlain } from '../../models/universalTracker';
import { SupportedMeasureUnits, UnitConfig } from '../units/unitTypes';
import { AnyBulkWriteOperation } from 'mongoose';
import { KeysEnum } from '../../models/commonProperties';
import { getRootInitiativeService, RootInitiativeService } from '../organization/RootInitiativeService';
import { ProjectionType, PipelineStage } from 'mongoose';
import { isSimpleNumericColumnType } from '../../util/universal-trackers';

type UtrMin = Pick<
  UniversalTrackerPlain,
  '_id' | 'valueType' | 'numberScale' | 'unitType' | 'unit' | 'valueValidation'
>;

export interface PipelineStagesOnOverrideType {
  initiativeUtrLookupAndUnwind: PipelineStage[];
  initiativeUtrOverrides: PipelineStage.Project['$project'];
}

const utrMinProjection: KeysEnum<UtrMin> = {
  _id: 1,
  unit: 1,
  unitType: 1,
  numberScale: 1,
  valueType: 1,
  valueValidation: 1,
};

const GLOBAL_UPDATE_CODE = '__DATA__';

interface UpdateValueValidationParams {
  /**
   * @param {{ [key: string]: number | null }} decimal
   * key can be column's code or '__DATA__'
   *
   * 1. setting decimal for single table question
   *    then key is column's code, e.g. { decimal: { colA: 2, colB: 3 } }
   *
   * 2. setting decimal for multiple questions (including Table question), or single question that has type Number / NumericValueList / Percentage
   *    then key is '__DATA__', e.g. { decimal: { __DATA__: 2 } }
   **/
  decimal?: Record<string, number | null>;

  /**
   * @param {{ [key: string]: Partial<Record<keyof UnitConfig, string> & Record<'unitLocked' | 'numberScaleLocked', boolean>> }} unitConfig
   * key can be column's code or '__DATA__'
   *
   * 1. setting unitConfig for single table question
   *    then key is column's code, e.g. { unitConfig: { colA: { numberScale: 'millions', numberScaleLocked: true }, colB: { time: 'h', unitLocked: false } } }
   *
   * 2. setting unitConfig for multiple questions (including Table question), or single question that has type Number / NumericValueList / Percentage
   *    then key is '__DATA__', e.g. { unitConfig: { __DATA__: { numberScale: 'millions',  time: 'h', unitLocked: true, numberScaleLocked: false } } }
   **/
  unitConfig?: Record<string, Partial<Record<keyof UnitConfig, string>> & Partial<Record<'unitLocked' | 'numberScaleLocked', boolean>>>;
  variation?: Record<string, Variation[] | null>;
  initiativeId: ObjectId;
  utrIds: string[];
}

export type UtrOverridesMap = Map<string, InitiativeUtrMin>;

type ConvertedParams = Record<string, string | number | boolean | null | Variation[]>;

interface ExtendedUpdateValueValidationParams extends Omit<UpdateValueValidationParams, 'utrIds'> {
  utr: UtrMin;
  initiativeUtrMap: Map<string, InitiativeUtrMin>;
}

export class InitiativeUniversalTrackerService {
  constructor(private rootInitiativeService: RootInitiativeService) {}
  public async setOverriddenValueValidation(params: UpdateValueValidationParams) {
    const { initiativeId, utrIds, decimal, variation, unitConfig } = params;
    const utrs = await UniversalTracker.find({ _id: { $in: utrIds } }, utrMinProjection)
      .lean<UtrMin[]>()
      .exec();

    const initiativeUtrMap = await this.getUtrOverridesMap({ initiativeId, utrIds });

    const updates = utrs.reduce((acc, utr) => {
      let update = undefined;
      switch (utr.valueType) {
        case UtrValueType.Number:
        case UtrValueType.NumericValueList:
        case UtrValueType.Percentage:
          update = this.createNumericQuestionUpdate({ utr, decimal, unitConfig, variation, initiativeId });
          break;
        case UtrValueType.Table:
          update = this.createTableQuestionUpdate({
            utr,
            decimal,
            unitConfig,
            variation,
            initiativeId,
            initiativeUtrMap,
          });
          break;
        default:
          break;
      }
      if (update) {
        acc.push(update as AnyBulkWriteOperation<InitiativeUniversalTrackerPlain>);
      }
      return acc;
    }, [] as AnyBulkWriteOperation<InitiativeUniversalTrackerPlain>[]);

    return InitiativeUniversalTracker.bulkWrite(updates);
  }

  private createNumericQuestionUpdate({
    utr,
    decimal,
    variation,
    unitConfig,
    initiativeId,
  }: Pick<ExtendedUpdateValueValidationParams, 'utr' | 'decimal' | 'unitConfig' | 'variation' | 'initiativeId'>) {
    const overrides = this.convertParams({ utr, unitConfig, decimal, variation });

    const update = Object.keys(overrides).reduce((acc, key) => {
      const value = overrides[key];
      if (value === null || value === '') {
        acc.$unset = { ...acc.$unset, [key]: '' };
        return acc;
      }
      acc.$set = { ...acc.$set, [key]: value };
      return acc;
    }, {} as { $set?: Record<string, string | number | boolean | Variation[]>; $unset?: Record<string, string> });

    if (!('$set' in update) && !('$unset' in update)) {
      return;
    }

    return {
      updateOne: {
        filter: { universalTrackerId: utr._id, initiativeId },
        update,
        // only allow to upsert new document if override value is not null and empty
        upsert: Object.values(overrides).some((v) => v !== null && v !== ''),
      },
    };
  }

  private createTableQuestionUpdate({
    utr,
    decimal,
    variation,
    unitConfig,
    initiativeId,
    initiativeUtrMap,
  }: ExtendedUpdateValueValidationParams) {
    const columns = utr.valueValidation?.table?.columns;

    if (!columns || !columns.length) {
      return undefined;
    }

    const overriddenColumns = this.convertTableParams({ utr, unitConfig, decimal, variation });
    const existingColumns = initiativeUtrMap.get(utr._id.toString())?.valueValidation?.table?.columns ?? [];

    const updatedTableColumns = columns.reduce((acc, col) => {
      const { name, code, type } = col;
      if (!isSimpleNumericColumnType(col)) {
        return acc;
      }
      const overriddenColumn = overriddenColumns?.find((c) => c.code === code);
      const existingColumn = existingColumns?.find((c) => c.code === code);
      const { code: _, ...overrides } = overriddenColumn ?? {};
      acc.push({ ...existingColumn, code, type, name, ...overrides });
      return acc;
    }, [] as TableColumn[]);

    return {
      updateOne: {
        filter: { universalTrackerId: utr._id, initiativeId },
        update: { valueValidation: { table: { columns: updatedTableColumns } } },
        upsert: overriddenColumns.some((c) => c.validation?.decimal !== undefined || c.numberScaleInput || c.unitInput || c.validation?.variations),
      },
    };
  }

  private convertParams({
    utr,
    unitConfig,
    decimal,
    variation,
  }: Pick<ExtendedUpdateValueValidationParams, 'utr' | 'decimal' | 'variation' | 'unitConfig'>): ConvertedParams {
    const overrides: ConvertedParams = {};
    if (decimal) {
      overrides['valueValidation.decimal'] = decimal[GLOBAL_UPDATE_CODE];
    }

    if (variation) {
      overrides['valueValidation.variations'] = variation[GLOBAL_UPDATE_CODE];
    }

    if (!unitConfig) {
      return overrides;
    }

    const { unitType, unit } = utr;

    const overriddenUnit = unitConfig[GLOBAL_UPDATE_CODE]?.[unitType as keyof UnitConfig];
    const { unitLocked, numberScaleLocked } = unitConfig[GLOBAL_UPDATE_CODE] ?? {};

    const isCurrency = unitType === SupportedMeasureUnits.currency;
    if (!isCurrency && unit && overriddenUnit !== undefined) {
      overrides.unitInput = overriddenUnit;
      overrides.unitLocked = overriddenUnit ? !!unitLocked : null;
    }
    const overriddenNumberScale = unitConfig[GLOBAL_UPDATE_CODE]?.numberScale;
    if (overriddenNumberScale !== undefined) {
      overrides.numberScaleInput = overriddenNumberScale;
      overrides.numberScaleLocked = overriddenNumberScale ? !!numberScaleLocked : null;
    }

    return overrides;
  }

  private convertTableParams({
    utr,
    unitConfig,
    decimal,
    variation,
  }: Pick<
    ExtendedUpdateValueValidationParams,
    'utr' | 'decimal' | 'unitConfig' | 'variation'
  >): Partial<TableColumn>[] {
    if (!utr.valueValidation?.table?.columns) {
      return [];
    }

    return utr.valueValidation?.table.columns.reduce((acc, c) => {
      if (!isSimpleNumericColumnType(c)) {
        return acc;
      }
      const overriddenColumn: Partial<TableColumn> = { code: c.code };
      if (decimal) {
        // single table question: decimal[c.code], multiple questions: decimal.__DATA__
        overriddenColumn.validation = { decimal: decimal[c.code] ?? decimal[GLOBAL_UPDATE_CODE] ?? undefined };
      }
      if (variation) {
        // single table question: variation[c.code], multiple questions: variation.__DATA__
        overriddenColumn.validation = {
          ...overriddenColumn.validation,
          variations: variation[c.code] ?? variation[GLOBAL_UPDATE_CODE] ?? undefined,
        };
      }
      if (unitConfig) {
        const unitType = c.unitType as keyof UnitConfig;
        const isCurrency = unitType === SupportedMeasureUnits.currency;
        const overriddenUnit = unitConfig[c.code]?.[unitType] ?? unitConfig[GLOBAL_UPDATE_CODE]?.[unitType];
        const { unitLocked, numberScaleLocked } = unitConfig[c.code] ?? unitConfig[GLOBAL_UPDATE_CODE];
        if (!isCurrency && c.unit && overriddenUnit !== undefined) {
          overriddenColumn.unitInput = overriddenUnit;
          overriddenColumn.unitLocked = overriddenUnit ? unitLocked : undefined;
        }

        const overriddenNumberScale = unitConfig[c.code]?.numberScale ?? unitConfig[GLOBAL_UPDATE_CODE]?.numberScale;
        if (overriddenNumberScale !== undefined) {
          overriddenColumn.numberScaleInput = overriddenNumberScale;
          overriddenColumn.numberScaleLocked = overriddenNumberScale ? numberScaleLocked : undefined;
        }
      }

      acc.push(overriddenColumn);
      return acc;
    }, [] as Partial<TableColumn>[]);
  }

  public async getUtrOverridesMap({ initiativeId, utrIds }: { initiativeId: ObjectId; utrIds: (ObjectId | string)[] }) {
    const initiativeUtrs = await this.getRootInitiativeUniversalTrackers<InitiativeUtrMin>(initiativeId, {
      universalTrackerId: { $in: utrIds },
      initiativeUtrMinProjection,
    });
    return new Map<string, InitiativeUtrMin>(initiativeUtrs.map((item) => [item.universalTrackerId.toString(), item]));
  }

  public async getRootInitiativeUniversalTrackers<T = InitiativeUniversalTrackerPlain>(
    initiativeId: string | ObjectId,
    match?: Record<string, unknown>,
    projection?: ProjectionType<T>
  ) {
    const rootOrg = await this.rootInitiativeService.getOrganizationById(initiativeId);
    return InitiativeUniversalTracker.find({ initiativeId: rootOrg._id, ...match }, projection)
      .lean<T[]>()
      .exec();
  }

  public async getPipelineStagesOnOverride(
    initiativeId: string | ObjectId,
    prefix: string = ''
  ): Promise<PipelineStagesOnOverrideType> {
    const rootOrg = await this.rootInitiativeService.getOrganizationById(initiativeId);
    const utrvPrefix = prefix ? `${prefix}.` : '';
    const initiativeUtrLookupAndUnwind: PipelineStage[] = [
      {
        $lookup: {
          from: 'initiative-universal-trackers',
          let: { utrId: `$${utrvPrefix}universalTrackerId` },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [{ $eq: ['$universalTrackerId', '$$utrId'] }, { $eq: ['$initiativeId', rootOrg._id] }],
                },
              },
            },
          ],
          as: 'initiativeUtrs',
        },
      },
      {
        $unwind: { path: '$initiativeUtrs', preserveNullAndEmptyArrays: true },
      },
    ];

    const initiativeUtrOverrides: PipelineStage.Project['$project'] = {
      isPrivate: {
        $cond: {
          if: {
            $eq: ['$initiativeUtrs.utrvConfig.isPrivate', UtrvConfigValue.Required],
          },
          then: true,
          else: {
            $cond: {
              if: {
                $eq: ['$initiativeUtrs.utrvConfig.isPrivate', UtrvConfigValue.Optional],
              },
              then: false,
              else: `$${utrvPrefix}isPrivate`, // Fallback if utrvConfig is not set or isPrivate: UtrvConfigValue.Default
            },
          },
        },
      },
    };
    return { initiativeUtrLookupAndUnwind, initiativeUtrOverrides };
  }
}

let instance: InitiativeUniversalTrackerService;
export const getInitiativeUniversalTrackerService = () => {
  if (!instance) {
    instance = new InitiativeUniversalTrackerService(getRootInitiativeService());
  }

  return instance;
};
