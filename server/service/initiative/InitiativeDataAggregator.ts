/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import Survey, { SurveyModelPlain, SurveyType } from '../../models/survey';
import { BreakdownsQuery, SurveyRepository, UserBreakdownQuery } from '../../repository/SurveyRepository';
import { ObjectId } from 'bson';
import { SafeUser, UserPlain } from '../../models/user';
import UniversalTrackerValue, { UniversalTrackerValuePlain } from "../../models/universalTrackerValue";
import { StakeholderGroup } from "../../models/stakeholderGroup";
import { projectDate } from "../../util/date";


export interface StatusStats {
  created: number,
  updated: number,
  rejected: number,
  verified: number
}

export const createStatusStats = (): StatusStats => ({
  created: 0,
  updated: 0,
  rejected: 0,
  verified: 0
})

export const modifyStatusCount = (utrv: { status: string }, statusStats: StatusStats) => {
  const status = utrv.status as keyof StatusStats;
  statusStats[status] = statusStats[status] + 1 || 0
  return statusStats;
}

export interface SurveyStats
  extends Pick<
    SurveyModelPlain,
    '_id' | 'name' | 'period' | 'effectiveDate' | 'initiativeId' | 'completedDate' | 'created'
  > {
  lastUpdated: string;
  status: StatusStats;
  creator?: Pick<SafeUser, '_id' | 'firstName' | 'surname'>;
}

export interface UserStats {
  _id: ObjectId;
  status: StatusStats;
}

export interface UserStatsWithName extends UserStats, Pick<UserPlain, 'firstName' | 'surname'> {
  isContributor: boolean,
  isVerifier: boolean,
}

export type UtrvsStakeholders = Pick<StakeholderGroup, 'stakeholder' | 'verifier'>;

export interface AggregatorSurveyUtrvs {
  _id: ObjectId,
  initiativeId: ObjectId,
  visibleValues: Pick<UniversalTrackerValuePlain, 'status' | 'stakeholders'>[]
}

export class InitiativeDataAggregator {

  public async getBreakdowns(param: BreakdownsQuery): Promise<SurveyStats[]> {
    return SurveyRepository.getAggregatedStats(param)
  }

  public async getSurveysUtrvs(param: BreakdownsQuery) {

    const { initiativeIds, startDate, endDate, isCompleted } = param

    return Survey.aggregate<AggregatorSurveyUtrvs>([
      {
        $match: {
          initiativeId: { $in: initiativeIds },
          ...projectDate({ field: 'effectiveDate', startDate, endDate }),
          type: SurveyType.Default,
          deletedDate: { $exists: false },
          ...typeof isCompleted === 'boolean' ? { completedDate: { $exists: isCompleted } } : {},
        }
      },
      {
        $lookup: {
          from: 'universal-tracker-values',
          localField: 'visibleUtrvs',
          foreignField: '_id',
          as: 'visibleValues',
        },
      },
      {
        $project: {
          _id: 1,
          initiativeId: 1,
          visibleValues: {
            status: 1,
            lastUpdated: 1,
            stakeholders: 1,
          },
        },
      },
    ]);
  }

  public async getUserBreakdownsBySurvey(param: {
    initiativeIds: ObjectId[];
    surveyIds: ObjectId[];
  }): Promise<UserStatsWithName[]> {
    return SurveyRepository.getAggregatedUserStatsBySurvey(param);
  }

  public async getUserSurveysBreakdowns(param: UserBreakdownQuery): Promise<SurveyStats[]> {
    return SurveyRepository.getAggregatedUserSurveyStats(param)
  }

  public async getSurveyUtrvStakeholders(utrvIds: ObjectId[]): Promise<UtrvsStakeholders> {
    const defaultReturn: UtrvsStakeholders = { stakeholder: [], verifier: [] };

    if (utrvIds.length === 0) {
      return defaultReturn;
    }

    return UniversalTrackerValue.aggregate<UtrvsStakeholders>([
      {
        $match: {
          _id: { $in: utrvIds }
        }
      },
      {
        $group: {
          _id: null,
          stakeholdersArray: { $push: '$stakeholders.stakeholder' },
          verifierArray: { $push: '$stakeholders.verifier' },
        }
      },
      {
        $project: {
          stakeholder: {
            $reduce: {
              input: "$stakeholdersArray",
              initialValue: [],
              in: { $setUnion: ["$$value", "$$this"] }
            }
          },
          verifier: {
            $reduce: {
              input: "$verifierArray",
              initialValue: [],
              in: { $setUnion: ["$$value", "$$this"] }
            }
          }
        }
      }
    ]).exec().then(([r]) => {
      return r || defaultReturn;
    });
  }
}


let instance: InitiativeDataAggregator;
export const getInitiativeDataAggregator = () => {
  if (!instance) {
    instance = new InitiativeDataAggregator();
  }
  return instance;
}
