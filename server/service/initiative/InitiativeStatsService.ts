/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import {
  AggregatorSurveyUtrvs,
  createStatusStats,
  getInitiativeDataAggregator,
  InitiativeDataAggregator,
  modifyStatusCount,
  StatusStats,
  SurveyStats,
  UtrvsStakeholders
} from './InitiativeDataAggregator';
import { InitiativePlain } from '../../models/initiative';
import { InitiativeRepository } from '../../repository/InitiativeRepository';
import { ObjectId } from 'bson';
import { UserRepository } from '../../repository/UserRepository';
import { getFullName, SafeUser, StatsUser, UserMin, UserPlain } from '../../models/user';
import Survey, { BASED_SURVEY_TYPES, SurveyModel, SurveyModelPlain } from '../../models/survey';
import { ActionList, DataPeriods } from '../utr/constants';
import sanitize from 'sanitize-filename';
import { customDateFormat, DateFormat, getDiffAgo } from '../../util/date';
import { getSurveyExcel, SurveyExcel } from '../survey/transfer/SurveyExcel';
import { FileParserType } from '../survey/transfer/parserTypes';
import { WorkBook } from '@sheet/core';
import { DateRangeType, SurveyRepository } from '../../repository/SurveyRepository';
import { AuthenticatedRequest } from '../../http/AuthRouter';
import { toBoolean } from '../../http/query';
import UserError from "../../error/UserError";
import { UserRoles } from "../user/userPermissions";
import { KeysEnum } from "../../models/commonProperties";
import { getCsvName } from "../assurance/csvContext";
import ContextError from "../../error/ContextError";
import { SURVEY } from '../../util/terminology';
import { getTracer } from "../../telemetry/tracing";
import { LoggerInterface, wwgLogger } from "../wwgLogger";
import { ConnectedUtrvStats, DelegationStats, DelegationStatsLegacySupport, UserDelegationStats } from "./statsTypes";
import { clearCounts, sumDelegationStats } from "./stats/statsUtil";
import { getInitiativeUserService } from '../user/InitiativeUserService';

interface QueryStats {
  initiative: InitiativePlain;
  userId: string;
  isCompleted?: boolean;
  startDate?: string;
  endDate?: string;
  includeStaff: boolean;
}


export enum DelegationRoles {
  UtrvStakeholder = 'utrvStakeholder',
  UtrvVerifier = 'utrvVerifier',
  InitiativeStakeholder = 'initiativeStakeholder',
  InitiativeVerifier = 'initiativeVerifier',
}

export interface SurveyDelegationUser extends UserMin {
  delegationRoles?: DelegationRoles[];
}

export const toStatsQuery = (req: AuthenticatedRequest, initiative: InitiativePlain): QueryStats => {
  const { startDate, endDate, isCompleted } = req.query;
  return {
    initiative,
    startDate: startDate ? String(startDate) : startDate,
    endDate: endDate ? String(endDate) : endDate,
    userId: req.params.userId,
    includeStaff: Boolean(req.user.isStaff),
    isCompleted: isCompleted ? toBoolean(isCompleted) : undefined,
  };
};

type SurveyWithInitiativeName = SurveyStats & { initiativeName: string };

interface InitiativeSurveyStats {
  _id: ObjectId,
  parentId?: ObjectId;
  name: string,
}

export type InitiativeSurveyStatsQuestionStatus = InitiativeSurveyStats
  & Pick<SurveyStats, 'status'>
  & { surveys: SurveyWithInitiativeName[] }
  & { parentNames?: string[] };

type InitiativeSurveyStatsSurveyCount = InitiativeSurveyStats & { surveyCount: number };


export interface ConnectedStats extends InitiativeSurveyStatsQuestionStatus {
  id: string;
  parentIdString?: string;
  leaf?: boolean;
  children?: ConnectedStats[],
  parent?: ConnectedStats
}

interface UserStatsExcel {
  fileName: string;
  exportType: FileParserType;
  workBook: WorkBook;
}

type UserSurveyLookup = Pick<SurveyModelPlain, '_id' | 'initiativeId' | 'visibleStakeholders' | 'visibleUtrvs'>;

interface UsersForSurveyParams {
  initiative: Pick<InitiativePlain, '_id'>;
  surveyId: ObjectId;
}

interface SurveyStatsExcelParams {
  initiativeId: string | ObjectId,
  surveyId: string;
  user: UserPlain;
  origin: string | undefined;
}

interface ProcessUserStatsParams {
  surveyUtrvs: AggregatorSurveyUtrvs[];
  users: (StatsUser & { delegateDisabled?: boolean })[];
  initiatives: InitiativePlain[];
  initiative: InitiativePlain;
}

function getUtrvDelegation(stats?: Partial<DelegationStats>): DelegationStats {
  return {
    contributor: stats?.contributor ?? 0,
    updated: stats?.updated ?? 0,
    verifier: stats?.verifier ?? 0,
    verified: stats?.verified ?? 0,
    created: stats?.created ?? 0,
    contributorRejected: stats?.contributorRejected ?? 0,
    verifierRejected: stats?.verifierRejected ?? 0,
  };
}

interface ProcessStakeholderParams {
  userUtrvStats: Map<string, DelegationStatsLegacySupport>;
  userId: ObjectId
  utrv: AggregatorSurveyUtrvs['visibleValues'][0];
  initiativeId: string;
}


interface TotalDelegationParams {
  user: Pick<SafeUser, '_id' | 'permissions'>;
  initiativeMap: Map<string, ConnectedUtrvStats>;
  utrvStats: DelegationStatsLegacySupport | undefined;
}

/**
 * Abstraction around generating stats for initiatives
 * Inner implementation can be database or external service
 */
export class InitiativeStatsService {

  constructor(
    private logger: LoggerInterface,
    private dataAggregator: InitiativeDataAggregator,
    private initiativeRepo: typeof InitiativeRepository,
    private userRepo: typeof UserRepository,
    private surveyExcel: SurveyExcel,
    private initiativeUserService: ReturnType<typeof getInitiativeUserService>,
  ) {

  }

  /**
   * Load initiative tree with survey count
   */

  public async withSurveyCount(query: Pick<QueryStats, 'initiative' | 'isCompleted'>): Promise<InitiativeSurveyStatsSurveyCount[]> {

    const {
      initiative,
      isCompleted,
    } = query;

    const initiatives = await this.initiativeRepo.getMainTreeChildren(initiative._id);

    const initiativeMap = new Map<string, InitiativeSurveyStatsSurveyCount>(initiatives.map(i => {
      return [String(i._id), {
        _id: i._id,
        name: i.name,
        parentId: i.parentId,
        surveyCount: 0,
      }]
    }))

    const surveyList: Pick<SurveyModel, '_id' | 'initiativeId'>[] = await Survey.find({
      initiativeId: { $in: [...initiativeMap.keys()] },
      deletedDate: { $exists: false },
      isCompleted,
      type: { $in: BASED_SURVEY_TYPES }
    }, { _id: 1, initiativeId: 1 });

    surveyList.forEach(s => {
      const item = initiativeMap.get(String(s.initiativeId))
      if (item) {
        item.surveyCount += 1;
      }
    })

    return Array.from(initiativeMap.values());
  }

  /**
   * Load initiative tree with stats about utrv status
   */

  public async withQuestionStatus(query: QueryStats): Promise<InitiativeSurveyStatsQuestionStatus[]> {

    const {
      initiative,
      isCompleted,
      startDate,
      endDate,
    } = query;

    const initiatives = await this.initiativeRepo.getMainTreeChildren(initiative._id);
    const ids = initiatives.map(i => i._id);

    const initiativeMap = new Map<string, InitiativeSurveyStatsQuestionStatus>(initiatives.map(i => {
      return [String(i._id), {
        _id: i._id,
        name: i.name,
        parentId: i.parentId,
        parentNames: [],
        status: {
          created: 0,
          updated: 0,
          rejected: 0,
          verified: 0,
        },
        surveys: [],
      }]
    }))

    const surveyStats = await this.dataAggregator.getBreakdowns({
      initiativeIds: ids,
      isCompleted,
      ...this.getDateFromPeriod(startDate, endDate),
    });

    surveyStats.forEach(s => {
      const item = initiativeMap.get(String(s.initiativeId))
      if (item) {
        item.surveys.push({ ...s, initiativeName: item.name });
        Object.entries(s.status).forEach(([k, v]) => {
          item.status[k as keyof SurveyStats['status']] += v;
        })
      }
    })

    const aggregatedInitiatives = await this.aggregateTree(initiativeMap, initiative);
    return aggregatedInitiatives.sort((a, b) => a.name.localeCompare(b.name));
  }


  /**
   * Load stats for initiative within the tree
   */
  public async userSurveyStats(query: QueryStats): Promise<{ user: StatsUser, surveys: SurveyStats[] }> {
    const statsOptions = await this.getStatsOptions(query);
    const user = await this.userRepo.findByIdStatsUser(query.userId);

    if (!user) {
      throw new Error(`User not found with id ${query.userId}`)
    }

    const userStats = await this.dataAggregator.getUserSurveysBreakdowns({
      ...statsOptions,
      user
    });

    return {
      user,
      surveys: userStats,
    }
  }

  public async userStats(query: QueryStats): Promise<UserDelegationStats[]> {
    const statsOptions = await this.getStatsOptions(query);

    const tracer = getTracer('user-stats');

    const [surveyUtrvs, users] = await Promise.all([
      tracer.startActiveSpan('getSurveysUtrvs', async (span) => {
        const stats = await this.dataAggregator.getSurveysUtrvs(statsOptions);
        span.end();
        return stats;
      }),
      tracer.startActiveSpan('findInitiativesUsers', async (span) => {
        const users = await this.userRepo.findInitiativesUsers(statsOptions.initiativeIds, query.includeStaff);
        const mappedUsers = await this.initiativeUserService.mapUsersWithDelegatePermission(users, query.initiative._id);
        span.end();
        return mappedUsers;
      })
    ]);

    return tracer.startActiveSpan('process-users-stats', async (span) => {
      const groups = await this.processUserStats({
        surveyUtrvs,
        users,
        initiatives : statsOptions.initiatives,
        initiative: query.initiative,
      });

      span.end();
      return groups;
    })
  }

  /**
   * Load stats for initiative within the tree
   * @deprecated, replaced by usersForSurvey
   */
   public async userStatsBySurvey(initiative: InitiativePlain, surveyIds: ObjectId[]): Promise<any> {
    const initiatives = await this.initiativeRepo.getMainTreeChildren(initiative._id);
    const initiativeIds = initiatives.map(i => i._id);

    const userStats = await this.dataAggregator.getUserBreakdownsBySurvey({ initiativeIds, surveyIds });

    return userStats;
  }

  /**
   * Initiative id is where the stats are being looked at, while
   * survey could belong to one of the child initiatives
   */
  public async usersForSurvey({ initiative, surveyId }: UsersForSurveyParams): Promise<SurveyDelegationUser[]> {

    const userLookupData: KeysEnum<UserSurveyLookup> = { _id: 1, initiativeId: 1, visibleStakeholders: 1, visibleUtrvs: 1 };
    const survey = await Survey.findById(surveyId, userLookupData)
      .lean<UserSurveyLookup>()
      .orFail()
      .exec();

    const [initiativeIds, utrvDelegation] = await Promise.all([
      this.getUserLookupInitiativeIds(initiative, survey),
      this.dataAggregator.getSurveyUtrvStakeholders(survey.visibleUtrvs),
    ])

    const initiativeUsers = await this.userRepo.getUsersByInitiativeIds<UserMin & Pick<UserPlain, 'permissions'>>({
      initiativeIds,
      roles: [UserRoles.Contributor, UserRoles.Verifier],
      projection: {
        _id: 1,
        firstName: 1,
        surname: 1,
        permissions: 1,
      },
    });

    const ids = initiativeIds.map(({ _id }) => _id.toHexString());

    const users = initiativeUsers.reduce((acc, u) => {

      let isStakeholder = false;
      let isVerifier = false;
      const delegationRoles: DelegationRoles[] = [];
      for (const { permissions, initiativeId } of u.permissions) {

        if (ids.includes(initiativeId.toHexString())) {
          if (!isStakeholder && permissions.includes(UserRoles.Contributor)) {
            isStakeholder = true;
            delegationRoles.push(DelegationRoles.InitiativeStakeholder);
          }

          if (!isVerifier && permissions.includes(UserRoles.Verifier)) {
            isVerifier = true;
            delegationRoles.push(DelegationRoles.InitiativeVerifier);
          }
        }

        if (isStakeholder && isVerifier) {
          break;
        }
      }

      acc.push({ ...u, delegationRoles });
      return acc;
    }, [] as SurveyDelegationUser[]);

    const loadedUserIds = initiativeUsers.map(u => u._id.toHexString());
    const surveyUserIds = survey.visibleStakeholders.filter(id => !loadedUserIds.includes(id.toHexString()));
    const surveyUsers = await this.userRepo.getUsersMin(surveyUserIds);

    return this.getSurveyUsers(users.concat(surveyUsers), utrvDelegation);
  }

  private async getSurveyUsers(users: SurveyDelegationUser[], utrvDelegation: UtrvsStakeholders) {

    const stakeholderIds = utrvDelegation.stakeholder.map(String);
    const verifierIds = utrvDelegation.verifier.map(String);

    return users.map(u => {
      const roles = u.delegationRoles ?? [];
      const userId = u._id.toString();

      if (stakeholderIds.includes(userId)) {
        roles.push(DelegationRoles.UtrvStakeholder);
      }

      if (verifierIds.includes(userId)) {
        roles.push(DelegationRoles.UtrvVerifier);
      }

      // Excluding things like .permissions, so only send back what is needed
      return {
        _id: u._id,
        firstName: u.firstName,
        surname: u.surname,
        delegationRoles: roles,
      } satisfies SurveyDelegationUser
    });
  }

  /**
   * Complicated way of finding everything in between. Suggestions are welcomed
   * [initiative, ...any initiatives in between or none..., surveyInitiative]
   *
   * Would be easier to just get all parents, but Admin Dashboard have a requirement
   * to show current level users and everything bellow. In survey case it must
   * be direct parents up to and including the current level that requested information.
   */
  private async getUserLookupInitiativeIds(initiative: Pick<InitiativePlain, '_id'>, survey: UserSurveyLookup) {
    if (survey.initiativeId.equals(initiative._id)) {
      // Same level, just return the initiative id
      return [initiative._id];
    }

    // Otherwise we need figure out the path from the survey initiative to the current level initiative
    // We only want to include current level and below, because it's sorted we can slice the ids
    // [rootOrg, parentA, parentB, directParentOfSurveyInitiative]
    // where initiative we passed should be rootOrg or one of the parents
    const [surveyInitiative] = await this.initiativeRepo.getAllParentsUptoId({
      initiativeId: survey.initiativeId,
      parentId: initiative._id,
      project: { _id: 1, parentId: 1 }
    });

    // NOTE: Does not include the current level that was passed in and just returns parents
    // also exclude the parentId in this case initiative,
    // essentially everything between surveyInitiative and current level initiative
    const parentIds = surveyInitiative.parents.map(i => i._id);

    const allParentIds = surveyInitiative.parents
      .map((p) => p.parentId)
      .concat(surveyInitiative.parentId)
      .filter(Boolean)
      .map(String)

    // Current level request must be one of the parent ids
    const id = initiative._id.toString();
    if (!allParentIds.includes(id)) {
      throw new UserError(`Unable to find users for given ${SURVEY.SINGULAR}`, {
        debugMessage: 'Survey initiative is not found in the initiative tree',
        initiativeId: initiative._id,
        surveyId: survey._id,
      });
    }

    return [initiative._id, surveyInitiative._id, ...parentIds];
  }

  private async getStatsOptions(query: QueryStats) {
    const {
      initiative,
      isCompleted,
      startDate = '',
      endDate,
    } = query;

    const initiatives = await this.initiativeRepo.getMainTreeChildren(initiative._id);

    return {
      initiatives,
      initiativeIds: initiatives.map(i => i._id),
      isCompleted,
      ...this.getDateFromPeriod(startDate, endDate),
    };
  }

  public async userStatsExcel(data: { user: StatsUser; surveys: SurveyStats[] }): Promise<UserStatsExcel> {
    const { user, surveys } = data;
    const userName = `${user.firstName} ${user.surname}`;

    const sheetData = surveys.map((row) => ({
      Username: userName,
      [`${SURVEY.CAPITALIZED_SINGULAR} type`]: row.period,
      [`${SURVEY.CAPITALIZED_SINGULAR} title`]: row.name,
      [`${SURVEY.CAPITALIZED_SINGULAR} status`]: row.completedDate ? 'Completed' : 'In Progress',
      Updated: customDateFormat(user.lastLogin, DateFormat.Humanize, false),
      Unanswered: row?.status?.created || 0,
      Answered: row?.status?.updated || 0,
      Rejected: row?.status?.rejected || 0,
      Verified: row?.status?.verified || 0,
    }));

    const exportType = FileParserType.Xlsx;
    const workBook = await this.surveyExcel.createSheet({
      sheets: [{ name: `Admin Dashboard - User View`, data: sheetData }],
    });
    const date = customDateFormat(new Date(), DateFormat.DefaultDashes);
    const fileName = sanitize(`${date} ${userName} view.${exportType}`);
    return { fileName, exportType, workBook };
  }

  private async aggregateTree(initiativeMap: Map<string, InitiativeSurveyStatsQuestionStatus>, initiative: InitiativePlain) {

    const values: ConnectedStats[] = Array.from(initiativeMap.values()).map(i => {
      return {
        ...i,
        id: i._id.toHexString(),
        parentIdString: i.parentId?.toHexString(),
      }
    });

    const parentId = initiative._id.toHexString();
    const parentNode = values.find(({ id }) => id === parentId);

    if (!parentNode) {
      return Array.from(initiativeMap.values());
    }

    const leafNodes: ConnectedStats[] = [];
    // Generate tree and create references
    let children = [parentNode]
    let nextChildren: ConnectedStats[] = [];
    while (children.length > 0) {
      nextChildren = []
      children.forEach(c => {
        const innerChildren = values.filter(v => v.parentIdString === c.id)

        const value = initiativeMap.get(c.id)
        if (value) {
          const parent = initiativeMap.get(String(c.parentId))
          value.parentNames = parent ? [...(parent.parentNames ?? []), parent.name] : []
        }

        if (innerChildren.length === 0) {
          leafNodes.push(c);
          return;
        }

        innerChildren.forEach(inner => {
          inner.parent = c;
          nextChildren.push(inner);
        })
      })
      children = nextChildren;
    }

    const keys = Object.keys(parentNode.status) as (keyof SurveyStats['status'])[]

    // Traverse back from leaf nodes to the top
    leafNodes.forEach(n => {
      let parent = n.parent;
      while (parent) {

        // Update original map values that do not have parent-child references
        const parentInitiative = initiativeMap.get(parent.id)
        if (parentInitiative) {
          keys.forEach(k => {
            parentInitiative.status[k] += n.status[k]
          })
        }
        parent = parent.parent;
      }
    })

    return Array.from(initiativeMap.values());
  }

  private getDateFromPeriod(startDate: string | undefined, endDate: string | undefined): DateRangeType {
    return { ...(startDate && { startDate: new Date(startDate) }), ...(endDate && { endDate: new Date(endDate) }) };
  }

  private getTotalCount(status?: StatusStats) {
    return status ? status.created + status.updated + status.verified + status.rejected : 0;
  }

  public async initiativeStatsExcel(query: QueryStats) {

    const reportData = await this.withQuestionStatus(query);
    const usersStats = await this.userStats(query);
    const summaryData: unknown[][] = []
    const surveyData: unknown[][] = []
    const usersData: unknown[][] = []

    const summaryHeaders = ["Level Name", `Total ${SURVEY.CAPITALIZED_PLURAL}`, `% of ${SURVEY.PLURAL} Completed`, "Total Questions", "Unanswered", "Answered", "Rejected", "Verified"]
    const surveyheaders = ["Level Name", "Level location", `${SURVEY.CAPITALIZED_SINGULAR} Type`, `${SURVEY.CAPITALIZED_SINGULAR} Date`, `${SURVEY.CAPITALIZED_SINGULAR} Name`, "Suvey status", "Last updated", "Total Questions", "Unanswered", "Answered", "Rejected", "Verified"]
    const usersHeaders = ["User name", "Last online", "Contributor", "Answered", "Verifier", "Verified"]

    reportData.forEach(initiative => {
      const completed = initiative.surveys.filter((s) => { return s.completedDate }).length

      if (
        !initiative.parentId ||
        String(initiative._id) === String(query.initiative._id) ||
        String(initiative.parentId) === String(query.initiative._id)
      ) {
        summaryData.push(
          [
            initiative.name,
            initiative.surveys.length,
            completed ? Math.floor(completed / initiative.surveys.length * 100) : 0,
            initiative.status.created + initiative.status.rejected + initiative.status.updated + initiative.status.verified,
            initiative.status.created,
            initiative.status.updated,
            initiative.status.rejected,
            initiative.status.verified
          ]
        );
      }

      initiative.surveys.forEach(survey => {
        surveyData.push(
          [
            survey.initiativeName,
            [...(initiative.parentNames ?? []), survey.initiativeName].join("/"),
            survey.period ?? DataPeriods.Yearly,
            customDateFormat(survey.effectiveDate, DateFormat.YearMonth),
            survey.name ?? "",
            survey.completedDate ? "Completed" : "In progress",
            getDiffAgo(survey.lastUpdated),
            survey.status.created + survey.status.rejected + survey.status.updated + survey.status.verified,
            survey.status.created,
            survey.status.updated,
            survey.status.rejected,
            survey.status.verified
          ]
        )
      })
    });
    usersStats.forEach(async (user) => {
      usersData.push([
        getFullName(user, ''),
        customDateFormat(user.lastLogin ?? '', DateFormat.YearMonth),
        this.getTotalCount(user.contributor),
        user.contributor?.updated ?? 0,
        this.getTotalCount(user.verifier),
        user.contributor?.verified ?? 0,
      ]);
    });
    const date = customDateFormat(new Date(), DateFormat.DefaultDashes);
    const fileName = `Admin Dashboard ${date}.xlsx`;
    const workBook = await this.surveyExcel.createSheetFromArray({
      sheets: [
        {
          name: 'Summary',
          data: [summaryHeaders, ...summaryData],
        },
        {
          name: SURVEY.CAPITALIZED_PLURAL,
          data: [surveyheaders, ...surveyData],
        },
        {
          name: 'Users',
          data: [usersHeaders, ...usersData],
        }
      ]
    });
    return { fileName, workBook };
  }


  /**
   * This should be heavily aligned with usersForSurvey as mostly delegation information
   */
  public async surveyStatsExcel({ initiativeId, surveyId, user, origin}: SurveyStatsExcelParams) {

    const reportData = await SurveyRepository.getSurveyData(surveyId, user, origin);
    const questionsData: unknown[][] = [];

    if (!reportData?.fragmentUniversalTrackerValues) {
      throw new ContextError(`No survey stats surveyId ${surveyId} uid ${user._id}`, {
        surveyId,
        userId: user._id,
      });
    }

    const users = await this.usersForSurvey({
      initiative: { _id: new ObjectId(initiativeId)},
      surveyId: new ObjectId(reportData._id),
    });

    const surveyDelegationHeaders = ["Type code", "Question title", "Contributors", "Verifiers", "Question status"];

    const utrvByUtrId = new Map(reportData.fragmentUniversalTrackerValues.map(el => {
      return [String(el.universalTrackerId), el];
    }));

    const userMap = new Map(users.map(u => [u._id.toString(), u]))

    const initiativeLevelUsers = users.reduce((acc, user) => {
      if (user.delegationRoles?.includes(DelegationRoles.InitiativeStakeholder)) {
        acc.stakeholders.push(user._id.toString())
      }
      if (user.delegationRoles?.includes(DelegationRoles.InitiativeVerifier)) {
        acc.verifiers.push(user._id.toString())
      }
      return acc
    }, { stakeholders: [] as string[], verifiers: [] as string[] })

    const concatUserNames = (userIds: Set<string>) => {
      const list: string[] = [];
      userIds.forEach((id) => {
        const user = userMap.get(id);
        if (user) {
          list.push(getFullName(user, id))
        }
      });
      return list.join(', ')
    }

    // build rows
    reportData.questionGroups.forEach(group => {
      group.questions.forEach(question => {

        const utrv = utrvByUtrId.get(String(question.utr._id));
        const stakeholderIds = new Set([...(utrv?.stakeholders?.stakeholder.map(String) ?? []), ...initiativeLevelUsers.stakeholders]);
        const verifierIds = new Set([...(utrv?.stakeholders?.verifier.map(String) ?? []), ...initiativeLevelUsers.verifiers]);

        questionsData.push([
          question.utr.typeCode ?? '',
          question.utr.name,
          concatUserNames(stakeholderIds),
          concatUserNames(verifierIds),
          utrv?.status === ActionList.Created ? 'Unanswered' : utrv?.status,
        ])
      })
    })

    const name = getCsvName({
      initiative: reportData.initiatives?.[0],
      survey: reportData,
      _id: reportData._id,
      type: 'Survey',
    })

    const fileName = `${SURVEY.CAPITALIZED_SINGULAR} ${name}.xlsx`;
    const workBook = await this.surveyExcel.createSheetFromArray({
      sheets: [
        {
          name: `${SURVEY.CAPITALIZED_SINGULAR} Delegation`,
          data: [surveyDelegationHeaders, ...questionsData],
        },
      ]
    });
    return { fileName, workBook };
  }

  private async processUserStats(params: ProcessUserStatsParams): Promise<UserDelegationStats[]> {
    const { surveyUtrvs, users, initiatives, initiative } = params;
    const tracer = getTracer('process-user-stats');

    const { userUtrvStats, initiativeGroupStats } = tracer.startActiveSpan('processed stats', (span) => {
      const r = this.getProcessedStats(surveyUtrvs);
      span.end();
      return r;
    });

    this.logger.info(`Processing ${initiativeGroupStats.size} initiatives, and ${userUtrvStats.size} users`, {
      surveyCount: surveyUtrvs.length,
      usersCountForUtrv: userUtrvStats.size,
    });

    const initiativeMap = new Map(initiatives.map(i => {
      const id = String(i._id);
      return [id, {
        _id: i._id,
        id,
        parentIdString: i.parentId?.toHexString(),
        parentIds: [],
        childIds: [],
        status: initiativeGroupStats.get(id) ?? createStatusStats(),
      } as ConnectedUtrvStats]
    }))

    const values = Array.from(initiativeMap.values());

    const parentId = initiative._id.toHexString();
    const parentNode = values.find(({ id }) => id === parentId);

    if (!parentNode) {
      throw new ContextError(`Unable to find parent node for ${initiative._id}`)
    }

    this.populateChildIds(parentNode, values, initiativeMap);

    const defaultStats = createStatusStats();

    return tracer.startActiveSpan('map', (span) => {
      const mappedUsers = users.map((user) => {
        const id = String(user._id);
        const utrvStats = userUtrvStats.get(id);
        const totalDelegation = this.processUserTotalDelegation({
          user,
          initiativeMap,
          utrvStats: utrvStats
        });

        return {
          ...user,
          contributor: utrvStats?.contributorCounts ?? defaultStats,
          verifier: utrvStats?.verifierCounts ?? defaultStats,
          utrvDelegation: getUtrvDelegation(utrvStats),
          totalDelegation,
        } satisfies UserDelegationStats;
      });

      span.end();
      return mappedUsers;
    });
  }

  private processUserTotalDelegation({ user, initiativeMap, utrvStats }: TotalDelegationParams) {
    const topLevelIds = {} as Record<string, DelegationStats | undefined>;
    const allUsedIds = new Set<string>();

    user.permissions.forEach((p) => {
      const id = p.initiativeId.toHexString();

      const group = initiativeMap.get(id);
      if (!group || allUsedIds.has(id)) {
        return; // Not part of the tree or already processed id.
      }

      const isContributor = p.permissions.includes(UserRoles.Contributor);
      const isVerifier = p.permissions.includes(UserRoles.Verifier);
      if (!isContributor && !isVerifier) {
        return;
      }

      allUsedIds.add(id)
      clearCounts({ isContributor, isVerifier, utrvStats, id });

      const initialValue = sumDelegationStats({
        status: group.status,
        isContributor: isContributor,
        isVerifier: isVerifier,
        delegationStats: getUtrvDelegation(),
      });

      topLevelIds[id] = group.childIds.reduce((acc, childId) => {
        allUsedIds.add(childId)
        if (topLevelIds[childId]) {
          // No longer root level should be removed
          delete topLevelIds[childId]
        }
        clearCounts({ isContributor, isVerifier, utrvStats, id: childId });

        const group = initiativeMap.get(childId);
        if (!group) {
          return acc;
        }

        return sumDelegationStats({
          status: group.status,
          isContributor,
          delegationStats: acc,
          isVerifier: isVerifier
        });
      }, initialValue);
    });

    const utrvDelegation = getUtrvDelegation();
    // Add top level counts, then go through remaining utrv counts and add those
    Object.values(topLevelIds)
      .concat(Object.values(utrvStats?.initiativeCounts ?? {}))
      .forEach((stats) => {
      if (stats) {
        utrvDelegation.contributor += stats.contributor;
        utrvDelegation.created += stats.created;
        utrvDelegation.updated += stats.updated;
        utrvDelegation.contributorRejected += stats.contributorRejected;

        utrvDelegation.verifier += stats.verifier;
        utrvDelegation.verified += stats.verified;
        utrvDelegation.verifierRejected += stats.verifierRejected;
      }
    });

    return utrvDelegation;
  }

  private populateChildIds(parentNode: ConnectedUtrvStats, values: ConnectedUtrvStats[], initiativeMap: Map<string, ConnectedUtrvStats>) {
    // Generate tree and create references
    let children = [parentNode]
    let nextChildren: ConnectedUtrvStats[] = [];
    while (children.length > 0) {
      nextChildren = []
      children.forEach(c => {
        const innerChildren = values.filter(v => v.parentIdString === c.id)
        c.childIds = innerChildren.map(i => i.id);

        const initiative = initiativeMap.get(c.id)
        if (initiative && c.parentIdString) {
          const parent = initiativeMap.get(c.parentIdString);
          if (parent) {
            parent.childIds.push(...c.childIds)
          }
        }

        innerChildren.forEach(inner => {
          inner.parent = c;
          nextChildren.push(inner);
        });
      });
      children = nextChildren;
    }
  }

  private getProcessedStats(surveyUtrvs: AggregatorSurveyUtrvs[]) {
    const userUtrvStats = new Map<string, DelegationStatsLegacySupport>();
    const initiativeGroupStats = new Map<string, StatusStats>();

    surveyUtrvs.forEach((survey) => {
      const initiativeId = survey.initiativeId.toString();
      survey.visibleValues.forEach((utrv) => {

        utrv.stakeholders?.stakeholder.forEach((userId) => {
          this.processStakeholder({
            userUtrvStats,
            userId,
            utrv,
            initiativeId
          });
        });

        utrv.stakeholders?.verifier.forEach((userId) => {
          this.processVerifier({
            userUtrvStats,
            userId,
            utrv,
            initiativeId
          });
        });

        const initiativeStatus = initiativeGroupStats.get(initiativeId);
        if (initiativeStatus) {
          modifyStatusCount(utrv, initiativeStatus)
        } else {
          initiativeGroupStats.set(initiativeId, modifyStatusCount(utrv, createStatusStats()));
        }

      })
    })
    return { userUtrvStats, initiativeGroupStats };
  }

  private processVerifier({ userUtrvStats, userId, utrv, initiativeId }: ProcessStakeholderParams) {
    const userStatus = userUtrvStats.get(String(userId));
    const verified = utrv.status === ActionList.Verified ? 1 : 0;
    const rejected = utrv.status === ActionList.Rejected ? 1 : 0;
    if (userStatus) {
      userStatus.verifier += 1;
      userStatus.verified += verified;
      modifyStatusCount(utrv, userStatus.verifierCounts)

      const initiativeStatus = userStatus.initiativeCounts[initiativeId];
      if (initiativeStatus) {
        initiativeStatus.verifier += 1;
        initiativeStatus.verified += verified;
        initiativeStatus.verifierRejected += rejected;
      } else {
        userStatus.initiativeCounts[initiativeId] = getUtrvDelegation({
          verifier: 1,
          verifierRejected: rejected,
          verified,
        });
      }
    } else {
      userUtrvStats.set(String(userId), {
        contributor: 0,
        updated: 0,
        created: 0,
        contributorRejected: 0,
        verifier: 1,
        verified: verified,
        verifierRejected: 0,
        contributorCounts: createStatusStats(),
        verifierCounts: modifyStatusCount(utrv, createStatusStats()),
        initiativeCounts: {
          [initiativeId]: getUtrvDelegation({
            verifier: 1,
            verifierRejected: rejected,
            verified,
          })
        },
      });
    }
  }

  private processStakeholder({ userUtrvStats, userId, utrv, initiativeId }: ProcessStakeholderParams) {
    const userStatus = userUtrvStats.get(String(userId));
    const updated = utrv.status === ActionList.Updated ? 1 : 0;
    const created = utrv.status === ActionList.Created ? 1 : 0;
    const contributorRejected = utrv.status === ActionList.Rejected ? 1 : 0;
    if (userStatus) {
      userStatus.contributor += 1;
      userStatus.updated += updated;
      userStatus.created += created;
      userStatus.contributorRejected += contributorRejected;
      modifyStatusCount(utrv, userStatus.contributorCounts)

      const initiativeStatus = userStatus.initiativeCounts[initiativeId];
      if (initiativeStatus) {
        initiativeStatus.contributor += 1;
        initiativeStatus.updated += updated;
      } else {
        userStatus.initiativeCounts[initiativeId] = getUtrvDelegation({
          contributor: 1,
          updated,
          created,
          contributorRejected,
        })
      }

    } else {
      const utrvDelegation = getUtrvDelegation({
        contributor: 1,
        updated,
        created,
        contributorRejected,
      });
      userUtrvStats.set(String(userId), {
        ...utrvDelegation,
        contributorCounts: modifyStatusCount(utrv, createStatusStats()),
        verifierCounts: createStatusStats(),
        initiativeCounts: { [initiativeId]: utrvDelegation },
      });
    }
  }
}

let instance: InitiativeStatsService;
export const getInitiativeStatsService = () => {
  if (!instance) {
    instance = new InitiativeStatsService(
      wwgLogger,
      getInitiativeDataAggregator(),
      InitiativeRepository,
      UserRepository,
      getSurveyExcel(),
      getInitiativeUserService(),
    );
  }
  return instance;
}
