/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { LinkedUniversalTrackerModel } from "../../models/initiative";
import { ObjectId } from "bson";

interface ToLinkedUtr {
  linkedUtrs: LinkedUniversalTrackerModel[];
  add: LinkedUniversalTrackerModel[];
}

export const addToLinkedUtr = ({ linkedUtrs, add }: ToLinkedUtr): LinkedUniversalTrackerModel[] => {

  // Ensure we are not modifying reference, by re-creating the object
  const initialValue = linkedUtrs.map(u => ({
    universalTrackerId: new ObjectId(u.universalTrackerId),
    usage: u.usage.map(u => u),
  }));

  return add.reduce((existing, c) => {

    const indexToAdd = existing.findIndex((el) => el.universalTrackerId.toString() === String(c.universalTrackerId));
    if (indexToAdd >= 0) {
      const utr = existing[indexToAdd];
      c.usage.forEach((usageId) => {
        if (!utr.usage.includes(usageId)) {
          utr.usage.push(usageId);
        }
      })
      return existing;
    }

    existing.push({
      universalTrackerId: c.universalTrackerId,
      usage: c.usage
    });
    return existing
  }, initialValue)
}
