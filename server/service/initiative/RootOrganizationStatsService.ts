import { ObjectId } from 'bson';
import ArchivedInitiative from '../../models/archivedInitiative';
import Initiative, { InitiativePlain } from '../../models/initiative';
import { excludeSoftDeleted } from '../../repository/aggregations';
import { ArchivedInitiativeRepository } from '../../repository/ArchivedInitiativeRepository';
import { statusCondition } from '../../repository/OnboardingRepository';

interface RootOrganizationStats extends Pick<InitiativePlain, '_id' | 'name' | 'created' | 'parentId'> {
  userCount: number;
  onboardingCount: number;
  surveyCount: number;
  isActive: boolean;
}

export class RootOrganizationStatsService {
  constructor(private initiativeModel: typeof Initiative, private archiveInitiativeModel: typeof ArchivedInitiative) {}

  private getAggregatePipeline({ initiativeIds, isActive }: { initiativeIds: ObjectId[]; isActive: boolean }) {
    return [
      {
        $match: {
          _id: { $in: initiativeIds },
        },
      },
      {
        $lookup: {
          from: isActive ? 'surveys' : 'archived-surveys',
          localField: '_id',
          foreignField: 'initiativeId',
          pipeline: [{ $match: excludeSoftDeleted() }],
          as: 'surveys',
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: 'permissions.initiativeId',
          as: 'users',
        },
      },
      {
        // TODO: count by initiativeId for now, may need to combine with permissions.initiativeId later
        $lookup: {
          from: 'onboarding',
          localField: '_id',
          foreignField: 'initiativeId',
          pipeline: [{ $match: { status: statusCondition } }],
          as: 'onboardings',
        },
      },
      {
        $project: {
          _id: 1,
          name: 1,
          created: 1,
          parentId: 1,
          surveyCount: { $size: '$surveys' },
          userCount: { $size: '$users' },
          onboardingCount: { $size: '$onboardings' },
        },
      },
      {
        $addFields: {
          isActive,
        },
      },
    ];
  }

  private async getStatsForActiveSubsidiaries(activeInitiativeIds: ObjectId[]): Promise<RootOrganizationStats[]> {
    const aggregate = this.getAggregatePipeline({ initiativeIds: activeInitiativeIds, isActive: true });
    return this.initiativeModel.aggregate(aggregate).exec();
  }

  private async getStatsForArchivedSubsidiaries(activeInitiativeIds: ObjectId[]): Promise<RootOrganizationStats[]> {
    const archivedSubsidiaries = await ArchivedInitiativeRepository.getInitiativeTreeByActiveParentIds(
      activeInitiativeIds
    );
    const initiativeIds = archivedSubsidiaries.map(({ _id }) => _id);
    const aggregate = this.getAggregatePipeline({ initiativeIds, isActive: false });
    return this.archiveInitiativeModel.aggregate(aggregate).exec();
  }

  public async get(activeInitiativeIds: ObjectId[]) {
    return Promise.all([
      this.getStatsForActiveSubsidiaries(activeInitiativeIds),
      this.getStatsForArchivedSubsidiaries(activeInitiativeIds),
    ]);
  }
}

let instance: RootOrganizationStatsService;

export const getRootOrganizationStatsService = (): RootOrganizationStatsService => {
  if (!instance) {
    instance = new RootOrganizationStatsService(Initiative, ArchivedInitiative);
  }
  return instance;
};
