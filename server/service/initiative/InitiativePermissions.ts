/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import type { UserPlain } from "../../models/user";
import { ObjectId } from 'bson';
import { UserRoles } from "../user/userPermissions";
import { InitiativeRepository } from "../../repository/InitiativeRepository";

export type InitiativePermissionsUser = Pick<UserPlain, '_id' | 'permissions'>;

export class InitiativePermissions {
  static canContributeRoles = [UserRoles.Contributor];
  static canVerifyRoles = [UserRoles.Verifier];
  static canManageRoles = [UserRoles.Owner, UserRoles.Manager];

  protected static async hasPermission(user: InitiativePermissionsUser, initiativeId: ObjectId | string, roles: UserRoles[]) {
    if (!user.permissions || user.permissions.length === 0) {
      return false;
    }

    const initiativeIdStr = String(initiativeId);

    const hasRoleForInitiativeId = (id: string): boolean => {
      const perm = user.permissions.find((p) => String(p.initiativeId) === id);
      if (!perm) {
        return false;
      }
      return perm.permissions.some((role) => roles.includes(role));
    };

    const simpleCheck = hasRoleForInitiativeId(initiativeIdStr);
    if (simpleCheck) {
      return true;
    }

    // Recursive lookup for any parent, don't really need anything else, only id.
    const [initiative] = await InitiativeRepository.getAllParentsById(initiativeId, { _id: 1 });
    if (!initiative?.parents) {
      return false;
    }

    return initiative.parents.some(i => hasRoleForInitiativeId(String(i._id)));
  }

  public static async canManageInitiative(this: void, user: InitiativePermissionsUser, initiativeId: ObjectId | string) {
    return InitiativePermissions.hasPermission(
      user,
      initiativeId,
      InitiativePermissions.canManageRoles,
    );
  }

  public static async canManageCustomReports(user: InitiativePermissionsUser, initiativeId: ObjectId | string) {
    return InitiativePermissions.hasPermission(
      user,
      initiativeId,
      [...InitiativePermissions.canManageRoles, UserRoles.User, UserRoles.Contributor, UserRoles.Viewer],
    );
  }

  public static async canAccess(user: InitiativePermissionsUser, initiativeId: ObjectId | string) {
    return InitiativePermissions.hasPermission(
      user,
      initiativeId,
      [...InitiativePermissions.canManageRoles, UserRoles.User, UserRoles.Contributor, UserRoles.Verifier, UserRoles.Viewer],
    );
  }

  public static async canAccessAllSurveyData(user: InitiativePermissionsUser, initiativeId: ObjectId | string) {
    return InitiativePermissions.hasPermission(
      user,
      initiativeId,
      [...InitiativePermissions.canManageRoles, UserRoles.Contributor, UserRoles.Verifier, UserRoles.Viewer],
    );
  }

  public static async canContribute(user: InitiativePermissionsUser, initiativeId: ObjectId | string) {
    return InitiativePermissions.hasPermission(
      user,
      initiativeId,
      InitiativePermissions.canContributeRoles,
    );
  }

  public static async canVerify(user: InitiativePermissionsUser, initiativeId: ObjectId | string) {
    return InitiativePermissions.hasPermission(
      user,
      initiativeId,
      InitiativePermissions.canVerifyRoles,
    );
  }

  public static async isOwner(user: InitiativePermissionsUser, initiativeId: ObjectId | string) {
    return InitiativePermissions.hasPermission(
      user,
      initiativeId,
      [
        UserRoles.Owner
      ]
    );
  }
}
