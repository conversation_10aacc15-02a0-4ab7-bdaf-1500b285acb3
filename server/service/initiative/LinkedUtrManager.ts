/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { LoggerInterface, wwgLogger } from "../wwgLogger";
import {
  InitiativePlain,
  LinkedUniversalTrackerModel
} from "../../models/initiative";
import UniversalTracker, {
  UniversalTrackerPlain,
  UtrType
} from "../../models/universalTracker";
import { InitiativeUsage } from "./UsageSlots";
import { KeysEnum } from "../../models/commonProperties";
import PermissionDeniedError from "../../error/PermissionDeniedError";
import { ObjectId } from "bson";
import {
  getInitiativeRepository,
  InitiativeRepository
} from "../../repository/InitiativeRepository";
import { addToLinkedUtr } from "./linkedUniversalTrackers";
import { UserInitiativeRepository } from "../../repository/UserInitiativeRepository";
import { UserPlain } from "../../models/user";
import { InitiativePermissions } from "./InitiativePermissions";

interface CopyOptions {
  onConflict: 'duplicate'
}

interface CopyRequest {
  targets: { initiativeId: string }[],
  usage: InitiativeUsage
  options: CopyOptions
  user: UserPlain,
}

type CopyUtr = Pick<UniversalTrackerPlain, 'name' | 'valueType' | 'calculation' | 'ownerId'>
const copyUtrProjection: KeysEnum<CopyUtr, 1> = {
  name: 1,
  valueType: 1,
  calculation: 1,
  ownerId: 1,
}


export class LinkedUtrManager {

  constructor(
    private logger: LoggerInterface,
    private repo: InitiativeRepository,
  ) {
  }

  public async copyCalculatedLinkedUtrs(initiative: InitiativePlain, copyRequest: CopyRequest) {

    if (!await this.hasPermissionsForTargets(initiative, copyRequest)) {
      throw new PermissionDeniedError("User does not have permissions to copy")
    }

    const { usage, user, targets } = copyRequest;

    // Not doing anything with these yet
    const options = this.getCopyOptions(copyRequest.options)
    const lutrs = await this.getLinkedUtrs(initiative, user);

    const utrIds = lutrs.filter(lutr => lutr.usage.includes(usage))
      .map(lutr => lutr.universalTrackerId);
    const utrs = await this.getCalculatedUtrs(utrIds);

    const targetInitiatives = await this.repo.find({
      _id: { $in: targets.map(t => new ObjectId(t.initiativeId)) }
    });

    const addCalculatedForUsage: LinkedUniversalTrackerModel[] = utrs.map(utr => ({
      universalTrackerId: utr._id,
      usage: [usage],
    }));

    // Still need to add audit logs for each target
    this.logger.info("Copying linkedUniversalTrackers", {
      initiativeId: initiative._id.toString(),
      targets,
      usage,
      add: utrs.map(u => String(u._id))
    })

    const updates = targetInitiatives.map(targetInitiative => {
      targetInitiative.linkedUniversalTrackers = addToLinkedUtr({
        linkedUtrs: targetInitiative.linkedUniversalTrackers,
        add: addCalculatedForUsage
      })

      const _id = targetInitiative._id;

      return targetInitiative.save()
        .then(() => ({ _id, status: 'success' }))
        .catch((e) => {
          this.logger.error(e);
          return { _id, status: 'error' };
        })
    })

    return Promise.all(updates);
  }

  private async getLinkedUtrs(initiative: InitiativePlain, user: UserPlain): Promise<LinkedUniversalTrackerModel[]> {
    if (initiative.linkedUniversalTrackers && initiative.linkedUniversalTrackers.length > 0) {
      return initiative.linkedUniversalTrackers;
    }

    // This must be inheriting UTRs, so need to query recursively and populate here
    const existingInitiative = await UserInitiativeRepository.getUserInitiative(user, initiative._id);
    return existingInitiative ? existingInitiative.linkedUniversalTrackers : [];
  }

  private async getCalculatedUtrs(utrIds: ObjectId[]): Promise<(CopyUtr & { _id: ObjectId })[]> {
    return UniversalTracker.find({
      _id: { $in: utrIds },
      type: UtrType.Calculation
    }, copyUtrProjection).lean().exec() as Promise<(CopyUtr & { _id: ObjectId })[]>;
  }

  private getCopyOptions(options?: Partial<CopyOptions>): CopyOptions {
    return {
      onConflict: 'duplicate',
      ...options
    }
  }


  /**
   * Check if user can copy the targets
   */
  private async hasPermissionsForTargets(initiative: InitiativePlain, copyRequest: CopyRequest) {
    return InitiativePermissions.canManageInitiative(copyRequest.user, String(initiative._id))
  }
}

let instance: LinkedUtrManager;
export const getLinkedUtrManager = () => {
  if (!instance) {
    instance = new LinkedUtrManager(
      wwgLogger,
      getInitiativeRepository(),
    );
  }
  return instance;
}



