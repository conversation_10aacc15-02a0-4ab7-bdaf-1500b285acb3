/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import { InitiativeTags } from '../../models/initiative';
import { createInitiativeRepository, FrameworkTypes, InitiativeRepository } from '../../repository/InitiativeRepository';
import AggregatorService from '../../service/utr/aggregatorService';
import AggregatorDataService, { ScorecardOptions } from '../utr/aggregation/aggregatorDataService';
import { IndustryLevels } from '../../types/initiative';

export class ComparisonService {

  constructor(private initiativeRepo: InitiativeRepository) {
  }

  public async getPeerComparison(
    id: ObjectId | string,
    framework: string = '',
    levels: IndustryLevels = { level1: '', level2: '', level3: '', level4: '' }
  ) {

    const initiativeId = new ObjectId(id);
    const initiative = await this.initiativeRepo.mustFindById(initiativeId);
    const industry = initiative.industry;

    // Set default
    if (!framework) {
      framework = FrameworkTypes.GICS;
    }

    // Set default from initiative
    if (industry && !levels.level1 && industry[framework]) {
      levels.level1 = industry[framework].level1;
    }

    const peerComparison: any = {
      current: {
        _id: initiativeId,
        value: undefined,
      },
      peers: [],
      min: 0,
      max: 0,
      avg: 0
    };

    const dataService = new AggregatorDataService();
    const options: ScorecardOptions = { isCompletedData: true };

    await dataService.preloadByCodes(String(initiativeId), ['sdg/score'], options);
    const utr = dataService.getUniversalTrackerByCode('sdg/score');
    if (!utr) {
      throw new Error(`Failed to find "sdg/score" utr`)
    }
    const utrIdString = utr.idString;
    const service = new AggregatorService(utrIdString, ['actual', 'baseline'], dataService);

    const parentInitiativeUtrv = await service.getAggregatedByInitiativeId(String(initiativeId));
    if (!parentInitiativeUtrv) {
      return peerComparison;
    }

    peerComparison.current = parentInitiativeUtrv.value;
    peerComparison.min = parentInitiativeUtrv.value;
    peerComparison.max = parentInitiativeUtrv.value;
    peerComparison.avg = 0;

    // const hasFramework = industry && (industry.gics || industry.icb || industry.icb2019);
    // if (initiative.type === 'initiativeGroup') {
    //   peerInitiatives = await this.initiativeRepo.findByParentIdPeers(initiative.parentId);
    // } else if (hasFramework && levels.level1) {
    //   peerInitiatives = await InitiativeRepository.findWithTags([InitiativeTags.Organization]);
    // }
    const peerInitiatives = await InitiativeRepository.findWithTags([InitiativeTags.Organization]);
    const peerIds: string[] = peerInitiatives.map((i: any) => String(i._id));

    const getAggregatedUtrv = async (peerId: string) => {
      const peerDS = new AggregatorDataService();
      await peerDS.preloadByCodes(peerId, ['sdg/score'], options);
      const s = new AggregatorService(utrIdString, ['actual', 'baseline'], peerDS);
      return s.getAggregatedByInitiativeId(peerId);
    };

    const initiativeScores = await Promise.all(peerIds.map(peerId => getAggregatedUtrv(peerId)));

    for (const initiativeScore of initiativeScores) {
      if (initiativeScore === undefined) {
        continue;
      }

      const value = initiativeScore.value as number;
      if (value > peerComparison.max) {
        peerComparison.max = value;
      }

      if (value < peerComparison.min) {
        peerComparison.min = value;
      }

      const peerCount = peerComparison.peers.length;
      peerComparison.avg = ((peerComparison.avg * peerCount) + value) / (peerCount + 1);
      peerComparison.peers.push(initiativeScore);
    }

    return peerComparison;
  }
}

export const createComparisonService = () => {
  return new ComparisonService(createInitiativeRepository());
};
