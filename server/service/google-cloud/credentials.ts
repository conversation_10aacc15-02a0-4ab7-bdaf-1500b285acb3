/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import config from '../../config';

// Consolidate logic where we provide or
// let library retrieve it from environment
export const getGoogleCloudCredentials = () => {
  if (config.googleCloud.credentials.private_key) {
    // Mostly required for non-GCP environments, like localhost
    return {
      projectId: config.googleCloud.projectId,
      credentials: config.googleCloud.credentials,
    };
  }

  // Use default credentials
  return undefined;
};

export const getGoogleCloudReleasesCredentials = (projectId?: string) => {
  if (config.g17ecoReleases.credentials.private_key) {
    return {
      projectId: projectId ?? config.g17ecoReleases.projectId,
      credentials: {
        client_email: config.g17ecoReleases.credentials.client_email,
        private_key: config.g17ecoReleases.credentials.private_key.replace(/\\n/gm, '\n'), // This is getting corrupted somewhere for background jobs
      },
    };
  }

  // Use default credentials
  return undefined;
};
