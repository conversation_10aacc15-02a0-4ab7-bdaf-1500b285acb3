/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import {
  DelegationPermissions,
  SurveyCheck,
  UtrvPermissionCheck,
  InitiativePermissionCheck
} from './DelegationPermissions'
import UserError from '../../error/UserError'
import { wwgLogger } from '../wwgLogger'
import { Logger } from 'winston'
import { UserModel } from '../../models/user'
import {
  createDelegationScopeManager,
  DelegationScopeManager,
} from '../survey/scope/DelegationScopeManager';
import { OnboardingModelPlain } from "../../models/onboarding";

export class DelegationManager {

  constructor(
    private delegationPermissions: DelegationPermissions,
    private delegationScopeManager: DelegationScopeManager,
    private logger: Logger,
  ) {
  }

  public async checkInitiative(check: InitiativePermissionCheck) {
    const isAllowed = await this.delegationPermissions.isAllowedInitiative(check);
    if (!isAllowed) {
      return this.throwPermissionError(check, `initiative ${check.initiative._id}`)
    }
    return true;
  }

  public async checkUtrv(check: UtrvPermissionCheck) {
    const isAllowed = await this.delegationPermissions.isAllowedUtrv(check)

    if (!isAllowed) {
      return this.throwPermissionError(check, `utrv ${check.utrv._id} ${check.type}`)
    }
    return true;
  }

  public async checkSurvey(check: SurveyCheck) {
    const isAllowed = await this.delegationPermissions.isAllowedSurvey(check)
    if (!isAllowed) {
      return this.throwPermissionError(check, `survey ${check.survey._id} ${check.type}`)
    }
    return true;
  }

  private throwPermissionError(check: { user: UserModel }, identifier: string) {
    this.logger.error(`Delegator ${check.user._id} (${check.user.email}) does not have permissions to update ${identifier}`);
    throw new UserError(`Delegator does not have permissions`)
  }

  public async onboardDelegationScope(onboarding: OnboardingModelPlain, user: UserModel) {
    return this.delegationScopeManager.onboardDelegationScope(onboarding, user);
  }
}

export const createDelegationManager = () => {
  return new DelegationManager(
    new DelegationPermissions(),
    createDelegationScopeManager(),
    wwgLogger
  );
};
