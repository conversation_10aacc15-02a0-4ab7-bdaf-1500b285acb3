/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import UniversalTrackerValue from '../../models/universalTrackerValue';
import { StakeholderGroup } from '../../models/stakeholderGroup';
import { ObjectId } from 'bson';
import { Actions } from '../action/Actions';

interface DelegationChangeData {
  ids: ObjectId[],
  role: keyof StakeholderGroup,
  userId: ObjectId,
  action: Actions,
}

interface Delegations {
  ids: ObjectId[];
  roles: (keyof StakeholderGroup)[];
  userIds: ObjectId[];
  action: Actions;
}

interface BulkSurveyUtrsDelegation extends Omit<Delegations, 'ids'> {
  utrIds: ObjectId[];
  surveyIds: ObjectId[];
}

export class UniversalTrackerValueDelegation {
  public static async applyDelegationChange(changeData: DelegationChangeData) {
    const { ids, role, userId, action } = changeData;

    const actionMethod = action === Actions.Remove ? '$pull' : '$push';
    const equalMethod = action === Actions.Remove ? '$eq' : '$ne';
    const stakeholderRole = `stakeholders.${role}`;

    return UniversalTrackerValue.updateMany(
      {
        _id: { $in: ids },
        [stakeholderRole]: { [equalMethod]: userId },
      },
      { [actionMethod]: { [stakeholderRole]: userId } },
      { multi: true }
    );
  }

  public static async applyDelegations(delegations: Delegations) {
    const { ids, roles, userIds, action } = delegations;
    const [operator, modifier] = action === Actions.Remove ? ['$pull', '$in'] : ['$addToSet', '$each'];
    const update = Object.fromEntries(roles.map((role) => [`stakeholders.${role}`, { [modifier]: userIds }]));

    return UniversalTrackerValue.updateMany(
      {
        _id: { $in: ids },
      },
      { [operator]: update }
    );
  }

  public static async applySurveyUtrsDelegations(delegations: BulkSurveyUtrsDelegation) {
    const { surveyIds, utrIds, roles, userIds, action } = delegations;
    const [operator, modifier] = action === Actions.Remove ? ['$pull', '$in'] : ['$addToSet', '$each'];
    const update = Object.fromEntries(roles.map((role) => [`stakeholders.${role}`, { [modifier]: userIds }]));

    return UniversalTrackerValue.updateMany(
      {
        universalTrackerId: { $in: utrIds },
        'compositeData.surveyId': { $in: surveyIds },
      },
      { [operator]: update }
    );
  }
}
