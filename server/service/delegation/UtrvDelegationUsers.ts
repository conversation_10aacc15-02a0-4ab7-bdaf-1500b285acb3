/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { UniversalTrackerValuePlain } from '../../models/universalTrackerValue';
import { ObjectId } from 'bson';
import { SurveyModel } from '../../models/survey';
import { UniversalTrackerValueRepository } from '../../repository/UniversalTrackerValueRepository';
import { getOnboardingManager } from '../onboarding/OnboardingManager';
import { UserRepository } from '../../repository/UserRepository';
import { SafeUser, safeUserFields } from '../../models/user';
import { InitiativePermissions } from '../initiative/InitiativePermissions';
import { UserRoles } from '../user/userPermissions';
import { SurveyUserRoles } from '../../types/roles';
import { SurveyPermissions } from '../survey/SurveyPermissions';
import { InitiativeRepository } from '../../repository/InitiativeRepository';
import { OnboardingStatus } from '../../models/onboarding';
import { getSurveyWorkgroupService, SurveyWorkgroup } from '../workgroup/SurveyWorkgroupService';
import { KeysEnum } from '../../models/public/projectionUtils';
import { ScopeUtrv } from '../../util/scope-utrv';
import { DataScopeAccess } from '../../models/dataShare';
import { UtrvIds } from '../../types/universalTrackerValue';
import { mergeObjectIdArrays } from '../../util/array';

// TODO: Align with frontend.
interface User {
  _id: ObjectId;
  initiativeRoles?: string[];
  surveyRoles?: string[];
  roles?: string[];
  workgroups?: SurveyWorkgroup[];
  // Keep count field for backwards compatibility.
  count?: {
    contributed?: number;
    verified?: number;
  };
  // explicit utrv delegation or partial access workgroup delegation.
  utrvIds?: UtrvIds;
}

type Survey = Pick<
  SurveyModel,
  '_id' | 'stakeholders' | 'sourceName' | 'permissions' | 'initiativeId' | 'visibleUtrvs'
>;

type Utrv = Pick<UniversalTrackerValuePlain, '_id' | 'stakeholders'>;
const utrvProjection: KeysEnum<Utrv> = {
  _id: 1,
  stakeholders: 1,
};

type Role = SurveyUserRoles.Admin | SurveyUserRoles.Stakeholder | SurveyUserRoles.Verifier;

const initiativeDelegationRoles = [
  ...InitiativePermissions.canContributeRoles,
  ...InitiativePermissions.canVerifyRoles,
  ...InitiativePermissions.canManageRoles,
];

export class UtrvDelegationUsers {
  constructor(
    private initiativeRepository: typeof InitiativeRepository,
    private userRepository: typeof UserRepository,
    private utrvRepository: typeof UniversalTrackerValueRepository,
    private onboardingManager: ReturnType<typeof getOnboardingManager>,
    private surveyWorkgroupService: ReturnType<typeof getSurveyWorkgroupService>
  ) {}

  public async get(survey: Survey, utrvIds: string[]) {
    const initiativeId = survey.initiativeId;

    const initiativeAndParentsIds = await this.initiativeRepository.getCurrentAndParentInitiativeIds(
      survey.initiativeId
    );
    const initiativeAndParentsStringIds = initiativeAndParentsIds.map((id) => id.toString());
    const initiativeAndParentsUsers = await this.userRepository.getUsersByInitiativeIds<SafeUser>({
      initiativeIds: initiativeAndParentsIds,
      roles: Object.values(UserRoles),
      projection: safeUserFields,
    });
    const utrvs = utrvIds.length
      ? await this.utrvRepository.getScopeUtrvs({
          utrvIds: utrvIds.map((id) => new ObjectId(id)),
          additionalProjection: utrvProjection,
        })
      : [];

    // Must not early exit here as we need to check for onboarding users

    const initiativeAndParentsUserIds = initiativeAndParentsUsers.map((user) => user._id.toString());

    const initiativeUsers = this.getInitiativeLevel(initiativeAndParentsUsers, initiativeAndParentsStringIds);
    const surveyUsers = await this.getSurveyLevel(survey, initiativeAndParentsUserIds);
    const utrvsUsers = await this.getUtrvLevel(
      utrvs,
      initiativeId,
      initiativeAndParentsUserIds,
      initiativeAndParentsStringIds
    );
    const workgroupUsers = await this.getWorkgroupLevel({
      survey,
      utrvs,
      initiativeAndParentsUserIds,
    });

    // workgroupUsers must be first to keep the `count`, `workgroups` and `utrvIds` fields. TODO: should do that in mergeUsers?
    const users = this.mergeUsers([...workgroupUsers, ...utrvsUsers, ...initiativeUsers, ...surveyUsers]);

    return {
      contributors: this.filterUsersByRole(users, SurveyUserRoles.Stakeholder),
      verifiers: this.filterUsersByRole(users, SurveyUserRoles.Verifier),
      admins: this.filterUsersByRole(users, SurveyUserRoles.Admin),
    };
  }

  private getInitiativeLevel(initiativeAndParentsUsers: SafeUser[], initiativeAndParentsIds: string[]) {
    return initiativeAndParentsUsers.reduce((delegationUsers, user) => {
      const delegationRoles = user.permissions.reduce((delegationRoles, permission) => {
        if (!initiativeAndParentsIds.includes(permission.initiativeId.toString())) {
          return delegationRoles;
        }

        (permission.permissions ?? []).forEach((role) => {
          if (initiativeDelegationRoles.includes(role)) {
            delegationRoles.add(role);
          }
        });

        return delegationRoles;
      }, new Set<UserRoles>());

      if (delegationRoles.size > 0) {
        delegationUsers.push({ ...user, initiativeRoles: Array.from(delegationRoles) });
      }

      return delegationUsers;
    }, [] as User[]);
  }

  private async getSurveyLevel(
    survey: Pick<SurveyModel, 'stakeholders' | 'roles'>,
    initiativeAndParentsUserIds: string[]
  ) {
    // Need to filter out users at this point to reduce user load.
    const isInitiativeAndParentsUser = (id: ObjectId) =>
      this.isValidUser({ userId: id.toString(), initiativeAndParentsUserIds });
    const stakeholderIds = [...(survey.stakeholders.stakeholder || [])].filter(isInitiativeAndParentsUser);
    const verifierIds = [...(survey.stakeholders.verifier || [])].filter(isInitiativeAndParentsUser);
    const adminIds = [...(survey.roles?.admin || [])].filter(isInitiativeAndParentsUser);
    const users = await this.utrvRepository.getUtrvStakeholderUsers(stakeholderIds, verifierIds, adminIds);

    return users.map(({ roles, ...user }) => ({
      ...user,
      surveyRoles: roles,
    }));
  }

  private async getUtrvLevel(
    utrvs: Utrv[],
    initiativeId: ObjectId,
    initiativeAndParentsUserIds: string[],
    initiativeAndParentsIds: string[]
  ) {
    if (utrvs.length === 0) {
      return [];
    }

    const stakeholderIds: { [key: string]: ObjectId[] } = {};
    const verifierIds: { [key: string]: ObjectId[] } = {};

    utrvs.forEach((utrv) => {
      utrv.stakeholders?.stakeholder.forEach((userId) => {
        const strUserId = String(userId);
        stakeholderIds[strUserId] = stakeholderIds[strUserId] ?? [];
        stakeholderIds[strUserId].push(utrv._id);
      });
      utrv.stakeholders?.verifier.forEach((userId) => {
        const strUserId = String(userId);
        verifierIds[strUserId] = verifierIds[strUserId] ?? [];
        verifierIds[strUserId].push(utrv._id);
      });
    });

    const toObjectIds = (ids: { [key: string]: ObjectId[] }) =>
      Object.keys(ids)
        .filter((id) => this.isValidUser({ userId: id, initiativeAndParentsUserIds }))
        .map((id) => new ObjectId(id));

    const users = (
      await this.utrvRepository.getUtrvStakeholderUsers(toObjectIds(stakeholderIds), toObjectIds(verifierIds))
    ).map((user) => ({
      ...user,
      utrvIds: {
        [SurveyUserRoles.Stakeholder]: stakeholderIds[user._id.toString()],
        [SurveyUserRoles.Verifier]: verifierIds[user._id.toString()],
      },
    }));

    return [...users, ...(await this.getOnboardingUsers(utrvs, initiativeId, initiativeAndParentsIds))];
  }

  private async getOnboardingUsers(utrvs: Utrv[], initiativeId: ObjectId, initiativeAndParentsIds: string[]) {
    const onboardingUsers = await this.onboardingManager.findOnboardingUtrvUsers(utrvs, initiativeId);

    return onboardingUsers.filter(
      (onboardingUser) =>
        onboardingUser.status === OnboardingStatus.Pending &&
        onboardingUser.user.permissions.some((p) => initiativeAndParentsIds.includes(p.initiativeId.toString()))
    );
  }

  private async getWorkgroupLevel({
    survey,
    utrvs,
    initiativeAndParentsUserIds,
  }: {
    survey: Survey;
    utrvs: (ScopeUtrv & Utrv)[];
    initiativeAndParentsUserIds: string[];
  }) {
    const workgroups = await this.surveyWorkgroupService.getWorkgroups({ survey, utrvs });
    const userIds = workgroups.reduce((userIds, workgroup) => {
      workgroup.users.forEach((user) => {
        const userId = user._id.toString();
        // Initiative roles doesn’t matter at this group level but we want to keep only users that are in the initiative and parents.
        // We will consider user roles using workgroup permission below.
        if (this.isValidUser({ userId, initiativeAndParentsUserIds })) {
          userIds.add(userId);
        }
      });
      return userIds;
    }, new Set<string>());

    const users = await this.userRepository.findByIds<SafeUser>(Array.from(userIds), true, safeUserFields);
    const userWorkgroupsMap = new Map<string, SurveyWorkgroup[]>(users.map((user) => [user._id.toString(), []]));
    workgroups.forEach((workgroup) => {
      workgroup.users.forEach((user) => {
        const userId = user._id.toString();
        const userWorkgroups = userWorkgroupsMap.get(userId) ?? [];
        userWorkgroups.push(workgroup);
        userWorkgroupsMap.set(userId, userWorkgroups);
      });
    });

    return users.map((user) => {
      const userWorkgroups = userWorkgroupsMap.get(user._id.toString()) ?? [];
      // If the workgroup has full access, then treat workgroup level as survey level.
      const surveyRoles = userWorkgroups
        .filter((workgroup) => workgroup.permission.access === DataScopeAccess.Full)
        .map((workgroup) => workgroup.permission.roles)
        .flat();
      // Partial access is considered for Stakeholder and Verifier roles only, so we count 'Partial' access Admin role if included. Ignore None access for now.
      if (
        userWorkgroups.some(
          (workgroup) =>
            workgroup.permission.access === DataScopeAccess.Partial &&
            workgroup.permission.roles.includes(SurveyUserRoles.Admin)
        )
      ) {
        surveyRoles.push(SurveyUserRoles.Admin);
      }

      // If the workgroup has partial access, then treat workgroup level as utrv level.
      const contributingUtrvIds = this.getWorkgroupUtrvIdsByRole(userWorkgroups, SurveyUserRoles.Stakeholder);
      const verifyingUtrvIds = this.getWorkgroupUtrvIdsByRole(userWorkgroups, SurveyUserRoles.Verifier);
      const roles: SurveyUserRoles[] = [];
      if (contributingUtrvIds.length > 0) {
        roles.push(SurveyUserRoles.Stakeholder);
      }
      if (verifyingUtrvIds.length > 0) {
        roles.push(SurveyUserRoles.Verifier);
      }

      return {
        ...user,
        workgroups: userWorkgroups,
        surveyRoles,
        roles,
        utrvIds: {
          [SurveyUserRoles.Stakeholder]: contributingUtrvIds,
          [SurveyUserRoles.Verifier]: verifyingUtrvIds,
        },
      };
    });
  }

  private getWorkgroupUtrvIdsByRole(
    workgroups: SurveyWorkgroup[],
    role: SurveyUserRoles.Stakeholder | SurveyUserRoles.Verifier
  ) {
    return workgroups
      .filter(
        (workgroup) =>
          workgroup.permission.access === DataScopeAccess.Partial && workgroup.permission.roles.includes(role)
      )
      .flatMap((workgroup) => workgroup.utrvIds);
  }

  // Merge the roles and other properties if the user already exists, filtering out duplicates.
  private mergeUsers(users: User[]) {
    const mergedUsers = users.reduce((acc, current) => {
      const existingUser = acc.find((u) => u._id.equals(current._id));

      if (!existingUser) {
        acc.push(current);
        return acc;
      }
      existingUser.roles = this.mergeRoles(existingUser.roles, current.roles);
      existingUser.surveyRoles = this.mergeRoles(existingUser.surveyRoles, current.surveyRoles);
      existingUser.initiativeRoles = this.mergeRoles(existingUser.initiativeRoles, current.initiativeRoles);
      existingUser.utrvIds = this.mergeUtrvIds(existingUser.utrvIds, current.utrvIds);
      return acc;
    }, [] as User[]);
    return mergedUsers.map((user) => {
      return {
        ...user,
        ...(user.utrvIds
          ? {
              count: {
                contributed: user.utrvIds?.[SurveyUserRoles.Stakeholder]?.length ?? 0,
                verified: user.utrvIds?.[SurveyUserRoles.Verifier]?.length ?? 0,
              },
            }
          : {}),
      };
    });
  }

  private mergeRoles(existingRoles: string[] | undefined = [], currentRoles: string[] | undefined = []) {
    return Array.from(new Set([...existingRoles, ...currentRoles]));
  }

  private mergeUtrvIds(existingUtrvIds: User['utrvIds'], currentUtrvIds: User['utrvIds']): UtrvIds | undefined {
    if (!existingUtrvIds && !currentUtrvIds) {
      return;
    }

    return {
      [SurveyUserRoles.Stakeholder]: mergeObjectIdArrays(
        existingUtrvIds?.[SurveyUserRoles.Stakeholder] ?? [],
        currentUtrvIds?.[SurveyUserRoles.Stakeholder] ?? []
      ),
      [SurveyUserRoles.Verifier]: mergeObjectIdArrays(
        existingUtrvIds?.[SurveyUserRoles.Verifier] ?? [],
        currentUtrvIds?.[SurveyUserRoles.Verifier] ?? []
      ),
    };
  }

  private filterUsersByRole(users: User[], role: Role) {
    return users.filter(this.hasRole(role));
  }

  private hasRole(role: Role) {
    return (user: User) => {
      const keyMap: Record<Role, 'canContributeRoles' | 'canVerifyRoles' | 'canManageRoles'> = {
        [SurveyUserRoles.Stakeholder]: 'canContributeRoles',
        [SurveyUserRoles.Verifier]: 'canVerifyRoles',
        [SurveyUserRoles.Admin]: 'canManageRoles',
      };
      const key = keyMap[role];
      return (
        user.initiativeRoles?.some((r) => InitiativePermissions[key].includes(r as UserRoles)) ||
        user.surveyRoles?.some((r) => SurveyPermissions[key].includes(r as SurveyUserRoles)) ||
        user.roles?.some((r) => role === r)
      );
    };
  }

  private isValidUser({
    userId,
    initiativeAndParentsUserIds,
  }: {
    userId: string;
    initiativeAndParentsUserIds: string[];
  }) {
    // Ensure we don’t show user that is deleted from initiative and parents.
    return initiativeAndParentsUserIds.includes(userId);
  }
}

let instance: UtrvDelegationUsers;

export function getUtrvDelegationUsers() {
  if (!instance) {
    instance = new UtrvDelegationUsers(
      InitiativeRepository,
      UserRepository,
      UniversalTrackerValueRepository,
      getOnboardingManager(),
      getSurveyWorkgroupService()
    );
  }
  return instance;
}
