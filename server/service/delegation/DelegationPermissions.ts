/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { UniversalTrackerValuePlain } from '../../models/universalTrackerValue';
import { SurveyModelPlain } from '../../models/survey';
import { StakeholderGroup } from '../../models/stakeholderGroup';
import { UserModel } from '../../models/user';
import { getCompositeDataById } from '../utr/utrvUtil';
import { InitiativePlain } from '../../models/initiative';
import { SurveyRepository } from '../../repository/SurveyRepository';
import { InitiativePermissions } from '../initiative/InitiativePermissions';

interface PermissionCheck {
  user: UserModel;
  utrv?: UniversalTrackerValuePlain;
  survey?: SurveyModelPlain | null;
  type: keyof StakeholderGroup;
}

interface PermissionRequest {
  user: UserModel;
  utrv?: UniversalTrackerValuePlain;
  survey?: SurveyModelPlain | null;
}

type PermissionKeys = keyof Pick<StakeholderGroup, 'stakeholder' | 'verifier'>;

type PermissionResponse = {
  [key in PermissionKeys]?: boolean;
}


export interface UtrvPermissionCheck extends PermissionCheck {
  utrv: UniversalTrackerValuePlain;
}

export interface SurveyCheck extends PermissionCheck {
  survey: SurveyModelPlain;
}

export interface InitiativePermissionCheck {
  user: UserModel;
  initiative: InitiativePlain;
}

export class DelegationPermissions {

  private isAllowed({user, utrv, survey, type}: PermissionCheck) {
    const id = String(user._id);
    const isInUTRV = utrv?.stakeholders?.[type].map(String).includes(id) ?? false;
    const isInSurvey = survey?.stakeholders?.[type].map(String).includes(id) ?? false;
    const isAnAdmin = survey?.roles?.admin?.map(String).includes(id) ?? false;

    return isInUTRV || isInSurvey || isAnAdmin;
  }

  public async isAllowedInitiative(check: InitiativePermissionCheck) {
    const { initiative, user } = check;
    return InitiativePermissions.canManageInitiative(user, String(initiative._id));
  }

  public async isAllowedUtrv(check: UtrvPermissionCheck) {
    const { survey, utrv, user } = check;

    if (!survey) {
      const compData = getCompositeDataById(utrv);
      if (compData?.surveyId) {
        check.survey = await SurveyRepository.mustFindById(compData.surveyId);
      }
    }

    if (this.isAllowed(check)) {
      return true;
    }

    return InitiativePermissions.canManageInitiative(user, String(utrv.initiativeId));
  }

  public async isAllowedSurvey(check: SurveyCheck) {
    if (this.isAllowed(check)) {
      return true;
    }
    return InitiativePermissions.canManageInitiative(check.user, String(check.survey.initiativeId));
  }

  public async getPermissions(check: PermissionRequest): Promise<PermissionResponse> {
    const permissions: PermissionResponse = {};
    const keys: PermissionKeys[] = ['stakeholder', 'verifier'];

    let fallbackToInitiative = false;
    for (const type of keys) {
      const isAllowed = this.isAllowed({ ...check, type });
      permissions[type] = isAllowed;

      if (!isAllowed) {
        fallbackToInitiative = true;
      }
    }

    const initiativeId = check.utrv ? check.utrv.initiativeId : check.survey?.initiativeId;
    if (!initiativeId) {
      return permissions;
    }

    if (fallbackToInitiative && (check.utrv || check.survey)) {
      const canManage = await InitiativePermissions.canManageInitiative(check.user, String(initiativeId));
      if (canManage) {
        // Override
        keys.forEach(k => {
          permissions[k] = true;
        });
      }
    }

    // const initPerm = await UserInitiativeRepository.getInitiativeUserPermissions(check.user, String(initiativeId));
    // permissions.userGroups = userGroups(initPerm);

    return permissions;
  }

  public async getUtrvPermissions(utrv: UniversalTrackerValuePlain, user: UserModel) {

    let survey;
    const compData = getCompositeDataById(utrv);
    if (compData && compData.surveyId) {
      survey = await SurveyRepository.mustFindById(compData.surveyId);
    }
    return this.getPermissions({ user, survey, utrv: utrv });
  }
}
