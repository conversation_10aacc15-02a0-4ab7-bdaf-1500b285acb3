export interface RangePosition {
  startCol: string;
  endCol: string;
  row: string | number;
}

export interface CellPosition {
  column: string;
  row: string | number;
}

export interface CellStyle {
  [name: string]: any;
}

export const COLUMNS = ['A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z'];
export const COLOUR_STYLE = [0xEEEEEE, 0xFFFFFF];
export const BORDER_COLOUR = 0xBCBCBC;
export const BLACK_COLOUR = 0x000000;
export const DARK_GREY_COLOUR = 0xa6a6a6;

export const mimeType = {
  csv: 'text/csv',
  xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  txt: 'text/plain',
  zip: 'application/zip',
};

export const allowedDocumentTypes = [
  'image/jpeg',
  'image/png',
  'image/webp',
  'image/gif',
  mimeType.csv,
  'text/rtf',
  mimeType.txt,
  'text/xml',
  'plain/text',
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  mimeType.zip,
  'application/x-zip-compressed',
  'application/vnd.ms-excel',
  mimeType.xlsx,
  'application/vnd.ms-excel.sheet.binary.macroenabled.12',
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'application/octet-stream',
  'video/mp4', // Big questions over this!
  '',
];