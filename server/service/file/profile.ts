/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import fs from "fs";
import path from "path";
import User from '../../models/user';
import Organization from '../../models/organization';
import Initiative from '../../models/initiative';
import UniversalTracker from '../../models/universalTracker';
import { getStorage } from '../storage/fileStorage';
import { ObjectId } from 'bson';
import { generatedUUID } from "../crypto/token";
import { downloadFile } from "./file";
import { wwgLogger } from "../wwgLogger";
import { AxiosError } from "axios";
import ContextError from "../../error/ContextError";

const storage = getStorage();
type SupporterModelType = 'user' | 'organization' | 'initiative' | 'universalTracker';

export function getModel(type: SupporterModelType) {
  switch (type) {
    case 'user':
      return User;
    case 'organization':
      return Organization;
    case 'initiative':
      return Initiative;
    case 'universalTracker':
      return UniversalTracker;
    default:
      throw new Error(`Not supported profile type ${type}`);
  }
}

interface UploadProfileFileParams {
  file: Express.Multer.File;
  type: SupporterModelType | 'metricGroup';
  id: string | ObjectId;
}

const allowedProfileTypes = [
  'image/jpeg',
  'image/png',
  'image/webp',
];

export const uploadProfileFile = async ({ file, type, id }: UploadProfileFileParams) => {
  const extension = storage.getExtensionFromMimeType(file.mimetype);
  if (!allowedProfileTypes.includes(storage.getContentTypeFromMimeType(file.mimetype))) {
    throw new Error(`Only image file type is supported.`)
  }

  const uploadPath = `profile/${type}/${id}.${extension}`;
  return storage.upload(file.path, uploadPath, file.mimetype, true);
};

export const saveProfile = async (id: ObjectId | string, type: SupporterModelType, files: Express.Multer.File[]) => {
  const file = files.shift();
  if (!file) {
    return `No files found`;
  }
  const result = await uploadProfileFile({ file, type, id });

  const modelRepository: any = getModel(type);
  const model: any = await modelRepository.findById(id).exec();

  model.profile = result.url;
  await model.save();
  return `Successfully updated ${type} ${id} avatar`;
};

interface MinModel {
  _id: ObjectId,
  profile?: string
}

interface UpdateProfileOptions<T extends MinModel = MinModel> {
  url: string;
  model: T;
  type: SupporterModelType
}

/**
 * Still require manual save call on user
 */
export async function modifyProfile<T extends MinModel = MinModel>(updateOptions: UpdateProfileOptions<T>): Promise<T> {

  const { url, model, type } = updateOptions;
  if (!url.toLowerCase().startsWith('http')) {
    return model;
  }

  const destination = `/tmp/${generatedUUID()}${path.extname(url) ?? ''}`
  const now = Date.now();

  return downloadFile(url, destination)
    .then(async ({ contentLength, contentType }) => {

      const downloadDuration = Date.now() - now;
      const file = {
        mimetype: contentType,
        originalname: path.basename(url),
        path: destination,
        size: Number(contentLength),
      }  as Express.Multer.File

      const result = await uploadProfileFile({ file, type, id: model._id });
      const existingProfile = model.profile ?? '';
      model.profile = result.url;

      wwgLogger.info(`Updated model profile`, {
        url,
        type,
        modelId: model._id,
        existingProfile,
        newProfile: model.profile,
        downloadDuration,
        fileSize: Number(contentLength),
        duration: Date.now() - now
      });

      return model;
    })
    .catch((e: Error | AxiosError) => {
      wwgLogger.error(new ContextError('Failed to download profile', {
        url,
        debugMessage: e.message,
      }));
      return model;
    })
    .finally(() => {
      fs.unlink(destination, (err) => {
        if (err) {
          wwgLogger.error(err)
        }
      })
    });
}
