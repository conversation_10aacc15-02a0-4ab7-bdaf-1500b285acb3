/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import * as fs from "fs";
import rimraf from "rimraf";
import { wwgLogger } from '../wwgLogger';

const path = require('path');

export const removeFile = (path: string) => {
  if (!path) {
    return;
  }

  fs.unlink(path, (e) => {
    if (e) {
      wwgLogger.error(e);
    }
  });
};

export const removeTmpFolder = async (bundleDir: string) => {
  if (bundleDir.startsWith('/tmp')) {
    return rimraf(bundleDir)
      .catch((err: Error) => {
        wwgLogger.error(err);
      });
  }
};

export const createTempDir = (prefix: string): Promise<string> => new Promise((resolve, reject) => {
  fs.mkdtemp(`/tmp/${prefix}`, (err, folder) => {
    (err) ? reject(err) : resolve(folder)
  });
});

export function mkDirByPathSync(targetDir: string, isRelativeToScript = false) {
  const sep = path.sep;
  const initDir = path.isAbsolute(targetDir) ? sep : '';
  const baseDir = isRelativeToScript ? __dirname : '.';

  return targetDir.split(sep).reduce((parentDir, childDir) => {
    const curDir = path.resolve(baseDir, parentDir, childDir);
    try {
      fs.mkdirSync(curDir);
    } catch (err) {
      if (err.code === 'EEXIST') { // curDir already exists!
        return curDir;
      }

      // To avoid `EISDIR` error on Mac and `EACCES`-->`ENOENT` and `EPERM` on Windows.
      if (err.code === 'ENOENT') { // Throw the original parentDir error on curDir `ENOENT` failure.
        throw new Error(`EACCES: permission denied, mkdir '${parentDir}'`);
      }

      const caughtErr = ['EACCES', 'EPERM', 'EISDIR'].indexOf(err.code) > -1;
      if (!caughtErr || caughtErr && curDir === path.resolve(targetDir)) {
        throw err; // Throw if it's just the last created dir.
      }
    }

    return curDir;
  }, initDir);
}
