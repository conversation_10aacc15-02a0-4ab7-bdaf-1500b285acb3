/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import {
  CellAddress,
  JSON2SheetOpts,
  Range,
  readFile,
  Sheet2JSONOpts,
  StyleZ,
  utils,
  WorkBook,
  WorkSheet,
} from '@sheet/core';
import { ParserOptions } from '../survey/transfer/FileParser';
import { FileParser } from '../survey/transfer/parserTypes';
import XLSX from '@sheet/core';
import { CellPosition, CellStyle, RangePosition } from './constants';

export type Headers = string[];

export class Excel implements FileParser {

  private cellSplit = /[a-zA-Z]+|[0-9]+/g

  public async loadFile<T = unknown[]>(path: string, options: ParserOptions): Promise<T[]> {
    const workbook = await this.readFile(path)
    const sheet = this.getSheet(workbook, options);
    if (!sheet) {
      throw new Error(`Failed to load sheet using "${options.tab?.name}" name`)
    }
    return this.sheetToJson<T>(sheet);
  }

  public jsonToSheet<T = any>(data: T[], opts?: JSON2SheetOpts): WorkSheet {
    return utils.json_to_sheet(data, opts);
  }

  public arrayToSheet<T = any>(data: T[][], opts?: JSON2SheetOpts): WorkSheet {
    return utils.aoa_to_sheet(data, opts);
  }

  public async addToBook(book: WorkBook, sheet: WorkSheet, name?: string) {
    return utils.book_append_sheet(book, sheet, name);
  }

  public async createBook() {
    return utils.book_new();
  }

  private getSheet(workbook: WorkBook, options: ParserOptions) {
    if (options.tab?.name) {
      return workbook.Sheets[options.tab.name];
    }

    return Object.values(workbook.Sheets)[0]
  }

  public async readFile(path: string) {
    return readFile(path)
  }

  public async getSheetFromFile(path: string, options: ParserOptions){
    const workbook = await this.readFile(path);
    return this.getSheet(workbook, options);
  }

  public sheetToJson<T>(sheet: WorkSheet, options?: Sheet2JSONOpts) {
    const fixedSheet = this.fixWorksheetReference(sheet)
    return utils.sheet_to_json<T>(fixedSheet, options)
  }

  public fixWorksheetReference(worksheet: WorkSheet) {
    // get all cells ranges
    const cellRanges = Object.keys(worksheet).filter(x => x.match(/[A-Z]+[0-9]+/g));

    // get the last one cell range in terms of rows
    const maxCellRange = cellRanges[cellRanges.length - 1];
    if (!maxCellRange) {
      return worksheet;
    }

    const maxRowResult = maxCellRange.match(this.cellSplit);
    if (!maxRowResult) {
      return worksheet;
    }

    // getLast in terms of columns
    const maxCols = cellRanges.sort().pop()?.match(this.cellSplit);
    if (!maxCols) {
      return worksheet;
    }

    // Max letter AA + max row 9999 = AA9999
    if ((maxCols[0] && maxRowResult[1])) {
      // update the reference because before of that, it could be A1:AMJ1048576
      worksheet['!ref'] = `A1:${maxCols[0] + maxRowResult[1]}`;
    }

    return worksheet;
  }

  public setRangeStyle(sheet: WorkSheet, range: string | Range, style: StyleZ) {
    return utils.sheet_set_range_style(sheet, range, style);
  }

  public getRowRange(sheet: WorkSheet, row: number): string | Range {
    if (!sheet['!ref']) {
      return `A${row}:Z${row}`;
    }

    let range = utils.decode_range(sheet['!ref']);
    range.s.r = row;
    range.e.r = row

    return range
  }

  public getColumnRange(sheet: WorkSheet, column: number): string | Range {
    if (!sheet['!ref']) {
      return `${column}0:${column}1000`;
    }

    let range = utils.decode_range(sheet['!ref']);
    range.s.c = column;
    range.e.c = column;

    return range
  }

  public getCellRange(column: number, row: number): Range {
    return {
      s: { c: column, r: row },
      e: { c: column, r: row }
    }
  }

  public encodeCellAddress(cellAddress: CellAddress) {
    return utils.encode_cell(cellAddress);
  }

  public getRange(sheet: WorkSheet): Range {

    const fixedSheet = sheet['!ref'] ? sheet : this.fixWorksheetReference(sheet)
    const ref = fixedSheet['!ref'];
    if (ref) {
      return utils.decode_range(ref);
    }

    return {
      s: { c: 0, r: 0 },
      e: { c: 999, r: 999 }
    }
  }

  public changeHeaders(sheet: WorkSheet, headers: string[]) {
    return utils.sheet_add_aoa(sheet, [headers], { origin: 'A1' });
  }

  public setColumnsWidth(sheet: WorkSheet, widths: (number|undefined)[]) {
    const colWidths = widths.map((w) => (w ? { wpx: w } : { hidden: true }));
    sheet['!cols'] = colWidths;
  }

  public lockAllColumns(sheet: WorkSheet) {
    sheet["!protect"] = {};
  }

  public unlockColumns(sheet: WorkSheet, columns: number[]) {
    columns.forEach((column) => {
      utils.sheet_set_range_style(sheet, this.getColumnRange(sheet, column), { editable: true })
    })
  }

  public makeColumnDropdown(sheet: WorkSheet, column: number, list: string[]) {
    this.makeCellDropdown(sheet, this.getColumnRange(sheet, column), list);
  }

  public makeCellDropdown(sheet: WorkSheet, cell: string | Range, list: string[] | string) {
    if (!sheet['!validations']) {
      sheet['!validations'] = [];
    }

    /*Lookup list doesn't work with row range like { s: { c: 0, r: 0 }, e: { c: 999, r: 0 } }.
      Specifically, it doesn't work with value columns in import file template.
      So we need to add validation for each column in the range.
    */
    const isLookup = typeof list === 'string';
    if (isLookup && typeof cell !== 'string') {
      for (let column = cell.s.c; column <= cell.e.c; column++) {
        sheet['!validations'].push({
          ref: { s: { c: column, r: cell.s.r }, e: { c: column, r: cell.e.r } },
          t: 'List',
          f: list,
        });
      }

      return;
    }

    sheet['!validations'].push({
      ref: cell,
      t: 'List',
      [isLookup ? 'f' : 'l']: list,
    });
  }

  public addLookupToBook(book: WorkBook, data: string[], lookupName: string) {
    // For maximal compatibility, the first row of lookup should be blank according to sheetjs documentation.
    const aoa = [''].concat(data).map(function (v) {
      return [v];
    });
    const lookupSheet = this.arrayToSheet(aoa);
    this.addToBook(book, lookupSheet, lookupName);
    this.hideSheet(book, lookupName);
  }

  public hideSheet(book: WorkBook, sheetName: string) {
    if (!book.Workbook) book.Workbook = {};
    if (!book.Workbook.Sheets) book.Workbook.Sheets = [];
    const lookupIdx = book.SheetNames.indexOf(sheetName);
    if (!book.Workbook.Sheets[lookupIdx]) book.Workbook.Sheets[lookupIdx] = {};
    book.Workbook.Sheets[lookupIdx].Hidden = 2
  }

  public getHeaders(sheet: WorkSheet): Headers {
    if (!sheet['!ref']) {
      return [];
    }

    const header = [];
    const columnCount = utils.decode_range(sheet['!ref']).e.c + 1;
    for (let i = 0; i < columnCount; ++i) {
      header[i] = sheet[`${utils.encode_col(i)}1`].v;
    }

    return header;
  }

  public getPlainSheetData(data: unknown[][]) {
    return data.map((row) => {
      return row.map((cell) => {
        // when cell with value and options
        if (cell && typeof cell === 'object') {
          return 'value' in cell && cell.value !== undefined ? cell.value : '';
        }
        return cell;
      });
    });
  }

  public applyColumnStyle(sheet: WorkSheet, range: Range, style: CellStyle) {
    sheet['!sheetFormat'] = { row: { hpx: 20, } };
    XLSX.utils.sheet_set_range_style(sheet, range, style);
  }

  /** @deprecated should use applyColumnStyle by passing range directly  **/
  public applyStyles(sheet: WorkSheet, cellPosition: CellPosition, style: CellStyle) {
    sheet['!sheetFormat'] = {
      row: {
        hpx: 20,
      },
    };
    const { column, row } = cellPosition;
    XLSX.utils.sheet_set_range_style(
      sheet,
      this.getCellRangeForStyle({ startCol: column, endCol: column, row }),
      style
    );
  }

  private getCellRangeForStyle(rangePosition: RangePosition) {
    const { startCol, endCol, row } = rangePosition;
    return `${startCol}${row}:${endCol}${row}`;
  }
}

let instance: Excel;
export const getExcel = () => {
  if (!instance) {
    instance = new Excel();
  }
  return instance;
}
