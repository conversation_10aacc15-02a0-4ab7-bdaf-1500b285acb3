/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { DocumentPlain } from '../../models/document';
import { getDocumentExtension } from './extension';
import { createWriteStream } from 'fs';
import axios from 'axios';
import { promisify } from 'util';
import { finished } from 'stream';

const finishedDownload = promisify(finished);

export const getFilename = (d: DocumentPlain) => {
  if (d.metadata && d.metadata.name) {
    return d.metadata.name;
  }

  const ext = getDocumentExtension(d);
  return `${d._id}${ext ? '.' + ext : ''}`;
};

export async function downloadFile(fileUrl: string, outputLocationPath: string) {
  const writer = createWriteStream(outputLocationPath);

  const response = await axios({
    method: 'get',
    url: fileUrl,
    responseType: 'stream',
    timeout: 3000,
  });

  response.data.pipe(writer);
  await finishedDownload(writer);

  const headers = response.headers;
  return {
    contentLength: headers['content-length'],
    contentType: headers['content-type'],
  }
}
