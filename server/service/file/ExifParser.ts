/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { ExifParserFactory } from 'ts-exif-parser';
import * as fs from 'fs';
import { ExifData } from 'ts-exif-parser/lib/ExifData';

const parseExif = (data: Buffer) => ExifParserFactory.create(data).parse();

export const parseFileExif = (path: string): Promise<ExifData> => new Promise((resolve, reject) => {
  fs.readFile(path, (err, data) => {
    if (err) {
      reject(err);
    } else {
      resolve(parseExif(data));
    }
  });
});
