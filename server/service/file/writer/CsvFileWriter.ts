/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */
import {
  createArrayCsvWriter,
  createObjectCsvStringifier,
  createArrayCsvStringifier,
  createObjectCsvWriter,
} from 'csv-writer';

import { Options, parse } from 'csv-parse/sync';
import { readFile } from 'fs/promises';

interface CsvOptions {
  records: any[],
  path: string,
  header?: any[],
}

export const writeCsvFile = (options: CsvOptions) => {
  const csvWriter = createArrayCsvWriter({
    path: options.path,
    header: options.header,
  });

  return csvWriter.writeRecords(options.records)       // returns a promise
};

/**
 * https://owasp.org/www-community/attacks/CSV_Injection
 * replace formula symbols at beginning of the string
 * replace formula symbols and separators in the middle, since attackers could try to start a new cell
 * alternatively double quote every field and escape existing quotes
 */
export const sanitizeCsvRecords = (records: any[]): any[] => {
  return records.map(record => {
    if (typeof record === 'string') {
      return record
        // removes from beginning, accounts eventual quote
        .replace(/^["']*[+=@\-\t\r]+/g, "")
        // removes from the middle separator and symbol, detectes separator eventually followed by quotes, and accounts for spaces
        .replace(/\s*[;,]+\s*["']*\s*[+=@\-\t\r]+/g, "")
    }
    if (record instanceof Array) {
      return sanitizeCsvRecords(record)
    }
    return record
  })
}

export const sanitizeCellString = (str: string): string => str.replace(/[\n\r\t]+/g, '');

export const stringifyArrayCsvFile = (options: Pick<CsvOptions, 'records' | 'header'>) => {
  const writer = createArrayCsvStringifier({ header: options.header });
  return writer.getHeaderString() +
    writer.stringifyRecords(sanitizeCsvRecords(options.records));
};

export const stringifyCsvFile = (options: Required<Pick<CsvOptions, 'records' | 'header'>>) => {
  const writer = createObjectCsvStringifier({ header: options.header });
  return writer.getHeaderString() +
    writer.stringifyRecords(sanitizeCsvRecords(options.records));
};

export const writeObjectCsvFile = (options: Required<CsvOptions>) => {
  const csvWriter = createObjectCsvWriter({
    path: options.path,
    header: options.header,
  });
  return csvWriter.writeRecords(options.records);
};

export const readCSVFile = async (filePath: string, options: Options = { columns: true, bom: true }) => {
  const content = await readFile(filePath);
  return parse(content, options);
};
