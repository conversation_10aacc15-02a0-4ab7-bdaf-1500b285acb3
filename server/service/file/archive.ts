/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import * as fs from 'fs';
import { wwgLogger } from '../wwgLogger';
import archiver from 'archiver';

export interface ArchiveFile {
  path: string,
  name: string,
  isDir?: boolean,
  isStream?: boolean,
}

export const createArchive = (files: ArchiveFile[], outputPath: string): Promise<{ path: string, size: number }> => {
  const output = fs.createWriteStream(outputPath);
  const archive = archiver.create('zip');

  return new Promise((resolve, reject) => {
    archive.on('error', (err: Error) => reject(err));

    // pipe archive data to the output file
    archive.pipe(output);
    files.forEach(({ path, name, isDir, isStream }) => {
      if (isStream) {
        archive.append(path, { name });
      } else {
        isDir ? archive.directory(path, false) : archive.file(path, { name });
      }
    });

    // good practice to catch warnings (ie stat failures and other non-blocking errors)
    archive.on('warning', (err: any) => {
      if (err.code === 'ENOENT') {
        wwgLogger.error(err);
      } else {
        throw err;
      }
    });

    // Wait for streams to complete
    archive.finalize().then(() => resolve({
      path: outputPath,
      size: output.bytesWritten,
    }));
  })
};

export enum CompressionLevel {
  Moderate = 3, // Moderate compression, Speed: Fast, Memory usage: Moderate
  Default = 6, // Default compression, Speed: Slower, Memory usage: Moderate
  Maximum = 9, // Maximum compression, Speed: Slowest, Memory usage: Highest
}

export const zipFolder = async ({
  sourceFolder,
  zipFilePath,
  compressionLevel = CompressionLevel.Default,
}: {
  sourceFolder: string;
  zipFilePath: string;
  compressionLevel?: CompressionLevel;
}): Promise<{ path: string; size: number }> => {
  return new Promise((resolve, reject) => {
    const output = fs.createWriteStream(zipFilePath);

    const archive = archiver.create('zip', { zlib: { level: compressionLevel } });

    archive.on('error', (err: any) => reject(err));

    // Pipe the archive to the output file
    archive.pipe(output);

    archive.directory(sourceFolder, false);

    archive.finalize().then(() =>
      resolve({
        path: zipFilePath,
        size: output.bytesWritten,
      })
    );
  });
};

export const getZipStats = (zipPath: string) => {
  const stats = fs.statSync(zipPath);

  return {
    size: stats.size,
  };
};
