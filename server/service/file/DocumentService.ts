import { ObjectId } from 'bson';
import DocumentModel, { DocumentOwnerType, DocumentPlain, DocumentSubType } from '../../models/document';
import { addDocumentUrl, FileStorageInterface, getFileNameWithExtension, getStorage } from '../storage/fileStorage';
import { getResultsFromPromiseAllSettled } from '../../util/promise';
import { UserMin } from '../../models/user';
import { wwgLogger } from '../wwgLogger';
import UserError from '../../error/UserError';
import { allowedDocumentTypes } from './constants';
import { InitiativePlain } from '../../models/initiative';
import { projectDate } from '../../util/date';
import { PipelineStage } from 'mongoose';
import {
  AIDocumentUtrMappingService,
  getAIDocumentLibraryScanService,
} from '../ai/document-utr-mapping/AIDocumentUtrMappingService';
import ContextError from '../../error/ContextError';
import { DocumentMediaType } from '../../types/document';
import { KeysEnum } from '../../models/public/projectionUtils';

type Document = Pick<DocumentPlain, '_id' | 'path' | 'metadata' | 'title'>;
const documentProjection: KeysEnum<Document, 1> = {
  _id: 1,
  path: 1,
  metadata: 1,
  title: 1,
};

type DocumentWithUrl = Document & { url: string };

interface UploadDocumentForm {
  title?: string;
  description?: string;
  ownerId?: ObjectId;
  ownerSubType?: DocumentSubType;
  ownerType: DocumentOwnerType;
}

interface QueryDocs {
  startDate?: Date;
  endDate?: Date;
  cursor?: Date;
  limit?: number;
  mediaTypes?: DocumentMediaType[];
  ownerSubType?: DocumentSubType;
  searchText?: string;
}

export class DocumentService {
  constructor(
    private documentModel: typeof DocumentModel,
    private storage: FileStorageInterface,
    private aiDocumentUtrMappingService: AIDocumentUtrMappingService,
  ) {}

  async getDocumentsMapByIds(documentIds: ObjectId[]): Promise<Map<string, DocumentWithUrl>> {
    if (!documentIds.length) {
      return new Map();
    }

    const documents = await this.documentModel
      .find({ _id: { $in: documentIds } }, documentProjection)
      .lean<Document[]>()
      .exec();
    const { fulfilled: documentsWithURLs } = getResultsFromPromiseAllSettled(
      await Promise.allSettled(
        documents.map(async (document) => {
          const [url] = await this.storage.getSignedUrl(document.path, { fileName: getFileNameWithExtension(document) });
          return [document._id.toString(), { ...document, url }];
        })
      )
    );

    return new Map<string, DocumentWithUrl>(documentsWithURLs as [string, DocumentWithUrl][]);
  }

  getMediaTypesMatch(mediaTypes: DocumentMediaType[]) {
    const match = [];
    if (mediaTypes.includes(DocumentMediaType.Image)) {
      match.push({ 'metadata.mimetype': { $regex: '^image/' } });
    }
    if (mediaTypes.includes(DocumentMediaType.Video)) {
      match.push({ 'metadata.mimetype': { $regex: '^video/' } });
    }
    if (mediaTypes.includes(DocumentMediaType.File)) {
      match.push({ 'metadata.mimetype': { $regex: '^(?!image/)(?!video/)' } });
    }
    return match;
  }

  async handleGetDocumentsWithUrl(initiativeId: ObjectId | string, params: QueryDocs) {
    const { startDate, endDate, cursor, limit, mediaTypes, ownerSubType, searchText } = params;

    let hasNextPagePipeline: PipelineStage.FacetPipelineStage[] = [];
    const matchPipeline: Record<string, any> = {
      ownerId: new ObjectId(initiativeId),
      ...(ownerSubType && { ownerSubType }),
      ...projectDate({ field: 'created', startDate, endDate }),
    };

    const andClauses: Record<string, any>[] = [];
    if (mediaTypes?.length) {
      andClauses.push({ $or: this.getMediaTypesMatch(mediaTypes) });
    }

    if (searchText) {
      const searchRegex = new RegExp(searchText, 'i');
      andClauses.push({
        $or: [{ title: { $regex: searchRegex } }, { 'metadata.extension': { $regex: searchRegex } }],
      });
    }

    if (andClauses.length > 0) {
      matchPipeline.$and = andClauses;
    }

    if (cursor) {
      matchPipeline.created = { ...(startDate && { $gte: new Date(startDate) }), $lt: new Date(cursor) };
    }

    if (limit) {
      hasNextPagePipeline = [{ $limit: limit + 1 }]; // Fetch 1 extra record to check if there's a next page
    }

    const rawDocuments = await this.documentModel
      .aggregate([{ $match: matchPipeline }, { $sort: { created: -1 } }, ...hasNextPagePipeline])
      .exec();

    const hasNextPage = typeof limit === 'number' && rawDocuments.length > limit;

    const { documents } = await addDocumentUrl({ documents: rawDocuments.slice(0, limit) });
    const nextCursor = hasNextPage ? documents[limit - 1].created : null;

    return {
      documents,
      hasNextPage,
      nextCursor,
    };
  }

  async handleDocumentUpload(data: UploadDocumentForm, file: Express.Multer.File, user: UserMin) {
    const extension = this.storage.getExtensionFromMimeType(file.mimetype);

    if (!allowedDocumentTypes.includes(this.storage.getContentTypeFromMimeType(file.mimetype))) {
      wwgLogger.info(`User tried to upload an document of type ${file.mimetype}.`);
      throw new UserError(`Files of type ${file.mimetype} are not supported.`);
    }

    const isPublic = false;
    const model = new this.documentModel({
      ...data,
      metadata: {
        name: file.originalname,
        mimetype: file.mimetype,
        extension: extension,
      },
      size: file.size,
      userId: user._id,
      public: isPublic,
    });

    const uploadPath = `/initiatives/${data.ownerId}/documents/${model.id}.${extension}`;
    const result = await this.storage.upload(file.path, uploadPath, file.mimetype, isPublic);

    // Save path and model
    model.path = result.path;
    await model.save();

    // This process take a while, don't wait for it.
    this.aiDocumentUtrMappingService.mapRelevantUtrs(model).catch((e) => {
      wwgLogger.error(
        new ContextError('Failed to map relevant UTRs', {
          documentId: model._id,
          cause: e,
        })
      );
    });

    return result;
  }

  async handleDocumentEdit(documentId: string, data: UploadDocumentForm) {
    return this.documentModel.findByIdAndUpdate(documentId, {
      title: data.title,
      description: data.description,
      ownerSubType: data.ownerSubType,
      ownerType: data.ownerType,
    });
  }

  async handleBulkDocumentEdit(documentIds: string[] | ObjectId[], data: UploadDocumentForm) {
    return this.documentModel.updateMany(
      { _id: { $in: documentIds } },
      {
        ownerSubType: data.ownerSubType,
      }
    );
  }

  async handleDeleteDocument({
    documentId,
    initiative,
  }: {
    documentId: ObjectId | string;
    initiative: Pick<InitiativePlain, '_id'>;
  }) {
    const document = await this.documentModel
      .findById(documentId)
      .orFail(new UserError('Document not found', { documentId }));
    if (!document.ownerId?.equals(initiative._id)) {
      throw new UserError('You do not have permission for this action', {
        documentOwnerId: document.ownerId,
        initiativeId: initiative._id,
      });
    }
    return await document.deleteOne();
  }
}

let instance: DocumentService;
export const getDocumentService = () => {
  if (!instance) {
    instance = new DocumentService(DocumentModel, getStorage(), getAIDocumentLibraryScanService());
  }

  return instance;
};
