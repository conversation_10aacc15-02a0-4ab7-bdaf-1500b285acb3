/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import multer from 'multer';
import { v4 as uuidv4 } from 'uuid';

const diskStorage = multer.diskStorage({
  destination: (req, file, cb) => cb(null, '/tmp/'),
  filename: (_req, file, cb) => cb(null, `${file.fieldname}-${Date.now()}-${uuidv4()}`),
})

const maxFileSize = 5e+7; // 50Mb
export default multer({ storage: diskStorage, limits: { fileSize: maxFileSize } })


const getMessage = (err: multer.MulterError) => {
  switch (err.code) {
    case 'LIMIT_FILE_SIZE':
      return 'File is too large to upload. The maximum supported file size is 50mb'
    default:
      return err.message;
  }
}


export function fileUploadErrorMiddleware(err: any, req: any, res: any, next: any) {

  if (err instanceof multer.MulterError) {
    res.status(400);
    return res.json({
      success: false,
      message: err.message,
      userMessage: getMessage(err),
    });
  }

  next(err)
}
