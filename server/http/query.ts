/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


export const toBoolean = (param: unknown) => {
  if (typeof param === 'string') {
    return param.toLowerCase() === 'true';
  }
  return Boolean(param);
}

/** Query param **/
export const toArray = <T = string>(param: T | T[] | undefined, defaultValue: T[] = []): T[] => {
  if (Array.isArray(param)) {
    return param
  }

  if (param === undefined || param === null || typeof param === 'object') {
    return defaultValue
  }

  // Must be scalar type now
  return [param]
}

