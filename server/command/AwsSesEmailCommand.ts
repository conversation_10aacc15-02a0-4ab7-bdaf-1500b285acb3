/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { Message } from '@aws-sdk/client-sqs';
import AwsSesWorker from '../service/email/AwsSesWorker';
import { wwgLogger } from '../service/wwgLogger';

export default class AwsSesEmailCommand {

  private worker: AwsSesWorker;
  private readonly queueName: string;
  private readonly numberOfMessages: number;
  private readonly maxJobs = 100;
  private readonly sleepTime: number;

  constructor(worker: AwsSesWorker, queueName: string, numberOfMessages = 10) {
    this.worker = worker;
    this.queueName = queueName;
    this.numberOfMessages = numberOfMessages;
    // 9 minutes
    this.sleepTime = (9 * 60 * 1000) / this.maxJobs;
  }

  public async execute() {

    const paramsReceiveMessage = await this.getReceiveMessageParams();

    // to stop the process after a number of jobs for memory management
    for (let jobs = 0; jobs < this.maxJobs; jobs++) {

      const data = await this.worker.receiveMessages(paramsReceiveMessage);

      // No Messages
      if (!data.Messages || !Array.isArray(data.Messages)) {
        await this.sleep();
        continue;
      }
      const messages: Message[] = data.Messages;

      // We might receive multiple messages at once.
      for (const message of messages) {

        const params = {
          QueueUrl: paramsReceiveMessage.QueueUrl,
          ReceiptHandle: message.ReceiptHandle
        };
        try {

          // Catch any errors and continue with the rest of the messages
          await this.worker.handle(message);
          // Everything succeeded, delete message
          this.deleteMessage(params);
        } catch (e) {
          wwgLogger.info(e);
          // Message could not be mapped to email log.
          // Most likely generic message, delete it.
          this.deleteMessage(params);
        }
      }
    }
  }

  private async getReceiveMessageParams() {
    const queueUrl = await this.worker.getQueueUrl(this.queueName);
    if (!queueUrl) {
      throw new Error(`Queue url for ${this.queueName} was not found. Please check that`);
    }

    return {
      QueueUrl: queueUrl,
      MaxNumberOfMessages: this.numberOfMessages
    };
  }

  private deleteMessage(params: { QueueUrl: any; ReceiptHandle: String | any }) {
    this.worker.deleteMessage(params).then(console.log);
  }

  private async sleep() {
    return new Promise((resolve) => {
      setTimeout(() => resolve(true), this.sleepTime);
    })
  }
}

