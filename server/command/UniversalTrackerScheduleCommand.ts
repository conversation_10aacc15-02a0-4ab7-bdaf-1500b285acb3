/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import UniversalTrackerScheduleService from '../service/utr/UniversalTrackerScheduleService';

export default class UniversalTrackerScheduleCommand {

  private schedulerService: UniversalTrackerScheduleService;

  constructor(scheduleService?: UniversalTrackerScheduleService) {
    this.schedulerService = scheduleService || new UniversalTrackerScheduleService();
  }

  public async execute() {
    console.log('Running UniversalTrackerScheduleCommand ' + (new Date()).toISOString());
    const data = await UniversalTrackerScheduleService.getPendingSchedules();
    console.log('Finished UniversalTrackerScheduleCommand ' + (new Date()).toISOString());
    return this.schedulerService.process(data);
  }

  public async reset(resetDate: Date, id?: string) {
    console.log('Running UniversalTrackerScheduleCommand Reset ' + (new Date()).toISOString());
    const result =  await this.schedulerService.resetNextRunDate(resetDate, id);
    console.log('Finished UniversalTrackerScheduleCommand Reset ' + (new Date()).toISOString());
    return result;
  }
}

