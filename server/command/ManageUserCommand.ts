/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { Logger } from 'winston';
import { wwgLogger } from '../service/wwgLogger';
import User from "../models/user";


export class ManageUserCommand {

  constructor(
    private logger: Logger,
  ) {
  }

  public async execute(action: string, email: string) {
    switch (action) {
      case 'superUser':
        return this.setSuperUser(email);
      default:
        throw new Error(`Unsupported action ${action}`)
    }
  }

  private async setSuperUser(email: string) {

    if (!email) {
      throw new Error(`Must provided a valid email address`)
    }
    const r = await User.findOneAndUpdate({ email }, { isSuperUser: true }).orFail().exec();
    let msg = `Upgraded user "${r._id}" to super user`;
    this.logger.info(msg, { userId: String(r._id), manageUser: 'setSuperUser' })

    return msg
  }
}

export const createManagerUserCmd = () => {
  return new ManageUserCommand(
    wwgLogger,
  );
};


