/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */



import { HydratedDocument, model, Schema } from 'mongoose';
import { v4 as uuid } from 'uuid';
import { ObjectId } from 'bson';
import { ValueList as ValueListPlain } from './public/valueList';

export type ValueListModel<T = ObjectId> = HydratedDocument<ValueListPlain<T>>;

export const ValueListOptionsValueSchema = new Schema<ValueListModel>({
  code: { type: Schema.Types.String, trim: true, required: true, lowercase: true, },
  name: { type: Schema.Types.String, trim: true, required: true },
}, { _id: false });

const ValueListSchema = new Schema({
  code: {
    type: Schema.Types.String,
    trim: true,
    lowercase: true,
    required: true,
    unique: true,
    validate: [
      /^[a-z0-9\/\-.]+$/,
      'Code can only contain alphanumeric, dash(-), dot(.) and forward slash (/)'
    ],
    default: function () {
      return uuid();
    }
  },
  name: { type: Schema.Types.String, trim: true, required: true },
  options: [ValueListOptionsValueSchema],
  created: { type: Date, default: Date.now },
}, { collection: 'value-list' });

ValueListSchema.pre('save', function (next) {
  if (this.isNew) {
    const codeRegex = /^[a-z0-9\/\-]+$/;
    if (this.options.some((option) => !codeRegex.test(option.code))) {
      next(new Error('Code can only contain alphanumeric, dash(-) and forward slash (/)'));
    }
  }
  next();
});


const ValueList = model<ValueListModel>('ValueListModel', ValueListSchema);

export default ValueList;
