/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */


import { HydratedDocument, InferSchemaType, model, Schema } from "mongoose";
import { ObjectId } from "bson";
import { generateAPIKey } from "prefixed-api-key"
import ContextError from "../error/ContextError";
import { ConnectionRole, ScopePermission } from "../public-api/scopePermissionModels";


export const rolesSchema = {
  type: [Schema.Types.String],
  default: [],
  enum: Object.values(ConnectionRole),
};

export const scopesSchema = {
  type: [Schema.Types.String],
  default: [],
  enum: Object.values(ScopePermission),
};


export interface PublicApiAccess {
  /** Roles that give you scopes **/
  roles: ConnectionRole[];
  /** Custom scopes that are merged together with scopes provided by roles **/
  scopes?: ScopePermission[];

  /**
   * Represent for which company/account this access is for
   * Should always be top level initiative tag organization
   **/
  initiativeId: ObjectId;
}


/**
 * based on GitHub Personal Access Token (Classic) ^ghp_[a-zA-Z0-9]{36}$
 */
const personalAccessTokenPrefix = 'g17eco' as const;

export const APIKeyConfig = {
  keyPrefix: personalAccessTokenPrefix,
  shortTokenPrefix: "pa",
  shortTokenLength: 10,
  longTokenLength: 36,
}

export const generatePersonalAccessToken = async () => {
  const token = await generateAPIKey(APIKeyConfig);
  if (!token.longTokenHash) {
    throw new ContextError(`Failed to generate personal access token`, APIKeyConfig)
  }
  return token
};

export interface ApiKey extends PublicApiAccess {
  /**
   * Hash version of the code that was used to generate the api key.
   * It was only visible when creating for the first time.
   *
   * based on GitHub Personal Access Token (Classic) ^ghp_[a-zA-Z0-9]{36}$
   * but support - and _ as that is what we use by nanoId by default [A-Za-z0-9_-]
   */
  longTokenHash: string;

  /**
   * It is very useful to see what was the original key was used
   * especially when you want to do the key rotation.
   * We can use name, but part of the token is even better and already supported
   * by the library. This short token is safe to use in the logs to identify it
   */
  shortToken: string;


  /**
   * Allow user to write name of the token if needed.
   * It is optional as we have shortToken property that should be enough to
   * identify the token we want to revoke or rotate.
   */
  name?: string

  /** Update max every 1 minute **/
  lastUsed: Date;


  /** Allow to revoke the key but still keep the reference to it. **/
  revokedDate?: Date;

  created: Date;

  userId: ObjectId;
}


const ApiKeySchema = new Schema<ApiKey, ApiKey>({
  userId: {
    type: Schema.Types.ObjectId,
    require: true
  },
  initiativeId: {
    type: Schema.Types.ObjectId,
    require: true
  },
  longTokenHash: {
    type: Schema.Types.String,
    required: true,
    unique: true,
    minLength: 36,
    maxlength: 128,
    match: [/^[A-Za-z0-9]+$/, 'alpha numeric string'],
  },

  shortToken: {
    type: Schema.Types.String,
    required: true,
    unique: true,
    minLength: 10,
    maxlength: 36,
  },
  name: {
    type: Schema.Types.String,
    trim: true,
    maxlength: 256,
  },
  roles: rolesSchema,
  scopes: scopesSchema,
  revokedDate: Schema.Types.Date,
  lastUsed: Schema.Types.Date,
  created: {
    type: Schema.Types.Date,
    default: Date.now
  }
}, { collection: 'user-api-keys' });


ApiKeySchema.index({ userId: 1 });

export type UserApiKeyModel = HydratedDocument<InferSchemaType<typeof ApiKeySchema>>;
export type UserApiKeyPlain = ApiKey & { _id: ObjectId };
export type UserApiKeyVirtuals = UserApiKeyPlain & { initiative?: { _id: ObjectId, name: string } };

ApiKeySchema.virtual('initiative', {
  ref: 'Initiative',
  localField: 'initiativeId',
  foreignField: '_id',
  justOne: true,
  options: {
    select: {
      _id: 1,
      name: 1
    }
  }
});


const userApiKey = model('UserApiKey', ApiKeySchema);
export default userApiKey;
