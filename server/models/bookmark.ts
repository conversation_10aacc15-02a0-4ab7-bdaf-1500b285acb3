import { Model, model, ObjectId, Schema } from 'mongoose';

export enum BookmarkTarget {
  UniversalTrackerValue = 'universalTrackerValue',
}

export interface BookmarkModel {
  _id?: ObjectId;
  userId: ObjectId;
  targetType: BookmarkTarget;
  targetId: ObjectId;
  surveyId?: ObjectId;
}

const BookmarkSchema = new Schema<BookmarkModel>(
  {
    userId: { type: Schema.Types.ObjectId, required: true },
    surveyId: { type: Schema.Types.ObjectId, required: false },
    targetType: {
      type: String,
      enum: Object.values(BookmarkTarget),
      default: BookmarkTarget.UniversalTrackerValue,
      required: true,
    },
    targetId: { type: Schema.Types.ObjectId, required: true },
  },
  { collection: 'bookmarks' }
);

export const Bookmark: Model<BookmarkModel> = model('Bookmark', BookmarkSchema);
