/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { Document, Model, model, Schema } from 'mongoose';
import { ObjectId } from 'bson';
import { permissionsSchema, UserRolePermissions } from './commonProperties';
import { KeysEnum } from "./public/projectionUtils";
import { ExtendedUserPermissions, UserRoles } from "../service/user/userPermissions";
import { AuthenticatedRequest } from '../http/AuthRouter';
import { PublicApiAccess, rolesSchema } from "./userApiKey";

export enum UserAgreement {
  ESGenomeTandC = 'ESGenomeTandC',
}

const AgreementSchema = new Schema({
  [UserAgreement.ESGenomeTandC]: {
    type: Date,
    required: false
  },
}, { _id: false });

const RegistrationDataSchema = new Schema({
  organizationName: Schema.Types.String,
  onboardingId: Schema.Types.ObjectId,
  agreements: {
    type: AgreementSchema,
    required: false
  },
  onboardingData: {
    type: {
      rootAppPath: Schema.Types.String,
      appConfigCode: Schema.Types.String,
      referenceCompany: Schema.Types.String,
      referralCode: Schema.Types.String,
    },
    required: false
  }
}, { _id: false });

const AuthProviderSchema = new Schema({
  type: Schema.Types.String,
  name: Schema.Types.String,
}, { _id: false });

export interface ServiceAccountConnection extends PublicApiAccess {
  /** Represent app from the vendor **/
  addonId: ObjectId;
}

const ConnectionSchema = new Schema<ServiceAccountConnection, ServiceAccountConnection>({
  initiativeId: {
    type: Schema.Types.ObjectId,
    required: true,
  },
  addonId: {
    type: Schema.Types.ObjectId,
    required: true,
  },
  roles: rolesSchema,
}, { _id: false });

export const emailRegex = /^([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)@([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}$/;

export enum UserType {
  ServiceAccount = 'service_account',
}

const UserSchema = new Schema<UserModel>({
  title: String,
  firstName: String,
  surname: String,
  sfId: Schema.Types.String,
  oktaUserId: {
    type: Schema.Types.String,
    unique: true,
  },
  type: {
    type: Schema.Types.String,
    enum: Object.values(UserType),
    required: false,
  },
  connection: {
    type: ConnectionSchema,
    required: function (this: UserPlain) {
      return this.type === UserType.ServiceAccount
    },
  },
  email: {
    type: String,
    trim: true,
    lowercase: true,
    unique: true,
    required: true,
    validate: [emailRegex, 'Please fill a valid email address']
  },
  authenticationProvider: { type: AuthProviderSchema, required: false },
  passwordHash: String,
  jobTitle: String,
  telephoneNumber: String,
  mobileNumber: String,
  profile: String,
  registrationData: { type: RegistrationDataSchema, required: false },
  partnerTypes: [{
    type: String,
    trim: true,
    lowercase: true,
    enum: ['financial', 'delivery', 'beneficiary', 'assurer', 'other']
  }],
  active: { type: Schema.Types.Boolean, default: false, required: true },
  isSuperUser: { type: Schema.Types.Boolean, default: false, required: false },
  stakeholderTypes: [{
    type: String,
    trim: true,
    lowercase: true,
    enum: [
      'auditor',
      'business_sector',
      'commercial_investor',
      'fund_manager',
      'international_government',
      'national_government',
      'local_government',
      'non_profit_sector',
      'philanthropic_donor',
      'public_member'
    ]
  }],
  organizationId: Schema.Types.ObjectId,
  ...permissionsSchema,
  lastLogin: { type: Schema.Types.Date },
  country: Schema.Types.String,
  scorecardFavourites: {
    type: [Schema.Types.ObjectId],
    required: false,
    default: []
  },
  loginAttempts: { type: Schema.Types.Number, required: true, default: 0 },
  created: { type: Schema.Types.Date, default: Date.now },
}, { collection: 'users' });

export const userMinFields: KeysEnum<UserMin> = {
  _id: 1,
  firstName: 1,
  surname: 1,
  profile: 1,
  active: 1,
};

export const safeUserFields: KeysEnum<SafeUser> = {
  ...userMinFields,
  title: 1,
  name: 1,
  stakeholderTypes: 1,
  organizationId: 1,
  permissions: 1,
  isStaff: 1,
  staffRoles: 1,
  isSuperUser: 1,
  authenticationProvider: 1,
  profile: 1,
  oktaUserId: 1,
  partnerTypes: 1,
  lastLogin: 1,
};

export const userPlain: KeysEnum<UserPlain> = {
  ...safeUserFields,
  firstName: 1,
  surname: 1,
  profile: 1,
  _id: 1,
  active: 1,
  email: 1,
  registrationData: 1,
  authenticationProvider: 1,
  sfId: 1,
  oktaUserId: 1,
  isSuperUser: 1,
  organization: 1,
  loginAttempts: 1,
  country: 1,
  mobileNumber: 1,
  created: 1,
  type: 1,
};

export const completeUserFields: KeysEnum<CompleteUser> = {
  ...safeUserFields,
  created: 1,
  telephoneNumber: 1,
  email: 1,
  jobTitle: 1,
  registrationData: 1,
}

export const statsUserFields: KeysEnum<StatsUser> = {
  _id: 1,
  firstName: 1,
  surname: 1,
  email: 1,
  permissions: 1,
  lastLogin: 1,
  isStaff: 1,
}

export interface UserMin<T = ObjectId> {
  _id: T;
  firstName: string;
  surname: string;
  profile?: string;
  active?: boolean;
}

export type UserMinData = Pick<UserPlain, '_id' | 'email' | 'permissions'>;

export const projectionMinDataFields: KeysEnum<UserMinData, 1> = { _id: 1, email: 1, permissions: 1 };

interface AuthenticationProvider {
  type: 'FEDERATION' | 'OKTA' | string;
  name: string;
}

export interface UserPlain<T = ObjectId> extends UserRolePermissions<T>, UserMin<T> {
  title?: string;
  sfId?: string;
  email: string;
  organization?: any;
  active: boolean;
  oktaUserId?: string;
  registrationData?: RegistrationData;
  authenticationProvider?: AuthenticationProvider;
  loginAttempts: number;
  isSuperUser?: boolean;
  /** @deprecated moved to organization permissions as 'restricted' */
  organizationId?: T;
  country?: string;
  mobileNumber?: number;
  created?: Date;
  type?: UserType;
}

export type UserSendNotificationData = Pick<UserPlain, '_id' | 'email' | 'firstName' | 'surname'>;

export interface SafeUser<T = ObjectId> extends UserRolePermissions<T> {
  _id: T;
  title?: string;
  name?: string;
  firstName: string;
  surname: string;
  stakeholderTypes: string[];
  partnerTypes?: string;
  lastLogin?: Date;
  organizationId?: T;
  isSuperUser?: boolean;
  profile?: string;
  authenticationProvider?: AuthenticationProvider;
  oktaUserId?: string;
  active: boolean;
}

export interface CompleteUser<T = ObjectId> extends SafeUser<T> {
  created: Date,
  telephoneNumber?: string;
  email: string;
  jobTitle?: string;
  registrationData?: RegistrationData;
}

export interface ExtendedCompleteUser<T = ObjectId> extends Omit<CompleteUser<T>, 'permissions'> {
  permissions: ExtendedUserPermissions<T>[]
}

export type StatsUser<T = ObjectId> = Pick<UserPlain<T>, '_id' | 'email' | 'firstName' | 'surname' | 'permissions' | 'isStaff'> & {
  lastLogin?: Date;
};

export interface RegistrationData {
  organizationName?: string;
  onboardingId?: ObjectId;
  agreements?: {
    [key in UserAgreement]?: Date;
  };
  onboardingData?: {
    rootAppPath?: string;
    appConfigCode?: string;
    referenceCompany?: string;
    referralCode?: string;
  };
}

export interface UserCreateData extends Omit<UserPlain, 'loginAttempts' | '_id' | 'active' | 'permissions'> {
  password: string;
  active?: boolean;
  oktaActive?: boolean;
  organizationName?: string;
  telephoneNumber?: string;
}

export interface UserUpdateData extends Pick<Partial<UserModel>, 'title' | 'jobTitle' | 'firstName' | 'surname' | 'telephoneNumber'> {
  password?: string;
  oldPassword?: string;
}

export interface LeanUserModel<T = ObjectId> extends UserPlain<T>, SafeUser<T> {}

export interface UserModel<T = ObjectId> extends LeanUserModel<T>, Document {
  _id: T;
  connection?: ServiceAccountConnection;
  passwordHash: string;
  scorecardFavourites?: T[];
  jobTitle?: string;
  telephoneNumber?: string;
  getPasswordHash(): string;
  getSafeUser(): SafeUser;
  getComplete(): CompleteUser;
  UserPlain(): UserPlain;
  getScorecardFavourites(): Promise<T[]>;
}

UserSchema.virtual('notificationPreferences', {
  ref: 'NotificationPreferences', // The model to use
  localField: '_id', // Find people where `localField`
  foreignField: 'userId', // is equal to `foreignField`
  justOne: true,
});


UserSchema.methods.getPasswordHash = function (): string {
  return this.passwordHash;
};

UserSchema.methods.getScorecardFavourites = function (): ObjectId[] {
  return this.scorecardFavourites || [];
};

UserSchema.methods.getSafeUser = function (): SafeUser {
  return {
    _id: this._id,
    title: this.title,
    name: this.firstName + ' ' + this.surname,
    firstName: this.firstName,
    surname: this.surname,
    stakeholderTypes: this.stakeholderTypes,
    organizationId: this.organizationId,
    permissions: this.permissions,
    isStaff: this.isStaff,
    staffRoles: this.staffRoles,
    isSuperUser: this.isSuperUser,
    profile: this.profile,
    lastLogin: this.lastLogin,
    active: this.active,
    oktaUserId: this.oktaUserId,
    authenticationProvider: this.authenticationProvider,
  };
};

UserSchema.methods.getComplete = function (): CompleteUser {
  return {
    ...this.getSafeUser(),
    created: this.created,
    telephoneNumber: this.telephoneNumber,
    email: this.email,
    jobTitle: this.jobTitle,
    registrationData: this.registrationData,
  };
};

UserSchema.methods.UserPlain = function (): UserPlain {
  return {
    isStaff: this.isStaff,
    staffRoles: this.staffRoles,
    isSuperUser: this.isSuperUser,
    permissions: this.permissions,
    _id: this._id,
    title: this.title,
    firstName: this.firstName,
    surname: this.surname,
    email: this.email,
    profile: this.profile,
    active: this.active,
    oktaUserId: this.oktaUserId,
    loginAttempts: this.loginAttempts,
  };
};

UserSchema.pre('save', function (next) {
  if (!this.isStaff) {
    this.staffRoles = undefined;
  }
  next();
});

const User: Model<UserModel> = model('User', UserSchema);
export default User;

export const isUserManager = (user: UserModel) => {
  return user.permissions.find(el => el.permissions.includes(UserRoles.Manager));
}

export const getFullName = (user: { firstName?: string, surname?: string }, fallback = '-') => {
  return `${user.firstName ?? ''} ${user.surname ?? ''}`.trim() || fallback;
}

interface UsersStatsQuery<T = string> {
  hideNonActive?: boolean;
  hideStaff?: boolean;
  startDate?: T;
  endDate?: T;
  searchStr?: string;
}
export type UsersStatsQueryRequest<T> = AuthenticatedRequest<any, any, any, Partial<UsersStatsQuery<T>>>;

UserSchema.index({ created: 1 }, { unique: false });
UserSchema.index({ lastLogin: 1 }, { unique: false });
UserSchema.index({ 'permissions.initiativeId': 1 });
