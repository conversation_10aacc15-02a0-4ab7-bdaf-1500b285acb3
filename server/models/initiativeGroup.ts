/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { model, Model, Schema, Types } from 'mongoose';

const InitiativeGroupInitiativeSchema = new Schema({
  initiativeId: { type: Schema.Types.ObjectId, required: true },
  weight: { type: Number, required: false }
},
{ _id: false });

const InitiativeGroupSchema = new Schema<InitiativeGroupModel>({
    code: {
        type: String,
        trim: true,
        lowercase: true,
        required: true,
        unique: true,
        validate: [/^[a-z0-9\/\-.]+$/, 'Code can only contain alphanumeric, dash(-), dot(.) and forward slash(/) ']
    },
    name: { type: String, required: true },
    group: [{ type: InitiativeGroupInitiativeSchema, required: true }],
    created: { type: Date, default: Date.now },
}, { collection: 'initiative-groups' });

export interface InitiativeGroupModel {
  _id?: Types.ObjectId;
  code: string;
  name: string;
  group: InitiativeGroupInitiativeModel[];
  created: Date;
}

export interface InitiativeGroupInitiativeModel {
  initiativeId: Types.ObjectId;
  weight: number;
}

const InitiativeGroup: Model<InitiativeGroupModel> = model('InitiativeGroup', InitiativeGroupSchema);
export default InitiativeGroup;
