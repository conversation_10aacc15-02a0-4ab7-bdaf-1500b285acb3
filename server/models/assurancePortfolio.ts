/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { model, Schema } from 'mongoose';
import { AssurancePortfolioPermissionsSchema } from './assurancePermission';
import { AssurancePortfolioPlain, AssurancePortfolioStatus } from '../service/assurance/model/AssurancePortfolio';

const AssurancePortfolioDocumentSchema = new Schema({
  type: { type: Schema.Types.String, required: true },
  documentId: { type: Schema.Types.ObjectId, required: true },
});

const AssurancePortfolioHistorySchema = new Schema({
  created: { type: Date, default: Date.now },
  action: { type: Schema.Types.String, required: true },
  userId: { type: Schema.Types.ObjectId, required: true },
  documents: [AssurancePortfolioDocumentSchema],
  utrvHistorySum: { type: Schema.Types.Number },
});

const AssurancePortfolioPlainSchema = new Schema<AssurancePortfolioPlain>({
  title: { type: Schema.Types.String, required: false },
  description: { type: Schema.Types.String, required: false },
  organizationId: { type: Schema.Types.ObjectId, required: true },
  portfolioType: { type: Schema.Types.String, required: true },
  initiativeId: { type: Schema.Types.ObjectId, required: true },
  surveyId: { type: Schema.Types.ObjectId, required: false },
  permissions: [AssurancePortfolioPermissionsSchema],
  status: {
    type: Schema.Types.String,
    required: true,
    enum: Object.values(AssurancePortfolioStatus),
    default: AssurancePortfolioStatus.Created
  },
  documents: [AssurancePortfolioDocumentSchema],
  history: [AssurancePortfolioHistorySchema],
  created: { type: Date, default: Date.now },
}, { collection: 'assurance-portfolios' });

AssurancePortfolioPlainSchema.index({ surveyId: 1 });
AssurancePortfolioPlainSchema.index({ organizationId: 1 });
AssurancePortfolioPlainSchema.virtual('survey', {
  ref: 'Survey', // The model to use
  localField: 'surveyId', // Find people where `localField`
  foreignField: '_id', // is equal to `foreignField`
  justOne: true,
});

AssurancePortfolioPlainSchema.virtual('initiative', {
  ref: 'Initiative', // The model to use
  localField: 'initiativeId', // Find people where `localField`
  foreignField: '_id', // is equal to `foreignField`
  justOne: true,
});

AssurancePortfolioPlainSchema.virtual('organization', {
  ref: 'Organization', // The model to use
  localField: 'organizationId', // Find people where `localField`
  foreignField: '_id', // is equal to `foreignField`
  justOne: true,
});

const AssurancePortfolio = model('AssurancePortfolio', AssurancePortfolioPlainSchema);


export default AssurancePortfolio;
