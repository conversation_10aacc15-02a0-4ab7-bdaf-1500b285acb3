/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import mongoose, { HydratedDocument } from 'mongoose';

export interface MetricUnitPlain {
  code: string,
  name: string,
  prefix: string,
  suffix: string,
  created?: Date,
}

type MetricUnitModel = HydratedDocument<MetricUnitPlain>;

const MetricUnitSchema = new mongoose.Schema<MetricUnitModel>({
    code: {
        type: String,
        trim: true,
        lowercase: true,
        required: true,
        unique: true,
        validate: [/^[a-z0-9\/\-\.]+$/, 'Code can only contain alphanumeric, dash(-), dot(.) and forwardslash(/) ']
    },
    name: String,
    prefix: String,
    suffix: String,
    created: { type: Date, default: Date.now },
}, { collection: 'metric-units' });


export default mongoose.model<MetricUnitModel>('MetricUnit', MetricUnitSchema);
