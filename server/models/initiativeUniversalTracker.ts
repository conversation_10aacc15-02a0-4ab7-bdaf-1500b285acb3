import { Document, Model, Schema, Types, model } from 'mongoose';
import { ValueValidation } from './public/universalTrackerType';
import {
  lockedProperties,
  numberScaleSchema,
  unitInputSchema,
  UniversalTrackerPlain,
  ValueValidationSchema,
} from './universalTracker';
import { KeysEnum } from './commonProperties';

export enum UtrvConfigValue {
  Default = 'default',
  Optional = 'optional',
  Required = 'required',
}

// mirror properties in UniversalTrackerValue
export const UTRV_CONFIG_CODES = ['verificationRequired', 'evidenceRequired', 'noteRequired', 'isPrivate'] as const;

export type UtrvConfigCode = (typeof UTRV_CONFIG_CODES)[number];

export type UtrvConfigType = Record<UtrvConfigCode, UtrvConfigValue | undefined>;

export interface InitiativeUniversalTrackerPlain<T = Types.ObjectId>
  extends Pick<UniversalTrackerPlain, '_id' | 'unitInput' | 'numberScaleInput' | 'unitLocked' | 'numberScaleLocked'> {
  valueValidation?: Pick<ValueValidation, 'decimal' | 'table' | 'variations'>;
  universalTrackerId: T;
  initiativeId: T;
  utrvConfig?: UtrvConfigType;
}

export type InitiativeUtrMin = Pick<
  InitiativeUniversalTrackerPlain,
  '_id' | 'universalTrackerId' | 'valueValidation' | 'unitInput' | 'numberScaleInput' | 'utrvConfig'
>;

export const initiativeUtrMinProjection: KeysEnum<InitiativeUtrMin> = {
  _id: 1,
  universalTrackerId: 1,
  valueValidation: 1,
  unitInput: 1,
  numberScaleInput: 1,
  utrvConfig: 1,
};

export type InitiativeUtrUnitConfig = Pick<InitiativeUniversalTrackerPlain, '_id' | 'utrvConfig'>;

export const initiativeUtrvConfigProjection: KeysEnum<InitiativeUtrUnitConfig> = {
  _id: 1,
  utrvConfig: 1,
}

export type InitiativeUniversalTrackerModel<T = Types.ObjectId> = InitiativeUniversalTrackerPlain<T> & Document;

const utrvConfigFieldSchemaDefinition = {
  type: Schema.Types.String,
  enum: Object.values(UtrvConfigValue),
  require: false,
};

const utrvConfigSchema = new Schema<UtrvConfigType>(
  {
    verificationRequired: utrvConfigFieldSchemaDefinition,
    evidenceRequired: utrvConfigFieldSchemaDefinition,
    noteRequired: utrvConfigFieldSchemaDefinition,
    isPrivate: utrvConfigFieldSchemaDefinition,
  },
  { _id: false }
);

const InitiativeUniversalTrackerSchema = new Schema<InitiativeUniversalTrackerModel>(
  {
    universalTrackerId: { type: Schema.Types.ObjectId, required: true },
    initiativeId: { type: Schema.Types.ObjectId, required: true },
    valueValidation: {
      type: ValueValidationSchema,
      required: false,
    },
    // Override default unit input value, takes priority above survey unitConfig
    unitInput: unitInputSchema,
    numberScaleInput: numberScaleSchema, // This will override the UTRV default number scale during the survey generation
    ...lockedProperties,
    utrvConfig: utrvConfigSchema,
  },
  { collection: 'initiative-universal-trackers' }
);

InitiativeUniversalTrackerSchema.virtual('universalTracker', {
  ref: 'UniversalTracker',
  localField: 'universalTrackerId',
  foreignField: '_id',
  justOne: true,
});

InitiativeUniversalTrackerSchema.virtual('initiative', {
  ref: 'Initiative',
  localField: 'initiativeId',
  foreignField: '_id',
  justOne: true,
});

const InitiativeUniversalTracker: Model<InitiativeUniversalTrackerModel> = model(
  'InitiativeUniversalTracker',
  InitiativeUniversalTrackerSchema
);

export default InitiativeUniversalTracker;
