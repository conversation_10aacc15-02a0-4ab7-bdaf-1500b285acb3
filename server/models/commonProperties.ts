/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { Schema } from 'mongoose';
import { UserPermissions, UserRoles, } from '../service/user/userPermissions';
import { ObjectId } from 'bson';
import { StaffRole, StaffRoles, StaffScope } from './staffRole';

export type KeysEnum<T, U = any> = { [P in keyof Required<T>]: U };

enum DeprecatedPermissionTypes {
  Admin = 'admin'
}

enum DashboardPermissions {
  Summary = 'summary',
  Partners = 'partners',
  Frameworks = 'frameworks',
  Financial = 'financial',
  UniversalTrackers = 'universal_trackers',
  Sustainability = 'impact',
  Data = 'data',
  Documents = 'documents',
  SurveyWorkflow = 'survey_workflow'
}

const permissionTypes = [
  ...Object.values(UserRoles),
  ...Object.values(DeprecatedPermissionTypes),
  ...Object.values(DashboardPermissions)
];

export const dashboardPermissions = {
  type: Schema.Types.String,
  trim: true,
  lowercase: true,
  enum: permissionTypes
};

const userPermissions = new Schema({
  permissions: [dashboardPermissions],
  initiativeId: Schema.Types.ObjectId,
}, { _id: false });


export interface BasicUserRolePermissions<T = ObjectId> {
  permissions: UserPermissions<T>[];
}

export interface UserRolePermissions<T = ObjectId> extends BasicUserRolePermissions<T> {
  isStaff?: boolean;
  staffRoles?: StaffRoles;
}

export const basicPermissionsSchema = {
  permissions: [userPermissions],
};

const staffRolesSchema = new Schema({
  roles: {
    type: [Schema.Types.String],
    default: [],
    enum: Object.values(StaffRole),
    required: true,
  },
  scopes: {
    type: [Schema.Types.String],
    default: [],
    enum: Object.values(StaffScope),
    required: true,
  },
}, { _id: false });

export const permissionsSchema: KeysEnum<UserRolePermissions> = {
  ...basicPermissionsSchema,
  isStaff: { type: Schema.Types.Boolean, default: false },
  staffRoles: {
    type: staffRolesSchema,
    // @TODO - make this required if isStaff is true - From 2024 Dec
    require: false,
  },
};
