/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { Schema, Model, model, Types } from 'mongoose';

const ReferenceDataSchema = new Schema<ReferenceDataModel>({
  initiativeId: {
    type: Schema.Types.ObjectId,
    required: true
  },
  effectiveDate: {
    type: Date,
    required: true
  },
  metricUnitMap: Schema.Types.Mixed,
  created: { type: Date, default: Date.now },
}, { collection: 'reference-data' });


export interface ReferenceDataModel {
  _id?: Types.ObjectId;
  initiativeId: Types.ObjectId
  effectiveDate: Date;
  metricUnitMap: unknown;
  created: Date;
}

const ReferenceData: Model<ReferenceDataModel> = model('ReferenceData', ReferenceDataSchema);
export default ReferenceData;
