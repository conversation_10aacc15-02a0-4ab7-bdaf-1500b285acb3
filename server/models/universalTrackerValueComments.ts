/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import { Model, model, Schema } from 'mongoose';
import { UserMin } from './user';

interface UniversalTrackerValueCommentsItemModel<T = ObjectId> {
  _id?: T;
  created?: Date;
  userId: T;
  text: String;
  mentions?: T[];
}

export interface UniversalTrackerValueCommentsModel<T = ObjectId> {
  _id: T;
  utrvId: T;
  created: Date;
  items: UniversalTrackerValueCommentsItemModel[];
}

const UniversalTrackerValueCommentsItemSchema = new Schema({
  created: { type: Date, default: Date.now },
  userId: { type: Schema.Types.ObjectId, required: true },
  text: { type: Schema.Types.String, required: true },
  mentions: { type: [Schema.Types.ObjectId], required: false },
});

const UniversalTrackerValueCommentsSchema = new Schema<UniversalTrackerValueCommentsModel>({
  utrvId: { type: Schema.Types.ObjectId, required: true },
  created: { type: Date, default: Date.now },
  items: {
    type: [UniversalTrackerValueCommentsItemSchema],
    default: []
  }
}, { collection: 'universal-tracker-value-comments' });

UniversalTrackerValueCommentsSchema.index({ utrvId: 1 }, { unique: true });
UniversalTrackerValueCommentsSchema.virtual('universalTrackerValue', {
  ref: 'UniversalTrackerValue', // The model to use
  localField: 'utrvId', // Find people where `localField`
  foreignField: '_id', // is equal to `foreignField`
  justOne: true,
});
UniversalTrackerValueCommentsSchema.virtual('users', {
  ref: 'User', // The model to use
  localField: 'items.userId', // Find people where `localField`
  foreignField: '_id', // is equal to `foreignField`
});
export interface UniversalTrackerValueCommentsPopulatedUsers extends UniversalTrackerValueCommentsModel {
  users: UserMin[];
}
const UniversalTrackerValueComments: Model<UniversalTrackerValueCommentsModel> = model(
  'UniversalTrackerValueComments',
  UniversalTrackerValueCommentsSchema
);

export default UniversalTrackerValueComments;
