/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { HydratedDocument, Model, model, Schema, Types } from 'mongoose';
import { generateRandomToken } from '../service/crypto/token';
import { StakeholderGroup, StakeholderGroupSchema } from './stakeholderGroup';
import { BasicUserRolePermissions, basicPermissionsSchema } from './commonProperties';
import { DelegationScopeSchema } from './common/delegationScopeSchema';
import { DelegationScope, Scope, ScopeSchema, SurveyType } from './survey';
import { EmailTemplate } from '../service/email/templates/onboarding/initial';
import { DataPeriods, UtrvType } from '../service/utr/constants';
import { emailRegex } from './user';
import { InitiativePlain } from './initiative';
import { OrganizationPermission } from './assurancePermission';
import { OrganizationEmailTemplate } from '../service/email/templates/onboarding/organizationInitial';

export enum OnboardingStatus {
  NotStarted = 'not_started',
  Pending = 'pending',
  Rejected = 'rejected',
  Complete = 'complete',
  Deleted = 'deleted',
}

export enum ObNotificationService {
  Local = 'local'
}

export enum ObNotificationCode {
  FirstReminder = 'first_reminder',
  FinalReminder = 'final_reminder',
  ManualReminder = 'manual_reminder',
}

export enum ObType {
  JoinRequest = 'join_request',
  Initiative = 'initiative',
  Organization = 'organization',
}

interface Metadata {
  domain?: string;
  app?: string;
}

const metadataSchema = new Schema<Metadata, Model<Metadata>, Metadata>({
  domain: Schema.Types.String,
  app: Schema.Types.String,
}, { _id: false });

export interface NotificationItem {
  type: 'email',
  code: ObNotificationCode,
  referenceId?: string;
  completedDate: Date
}

const notificationItemSchema = new Schema<NotificationItem, Model<NotificationItem>, NotificationItem>({
  type: {
    type: Schema.Types.String,
    required: true,
    enum: ['email']
  },
  code: {
    type: Schema.Types.String,
    required: true,
    enum: Object.values(ObNotificationCode)
  },
  completedDate: {
    type: Schema.Types.Date,
    required: true
  },
  referenceId: Schema.Types.String,
});

const organizationOnboardingRoleSchema = new Schema(
  {
    permissions: {
      type: [Schema.Types.String],
      enum: Object.values(OrganizationPermission),
      default: [OrganizationPermission.RestrictedUser],
      required: true,
    },
    modelId: Schema.Types.ObjectId,
  },
);


interface ObNotifications {
  service: ObNotificationService,
  items: NotificationItem[]
}

const DefaultNotificationService = ObNotificationService.Local;
const ObNotificationsSchema = new Schema<ObNotifications, Model<ObNotifications>, ObNotifications>({
  service: {
    type: Schema.Types.String,
    required: true,
    enum: Object.values(ObNotificationService),
    default: DefaultNotificationService,
  },
  items: { type: [notificationItemSchema], required: true, default: [] },
});

const userOnboardingSchema = new Schema<OnboardingUser>({
  title: Schema.Types.String,
  message: Schema.Types.String,
  firstName: Schema.Types.String,
  surname: Schema.Types.String,
  profile: Schema.Types.String,
  emailTemplate: {
    type: Schema.Types.String,
    enum: Object.values(EmailTemplate),
  },
  initiativeIds: {
    type: [Schema.Types.ObjectId],
    required: true
  },
  ...basicPermissionsSchema,
  email: {
    type: Schema.Types.String,
    trim: true,
    lowercase: true,
    required: true,
    validate: [emailRegex, 'Please fill a valid email address']
  },
  sfId: Schema.Types.String,
  userId: Schema.Types.ObjectId,
  complete: { type: Schema.Types.Boolean, default: false }
}, { _id: false });

const surveyOnboardingSchema = new Schema({
  code: { type: Schema.Types.String, required: true },
  sourceName: { type: Schema.Types.String, required: true },
  effectiveDate: { type: Schema.Types.Date, required: true },
  utrvType: {
    type: Schema.Types.String,
    enum: ['actual', 'baseline', 'target'],
    default: 'actual',
  },
  surveyId: Schema.Types.ObjectId,
  evidenceRequired: { type: Schema.Types.Boolean, default: false, required: true },
  verificationRequired: { type: Schema.Types.Boolean, default: true, required: true },
  scope: ScopeSchema,
  complete: { type: Schema.Types.Boolean, default: false }
}, { _id: false });

const OnboardingSchema = new Schema<OnboardingModelPlain>({
  token: {
    type: Schema.Types.String,
    trim: true,
    unique: true,
    required: true,
    default: () => generateRandomToken(30),
    validate: [/^[a-zA-Z0-9\/\-_.]+$/, 'Code can only contain alphanumeric, dash(-), dot(.) and forward slash(/) '],
  },
  type: {
    type: Schema.Types.String,
    enum: Object.values(ObType),
    default: ObType.Initiative,
    required: true,
  },
  status: {
    type: Schema.Types.String,
    required: true,
    enum: Object.values(OnboardingStatus),
    default: OnboardingStatus.NotStarted,
  },
  initiativeId: {
    type: Schema.Types.ObjectId,
    required: function () {
      return this.type === ObType.Initiative || this.type === ObType.JoinRequest;
    },
  },
  organizationId: {
    type: Schema.Types.ObjectId,
    required: function () {
      return this.type === ObType.Organization;
    },
  },
  surveyConfig: {
    type: surveyOnboardingSchema,
    required: false
  },
  utrvConfig: StakeholderGroupSchema,
  surveyStakeholders: StakeholderGroupSchema,
  assuranceStakeholders: StakeholderGroupSchema,
  delegationScope: DelegationScopeSchema,
  onboardingListId: { type: Schema.Types.ObjectId, required: false },
  user: { type: userOnboardingSchema, required: true },
  surveyRoles: { admin: [Schema.Types.ObjectId], viewer: [Schema.Types.ObjectId]},
  surveyIds: [Schema.Types.ObjectId],
  metadata: metadataSchema,
  notifications: {
    type: ObNotificationsSchema,
    required: true,
    default: {
      service: DefaultNotificationService,
      items: []
    },
  },
  created: { type: Schema.Types.Date, default: () => new Date() },
  startDate: Schema.Types.Date,
  createdBy: Schema.Types.ObjectId,
  organizationRoles: {
    type: [organizationOnboardingRoleSchema],
    required: function () {
      return this.type === ObType.Organization;
    },
    default: undefined,
  },
}, { collection: 'onboarding' });

export interface SurveyConfig<T = Types.ObjectId> {
  code: string;
  surveyId?: T;
  sourceName: string;
  period: DataPeriods;
  effectiveDate: Date;
  utrvType: UtrvType;
  evidenceRequired: boolean;
  verificationRequired: boolean;
  scope?: Scope;
  complete: boolean;
  type?: SurveyType;
}

export interface OnboardingUser<T = Types.ObjectId> extends BasicUserRolePermissions<T> {
  emailTemplate?: EmailTemplate | OrganizationEmailTemplate;
  userId?: T;
  sfId?: string;
  firstName?: string;
  surname?: string;
  profile?: string;
  email: string;
  complete: boolean;
  message?: string;
  title?: string; // Not used?
  initiativeIds?: T[];
}

export interface SurveyRoles<T = Types.ObjectId> {
  admin: T[],
  viewer: T[]
}

interface OrganizationOnboardingRole {
  permissions: OrganizationPermission[];
  modelId: Types.ObjectId;
}

export interface OnboardingCommon<T = Types.ObjectId> {
  user: OnboardingUser<T>;
  initiativeId?: T;
  organizationId?: T;
  metadata?: Metadata;
  utrvConfig?: StakeholderGroup<T>;
  surveyStakeholders?: StakeholderGroup<T>;
  /** @deprecated replaced with organizationRoles */
  assuranceStakeholders?: StakeholderGroup<T>;
  surveyRoles?: SurveyRoles<T>
  surveyIds?: T[];
  delegationScope?: DelegationScope;
  surveyConfig?: SurveyConfig;
  notifications?: ObNotifications;
  createdBy?: T;
  startDate?: Date;
  type: ObType;
  organizationRoles?: OrganizationOnboardingRole[];
}

export interface OnboardingCreateData<T = Types.ObjectId> extends OnboardingCommon<T> {
  type: ObType.Initiative | ObType.JoinRequest;
  onboardingListId?: T;
  initiativeId: T;
}

export interface OrganizationOnboardingCreateData<T = Types.ObjectId> extends OnboardingCommon<T> {
  type: ObType.Organization;
  organizationId: T;
  organizationRoles: OrganizationOnboardingRole[];
}

export interface OnboardingModelPlain<T = Types.ObjectId> extends OnboardingCommon<T> {
  _id: T;
  token: string;
  status: OnboardingStatus;
  created: Date;
  onboardingListId?: T;
  surveyConfig?: SurveyConfig;
}

export interface InitiativeOnboardingPlain<T = Types.ObjectId> extends OnboardingModelPlain<T> {
  type: ObType.Initiative | ObType.JoinRequest;
  initiativeId: T;
}

export type InitiativeOnboardingModel = HydratedDocument<InitiativeOnboardingPlain>;

export interface OrganizationOnboardingPlain<T = Types.ObjectId> extends OnboardingModelPlain<T> {
  type: ObType.Organization;
  organizationId: T;
  organizationRoles: OrganizationOnboardingRole[];
}

export type OrganizationOnboardingModel = HydratedDocument<OrganizationOnboardingPlain>;

export interface OnboardingWithSurveyConfig extends InitiativeOnboardingPlain {
  surveyConfig: SurveyConfig;
}

export interface OnboardingWithUtrConfig extends InitiativeOnboardingPlain {
  utrvConfig: StakeholderGroup;
}

export interface OnboardingWithAssurance extends OnboardingModelPlain {
  assuranceStakeholders: StakeholderGroup;
}

export interface OnboardingWithDelegationScope extends InitiativeOnboardingPlain {
  delegationScope: DelegationScope;
}

export type OnboardingMinData = Pick<InitiativeOnboardingPlain, '_id' | 'user' | 'initiativeId'>;

export const hasSurveyConfig = (ob: OnboardingModelPlain): ob is OnboardingWithSurveyConfig => ob.surveyConfig !== undefined;
export const hasUtrvConfig = (ob: OnboardingModelPlain): ob is OnboardingWithUtrConfig => ob.utrvConfig !== undefined;
export const hasAssurance = (ob: OnboardingModelPlain): ob is OnboardingWithAssurance => ob.assuranceStakeholders !== undefined;

export const isInitiativeOnboardingModel = (model: OnboardingModel): model is InitiativeOnboardingModel =>
  model.type === ObType.Initiative || model.type === ObType.JoinRequest;

export const isOrganizationOnboardingModel = (model: OnboardingModel): model is OrganizationOnboardingModel =>
  model.type === ObType.Organization;

export type OnboardingModel = HydratedDocument<OnboardingModelPlain, {
  initiative?: InitiativePlain
}>

export interface OnboardingWithNotifications extends OnboardingModel {
  notifications: ObNotifications
}

OnboardingSchema.virtual('initiative', {
  ref: 'Initiative', // The model to use
  localField: 'initiativeId', // Find people where `localField`
  foreignField: '_id', // is equal to `foreignField`
  justOne: true,
});

OnboardingSchema.index({ initiativeId: 1, status: 1 });
OnboardingSchema.index({ 'user.email': 1, status: 1 });
OnboardingSchema.index({ status: 1, created: 1 });

const Onboarding = model('Onboarding', OnboardingSchema);

export default Onboarding;
