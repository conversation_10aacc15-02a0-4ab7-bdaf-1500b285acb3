/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */


import { model, Schema } from "mongoose";
import { SponsorshipConfigPlain, SponsorshipSurveyConfig, SurveyConfigSchema } from "./sponsorshipConfig";
import { ObjectId } from "bson";
import { AcceptedAgreement } from "../types/agreement";

const AgreementSchema = new Schema<AcceptedAgreement>({
  code: {
    type: Schema.Types.String,
    required: true,
    trim: true
  },
  date: {
    type: Schema.Types.Date,
    default: () => new Date()
  },
  userId: {
    type: Schema.Types.ObjectId,
    required: true
  },
}, { _id: false });

const SubscriptionsSchema = new Schema({
  subscriptionId: { type: Schema.Types.String, required: true, trim: true },
}, { _id: false });


export interface SponsorshipPlain<D extends string | Date = string> {
  _id: ObjectId;
  sponsorshipConfigId: ObjectId;
  initiativeId: ObjectId;
  referralCode: string;
  dataShareIds: ObjectId[];

  surveyConfig?: SponsorshipSurveyConfig;

  cancelDate?: D;

  /**
   * Usually this is created + period of discount end date,
   * @example CT Starter + 12 months discount = now + 12 month;
   */
  periodEndDate: D;
  updateRequired?: D;
  lastUpdated: D;
  created: D;

  agreements?: AcceptedAgreement[];
  subscriptions?: { subscriptionId: string }[];

  sponsorshipConfig?: SponsorshipConfigPlain;

  description?: string;
}

export type SponsorshipWithSurveyConfig = Required<Pick<SponsorshipPlain, 'surveyConfig'>>;

const SponsorshipSchema = new Schema<SponsorshipPlain<Date>>({
  sponsorshipConfigId: { type: Schema.Types.ObjectId, required: true },
  initiativeId: { type: Schema.Types.ObjectId, required: true },

  referralCode: { type: Schema.Types.String, required: true },

  dataShareIds: [Schema.Types.ObjectId],
  surveyConfig: SurveyConfigSchema,

  cancelDate: Schema.Types.Date,

  // created+coupon_len
  periodEndDate: { type: Schema.Types.Date, required: true },

  updateRequired: Schema.Types.Date,

  lastUpdated: { type: Schema.Types.Date, default: () => new Date() },
  created: { type: Schema.Types.Date, default: () => new Date() },

  agreements: { type: [AgreementSchema], default: undefined },
  subscriptions: { type: [SubscriptionsSchema], default: undefined },

  description: {
    type: Schema.Types.String,
    required: false,
    trim: true,
    maxlength: 2000,
  },
}, { toJSON: { virtuals: true } });

SponsorshipSchema.virtual('sponsorshipConfig', {
  ref: 'SponsorshipConfig',
  localField: 'sponsorshipConfigId',
  foreignField: '_id',
  justOne: true,
});

SponsorshipSchema.virtual('initiative', {
  ref: 'Initiative',
  localField: 'initiativeId',
  foreignField: '_id',
  justOne: true,
});

export type SponsorshipModel = ReturnType<(typeof Sponsorship)['hydrate']>;
export type SponsorshipCreateData<D extends string | Date = string | Date> = Omit<SponsorshipPlain<D>, '_id' | 'created' | 'lastUpdated'>;

export type ExtendedSponsorshipModel = SponsorshipModel & { sponsorshipConfig: SponsorshipConfigPlain };

// Only one config per initiative
SponsorshipSchema.index({ initiativeId: 1, sponsorshipConfigId: 1 }, { unique: true });
SponsorshipSchema.index({ referralCode: 1, initiativeId: 1 }, { unique: true });
SponsorshipSchema.index({ sponsorshipConfigId: 1 });

const Sponsorship = model('Sponsorship', SponsorshipSchema);

export default Sponsorship;
