/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { HydratedDocument, InferSchemaType, model, Schema } from 'mongoose';
import type { SurveyModelPlain } from './survey';
import type { UniversalTrackerValuePlain } from './universalTrackerValue';
import { ObjectId } from 'bson';

export enum ScheduledType {
  SurveyDeadline = 'survey_deadline',
  SummaryEmail = 'summary_email',
}

export const ScheduledDateSchema = new Schema(
  {
    idempotencyKey: { type: Schema.Types.String },
    date: { type: Schema.Types.Date, required: true },
  },
  { _id: false }
);
export type ScheduledDate = InferSchemaType<typeof ScheduledDateSchema>;

export const ScheduledNotificationDataSchema = new Schema(
  {
    surveyId: Schema.Types.ObjectId,
  },
  { _id: false }
);

const ScheduledNotificationSchema = new Schema(
  {
    type: { type: Schema.Types.String, enum: Object.values(ScheduledType), required: true },
    initiativeId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    idempotencyKey: {
      type: Schema.Types.String,
      required: true,
    },
    scheduledDate: {
      type: Schema.Types.Date,
      required: true,
    },
    data: {
      type: ScheduledNotificationDataSchema,
      required: true,
    },
    completedDate: Schema.Types.Date,
    deletedDate: Schema.Types.Date,
    created: {
      type: Schema.Types.Date,
      default: Date.now,
    },
  },
  { collection: 'scheduled-notifications' }
);

ScheduledNotificationSchema.index({ initiativeId: 1, type: 1, idempotencyKey: 1 });

export type ScheduledNotificationModel = HydratedDocument<InferSchemaType<typeof ScheduledNotificationSchema>>;

export interface ScheduledNotificationSurveyDeadline extends ScheduledNotificationModel {
  _id: ObjectId,
  survey: SurveyModelPlain;
  utrvs: UniversalTrackerValuePlain[];
}

const ScheduledNotification = model<ScheduledNotificationModel>(
  'ScheduledNotificationModel',
  ScheduledNotificationSchema
);

export default ScheduledNotification;
