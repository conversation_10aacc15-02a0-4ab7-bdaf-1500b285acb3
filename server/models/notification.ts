/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { Model, model, Schema } from 'mongoose';
import {
  NotificationModel,
  NotificationCategory,
  Recipient,
} from '../service/notification/NotificationTypes';


const RecipientSchema = new Schema<Recipient>({
  userId: Schema.Types.ObjectId,
}, { _id: false });

const NotificationSchema = new Schema<NotificationModel>({
  title: { type: Schema.Types.String, required: true, trim: true },
  content: { type: Schema.Types.String, required: false, trim: true },
  category: {
    type: Schema.Types.String,
    required: true,
    trim: true,
    enum: Object.values(NotificationCategory),
  },
  topic: { type: Schema.Types.String, required: false, trim: true },
  customAttributes: Schema.Types.Mixed,
  actionUrl: { type: Schema.Types.String, required: false, trim: true },
  recipients: [RecipientSchema],
  created: { type: Date, default: Date.now },
}, { collection: 'notifications' });

const Notification: Model<NotificationModel>  = model('Notification', NotificationSchema);
export default Notification;
