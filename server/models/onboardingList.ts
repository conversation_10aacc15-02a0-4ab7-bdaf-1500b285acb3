/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { HydratedDocument, model, Schema, Types } from 'mongoose';
import { InitiativeOnboardingPlain } from './onboarding';
import { UserMin } from './user';

const OnboardingListSchema = new Schema<OnboardingListPlain>({
  code: {
    type: String,
    trim: true,
    lowercase: true,
    required: true,
    unique: true,
    validate: [/^[a-z0-9\/\-.]+$/, 'Code can only contain alphanumeric, dash(-), dot(.) and forward slash(/) ']
  },
  name: String,
  fileName: { type: String, required: false },
  initiativeId: { type: Schema.Types.ObjectId, required: false },
  createdBy: { type: Schema.Types.ObjectId, required: false },
  created: { type: Schema.Types.Date, default: Date.now },
}, { collection: 'onboarding-list' });

OnboardingListSchema.virtual('initiative', {
  ref: 'Initiative',
  localField: 'initiativeId',
  foreignField: '_id',
  justOne: true,
});

OnboardingListSchema.virtual('creator', {
  ref: 'User',
  localField: 'createdBy',
  foreignField: '_id',
  justOne: true,
});

export interface OnboardingListPlain<T = Types.ObjectId> {
  _id: T;
  name: string;
  code: string;
  fileName?: string;
  initiativeId?: T;
  createdBy?: T;
  created: Date;
}

export type OnboardingListModel = HydratedDocument<OnboardingListPlain>;

export interface OnboardingListExtended extends OnboardingListPlain {
  onboardings: InitiativeOnboardingPlain[];
  creator: UserMin;
}

const OnboardingList = model('OnboardingList', OnboardingListSchema);

export default OnboardingList;
