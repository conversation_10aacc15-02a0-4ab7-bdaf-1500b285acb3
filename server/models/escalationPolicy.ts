/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { Schema, Types } from 'mongoose';

export enum EscalationPolicyType {
  Default = 'default',
  Custom = 'custom',
  Preset = 'preset',
}

export enum EscalationAction {
  None = 'none',
  Escalate = 'escalate',
  Deescalate = 'deescalate',
}

interface EscalationHistory {
  action: EscalationAction;
  date: Date;
}

export interface EscalationPolicy<T = Types.ObjectId> {
  type: string;
  triggerDate?: Date;
  isEscalated: boolean;
  history: EscalationHistory[];
  presetId?: T;
  custom?: any;
}

const ValueHistorySchema = new Schema({
  action: {
    type: String,
    enum: [EscalationAction.Escalate, EscalationAction.Deescalate]
  },
  date: { type: Date, default: Date.now },
});

export const EscalationPolicySchema = new Schema({
  type: {
    type: Schema.Types.String,
    required: true,
    default: EscalationPolicyType.Default,
    enum: [
      EscalationPolicyType.Default,
      EscalationPolicyType.Preset,
      EscalationPolicyType.Custom
    ]
  },
  triggerDate: { type: Schema.Types.Date, required: false },
  isEscalated: { type: Schema.Types.Boolean, required: true },
  history: { type: [ValueHistorySchema], required: true },
  presetId: { type: Schema.Types.ObjectId, required: false },
  custom: { type: Schema.Types.Mixed, required: false },
}, { _id: false });
