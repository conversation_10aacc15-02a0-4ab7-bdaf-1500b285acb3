/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { HydratedDocument, model, Schema, Types } from 'mongoose';
import { StakeholderGroup, StakeholderGroupSchema } from './stakeholderGroup';
import { EscalationPolicySchema } from './escalationPolicy';

const UniversalTrackerScheduleHistorySchema = new Schema({
    created: { type: Date, default: Date.now },
    effectiveDate: { type: Date, default: Date.now },
    universalTrackerValueId: Schema.Types.ObjectId,
}, { _id: false });

const UniversalTrackerScheduleSchema = new Schema<UniversalTrackerScheduleModel>({
    universalTrackerId: Schema.Types.ObjectId,
    initiativeId: Schema.Types.ObjectId,
    userId: Schema.Types.ObjectId,
    created: { type: Date, default: Date.now },
    code: {
        type: String,
        trim: true,
        lowercase: true,
        required: true,
        unique: true,
        validate: [/^[a-z0-9\/\-.]+$/, 'Code can only contain alphanumeric, dash(-), dot(.) and forward slash(/) ']
    },
    name: String,
    type: {
        type: String,
        enum: ['actual', 'baseline', 'target']
    },
    instructions: { type: String, required: false },
    evidenceRequired: { type: Boolean, default: false, required: true },
    verificationRequired: { type: Boolean, default: true, required: true },
    stakeholders: { type: StakeholderGroupSchema, required: true },
    escalationPolicy: { type: EscalationPolicySchema, required: false },
    // advanceNotification: { type: Number, default: 0 }, // 30d, 7d, 1d?
    enabled: {
        type: Boolean,
        default: true
    },
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },
    cronSchedule: { type: String, required: true },
    lastRunDate: { type: Date, required: false },
    nextRunDate: { type: Date, required: false },
    history: {
        type: [UniversalTrackerScheduleHistorySchema]
    }
}, { collection: 'universal-tracker-schedules' });


interface History {
  created?: Date;
  effectiveDate: Date;
  universalTrackerValueId: Types.ObjectId;
}

export interface UniversalTrackerSchedulePlain<T = Types.ObjectId> {
  _id: T;
  evidenceRequired: boolean;
  verificationRequired?: boolean;
  enabled: boolean;
  instructions?: string;
  stakeholders?: StakeholderGroup<T>;
  escalationPolicy?: unknown;
  startDate: Date;
  nextRunDate?: Date;
  lastRunDate?: Date;
  cronSchedule: string;
  name: string;
  code: string;
  type: string;
  endDate: Date;
  universalTrackerId: T;
  initiativeId: T;
  history: History[];
  created: Date;
  userId: T;
}

export type UniversalTrackerScheduleModel = HydratedDocument<UniversalTrackerSchedulePlain>;

UniversalTrackerScheduleSchema.index({ universalTrackerId: 1, initiativeId: 1, type: 1 }, { unique: true });

const UniversalTrackerSchedule = model<UniversalTrackerScheduleModel>('UniversalTrackerSchedule', UniversalTrackerScheduleSchema);

UniversalTrackerScheduleSchema.statics.findByIdExtended = function (id: Types.ObjectId) {
  return UniversalTrackerSchedule.aggregate([
    {
      $match: { _id: new Types.ObjectId(id) }
    },
    {
      $lookup: {
        from: 'universal-tracker-values',
        localField: 'history.universalTrackerValueId',
        foreignField: '_id',
        as: 'universalTrackerValues'
      }
    }
  ]).then(result => Array.isArray(result) ? result.pop() : result);
};

export default UniversalTrackerSchedule;
