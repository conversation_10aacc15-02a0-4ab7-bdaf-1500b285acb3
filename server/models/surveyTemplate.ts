import { HydratedDocument, model, Model, Schema } from 'mongoose';
import { ObjectId } from 'bson';
import { SurveyModelMinData, ScopeSchema, SurveyType } from './survey';
import { UnitConfigSchema } from '../service/units/unitTypes';
import { Blueprints, DefaultBlueprintCode } from '../survey/blueprints';
import { ScheduledDateSchema } from './scheduledNotification';

export interface SurveyTemplateMinData<T = ObjectId>
  extends Pick<
    SurveyModelMinData<T>,
    | 'isPrivate'
    | 'verificationRequired'
    | 'evidenceRequired'
    | 'noteRequired'
    | 'scope'
    | 'initiativeId'
    | 'unitConfig'
    | 'noteInstructions'
    | 'noteInstructionsEditorState'
    | 'deadlineDate'
    | 'scheduledDates'
  > {
  type: SurveyType;
  sourceName: Blueprints;
  name: string;
  surveyName?: string;
  useInitiativeSettings?: boolean;
  hasCurrentInitiatives?: boolean;
  reportingLevels?: ObjectId[];
}

export interface SurveyTemplatePlain<T = ObjectId> extends SurveyTemplateMinData<T> {
  _id: T;
  created: Date;
  lastUpdated: Date;
  deletedDate?: Date;
}

export type SurveyTemplateModel<T = ObjectId> = HydratedDocument<SurveyTemplatePlain<T>>;

export const SurveyTemplateFields = {
  type: { type: Schema.Types.String, enum: Object.values(SurveyType), default: SurveyType.Default, required: true },
  name: { type: Schema.Types.String, required: true },
  isPrivate: { type: Boolean, default: false, required: true },
  verificationRequired: { type: Boolean, default: false, required: true },
  evidenceRequired: { type: Boolean, default: false, required: true },
  noteRequired: { type: Boolean, default: false },
  noteInstructions: {
    type: Schema.Types.String,
    trim: true,
    maxLength: 2000
  },
  noteInstructionsEditorState: Schema.Types.Mixed,
  sourceName: { type: Schema.Types.String, required: true, default: DefaultBlueprintCode },
  scope: ScopeSchema,
  unitConfig: UnitConfigSchema,
  useInitiativeSettings: { type: Schema.Types.Boolean, default: false },
  lastUpdated: { type: Date, default: Date.now },
  surveyName: Schema.Types.String,
  hasCurrentInitiatives: { type: Boolean, default: false, required: true },
  reportingLevels: { type: [Schema.Types.ObjectId] },
};

export const deadlineFields = {
  deadlineDate: {
    type: Schema.Types.Date,
    required: function (this: SurveyTemplateModel) {
      return this.scheduledDates && this.scheduledDates.length > 0;
    },
  },
  scheduledDates: [ScheduledDateSchema],
}

const SurveyTemplateSchema = new Schema<SurveyTemplateModel>(
  {
    ...SurveyTemplateFields,
    initiativeId: { type: Schema.Types.ObjectId, required: true },
    created: { type: Schema.Types.Date, default: Date.now },
    deletedDate: Schema.Types.Date,
    /** @deprecated */
    ...deadlineFields,
  },
  { collection: 'survey-templates', toJSON: { virtuals: true } }
);

SurveyTemplateSchema.index({ initiativeId: 1 });

SurveyTemplateSchema.virtual('initiative', {
  ref: 'Initiative',
  localField: 'initiativeId',
  foreignField: '_id',
  justOne: true,
});

export const SurveyTemplate: Model<SurveyTemplateModel> = model('SurveyTemplate', SurveyTemplateSchema);
