/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { model, Model, Schema, Types } from 'mongoose';
import {
  OrganizationPermission,
  OrganizationPermissionsSchema,
  AssurancePermissionType,
} from './assurancePermission';
import { ObjectId } from 'bson';

export enum OrganizationPartnerTypes {
  CreditRatingAgencies = 'credit_rating_agencies',
  Index = 'index',
  StandardsGuidelinesFrameworks = 'standards_guidelines_frameworks',
  SustainabilityEsgRating = 'sustainability_esg_rating',
  Assurer = 'assurer',
  Other = 'other',
  Financial = 'financial',
  Delivery = 'delivery',
  Beneficiary = 'beneficiary',
}

const partnerTypes = [
  OrganizationPartnerTypes.Financial,
  OrganizationPartnerTypes.Delivery,
  OrganizationPartnerTypes.Beneficiary,
  OrganizationPartnerTypes.Assurer,
  OrganizationPartnerTypes.Other,
  OrganizationPartnerTypes.CreditRatingAgencies,
  OrganizationPartnerTypes.Index,
  OrganizationPartnerTypes.StandardsGuidelinesFrameworks,
  OrganizationPartnerTypes.SustainabilityEsgRating,
];

export interface AddressPlain {
  line1: string;
  line2?: string;
  city?: string;
  postcode: string;
  country?: string;
}

export const AddressSchema = new Schema<AddressPlain>({
  line1: Schema.Types.String,
  line2: Schema.Types.String,
  city: Schema.Types.String,
  postcode: Schema.Types.String,
  country: Schema.Types.String, // Need to validate according to 3-letter code
}, { _id: false })

const OrganizationSchema = new Schema<OrganizationModel>({
  code: {
    type: Schema.Types.String,
    trim: true,
    lowercase: true,
    required: true,
    unique: true,
    validate: [/^[a-z0-9\/\-.]+$/, 'Code can only contain alphanumeric, dash(-), dot(.) and forward slash(/) ']
  },
  name: Schema.Types.String,
  address: AddressSchema,
  organizationType: {
    type: Schema.Types.String,
    trim: true,
    lowercase: true,
    enum: ['private', 'public']
  },
  partnerTypes: [{
    type: Schema.Types.String,
    trim: true,
    lowercase: true,
    enum: partnerTypes
  }],
  profile: Schema.Types.String,
  parentId: Schema.Types.ObjectId,
  created: { type: Date, default: Date.now },
  permissions: [OrganizationPermissionsSchema],
}, { collection: 'organizations' });

export interface OrganizationPlain<T = Types.ObjectId> {
  _id: T;
  code: string;
  profile?: string;
  name: string;
  organizationType: string;
  partnerTypes?: string[];
  address?: AddressPlain;
  permissions?: AssurancePermissionType<OrganizationPermission>[];
  created?: Date;
}

export interface OrganizationModel<T = Types.ObjectId> extends OrganizationPlain<T> {
  _id: T;
  parentId?: T;
}

const Organization: Model<OrganizationModel> = model('Organization', OrganizationSchema);
export default Organization;

export const isOrganizationAssurer = (org: OrganizationModel | null) => {
  if(org === null) {
    return false
  }
  return org.partnerTypes && org.partnerTypes.includes(OrganizationPartnerTypes.Assurer)
}

export type OrganizationPermissionsMin = Pick<OrganizationPlain, '_id' | 'permissions'>;

export type OrganizationLike = OrganizationPermissionsMin | string | ObjectId | undefined;

export type OrganizationCreate = Omit<OrganizationPlain, '_id'>;

export const isOrganizationPermissionsMin = (
  organizationOrId: OrganizationLike
): organizationOrId is OrganizationPermissionsMin => {
  return typeof organizationOrId === 'object' && 'permissions' in organizationOrId;
};
