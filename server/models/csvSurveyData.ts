/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { Schema, Types, model } from 'mongoose';

interface CSVSurveyDataModel<T = Types.ObjectId> {
  _id?: T,
  name?: string,
  description?: string,
  effectiveDate: Date,
  data: unknown,
  dataType: string,
  startRow: number,
  universalTrackers: T[],
  initiativeCodeColumn: string,
  sourceReferenceColumn: string,
  initiativeCodeMappingData: unknown,
  importConfigurationData: unknown,
  created: Date,
}

const csvSurveyDataSchema = new Schema<CSVSurveyDataModel>({
    name: String,
    description: String,
    effectiveDate: { type: Date, default: Date.now },
    data: Schema.Types.Mixed,
    dataType: {
        type: String,
        enum: ['actual', 'baseline']
    },
    startRow: { type: Number, default: 1 },
    universalTrackers: [Schema.Types.ObjectId],
    initiativeCodeColumn: String,
    sourceReferenceColumn: String,
    initiativeCodeMappingData: Schema.Types.Mixed,
    importConfigurationData: Schema.Types.Mixed,
    created: { type: Date, default: Date.now },
}, { collection: 'survey-data' });

const CSVSurveyData = model<CSVSurveyDataModel>('CSVSurveyData', csvSurveyDataSchema);
export default CSVSurveyData;
