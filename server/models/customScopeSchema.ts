/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { Schema } from "mongoose";

// Limit to what we care about now.
const scopeTypes = ['standards', 'frameworks', 'custom'] as const;
type CustomScopeType = typeof scopeTypes[number];

export interface CustomScope {
  productCode?: string;
  scopeType: CustomScopeType;
  code: string;
  required: boolean;
  /** If this is not required, but should be added during registration **/
  onRegistration?: boolean;
}

export const CustomScopeSchema = new Schema<CustomScope>({
  productCode: Schema.Types.String,
  code: {
    type: Schema.Types.String,
    required: true,
    trim: true
  },
  scopeType: {
    type: Schema.Types.String,
    required: true,
    enum: scopeTypes
  },
  required: {
    type: Schema.Types.Boolean,
    required: true,
    default: false
  },
  onRegistration: {
    type: Schema.Types.Boolean,
    required: false,
    default: undefined
  },
}, { _id: false });
