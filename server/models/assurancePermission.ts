/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { Schema, Types } from 'mongoose';

export enum OrganizationPermission {
  Admin = 'admin',
  Assurer = 'user',
  Viewer = 'viewer',
  RestrictedUser = 'restricted',
}

export enum AssurancePortfolioPermission {
  Admin = 'admin',
  Assurer = 'user',
  RestrictedUser = 'restricted',
}

export enum UtrvAssurancePermissions {
  AssurerFollower = 'assurer_follower'
}

export interface AssurancePermissionType<T extends string> {
  userId: Types.ObjectId,
  permissions: T[],
}

const assurancePortfolioPermissions = {
  type: Schema.Types.String,
  trim: true,
  lowercase: true,
  enum: Object.values(AssurancePortfolioPermission),
};

export const AssurancePortfolioPermissionsSchema = new Schema<AssurancePermissionType<string>>({
  userId: Schema.Types.ObjectId,
  permissions: [assurancePortfolioPermissions],
}, { _id: false });

const utrvAssurancePermissions = {
  type: Schema.Types.String,
  trim: true,
  lowercase: true,
  enum: [UtrvAssurancePermissions.AssurerFollower],
};

export const UtrvAssurancePermissionsSchema = new Schema<AssurancePermissionType<string>>({
  userId: Schema.Types.ObjectId,
  permissions: [utrvAssurancePermissions],
}, { _id: false });

const organizationPermissions = {
  type: Schema.Types.String,
  trim: true,
  lowercase: true,
  enum: Object.values(OrganizationPermission),
};

export const OrganizationPermissionsSchema = new Schema<AssurancePermissionType<string>>(
  {
    userId: Schema.Types.ObjectId,
    permissions: [organizationPermissions], // Support multiple permissions but for now only one per user
  },
  { _id: false }
);
