import { model, Schema } from "mongoose";

export interface AIInteractionPlain {
  model: string;
  prompt: string;
  maxTokens: number;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  created: Date;
  updated: Date;
}

const AIInteractionSchema = new Schema<AIInteractionPlain>(
  {
    model: { type: Schema.Types.String, required: true },
    prompt: { type: Schema.Types.String, required: true },
    maxTokens: { type: Schema.Types.Number, required: true },
    usage: {
      promptTokens: { type: Schema.Types.Number, required: true },
      completionTokens: { type: Schema.Types.Number, required: true },
      totalTokens: { type: Schema.Types.Number, required: true },
    },
    created: { type: Schema.Types.Date, required: true, default: Date.now },
    updated: { type: Schema.Types.Date, required: true, default: Date.now },
  },
  { collection: 'ai-interactions', timestamps: { createdAt: 'created', updatedAt: 'updated' } }
);

export const AIInteraction = model('AIInteraction', AIInteractionSchema);

