/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import { ObjectId } from "bson";
import { SupportedMeasureUnits } from "../../service/units/unitTypes";
import { Option } from './valueList';
import { EditorState } from '../../types/editorState';

// This file should not contain any imports from other source files
// Only third party libs to avoid circular deps issues.

export enum UtrValueType {
  Number = 'number',
  Sample = 'sample', // @deprecated
  Percentage = 'percentage',
  Text = 'text',
  Date = 'date',
  ValueListMulti = 'valueListMulti', // MultiSelect
  ValueList = 'valueList', // Single Select
  NumericValueList = 'numericValueList', // Numeric Multi Checkbox
  TextValueList = 'textValueList', // Multi Checkbox
  Table = 'table',
}

export const simpleUtrValueTypes = [
  UtrValueType.Number,
  UtrValueType.Percentage,
  UtrValueType.Date,
  UtrValueType.Text
];

export enum ValueValidationType {
  List = 'list',
  Custom = 'custom',
}

export enum ColumnType {
  Number = 'number',
  Text = 'text',
  Date = 'date',
  ValueList = 'valueList',
  ValueListMulti = 'valueListMulti',
  Percentage = 'percentage',
}

export interface TableColumn {
  type: ColumnType | string;
  valueAggregation?: ColumnValueAggregation
  code: string;
  name: string;
  shortName?: string;
  options?: Option[];
  unit?: string;
  unitType?: string;
  numberScale?: string;
  unitInput?: string;
  numberScaleInput?: string;
  unitLocked?: boolean;
  numberScaleLocked?: boolean;
  instructions?: string;
  listId?: ObjectId;
  calculation?: {
    formula: string;
  };
  visibilityRule?: {
    formula: string;
  };
  validation?: {
    max?: number;
    min?: number;
    decimal?: number;
    variations?: Variation[];
    required?: boolean;
    allowCustomOptions?: boolean;
  };
}

export interface ValueTable<CT extends TableColumn = TableColumn> {
  aggregation?: TableAggregation;
  validation?: {
    maxRows: number,
  };
  columns: CT[];
}

export enum VariationDataSource {
  LastMonth = 'last_month',
  LastYear = 'last_year',
  LatestVerified = 'latest_verified',
}

export enum VariationType {
  Percentage = 'percentage',
}

export interface Variation {
  type: VariationType;
  min: number;
  max: number;
  dataSource: VariationDataSource;
  confirmationRequired: boolean;
}

export interface ValueValidation<VT extends ValueTable = ValueTable> {
  min?: number;
  max?: number;
  decimal?: number;
  variations?: Variation[];
  valueList?: {
    list?: Option[];
    type: ValueValidationType;
    listId?: ObjectId;
    custom?: Option[];
    allowCustomOptions?: boolean;
  };
  table?: VT;
}

interface Tags {
  [key: string]: string[]
}

/**
 * Alternative represents text information about the questions from standard
 * perspective. Each Standard will have it's own valueLabel and type (code of the standard)
 */
export interface Alternative {
  /** Represent a name of the question, shorter than valueLabel **/
  name: string;
  /** Question label that is displayed as title when answering question **/
  valueLabel: string;
  /**
   * Suppose to be a specific code used by standards, but was miss-used and now
   * mostly work as prefix for question type
   **/
  typeCode?: string;
  /**
   * Represents a connection to standards subgroups using subgroup codes,
   * In survey scope view: navigating to standard subgroups, these tags will be
   * used to assign questions to subgroup cards based on these tags
   *
   * Example: typeTags: ["gri-1", "gri-1-102"]
   */
  typeTags?: string[];

  /** General description of the question, not used much **/
  description?: string;

  /**
   * Data entry guidance, where to get data or format we expect it be entered in.
   * Sometimes also contains what kind of evidence is expected to be provided.
   **/
  instructions?: string;

  /** JSON structure representing rich text editor state **/
  instructionsEditorState?: EditorState;

  /** URL link to external resource that contains additional instruction information **/
  instructionsLink?: string;

  /** Additional evidence instruction, displayed as tooltip in question view **/
  evidenceInstructions?: string;

  /** Used to represent a translation of the question rather than alternative standard text **/
  alternativeType?: 'language';

  /**
   * Two-letter code of the language
   * Currently supported - ['fr', 'de', 'es']
   **/
  languageCode?: string;
}

export interface UniversalTrackerPublic<T = string, U = Tags> extends Omit<Alternative, 'languageCode' | 'alternativeType'> {
  /** Unique Id **/
  _id: T;
  /** Unique code representing a question **/
  code: string;
  /**
   * type (or code) that represents a standard code or
   * custom_kpi also know as custom metric (private question) that is owned by
   * the organization, this will require ownerId to be populated as well
   **/
  type: string;
  /**
   * Alternative text, instructions that represents the same questions, but use
   * different labels to represent it in the UI
   */
  alternatives?: { [key: string]: Alternative };
  /**
   * Only works when type is `custom_kpi`
   * Private, owned by specific company
   */
  ownerId?: T;
  /**
   * Tags represents a connection to frameworks, where each tag is a reference
   * to frameworks code
   *
   * "tags" : {
   *   "tcfd" : [],
   *   "pic" : ["poverty"],
   *   "sec" : [ "diversity", "labor" ],
   *   "see" : [ "see-social", "see-economic" ],
   *   "ctl" : [],
   *   "dei" : [ "dei-inclusion", "dei-equality" ]
   *  }
   *
   *  Use to associate questions with frameworks in scope view
   */
  tags?: U;

  /**
   * Represent a type of data we expect to receive. Represented by UtrValueType enum
   *
   * UtrValueType Options:
   *   Number = 'number',
   *   Sample = 'sample', // @deprecated
   *   Percentage = 'percentage',
   *   Text = 'text',
   *   Date = 'date',
   *   ValueListMulti = 'valueListMulti', // MultiSelect
   *   ValueList = 'valueList', // Single Select
   *   NumericValueList = 'numericValueList', // Numeric Multi Checkbox
   *   TextValueList = 'textValueList', // Multi Checkbox
   *   Table = 'table',
   *
   *   @link UtrValueType
   */
  valueType: string;

  /** First time this question was created */
  created: Date;

  /**
   * type of unit, like
   *
   *   time = "time",
   *   area = "area",
   *   mass = "mass",
   *   volume = "volume",
   *   energy = "energy",
   *   currency = "currency",
   *   co2Emissions = "co2Emissions",
   *   partsPer = 'partsPer',
   *   numberScale = 'numberScale',
   *   length = 'length',
   **/
  unitType?: SupportedMeasureUnits;

  /** Code representing measure unit, based on type **/
  unit?: string;

  /**
   *  Contains validation logic such as min, max
   *  Or option list for valueList types
   *  or table configuration for table valueType
   *
   *  "valueValidation" : {
   *    "table" : {
   *      "columns" : [
   *          {
   *              "code" : "org_scale_employees_male",
   *              "name" : "i. Total Number of Employees - Male",
   *              "type" : "number",
   *              "shortName" : "i. Total Number of Employees",
   *          },
   *          {
   *              "code" : "total_employees",
   *              "name" : "Total Employees",
   *              "type" : "number",
   *              "validation" : {
   *                  "required" : false,
   *                  "allowCustomOptions" : false,
   *                  "min" : 0,
   *                  "max" : 100
   *              },
   *          },
   *        ],
   *        "validation" : {
   *            "maxRows" : 1
   *        }
   *    }
   **/
  valueValidation?: ValueValidation;

  /**
   * What is considered to be positive impact,
   * usually represented by green colour in a chart
   **/
  targetDirection: 'increase' | 'decrease' | 'tracker';

  numberScale?: string;
}

export enum ValueAggregation {
  ValueSumAggregator = 'valueSumAggregator',
  ValueCountAggregator = 'valueCountAggregator',   // Just count utrvs
  ValueAverageAggregator = 'valueAverageAggregator',
  ValueConcatenateAggregator = 'valueConcatenateAggregator',
  ValueListCountAggregator = 'valueListCountAggregator',
  NumericValueListAverageAggregator = 'numericValueListAverageAggregator',
  TextCountAggregator = 'textCountAggregator',
  NumericValueListSumAggregator = 'numericValueListSumAggregator',
  TableColumnAggregator = 'tableAggregator',
  TableConcatenationAggregator = 'tableConcatenationAggregator', // Just appends row as new rows
  /** Group rows first and then apply column level aggregation for each grouped row **/
  TableRowGroupAggregator = 'tableRowGroupAggregator',
  LatestAggregator = 'latestAggregator',
  EmptyAggregator = 'emptyAggregator',
}

export enum ColumnValueAggregation {
  ColumnSumAggregator = 'columnSumAggregator',
  ColumnAverageAggregator = 'columnAverageAggregator',
  ColumnLatestAggregator = 'columnLatestAggregator',
  ColumnEmptyAggregator = 'columnEmptyAggregator',
  ColumnConcatenateAggregator = 'columnConcatenateAggregator'
}

type ValueAggregationCompatibilityInterface = {
  [key in UtrValueType]: {
    default: ValueAggregation,
    compatible?: ValueAggregation[]
  }
}
type ColumnAggregationCompatibilityInterface = {
  [key in ColumnType]: {
    default: ColumnValueAggregation,
    compatible?: ColumnValueAggregation[]
  }
}

const defaultCompatibile = [
  ValueAggregation.LatestAggregator,
  ValueAggregation.EmptyAggregator,
  ValueAggregation.ValueCountAggregator,
];

const defaultLatestCompatibilty = {
  default: ValueAggregation.LatestAggregator,
  compatible: [
    ValueAggregation.EmptyAggregator,
    ValueAggregation.ValueCountAggregator,
  ]
}

export const ValueAggregationSiblingsCompatibility: ValueAggregationCompatibilityInterface = {
  [UtrValueType.Number]: {
    default: ValueAggregation.ValueSumAggregator,
    compatible: [
      ...defaultCompatibile,
      ValueAggregation.ValueAverageAggregator,
    ]
  },
  [UtrValueType.Percentage]: {
    default: ValueAggregation.ValueAverageAggregator,
    compatible: [
      ...defaultCompatibile,
      ValueAggregation.ValueSumAggregator
    ]
  },

  [UtrValueType.Table]: {
    default: ValueAggregation.TableColumnAggregator,
    compatible: [
      ...defaultCompatibile,
      ValueAggregation.TableConcatenationAggregator,
      ValueAggregation.TableRowGroupAggregator,
    ]
  },

  [UtrValueType.NumericValueList]: {
    default: ValueAggregation.NumericValueListSumAggregator,
    compatible: [...defaultCompatibile, ValueAggregation.NumericValueListAverageAggregator],
  },

  [UtrValueType.ValueListMulti]: {
    default: defaultLatestCompatibilty.default,
    compatible: [
      ...defaultCompatibile,
      ValueAggregation.ValueListCountAggregator
    ]
  },
  [UtrValueType.ValueList]: {
    default: defaultLatestCompatibilty.default,
    compatible: [
      ...defaultCompatibile,
      ValueAggregation.ValueListCountAggregator
    ]
  },
  [UtrValueType.Text]: {
    default: defaultLatestCompatibilty.default,
    compatible: [
      ...defaultCompatibile,
      ValueAggregation.TextCountAggregator,
      ValueAggregation.ValueConcatenateAggregator,
    ]
  },
  [UtrValueType.Date]: {
    default: defaultLatestCompatibilty.default,
    compatible: [
      ...defaultCompatibile,
      ValueAggregation.TextCountAggregator
    ]
  },

  // Unhandled
  [UtrValueType.Sample]: defaultLatestCompatibilty,
  [UtrValueType.TextValueList]: {
    default: defaultLatestCompatibilty.default,
    compatible: [
      ...defaultCompatibile,
      ValueAggregation.TextCountAggregator,
    ]
  },
}

export const ValueAggregationChildrenCompatibility: ValueAggregationCompatibilityInterface = {
  [UtrValueType.Number]: {
    default: ValueAggregation.ValueSumAggregator,
    compatible: []
  },
  [UtrValueType.Percentage]: {
    default: ValueAggregation.ValueAverageAggregator,
    compatible: []
  },

  [UtrValueType.Table]: {
    default: ValueAggregation.TableColumnAggregator,
    compatible: [
      ValueAggregation.TableRowGroupAggregator,
    ]
  },

  [UtrValueType.NumericValueList]: {
    default: ValueAggregation.NumericValueListSumAggregator,
    compatible: []
  },

  [UtrValueType.ValueListMulti]: {
    default: defaultLatestCompatibilty.default,
    compatible: []
  },
  [UtrValueType.ValueList]: {
    default: defaultLatestCompatibilty.default,
    compatible: []
  },
  [UtrValueType.Text]: {
    default: defaultLatestCompatibilty.default,
    compatible: []
  },
  [UtrValueType.Date]: {
    default: defaultLatestCompatibilty.default,
    compatible: []
  },

  // Unhandled
  [UtrValueType.Sample]: defaultLatestCompatibilty,
  [UtrValueType.TextValueList]: defaultLatestCompatibilty,
}

export const ColumnAggregationCompatibility: ColumnAggregationCompatibilityInterface = {
  [ColumnType.Number]: {
    default: ColumnValueAggregation.ColumnSumAggregator,
    compatible: [
      ColumnValueAggregation.ColumnAverageAggregator,
      ColumnValueAggregation.ColumnLatestAggregator,
      ColumnValueAggregation.ColumnEmptyAggregator
    ]
  },
  [ColumnType.Percentage]: {
    default: ColumnValueAggregation.ColumnAverageAggregator,
    compatible: [
      ColumnValueAggregation.ColumnSumAggregator,
      ColumnValueAggregation.ColumnLatestAggregator,
      ColumnValueAggregation.ColumnEmptyAggregator
    ]
  },
  [ColumnType.Text]: {
    default: ColumnValueAggregation.ColumnLatestAggregator,
    compatible: [
      ColumnValueAggregation.ColumnEmptyAggregator,
      ColumnValueAggregation.ColumnConcatenateAggregator
    ]
  },
  [ColumnType.Date]: {
    default: ColumnValueAggregation.ColumnLatestAggregator,
    compatible: [
      ColumnValueAggregation.ColumnEmptyAggregator
    ]
  },
  [ColumnType.ValueListMulti]: {
    default: ColumnValueAggregation.ColumnLatestAggregator,
    compatible: [
      ColumnValueAggregation.ColumnEmptyAggregator
    ]
  },
  [ColumnType.ValueList]: {
    default: ColumnValueAggregation.ColumnLatestAggregator,
    compatible: [
      ColumnValueAggregation.ColumnEmptyAggregator
    ]
  },
}

export enum TableAggregationType {
  Group = 'group',
}

/**
 * Allow to group using list of columns
 */
export interface TableGroupColumn {
  /** Column code to group by **/
  code: string,
  // /** Aggregation override type for this column **/
  // valueAggregation?: ColumnValueAggregation
}

interface TableGroupAggregation {
  type: TableAggregationType.Group,
  columns: TableGroupColumn[]
}

export type TableAggregation = TableGroupAggregation;
