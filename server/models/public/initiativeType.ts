/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { ObjectId } from "bson";
import { DataShareMin } from "../../repository/DataShareRepository";
import { KeysEnum } from "./projectionUtils";

export interface InitiativeMin<T = ObjectId> {
  _id: T,
  code: string,
  name: string,
  type: string,
  startDate?: string,
  endDate?: string,
  profile?: string,
  industry?: any;
  industryText: string;
  parentId?: T;
  dataShare?: DataShareMin;
}


export interface InitiativePublicMin<T = ObjectId> {
  _id: T,
  name: string,
  /** Public image url **/
  profile?: string,
  /** Parent id of the current node **/
  parentId?: T;
  /** ISO 8601 date string **/
  created: string
}

export const initiativePublicMinProjection : KeysEnum<InitiativePublicMin, 1> = {
  _id: 1,
  name: 1,
  profile: 1,
  parentId: 1,
  created: 1,
}
