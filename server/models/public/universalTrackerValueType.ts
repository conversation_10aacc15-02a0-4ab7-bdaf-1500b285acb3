/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import { ObjectId } from "bson";
import { UniversalTrackerPublic, UtrValueType } from "./universalTrackerType";
import type { DataPeriods } from "../../service/utr/constants";
import type { UtrvAssuranceStatus } from "../universalTrackerValue";
import type { EvidenceData } from "../../types/universalTrackerValue";
import type { LatestUtrvNotes, UtrvNote } from "../common/utrvNote";


export interface StakeholderGroup<T = ObjectId> {
  stakeholder: T[];
  verifier: T[];
  escalation: T[];
}

interface UtrvValueHistory<T = ObjectId> extends Partial<Pick<UtrvNote, 'note' | 'editorState'>>{
  action: string;
  userId: T;
  value?: number;
  date?: Date;
  evidence?: T[];
  unit?: string;
  numberScale?: string;
  valueType?: UtrValueType;
  valueData?: {
    data: any;
    explain?: string;
  };
}

// We can re-use later on like for filling data from AI or connections.
export enum ValueDataSourceType {
  AIAutoAnswer = 'ai_auto_answer',
  ExternalConnection = 'external-connection',
  SecondaryConnection = 'secondary-connection',
}
export interface AIAutoAnswerSource {
  type: ValueDataSourceType.AIAutoAnswer;
  data?: {
    model: string;
    backgroundJobId: ObjectId;
  };
}

interface SecondaryConnectionSourceData {
  calculationGroupCode: string;
  connectionId: ObjectId;
  value: number | string;
  unit?: string;
  numberScale?: string;
  valueListCode?: string;
}

interface ExternalConnectionSourceData extends SecondaryConnectionSourceData {
  integrationCode: string;
}

export interface ExternalConnectionSource {
  type: ValueDataSourceType.ExternalConnection;
  data: ExternalConnectionSourceData;
}

export interface SecondaryConnectionSource {
  type: ValueDataSourceType.SecondaryConnection;
  data: SecondaryConnectionSourceData;
}

export type ValueDataSource = AIAutoAnswerSource | ExternalConnectionSource | SecondaryConnectionSource;

export interface RowData {
  code: string;
  numberScale?: string;
  unit?: string;
  value: any;
  source?: ValueDataSource;
}
export type TableData = RowData[][];

export interface InputRowData extends RowData {
  numberScale: string | undefined;
  unit: string | undefined;
}
export type InputTableData = InputRowData[][];

export type TableDataType = TableData | InputTableData;
export type RowDataType = RowData | InputRowData;

export interface ValueData<T = any> {
  data?: T;
  table?: TableData;
  explain?: string;
  notApplicableType?: string;
  isImported?: boolean;
  input?: {
    table?: InputTableData;
    data?: T;
    /** Storing this for percentage and numeric type utrs **/
    value?: number;
    
    /** unit and numberScale are no longer optional, but have to be defined for value input */
    /** There is a trade-off for text, valueListText or table utrs when we have to define unit and numberScale */
    
    /** unit code, based on utr unitType property, km2, kJ, h etc. **/
    unit: string | undefined;
    /** numberScale code: millions, thousands, hundreds, single etc.  **/
    numberScale: string | undefined;
    /** @deprecated and no longer used **/
    valueChainPercentage?: number;
    // store the source of the connection data on population
    source?: ValueDataSource | ValueDataSource[];
  };
  // always an array, except old data generated from AI survey auto-answer - only used for AI survey auto-answer
  source?: ValueDataSource | ValueDataSource[];
}

export interface ValueDataInput<T = any> extends ValueData<T> {
  input: {
    table?: InputTableData;
    data: T;
    value?: number;
    unit: string | undefined;
    numberScale: string | undefined;
    valueChainPercentage?: number;
  };
}

export interface UniversalTrackerValuePublic<T = ObjectId, U = UniversalTrackerPublic, H = UtrvValueHistory> {
  _id: T;
  universalTrackerId: T;
  universalTracker?: U;
  initiativeId: T;
  status: string;
  sourceType: string;
  sourceCode?: string;
  type: string;
  valueType?: UtrValueType;

  value?: number;
  valueData?: ValueData;
  /** a copy of UTR unit property on the latest submission (represent unit tied to value) **/
  unit?: string;
  numberScale?: string;
  history: H[];

  lastUpdated: Date;
  effectiveDate: Date;

  stakeholders?: StakeholderGroup<T>;

  evidenceRequired: boolean;
  noteRequired?: boolean;
  verificationRequired: boolean;
  isPrivate?: boolean;

  note?: string;
  // Save latest notes to avoid getting all utrv histories.
  notes?: LatestUtrvNotes;
  evidence?: T[];
  evidenceData?: EvidenceData<T>[];

  period?: DataPeriods;

  assuranceStatus?: UtrvAssuranceStatus;
}

export enum SourceTypes {
  /** Seems to be not used? maybe related to legacy survey-source collection **/
  Survey = 'survey',

  /** Default type, re-occurring schedule used this to create ad-hoc universalTrackerValues **/
  Manual = 'manual',

  /** These questions cannot be answered directly and will be calculated using fragments */
  Composite = 'composite',

  /** Questions at leaf level of the tree, visible utrvs that users can answer on survey */
  Fragment = 'fragment',

  /** @deprecated was used by value chain as sub-fragment of visible questions  **/
  SubFragment = 'sub_fragment',

  /** Aggregated as part of the aggregation flow */
  Aggregated = 'aggregated',
}
