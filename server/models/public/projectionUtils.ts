/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import {
  UniversalTrackerPublic,
  ValueValidation
} from "./universalTrackerType";
import { UniversalTrackerValuePublic } from "./universalTrackerValueType";

type DefaultOptions = 0 | 1 | object;
export type KeysEnum<T, U extends DefaultOptions = DefaultOptions> = { [P in keyof Required<T>]: U };
export type PartialKeysEnum<T> = { [P in keyof Required<T>]?: 0 | 1 | object };

const valueValidation: KeysEnum<ValueValidation> = {
  min: 1,
  max: 1,
  table: 1,
  decimal: 1,
  variations: 1,
  valueList: {
    type: 1,
    listId: 1,
    custom: 1,
    list: { $arrayElemAt: ['$list.options', 0] }
  }
};

export const universalTrackerPublicFields: KeysEnum<UniversalTrackerPublic> = {
  _id: 1,
  alternatives: 1,
  evidenceInstructions: 1,
  instructionsLink: 1,
  ownerId: 1,
  tags: 1,
  valueValidation,
  instructions: 1,
  instructionsEditorState: 1,
  code: 1,
  created: 1,
  description: 1,
  type: 1,
  typeCode: 1,
  typeTags: 1,
  targetDirection: 1,
  valueLabel: 1,
  valueType: 1,
  unit: 1,
  unitType: 1,
  name: 1,
  numberScale: 1,
};

export const universalTrackerValuePublicFields: KeysEnum<UniversalTrackerValuePublic> = {
  _id: 1,
  evidence: 1,
  evidenceData: 1,
  evidenceRequired: 1,
  note: 1,
  notes: 1,
  noteRequired: 1,
  stakeholders: 1,
  status: 1,
  verificationRequired: 1,
  universalTrackerId: 1,
  universalTracker: 1,
  initiativeId: 1,
  sourceType: 1,
  sourceCode: 1,
  type: 1,
  unit: 1,
  numberScale: 1,
  effectiveDate: 1,
  history: 1,
  isPrivate: 1,
  value: 1,
  valueData: 1,
  lastUpdated: 1,
  valueType: 1,
  period: 1,
  assuranceStatus: 1,
};

interface ProjectToMapParams<T extends Record<string, unknown>> {
  input: string;
  projection: KeysEnum<T>;
  override: KeysEnum<T>;
  as: string;
}

export function projectToMap<T extends Record<string, unknown>>(options: ProjectToMapParams<T>) {

  const { input, projection, override, as } = options;

  const keys = Object.keys(projection) as (keyof T)[];
  return {
    $map: {
      input,
      as,
      in: keys.reduce((acc, prop) => {
        return {
          ...acc,
          [prop]: override[prop] ?? `$$${as}.${String(prop)}`
        };
      }, {})
    }
  }
}
