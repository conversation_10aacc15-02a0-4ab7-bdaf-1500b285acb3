/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { Model, Schema, model } from "mongoose";
import Stripe from "stripe";
import { ObjectId } from "bson";

/**
 * @link https://stripe.com/docs/billing/subscriptions/overview#subscription-statuses
 **/
export type SubscriptionStatus =
  // Fine
  /** The subscription is in good standing and the most recent payment is successful. **/
  | 'active'

  /** The subscription is currently in a trial period and it’s safe to provision your product for your customer. **/
  | 'trialing'

  // Not recoverable
  /** This is a terminal state that can’t be updated. **/
  | 'canceled'

  /**
   * The initial payment on the subscription failed and no successful payment
   * was made within 23 hours of creating the subscription
   **/
  | 'incomplete_expired'

  // Need user interaction
  /**
   * aka Overdue
   * Payment on the latest finalised invoice either failed or was not attempted.
   * The subscription continues to create invoices.
   */
  | 'past_due'

  /**
   * The latest invoice has not been paid but the subscription remains in place.
   * The latest invoice remains open and invoices continue to be generated
   * but payments aren’t attempted. You should revoke access to your product
   * when the subscription is unpaid since payments were already attempted
   * and retried when it was past_due. To move the subscription to active,
   * pay the most recent invoice before its due date.
   */
  | 'unpaid'

  /** A successful payment needs to be made within 23 hours to activate the subscription. **/
  | 'incomplete'

  /** The subscription has ended its trial period without a default payment method and
   * the trial_settings.end_behavior.missing_payment_method is set to pause. Invoices
   * will no longer be created for the subscription. After a default payment method
   * has been attached to the customer, you can resume the subscription. */
  | 'paused'


// Can no longer recover from these and require new subscription
export const requireNewSubStatus: SubscriptionStatus[] = [
  'canceled',
  'incomplete_expired',
]

export const requireUserInteraction: SubscriptionStatus[] = [
  'past_due',
  'unpaid',
  'incomplete',
]

/** Statuses that allow access to the product **/
export const allowAccessStatuses: SubscriptionStatus[] = [
  'active',
  'trialing',
]

export const cancellableStatuses = [...allowAccessStatuses, ...requireUserInteraction];

export const isRecoverableStatus = (status: SubscriptionStatus): boolean => {
  return !requireNewSubStatus.includes(status);
}

export const materialityTrackerProductCodes = {
  MaterialityTracker: 'materiality_assessment',
  MaterialityTrackerSolopreneur: 'materiality_assessment_solopreneur',
  MaterialityTrackerStartup: 'materiality_assessment_startup',
  MaterialityTrackerMicro: 'materiality_assessment_micro',
  MaterialityTrackerSME: 'materiality_assessment_sme',
  MaterialityTrackerMidCap: 'materiality_assessment_midcap',
  MaterialityTrackerMNC: 'materiality_assessment_mnc',
  MaterialityTrackerLarge: 'materiality_assessment_large',
} as const;

export const ProductCodes = {
  SGXESGenome: 'company_tracker_light',
  CompanyTrackerStarter: 'company_tracker_starter',
  CompanyTracker: 'company_tracker',
  CompanyTrackerPro: 'company_tracker_pro',
  CompanyTrackerEnterprise: 'company_tracker_enterprise',
  PortfolioTracker: 'portfolio_tracker',
  ...materialityTrackerProductCodes,
} as const;

export type ProductCodes = typeof ProductCodes[keyof typeof ProductCodes];

interface ItemPrice {
  id: string;
  type: Stripe.Price.Type;
  recurring?: Stripe.Price.Recurring
}

export interface SubscriptionItem {
  id: string;
  priceId: string;
  productId: string;
  productCode?: string;
  /**
   * Quantity of items, optional for now but assume 1 if missing
   */
  quantity: number | undefined;

  // One additional price information model
  price?: ItemPrice

  created: number;
}

const PriceRecurringSchema = new Schema<ItemPrice['recurring']>({
  aggregate_usage: { type: Schema.Types.String, trim: true },
  interval: { type: Schema.Types.String, required: true },
  interval_count: { type: Schema.Types.Number, required: true },
  trial_period_days: { type: Schema.Types.Number, required: false },
  usage_type: { type: Schema.Types.String, required: true },
}, { _id: false })

const ItemPriceSchema = new Schema<ItemPrice>({
  id: { type: Schema.Types.String, required: true, trim: true },
  type: { type: Schema.Types.String, required: true, trim: true },
  recurring: {
    type: PriceRecurringSchema,
    required: false,
    default: undefined
  },
}, { _id: false });

const SubscriptionItemSchema = new Schema<SubscriptionItem>({
  id: { type: Schema.Types.String, required: true, trim: true },
  priceId: { type: Schema.Types.String, required: true, trim: true },
  productId: { type: Schema.Types.String, required: true, trim: true },
  productCode: { type: Schema.Types.String, required: false, trim: true },
  quantity: {
    type: Schema.Types.Number,
    required: true,
    // Set 1 as default in-case we are dealing with old subs that did not save it
    default: 1
  },
  price: ItemPriceSchema,
  created: { type: Schema.Types.Number, required: true },
}, { _id: false })

export interface Coupon {
  id: string;
  currency: string | null;
  name?: string | null;
  percent_off: number | null
}

export interface Discount {
  id: string;
  coupon?: Coupon;
  start: number;
  end: number | null;
}

const CouponSchema = new Schema<Coupon>({
  id: { type: Schema.Types.String, required: true, trim: true },
  currency: { type: Schema.Types.String, required: false, trim: true },
  name: { type: Schema.Types.String, required: false, trim: true }
}, { _id: false })

const DiscountSchema = new Schema<Discount>({
  id: { type: Schema.Types.String, required: true, trim: true },
  coupon: { type: CouponSchema, required: false },
  start: { type: Schema.Types.Number, required: true, trim: true },
  end: { type: Schema.Types.Number, required: false, trim: true },
}, { _id: false })

// @link CustomerManager.shouldUpdate needs to be updated if any extra arrays are added
export interface Subscription {
  id: string;
  status: SubscriptionStatus;
  // List of subscription items, each with an attached price.
  items: SubscriptionItem[];
  startDate: number;
  /**
   * If the subscription has ended, the date the subscription ended.
   */
  endDate?: number;
  cancelDate?: number;
  periodStart: number;
  periodEnd: number;
  trialStart?: number;
  trialEnd?: number;
  paymentProvider?: 'stripe' | 'wwg';
  discount?: Discount;
  discounts?: Discount[];
}

const subscriptionSchema = new Schema<Subscription>({
  id: { type: Schema.Types.String, required: true, trim: true },
  items: [SubscriptionItemSchema],
  status: { type: Schema.Types.String, required: true, trim: true },
  startDate: { type: Schema.Types.Number, required: true, default: 0 },
  endDate: { type: Schema.Types.Number, required: false },
  cancelDate: { type: Schema.Types.Number, required: false },
  periodStart: { type: Schema.Types.Number, required: true },
  periodEnd: { type: Schema.Types.Number, required: true },
  trialStart: { type: Schema.Types.Number, required: false },
  trialEnd: { type: Schema.Types.Number, required: false },
  paymentProvider: {
    type: Schema.Types.String,
    required: true,
    enum: ['stripe', 'wwg'],
    default: 'stripe'
  },
  discount: { type: DiscountSchema, required: false },
  discounts: { type: [DiscountSchema], required: false },
}, { _id: false });

export interface SubscriptionCustomerPlain {
  _id: ObjectId;
  created?: Date;
  initiativeId: ObjectId;
  customerId: string;
  currency?: string;
  defaultPaymentMethod?: string;
  subscriptions: Subscription[];
}

// originally Initiative had a customer property.
// With subscriptions this can get big, so is now moved to separate collection
export const CustomerSchema = new Schema<SubscriptionCustomerPlain>({
  created: {  type: Schema.Types.Date, default: Date.now },
  initiativeId: { type: Schema.Types.ObjectId, unique: true },
  customerId: { type: Schema.Types.String, required: true, trim: true },
  defaultPaymentMethod: { type: Schema.Types.String },
  currency: { type: Schema.Types.String },
  subscriptions: { type: [subscriptionSchema], required: true, default: [] },
}, { collection: 'subscription-customers' });

export const SubscriptionCustomer: Model<SubscriptionCustomerPlain> = model('SubscriptionCustomerPlain', CustomerSchema);
