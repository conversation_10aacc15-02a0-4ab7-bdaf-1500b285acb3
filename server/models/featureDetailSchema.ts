/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { Schema } from "mongoose";
import { FeatureCode, FeatureDetails, ScopePackDataScope } from "@g17eco/core";


const ScopeSchema = new Schema({
  standards: [Schema.Types.String],
  framework: [Schema.Types.String],
})

const scopePacksSchema = new Schema({
  access: {
    type: Schema.Types.String,
    enum: ['basic', 'custom'],
    default: undefined,
  },
  scope: {
    type: ScopeSchema,
    required: function (this: ScopePackDataScope) {
      return this.access === 'custom';
    }
  }
});

export const FeatureDetailSchema = new Schema<FeatureDetails>(
  {
    name: { type: Schema.Types.String, required: true, trim: true },
    code: {
      type: Schema.Types.String,
      required: true,
      enum: Object.values(FeatureCode),
      trim: true,
    },
    // If true, the feature is currently enabled/active.
    // This takes the priority over the tags in initiative
    active: { type: Schema.Types.Boolean, required: false },
    // If true, prevent any changes to this feature
    disabled: { type: Schema.Types.Boolean, required: false },
    // Explanation for why the feature is disabled
    disabledReason: { type: Schema.Types.String, required: false },
    config: {
      limit: {
        type: Schema.Types.Number,
        max: 10000,
        required: false,
      },
      scopePacks: {
        type: scopePacksSchema,
        required: false,
      },
    },
  },
  { _id: false }
);
