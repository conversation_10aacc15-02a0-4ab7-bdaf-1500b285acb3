/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { HydratedDocument, InferSchemaType, model, Schema, Types } from "mongoose";


type BaseData = unknown;

export type IntegrationStatus =
  // Setup is not completed
  | 'setup_required'
  // Setup is pending (3rd party is processing)
  | 'pending'
  // Setup is completed and ready to use
  | 'active'
  // Error occurred during setup
  | 'error';


export interface IntegrationConnectionCreate<D extends BaseData, T = Types.ObjectId> {
  name?: string;
  initiativeId: T;
  integrationCode: string;
  createdBy: T;
  lastUpdated?: Date;
  status: IntegrationStatus;
  data: D,
}

export interface IntegrationConnectionPlain<D extends BaseData = BaseData, T = Types.ObjectId> extends IntegrationConnectionCreate<D, T> {
  _id: T;
  created?: Date;
}

const IntegrationConnectionSchema = new Schema<IntegrationConnectionPlain>({
  name: Schema.Types.String,
  integrationCode: {
    type: Schema.Types.String,
    trim: true,
    lowercase: true,
  },
  initiativeId: { type: Schema.Types.ObjectId, required: true },
  createdBy: { type: Schema.Types.ObjectId, required: true },
  data: Schema.Types.Mixed,
  status: {
    type: Schema.Types.String,
    enum: ['setup_required', 'pending', 'active', 'error'],
    required: true,
    default: 'setup_required',
  },
  created: { type: Date, default: Date.now },
  lastUpdated: { type: Date, default: Date.now },
}, { collection: 'integration-connections' });


IntegrationConnectionSchema.index({ integrationCode: 1 });
IntegrationConnectionSchema.index({ initiativeId: 1, integrationCode: 1 }, { unique: true });

export type IntegrationConnectionModel = HydratedDocument<InferSchemaType<typeof IntegrationConnectionSchema>>;

const IntegrationConnection = model('IntegrationConnection', IntegrationConnectionSchema);

export default IntegrationConnection;
