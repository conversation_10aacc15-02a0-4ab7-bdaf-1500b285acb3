/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

export enum Materiality {
  Low = 'low',
  Medium = 'medium',
  High = 'high',
  None = 'none',
}

type OrderedTopic = {
  code: string;
  disabled?: boolean;
};

export interface MaterialityAssessmentConfig {
  orderedTopics: OrderedTopic[];
  explanation: string;
}

export enum UpdateImpactScope {
  Reports = 'reports',
  ModuleMetrics = 'module-metrics',
}