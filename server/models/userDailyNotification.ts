/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { HydratedDocument, InferSchemaType, model, Schema } from 'mongoose';
import { ScheduledType } from './scheduledNotification';

export const TemplateDateSchema = new Schema(
  {
    surveyIds: {
      type: [Schema.Types.ObjectId],
      required: false,
    },
    total: {
      type: Schema.Types.Number,
      require: true,
      default: 0
    },
    stakeholderCount: Schema.Types.Number,
    verifierCount: Schema.Types.Number,
  },
  { _id: false }
);

export const DailyNotificationDataSchema = new Schema(
  {
    scheduledNotificationIds: [Schema.Types.ObjectId],
    templateData: {
      type: TemplateDateSchema,
      required: true,
    },
  },
  { _id: false }
);

const UserDailyNotificationSchema = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, required: true },
    scheduledDate: {
      type: Schema.Types.Date,
      required: true,
    },
    type: { type: Schema.Types.String, enum: Object.values(ScheduledType), required: true },
    data: {
      type: DailyNotificationDataSchema,
      required: true,
    },
    erroredDate: Schema.Types.Date,
    completedDate: Schema.Types.Date,
    created: {
      type: Schema.Types.Date,
      default: Date.now,
    },
  },
  { collection: 'user-daily-notifications' }
);

UserDailyNotificationSchema.index({ userId: 1, scheduledDate: 1, type: 1 });
UserDailyNotificationSchema.index({ scheduledDate: 1, completedDate: 1, erroredDate: 1 });

export type UserDailyNotificationModel = HydratedDocument<InferSchemaType<typeof UserDailyNotificationSchema>>;

const UserDailyNotification = model<UserDailyNotificationModel>(
  'UserDailyNotificationModel',
  UserDailyNotificationSchema
);

export default UserDailyNotification;
