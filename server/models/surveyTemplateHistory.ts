import { ObjectId } from 'bson';
import { Document, Model, model, Schema } from 'mongoose';
import { DataPeriods } from '../service/utr/constants';
import { InitiativePlain } from './initiative';
import { BulkAggregatedSurveysData, BulkSurveysData } from './survey';
import { deadlineFields, SurveyTemplateFields } from './surveyTemplate';
import { UserModel } from './user';

export enum JobStatus {
  Error = 'error',
  Completed = 'completed'
}

interface ResultDetail {
  message: string;
  created: Date;
}

export interface BulkSurveyResult {
  surveyId?: ObjectId;
  initiativeId: ObjectId;
  status: JobStatus;
  details?: ResultDetail;
}

export interface BulkSurveysDeleteData {
  history: SurveyTemplateHistoryModel;
  user: UserModel;
}

export const DetailsSchema = new Schema<ResultDetail>(
  {
    message: { type: Schema.Types.String, required: true },
    created: { type: Schema.Types.Date, default: Date.now },
  },
  { _id: false }
);

export const SnapshotSchema = new Schema<BulkSurveysData>(
  {
    ...SurveyTemplateFields,
    ...deadlineFields,
    name: { type: Schema.Types.String, required: false },
    period: {
      type: Schema.Types.String,
      enum: DataPeriods,
      default: DataPeriods.Yearly,
    },
    effectiveDate: Schema.Types.Date,
    reportingLevels: [Schema.Types.ObjectId],
  },
  { _id: false }
);

export const BulkSurveyResultSchema = new Schema<BulkSurveyResult>(
  {
    surveyId: { type: Schema.Types.ObjectId },
    initiativeId: { type: Schema.Types.ObjectId, required: true },
    status: { type: Schema.Types.String, enum: [...Object.values(JobStatus)], required: true },
    details: DetailsSchema
  },
  { _id: false }
);

export interface SurveyTemplateHistoryMinData {
  templateId: ObjectId;
  initiativeId: ObjectId;
  snapshot: BulkSurveysData | BulkAggregatedSurveysData;
  results: BulkSurveyResult[];
  userId: ObjectId;
  completedDate?: Date;
  lastUpdated?: Date;
  deletedDate?: Date;
}

export interface SurveyTemplateHistoryModelPlain<T = ObjectId> extends SurveyTemplateHistoryMinData {
  _id: T;
  created: Date;
}

interface ExtendedBulkSurveyResult extends BulkSurveyResult {
  initiative: Pick<InitiativePlain, 'name' | '_id'>;
}

export interface ExtendedTemplateHistory extends SurveyTemplateHistoryModelPlain {
  results: ExtendedBulkSurveyResult[];
}

export interface SurveyTemplateHistoryModel<T = ObjectId>
  extends SurveyTemplateHistoryModelPlain<T>,
    Document<T, any, SurveyTemplateHistoryModelPlain> {
  _id: T;
}

const SurveyTemplateHistorySchema = new Schema<SurveyTemplateHistoryModel>(
  {
    templateId: { type: Schema.Types.ObjectId, required: true },
    initiativeId: { type: Schema.Types.ObjectId, required: true },
    snapshot: SnapshotSchema,
    results: { type: [BulkSurveyResultSchema], default: [], required: true },
    completedDate: { type: Schema.Types.Date },
    created: { type: Schema.Types.Date, default: Date.now },
    userId: { type: Schema.Types.ObjectId, required: true },
    lastUpdated: { type: Schema.Types.Date },
    deletedDate: { type: Schema.Types.Date }
  },
  { collection: 'survey-template-history', toJSON: { virtuals: true } }
);

SurveyTemplateHistorySchema.index({ initiativeId: 1 });
SurveyTemplateHistorySchema.index({ templateId: 1 });

SurveyTemplateHistorySchema.virtual('results.initiative', {
  ref: 'Initiative',
  localField: 'results.initiativeId',
  foreignField: '_id',
  justOne: true,
});

export const SurveyTemplateHistory: Model<SurveyTemplateHistoryModel> = model(
  'SurveyTemplateHistory',
  SurveyTemplateHistorySchema
);
