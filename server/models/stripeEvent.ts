/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import { model, Schema } from "mongoose";
import <PERSON><PERSON> from "stripe";
import { ObjectId } from "bson";

const RequestSchema = new Schema ({

  /**
   * ID of the API request that caused the event. If null, the event was automatic (e.g., <PERSON>e's automatic subscription handling). Request logs are available in the [dashboard](https://dashboard.stripe.com/logs), but currently not in the API.
   */
  id: Schema.Types.String,

  /**
   * The idempotency key transmitted during the request, if any. *Note: This property is populated only for events on or after May 23, 2017*.
   */
  idempotency_key:  Schema.Types.String,
}, {_id: false });

const EventSchema = new Schema<Stripe.Event>({

  id: {
    type: Schema.Types.String,
    required: true,
    unique: true,
  },

  /**
   * String representing the object's type. Objects of the same type share the same value.
   */
  object: { type: Schema.Types.String, required: true },

  /**
   * The connected account that originated the event.
   */
  account: Schema.Types.String,

  /**
   * The Stripe API version used to render `data`. *Note: This property is populated only for events on or after October 31, 2014*.
   */
  api_version: Schema.Types.String,

  /**
   * Time at which the object was created. Measured in seconds since the Unix epoch.
   */
  created: Schema.Types.Number,

  data: {
    object: Schema.Types.Mixed,
    /**
     * Object containing the names of the attributes that have changed, and their previous values (sent along only with *.updated events).
     */
    previous_attributes: Schema.Types.Mixed,
  },

  /**
   * Has the value `true` if the object exists in live mode or the value `false` if the object exists in test mode.
   */
  livemode: Schema.Types.Boolean,

  /**
   * Number of webhooks that have yet to be successfully delivered (i.e., to return a 20x response) to the URLs you've specified.
   */
  pending_webhooks: Schema.Types.Number,

  /**
   * Information on the API request that instigated the event.
   */
  request: {
    type: RequestSchema,
    required: false,
  },

  /**
   * Description of the event (e.g., `invoice.created` or `charge.refunded`).
   */
  type:  {
    type: Schema.Types.String,
    required: true,
  }
}, { _id: false });


const StripeEventSchema = new Schema({
  event: EventSchema,
  created: { type: Date, default: Date.now },
  processedDate: { type: Date },
}, { collection: 'stripe-events' })

export interface StripeEventModel<T = ObjectId> {
  _id: T;
  event: Stripe.Event,
  processedDate?: Date,
  created: string;
}

StripeEventSchema.index({ 'event.request.id': 1 });

const StripeEvent = model<StripeEventModel>('StripeEvent', StripeEventSchema)
export default StripeEvent;
