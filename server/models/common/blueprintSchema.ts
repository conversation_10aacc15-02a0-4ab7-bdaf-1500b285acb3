/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { Schema } from "mongoose";
import { UnitConfigSchema } from '../../service/units/unitTypes';
import { GroupType } from '../../survey/utrGroupConfigs';

const SurveyUtrGroupConfigSchema = new Schema({
  groupName: { type: Schema.Types.String, required: true },
  utrCodes: { type: [Schema.Types.String], required: true, default: [] },
  fragmentUtrConfiguration: {
    type: Schema.Types.Mixed, // Variable keys, needs to mixed
    required: false
  },
  type: {
    type: Schema.Types.String,
    enum: Object.values(GroupType),
    required: false,
    default: 'static',
  },
  groupDate: Schema.Types.Date,
  groupId: {
    type: Schema.Types.ObjectId,
    required: function (this: { type: GroupType }) {
      return [GroupType.Custom, GroupType.Group].includes(this.type);
    },
  },
  groupData: {
    type: {
      colour: Schema.Types.String,
      link: Schema.Types.String,
      icon: Schema.Types.String,
    },
    required: false
  },
}, { _id: false });

const SurveyFormsSchema = new Schema({
  utrGroupConfig: {
    type: SurveyUtrGroupConfigSchema,
    required: false
  },
  compositeConfig: {
    type: Schema.Types.String,
    required: false
  },
}, { _id: false });

export const BlueprintSchema = new Schema({
  name: { type: Schema.Types.String, required: true },
  code: { type: Schema.Types.String, required: true },
  date: { type: Schema.Types.Date, required: false },
  references: [Schema.Types.ObjectId],
  unitConfig: UnitConfigSchema,
  forms: [SurveyFormsSchema],
  customGroups: [SurveyUtrGroupConfigSchema],
  additionalConfigs: [SurveyFormsSchema],
}, { _id: false });
