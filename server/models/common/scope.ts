import { Schema } from 'mongoose';
import { ObjectId } from 'bson';

export interface Scope<T = ObjectId> {
  sdg: string[],
  materiality: string[],
  standards: string[],
  frameworks: string[],
  custom: T[];
}

export const ScopeSchema = new Schema<Scope>({
  sdg: [Schema.Types.String],
  materiality: [Schema.Types.String],
  standards: [Schema.Types.String],
  frameworks: [Schema.Types.String],
  custom: [Schema.Types.ObjectId],
}, { _id: false });