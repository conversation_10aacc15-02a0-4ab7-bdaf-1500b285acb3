/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { Schema, SchemaDefinition } from "mongoose";
import { frameworks, standards } from '@g17eco/core';
import { StakeholderGroupSchema } from '../stakeholderGroup';
import { Materiality } from '../materiality';


const children = StakeholderGroupSchema.clone();
children.add({
  code: { type: Schema.Types.String, required: true },
  inScope: Schema.Types.Boolean,
  isPartial: Schema.Types.Boolean,
});

const childrenSchema = children.clone();
children.add({ children: [children.clone()] });

const userIdsSchema = StakeholderGroupSchema.clone();
userIdsSchema.add({
  inScope: Schema.Types.Boolean,
  isPartial: Schema.Types.Boolean,
  children: [childrenSchema],
});

const standardsSchema: SchemaDefinition = {};
Object.keys(standards).forEach(standardCode => {
  standardsSchema[standardCode] = userIdsSchema;
});

const frameworkSchema: SchemaDefinition = {};
Object.keys(frameworks).forEach(frameworkCode => {
  frameworkSchema[frameworkCode] = userIdsSchema;
});

const materialitySchema = new Schema({
  [Materiality.Low]: userIdsSchema,
  [Materiality.Medium]: userIdsSchema,
  [Materiality.High]: userIdsSchema,
  [Materiality.None]: userIdsSchema,

}, { _id: false });

const sdgSchema: SchemaDefinition = {};
Array.from({ length: 17 }, (_, k) => String(k + 1))
  .forEach(goal => {
    sdgSchema[goal] = userIdsSchema;
  });

export const DelegationScopeSchema = new Schema({
  sdg: sdgSchema,
  materiality: materialitySchema,
  standards: standardsSchema,
  frameworks: frameworkSchema,
  custom: { type: Schema.Types.Mixed, default: {} },
}, { _id: false });
