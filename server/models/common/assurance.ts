/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { Schema } from "mongoose";
import type { PartialAssuranceField } from "../../types/assurance";

const PartialFieldSchema = new Schema<PartialAssuranceField>({
  code: { type: Schema.Types.String, required: true },
  rowIndex: { type: Schema.Types.Number },
  date: { type: Date, default: Date.now }
}, { _id: false });

export const assuranceFieldsSchema = {
  type: [PartialFieldSchema],
  default: undefined,
  required: false
}
