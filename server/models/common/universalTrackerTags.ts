/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { Schema } from "mongoose";
import { frameworks } from '@g17eco/core';
import { Tags } from '../../types/universalTrackerValue';

const picEnum = [
  'poverty',
  'inequality',
  'climate',
];
const eseEnum = [
  'economic',
  'social',
  'environmental',
  'sustainability',
  'socio-environmental',
  'socio-economic',
  'eco-economic',
];


const getTagsSchema = (tagsEnum: string[]) => ({
  type: [Schema.Types.String],
  required: false,
  enum: tagsEnum,
  default: undefined,
});

const schema = new Schema({
  pic: getTagsSchema(picEnum),
  ese: getTagsSchema(eseEnum),
  sdg: { type: [Schema.Types.String], required: false, default: undefined },
}, { _id: false } );

// Add dynamic framework schema
Object.entries(frameworks).forEach(([key, group]) => {
  if (!group.subgroups) {
    return []
  }
  const enumValues = group.subgroups.map(f => f.code);
  schema.add({ [key]: getTagsSchema(enumValues) })
});

export const frameworkTags = Object.keys(schema.paths);
export const createEmptyTags = (): Tags => frameworkTags.reduce((tags, code) => {
  tags[code] = [];
  return tags;
}, <Tags>{ pic: [], ese: [], sdg: [] });

export const TagSchema = schema;
