import { Schema } from 'mongoose';
import { ActionList } from '../../service/utr/constants';
import { EditorState } from '../../types/editorState';
import { ObjectId } from 'bson';
import type { EvidenceData } from '../../types/universalTrackerValue';

export interface UtrvNote {
  historyId?: ObjectId;
  note: string; // Plain text.
  editorState?: EditorState; // Rich text.
  action: ActionList;
  date: Date;
  evidence?: ObjectId[];
  evidenceData?: EvidenceData[];
}

export interface LatestUtrvNotes {
  stakeholder?: UtrvNote;
  verifier?: UtrvNote;
}

export const evidenceDataSchema = new Schema<EvidenceData>({
  documentId: { type: Schema.Types.ObjectId, required: true },
  description: Schema.Types.String,
}, { _id: false });

export const UtrvNoteSchema = new Schema<UtrvNote>(
  {
    historyId: {
      type: Schema.Types.ObjectId,
    },
    note: {
      type: Schema.Types.String,
      trim: true,
      required: function (this: UtrvNote) {
        return typeof this.note !== 'string'; // allow empty string
      },
      default: '',
    },
    editorState: {
      type: Schema.Types.Mixed,
    },
    action: {
      type: Schema.Types.String,
      required: true,
      enum: Object.values(ActionList),
    },
    date: { type: Schema.Types.Date, default: Date.now },
    evidence: {
      type: [Schema.Types.ObjectId],
      default: undefined,
    },
    evidenceData: { type: [evidenceDataSchema], default: undefined },
  },
  { _id: false }
);

export const LatestUtrvNotesSchema = new Schema<LatestUtrvNotes>(
  {
    stakeholder: UtrvNoteSchema,
    verifier: UtrvNoteSchema,
  },
  { _id: false }
);
