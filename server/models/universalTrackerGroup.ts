/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { Schema, Model, model } from 'mongoose';
import { ObjectId } from 'bson';

const UniversalTrackerGroupSchema = new Schema<UniversalTrackerGroupModel>({
  initiativeId: { type: Schema.Types.ObjectId, required: true },
  code: {
      type: String,
      trim: true,
      lowercase: true,
      required: true,
      validate: [/^[a-z0-9\/\-.]+$/, 'Code can only contain alphanumeric, dash(-), dot(.) and forwardslash(/) ']
  },
  name: String,
  universalTrackers: [Schema.Types.ObjectId],
  created: { type: Date, default: Date.now },
}, { collection: 'universal-tracker-groups' });

export interface UniversalTrackerGroupModel {
  _id?: ObjectId;
  initiativeId: ObjectId;
  code: string;
  name: string;
  universalTrackers: ObjectId[];
  created?: Date;
}

const UniversalTrackerGroup: Model<UniversalTrackerGroupModel> = model('UniversalTrackerGroup', UniversalTrackerGroupSchema);
export default UniversalTrackerGroup;
