/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { HydratedDocument, model, Schema, Types } from "mongoose";

export enum IntegrationTaskType {
  SubConnection = 'sub-connection',
}

type DefaultData = {
  [key: string]: unknown;
}

export enum IntegrationTaskStatus {
  SetupRequired = 'setup_required',
  Pending = 'pending',
  Active = 'active',
  Error = 'error'
}

export interface IntegrationTaskCreate<D extends DefaultData = DefaultData, T = Types.ObjectId> {
  name: string;
  type: IntegrationTaskType;
  subType?: string;
  connectionId: T;
  initiativeId: T;
  status: IntegrationTaskStatus;
  externalId?: string;
  data: D,

  activeDate?: string | Date;
  errorDate?: string | Date;
  errorMessage?: string;
}

export interface IntegrationTaskPlain<D extends DefaultData = DefaultData, T = Types.ObjectId> extends IntegrationTaskCreate<D, T> {
  _id: T;
  created: string | Date;
}

const IntegrationTaskSchema = new Schema<IntegrationTaskPlain>({
  name: Schema.Types.String,
  type: {
    type: Schema.Types.String,
    required: true,
    trim: true,
    lowercase: true,
    enum: Object.values(IntegrationTaskType)
  },
  subType: { type: Schema.Types.String, trim: true, lowercase: true },
  connectionId: { type: Schema.Types.ObjectId, required: true },
  initiativeId: { type: Schema.Types.ObjectId, required: true },
  // External ID of the initiative on the integration platform
  externalId: { type: Schema.Types.String, trim: true, default: undefined },
  status: {
    type: Schema.Types.String,
    trim: true,
    lowercase: true,
    enum: Object.values(IntegrationTaskStatus),
    default: IntegrationTaskStatus.Pending
  },
  data: { type: Schema.Types.Mixed, default: {} },
  activeDate: { type: Schema.Types.Date, default: undefined },
  errorDate: { type: Schema.Types.Date, default: undefined },
  errorMessage: { type: Schema.Types.String, default: undefined },
}, {
  collection: 'integration-tasks',
  timestamps: { createdAt: 'created', updatedAt: 'lastUpdated' }
});

export type IntegrationTaskModel = HydratedDocument<IntegrationTaskPlain<DefaultData>>;

IntegrationTaskSchema.index({ initiativeId: 1, connectionId: 1, type: 1, status: 1 });

const IntegrationTask = model('IntegrationTask', IntegrationTaskSchema);


export default IntegrationTask;
