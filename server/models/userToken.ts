/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { Model, model, Schema } from 'mongoose';
import { ObjectId } from 'bson';
import { generateRandomToken } from '../service/crypto/token';

export enum UserTokenTypes {
  UserActivation = 'user_activation',
}

const UserTokenSchema = new Schema<UserTokenModel>(
  {
    userId: Schema.Types.ObjectId,
    created: { type: Date, default: Date.now },
    token: String,
    expiryDate: { type: Date, default: Date.now },
    type: {
      type: String,
      enum: Object.values(UserTokenTypes)
    },
  },
  { collection: 'users-token' }
);

export interface UserTokenPlain<T = ObjectId> {
  _id: T;
  token: string,
  type: UserTokenTypes;
  userId: T,
  expiryDate: Date;
  created: Date;
}

export interface UserTokenModel<T = ObjectId> extends UserTokenPlain<T> {
  _id: T;
}

const UserToken: Model<UserTokenModel> = model('UserToken', UserTokenSchema);

export const createActivationToken = (userId: ObjectId) => {
  return new UserToken({
    userId,
    token: generateRandomToken(20),
    type: UserTokenTypes.UserActivation,
  });
};

export default UserToken;
