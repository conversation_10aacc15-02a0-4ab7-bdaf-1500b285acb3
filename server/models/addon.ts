/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { HydratedDocument, InferSchemaType, model, Schema } from 'mongoose';


export const AddonSchema = new Schema({
  code: {
    type: Schema.Types.String,
    trim: true,
    lowercase: true,
    required: true,
    unique: true,
    validate: [/^[a-z0-9\/\-.]+$/, 'Code can only contain alphanumeric, dash(-), dot(.) and forward slash(/) ']
  },
  name: { type: Schema.Types.String, required: true, trim: true },
  description: { type: Schema.Types.String },
  logo: { type: Schema.Types.String },
}, { collection: 'addons' });


export type AddonModel = HydratedDocument<InferSchemaType<typeof AddonSchema>>;

export default model('Addon', AddonSchema);
