/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { Model, model, Schema, Types, HydratedDocument } from 'mongoose';
import {
  NotificationPreferencesPlain,
} from '../service/notification/NotificationModels';
import { TimePeriod } from '../util/date';
import { ObjectId } from 'bson';
import { InitiativePlain } from './initiative';

export interface NotificationSchedule {
  isSummary: boolean;
  period: TimePeriod;
}

export interface NotificationSchedulePlain<T = Types.ObjectId> {
  userId: T;
  initiativeId: T;
  notificationIds: T[];
  completedDate?: Date;
}

export interface NotificationScheduleExtended extends NotificationSchedulePlain {
  _id: ObjectId;
  notificationPreferences?: NotificationPreferencesPlain;
  initiative: InitiativePlain;
}

export type NotificationScheduleModel = HydratedDocument<NotificationSchedulePlain>;

const NotificationScheduleSchema = new Schema<NotificationScheduleModel>({
  // Ownership
  userId: { type: Schema.Types.ObjectId, required: true },
  // Group by initiative
  initiativeId: { type: Schema.Types.ObjectId, required: true },
  // Accumulate notifications
  notificationIds: { type: [Schema.Types.ObjectId], default: [] },
  completedDate: Schema.Types.Date,
}, { collection: 'notification-schedules' });

NotificationScheduleSchema.index({ userId: 1, initiativeId: 1, completedDate: 1 });

NotificationScheduleSchema.virtual('user', {
  ref: 'User', // The model to use
  localField: 'userId', // Find people where `localField`
  foreignField: '_id', // is equal to `foreignField`
  justOne: true,
});

NotificationScheduleSchema.virtual('initiative', {
  ref: 'Initiative',
  localField: 'initiativeId',
  foreignField: '_id',
  justOne: true,
});

const NotificationSchedule: Model<NotificationScheduleModel> = model<NotificationScheduleModel>('NotificationSchedule', NotificationScheduleSchema);

export default NotificationSchedule;
