/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { Schema, Model, model, Types } from 'mongoose';

const LedgerUniversalTrackerValueSchema = new Schema<LedgerUniversalTrackerValueModel>({
  utrvId: { type: Schema.Types.ObjectId, required: true, unique: true },
  lastUpdatedDate: { type: Schema.Types.Date, required: true },
  documentId: { type: Schema.Types.String, required: true },
  created: { type: Date, default: Date.now },
  historyIds: { type: [Schema.Types.ObjectId], required: true, default: [] },
}, { collection: 'ledger-universal-tracker-values' });

export interface LedgerUniversalTrackerValuePlain<T = Types.ObjectId> {
  created: Date;
  lastUpdatedDate: Date
  documentId: string;
  utrvId: T;
  historyIds: T[];
}

export interface LedgerUniversalTrackerValueModel {
  _id?: Types.ObjectId;
  created: Date;
  lastUpdatedDate: Date | string;
  documentId: string;
  utrvId: Types.ObjectId;
  historyIds: Types.ObjectId[];
}

LedgerUniversalTrackerValueSchema.index({ 'utrvId' : 1, 'documentId' : 1 });

const LedgerUniversalTrackerValue: Model<LedgerUniversalTrackerValueModel> = model('LedgerUniversalTrackerValue', LedgerUniversalTrackerValueSchema);
export default LedgerUniversalTrackerValue;
