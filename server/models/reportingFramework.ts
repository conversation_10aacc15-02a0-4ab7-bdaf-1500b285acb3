/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { HydratedDocument, Model, model, Schema, Types } from 'mongoose';

enum ReportingFrameworkTypes {
  Standard = 'standard',
  Framework = 'framework',
  Legislation = 'legislation'
}

const ItemSchema = new Schema({
  itemCode: { type: String, required: true },
  itemName: { type: String, required: true },
  sdgCodes: [String],
  displayCode: {
    type: Schema.Types.String,
    required: false,
    trim: true,
  },
}, { _id: false, strict: false });

const ReportingFrameworkSchema = new Schema<ReportingFrameworkModel>({
  code: {
    type: String,
    trim: true,
    lowercase: true,
    required: true,
    unique: true,
    validate: [/^[a-z0-9\-.]+$/, 'Code can only contain lowercase alphanumeric (a-z0-9), dash(-) and dot(.)']
  },
  name: {
    type: String,
    required: true
  },
  shortName: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  type: {
    type: String,
    enum: [ReportingFrameworkTypes.Standard, ReportingFrameworkTypes.Framework, ReportingFrameworkTypes.Legislation],
    default: ReportingFrameworkTypes.Standard,
    required: false
  },
  items: [ItemSchema],
  created: { type: Date, default: Date.now },
}, { collection: 'reporting-frameworks' });

export interface ItemModel {
  itemCode: string;
  itemName: string;
  sdgCodes: string[];
  displayCode?: string;
}

export interface CreateReportingFrameworkData {
  code: string;
  name: string;
  shortName: string;
  description: string;
  type: string;
  items: ItemModel[];
  created?: Date;
}

export interface ReportingFrameworkPlain<T = Types.ObjectId> extends CreateReportingFrameworkData {
  _id: T;
}

export type ReportingFrameworkModel<T = Types.ObjectId>  = HydratedDocument<ReportingFrameworkPlain<T>>;

const ReportingFramework: Model<ReportingFrameworkModel> = model('ReportingFramework', ReportingFrameworkSchema);

export default ReportingFramework;
