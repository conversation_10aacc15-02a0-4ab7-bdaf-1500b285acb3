import { ObjectId } from 'bson';
import { model, Schema } from 'mongoose';

export enum Permission {
  User = 'user',
}

interface User {
  _id: ObjectId;
  permissions: Permission[];
}

export type UserWithInfo = User & {
  name: string;
  email: string;
};

export interface WorkgroupPlain<U extends User = User> {
  _id: ObjectId;
  initiativeId: ObjectId;
  name: string;
  description?: string;
  icon: string;
  color: string;
  users: U[];
  creatorId: ObjectId;
  created: Date;
  updated: Date;
}

const UserSchema = new Schema<User>(
  {
    _id: { type: Schema.Types.ObjectId, required: true },
    permissions: [
      { type: Schema.Types.String, enum: Object.values(Permission), default: [Permission.User], required: true },
    ],
  },
  { _id: false }
);

const WorkgroupSchema = new Schema<WorkgroupPlain>(
  {
    initiativeId: { type: Schema.Types.ObjectId, required: true },
    name: { type: Schema.Types.String, required: true },
    description: { type: Schema.Types.String },
    icon: { type: Schema.Types.String, required: true },
    color: { type: Schema.Types.String, required: true },
    users: { type: [UserSchema], required: true },
    creatorId: { type: Schema.Types.ObjectId, required: true },
  },
  { collection: 'workgroups', timestamps: { createdAt: 'created', updatedAt: 'updated' } }
);

export const Workgroup = model('Workgroup', WorkgroupSchema);