import { HydratedDocument, InferSchemaType, model, Schema } from "mongoose";
import { ObjectId } from 'bson';

const SetupTaskActionSchema = new Schema({
  taskId: { type: Schema.Types.ObjectId, required: true },
  userId: { type: Schema.Types.ObjectId, required: true },
  initiativeId: Schema.Types.ObjectId,
  deletedDate: { type: Schema.Types.Date },
  completedDate: { type: Schema.Types.Date },
}, { collection: 'setup-task-actions' })

export type SetupTaskActionModel = HydratedDocument<InferSchemaType<typeof SetupTaskActionSchema>>;
export type SetupTaskActionPlain = InferSchemaType<typeof SetupTaskActionSchema> & { _id: ObjectId};

export default model('SetupTaskAction', SetupTaskActionSchema);
