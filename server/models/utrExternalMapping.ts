import { model, HydratedDocument, InferSchemaType, Schema } from 'mongoose';

export interface MappedUtr {
  utrCode: string;
  valueListCode?: string;
}

export interface UtrExternalMappingPlain<D = Date> {
  /* Unique, should be the combination of type and mappingCode */
  code: string;
  mappingCode: string;
  type: string;
  verifiedDate?: D;
  utrs: MappedUtr[];
}

const TagUtrDataSchema = new Schema<MappedUtr>(
  {
    utrCode: { type: Schema.Types.String, trim: true, required: true },
    valueListCode: { type: Schema.Types.String, trim: true, required: false },
  },
  { _id: false },
);

export const UtrExternalMappingSchema = new Schema<UtrExternalMappingPlain>(
  {
    code: {
      type: Schema.Types.String,
      trim: true,
      required: true,
      unique: true
    },
    mappingCode: {
      type: Schema.Types.String,
      trim: true,
      required: true,
    },
    type: { type: Schema.Types.String, trim: true, required: true },
    verifiedDate: { type: Date, required: false },
    utrs: { type: [TagUtrDataSchema], required: false },
  },
  { collection: 'utr-external-mappings' },
);

export type UtrExternalMappingModel = HydratedDocument<InferSchemaType<typeof UtrExternalMappingSchema>>;

const UtrExternalMapping = model('UtrExternalMapping', UtrExternalMappingSchema);

export default UtrExternalMapping;