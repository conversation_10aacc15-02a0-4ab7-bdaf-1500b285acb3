
export enum StaffRole {
  User = 'user',
  Sustainability = 'sustainability',
  Success = 'success',
  Admin = 'admin',
}

export enum StaffScope {
  UserRead = 'user.read',
  UserWrite = 'user.write',
  CompanyRead = 'company.read',
  CompanyWrite = 'company.write',
  ReferralCodeRead = 'referral_code.read',
  ReferralCodeWrite = 'referral_code.write',
  ReportingRead = 'reporting.read',
  ReportingWrite = 'reporting.write',
  UtrRead = 'utr.read',
  UtrWrite = 'utr.write',
  AdminRead = 'admin.read',
  AdminWrite = 'admin.write',
}

export interface StaffRoles {
  roles: StaffRole[];
  scopes: StaffScope[];
}

export const StaffRoleToScopesMap = {
  [StaffRole.User]: {
    name: 'User',
    scopes: [StaffScope.UserRead, StaffScope.CompanyRead, StaffScope.ReportingRead],
  },
  [StaffRole.Sustainability]: {
    name: 'Sustainability',
    scopes: [
      StaffScope.UserRead,
      StaffScope.CompanyRead,
      StaffScope.ReportingRead,
      StaffScope.UtrRead,
      StaffScope.UtrWrite,
    ],
  },
  [StaffRole.Success]: {
    name: 'Success',
    scopes: [
      StaffScope.UserRead,
      StaffScope.CompanyRead,
      StaffScope.ReferralCodeRead,
      StaffScope.ReportingRead,
      StaffScope.UserWrite,
      StaffScope.CompanyWrite,
      StaffScope.ReferralCodeWrite,
    ],
  },
  [StaffRole.Admin]: {
    name: 'Admin',
    scopes: [
      StaffScope.UserRead,
      StaffScope.CompanyRead,
      StaffScope.ReferralCodeRead,
      StaffScope.ReportingRead,
      StaffScope.UtrRead,
      StaffScope.AdminRead,
      StaffScope.UserWrite,
      StaffScope.CompanyWrite,
      StaffScope.ReferralCodeWrite,
      StaffScope.ReportingWrite,
      StaffScope.UtrWrite,
      StaffScope.AdminWrite,
    ],
  },
};
