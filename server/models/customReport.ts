/*
 * Copyright (c) 2022
. World Wide Generation Ltd
 */

import { HydratedDocument, model, Model, Schema, Types } from 'mongoose';
import { ActionList, DataPeriods } from '../service/utr/constants';
import { Scope, ScopeSchema, SurveyType } from './survey';
import { UtrvAssuranceStatus } from './universalTrackerValue';
import { VisibilityStatus } from '../service/survey/scope/visibilityStatus';
import { ObjectId } from 'bson';
import { OrderingDirection } from '../types/ordering';
import { UniversalTrackerPlain } from './universalTracker';

export enum MetricTypes {
  Single = 'single',
  Calculated = 'calculated',
  Text = 'text'
}

export enum CustomReportType {
  Metrics = 'metrics',
  Initiatives = 'initiatives',
  Survey = 'survey',
  /** @deprecated it was used only for filtering on FE that is no longer the case */
  SurveyAggregation = 'survey_aggregation',
  Template = 'template',
}

const CustomReportMetricSchema = new Schema({
  utrId: {
    type: Types.ObjectId,
    required: true
  },
  type: {
    type: String,
    enum: Object.values(MetricTypes),
    required: true
  },
}, { _id: false });

const SurveyFilterSchema = new Schema<SurveyFilter>(
  {
    effectiveDate: {
      type: Schema.Types.Date,
      required: true,
    },
    period: {
      type: Schema.Types.String,
      enum: Object.values(DataPeriods),
      required: true,
    },
    type: {
      type: Schema.Types.String,
      enum: Object.values(SurveyType),
      required: true,
    },
  },
  { _id: false }
);

const SurveyReportSchema = new Schema({
  initiativeIds: {
    type: [Schema.Types.ObjectId],
    required: function (this: { parent: () => CustomReportPlain }): boolean {
      return [CustomReportType.Initiatives, CustomReportType.Template].includes(this.parent().type as CustomReportType);
    },
    default: undefined
  },
  /** @deprecated it was used only for subsidiary comparison report that is now replaced by surveyFilters */
  period: {
    type: Schema.Types.String,
    enum: Object.values(DataPeriods),
    required: false,
  },
  /** @deprecated it was used only for subsidiary comparison report that is now replaced by surveyFilters */
  type: {
    type: Schema.Types.String,
    enum: Object.values(SurveyType),
    default: undefined,
  },
  statuses: {
    type: [Schema.Types.String],
    enum: Object.values(ActionList),
    default: undefined,
    required: function (this: { parent: () => CustomReportPlain }): boolean {
      return CustomReportType.Template === this.parent().type;
    },
  },
  assuranceStatus: {
    type: [Schema.Types.String],
    enum: Object.values(UtrvAssuranceStatus),
    default: undefined
  },
  /** @deprecated it was used only for subsidiary comparison report that is now replaced by surveyFilters */
  effectiveDate: {
    type: Schema.Types.Date,
    required: false,
  },
  ids: {
    type: [Schema.Types.ObjectId],
    required: function (this: { parent: () => CustomReportPlain }): boolean {
      const type = this.parent().type as CustomReportType;
      return [CustomReportType.Survey, CustomReportType.SurveyAggregation].includes(type);
    },
    default: undefined
  },
  visibility: {
    type: Schema.Types.String,
    enum: Object.values(VisibilityStatus),
    required: function (this: { parent: () => CustomReportPlain }): boolean {
      return CustomReportType.Template === this.parent().type;
    },
  },
  scope: {
    type: ScopeSchema,
    required: function (this: { parent: () => CustomReportPlain }): boolean {
      return CustomReportType.Template === this.parent().type;
    },
  },
  displayMetricOverrides: {
    type: Schema.Types.Boolean,
  },
  displayUserInput: {
    type: Schema.Types.Boolean,
  },
  surveyFilters: {
    type: [SurveyFilterSchema],
    required: function (this: { parent: () => CustomReportPlain }): boolean {
      return [CustomReportType.Template, CustomReportType.Initiatives].includes(this.parent().type as CustomReportType);
    },
    default: undefined
  }
}, { _id: false });

const COLUMN_CODES = [
  'standard',
  'code',
  'mapping',
  'valueType',
  'name',
  'valueLabel',
  'columnLabel',
  'unit',
  'numberScale',
  'combinedStatus',
  'input',
  'note',
  'tags',
  'assurance',
  'provenance',
  'reporter',
  'instructions',
  'status',
  'assuranceStatus',
  'updatedDate',
  'effectiveDate',
  'row',
  'subsidiaryCode',
  'subsidiaryName',
  'subsidiaryHierarchy',
  'surveyName',
  'aggregationGroupBy',
  'evidenceFiles',
  'externalEvidenceLinks',
  'internalEvidenceLinks',
  'explicitContributors',
  'explicitVerifiers'
] as const;

export type ColumnCode = (typeof COLUMN_CODES)[number];

export enum InputColumnRule {
  Latest = 'latest',
  All = 'all',
}

export enum AggregationGroupByRule {
  Exclude = 'exclude',
  Include = 'include',
}

interface BaseColumn {
  code: ColumnCode;
  header?: string;
}

interface InputColumn extends BaseColumn {
  code: 'input';
  rule?: InputColumnRule;
}

interface AggregationGroupByColumn extends BaseColumn {
  code: 'aggregationGroupBy';
  rule?: AggregationGroupByRule;
}

const ruleMap: { [key: string]: string[] } = {
  input: Object.values(InputColumnRule),
  aggregationGroupBy: Object.values(AggregationGroupByRule),
}

export type Column = BaseColumn | InputColumn | AggregationGroupByColumn;


export function isInputColumn(column: Column): column is InputColumn {
  return column.code === 'input';
}

export function isAggregationGroupByColumn(column: Column): column is AggregationGroupByColumn {
  return column.code === 'aggregationGroupBy';
}

/*
  For now, this type does not affect the way we order the records in custom report.
  In both cases, user can choose 1 column only and Ordering['columns'] will contains only 1 element but
  we need the type to know that user select the custom option or one of the column options from ordering dropdown in FE.
*/
export enum OrderingType {
  Default = 'default', // Ordering['columns'] contains only 1 element
  Custom = 'custom', // Ordering['columns'] contains 1 or more elements
}

export interface OrderingColumn {
  code: ColumnCode;
  direction: OrderingDirection;
}

interface Ordering {
  type: OrderingType;
  columns: OrderingColumn[];
}

export enum CustomReportTemplateType {
  Default = 'default',
  Transposed = 'transposed',
  Tabular = 'tabular',
}

interface Config {
  columns: Column[];
  ordering?: Ordering;
  templateType?: CustomReportTemplateType;
}

const ColumnSchema = new Schema<Column>(
  {
    code: {
      type: Schema.Types.String,
      enum: COLUMN_CODES,
      required: true,
    },
    header: {
      type: Schema.Types.String,
      trim: true,
    },
    rule: {
      type: Schema.Types.String,
      validate: {
        validator: function (this: Column, value: string) {
          return this.code in ruleMap && ruleMap[this.code].includes(value);
        },
        message: (props: { value?: string }) => {
          return `rule ${props.value} is invalid!`;
        }
      },
    },
  },
  { _id: false }
);

const OrderingColumnSchema = new Schema<OrderingColumn>(
  {
    code: {
      type: Schema.Types.String,
      enum: COLUMN_CODES,
      required: true,
    },
    direction: {
      type: Schema.Types.String,
      enum: Object.values(OrderingDirection),
      required: true,
    },
  },
  { _id: false }
);

const OrderingSchema = new Schema<Ordering>(
  {
    type: {
      type: Schema.Types.String,
      enum: Object.values(OrderingType),
      required: true,
    },
    columns: {
      type: [OrderingColumnSchema],
      required: true,
    },
  },
  { _id: false }
);

const ConfigSchema = new Schema<Config>(
  {
    columns: {
      type: [ColumnSchema],
      required: true,
    },
    ordering: {
      type: OrderingSchema,
    },
    templateType: {
      type: Schema.Types.String,
      enum: Object.values(CustomReportTemplateType),
      default: CustomReportTemplateType.Default,
    },
  },
  { _id: false }
);

const CustomReportSchema = new Schema<CustomReportPlain>({
  initiativeId: String,
  name: String,
  description: String,
  created: { type: Date, default: Date.now },
  lastUpdated: { type: Date, default: Date.now },
  type: {
    type: Schema.Types.String,
    enum: Object.values(CustomReportType),
    default: CustomReportType.Metrics
  },
  survey: {
    type: SurveyReportSchema,
    required: function (this: CustomReportPlain) {
      return this.type && [
        CustomReportType.Survey,
        CustomReportType.SurveyAggregation,
        CustomReportType.Initiatives,
        CustomReportType.Template,
      ].includes(this.type)
    }
  },
  metrics: {
    type: [CustomReportMetricSchema],
    default: []
  },
  config: {
    type: ConfigSchema,
    required: function (this: CustomReportPlain): boolean {
      return CustomReportType.Template === this.type;
    }
  },
}, { collection: 'custom-reports', toJSON: { virtuals: true }});

interface CustomReportMetricModel<T = Types.ObjectId> {
  utrId: T;
  type: MetricTypes;
}

type SurveyData = {
  initiativeIds: Types.ObjectId[];
  /** @deprecated it was used only for subsidiary comparison report that is replaced by surveyFilters */
  period?: DataPeriods,
  /** @deprecated it was used only for subsidiary comparison report that is replaced by surveyFilters */
  effectiveDate?: string, // ISODateString
  statuses?: ActionList[];
  assuranceStatus?: UtrvAssuranceStatus[];
  /** @deprecated it was used only for subsidiary comparison report that is replaced by surveyFilters */
  type?: SurveyType,
  surveyFilters?: SurveyFilter[];
};

type SurveyDataExtended = Omit<SurveyData, 'surveyFilters'> & { surveyFilters: SurveyFilter[] };

type SurveyDataRange = {
  // Aggregation is restricted to 1 surveyId
  ids: Types.ObjectId[];
  statuses?: string[];
  assuranceStatus?: string[];
}

export type SurveyFilter = {
  effectiveDate: Date;
  period: DataPeriods;
  type: SurveyType;
};

type SurveyTemplate = {
  /** @deprecated use initiativeIds and surveyFilters instead **/
  ids?: ObjectId[];
  initiativeIds?: ObjectId[];
  statuses: ActionList[];
  assuranceStatus?: UtrvAssuranceStatus[];
  visibility: VisibilityStatus;
  scope: Scope;
  displayMetricOverrides?: boolean;
  displayUserInput?: boolean;
  surveyFilters?: SurveyFilter[];
};

export type CustomReportModel = HydratedDocument<CustomReportPlain<ObjectId>>;

export interface CustomReportPlain<T = Types.ObjectId> {
  _id?: T;
  initiativeId: string;
  name: string;
  description: string;
  created: Date;
  lastUpdated: Date;
  metrics: CustomReportMetricModel[];
  type?: CustomReportType;
  survey?: SurveyData | SurveyDataRange | SurveyTemplate;
  config?: Config
}

export interface CustomReportWithUtrs extends CustomReportPlain {
  universalTrackers: UniversalTrackerPlain[];
}

export interface CustomReportInitiatives extends CustomReportPlain {
  type: CustomReportType.Initiatives,
  survey: SurveyData
}

export interface CustomReportInitiativesExtended extends CustomReportPlain {
  type: CustomReportType.Initiatives,
  survey: SurveyDataExtended,
}

export interface SurveyRange extends CustomReportPlain {
  type: CustomReportType.Survey | CustomReportType.SurveyAggregation,
  survey: SurveyDataRange
}

export interface CustomReportTemplate extends CustomReportPlain {
  type: CustomReportType.Template;
  survey: SurveyTemplate;
  config?: Config;
}

export const isInitiativesReport = (r: CustomReportPlain): r is CustomReportInitiatives => {
  return r.type === CustomReportType.Initiatives;
}

export const isSurveyRangeReport = (r: CustomReportPlain): r is SurveyRange => {
  return r.type === CustomReportType.Survey || r.type === CustomReportType.SurveyAggregation;
}

export const isCustomReportTemplate = (r: CustomReportPlain): r is CustomReportTemplate => {
  return r.type === CustomReportType.Template;
}

CustomReportSchema.virtual('universalTrackers', {
  ref: 'UniversalTracker', // The model to use
  localField: 'metrics.utrId', // Find people where `localField`
  foreignField: '_id', // is equal to `foreignField`
  justOne: false,
});

CustomReportSchema.index({ initiativeId: 1 }, { unique: false });
const CustomReport: Model<CustomReportPlain> = model('CustomReport', CustomReportSchema);

export default CustomReport;
