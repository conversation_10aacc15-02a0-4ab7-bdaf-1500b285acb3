/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { Model, model, Schema } from 'mongoose';
import { StakeholderGroupSchema } from './stakeholderGroup';
import { UniversalTrackerValueAssuranceModel } from '../service/assurance/model/Assurance';
import { UtrvAssurancePermissionsSchema } from './assurancePermission';
import { assuranceFieldsSchema } from "./common/assurance";

const UniversalTrackerValueAssuranceHistorySchema = new Schema({
  created: { type: Date, default: Date.now },
  action: { type: Schema.Types.String, required: true },
  utrvHistoryIndex: { type: Schema.Types.Number, required: false },
  userId: { type: Schema.Types.ObjectId, required: true },
  documents: [Schema.Types.ObjectId],
  partialFields: assuranceFieldsSchema,
});

const UniversalTrackerValueAssuranceSchema = new Schema<UniversalTrackerValueAssuranceModel>({
  utrvId: { type: Schema.Types.ObjectId, required: true },
  portfolioId: { type: Schema.Types.ObjectId, required: false },
  utrvHistoryIndex: { type: Schema.Types.Number, required: true },
  stakeholders: { type: StakeholderGroupSchema, required: true },
  permissions: [UtrvAssurancePermissionsSchema],
  status: { type: Schema.Types.String, required: true },
  history: [UniversalTrackerValueAssuranceHistorySchema],
  partialFields: assuranceFieldsSchema,
  created: { type: Date, default: Date.now },
}, { collection: 'universal-tracker-value-assurances' });



UniversalTrackerValueAssuranceSchema.index({ utrvId: 1, portfolioId: 1 }, { unique: true });
UniversalTrackerValueAssuranceSchema.index({ portfolioId: 1 }, { unique: false });
UniversalTrackerValueAssuranceSchema.virtual('universalTrackerValue', {
  ref: 'UniversalTrackerValue', // The model to use
  localField: 'utrvId', // Find people where `localField`
  foreignField: '_id', // is equal to `foreignField`
  justOne: true,
});

UniversalTrackerValueAssuranceSchema.virtual('portfolio', {
  ref: 'AssurancePortfolio', // The model to use
  localField: 'portfolioId', // Find people where `localField`
  foreignField: '_id', // is equal to `foreignField`
  justOne: true,
});


const UniversalTrackerValueAssurance: Model<UniversalTrackerValueAssuranceModel> = model(
  'UniversalTrackerValueAssurance',
  UniversalTrackerValueAssuranceSchema
);

if (process.env.DB_SYNC_INDEXES) {
  UniversalTrackerValueAssurance.syncIndexes({});
}

export default UniversalTrackerValueAssurance;
