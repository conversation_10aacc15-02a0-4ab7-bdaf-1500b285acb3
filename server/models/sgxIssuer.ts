/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { HydratedDocument, InferSchemaType, model, Schema } from 'mongoose';
import { YesNo } from '../service/data/sgx/IssuerFile';
import { ObjectId } from 'bson';

const yesNoSchema = {
  type: Schema.Types.String,
  required: true,
  default: YesNo.N,
  enum: Object.values(YesNo),
};

const IssuerAddressSchema = new Schema(    {
  issuerAddressType: { type: Schema.Types.String, required: true },
  issuerAddress1: Schema.Types.String,
  issuerAddress2: Schema.Types.String,
  issuerAddress3: Schema.Types.String,
  issuerPhone: Schema.Types.String
}, { _id: false });

const issuerName = new Schema({
  name: { type: Schema.Types.String, required: true, trim: true },
  language: { type: Schema.Types.String },
})

export const SgxIssuerSchema = new Schema({
  issuerName: { type: issuerName, required: true },
  isListed: yesNoSchema,
  countryOfIncorporation: Schema.Types.String,
  dateOfIncorporation: Schema.Types.String,
  companyRegNo: Schema.Types.String,
  principalPlaceOfBusiness: Schema.Types.String,
  designatedMarketMakerFlag: yesNoSchema,
  superParentFlag: yesNoSchema,
  corporateEmailAddress: Schema.Types.String,
  website: Schema.Types.String,
  businessDescription: Schema.Types.String,
  recordStatus: Schema.Types.String,
  localForeignFlag: yesNoSchema,
  issuerAddress: [IssuerAddressSchema],
}, { collection: 'sgx-issuers' });


export type SgxIssuerModel = HydratedDocument<InferSchemaType<typeof SgxIssuerSchema>>;
export type SgxIssuerPlain = InferSchemaType<typeof SgxIssuerSchema> & { _id: ObjectId};

export default model('SgxIssuer', SgxIssuerSchema);
