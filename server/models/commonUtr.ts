/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { StakeholderGroup } from './stakeholderGroup';
import { Types } from 'mongoose';
import { EscalationPolicy } from './escalationPolicy';

export interface CommonUtrv<T = Types.ObjectId> {
  _id?: T;
  created?: Date;

  stakeholders?: StakeholderGroup<T>;
  lastUpdated: Date | string;
  instructions?: string;

  evidenceRequired: boolean;
  verificationRequired: boolean;
  escalationPolicy?: EscalationPolicy<T>;

  status: string;
  note?: string;
  evidence?: T[];

  // Due Date  is ether endDate or effectiveDate
  endDate?: Date;
  effectiveDate?: Date;

  history: any[];
  value?: any;
}
