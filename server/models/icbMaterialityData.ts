/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

export default class ICBMaterialityData {
  static getById(ICBCode: string) {
    return data[ICBCode];
  }

  static getByIdAsDictionaryKey(ICBCode: any, prefix: string = ''): any {
    const item = this.getById(ICBCode);
    if (!item) {
      return undefined;
    }

    const result: any = {};
    item.high.map((x: number) => {
      result[prefix + x] = 'high';
    });
    item.medium.map((x: number) => {
      result[prefix + x] = 'medium';
    });
    item.low.map((x: number) => {
      result[prefix + x] = 'low';
    });
    return result;
  }

  static getByIdAsDictionaryValue(ICBCode: string, prefix: string = '') {
    const item = this.getById(ICBCode) || {};

    const result: any = {};
    if (item.high) {
      item.high.map((x: number) => {
        result[prefix + x] = 100;
      });
    }
    if (item.medium) {
      item.medium.map((x: number) => {
        result[prefix + x] = 66;
      });
    }
    if (item.low) {
      item.low.map((x: number) => {
        result[prefix + x] = 33;
      });
    }
    for (let i = 1; i < 18; i++) {
      const resultElement = result[prefix + i];
      if (!resultElement) {
        result[prefix + i] = 0;
      }
    }
    return result;
  }
}

const data: { [key: string]: { high: number[], medium: number[], low: number[] } } = ({
  '601010': {
    'high': [7, 8],
    'medium': [9, 11, 12, 13, 14, 16],
    'low': [1, 4, 5, 6, 10, 15, 17]
  },
  '601020': {
    'high': [7, 8],
    'medium': [9, 11, 12, 13, 14, 16],
    'low': [1, 4, 5, 6, 10, 15, 17]
  },
  '552010': {
    'high': [12, 13],
    'medium': [1, 2, 3, 6, 8, 9, 14, 15, 16],
    'low': [4, 5, 7, 10, 11, 17]
  },
  '551010': {
    'high': [6, 8, 9, 13],
    'medium': [1, 7, 11, 12, 15],
    'low': [3, 4, 5, 10, 14, 16, 17]
  },
  '551020': {
    'high': [6, 8, 9, 13],
    'medium': [1, 7, 11, 12, 15],
    'low': [3, 4, 5, 10, 14, 16, 17]
  },
  '551030': {
    'high': [6, 8, 9, 13],
    'medium': [1, 7, 11, 12, 15],
    'low': [3, 4, 5, 10, 14, 16, 17]
  },
  '501010': {
    'high': [8, 9, 11, 12],
    'medium': [7, 10, 13, 14, 15, 16],
    'low': [1, 3, 4, 5, 6, 17]
  },
  '502010': {
    'high': [8, 9, 12],
    'medium': [3, 7, 10, 11, 13, 14, 15, 16],
    'low': [1, 4, 5, 6, 17]
  },
  '502030': {
    'high': [8, 9, 12],
    'medium': [3, 7, 10, 11, 13, 14, 15, 16],
    'low': [1, 4, 5, 6, 17]
  },
  '502020': {
    'high': [8, 9, 12],
    'medium': [3, 7, 10, 11, 13, 14, 15, 16],
    'low': [1, 4, 5, 6, 17]
  },
  '502040': {
    'high': [8, 9, 11, 12],
    'medium': [7, 10, 13, 14, 15, 16],
    'low': [1, 3, 4, 5, 6, 17]
  },
  '502060': {
    'high': [8, 9, 12],
    'medium': [3, 6, 7, 10, 11, 13, 14, 15, 16],
    'low': [1, 4, 5, 17]
  },
  '502050': {
    'high': [8, 9, 12],
    'medium': [3, 7, 10, 11, 13, 14, 15, 16],
    'low': [1, 4, 5, 6, 17]
  },
  '451010': {
    'high': [2, 3, 8, 12],
    'medium': [1, 4, 6, 9, 13, 14, 17],
    'low': [5, 7, 10, 11, 15, 16]
  },
  '451020': {
    'high': [2, 3, 8, 12],
    'medium': [1, 4, 6, 9, 13, 14, 17],
    'low': [5, 7, 10, 11, 15, 16]
  },
  '451030': {
    'high': [2, 3, 8, 12],
    'medium': [1, 4, 6, 9, 13, 14, 17],
    'low': [5, 7, 10, 11, 15, 16]
  },
  '452010': {
    'high': [2, 3, 4, 8, 12],
    'medium': [11, 13, 14],
    'low': [1, 5, 9, 10, 16, 17]
  },
  '402010': {
    'high': [2, 3, 8, 12],
    'medium': [1, 4, 6, 9, 13, 14, 17],
    'low': [5, 7, 10, 11, 15, 16]
  },
  '401010': {
    'high': [2, 3, 8, 9, 12],
    'medium': [1, 6, 7, 13, 14, 17],
    'low': [4, 5, 10, 11, 15, 16]
  },
  '402020': {
    'high': [2, 3, 8, 12],
    'medium': [1, 4, 6, 9, 13, 14, 17],
    'low': [5, 7, 10, 11, 15, 16]
  },
  '402030': {
    'high': [2, 3, 8, 12],
    'medium': [1, 4, 6, 9, 13, 14, 17],
    'low': [5, 7, 10, 11, 15, 16]
  },
  '402040': {
    'high': [2, 3, 8, 12],
    'medium': [1, 4, 6, 9, 13, 14, 17],
    'low': [5, 7, 10, 11, 15, 16]
  },
  '404010': {
    'high': [2, 3, 4, 8, 12],
    'medium': [11, 13, 14],
    'low': [1, 5, 9, 10, 16, 17]
  },
  '403010': {
    'high': [3, 4, 8, 9, 11],
    'medium': [2, 5, 12, 16],
    'low': [1, 10, 13, 14, 17]
  },
  '405010': {
    'high': [2, 3, 4, 8, 12],
    'medium': [11, 13, 14],
    'low': [1, 5, 9, 10, 16, 17]
  },
  '201010': {
    'high': [3, 9],
    'medium': [1, 4, 5, 6, 8, 12, 15, 16, 17],
    'low': [2, 10, 11, 13]
  },
  '201020': {
    'high': [3, 9],
    'medium': [1, 4, 5, 6, 8, 12, 15, 16, 17],
    'low': [2, 10, 11, 13]
  },
  '201030': {
    'high': [3, 9],
    'medium': [1, 4, 5, 6, 8, 12, 15, 16, 17],
    'low': [2, 10, 11, 13]
  },
  '151010': {
    'high': [4, 8, 9],
    'medium': [1, 3, 5, 10, 11, 12, 13, 16],
    'low': [6, 15, 17]
  },
  '151020': {
    'high': [4, 8, 9],
    'medium': [1, 3, 5, 10, 11, 12, 13, 16],
    'low': [6, 15, 17]
  },
  '651010': {
    'high': [6, 7, 9, 11],
    'medium': [8, 12, 13, 14, 15],
    'low': [1, 4, 5, 10, 16, 17]
  },
  '651020': {
    'high': [6, 7, 9, 11],
    'medium': [8, 12, 13, 14, 15],
    'low': [1, 4, 5, 10, 16, 17]
  },
  '651030': {
    'high': [6, 7, 9, 11],
    'medium': [8, 12, 13, 14, 15],
    'low': [1, 4, 5, 10, 16, 17]
  },
  '301010': {
    'high': [8, 9],
    'medium': [1, 4, 5, 10, 11, 13, 16, 17],
    'low': [2, 7]
  },
  '303010': {
    'high': [8, 9],
    'medium': [1, 4, 5, 10, 11, 13, 16, 17],
    'low': [2, 7]
  },
  '303020': {
    'high': [8, 9],
    'medium': [1, 4, 5, 10, 11, 13, 16, 17],
    'low': [2, 7]
  },
  '302010': {
    'high': [8, 9],
    'medium': [1, 4, 5, 10, 11, 13, 16, 17],
    'low': [2, 7]
  },
  '302020': {
    'high': [8, 9],
    'medium': [1, 4, 5, 10, 11, 13, 16, 17],
    'low': [2, 7]
  },
  '302030': {
    'high': [8, 9],
    'medium': [1, 4, 5, 10, 11, 13, 16, 17],
    'low': [2, 7]
  },
  '302040': {
    'high': [8, 9],
    'medium': [1, 4, 5, 10, 11, 13, 16, 17],
    'low': [2, 7]
  },
  '302050': {
    'high': [8, 9],
    'medium': [1, 4, 5, 10, 11, 13, 16, 17],
    'low': [2, 7]
  },
  '351010': {
    'high': [8],
    'medium': [1, 4, 5, 9, 10, 13, 16, 17],
    'low': [2, 3, 7, 11, 12]
  },
  '351020': {
    'high': [8, 9],
    'medium': [1, 4, 5, 10, 11, 13, 16, 17],
    'low': [2, 7]
  },
  '101010': {
    'high': [4, 8, 9, 12, 13],
    'medium': [1, 3, 5, 6, 7, 10, 11, 15, 16, 17],
    'low': [2, 14]
  },
  '101020': {
    'high': [4, 8, 9, 12, 13],
    'medium': [1, 3, 5, 6, 7, 10, 11, 15, 16, 17],
    'low': [2, 14]
  }
});
