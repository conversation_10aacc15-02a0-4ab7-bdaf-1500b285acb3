/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { Schema, Types } from 'mongoose';

export interface StakeholderGroup<T = Types.ObjectId> {
  stakeholder: T[];
  verifier: T[];
  escalation: T[];
}

export const StakeholderGroupSchema = new Schema({
  stakeholder: {
    type: [Schema.Types.ObjectId],
    required: true
  },
  verifier: {
    type: [Schema.Types.ObjectId],
    required: true
  },
  escalation: {
    type: [Schema.Types.ObjectId],
    required: true
  },
}, { _id: false });
