/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { model, Schema } from 'mongoose';
import type {
  Actor,
  AuditLogEntry,
  AuditClient,
  GeographicalContext,
  UserAgent,
} from '../service/audit/AuditModels';
import { Operation } from '../service/audit/AuditModels';
import { LEVEL, validLevels } from '../service/event/Events';
import { generatedUUID } from '../service/crypto/token';
import ContextError from '../error/ContextError';


interface AuditLogModel extends AuditLogEntry {
  __v: number;
}

const ActorSchema = new Schema<Actor>({
  id: { type: Schema.Types.ObjectId, required: true },
  type: { type: Schema.Types.String, required: true },
  alternateId: Schema.Types.String,
  detailedEntry: Schema.Types.Mixed,
}, { _id: false });

const UserAgentSchema = new Schema<UserAgent>({
  rawUserAgent: { type: Schema.Types.String, required: true },
  os: Schema.Types.String,
  browser: Schema.Types.String,
}, { _id: false });

const GeographicalContextSchema = new Schema<GeographicalContext>({
  city: { type: Schema.Types.String, required: false },
  state: { type: Schema.Types.String, required: false },
  country: { type: Schema.Types.String, required: false },
  postalCode: { type: Schema.Types.String, required: false },
  geolocation: {
    lat: Schema.Types.Number,
    lon: Schema.Types.Number,
  },
}, { _id: false, autoCreate: false });

const AuditClientSchema = new Schema<AuditClient>({
  ipAddress: { type: Schema.Types.String, required: false },
  userAgent: { type: UserAgentSchema, required: true },
  // Optional
  device: Schema.Types.String,
  id: Schema.Types.String,
  geographicalContext: GeographicalContextSchema,
}, { _id: false });


const AuditLogSchema = new Schema<AuditLogModel>({
  // Ownership
  organizationId: { type: Schema.Types.ObjectId, required: false },
  initiativeId: { type: Schema.Types.ObjectId, required: false },

  // Event description
  service: { type: Schema.Types.String, required: true },
  event: { type: Schema.Types.String, required: true },
  message: { type: Schema.Types.String, required: true },
  severity: {
    type: Schema.Types.Number,
    enum: validLevels,
    required: true,
    default: LEVEL.INFO,
  },
  operation: {
    type: Schema.Types.String,
    enum: Object.values(Operation),
    required: true,
  },

  // Source - Targets - Outcome
  actor: ActorSchema,
  targets: [ActorSchema],
  outcome: {
    result: { type: Schema.Types.String, required: true },
    reason: Schema.Types.String,
  },

  // AuditClient Info
  client: AuditClientSchema,

  // Request info and debug
  transaction: {
    id: { type: Schema.Types.String, required: true },
    type: { type: Schema.Types.String, required: true, default: 'WEB' },
    detailedEntry: { type: Schema.Types.Mixed, default: {} },
  },
  debugContext: {
    debugData: { type: Schema.Types.Mixed, default: {} }
  },

  // General info
  eventDate: { type: Date, required: true },
  version: { type: Number, default: 0 },
  created: { type: Date, default: Date.now },
  uuid: { type: Schema.Types.String, required: true, default: () => generatedUUID() }
}, { collection: 'audit-log' });

AuditLogSchema.index({ initiativeId: 1, eventDate: -1 });

AuditLogSchema.pre('save', function (next) {
  if (!this.isNew) {
    return next(new ContextError('Save is not allowed'));
  }
  return next();
});

AuditLogSchema.pre(
  [
    'updateOne',
    'updateMany',
    'findOneAndUpdate',
    'findOneAndReplace',
    'replaceOne',
  ],
  () => {
    throw new ContextError('Update is not allowed');
  }
);

AuditLogSchema.pre(
  [
    'findOneAndDelete',
    'deleteOne',
    'deleteMany',
  ],
  () => {
    throw new ContextError('Delete is not allowed');
  }
);

export default model<AuditLogModel>('AuditLog', AuditLogSchema);
