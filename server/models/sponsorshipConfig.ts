/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */


import { HydratedDocument, model, Schema } from "mongoose";
import { DataScopeSchema, RequesterType, RestrictionType } from "./dataShare";
import { ObjectId } from "bson";
import type { DataShareMapper } from "../service/referral/referralCodesMap";
import { CustomScope, CustomScopeSchema } from "./customScopeSchema";
import { FeatureDetails } from "@g17eco/core";
import { FeatureDetailSchema } from "./featureDetailSchema";


enum DescriptionType {
  ExternalLink = 'external_link',
  HyGraph = 'hygraph',
  Text = 'text',
}

interface GraphCms {
  type: DescriptionType.HyGraph,
  code: string
  text: string;
}

interface ExternalLink {
  type: DescriptionType.ExternalLink,
  text: string;
  url: string
}

interface TextNode {
  type: DescriptionType.Text,
  text: string
}

type DescriptionElement = TextNode | ExternalLink | GraphCms;

/** Represent Agreement modal data **/
interface AgreementData {
  title: string;
  description: DescriptionElement[];
}

export interface AgreementConfig {
  code: string;
  /** These apply for initiative created Date **/
  fromDate?: Date;

  /** These only take active from specific date, therefore they can be scheduled **/
  startDate?: Date;

  /**
   * It will no longer be required or active after end date
   * Most of the time it should be replaced by newer version of agreement
   */
  endDate?: Date;

  /**
   * Custom rendering data
   * Mostly to populate custom agreement modal, not the actual agreement that
   * ideally will live outside the platform.
   */
  data?: AgreementData
}


const DescriptionElementSchema = new Schema<DescriptionElement>({
  type: {
    type: Schema.Types.String,
    required: true,
    enum: Object.values(DescriptionType)
  },
  code: {
    type: Schema.Types.String,
    required: function (this: DescriptionElement) {
      return this.type === DescriptionType.HyGraph;
    },
    trim: true
  },
  text: {
    type: Schema.Types.String,
    required: true,
    trim: true
  },
  url: {
    type: Schema.Types.String,
    required: function (this: DescriptionElement) {
      return this.type === DescriptionType.ExternalLink;
    },
    trim: true
  },
});

const AgreementDataSchema = new Schema<AgreementData>({
  title: { type: Schema.Types.String, required: true, trim: true },
  description: { type: [DescriptionElementSchema], required: true, trim: true },
}, { _id: false });

const AgreementSchema = new Schema<AgreementConfig>({
  code: { type: Schema.Types.String, required: true, trim: true },
  fromDate: { type: Schema.Types.Date, required: false },
  startDate: {
    type: Schema.Types.Date,
    required: function (this: AgreementConfig) {
      return this.endDate !== undefined;
    },
  },
  endDate: { type: Schema.Types.Date, required: false },
  data: {
    type: AgreementDataSchema,
    required: false,
  }
}, { _id: false });

const ReferralsSchema = new Schema({
  code: { type: Schema.Types.String, required: true, trim: true },
}, { _id: false });

export enum SponsorType {
  Portfolio = 'portfolio',
}

interface SubscriptionConfigItem {
  productCode: string
}

interface ConfigSubscription {
  items: SubscriptionConfigItem[];
}

interface SubscriptionConfiguration {
  couponId: string;
  subscriptions: ConfigSubscription[];
}

export interface SponsorshipSurveyConfig {
  customScopes: CustomScope[];
}

interface SponsorshipReferral {
  code: string;
}

export interface SponsorshipConfigPlain<D extends string | Date = string | Date> {
  _id: ObjectId;
  title: string;
  sponsorCode: string;
  sponsorType: SponsorType;
  /** Referral codes used to lookup sponsorship */
  referrals?: SponsorshipReferral[];
  agreements?: AgreementConfig[];
  surveyConfig?: SponsorshipSurveyConfig,
  dataShareConfigs?: DataShareMapper[];
  features?: FeatureDetails[];

  /**
   * Allow to add custom subscription items that should be provided
   * as part of sponsorship
   */
  subscriptionConfig?: SubscriptionConfiguration;

  registrationConfig: {
    /** @deprecated **/
    enableCustomCompany: boolean;
  }
  autoRenew?: boolean;
  endDate: D;
  lastUpdated: D;
  created: D;
}

export const SurveyConfigSchema = new Schema<SponsorshipSurveyConfig>({
  customScopes: {
    type: [CustomScopeSchema],
    required: false,
    default: undefined,
  },
}, { _id: false });

const DataShareConfig = new Schema<DataShareMapper>({
  title: { type: Schema.Types.String, required: true, trim: true },
  requesterCode: { type: Schema.Types.String, required: true, trim: true },
  requesterType: { type: Schema.Types.String, required: true, trim: true, enum: Object.values(RequesterType) },
  restrictionType: { type: Schema.Types.String, required: false, enum: Object.values(RestrictionType) },
  dataScope: {
    type: DataScopeSchema,
    required: false
  },
}, { _id: false });

const SubscriptionSchema = new Schema<ConfigSubscription>({
  items: [{
    productCode: {
      type: Schema.Types.String,
      required: true,
      trim: true
    }
  }],
}, { _id: false });

const SubscriptionConfig = new Schema<SubscriptionConfiguration>({
  couponId: {
    type: Schema.Types.String,
    required: true,
    trim: true,
  },
  subscriptions: [SubscriptionSchema],
}, { _id: false });

const SponsorshipConfigSchema = new Schema<SponsorshipConfigPlain<Date>>({
  title: { type: Schema.Types.String, required: true },

  sponsorCode: {
    type: Schema.Types.String,
    required: true,
    trim: true,
    minlength: 2,
  },
  sponsorType: {
    type: Schema.Types.String,
    enum: Object.values(SponsorType),
    required: true
  },

  referrals: { type: [ReferralsSchema], default: undefined },
  agreements: { type: [AgreementSchema], default: undefined },

  features: {
    type: [FeatureDetailSchema],
    default: undefined
  },

  dataShareConfigs: {
    type: [DataShareConfig],
    default: undefined,
    required: false
  },
  surveyConfig: { type: SurveyConfigSchema, required: false, default: undefined },
  subscriptionConfig: {
    type: SubscriptionConfig,
    default: undefined,
  },
  registrationConfig: {
    enableCustomCompany: Schema.Types.Boolean,
  },
  autoRenew: {
    type: Schema.Types.Boolean,
    required: false,
    default: false,
  },
  endDate: {
    type: Schema.Types.Date,
    required: true
  },
  lastUpdated: { type: Schema.Types.Date, default: () => new Date() },
  created: { type: Schema.Types.Date, default: () => new Date() },
}, { collection: 'sponsorship-configs' });

export type SponsorshipConfigModel = HydratedDocument<SponsorshipConfigPlain<Date>>;

// Most common lookup
SponsorshipConfigSchema.index({ 'referrals.code': 1 });
SponsorshipConfigSchema.index({ sponsorCode: 1, sponsorType: 1 });

const SponsorshipConfig = model('SponsorshipConfig', SponsorshipConfigSchema);

export default SponsorshipConfig;
