import { model, Schema } from 'mongoose';
import { ObjectId } from 'bson';
import { CalculationGroupPlain } from './calculationGroup';

interface Calculation {
  calculationGroupId: CalculationGroupPlain['_id'];
}

const CalculationSchema = new Schema<Calculation>(
  {
    calculationGroupId: { type: Schema.Types.ObjectId, ref: 'CalculationGroup', required: true },
  },
  { _id: false }
);

export interface UniversalTrackerConnectionPlain<T extends string | ObjectId = ObjectId> {
  _id: T;
  code: string;
  utrCode: string;
  valueListCode?: string;
  calculations: Calculation[];
}

export type ImportCalculation = { calculationGroupId?: ObjectId, calculationGroupCode: string };
export interface ImportUniversalTrackerConnection
  extends Omit<UniversalTrackerConnectionPlain, 'calculations'> {
  calculations: ImportCalculation[];
}

const UniversalTrackerConnectionSchema = new Schema<UniversalTrackerConnectionPlain>(
  {
    code: {
      type: Schema.Types.String,
      trim: true,
      required: true,
      unique: true
    },
    utrCode: { type: Schema.Types.String, required: true },
    valueListCode: { type: Schema.Types.String },
    calculations: { type: [CalculationSchema], required: true },
  },
  { collection: 'universal-tracker-connections' }
);

UniversalTrackerConnectionSchema.index({ utrCode: 1 });

export const UniversalTrackerConnection = model('UniversalTrackerConnection', UniversalTrackerConnectionSchema);
