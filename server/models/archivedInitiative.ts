/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { model } from 'mongoose';
import { ObjectId } from 'bson';
import { InitiativeSchema } from './initiative';
import { UserModel } from './user';

export interface ArchiveSubsidiaryRequest<T = string> {
  reassignedUserIds: T[];
  removedUserIds: T[];
  reassignedInitiativeId?: T;
}

export interface ArchiveSubsidiaryData<T = string> extends ArchiveSubsidiaryRequest<T> {
  archivedIds: T[];
}

export interface ArchiveSubsidiaryInput extends ArchiveSubsidiaryRequest<ObjectId> {
  archivedId: ObjectId;
  user: UserModel;
}

const ArchivedInitiativeSchema = InitiativeSchema.clone();

const ArchivedInitiative = model(
  'ArchivedInitiative',
  ArchivedInitiativeSchema,
  'archived-initiatives'
);

export default ArchivedInitiative;
