/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { model } from 'mongoose';
import { UniversalTrackerValueSchema } from './universalTrackerValue';

const ArchivedUniversalTrackerValueSchema = UniversalTrackerValueSchema.clone();

const ArchivedUniversalTrackerValue = model(
  'ArchivedUniversalTrackerValue',
  ArchivedUniversalTrackerValueSchema,
  'archived-utrvs'
);

export default ArchivedUniversalTrackerValue;
