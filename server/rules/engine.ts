/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import {
  UniversalTrackerValueExtended,
  UniversalTrackerValuePlain,
} from '../models/universalTrackerValue';
import {
  CompositeUtrRepositoryInterface,
  createCompositeUtrRepository,
} from '../repository/CompositeUtrRepository';
import {
  CompositeUtrConfigRepositoryInterface,
  getCompositeUtrConfigRepository,
} from '../repository/CompositeUtrConfigRepository';
import {
  calculateValue,
  preValidateValue,
  ValidationWithPre,
} from './calculation';
import {
  CalculationStrategy,
  CalculationType,
  ProcessUtrvUpdateResult,
} from './rule';
import { ActionList } from '../service/utr/constants';
import {
  CompositeUtrConfigInterface,
  ConfigurationVariableSetup,
  FragmentUtrConfig,
  ImportConfiguration,
  ImportConfigurationVariables,
} from '../survey/compositeUtrConfigs';
import { ObjectId } from 'bson';
import { UtrConfig } from './UtrConfig';
import AllVariablesFailedError from './error/AllVariablesFailedError';
import { getConfigCode } from '../service/utr/utrvUtil';
import { resolveVariableByUtrValueType } from './utrVariables';
import { UtrValueType } from "../models/public/universalTrackerType";
import { SourceTypes } from "../models/public/universalTrackerValueType";


export class Engine {
  private utrRepository: CompositeUtrRepositoryInterface;
  private utrConfigRepo: CompositeUtrConfigRepositoryInterface;

  constructor(
    utrRepository: CompositeUtrRepositoryInterface,
    utrConfigRepo: CompositeUtrConfigRepositoryInterface,
  ) {
    this.utrRepository = utrRepository;
    this.utrConfigRepo = utrConfigRepo;
  }

  public async calculateCompositeValue(utrv: Pick<UniversalTrackerValuePlain, '_id' | 'compositeData' | 'sourceType'>, blueprint?: string) {
    const { calculation, validation, resolved } = await this.resolvedValues(utrv, undefined, blueprint);

    // If nothing is resolved, break out
    if (Object.keys(resolved).length === 0) {
      throw new AllVariablesFailedError(
        `Failed to resolve any variables for utrvId=${utrv._id.toString()}, stopping calculation chain`
      );
    }

    return calculateValue({ calculation, variables: resolved, validation });
  }

  public async preValidateCompositeValue(utrv: UniversalTrackerValuePlain, values?: ProcessUtrvUpdateResult[], blueprint?: string) {
    const configCode = getConfigCode(utrv, blueprint);
    if (!configCode) {
      return true;
    }

    const { calculation, validation, resolved, variableOverrides } = await this.resolvedValues(utrv, values, blueprint);

    if (!validation?.pre) {
      return true;
    }

    const relevantConditions = this.relevantConditions(variableOverrides, validation as ValidationWithPre);

    return preValidateValue({
      calculation,
      variables: resolved,
      validation: {
        ...validation,
        pre: {
          ...validation.pre,
          conditions: relevantConditions
        }
      },
    });
  }

  private relevantConditions(variableOverrides: { [p: string]: boolean }, validation: ValidationWithPre) {
    const variables = Object.keys(variableOverrides);

    if (variables.length === 0) {
      return validation.pre.conditions;
    }

    return validation.pre.conditions?.filter(c => {

      if (c.type === CalculationType.Formula) {
        for (const variable of variables) {
          if (c.condition.includes(`{${variable}`)) {
            return true;
          }
        }
      }

      return false;
    });
  }

  private async resolvedValues(utrv: Pick<UniversalTrackerValuePlain, '_id' | 'compositeData' | 'sourceType'>, valueOverrides?: ProcessUtrvUpdateResult[], blueprint?: string) {

    const configCode = getConfigCode(utrv, blueprint);
    if (!configCode) {
      throw new Error(`No config code available for utrv id: "${utrv._id}"`);
    }

    const config = await this.utrConfigRepo.getSurveyByCode(configCode);
    if (!config) {
      throw new Error(`Failed to find ${configCode}, id: ${utrv._id} config`);
    }

    const expandedData = await this.fetchUtrvExpandedData(utrv, blueprint);
    const { variables, calculation, validation } = this.processImportConfig(
      utrv._id,
      config,
      expandedData
    );

    const codes: { code: string }[] = [];
    const mergedData = expandedData.map((fragmentUtrv) => {
      if (valueOverrides) {
        const override = valueOverrides.find(u => u._id === fragmentUtrv._id.toString());

        // Throw away data
        if (override) {
          fragmentUtrv.value = override.value;
          fragmentUtrv.status = ActionList.Verified;
          codes.push({ code: fragmentUtrv.universalTracker.code });
        }
      }

      return fragmentUtrv;
    });

    const variableOverrides = this.resolveVariableOverrides(codes, variables);

    const resolved = this.resolveVariables(variables, mergedData);
    return { calculation, validation, resolved, variableOverrides };
  }

  private resolveVariables(variables: ImportConfigurationVariables, data: UniversalTrackerValueExtended[]) {
    const resolved: { [key: string]: any } = {};

    Object.entries(variables).forEach(([key, v]: [string, ConfigurationVariableSetup]) => {
      for (const utrv of data) {
        const utr = utrv.universalTracker;
        if (utr.code !== v.code) {
          continue;
        }

        // Only resolved verified matching code
        if (utrv.status === ActionList.Verified) {
          resolved[key] = resolveVariableByUtrValueType(v, utr, utrv);
        }
      }
    });
    return resolved;
  }

  private async fetchUtrvExpandedData(utrv: Pick<UniversalTrackerValuePlain, '_id' | 'compositeData' | 'sourceType'>, blueprint?: string): Promise<UniversalTrackerValueExtended[]> {
    return this.utrRepository.fetchCompositeUtrvData(utrv, blueprint)
      .then((data) => data.map((v) => {
        if (Array.isArray(v.universalTracker)) {
          v.universalTracker = v.universalTracker.pop();
        }

        return v;
      }));
  }

  private resolveVariableOverrides(codes: { code: string }[], variables: ImportConfigurationVariables) {
    const resolved: { [key: string]: boolean } = {};

    Object.entries(variables).forEach(([key, v]: any) => {
      for (const overrideCode of codes) {
        if (overrideCode.code === v.code) {
          resolved[key] = true;
          return;
        }
      }
    });
    return resolved;
  }

  private processImportConfig(utrvId: ObjectId, config: CompositeUtrConfigInterface, expandedData: UniversalTrackerValueExtended[]) {

    const mainUtrvId = utrvId.toString();
    const mainUtrv = expandedData.find(extended => extended._id.toString() === mainUtrvId);
    if (!mainUtrv) {
      throw new Error(`Failed to find main utrv ${mainUtrvId} in the extended data`);
    }

    // Are we dealing with generated composite
    const mainUtrCode = mainUtrv.universalTracker.code;
    if (config.compositeUtrCode === mainUtrCode) {
      return config.importConfigurationData;
    }

    // is this target level composite utr
    if (mainUtrCode.startsWith(config.compositeUtrCode)) {
      return this.createSiblingConfiguration(mainUtrv, config);
    }

    const fragmentConfigList = config.fragmentUtrConfiguration;
    if (!fragmentConfigList) {
      throw new Error(`Failed to find fragmentUtrConfiguration for ${mainUtrCode} when processing ${mainUtrvId}`);
    }

    const fragmentConfig = fragmentConfigList[mainUtrCode];
    if (!fragmentConfig) {
      throw new Error(`Failed to find fragmentUtrConfiguration for ${mainUtrCode} when processing ${mainUtrvId}`);
    }

    // Deal with question sub fragment value chain calculation (avg).
    if (this.isSubFragmentConfig(mainUtrCode, fragmentConfig, expandedData)) {
      return this.createFragmentConfiguration(
        mainUtrCode,
        fragmentConfig,
        mainUtrv.universalTracker.valueType
      );
    }

    // Deal with SDG target value chain calculation
    throw new Error(`Missed all expected configuration case for
        ${mainUtrv._id.toString()} (source ${mainUtrv.sourceCode}, type ${mainUtrv.sourceType})
    `);
  }

  private createFragmentConfiguration(prefixCode: string, fragmentConfigs: FragmentUtrConfig[], valueType: string): ImportConfiguration {

    let variableCount = 0;
    const variables: ImportConfigurationVariables = {};

    for (const variableConfig of fragmentConfigs) {
      variables['v' + variableCount++] = {
        code: UtrConfig.generateCode(prefixCode, variableConfig)
      };
    }

    return {
      variables: variables,
      calculation: {
        type: CalculationStrategy.FirstMatch,
        values: [
          {
            type: this.getValueCalculationType(valueType),
            variables: Object.keys(variables).map((key) => {
              return { type: CalculationType.Formula, formula: `{${key}}` };
            })
          }
        ]
      }
    };
  }

  private getValueCalculationType(valueType: string) {
    if (valueType === UtrValueType.Percentage) {
      return CalculationType.ValueChainAverage;
    }
    return CalculationType.ValueChainSum;
  }

  private isSubFragmentConfig(
    prefixCode: string,
    fragmentConfiguration: FragmentUtrConfig[],
    expandedData: UniversalTrackerValueExtended[]
  ): boolean {

    for (const fragmentConfig of fragmentConfiguration) {
      const code = UtrConfig.generateCode(prefixCode, fragmentConfig);
      const data = expandedData.find(utrv => utrv.universalTracker.code === code);
      if (!data) {
        throw new Error(`Failed to find data for ${code} when processing ${prefixCode}`);
      }

      // Break out if it's not target
      if (data.sourceType !== SourceTypes.SubFragment) {
        return false;
      }
    }

    return true;
  }

  private createSiblingConfiguration(mainUtrv: UniversalTrackerValueExtended, config: CompositeUtrConfigInterface) {
    const configSubType = mainUtrv.universalTracker.code.split('/').pop();
    return UtrConfig.toSiblingConfig(config, configSubType ?? '');
  }
}

export const createRuleEngine = (repo = createCompositeUtrRepository()) => new Engine(
  repo,
  getCompositeUtrConfigRepository(),
);
