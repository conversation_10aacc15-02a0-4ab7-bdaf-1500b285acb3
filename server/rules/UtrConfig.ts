/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import {
  CompositeUtrConfigInterface,
  FragmentUtrConfig,
  ImportConfiguration,
  ImportConfigurationVariables
} from '../survey/compositeUtrConfigs';


export class UtrConfig {

  static toSiblingConfig(config: CompositeUtrConfigInterface, subType: string): ImportConfiguration {

    const variables: ImportConfigurationVariables = {};

    Object.entries(config.importConfigurationData.variables).forEach(([k, v]: any) => {
      variables[k] = { code: `${v.code}/${subType}` };
    });

    return { ...config.importConfigurationData, variables };
  }

  static generateCode(prefixCode: string, variableConfig: FragmentUtrConfig) {
    if (variableConfig.useComposite) {
      return prefixCode;
    }
    return `${prefixCode}/${variableConfig.code}`;
  }

  static getFragmentUtrCodes(config: CompositeUtrConfigInterface): string[] {
    const utrCodes: string[] = [];
    if (config.fragmentUtrCodes) {
      utrCodes.push(...config.fragmentUtrCodes);
    } else {
      const fragments = new Set(
        Object.values(config.importConfigurationData.variables).map(v => v.code)
      );
      utrCodes.push(...fragments);
    }
    return utrCodes;
  }

}
