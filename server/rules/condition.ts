/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { Condition, VariableMap } from './rule';
import { calculateFormula } from './calculation/formula';
import { OperatorsMap } from './calculation/operator';
import { wwgLogger } from '../service/wwgLogger';

export enum ConditionTypes {
  Formula = 'formula',
  Equal = 'equal',
  Operator = 'operator',
}
export type ConditionsProcessingType = (conditions: Condition[] | undefined, variables: VariableMap) => boolean;

const calculateConditionValue = function ({type, condition}: Condition, variables: VariableMap) {
  switch (type) {
    case ConditionTypes.Formula:
      return calculateFormula(condition, variables);
    case ConditionTypes.Operator:
      if (Array.isArray(condition)) {
        return condition.map(c => variables[c]);
      }
      return  variables[condition];
    default:
      throw new Error(
        `calculation "${type}" type is not supported`
      );
  }
};

const processCondition = (condition: Condition, variables: VariableMap) => {

  const value = calculateConditionValue(condition, variables);

  for (const [operator, expected] of Object.entries(condition.result))  {
    const op = OperatorsMap[operator];
    if (typeof op !== 'function') {
      return false;
    }

    const valueArray = Array.isArray(value) ? value : [value];
    for (const v of valueArray) {
      if (!op(v, expected)) {
        return false;
      }
    }
  }

  // Everything passed
  return true;
};

// Can throw errors
export const throwableConditionProcess: ConditionsProcessingType = (conditions, variables) => {
  if (!Array.isArray(conditions)) {
    return true;
  }

  for (const condition of conditions) {
    if (!processCondition(condition, variables)) {
      return false;
    }
  }

  return true;
};

export const conditionProcess: ConditionsProcessingType = (conditions, variables) => {
  try {
    return throwableConditionProcess(conditions, variables);
  } catch (e) {
    // If anything fails -
    wwgLogger.debug(e);
    return false;
  }
};
