/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CalculationRule, VariableMap } from '../rule';
import { evaluate } from 'mathjs'
import { NotApplicableTypes } from '../../models/universalTrackerValue';
import { wwgLogger } from '../../service/wwgLogger';

const pattern = /\{(.*?)\}/g; // {property}
const NOT_REPORTING_DEFAULT_VALUE = 0;
const NOT_APPLICABLE_DEFAULT_VALUE = 0;

export const isNAError = (e: { message: string }) => {
  switch (e.message) {
    case 'Undefined symbol not_applicable':
    case 'Cannot convert "not_applicable" to a number':
      return true;
  }
  return false;
}

export const isNRError = (e: { message: string }) => {
  switch (e.message) {
    case 'Undefined symbol not_reported':
    case 'Cannot convert "not_reported" to a number':
      return true;
  }
  return false;
}

const mathScope = {
  resolveString: (testVariable: string, testValue: string, onSuccess: number = 1, onFail: number = 0) => {
    if (testVariable === testValue) {
      return onSuccess;
    }
    if (testVariable === NotApplicableTypes.NA) {
      throw Error('Undefined symbol not_applicable');
    }
    if (testVariable === NotApplicableTypes.NR) {
      throw Error('Undefined symbol not_reported');
    }
    return onFail;
  },
  linearDistribution: (testVariable: number, minInput: number, maxInput: number, minOutput: number, maxOutput: number) => {
    if (!isFinite(testVariable)) { // deals with xxx/0 === 'Infinity'
      testVariable = 0;
    }

    const value = Math.max(Math.min(testVariable, maxInput), minInput);
    const valuePc = 100 * (value - minInput) / (maxInput - minInput);
    const outputPcIncrement = (maxOutput - minOutput) / 100;
    return valuePc * outputPcIncrement;
  },
  resolveNaNr: (
    testVariable: number | string,
    ifNa: string | number = NOT_APPLICABLE_DEFAULT_VALUE,
    ifNr: string | number = NOT_REPORTING_DEFAULT_VALUE,
    ifElse: undefined | number = undefined
  ) => {
    if (testVariable === NotApplicableTypes.NA) {
      return ifNa;
    }
    if (testVariable === NotApplicableTypes.NR) {
      return ifNr;
    }
    if (ifElse !== undefined) {
      return ifElse;
    }
    return isNaN(testVariable as number) ? testVariable : Number(testVariable);
  },
  ifNA: (testVariable: number | string, ifTrue: number, ifFalse: number | undefined) => {
    if (testVariable === NotApplicableTypes.NA) {
      return ifTrue;
    }
    if (ifFalse !== undefined) {
      return ifFalse;
    }
    return isNaN(testVariable as number) ? testVariable : Number(testVariable);
  },
  ifNR: (testVariable: number | string, ifTrue: number, ifFalse: number) => {
    if (testVariable === NotApplicableTypes.NR) {
      return ifTrue;
    }
    if (ifFalse !== undefined) {
      return ifFalse;
    }
    return isNaN(testVariable as number) ? testVariable : Number(testVariable);
  },
};

export const mathScopeFunctions = Object.keys(mathScope);

const getSum = (total: number, num: number) => total + num;

export const replaceOnly = (formula: string, variables: VariableMap) => {
  return formula.replace(pattern, (match, token) => {
    return String(variables[token]);
  });
};

const fnRegexPattern = `(${(mathScopeFunctions.join('|'))})\\((.*?)\\)`;
export const fnPattern = new RegExp(fnRegexPattern, 'g');
export const replaceExecuteScopeFn = (formula: string) => {
  try {
    return formula.replace(fnPattern, (match) => String(evaluate(match, mathScope)));
  } catch (e) {
    return formula;
  }
};

const createExpression = (formula: string, variables: VariableMap) => {
  return formula.replace(pattern, (match, token) => {
    const variable = variables[token];
    if (variable === undefined) {
      // Treat empties as NotReporting so it can be handled later
      return NotApplicableTypes.NR;
      // throw new FormulaExpressionVariableError(`Unable to replace formula variable ${token} for formula "${formula}"`);
    }

    return variable.toString();
  });
};

const createExpressionWithDefaultVariable = (formula: string, variables: VariableMap, defaultVariable?: number) => {
  return formula.replace(pattern, (match, token) => {
    const variable = variables[token];

    if (variable !== undefined) {
      return variable.toString();
    }

    if (defaultVariable !== undefined) {
      return defaultVariable.toString();
    }

    throw new Error(`Unable to replace formula variable ${token} for formula "${formula}"`);
  });
};

export const calculateFormula = function (formula: string | undefined, variables: VariableMap) {
  if (!formula) {
    throw new Error('Formula is not defined for config');
  }

  const expression = createExpression(formula, variables);

  return evaluate(expression, mathScope);
};

export const calculateFormulaWithDefaultVariable = (formula: string, variables: VariableMap, defaultVariable = 0) => {
  if (!formula) {
    throw new Error('Formula is not defined for config');
  }

  // Allow to fallback variables to defaultValue
  const replaced = matchCount(formula, variables);
  const variableFallback = replaced > 0 ? defaultVariable : undefined;

  const expression = createExpressionWithDefaultVariable(formula, variables, variableFallback);
  return evaluate(expression, mathScope);
};

export const calculateValueFromFormula = ({ variables, value }: CalculationRule) => {
  return calculateFormula(value.formula, variables);
};

export const calculateAverageFromFormula = ({ variables, value }: CalculationRule) => {

  if (!Array.isArray(value.variables)) {
    throw new Error('Average formula configuration must have variables array')
  }

  const average = [];
  for (const groupVariable of value.variables) {

    try {
      const calculatedValue = calculateValueFromFormula({ value: groupVariable, variables });
      average.push(calculatedValue);
    } catch (e) {
      // Early exit with NA or NR
      if (isNAError(e)) {
        continue;
      }
      if (isNRError(e)) {
        average.push(NOT_REPORTING_DEFAULT_VALUE);
        continue;
      }
      wwgLogger.error(`Unhandled error calculateAverageFromFormula in formula ${groupVariable.formula}: ${e.message}`);
    }
  }

  if (average.length) {
    return average.reduce(getSum) / average.length;
  }

  return NotApplicableTypes.NA;
};

export const calculateSumFromFormula = ({ variables, value }: CalculationRule, setDefaultValue = false) => {

  if (!Array.isArray(value.variables)) {
    throw new Error(`calculateSumFromFormula configuration must have variables array`)
  }

  const groupSum = [];
  for (const groupVariable of value.variables) {

    try {
      const calculatedValue = calculateValueFromFormula({ value: groupVariable, variables });
      groupSum.push(calculatedValue);
    } catch (e) {
      // Early exit with NA or NR
      if (isNAError(e)) {
        return NotApplicableTypes.NA;
      }
      if (isNRError(e)) {
        return NotApplicableTypes.NR;
      }
      // Check if need to push it to the not reporting value
      if (setDefaultValue) {
        groupSum.push(NOT_REPORTING_DEFAULT_VALUE);
        continue;
      }
      wwgLogger.error(`Unhandled error calculateSumFromFormula in formula ${groupVariable.formula}: ${e.message}`);
    }
  }

  if (groupSum.length) {
    return groupSum.reduce(getSum);
  }

  return NotApplicableTypes.NA;
};

export const calculateWeightedSumFromFormula = ({ variables, value }: CalculationRule) => {

  if (!Array.isArray(value.variables)) {
    throw new Error(`calculateSumFromFormula configuration must have variables array`)
  }

  const groupSum = [];
  const denominatorSum: number[] = [];
  for (const groupVariable of value.variables) {
    const weight = groupVariable.weight ?? 1;

    try {
      const calculatedValue = calculateValueFromFormula({ value: groupVariable, variables });
      groupSum.push(weight * calculatedValue);
      denominatorSum.push(weight);

    } catch (e) {
      // Early exit with NA or NR
      if (isNAError(e)) {
        continue;
      }
      if (isNRError(e)) {
        groupSum.push(NOT_REPORTING_DEFAULT_VALUE);
        denominatorSum.push(weight);
        continue;
      }
      // Check if need to push it to the not reporting value
      wwgLogger.error(`Unhandled error calculateWeightedSumFromFormula in formula ${groupVariable.formula}: ${e.message}`);
    }
  }

  if (groupSum.length) {
    const denominator = denominatorSum.reduce(getSum);
    return groupSum.map(g => g / denominator).reduce(getSum);
  }

  return NotApplicableTypes.NA;
};

export const matchCount = (formula: string, variables: VariableMap) => {
  let available = 0;
  formula.replace(pattern, (match, token) => {
    if (variables[token] !== undefined) {
      available++;
    }
    return match;
  });
  return available;
};

export const tryCalculation = ({ formula, variables }: { formula: string; variables: VariableMap }) => {
  try {
    const value = calculateFormulaWithDefaultVariable(formula, variables);
    if (!isFinite(value)) {
      return 0;
    }
    return value;
  } catch (e) {
    return '';
  }
};
