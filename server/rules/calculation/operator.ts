/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { ActionList } from '../../service/utr/constants';

type Operator = (v: number, expected: any) => boolean;

const isUndefined = (v: any, expected: boolean) => (v === undefined) === expected;

export const OperatorsMap: {[key: string]: Operator} = {
  gt: (v: number, expected: number) => v > expected,
  lt: (v: number, expected: number) => v < expected,
  gte: (v: number, expected: number) => v >= expected,
  lte: (v: number, expected: number) => v <= expected,
  equal: (v: number, expected: any) => v === expected,
  isNumber: (v: any, expected: boolean) => !isNaN(v) === expected,
  isNotReported: (v: any, expected: boolean) => v === ActionList.NotReported,
  isNotApplicable: isUndefined,
  isUndefined,
};
