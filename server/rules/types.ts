/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { ValueRule, VariableMap } from './rule';

export interface Calculation {
  type: string;
  values: ValueRule[];
}

export interface Validation {
  pre?: ValueRule;
  after?: ValueRule;
}

export interface ValidationWithPre extends Validation {
  pre: ValueRule;
}

export interface ValidationRules {
  variables: VariableMap;
  validation?: ValueRule;
}

export interface ResolvedImportConfiguration {
  variables: VariableMap;
  calculation: Calculation;
  validation?: Validation;
}