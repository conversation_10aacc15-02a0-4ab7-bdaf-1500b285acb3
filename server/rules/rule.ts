/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

export interface CalculationValueConfig {
  type: string;
  formula?: string;
  variables?: CalculationValueConfig[];
  weight?: number;
}

export interface VariableMap {
  [key: string]: number | string | undefined;
}

export interface CalculationRule {
  variables: VariableMap;
  value: CalculationValueConfig;
}

export interface Condition {
  type: string;
  condition: any;
  result: { [key: string]: any; };
}

export interface ValueRule {
  type: string | CalculationType;
  formula?: string;
  [key: string]: any;
  variables?: CalculationValueConfig[];
  conditions?: Condition[];
}

export enum CalculationType {
  ValueChainAverage = 'value_chain_average',
  ValueChainSum = 'value_chain',
  Average = 'average',
  Sum = 'sum',
  WeightedSum = 'weighted_sum',
  Formula = 'formula',
  Text = 'text',
}

export enum CalculationStrategy {
  FirstMatch = 'first_match',
}

export interface ProcessUtrvUpdateResult {
  _id: string;
  value?: number;
  oldValue?: any;
}
