/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { Request<PERSON>andler } from 'express';
import { decodeToken } from '../service/authentication/token';

export const checkToken: RequestHandler = (req, res, next) => {
  if (!req.params.token) {
    return res.Invalid('Token is not valid');
  }
  decodeToken(req.params.token)
    .then(data => {
      req.tokenData = data;
      next();
    })
    .catch(() => res.Invalid('Token is not valid'));
};
