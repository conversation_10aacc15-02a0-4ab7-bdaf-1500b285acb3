import { RequestHand<PERSON> } from 'express';
import { MergeTags } from '../service/survey-template/types';
import { BulkSurveysData } from '../models/survey';
import { SurveyTemplateRepository } from '../repository/SurveyTemplateRepository';
import { InitiativePermissions } from '../service/initiative/InitiativePermissions';
import UserError from '../error/UserError';
import { getCleanTag, tagRegex } from '../util/survey-templates';
import { SURVEY } from '../util/terminology';

export const canManageTemplate: RequestHandler = async (req, res, next) => {
  const { user, params } = req;
  const templateId = params.templateId;

  if (!user) {
    return res.NotPermitted();
  }

  try {
    const template = await SurveyTemplateRepository.mustFindByIdWithRootCurrency(templateId);
    res.locals.template = template;
    const hasPermission = await InitiativePermissions.canManageInitiative(user, template.initiativeId);
    if (!hasPermission) {
      return res.NotPermitted();
    }
  } catch (e) {
    return next(e);
  }
  return next();
};

export const hasValidTags: RequestHandler = (req, res, next) => {
  const { surveyName } = req.body as BulkSurveysData;
  if (!surveyName) {
    return next();
  }
  const mergeTags = surveyName.match(tagRegex);
  if (!mergeTags || mergeTags.length === 0) {
    return next();
  }
  const invalidTags = mergeTags.filter(
    (tag) => !Object.values(MergeTags).includes(getCleanTag(tag) as MergeTags)
  );
  if (invalidTags.length > 0) {
    return res.Exception(new UserError(`${SURVEY.CAPITALIZED_SINGULAR} name has invalid merge tags: ${invalidTags.toString()}`));
  }
  return next();
};
