/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { NextFunction, RequestHandler, Response } from 'express';
import { SubmissionInsightsQuery, SubmissionInsightsView } from '../repository/PortfolioRepository';
import PermissionDeniedError from '../error/PermissionDeniedError';
import UserError from '../error/UserError';
import { UserInitiativeRepository } from '../repository/UserInitiativeRepository';
import { InitiativePermissions } from '../service/initiative/InitiativePermissions';
import { AuthenticatedRequest } from '../http/AuthRouter';
import { toBoolean } from '../http/query';

type SubmissionInsightsRequest<T> = AuthenticatedRequest<any, any, any, Partial<SubmissionInsightsQuery<T>>>;

export const populatePortfolio: RequestHandler = (req, res, next) => {
  if (!req.user) {
    return next(new PermissionDeniedError())
  }
  UserInitiativeRepository.getUserInitiative(req.user, req.params.portfolioId)
    .then((portfolio) => {
      if (!portfolio) {
        return next(new PermissionDeniedError())
      }
      if (!portfolio?.initiativeGroupId) {
        return next(new UserError('Not a valid Portfolio or Portfolio Group'));
      }
      res.locals.portfolio = portfolio;
      next();
    }).catch((e: Error) => res.Exception(e));
};

export const canManagePortfolio: RequestHandler = async (req, res, next) => {
  const { user, params } = req;
  if (!user) {
    return next(new PermissionDeniedError());
  }

  try {
    if (!await InitiativePermissions.canManageInitiative(user, params.portfolioId)) {
      return next(new PermissionDeniedError());
    }
  } catch (e) {
    return next(e);
  }
  return next();
};

const isValidView = (view: string): view is SubmissionInsightsView => {
  return Object.values(SubmissionInsightsView).includes(view as SubmissionInsightsView);
}

export const fromSubmissionInsightsRequest = (
  req: SubmissionInsightsRequest<string>,
  res: Response,
  next: NextFunction
) => {
  const { view = SubmissionInsightsView.Company, isCompleted = false, startDate, endDate, scopeGroup } = req.query;

  if (!isValidView(view as string)) {
    throw new UserError('Invalid view, view must be sector or company');
  }

  const filters: SubmissionInsightsQuery<Date> = {
    view,
    isCompleted: toBoolean(isCompleted),
    startDate: startDate ? new Date(startDate) : undefined,
    endDate: endDate ? new Date(endDate) : undefined,
    scopeGroup,
  };

  res.locals.filters = filters;
  return next();
};
