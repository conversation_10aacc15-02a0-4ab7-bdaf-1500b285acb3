/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { Request<PERSON>andler } from 'express';
import config from '../config';
import HttpError from '../error/HttpError';
import { wwgLogger } from '../service/wwgLogger';

export const webhookAuth: RequestHandler = (req, res, next) => {
  const envSecret = config.queue.webhook.secret;
  const requestSecret = req.headers.authorization;

  if (!requestSecret || requestSecret !== envSecret) {
    wwgLogger.error('Invalid webhook authentication header');
    return next(new HttpError('Invalid authentication header', 401));
  }
  next()

};
