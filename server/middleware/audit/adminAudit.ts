/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import { RequestHandler } from 'express';
import { AuditEvent, CreateAuditEntry } from '../../service/audit/AuditModels';
import { fromRequest } from './contextMiddleware';
import { wwgLogger } from '../../service/wwgLogger';
import { AuthenticatedRequest } from '../../http/AuthRouter';
import HttpError from '../../error/HttpError';

interface AdditionalContext extends Pick<Partial<CreateAuditEntry>, 'operation' | 'severity' | 'organizationId' | 'outcome'>{
  service: string;
  event?: AuditEvent;
  skipMethods?: string[]
}

export const AdminAuditLogs: (additionalContext: AdditionalContext) => RequestHandler = (additionalContext) => {

  const {
    operation,
    severity,
    event,
    service,
    skipMethods = ['get'],
  } = additionalContext;

  return (req, res, next) => {

    if (!req.user) {
      return next(new HttpError('User is not authenticated for admin context', 401));
    }

    if (skipMethods.includes(req.method.toLowerCase())) {
      return next();
    }

    fromRequest(req as AuthenticatedRequest).then((context) => {
      const op = operation ?? context.operation;
      const sev = severity ?? context.severity;
      // Custom one or create from service and operation
      const eventCode = event?.code ?? `admin.${service}.${op}`;

      context.operation = op;
      context.severity = sev;
      context.event = eventCode;
      context.message = event?.name ?? `User executing ${eventCode}, operation: ${op}`;

      wwgLogger.info(context)
      next()
    }).catch(err => next(err));
  };
};
