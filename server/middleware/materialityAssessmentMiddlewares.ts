import { RequestHandler } from 'express';
import { SurveyRepository } from '../repository/SurveyRepository';
import UserError from '../error/UserError';

export const canGenerateScore: RequestHandler = async (req, res, next) => {
  const { surveyId } = req.params;
  try {
    const survey = await SurveyRepository.mustFindById(surveyId);
    if (!survey.completedDate) {
      return next(new UserError('This assessment has not been completed yet', { surveyId }));
    }
  } catch (e) {
    return next(e);
  }
  return next();
};
