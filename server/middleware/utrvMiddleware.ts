/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { <PERSON><PERSON><PERSON>andler } from 'express';
import PermissionDeniedError from '../error/PermissionDeniedError';
import { UniversalTrackerValuePlain } from '../models/universalTrackerValue';
import { UniversalTrackerValueRepository } from '../repository/UniversalTrackerValueRepository';
import { UtrvPermissions } from '../service/utr/UtrvPermissions';
import { InitiativePermissions } from '../service/initiative/InitiativePermissions';
import ContextError from '../error/ContextError';
import { UtrvType } from '../service/utr/constants';
import { KeysEnum } from '../models/commonProperties';
import { QUESTION } from '../util/terminology';

const NO_ACCESS_ERROR_MESSAGE = `Your access to this ${QUESTION.SINGULAR} has been removed. If you think this is a mistake, contact an administrator.`;

export type AccessUtrv = Pick<
  UniversalTrackerValuePlain,
  | '_id'
  | 'stakeholders'
  | 'initiativeId'
  | 'compositeData'
  | 'type'
  | 'sourceItems'
  | 'valueAggregation'
  | 'universalTrackerId'
  | 'effectiveDate'
>;

const getUtrv = async (utrvId: string): Promise<undefined | AccessUtrv> => {
  return (
    await UniversalTrackerValueRepository.findById(utrvId, {
      _id: 1,
      stakeholders: 1,
      universalTrackerId: 1,
      initiativeId: 1,
      compositeData: 1,
      type: 1,
      sourceItems: 1,
      valueAggregation: 1,
      effectiveDate: 1,
    } satisfies KeysEnum<AccessUtrv>)
  )?.toObject();
};

export const canAccessUtrv: RequestHandler = async (req, res, next) => {
  const { user, params } = req;
  const { utrvId } = params;

  if (!user) {
    return next(new PermissionDeniedError());
  }

  try {
    const utrv = await getUtrv(utrvId);
    if (!utrv) {
      return next(new PermissionDeniedError());
    }
    const hasPerm = await UtrvPermissions.canAccess(utrv, user);
    if (!hasPerm) {
      return next(new PermissionDeniedError(NO_ACCESS_ERROR_MESSAGE));
    }
    res.locals.utrv = utrv;
  } catch (e) {
    return next(e);
  }
  return next();
};

export const canContributeUtrv: RequestHandler = async (req, res, next) => {
  const { user, params } = req;
  const { utrvId } = params;

  if (!user) {
    return next(new PermissionDeniedError());
  }

  try {
    const utrv = await getUtrv(utrvId);
    if (!utrv) {
      return next(new PermissionDeniedError());
    }
    const hasPerm = await UtrvPermissions.canContribute(utrv, user);
    if (!hasPerm) {
      return next(new PermissionDeniedError(NO_ACCESS_ERROR_MESSAGE));
    }
  } catch (e) {
    return next(e);
  }
  return next();
};

export const canVerifyUtrv: RequestHandler = async (req, res, next) => {
  const { user, params } = req;
  const { utrvId } = params;

  if (!user) {
    return next(new PermissionDeniedError());
  }

  try {
    const utrv = await getUtrv(utrvId);
    if (!utrv) {
      return next(new PermissionDeniedError());
    }
    const hasPerm = await UtrvPermissions.canVerify(utrv, user);
    if (!hasPerm) {
      return next(new PermissionDeniedError(NO_ACCESS_ERROR_MESSAGE));
    }
  } catch (e) {
    return next(e);
  }
  return next();
};

export const canRejectUtrv: RequestHandler = async (req, res, next) => {
  const { user, params } = req;
  const { utrvId } = params;

  if (!user) {
    return next(new PermissionDeniedError());
  }

  try {
    const utrv = await getUtrv(utrvId);
    if (!utrv) {
      return next(new ContextError('Cannot find UTRV', { utrvId }));
    }
    // only allow admin/owner to reject utrvs (target/baseline)
    if ([UtrvType.Target, UtrvType.Baseline].includes(utrv.type as UtrvType)) {
      const initiativeId = utrv.initiativeId;
      const canManageInitiative = await InitiativePermissions.canManageInitiative(user, initiativeId);
      if (canManageInitiative) {
        return next();
      }
      return next(new PermissionDeniedError(NO_ACCESS_ERROR_MESSAGE));
    }

    const canVerifyUtrv = await UtrvPermissions.canVerify(utrv, user);
    if (!canVerifyUtrv) {
      return next(new PermissionDeniedError(NO_ACCESS_ERROR_MESSAGE));
    }
  } catch (e) {
    return next(e);
  }
  return next();
};

export const canManageTargetBaseline: RequestHandler = async (req, res, next) => {
  const { user, params } = req;
  const { utrvId } = params;

  if (!user) {
    return next(new PermissionDeniedError());
  }

  try {
    const utrv = await getUtrv(utrvId);
    if (!utrv) {
      return next(new ContextError('Cannot find UTRV', { utrvId }));
    }
    if (![UtrvType.Target, UtrvType.Baseline].includes(utrv.type as UtrvType)) {
      return next(new ContextError('Only target or baseline is supported'));
    }
    const initiativeId = utrv.initiativeId;
    const canManageInitiative = await InitiativePermissions.canManageInitiative(user, initiativeId);
    if (canManageInitiative) {
      res.locals.initiativeId = initiativeId;
      return next();
    }
    return next(new PermissionDeniedError(NO_ACCESS_ERROR_MESSAGE));
  } catch (e) {
    return next(e);
  }
};
