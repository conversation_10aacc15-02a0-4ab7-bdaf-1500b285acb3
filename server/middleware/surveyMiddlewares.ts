/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { RequestHandler } from 'express';
import { SurveyPermissions } from '../service/survey/SurveyPermissions';
import { SurveyRepository } from '../repository/SurveyRepository';
import { SurveyType } from '../models/survey';
import UserError from '../error/UserError';
import BackgroundJob, { JobType } from '../models/backgroundJob';
import { ObjectId } from 'bson';
import BadRequestError from '../error/BadRequestError';
import { JobStatus } from '../models/surveyTemplateHistory';
import { SupportedJobModel } from '../service/materiality-assessment/background-job/types';

export const canManageSurvey: RequestHandler = async (req, res, next) => {
  const { user, params } = req;
  const surveyId = params.surveyId;

  if (!user) {
    return res.NotPermitted();
  }

  try {
    const survey = await SurveyRepository.mustFindById(surveyId);
    res.locals.survey = survey;
    const hasPerm = await SurveyPermissions.canManage(survey, user);
    if (!hasPerm) {
      return res.NotPermitted();
    }
  } catch (e) {
    return next(e);
  }
  return next();
};

export const canViewSurvey: RequestHandler = async (req, res, next) => {
  const { user, params } = req;
  const surveyId = params.surveyId;

  if (!user) {
    return res.NotPermitted();
  }

  try {
    const survey = await SurveyRepository.mustFindById(surveyId);
    res.locals.survey = survey;
    const hasPerm = await SurveyPermissions.canAccess(survey, user);
    if (!hasPerm) {
      return res.NotPermitted();
    }
  } catch (e) {
    return next(e);
  }
  return next();
};

export const canAccessInsights: RequestHandler = async (req, res, next) => {
  const { user, params } = req;
  const surveyId = params.surveyId;

  if (!user) {
    return res.NotPermitted();
  }

  try {
    const survey = await SurveyRepository.mustFindById(surveyId);
    res.locals.survey = survey;
    const hasPerm = await SurveyPermissions.canAccessAllData(survey, user);
    if (!hasPerm) {
      return res.NotPermitted();
    }
  } catch (e) {
    return next(e);
  }
  return next();
};

export const isMaterialitySurvey: RequestHandler = async (req, res, next) => {
  try {
    if (!res.locals.survey) {
      res.locals.survey = await SurveyRepository.mustFindById(req.params.surveyId);
    }
    const isMaterialitySurvey = res.locals.survey.type === SurveyType.Materiality;
    if (!isMaterialitySurvey) {
      return next(new UserError('This assessment is not a materiality assessment', { surveyId: req.params.surveyId }));
    }
  } catch (e) {
    return next(e);
  }
  return next();
}

export const isScoresCalculated: RequestHandler = async (req, res, next) => {
  try {
    const { scoreJobId } = req.params;
    const existingJob = await BackgroundJob.findOne<SupportedJobModel>({
      type: JobType.MaterialityAssessmentScores,
      _id: new ObjectId(scoreJobId),
    }).exec();

    const result = existingJob?.tasks[0].data.result;
    if (!existingJob || existingJob.status !== JobStatus.Completed || !result) {
      return next(
        new BadRequestError('Materiality scores are not available. Please try again later.', {
          scoreJobId,
        })
      );
    }

    res.locals.scoreJob = existingJob;
  } catch (e) {
    return next(e);
  }
  return next();
}
