import { RequestHandler } from 'express';
import PermissionDeniedError from '../error/PermissionDeniedError';
import { ObjectId } from 'bson';
import { AssurancePermissions } from '../service/assurance/AssurancePortfolioPermissions';
import { AssurancePortfolioPlain } from '../service/assurance/model/AssurancePortfolio';
import { UserPlain } from '../models/user';

type Handler = (
  portfolio: Pick<AssurancePortfolioPlain, 'permissions' | 'organizationId' | 'initiativeId'>,
  user: UserPlain
) => Promise<boolean>;

const hasAssurancePortfolioPermission =
  (handler: Handler): RequestHandler =>
  async (req, res, next) => {
    try {
      const {
        params: { id: portfolioId },
        user,
      } = req;

      if (!user || !ObjectId.isValid(portfolioId)) {
        return res.NotPermitted();
      }

      const portfolio = await AssurancePermissions.getPortfolio(req.params.id, user);
      const hasPerm = await handler(portfolio, user);

      if (!hasPerm) {
        return next(new PermissionDeniedError());
      }
      res.locals.assurancePortfolio = portfolio.toObject<AssurancePortfolioPlain>();
    } catch (error) {
      return next(error);
    }
    return next();
  };

/** Managing includes delegating users, assuring portfolio and managing users at assurance portfolio level. */
export const canManageAssurancePortfolio: RequestHandler = hasAssurancePortfolioPermission((...args) =>
  AssurancePermissions.isAdmin(...args)
);

export const canViewAssurancePortfolio: RequestHandler = hasAssurancePortfolioPermission((...args) =>
  AssurancePermissions.hasAccess(...args)
);

export const canAssureMetrics: RequestHandler = hasAssurancePortfolioPermission((...args) =>
  AssurancePermissions.canAssureMetrics(...args)
);
