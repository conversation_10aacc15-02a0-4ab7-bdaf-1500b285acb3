/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { RequestHandler } from 'express';
import { UserErrorMessages } from '../error/ErrorMessages';

export const UserActive: RequestHandler = async (req, res, next) => {
  if (!req.user?.active) {
    return res.NotPermitted(UserErrorMessages.NotActive);
  }
  return next();
};

export const checkIsStaff: RequestHandler = (req, res, next) => {
  if (!req.user?.isStaff) {
    return res.NotPermitted();
  }
  return next();
};

export const checkIsSuperAdmin: RequestHandler = (req, res, next) => {
  if (!req.user?.isSuperUser) {
    return res.NotPermitted("Not Permitted. Must have additional privileges");
  }
  return next();
};
