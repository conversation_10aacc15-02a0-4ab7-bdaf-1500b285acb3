/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { RequestHandler } from 'express';
import PermissionDeniedError from '../error/PermissionDeniedError';
import { InitiativePermissions } from '../service/initiative/InitiativePermissions';
import Initiative, { isOrganization } from '../models/initiative';
import UserError from '../error/UserError';

export const canManageCustomReports: RequestHandler = async (req, res, next) => {
  const { user, params } = req;
  res.locals.initiativeId = params.initiativeId;

  if (!user) {
    return next(new PermissionDeniedError());
  }

  try {
    const hasPerm = await InitiativePermissions.canManageCustomReports(user, params.initiativeId);
    if (!hasPerm) {
      return next(new PermissionDeniedError());
    }
  } catch (e) {
    return next(e);
  }
  return next();
};

export const canManageInitiative: RequestHandler = async (req, res, next) => {
  const { user, params } = req;
  res.locals.initiativeId = params.initiativeId;

  if (!user) {
    return next(new PermissionDeniedError());
  }

  try {
    const hasPerm = await InitiativePermissions.canManageInitiative(user, params.initiativeId);
    if (!hasPerm) {
      return next(new PermissionDeniedError());
    }
  } catch (e) {
    return next(e);
  }
  return next();
};

export const canAccessInitiative: RequestHandler = async (req, res, next) => {
  const { user, params } = req;
  res.locals.initiativeId = params.initiativeId;

  if (!user) {
    return next(new PermissionDeniedError());
  }

  try {
    const hasPerm = await InitiativePermissions.canAccess(user, params.initiativeId);
    if (!hasPerm) {
      return next(new PermissionDeniedError());
    }
  } catch (e) {
    return next(e);
  }
  return next();
};

export const isNotRootLevel: RequestHandler = async (req, res, next) => {
  try {
    const initiative = await Initiative.findById(req.params.initiativeId).orFail().exec();
    if (isOrganization(initiative)) {
      return next(new UserError('Root organisation cannot be archived'));
    }
  } catch (error) {
    return next(error);
  }
  return next();
};

export const isRootLevel: RequestHandler = async (req, res, next) => {
  try {
    const initiative = await Initiative.findById(req.params.initiativeId).orFail().exec();
    if (!isOrganization(initiative)) {
      return next(new UserError('Change can only be made at the organisation', { initiativeId: req.params.initiativeId }));
    }
    res.locals.initiative = initiative;
  } catch (error) {
    return next(error);
  }
  return next();
};

export const isInitiativeExists: RequestHandler = async (req, res, next) => {
  try {
    const initiativeId = req.params.initiativeId;
    const initiative = await Initiative.findById(initiativeId).lean().exec();
    if (!initiative) {
      return next(
        new UserError('The initiative does not exist. Please check and try again.', {
          initiativeId,
        })
      );
    }
    res.locals.initiative = initiative;
  } catch (error) {
    return next(error);
  }
  return next();
};
