import { Request<PERSON><PERSON><PERSON> } from 'express';
import { StaffR<PERSON>, StaffRoleToScopesMap, StaffScope } from '../models/staffRole';
import { UserPlain } from '../models/user';
import PermissionDeniedError from '../error/PermissionDeniedError';

export const getCombinedStaffScopes = (user: Pick<UserPlain, 'isStaff' | 'isSuperUser' | 'staffRoles'>) => {
  if (user.isSuperUser) {
    return StaffRoleToScopesMap[StaffRole.Admin].scopes;
  }

  if (!user.staffRoles) {
    if (user.isStaff) {
      return StaffRoleToScopesMap[StaffRole.User].scopes;
    }
    return [];
  }

  const { roles, scopes } = user.staffRoles;
  if (roles.length === 0) {
    return scopes;
  }

  const mergedScopes = roles.reduce((acc, role) => {
    return [...acc, ...StaffRoleToScopesMap[role].scopes];
  }, scopes);

  return Array.from(new Set(mergedScopes));
};

export const requireStaffScopes = (requiredScopes: StaffScope[]): RequestHandler => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new PermissionDeniedError());
    }
    const combinedScopes = getCombinedStaffScopes(req.user);

    const missingScopes = requiredScopes.filter((scope) => !combinedScopes.includes(scope));
    if (missingScopes.length > 0) {
      return res.NotPermitted(`Not Permitted. Missing scopes: ${missingScopes.toString()}`);
    }
    return next();
  };
};

export const hasStaffScopes = (requiredScopes: StaffScope[]): RequestHandler => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new PermissionDeniedError());
    }
    const combinedScopes = getCombinedStaffScopes(req.user);
    const hasMatchedScope = combinedScopes.some((scope) => requiredScopes.includes(scope));
    if (!hasMatchedScope) {
      return res.NotPermitted(`Not Permitted. Missing one of these scopes: ${requiredScopes.toString()}`);
    }
    return next();
  };
};
