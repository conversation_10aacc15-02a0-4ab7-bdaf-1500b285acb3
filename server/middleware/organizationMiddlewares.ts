import { RequestHand<PERSON> } from 'express';
import PermissionDeniedError from '../error/PermissionDeniedError';
import Organization, { OrganizationLike, OrganizationPermissionsMin } from '../models/organization';
import { organizationPermissionsMinProjection } from '../repository/projections';
import { AssuranceOrganizationPermissions } from '../service/assurance/AssuranceOrganizationPermissions';
import { ObjectId } from 'bson';

type Handler = (userId: ObjectId | string, organizationOrId: OrganizationLike) => Promise<boolean>;

const hasOrganizationPermission =
  (handler: Handler): RequestHandler =>
  async (req, res, next) => {
    try {
      const { params, user } = req;
      const userOrganizationId = user?.organizationId;

      if (!userOrganizationId || !userOrganizationId.equals(params.organizationId)) {
        return next(new PermissionDeniedError());
      }

      const organization = await Organization.findById(userOrganizationId, organizationPermissionsMinProjection)
        .orFail()
        .lean<OrganizationPermissionsMin>()
        .exec();

      if (!(await handler(user._id, organization))) {
        return next(new PermissionDeniedError());
      }
      res.locals.organization = organization;
    } catch (error) {
      return next(error);
    }
    return next();
  };

export const canManageOrganization: RequestHandler = hasOrganizationPermission((...args) =>
  AssuranceOrganizationPermissions.canManageOrganization(...args)
);

export const canViewPortfolios: RequestHandler = hasOrganizationPermission((...args) =>
  AssuranceOrganizationPermissions.canAccessOrganization(...args)
);
