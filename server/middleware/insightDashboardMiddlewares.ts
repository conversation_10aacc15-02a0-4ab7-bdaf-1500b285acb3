import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { ObjectId } from 'bson';
import { InsightDashboard } from '../models/insightDashboard';
import { InitiativePermissions, InitiativePermissionsUser } from '../service/initiative/InitiativePermissions';
import PermissionDeniedError from '../error/PermissionDeniedError';
import { InitiativeRepository } from '../repository/InitiativeRepository';
import UserError from '../error/UserError';
import { getSummaryDashboardService } from '../service/insight-dashboard/summary/SummaryDashboardService';
import { isStaticDashboardType } from '../service/insight-dashboard/utils';

const summaryDashboardService = getSummaryDashboardService();

const handlePerm = (
  permissionCheckCallback: (user: InitiativePermissionsUser, initiativeId: string | ObjectId) => Promise<boolean>
): RequestHandler => {
  return async (req, res, next) => {
    const { user, params } = req;

    if (!user) {
      return res.NotPermitted();
    }

    try {
      const hasPerm = await permissionCheckCallback(user, params.initiativeId);
      if (!hasPerm) {
        return next(new PermissionDeniedError());
      }

      res.locals.initiativeId = params.initiativeId;
      const initiativeId = new ObjectId(params.initiativeId);

      if (isStaticDashboardType(params.dashboardId)) {
        res.locals.dashboard = await summaryDashboardService.findOrCreate({
          initiativeId,
          creatorId: user._id,
          type: params.dashboardId,
        });
      } else {
        const dashboardId = new ObjectId(params.dashboardId);
        res.locals.dashboard = await InsightDashboard.findOne({ _id: dashboardId, initiativeId }).orFail().exec();
      }
    } catch (e) {
      return next(e);
    }
    return next();
  };
};

const handleAccessDashboardPerm = (): RequestHandler => {
  return async (req, res, next) => {
    const { user, params } = req;

    if (!user) {
      return res.NotPermitted();
    }

    try {
      const hasPerm = await InitiativePermissions.canAccess(user, params.initiativeId);
      if (!hasPerm) {
        return next(new PermissionDeniedError());
      }

      const dashboard = await InsightDashboard.findById(params.dashboardId).orFail().exec();

      res.locals.initiativeId = params.initiativeId;
      res.locals.dashboard = dashboard;

      if (dashboard.initiativeId.toString() === params.initiativeId) {
        return next();
      }

      if (!dashboard.filters.shareWithSubsidiaries?.enabled) {
        return next(new PermissionDeniedError());
      }

      const [initiative] = await InitiativeRepository.getAllParentsById(params.initiativeId);

      const dashboardParentInitiative = (initiative?.parents ?? []).find((parent) =>
        parent._id.equals(dashboard.initiativeId)
      );

      if (!dashboardParentInitiative) {
        return next(new PermissionDeniedError());
      }
    } catch (e) {
      return next(e);
    }
    return next();
  };
};

export const canManageSummaryDashboard: RequestHandler = async (req, res, next) => {
  const { params, user } = req;
  try {
    if (!user) {
      return res.NotPermitted();
    }
    const type = req.params.dashboard;
    if (!isStaticDashboardType(type)) {
      return next(
        new UserError('Unsupported dashboard', { initiativeId: params.initiativeId, type: params.dashboard })
      );
    }

    const initiativeId = new ObjectId(req.params.initiativeId);
    const creatorId = user._id;
    res.locals.dashboard = await summaryDashboardService.findOrCreate({ initiativeId, creatorId, type });
  } catch (e) {
    next(e);
  }
  return next();
};

export const canManageDashboard: RequestHandler = handlePerm(InitiativePermissions.canManageInitiative);

export const canAccessDashboard: RequestHandler = handleAccessDashboardPerm();
