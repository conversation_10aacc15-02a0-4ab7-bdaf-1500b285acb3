import { RequestHandler } from 'express';
import PermissionDeniedError from '../error/PermissionDeniedError';
import { InitiativePermissions } from '../service/initiative/InitiativePermissions';
import BackgroundJob, { BackgroundJobModel } from '../models/backgroundJob';
import { ObjectId } from 'bson';

export const canManageBackgroundJob: RequestHandler = async (req, res, next) => {
  const { user, params } = req;

  if (!user) {
    return next(new PermissionDeniedError());
  }

  const job = await BackgroundJob.findOne<BackgroundJobModel>({
    _id: new ObjectId(params.id),
  })
    .orFail()
    .exec();

  const canManageInitiative = await InitiativePermissions.canManageInitiative(user, job.initiativeId ?? '');
  if (!canManageInitiative) {
    return next(new PermissionDeniedError());
  }
  res.locals.job = job;
  return next();
};
