#!/usr/bin/env sh

# Deploy to GCP app engine, https://cloud.google.com/appengine/
#
set -e

gray="\\e[37m"
blue="\\e[36m"
red="\\e[31m"
green="\\e[32m"
reset="\\e[0m"

info() { echo "${blue}INFO: $*${reset}"; }
error() { echo "${red}ERROR: $*${reset}"; }
debug() {
    if [[ "${DEBUG}" == "true" ]]; then
        echo "${gray}DEBUG: $*${reset}";
    fi
}
success() { echo "${green}✔ $*${reset}"; }
fail() {
  echo "${red}✖ $*${reset}"
  exit 1
}

# Execute a command, saving its output and exit status code, and echoing its output upon completion.
# Globals set:
#   status: Exit status of the command that was executed.
#
run() {
  echo "$@"
  set +e
  eval "$@" 2>&1
  status=$?
  set -e
}

info "Setting up environment".
if [ -z "$KEY_FILE" ]; then
  fail "Missing required KEY_FILE environment variable"
  exit 1
fi

echo "${KEY_FILE}" | base64 -d >> /tmp/key-file.json
run export GOOGLE_APPLICATION_CREDENTIALS=/tmp/key-file.json
run cp ./deploy/.initial_npmrc ~/.npmrc
run npm run artifactregistry-login

run npm install
success "Completed setup"
