#!/bin/bash

if [ -z "$GCP_GLO<PERSON>L_KEY_FILE_BASE64" ]; then
  echo "Missing required GCP_GLOBAL_KEY_FILE_BASE64 environment variable"
  exit 1
fi

if [ -z "$2" ]; then
  echo "Usage: ./deploy/push-image.sh <BITBUCKET_BRANCH> <BITBUCKET_BUILD_NUMBER>"
  exit
fi

# Fail on any non 0 exits
set -ex

BITBUCKET_BRANCH=${1/\//_}
BUILD_NUM=$2

PROJECT=${SENTRY_PROJECT:-"express-g17eco-api"}
ORG=${SENTRY_ORG:-"world-wide-generation"}

#${parameter//pattern/string}
CLEAN_NAME=${1//release\//""}
CLEAN_VERSION=${CLEAN_NAME/\//""}
export SENTRY_RELEASE="g17eco-api@$CLEAN_VERSION"

echo $GCP_GLOBAL_KEY_FILE_BASE64 | base64 -d >> /tmp/key-file.json
export GOOGLE_APPLICATION_CREDENTIALS="/tmp/key-file.json"
cp ./deploy/.initial_npmrc ~/.npmrc

npm run artifactregistry-login
npm install
npm run build-ts
npm ci --omit=dev # Re-create production only

npx sentry-cli releases new --org $ORG --project $PROJECT $SENTRY_RELEASE
npx sentry-cli sourcemaps inject --org $ORG --project $PROJECT --release $SENTRY_RELEASE ./dist
npx sentry-cli sourcemaps upload --org $ORG --project $PROJECT --release $SENTRY_RELEASE --dist $BUILD_NUM ./dist

cat /tmp/key-file.json | docker login -u _json_key --password-stdin https://europe-west2-docker.pkg.dev

IMAGE="europe-west2-docker.pkg.dev/g17-eco/docker/api"

# Cannot use --secret in DOCKER_BUILDKIT because not supported by Bibucket Pipelines as of 2022-02-04.
# @TODO: Check again later
# DOCKER_BUILDKIT=1 docker build . -t $IMAGE --platform linux/amd64 --secret id=npmrc,src=$HOME/.npmrc

docker build . -t $IMAGE:$BITBUCKET_BRANCH \
--platform linux/amd64 \
--build-arg SENTRY_RELEASE=${SENTRY_RELEASE} \
--build-arg BUILD_NUM=${BUILD_NUM}

docker image tag $IMAGE:$BITBUCKET_BRANCH $IMAGE:$BITBUCKET_BRANCH-$BUILD_NUM
docker image push $IMAGE:$BITBUCKET_BRANCH
docker image push $IMAGE:$BITBUCKET_BRANCH-$BUILD_NUM

npx sentry-cli releases finalize $SENTRY_RELEASE
