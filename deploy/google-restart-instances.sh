#!/bin/bash

if [ -z "$GCP_GLO<PERSON><PERSON>_KEY_FILE_BASE64" ]; then
  echo "Missing required GCP_GLOBAL_KEY_FILE_BASE64 environment variable"
  exit 1
fi

if [ -z "$3" ]; then
  echo "Usage: ./deploy/google-restart-instances.sh <PROJECT> <INSTANCE_GROUP_ID> <REGION>"
  exit
fi

PROJECT=$1
INSTANCE_GROUP_ID=$2
REGION=$3

set -ex

echo $GCP_GLOBAL_KEY_FILE_BASE64 | base64 -d >> /tmp/key-file.json
gcloud auth activate-service-account --key-file=/tmp/key-file.json

gcloud compute instance-groups managed rolling-action \
            replace $INSTANCE_GROUP_ID \
            --project $PROJECT \
            --replacement-method=substitute \
            --region=$REGION \
            --max-surge=3 \
            --max-unavailable=0 \
