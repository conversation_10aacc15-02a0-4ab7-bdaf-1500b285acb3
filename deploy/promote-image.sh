#!/bin/bash

set -ex

if [ -z "$GCP_GLOBAL_KEY_FILE_BASE64" ]; then
  echo "Missing required GCP_GLOBAL_KEY_FILE_BASE64 environment variable"
  exit 1
fi

if [ -z "$2" ]; then
  echo "Usage: ./deploy/promote-image.sh <TAG_SOURCE> <TAG_TARGET>"
  exit 1
fi


IMAGE="europe-west2-docker.pkg.dev/g17-eco/docker/api"
TAG_SOURCE=${1/\//_}
TAG_TARGET=$2

#${parameter//pattern/string}
CLEAN_NAME=${1//release\//""}
CLEAN_VERSION=${CLEAN_NAME/\//""}
SENTRY_RELEASE="g17eco-api@$CLEAN_VERSION"
PROJECT=${SENTRY_PROJECT:-"express-g17eco-api"}
ORG=${SENTRY_ORG:-"world-wide-generation"}

echo $GCP_GLOBAL_KEY_FILE_BASE64 | base64 -d > /tmp/key-file.json
gcloud auth activate-service-account --key-file=/tmp/key-file.json

gcloud artifacts docker tags add $IMAGE:$TAG_SOURCE $IMAGE:$TAG_TARGET

curl https://sentry.io/api/0/organizations/$ORG/releases/$SENTRY_RELEASE/deploys/ \
  -X POST \
  -H "Authorization: Bearer ${SENTRY_AUTH_TOKEN}" \
  -H "Content-Type: application/json" \
  -d "{ \"environment\": \"${TAG_TARGET}\" }"
