#!/bin/bash
#
# Copyright (c) 2023. World Wide Generation Ltd
#
set -x
apt-get update && apt-get install curl gnupg coreutils -y
curl https://keybase.io/codecovsecurity/pgp_keys.asc | gpg --no-default-keyring --keyring trustedkeys.gpg --import # One-time step

curl -Os https://uploader.codecov.io/latest/linux/codecov
curl -Os https://uploader.codecov.io/latest/linux/codecov.SHA256SUM
curl -Os https://uploader.codecov.io/latest/linux/codecov.SHA256SUM.sig

gpgv codecov.SHA256SUM.sig codecov.SHA256SUM
sha256sum -c codecov.SHA256SUM

chmod +x codecov
./codecov -t ${CODECOV_TOKEN}
