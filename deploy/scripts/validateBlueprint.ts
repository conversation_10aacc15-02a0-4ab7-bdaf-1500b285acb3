#!/usr/bin/env node
import axios from 'axios';
import config from '../../server/config';
import { getBlueprintRepository } from '../../server/repository/BlueprintRepository';
import { activeBlueprints } from '../../server/survey/blueprints';
import { extractUtrCodes, extractVariables } from '../../server/survey/surveyForms';

const host = process.argv[2];

const fail = () => process.exit(1);
const success = () => process.exit(0);

const blueprintRepo = getBlueprintRepository();

let failure = false;

Promise.all(
  activeBlueprints.map(blueprintCode => {
    return blueprintRepo.mustFindExpandedByCode(blueprintCode)
      .then((blueprint) => {
        console.log(`Started checking ${blueprintCode}`);
        const utrCodes = extractUtrCodes(blueprint);
        const variables = extractVariables(blueprint);
        return axios.post(
          `${host}/api/inbound/deployment/pre-validation`,
          { utrCodes, variables },
          {
            headers: {
              authorization: config.deployment.authorizationToken
            }
          });
      })
      .then(({ data }) => {
        if (data.data) {
          console.log(`FAILURE: Missing utr codes in database for ${blueprintCode}`);
          console.log(data.data);
          return failure = true;
        }
        console.log(`SUCCESS: All utr codes are in the database for ${blueprintCode}`);
      })
      .catch((e) => {
        console.log(`FAILURE: Could not check UTRs in ${host} for ${blueprintCode}`);
        console.log(e);
        return failure = true;
      })
  })
)
  .then(() => {
    if (failure) {
      console.log(`============= [FAILURE] =============`);
      fail();
    } else {
      success();
    }
  });
