// @ts-check

import unusedImports from 'eslint-plugin-unused-imports';
import importPlugin from 'eslint-plugin-import';
import globals from 'globals';
import eslintJs from '@eslint/js';
import eslintTs from 'typescript-eslint';
import oxlint from 'eslint-plugin-oxlint';
import { fileURLToPath } from 'node:url';
import path from 'node:path';

// Add the files for applying the recommended TypeScript configs
// only for the Typescript files.
// This is necessary when we have the multiple extensions files
// (e.g. .ts, .tsx, .js, .cjs, .mjs, etc.).
const tsFiles = ['{server,tests}/**/*.ts'];
const defaultFiles = ['**/*.{js,mjs,cjs,ts}'];
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const configArray = eslintTs.config(
  {
    name: 'Apply global ignore',
    // https://eslint.org/docs/latest/use/configure/ignore
    //  if an ignores key is used without any other keys in the configuration object, then the patterns act as global ignores
    ignores: [
      // only ignore node_modules in the same directory
      // as the configuration file
      'node_modules',
      'deploy',
      // so you have to add `**/` pattern to include nested directories
      // for example, if you use pnpm workspace
      '**/node_modules',
      '**/dist',
      '**/coverage',
      '**/migrations/',
      '**/nyc.config.js',
      '**/.DS_Store',
      '**/.bitbucket',
      '.editorconfig',
      '.eslintrc.js',
      '.env',
      '.env.*',
      '.gitignore',
    ],
  },
  {
    name: 'Apply global settings and options',
    languageOptions: {
      globals: {
        ...globals.node,
      },
    },
    settings: {
      'import/parsers': {
        '@typescript-eslint/parser': ['.ts'],
      },
      'import/extensions': ['.js', '.ts'],
      'import/resolver': {
        typescript: true,
        node: true
      },
    },
  },
  {
    name: 'Default ESLint Recommended',
    ...eslintJs.configs.recommended,
  },
  ...eslintTs.configs.recommended.map((config) => ({
    ...config,
    files: tsFiles,
  })),
  ...eslintTs.configs.recommendedTypeChecked.map((config) => ({
    ...config,
    files: tsFiles,
  })),
  {
    files: ['**/*.js'],
    extends: [eslintTs.configs.disableTypeChecked],
  },
  {
    files: ['**/*.spec.ts', 'tests/**/*'],
    extends: [eslintTs.configs.disableTypeChecked],
  },
  {
    name: 'Main ESLint config',
    files: defaultFiles,
    plugins: {
      '@typescript-eslint': eslintTs.plugin,
      'unused-imports': unusedImports,
    },
    extends: [importPlugin.flatConfigs.recommended, importPlugin.flatConfigs.typescript],

    languageOptions: {
      globals: {
        ...globals.node,
      },
      parser: eslintTs.parser,
      ecmaVersion: 2023,
      sourceType: 'module',
      parserOptions: {
        projectService: true,
        project: './tsconfig.json',
        tsconfigRootDir: __dirname,
      },
    },
    rules: {
      // API rules
      '@typescript-eslint/no-explicit-any': 0,
      '@typescript-eslint/explicit-module-boundary-types': 0,
      '@typescript-eslint/no-unused-vars': 0,
      '@typescript-eslint/no-empty-function': 0,
      '@typescript-eslint/no-inferrable-types': 0,
      '@typescript-eslint/no-var-requires': 'warn',
      '@typescript-eslint/no-unsafe-return': 0,

      // Seems like there is no problem here at all. Very strict
      '@typescript-eslint/no-unsafe-enum-comparison': 0,
      '@typescript-eslint/require-await': 0,

      // Type related warnings and errors we don't want to deal with right now
      '@typescript-eslint/no-unsafe-member-access': 0,
      '@typescript-eslint/restrict-template-expressions': 0,
      '@typescript-eslint/consistent-type-definitions': 0,
      '@typescript-eslint/consistent-type-assertions': 0,
      '@typescript-eslint/consistent-indexed-object-style': 0,
      '@typescript-eslint/consistent-generic-constructor': 0,
      '@typescript-eslint/no-for-in-array': 0,
      '@typescript-eslint/no-empty-object-type': 0,

      // This seems to have bugs with declarations https://github.com/microsoft/TypeScript/issues/38347
      '@typescript-eslint/no-base-to-string': 0,

      // Should fix these as some point
      '@typescript-eslint/no-unsafe-assignment': 'warn',
      '@typescript-eslint/no-unsafe-argument': 'warn',
      '@typescript-eslint/no-unnecessary-type-assertion': 'warn',
      '@typescript-eslint/no-unsafe-call': 'warn',
      '@typescript-eslint/no-redundant-type-constituents': 'warn',
      '@typescript-eslint/restrict-plus-operands': 'warn',
      '@typescript-eslint/await-thenable': 'warn',
      '@typescript-eslint/unbound-method': 'warn',

      '@typescript-eslint/no-floating-promises': ['warn', { ignoreVoid: true }],

      '@typescript-eslint/no-misused-promises': [
        'error',
        {
          checksConditionals: true,
          checksVoidReturn: false,
        },
      ],

      'import/no-restricted-paths': [
        'warn',
        {
          zones: [
            {
              target: './server/models/**/*',
              from: ['./server/service', './server/repository'],
              message: 'Models should not import service and repository.',
            },
          ],
        },
      ],

      'no-restricted-syntax': [
        'warn',
        {
          selector: "NewExpression[callee.name='Error']",
          message: 'Use ContextError() or UserError() instead of Error().',
        },
      ],

      'no-restricted-imports': [
        'warn',
        {
          patterns: [
            {
              group: ['**/SurveyUsers'],
              importNames: ['SurveyUserRoles'],
              message: "Import from 'server/types/roles' instead to avoid circular dependencies.",
            },
            {
              group: ['**/unitTypes'],

              importNames: ['SupportedMeasureUnits', 'NumberScale', 'Unit', 'validUnitTypes', 'UnitConfig'],

              message: "Import from 'server/types/units' instead to avoid circular dependencies.",
            },
            {
              group: ['**/utr/constants'],
              importNames: ['ActionList', 'ActionMap', 'UtrvType', 'DataPeriods'],
              message: "Import from 'server/types/constants' instead to avoid circular dependencies.",
            },
          ],

          paths: [
            {
              name: 'moment',
              message:
                'Direct usage of "moment" is deprecated. Please use the utility functions in `server/util/date.ts` instead.',
            },
            {
              name: 'dayjs',
              message:
                'Direct usage of "dayjs" is deprecated. Please use the utility functions in `server/util/date.ts` instead.',
            },
          ],
        },
      ],

      '@typescript-eslint/consistent-type-imports': [
        'warn',
        {
          prefer: 'type-imports',
          disallowTypeAnnotations: true,
        },
      ],

      'prefer-const': 'error',
      '@typescript-eslint/ban-types': 0,
      'no-useless-escape': 0,
      'unused-imports/no-unused-imports': 'error',

      'max-params': 'off',
      '@typescript-eslint/max-params': ['warn', { max: 5 }],

      'import/no-named-as-default-member': 0,
      'import/no-named-as-default': 0,
      'no-case-declarations': 'warn',
      'no-extra-boolean-cast': 'warn',
      'no-prototype-builtins': 'warn',

      quotes: ['warn', 'single'],
      'no-extra-semi': 'warn',
      'import/no-unresolved': 'warn',
    },
  },
  // other plugins
  ...oxlint.configs['flat/recommended'] // oxlint should be the last one
);

export default configArray;