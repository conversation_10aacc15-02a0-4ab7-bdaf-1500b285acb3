/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import UniversalTrackerValue, {
  UniversalTrackerValueModel,
  UniversalTrackerValuePlain,
} from '../models/universalTrackerValue';
import { userMinFields, userPlain, UserPlain } from '../models/user';
import { ObjectId } from 'bson';
import { addDocumentUrl } from '../service/storage/fileStorage';
import {
  actionProjection,
  initiativeMinFields,
  surveyBundleData,
  metricUnitProjection,
  universalTrackerFields,
  universalTrackerValuePlainFields,
  valueValidationProjection,
  connectionUtrvFields,
  ConnectionUtrv,
  universalTrackerPlainFields,
} from './projections';
import {
  documentsLookup,
  universalTrackerLookup,
  userStakeholderAndHistoryLookup
} from './utrvAggregations';
import UniversalTracker, {
  UniversalTrackerModel,
  UniversalTrackerPlain,
} from '../models/universalTracker';
import { ReportingFrameworkPlain } from '../models/reportingFramework';
import { MetricUnitPlain } from '../models/metricUnit';
import { InitiativePlain } from "../models/initiative"
import { SurveyModelPlain, SurveyType } from "../models/survey"
import { UniversalTrackerValueAssurancePlain } from "../service/assurance/model/Assurance"
import { utrvAssuranceLookUp } from './aggregations';
import { ActionList, DataPeriods } from "../service/utr/constants";
import { PipelineStage, ProjectionType } from 'mongoose';
import { ValueList } from '../models/public/valueList';
import type { DisaggregationUniversalTrackerValueFields } from "../service/utr/aggregation/AggregatedUniversalTracker";
import type { KeysEnum } from "../models/public/projectionUtils";
import { getBlueprintRepository } from "./BlueprintRepository";
import { DefaultBlueprintCode } from "../survey/blueprints";
import { extractVisibleUtrCodes } from "../survey/surveyForms";
import { UtrMatchFilters } from "../types/universalTracker";

interface UtrvHistoryInfoInterface extends UniversalTrackerValuePlain {
  documents: any;
  surveyType?: SurveyType;
  users: UserPlain;
}

export interface BundleData extends UniversalTrackerValuePlain {
  universalTracker: UniversalTrackerPlain,
  users: UserPlain[],
  reportingFrameworks?: ReportingFrameworkPlain[],
  universalTrackerValueAssurances: UniversalTrackerValueAssurancePlain[],
  metricUnit?: MetricUnitPlain,
  initiative?: InitiativePlain,
  survey?: SurveyModelPlain,
  [key: string]: unknown,
}

export interface SurveyUtrvs {
  _id: ObjectId;
  universalTracker: UniversalTrackerPlain;
  utrvs: DisaggregationUniversalTrackerValueFields[];
}

export type Projection =
  { [k in keyof UniversalTrackerPlain]?: 1 }
  & { code: 1 };

interface LatestQuery extends Pick<UniversalTrackerValuePlain, 'type' | 'initiativeId' | 'period'> {
  statuses?: ActionList[],
  utrIds: ObjectId[];
}

export class UniversalTrackerRepository {

  /** @todo: [EvidenceData] this needs to switch to use the new property evidenceData */
  public static async getHistoryInfoForIds(
    ids: (string | ObjectId)[],
    options = { utr: true }
  ): Promise<UtrvHistoryInfoInterface[]> {

    const objectIds = ids.map(i => new ObjectId(i));

    const aggregations: any[] = [
      {
        $match: { _id: { $in: objectIds } }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'history.userId',
          foreignField: '_id',
          as: 'users'
        }
      },
      {
        $lookup: {
          from: 'document',
          localField: 'history.evidence',
          foreignField: '_id',
          as: 'documents'
        }
      },
      {
        $lookup: {
          from: 'surveys',
          localField: 'compositeData.surveyId',
          foreignField: '_id',
          as: 'survey'
        }
      },
    ];

    let universalTrackerProjection: object = {};
    if (options.utr) {
      aggregations.push(
        {
          $lookup: {
            from: 'universal-trackers',
            localField: 'universalTrackerId',
            foreignField: '_id',
            as: 'universalTracker'
          }
        },
        {
          $lookup: {
            from: 'value-list',
            localField: 'universalTracker.valueValidation.valueList.listId',
            foreignField: '_id',
            as: 'valueList'
          }
        }
      );

      universalTrackerProjection = {
        universalTracker: {
          ...universalTrackerFields,
          ...valueValidationProjection,
          ...metricUnitProjection,
        }
      };
    }

    aggregations.push(
      {
        $project: {
          ...actionProjection,
          compositeData: 1, // Needed for permission survey check
          sourceType: 1, // Need for history update action (only fragments)
          users: userMinFields,
          documents: 1,
          history: 1,
          sourceItems: 1,
          surveyType: { $arrayElemAt: ['$survey.type', 0] },
          ...universalTrackerProjection
        }
      },
    );

    return UniversalTrackerValue.aggregate(aggregations).then(addDocumentUrl);
  }

  public static async getBundleData(utrvIds: ObjectId[]): Promise<BundleData[]> {
    const aggregation: PipelineStage[] = [
      {
        $match: {
          _id: { $in: utrvIds }
        }
      },
      universalTrackerLookup,
      ...userStakeholderAndHistoryLookup,
      documentsLookup,
      {
        $lookup: {
          from: 'surveys',
          localField: 'compositeData.surveyId',
          foreignField: '_id',
          as: 'survey'
        }
      },
      {
        $project: {
          ...universalTrackerValuePlainFields,
          universalTracker: { $arrayElemAt: ['$universalTracker', 0] },
          survey: { $arrayElemAt: ['$survey', 0] },
          users: userPlain,
          documents: 1,
        }
      },
      {
        $lookup: {
          from: 'initiatives',
          localField: 'survey.initiativeId',
          foreignField: '_id',
          as: 'initiatives'
        }
      },
      {
        $project: {
          ...universalTrackerValuePlainFields,
          universalTracker: 1,
          survey: 1,
          initiative: { $arrayElemAt: ['$initiatives', 0] },
          users: userPlain,
          documents: 1,
        }
      },
      {
        $project: {
          ...universalTrackerValuePlainFields,
          universalTracker: 1,
          initiative: initiativeMinFields,
          survey: surveyBundleData,
          users: userPlain,
          documents: 1,
          typeCode: {
            $toUpper: {
              $concat: ['$universalTracker.type', '/', '$universalTracker.typeCode']
            }
          }
        }
      },
      {
        $lookup: {
          from: 'reporting-frameworks',
          localField: 'typeCode',
          foreignField: 'items.itemCode',
          as: 'reportingFrameworks'
        }
      },
      {
        $lookup: {
          from: 'value-list',
          localField: 'universalTracker.valueValidation.valueList.listId',
          foreignField: '_id',
          as: 'list'
        }
      },
      utrvAssuranceLookUp('_id', 'utrvId'),
      {
        $project: {
          ...universalTrackerValuePlainFields,
          users: userPlain,
          documents: 1,
          reportingFrameworks: 1,
          initiative: initiativeMinFields,
          survey: surveyBundleData,
          universalTrackerValueAssurances: 1,
          universalTracker: {
            alternatives: 1,
            tags: 1,
            ...universalTrackerFields,
            ...valueValidationProjection,
            ...metricUnitProjection,
          },
        }
      }
    ];

    return UniversalTrackerValue.aggregate(aggregation).then(addDocumentUrl);
  }

  public static async getLatestUtrvs(query: LatestQuery): Promise<ConnectionUtrv[]> {
    const { initiativeId, type, period, utrIds, statuses = [ActionList.Verified] } = query;

    const resolvedPeriod = { period: period || DataPeriods.Yearly };

    const aggregation: PipelineStage[] = [
      {
        $match: {
          initiativeId,
          universalTrackerId: { $in: utrIds },
          deletedDate: { $exists: false },
          status: { $in: statuses },
          type,
          ...resolvedPeriod,
        }
      },
      { $sort: { effectiveDate: -1 } },
      {
        $group: {
          _id: "$universalTrackerId",
          utrv: { $first: "$$ROOT" },
        }
      },
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore Not updated type on mongoose
      { $replaceWith: "$utrv" },
      { $project: connectionUtrvFields },
    ];

    return UniversalTrackerValue.aggregate(aggregation)
  }

  public static async getUtrvs<T = UniversalTrackerValuePlain>(surveyId: ObjectId, utrIds: ObjectId[], projection?: ProjectionType<UniversalTrackerValuePlain>): Promise<T[]> {
    return UniversalTrackerValue.find({
      'compositeData.surveyId': surveyId,
      universalTrackerId: { $in: utrIds },
      deletedDate: { $exists: false },
    }, projection);
  }

  public static async getSurveyUtrvs({
    surveyIds,
    utrIds,
    statuses,
  }: {
    surveyIds: ObjectId[];
    utrIds: ObjectId[];
    statuses: ActionList[];
  }): Promise<SurveyUtrvs[]> {

    const aggregation: PipelineStage[] = [
      {
        $match: {
          'compositeData.surveyId': { $in: surveyIds },
          universalTrackerId: { $in: utrIds },
          deletedDate: { $exists: false },
          status: { $in: statuses },
        }
      },
      {
        $project: {
          _id: 1,
          initiativeId: 1,
          assuranceStatus: 1,
          effectiveDate: 1,
          period: 1,
          type: 1,
          universalTrackerId: 1,
          value: 1,
          valueData: 1,
          status: 1,
          history: {
            $sortArray: {
              input: '$history',
              sortBy: {
                // We sort backwards to get the latest history first
                date: -1
              },
            }
          },
        }
      },
      {
        $addFields: {
          // Actually updated, not latest...
          updatedHistory: {
            $arrayElemAt: [
              {
                $filter: {
                  input: '$history',
                  cond: { $eq: [ActionList.Updated, '$$this.action'] },
                },
              },
              0,
            ],
          },
          // As history is now sorted DESC, we can just take the first element
          latestHistory: { $first: '$history' },
        } satisfies Partial<KeysEnum<DisaggregationUniversalTrackerValueFields, Record<string, unknown>>>,
      },
      {
        $project: {
          _id: 1,
          initiativeId: 1,
          assuranceStatus: 1,
          effectiveDate: 1,
          period: 1,
          type: 1,
          universalTrackerId: 1,
          value: 1,
          valueData: 1,
          status: 1,
          latestHistory: 1,
          updatedHistory: 1,
          sourceItems: 1,
        } satisfies KeysEnum<DisaggregationUniversalTrackerValueFields>,
      },
      {
        $group: {
          _id: "$universalTrackerId",
          utrvs: { $push: "$$ROOT" },
        }
      },
      {
        $lookup: {
          from: 'universal-trackers',
          localField: '_id',
          foreignField: '_id',
          as: 'universalTracker'
        }
      },
      {
        $project: {
          utrvs: 1,
          universalTracker: {
            $arrayElemAt: ['$universalTracker', 0]
          },
        }
      },
    ];

    return UniversalTrackerValue.aggregate(aggregation);
  }

  public static async findUtrvById(_id: ObjectId | string): Promise<UniversalTrackerValueModel | null> {
    return UniversalTrackerValue.findById(_id);
  }

  public static async find(conditions: any, projection?: ProjectionType<UniversalTrackerPlain>, options?: any): Promise<UniversalTrackerModel[]> {
    return UniversalTracker.find(conditions, projection, options).exec() as unknown as Promise<UniversalTrackerModel[]>;
  }

  public static async findById(id: ObjectId, projection?: ProjectionType<UniversalTrackerPlain>): Promise<UniversalTrackerModel | null> {
    return UniversalTracker.findById(id, projection).exec();
  }

  public static async findByCodesWithValueList(codes: string[], projection?: ProjectionType<UniversalTrackerPlain>): Promise<UniversalTrackerModelWithValueList[]> {
    return UniversalTracker
      .find(
        {
          code: { $in: codes }
        },
        projection)
      .populate<Pick<UniversalTrackerModelWithValueList,'valueListOptions'>>('valueListOptions')
      .lean().exec() as Promise<UniversalTrackerModelWithValueList[]>;
  }

  public static async findByCodes(codes: string[], projection: Projection = { code: 1 }) {
    return UniversalTracker.find({ code: { $in: codes } }, projection).lean().exec() as Promise<UniversalTrackerPlain[]>;
  }

  public static async populateValueValidationByCodes(codes: string[]) {
    const aggregation: PipelineStage[] = [
      {
        $match: {
          code: { $in: codes }
        }
      },
      {
        $lookup: {
          from: 'value-list',
          localField: 'valueValidation.valueList.listId',
          foreignField: '_id',
          as: 'valueList'
        }
      },
      {
        $project: {
          ...universalTrackerPlainFields,
          ...valueValidationProjection,
        }
      }
    ];
    return UniversalTracker.aggregate<UniversalTrackerPlain>(aggregation);
  }

  public static async getBlueprintUtrs<T extends Partial<UniversalTrackerPlain> & {
    _id: ObjectId
  } = UniversalTrackerPlain>(
    utrFilter: UtrMatchFilters,
    projection?: ProjectionType<UniversalTrackerPlain>,
    blueprintCode = DefaultBlueprintCode,
  ) {
    const blueprint = await getBlueprintRepository().mustFindExpandedByCode(blueprintCode);
    return UniversalTracker.find({
      code: { $in: extractVisibleUtrCodes(blueprint) },
      ...utrFilter
    }, projection)
      .lean<T[]>()
      .exec()
  }
}

interface UniversalTrackerModelWithValueList extends UniversalTrackerPlain {
  valueListOptions: ValueList | null;
}
