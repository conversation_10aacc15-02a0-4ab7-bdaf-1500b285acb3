/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import { FileMetadata } from '../utr/model/actionRequest';
import EvidenceUploader, { createEvidenceUploader, UploadRequest } from './EvidenceUploader';
import TrackerEvidence from './TrackerEvidence';
import { DocumentModel, DocumentOwnerType } from '../../models/document';

export default class UniversalTrackerEvidence extends TrackerEvidence {
  constructor(uploader: EvidenceUploader) {
    super(uploader, 'universal_tracker_value');
  }

  public async upload({
    utrvId,
    initiativeId,
    userId,
    files,
    filesDescriptions,
    filesMetadata,
  }: {
    utrvId: string;
    initiativeId: string;
    userId: ObjectId;
    files: any;
    filesDescriptions?: string[]; // merge
    filesMetadata?: FileMetadata[];
  }) {
    if (!Array.isArray(files)) {
      return [];
    }

    const requests: Promise<DocumentModel>[] = [];

    files.forEach((file, index) => {
      const saveToLibrary = filesMetadata?.[index]?.saveToLibrary;
      // Disallow saving description to document if it is library document
      const rawDescription = filesMetadata?.[index].description ?? filesDescriptions?.[index] ?? ''
      const fileDescription = saveToLibrary ? '' : rawDescription;
      const uploadRequest: Omit<UploadRequest, 'data' | 'filesDescriptions'> = {
        userId,
        type: saveToLibrary ? DocumentOwnerType.Initiative : this.type,
        ownerId: saveToLibrary ? initiativeId : utrvId,
        ownerType: saveToLibrary ? DocumentOwnerType.Initiative : DocumentOwnerType.UniversalTrackerValue,
      };
      requests.push(this.uploader.uploadFile(file, uploadRequest, fileDescription));
    });

    return Promise.all(requests);
  }
}

export const createUniversalTrackerEvidence = () => new UniversalTrackerEvidence(createEvidenceUploader());
