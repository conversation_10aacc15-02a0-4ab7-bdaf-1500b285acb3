/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { <PERSON><PERSON> } from 'winston';
import { wwgLogger } from '../wwgLogger';
import { ClaudeA<PERSON>, getClaudeAI } from './models/ClaudeAI';
import { ChatGPT, getChatGPT } from './models/ChatGPT';
import { AIModel } from './models/AIModel';

let instance: AIModelFactory | undefined;

export enum AIModelType {
  ChatGPT = 'chatgpt',
  Claude = 'claude',
}

export class AIModelFactory {
  constructor(
    protected logger: Logger,
    private aiService: {
      chatgpt: ChatGPT,
      claude: ClaudeAI,
    }
  ) {}

  /** @deprecated Use getAiModel instead */
  getModel(model: AIModelType) {
    switch (model) {
      case AIModelType.Claude:
        return this.aiService.claude;
      case AIModelType.ChatGPT:
      default:
        return this.aiService.chatgpt;
    }
  }

  getAiModel(model: AIModelType): AIModel {
    return this.getModel(model) as AIModel;
  }
}

export const getAIModelFactory = () => {
  if (!instance) {
    instance = new AIModelFactory(
      wwgLogger,
      {
        chatgpt: getChatGPT(),
        claude: getClaudeAI(),
      }
    );
  }
  return instance;
};
