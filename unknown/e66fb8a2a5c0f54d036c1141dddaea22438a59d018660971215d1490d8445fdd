/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { ResponseFormat } from '../../../routes/validation-schemas/ai-response-formats/ai-assistant';

export type AIPrompt = {
  role: "user" | "system",
  content: string;
};

export type AIResponse<T = string> = {
  content: T;
  usage?: {
    total_tokens: number;
    prompt_tokens?: number;
    completion_tokens?: number;
  }
}

export interface AIModel {
  parseCompletion<T = any>(messages: AIPrompt[], maxTokens?: number, responseFormat?: ResponseFormat): Promise<AIResponse<T>>;
  runCompletion(messages: AIPrompt[], maxTokens?: number): Promise<AIResponse>;
  getModelVersion(): string;
}
