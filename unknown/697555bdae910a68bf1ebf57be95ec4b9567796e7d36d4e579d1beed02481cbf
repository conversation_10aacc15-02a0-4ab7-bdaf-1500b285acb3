import { Schema, model } from 'mongoose';
import { ObjectId } from 'bson';

export interface RelevantUtr {
  code: string;
  score: number; // 0-1, 1 is most relevant
}

export interface DocumentUtrMappingPlain {
  documentId: ObjectId;
  initiativeId: ObjectId;
  utrs: RelevantUtr[];
  created: Date;
  updated: Date;
}

const UtrSchema = new Schema<RelevantUtr>({
  code: { type: Schema.Types.String, required: true },
  score: { type: Schema.Types.Number, required: true },
});

const DocumentUtrMappingSchema = new Schema<DocumentUtrMappingPlain>(
  {
    documentId: { type: Schema.Types.ObjectId, ref: 'Document', required: true },
    initiativeId: { type: Schema.Types.ObjectId, ref: 'Initiative', required: true },
    utrs: { type: [UtrSchema], required: true },
  },
  {
    timestamps: { createdAt: 'created', updatedAt: 'updated' },
  }
);

DocumentUtrMappingSchema.index({ documentId: 1 }, { unique: true });
DocumentUtrMappingSchema.index({ 'utrs.code': 1 });

export const DocumentUtrMapping = model<DocumentUtrMappingPlain>(
  'DocumentUtrMapping',
  DocumentUtrMappingSchema,
  'document-universal-tracker-mappings'
);
