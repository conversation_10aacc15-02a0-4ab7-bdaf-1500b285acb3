import { LinkDocument } from '../../server/models/document';
import { ExistingEvidence, FileMetadata } from '../../server/service/utr/model/actionRequest';
import { ObjectId } from 'bson';

export const existingEvidenceOneId = new ObjectId('60d21b4667d0d8992e610005');
export const existingEvidenceOne: ExistingEvidence = {
  _id: existingEvidenceOneId.toString(),
  description: 'Existing Evidence',
};

export const links: LinkDocument[] = [{ link: 'https://example.com', title: 'Example Link' }];

export const files = [{ originalname: 'file1.pdf' }, { originalname: 'file2.pdf' }] as Express.Multer.File[];

export const filesDescriptions = ['File 1 Description', 'File 2 Description'];
export const filesMetadata: FileMetadata[] = [
  { id: 0, saveToLibrary: false, description: '' },
  { id: 1, saveToLibrary: true, description: '' },
];

export const documentLinkOneId = new ObjectId('60d21b4667d0d8992e610003');
export const documentLinkOne = { _id: documentLinkOneId, description: 'Link 1' };

export const documentFileOneId = new ObjectId('60d21b4667d0d8992e610001');
export const documentFileOne = { _id: documentFileOneId, description: 'File 1' };

export const documentFileTwoId = new ObjectId('60d21b4667d0d8992e610002');
export const documentFileTwo = { _id: documentFileTwoId, description: 'File 2' };
