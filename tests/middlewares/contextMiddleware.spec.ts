/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { expect } from 'chai';
import sinon, { createSandbox } from 'sinon';
import { Request, Response } from 'express';
import {
  fromRequest,
  getContext,
  tryGetContext,
  ContextMiddleware,
  operationFromRequest,
  asyncRequestContext,
} from '../../server/middleware/audit/contextMiddleware';
import { AuthenticatedRequest } from '../../server/http/AuthRouter';
import { userOne } from '../fixtures/userFixtures';
import { Operation } from '../../server/service/audit/AuditModels';
import ContextError from '../../server/error/ContextError';
import * as LocationService from '../../server/service/location/LocationService';
import { DomainConfigLocal } from '../../server/service/organization/DomainConfigRepository';
import { wwgLogger } from '../../server/service/wwgLogger';
import { UserModel } from '../../server/models/user';
import * as tokenService from '../../server/service/crypto/token';

describe('contextMiddleware', () => {
  const sandbox = createSandbox();
  const mockRequest: Partial<AuthenticatedRequest> = {
    ip: '127.0.0.1',
    method: 'GET',
    path: '/test',
    originalUrl: '/test?param=value',
    user: { ...userOne } as unknown as UserModel,
    header: sandbox.stub().callsFake((name) => {
      if (name === 'user-agent') return 'Mozilla/5.0';
      if (name === 'origin') return 'https://test.example.com';
      return undefined;
    }),
  };
  const mockResponse: Partial<Response> = {};

  beforeEach(() => {
    sandbox.stub(LocationService, 'getLocationInfo').resolves({
      country: 'United Kingdom',
      latitude: 51.5074,
      longitude: -0.1278,
      postalCode: 'SW1A 1AA',
      state: 'England',
      city: 'London',
      continentCode: 'EU',
      ip: '127.0.0.1',
    });
    sandbox.stub(LocationService, 'isExtendedLocation').returns(true);

    sandbox.stub(DomainConfigLocal.prototype, 'getBySubDomain').resolves(undefined);
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('operationFromRequest', () => {
    it('should return correct operation for HTTP methods', () => {
      expect(operationFromRequest('get')).to.equal(Operation.Access);
      expect(operationFromRequest('post')).to.equal(Operation.Create);
      expect(operationFromRequest('put')).to.equal(Operation.Modify);
      expect(operationFromRequest('patch')).to.equal(Operation.Modify);
      expect(operationFromRequest('delete')).to.equal(Operation.Remove);
      expect(operationFromRequest('unknown')).to.equal(Operation.Access);
    });
  });

  describe('fromRequest', () => {
    it('should create context from request', async () => {
      const context = await fromRequest(mockRequest as AuthenticatedRequest);

      expect(context).deep.equals({
        requestId: context.requestId,
        ip: '127.0.0.1',
        operation: Operation.Access,
        severity: 6,
        actor: {
          id: userOne._id,
          type: 'User',
          alternateId: undefined,
        },
        client: {
          userAgent: {
            rawUserAgent: 'Mozilla/5.0',
            os: undefined,
            browser: undefined,
          },
          device: undefined,
          id: undefined,
          ipAddress: '127.0.0.1',
          geographicalContext: {
            country: 'United Kingdom',
            geolocation: {
              lat: 51.5074,
              lon: -0.1278,
            },
            postalCode: 'SW1A 1AA',
            state: 'England',
            city: 'London',
            continentCode: 'EU',
          },
        },
        debugContext: {
          debugData: {
            requestId: context.requestId,
            requestUri: '/test',
            url: '/test?param=value',
          },
        },
        transaction: {
          id: context.requestId,
          type: 'WEB',
        },
        origin: 'https://test.example.com',
        domainConfig: undefined,
      });
    });

    it('should throw ContextError if user is not authenticated', async () => {
      expect(fromRequest({ ...mockRequest, user: undefined } as unknown as AuthenticatedRequest)).to.eventually.throw(
        new ContextError('user must be authenticated for audit logs')
      );
    });
  });

  describe('ContextMiddleware', () => {
    it('should call asyncRequestContext.run with context and next function', async () => {
      const nextFunction = sandbox.stub();
      const requestId = '123';
      sandbox.stub(tokenService, 'generatedUUID').returns(requestId);
      await ContextMiddleware(mockRequest as Request, mockResponse as Response, () => nextFunction(getContext()));

      const context = nextFunction.firstCall.args[0];
      const expectedContext = await fromRequest(mockRequest as AuthenticatedRequest);
      expect(context).to.deep.equal(expectedContext);
    });

    it('should call next with error if fromRequest fails', async () => {
      const nextFunction = sandbox.stub();
      await ContextMiddleware({ ...mockRequest, user: undefined } as Request, mockResponse as Response, nextFunction);

      const context = nextFunction.firstCall.args[0];
      expect(context).to.be.instanceOf(ContextError);
      expect(context.message).to.equal('user must be authenticated for audit logs');
    });
  });

  describe('getContext and tryGetContext', () => {
    let getStoreStub: sinon.SinonStub;

    beforeEach(() => {
      getStoreStub = sandbox.stub(asyncRequestContext, 'getStore');
    });

    it('getContext should return context when available', () => {
      const mockContext = { requestId: '123' };
      getStoreStub.returns(mockContext);

      const result = getContext();
      expect(result).to.equal(mockContext);
    });

    it('getContext should throw ContextError when context is not available', () => {
      getStoreStub.returns(undefined);

      expect(() => getContext()).to.throw(
        ContextError,
        'Request context not found. Ensure you are within a ContextMiddleware chain.'
      );
    });

    it('tryGetContext should return context when available', () => {
      const mockContext = { requestId: '123' };
      getStoreStub.returns(mockContext);

      const result = tryGetContext();
      expect(result).to.equal(mockContext);
    });

    it('tryGetContext should log error and return undefined when context is not available', () => {
      getStoreStub.returns(undefined);
      const loggerStub = sandbox.stub(wwgLogger, 'error');

      const result = tryGetContext();

      expect(result).to.be.undefined;
      expect(loggerStub.calledOnce).to.be.true;
    });
  });
});
