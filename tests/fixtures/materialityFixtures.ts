import { ObjectId } from 'bson';
import {
  MaterialityAssessmentType,
  MaterialityMetricWithUtrAndValueListPlain,
} from '../../server/models/materialityMetric';
import {
  ESGCategory,
  MaterialPillar,
  MaterialityBoundary,
  MaterialityScopeScore,
  MaterialTopicPlainWithId,
} from '../../server/models/materialTopics';
import { MaterialityAssessmentScope } from '../../server/service/materiality-assessment/types';
import { UtrValueType } from '../../server/models/public/universalTrackerType';

export function createMaterialityMetric(options: Partial<MaterialityMetricWithUtrAndValueListPlain<string>> = {}) {
  const id = options._id || new ObjectId().toString();
  return {
    _id: id,
    code: options.code || 'default-metric-code',
    utrCode: options.utrCode || 'default-utr-code',
    type: options.type || MaterialityAssessmentType.Financial,
    valueListCode: options.valueListCode || 'default-value-list-code',
    options: options.options || [
      {
        optionCode: 'opt1',
        scores: [{ materialityCode: 'default-topic-code', score: 10 }],
      },
    ],
    tags: options.tags || [MaterialityAssessmentScope.SME, MaterialityAssessmentType.Financial],
    created: new Date(), // Ensure created is Date
    utr: options.utr,
    valueList: options.valueList,
  };
}

export function createMaterialTopic(
  options: Partial<MaterialTopicPlainWithId> = {}
): MaterialTopicPlainWithId {
  const id = options._id || new ObjectId();

  return {
    code: options.code || 'default-topic-code',
    name: options.name || 'Default Material Topic',
    created: options.created || new Date(),
    description: options.description || 'test description',
    action: options.action || 'test action',
    verifiedDate: options.verifiedDate || undefined,
    mappedDate: options.mappedDate || undefined,
    types: options.types || [MaterialityAssessmentType.Financial],
    utrMapping: options.utrMapping || [{ code: 'default-metric-code', score: 5 }],
    categories: options.categories || {
      esg: [ESGCategory.Environmental],
      sdg: ['13'],
      materialPillar: [MaterialPillar.Planet],
      boundary: [MaterialityBoundary.SupplyChain],
    },
    scopeScores: (options.scopeScores?.map((ss) => ({
      scope: ss.scope || MaterialityAssessmentScope.SME,
      type: ss.type || MaterialityAssessmentType.Financial,
      maxScore: ss.maxScore ?? 0,
      referenceCount: ss.referenceCount ?? 0,
    })) || []) as MaterialityScopeScore[],
    _id: id,
  };
}

export const materialityMetricOne = createMaterialityMetric({
  code: 'materiality/materiality-2024/esg-investors',
  utrCode: 'materiality-2024/esg-investors',
  valueListCode: 'materiality-2024/esg-investors',
  type: MaterialityAssessmentType.Financial,
  options: [
    {
      optionCode: 'blue',
      scores: [
        { materialityCode: 'air-emissions-management-and-reduction', score: 100 },
        { materialityCode: 'renewable-energy-access-and-development', score: 90 },
      ],
    },
    {
      optionCode: 'red',
      scores: [
        { materialityCode: 'business-sustainability-and-ethics', score: 100 },
        { materialityCode: 'transparency-disclosure-and-compliance-practices', score: 90 },
      ],
    },
    {
      optionCode: 'green',
      scores: [
        { materialityCode: 'financial-management-practices-and-reporting', score: 100 },
        { materialityCode: 'risk-management-practices-and-mitigation', score: 90 },
      ],
    },
    {
      optionCode: 'orange',
      scores: [
        { materialityCode: 'talent-acquisition-development-and-retention', score: 100 },
        { materialityCode: 'integrated-workforce-empowerment-advancing-employee-development-safety-and-compliance-initiatives', score: 90 },
      ],
    },
    {
      optionCode: 'yellow',
      scores: [
        { materialityCode: 'risk-management-practices-and-mitigation', score: 100 },
        { materialityCode: 'business-continuity-and-resilience', score: 90 },
      ]
    }
  ],
  tags: [
    MaterialityAssessmentScope.Micro,
    MaterialityAssessmentScope.SME,
    MaterialityAssessmentScope.MidCap,
    MaterialityAssessmentScope.MNC,
    MaterialityAssessmentScope.Large,
    MaterialityAssessmentType.Financial,
    MaterialityAssessmentType.Impact,
  ],
  utr: {
    _id: new ObjectId().toString(),
    code: 'materiality-2024/esg-investors',
    created: new Date(),
    name: 'Which of the following ESG issues are most relevant to your Company\'s investors?',
    targetDirection: 'tracker',
    type: 'kpi',
    valueLabel: 'Which of the following ESG issues are most relevant to your Company\'s investors?',
    valueType: UtrValueType.ValueList,
    valueListOrdered: [],
    valueListTargets: [],
  },
});

export const materialityTopicOne = createMaterialTopic({
  code: 'air-emissions-management-and-reduction',
  name: 'Air emissions management and reduction',
  types: [MaterialityAssessmentType.Financial, MaterialityAssessmentType.Impact],
  utrMapping: [],
  categories: { boundary: [], esg: [], materialPillar: [], sdg: [] },
  scopeScores: [],
});
