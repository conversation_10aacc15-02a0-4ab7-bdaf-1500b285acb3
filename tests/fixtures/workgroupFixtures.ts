import { ObjectId } from 'bson';
import { WorkgroupPlain } from '../../server/models/workgroup';
import { createSurveyPermission } from './survey';
import { SurveyWorkgroup } from '../../server/service/workgroup/SurveyWorkgroupService';
import { ToGetWorkgroupsSurvey } from '../../server/types/workgroup';
import { Blueprints } from '../../server/survey/blueprints';
import { SurveyPermission } from '../../server/models/survey';

export const createWorkgroup = (overrides: Partial<WorkgroupPlain> = {}): WorkgroupPlain => ({
  _id: new ObjectId(),
  initiativeId: new ObjectId(),
  name: 'Workgroup name',
  description: 'Workgroup description',
  icon: 'fa-users',
  color: '#ffffff',
  users: [],
  creatorId: new ObjectId(),
  created: new Date(),
  updated: new Date(),
  ...overrides,
});

export const createSurveyWorkgroup = (
  overrides: Partial<SurveyWorkgroup> = {},
  permissionOverrides: Partial<SurveyPermission> = {}
): SurveyWorkgroup => {
  const workgroup = createWorkgroup();
  return {
    ...workgroup,
    permission: createSurveyPermission({ modelId: workgroup._id, ...permissionOverrides }),
    utrvIds: [],
    ...overrides,
  };
};

export const createToGetWorkgroupsSurvey = (overrides: Partial<ToGetWorkgroupsSurvey> = {}): ToGetWorkgroupsSurvey => ({
  _id: new ObjectId(),
  sourceName: Blueprints.Gri2020,
  permissions: [],
  initiativeId: new ObjectId(),
  visibleUtrvs: [],
  ...overrides,
});
