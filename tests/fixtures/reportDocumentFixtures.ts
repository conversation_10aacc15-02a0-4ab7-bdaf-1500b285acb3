import { ObjectId } from 'bson';
import { CreateReportDocument, ReportDocumentPlain, ReportDocumentType } from '../../server/models/reportDocument';
import { SerializedEditorState } from 'lexical';

export const getCreateReportDocumentFixture = (): CreateReportDocument => ({
  title: 'Test Report',
  description: 'Test Content',
  type: ReportDocumentType.CSRD,
  initiativeId: new ObjectId(),
  createdBy: new ObjectId(),
});

export const getReportDocumentFixture = (
  id: ObjectId = new ObjectId(),
  initiativeId: ObjectId = new ObjectId()
): ReportDocumentPlain => ({
  _id: id,
  type: ReportDocumentType.CSRD,
  initiativeId: initiativeId,
  title: 'Fixture Report',
  description: 'Fixture Content',
  createdBy: new ObjectId(),
  created: new Date(),
  lastUpdated: new Date(),
});

export const getEditorStateFixture = (): SerializedEditorState => ({
  root: {
    children: [],
    direction: 'ltr',
    format: '',
    indent: 0,
    type: 'root',
    version: 1,
  },
});
