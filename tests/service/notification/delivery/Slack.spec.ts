import { expect } from 'chai';
import sinon from 'sinon';
import {
  CreateNotificationData,
  SlackNotificationCategory,
  SlackProvider,
} from '../../../../server/service/notification/delivery/Slack';
import { wwgLogger } from '../../../../server/service/wwgLogger';
import ContextError from '../../../../server/error/ContextError';
import { WebClient } from '@slack/web-api/dist';

describe('SlackProvider', () => {
  const sandbox = sinon.createSandbox();
  let slackClient: WebClient;
  let slackProvider: SlackProvider;
  let loggerStub: sinon.SinonStub;
  let postMessageStub: sinon.SinonStub;
  const mockChannel = 'C123';

  beforeEach(() => {
    postMessageStub = sandbox.stub();
    slackClient = {
      chat: {
        postMessage: postMessageStub,
      },
    } as unknown as WebClient;
    slackProvider = new SlackProvider(wwgLogger, slackClient);
    loggerStub = sandbox.stub(wwgLogger, 'info');
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('create', () => {
    it('should send a Slack message for a valid category', async () => {
      postMessageStub.resolves({ ok: true, channel: mockChannel, ts: '12345.6789' });

      const createData: CreateNotificationData = {
        category: SlackNotificationCategory.Announcements,
        title: 'Test Title',
        content: 'Test Content',
        topic: 'test-topic',
        channel: mockChannel,
      };

      const expectedMessage = {
        channel: mockChannel,
        text: '📢 Test Title',
      };

      await slackProvider.create(createData);

      expect(postMessageStub.calledOnce).to.be.true;
      expect(postMessageStub.firstCall.args[0]).to.deep.include(expectedMessage);
      expect(loggerStub.calledWith('Slack message sent successfully')).to.be.true;
    });

    it('should not send a message if no channel is configured', async () => {
      const createData: CreateNotificationData = {
        category: SlackNotificationCategory.Announcements,
        title: 'Test Title',
        content: 'Test Content',
        topic: 'test-topic',
        channel: undefined,
      };

      await slackProvider.create(createData);

      expect(postMessageStub.called).to.be.false;
      expect(loggerStub.calledWith('No channel for Slack notification')).to.be.true;
    });

    it('should not send a message if it is wrong category', async () => {
      const createData: CreateNotificationData = {
        category: 'wrong-category' as SlackNotificationCategory,
        title: 'Test Title',
        content: 'Test Content',
        topic: 'test-topic',
        channel: mockChannel,
      };

      await slackProvider.create(createData);

      expect(postMessageStub.called).to.be.false;
      expect(loggerStub.calledWith('No channel for Slack notification')).to.be.true;
    });

    it('should throw a ContextError if sending the message fails', async () => {
      const testError = new Error('Slack API error');
      postMessageStub.rejects(testError);

      const createData: CreateNotificationData = {
        category: SlackNotificationCategory.Announcements,
        title: 'Test Title',
        content: 'Test Content',
        topic: 'test-topic',
        channel: mockChannel,
      };

      await expect(slackProvider.create(createData)).to.be.rejectedWith(ContextError, 'Failed to send Slack message');
    });
  });
});
