import { expect } from 'chai';
import sinon from 'sinon';
import { ObjectId } from 'bson';
import { getBackgroundJobService } from '../../../server/service/background-process/BackgroundJobService';
import { BackgroundBaseWorkflow } from '../../../server/service/background-process/BackgroundBaseWorkflow';
import BackgroundJob, { JobStatus, JobType } from '../../../server/models/backgroundJob';
import UserError from '../../../server/error/UserError';
import { userOne } from '../../fixtures/userFixtures';

describe('BackgroundJobService', () => {
  const sandbox = sinon.createSandbox();
  const service = getBackgroundJobService();

  afterEach(() => {
    sandbox.restore();
  });

  describe('resetAndRunJob', () => {
    it('should reset and run a job successfully', async () => {
      const job = new BackgroundJob({
        _id: new ObjectId(),
        type: JobType.GenerateReport,
        status: JobStatus.Error,
        retryCount: 0,
      });

      const resetJobStub = sandbox.stub(BackgroundBaseWorkflow, 'resetJob').resolves(job);
      const runByIdStub = sandbox.stub(service, 'runById').resolves();

      const result = await service.resetAndRunJob(job, userOne);

      expect(resetJobStub.calledOnceWith(job, userOne)).to.be.true;
      expect(job.retryCount).to.equal(1);
      expect(runByIdStub.calledOnceWith(job._id, userOne)).to.be.true;
      expect(result).to.equal(job);
    });

    it('should throw error when max retry count is reached', async () => {
      const job = new BackgroundJob({
        _id: new ObjectId(),
        type: JobType.GenerateReport,
        status: JobStatus.Error,
        retryCount: 3,
        maxRetryCount: 3,
      });

      await expect(service.resetAndRunJob(job, userOne)).to.eventually.be.rejectedWith(
        UserError,
        /Job has reached max retry count/
      );
    });
  });
});
