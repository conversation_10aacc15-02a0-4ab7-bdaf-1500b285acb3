import { ObjectId } from 'bson';
import { expect } from 'chai';
import sinon from 'sinon';
import { getScopeUtrvsService } from '../../../server/service/survey/ScopeUtrvsService';
import { createScopeUtrv } from '../../fixtures/universalTrackerValueFixtures';
import { MetricGroupRepository } from '../../../server/repository/MetricGroupRepository';

describe('ScopeUtrvsService', () => {
  const sandbox = sinon.createSandbox();
  const service = getScopeUtrvsService();

  beforeEach(() => {
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('filterUtrvsByScope', () => {
    it('should filter utrvs by scope', async () => {
      const universalTracker1 = {
            _id: new ObjectId(),
            code: 'sdg/1',
            alternatives: {},
            type: 'sdg',
            typeTags: [],
          }
          const universalTracker2 = {
            _id: new ObjectId(),
            code: 'sdg/2',
            alternatives: {},
            type: 'sdg',
            typeTags: [],
          }
          const contribution = {
            'sdg/1': ['sdg/1'],
            'sdg/2': ['sdg/2'],
          }
          const scope = {
            sdg: ['1', '2'],
          }
      const utrvs = [
        createScopeUtrv({universalTracker: universalTracker1, universalTrackerId: universalTracker1._id }),
        createScopeUtrv({universalTracker: universalTracker2, universalTrackerId: universalTracker2._id }),
      ]
      const result = await service.filterUtrvsByScope({ utrvs, contribution, scope });
      expect(result).to.deep.equal(utrvs);
    });

    it('should also filter by custom group', async () => {
      const utr1 = {
        _id: new ObjectId(),
        code: 'custom/1',
        alternatives: {},
        type: 'custom',
        typeTags: [],
      }
      const utr2 = {
        _id: new ObjectId(),
        code: 'custom/2',
        alternatives: {},
        type: 'custom',
        typeTags: [],
      }
      const utr3 = {
        _id: new ObjectId(),
        code: 'custom/3',
        alternatives: {},
        type: 'custom',
        typeTags: [],
      }
      const utrv1 = createScopeUtrv({universalTracker: utr1, universalTrackerId: utr1._id });
      const utrv2 = createScopeUtrv({universalTracker: utr2, universalTrackerId: utr2._id });
      const utrv3 = createScopeUtrv({universalTracker: utr3, universalTrackerId: utr3._id });
      const utrvs = [utrv1, utrv2, utrv3];
      const contribution = {};
      const scope = {
        custom: [utr1._id, utr2._id],
      }
      const getUtrIdsStub = sandbox.stub(MetricGroupRepository, 'getUtrIds').resolves([utr1._id, utr2._id]);

      const result = await service.filterUtrvsByScope({ utrvs, contribution, scope });

      expect(getUtrIdsStub.calledOnceWith(scope.custom)).to.be.true;
      expect(result).to.deep.equal([utrv1, utrv2]);
    })
  });
});
