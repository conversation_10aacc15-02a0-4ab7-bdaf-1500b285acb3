/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { expect } from 'chai';
import { createSandbox, SinonStub } from 'sinon';
import { DelegatedSurveysReportDownload } from '../../../../server/service/survey/download/DelegatedSurveysReportDownload';
import { SurveyRepository } from '../../../../server/repository/SurveyRepository';
import User from '../../../../server/models/user';
import { getSimpleReportGenerator } from '../../../../server/service/custom-report/SimpleReportGenerator';
import { getSurveyExcel } from '../../../../server/service/survey/transfer/SurveyExcel';
import { DownloadMultiScope } from '../../../../server/service/survey/scope/downloadScope';
import { FileParserType } from '../../../../server/service/survey/transfer/parserTypes';
import * as CsvFileWriter from '../../../../server/service/file/writer/CsvFileWriter';
import { createSurvey } from '../../../fixtures/survey';
import { userOne } from '../../../fixtures/userFixtures';
import { SurveyModelPlain } from '../../../../server/models/survey';
import { createMongooseModel } from '../../../setup';
import { ActionList } from '../../../../server/service/utr/constants';
import { DataScopeAccess } from '../../../../server/models/dataShare';
import { VisibilityStatus } from '../../../../server/service/survey/scope/visibilityStatus';
import { SurveyScope } from '../../../../server/service/survey/SurveyScope';
import { initiativeOneSimpleId } from '../../../fixtures/initiativeFixtures';
import { InitiativePermissions } from '../../../../server/service/initiative/InitiativePermissions';
import { testLogger } from '../../../factories/logger';
import { PipelineStage } from 'mongoose';

describe('DelegatedSurveysReportDownload', () => {
  const reportGeneratorService = getSimpleReportGenerator();
  const surveyExcelService = getSurveyExcel();

  let getDownloadDataStub: SinonStub;
  let createSimpleReportSheetStub: SinonStub;
  let getPlainSheetDataStub: SinonStub;
  let delegatedSurveysReportDownload: DelegatedSurveysReportDownload;
  let findSurveysStub: SinonStub;
  let findOneUserStub: SinonStub;
  let canAccessAllSurveyDataStub: SinonStub;
  let stringifyStub: SinonStub;

  const sandbox = createSandbox();

  const mockSurveys = [
    createSurvey({ stakeholders: { stakeholder: [userOne._id], verifier: [], escalation: [] } }),
    createSurvey({ stakeholders: { stakeholder: [], verifier: [userOne._id], escalation: [] } }),
  ];
  const mockDownloadData = {
    headers: ['Header1', 'Header2'],
    records: [
      ['Value1', 'Value2'],
      ['Value3', 'Value4'],
    ],
  };
  const downloadScope: DownloadMultiScope = {
    access: DataScopeAccess.Full,
    scope: SurveyScope.createEmpty(),
    statuses: [ActionList.Created, ActionList.Verified],
    visibilityStatus: VisibilityStatus.ExcludeData,
    displayUserInput: true,
    displayTag: true,
    displayMetricOverrides: false,
  };

  beforeEach(() => {
    getDownloadDataStub = sandbox.stub(reportGeneratorService, 'getDownloadData').resolves(mockDownloadData);
    createSimpleReportSheetStub = sandbox
      .stub(surveyExcelService, 'createSimpleReportSheet')
      .resolves({ SheetNames: ['Sheet1'], Sheets: { Sheet1: {} } });
    getPlainSheetDataStub = sandbox.stub(surveyExcelService, 'getPlainSheetData').returns(mockDownloadData.records);

    findSurveysStub = sandbox.stub(SurveyRepository, 'findSurveys').resolves(mockSurveys as SurveyModelPlain[]);
    findOneUserStub = sandbox.stub(User, 'findOne').returns(createMongooseModel(userOne));
    canAccessAllSurveyDataStub = sandbox.stub(InitiativePermissions, 'canAccessAllSurveyData');
    stringifyStub = sandbox.stub(CsvFileWriter, 'stringifyArrayCsvFile').returns('test,csv');

    delegatedSurveysReportDownload = new DelegatedSurveysReportDownload(
      testLogger,
      reportGeneratorService,
      surveyExcelService
    );
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('getDownloadData', () => {
    it('should get download data with no filters when user has full access', async () => {
      // User has full access to all surveys
      canAccessAllSurveyDataStub.resolves(true);

      const result = await delegatedSurveysReportDownload.getDownloadData({
        initiativeId: initiativeOneSimpleId,
        userIds: [userOne._id],
        surveyIds: mockSurveys.map((survey) => survey._id),
        downloadScope,
      });

      expect(findSurveysStub.calledOnce).to.be.true;
      expect(findOneUserStub.calledOnce).to.be.true;
      expect(canAccessAllSurveyDataStub.calledOnce).to.be.true;
      expect(getDownloadDataStub.calledOnce).to.be.true;

      // Check that survey filters map has empty arrays (no filters)
      const reportGeneratorArgs = getDownloadDataStub.firstCall.args[0];
      const surveyFiltersMap = reportGeneratorArgs.surveyFiltersMap as Map<string, PipelineStage[]>;
      expect(surveyFiltersMap.size).to.equal(mockSurveys.length);
      for (const filters of surveyFiltersMap.values()) {
        expect(filters).to.be.an('array').that.is.empty;
      }

      expect(result).to.equal(mockDownloadData);
    });

    it('should get download data with filters when user does not have full access', async () => {
      // User does not have full access to surveys
      canAccessAllSurveyDataStub.resolves(false);

      const result = await delegatedSurveysReportDownload.getDownloadData({
        initiativeId: initiativeOneSimpleId,
        userIds: [userOne._id],
        surveyIds: mockSurveys.map((survey) => survey._id),
        downloadScope,
      });

      expect(findSurveysStub.calledOnce).to.be.true;
      expect(findOneUserStub.calledOnce).to.be.true;
      expect(canAccessAllSurveyDataStub.calledOnce).to.be.true;
      expect(getDownloadDataStub.calledOnce).to.be.true;

      const reportGeneratorArgs = getDownloadDataStub.firstCall.args[0];
      const surveyFiltersMap = reportGeneratorArgs.surveyFiltersMap as Map<string, PipelineStage[]>;
      expect(surveyFiltersMap.size).to.equal(mockSurveys.length);
      for (const filters of surveyFiltersMap.values()) {
        expect(filters).to.deep.equals([]);
      }
      expect(result).to.equal(mockDownloadData);
    });
  });

  describe('getFileContent', () => {
    it('should generate XLSX file content', async () => {
      const mockWorkbook = {
        SheetNames: ['Sheet1'],
        Sheets: {
          Sheet1: {
            '!ref': 'A1:B2',
            A1: { t: 's', v: 'Header1' },
            B1: { t: 's', v: 'Header2' },
            A2: { t: 's', v: 'Value1' },
            B2: { t: 's', v: 'Value2' },
          },
        },
      };
      createSimpleReportSheetStub.resolves(mockWorkbook);

      const result = await delegatedSurveysReportDownload.getFileContent({
        exportType: FileParserType.Xlsx,
        downloadData: mockDownloadData,
      });

      expect(createSimpleReportSheetStub.calledOnce).to.be.true;
      expect(result).to.be.instanceOf(Buffer);
    });

    it('should generate file content', async () => {
      const result = await delegatedSurveysReportDownload.getFileContent({
        exportType: FileParserType.Csv,
        downloadData: mockDownloadData,
      });

      expect(getPlainSheetDataStub.calledOnce).to.be.true;
      expect(stringifyStub.calledOnce).to.be.true;
      expect(stringifyStub.firstCall.args[0]).to.deep.equal({
        records: mockDownloadData.records,
        header: mockDownloadData.headers,
      });
      expect(result).to.equal('test,csv');
    });
  });
});
