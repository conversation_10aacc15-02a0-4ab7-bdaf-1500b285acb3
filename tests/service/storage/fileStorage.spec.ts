import { expect } from 'chai';
import { getFileNameWithExtension } from '../../../server/service/storage/fileStorage';

describe('FileStorage', () => {
  describe('getFileNameWithExtension', () => {
    it('should return the fileName if it already has a valid extension', () => {
      const doc = {
        title: 'My Report.pdf',
        metadata: { name: 'report.pdf', extension: 'pdf' },
      };
      const result = getFileNameWithExtension(doc);
      expect(result).to.equal('My Report.pdf');
    });

    it('should append the extension from metadata if the title has none', () => {
      const doc = {
        title: 'My Report',
        metadata: { name: 'report.pdf', extension: 'pdf' },
      };
      const result = getFileNameWithExtension(doc);
      expect(result).to.equal('My Report.pdf');
    });

    it('should use metadata.name if title is not provided', () => {
      const doc = {
        metadata: { name: 'report.docx', extension: 'docx' },
      };
      const result = getFileNameWithExtension(doc);
      expect(result).to.equal('report.docx');
    });

    it('should return undefined if the fileName has no extension and metadata provides no extension', () => {
      const doc = {
        title: 'My Report',
        metadata: { name: 'report' },
      };
      const result = getFileNameWithExtension(doc);
      expect(result).to.be.undefined;
    });

    it('should return undefined if no fileName can be determined', () => {
      const doc = {
        metadata: {},
      };
      const result = getFileNameWithExtension(doc);
      expect(result).to.be.undefined;
    });

    it('should return undefined if the document has no metadata', () => {
      const doc = {
        title: 'My Report',
      };
      const result = getFileNameWithExtension(doc);
      expect(result).to.be.undefined;
    });

    it('should handle filenames with dots that are not extensions', () => {
      const doc = {
        title: 'My.Report.Version.1',
        metadata: { name: 'My.Report.Version.1.zip', extension: 'zip' },
      };
      const result = getFileNameWithExtension(doc);
      expect(result).to.equal('My.Report.Version.1.zip');
    });

    it('should return the full name from metadata if it has an extension and there is no title', () => {
      const doc = {
        metadata: { name: 'data.archive.tar.gz', extension: 'gz' },
      };
      const result = getFileNameWithExtension(doc);
      expect(result).to.equal('data.archive.tar.gz');
    });
  });
});
