import { expect } from 'chai';
import { createSandbox, SinonSandbox, SinonSpy, SinonStub } from 'sinon';
import { MaterialityPostReleaseProcessor } from '../../../server/service/internal-release/MaterialityPostReleaseProcessor';
import { InternalRelease, ReleaseCollection } from '../../../server/service/internal-release/types';
import MaterialityMetric from '../../../server/models/materialityMetric';
import MaterialTopic from '../../../server/models/materialTopics';
import { testLogger } from '../../factories/logger';
import { materialityMetricOne, materialityTopicOne } from '../../fixtures/materialityFixtures';

describe('MaterialityPostReleaseProcessor', () => {
  let sandbox: SinonSandbox;
  let processor: MaterialityPostReleaseProcessor;
  let findMetricsStub: SinonStub;
  let findTopicsStub: SinonStub;
  let infoSpy: SinonSpy;

  beforeEach(() => {
    sandbox = createSandbox();
    processor = new MaterialityPostReleaseProcessor(testLogger);

    findMetricsStub = sandbox.stub(MaterialityMetric, 'find');
    findTopicsStub = sandbox.stub(MaterialTopic, 'find');
    infoSpy = sandbox.spy(testLogger, 'info');
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('canProcess', () => {
    it('should return true if release scope includes MaterialTopics', () => {
      const release = {
        scope: [{ collectionName: ReleaseCollection.MaterialTopics }],
      } as InternalRelease['release'];
      expect(processor.canProcess(release)).to.be.true;
    });

    it('should return true if release scope includes MaterialityMetrics', () => {
      const release = {
        scope: [{ collectionName: ReleaseCollection.MaterialityMetrics }],
      } as InternalRelease['release'];
      expect(processor.canProcess(release)).to.be.true;
    });

    it('should return false and log info if release scope does not include relevant collections', () => {
      const release = {
        code: 'test-release',
        scope: [{ collectionName: ReleaseCollection.UniversalTrackers }],
      } as InternalRelease['release'];

      expect(processor.canProcess(release)).to.be.false;
      expect(
        infoSpy.calledOnceWith(
          'Skipping post-process as MaterialTopics or MaterialityMetrics are not included for this release.',
          { releaseCode: 'test-release' }
        )
      ).to.be.true;
    });
  });

  describe('process', () => {
    const mockRelease = { code: 'test-release' } as InternalRelease['release'];

    afterEach(() => {
      sandbox.restore();
    });

    it('should log info and return if no topics match metrics', async () => {
      findMetricsStub.returns({
        populate: sandbox
          .stub()
          .returns({ lean: sandbox.stub().returns({ exec: sandbox.stub().resolves([materialityMetricOne]) }) }),
      });
      findTopicsStub.returns({ lean: sandbox.stub().returns({ exec: sandbox.stub().resolves([]) }) });
      await processor.process(mockRelease);
      expect(infoSpy.calledWith('No topics found that match metrics. Skipping further processing.')).to.be.true;
    });

    it('should process topics and save them', async () => {
      findMetricsStub.returns({
        populate: sandbox
          .stub()
          .returns({ lean: sandbox.stub().returns({ exec: sandbox.stub().resolves([materialityMetricOne]) }) }),
      });
      findTopicsStub.returns({ lean: sandbox.stub().returns({ exec: sandbox.stub().resolves([materialityTopicOne]) }) });
      const findByIdAndUpdateStub = sandbox.stub(MaterialTopic, 'findByIdAndUpdate');

      await processor.process(mockRelease);
      expect(infoSpy.calledWith('Starting post-process for materiality.', { releaseCode: 'test-release' })).to.be.true;
      expect(findByIdAndUpdateStub.calledOnce).to.be.true;
      expect(infoSpy.calledWith('Finished post-process for materiality.')).to.be.true;
    });
  });
});
