import { expect } from 'chai';
import {
  getData,
  getStringData,
  getFactLabel,
  getFactSectionAndDataPoint,
  getMajorSection,
  getOrderedMappingItems,
  getContextualIxbrlNodes,
  getDefaultMapping,
  buildIxbrlNodesFromMapping,
} from '../../../../server/service/reporting/esrs/utils';
import { XBRLMapping, UtrvData, EsrsFact } from '../../../../server/service/reporting/esrs/types';
import { createExtendedUtrv } from '../../../../tests/fixtures/compositeUTRVFixtures';
import { UtrValueType } from '../../../../server/models/public/universalTrackerType';
import { NumberScale } from '../../../../server/service/units/unitTypes';
import { ObjectId } from 'bson';
import { utrOneId, utrTwoId } from '../../../../tests/fixtures/universalTrackerFixtures';
import { XbrlTracker } from '../../../../server/service/reporting/XbrlTracker';
import { createSandbox } from 'sinon';
import { createHeadlessEditor } from '@lexical/headless';
import { editorConfig } from '../../../../server/service/reporting/esrs/CsrdLexicalStateGenerator';
import { IXBRLNode } from '../../../../server/service/reporting/lexical/nodes/IXBRLNode';
import { ParagraphNode } from 'lexical';
import { HeadingNode } from '@lexical/rich-text';
import { ListItemNode, ListNode } from '@lexical/list';

describe('utils', () => {
  const sandbox = createSandbox();

  afterEach(() => {
    sandbox.restore();
  });
  const factOne = 'esrs:BasisForPreparationOfSustainabilityStatement'; // Section: "BP-1", dataPointId: "BP-1_01"
  const factTwo = 'esrs:PercentageOfTotalEmissionsOfPollutantsToSoilOccurringInAreasAtWaterRisk'; // Section: "E2-4", dataPointId: "E2-4_13"
  const factThree = 'esrs:GeneralBasisForPreparationOfSustainabilityStatementAbstract'; // No section, dataPointId
  const noRefFact = 'esrs:NoRefFact';

  // Types from types.ts
  const mapping: XBRLMapping = {
    [factOne]: { factName: factOne, utrCode: 'utr1' },
    [factTwo]: { factName: factTwo, utrCode: 'utr2' },
    [factThree]: { factName: factThree, utrCode: 'utr3' },
  };

  const utrvId1 = new ObjectId();
  const utrvId2 = new ObjectId();

  const utrCodeToUtrvMap: Map<string, UtrvData> = new Map([
    [
      'utr1',
      createExtendedUtrv({
        id: utrvId1,
        utrId: utrOneId,
        value: 123,
        overridesUtr: { code: 'utr1', valueType: UtrValueType.Number, numberScale: NumberScale.Hundreds, unit: 'm3' },
      }),
    ],
    [
      'utr2',
      createExtendedUtrv({
        id: utrvId2,
        utrId: utrTwoId,
        overrides: { valueData: { data: 'abc' } },
        overridesUtr: { code: 'utr2', valueType: UtrValueType.Text, numberScale: undefined, unit: undefined },
      }),
    ],
  ]);

  describe('getData', () => {
    it('returns value for number type', () => {
      expect(getData({ factName: factOne, mapping, utrCodeToUtrvMap })).to.equal(123);
    });
    it('returns value for text type', () => {
      expect(getData({ factName: factTwo, mapping, utrCodeToUtrvMap })).to.equal('abc');
    });
    it('returns fallback for missing mapping', () => {
      expect(getData({ factName: factThree, mapping, utrCodeToUtrvMap, fallback: 'fallback' })).to.equal('fallback');
    });
    it('returns fallback for missing utrv', () => {
      expect(getData({ factName: noRefFact, mapping, utrCodeToUtrvMap, fallback: 'fallback' })).to.equal('fallback');
    });
  });

  describe('getStringData', () => {
    it('returns string value', () => {
      expect(getStringData({ factName: factOne, mapping, utrCodeToUtrvMap })).to.equal('123');
    });
    it('returns fallback as string', () => {
      expect(getStringData({ factName: noRefFact, mapping, utrCodeToUtrvMap, fallback: 'fallback' })).to.equal(
        'fallback'
      );
    });
  });

  describe('getFactLabel', () => {
    it('returns label if present', () => {
      expect(getFactLabel(factOne)).to.equal('Basis for preparation of sustainability statement');
    });
    it('returns empty string if not present', () => {
      expect(getFactLabel(noRefFact)).to.equal('');
    });
  });

  describe('getFactSectionAndDataPoint', () => {
    it('returns section and dataPointId', () => {
      expect(getFactSectionAndDataPoint(factOne)).to.deep.equal({ section: 'BP-1', dataPointId: 'BP-1_01' });
    });
    it('returns undefined for missing references', () => {
      expect(getFactSectionAndDataPoint(noRefFact)).to.be.undefined;
    });
  });

  describe('getMajorSection', () => {
    it('extracts E1 from E1-1', () => {
      expect(getMajorSection('E1-1')).to.equal('E1');
    });
    it('extracts SBM from SBM-2', () => {
      expect(getMajorSection('SBM-2')).to.equal('SBM');
    });
    it('returns undefined for unknown section', () => {
      expect(getMajorSection('XYZ-1')).to.be.undefined;
    });
    it('returns undefined for undefined', () => {
      expect(getMajorSection(undefined)).to.be.undefined;
    });
  });

  describe('getOrderedMappingItems', () => {
    it('returns only items matching majorSection', () => {
      const result = getOrderedMappingItems({ mapping, majorSection: 'BP' });
      expect(result).to.have.lengthOf(1);
      expect(result[0].factName).to.equal(factOne);
    });
    it('returns empty for non-matching majorSection', () => {
      expect(getOrderedMappingItems({ mapping, majorSection: 'E4' })).to.have.lengthOf(0);
    });
  });

  describe('getDefaultMapping', () => {
    const existingFact = 'esrs:DescriptionOfMaterialImpactsResultingFromMaterialityAssessmentExplanatory' as EsrsFact;

    it('returns default mapping when no overrides are provided', () => {
      const defaultMapping = getDefaultMapping({});
      expect(defaultMapping).to.have.property(existingFact);
    });

    it('overrides existing mapping items when overrides are provided', () => {
      const overrides = {
        [existingFact]: {
          factName: existingFact,
          utrCode: 'custom-utr',
          valueListCode: 'custom-value',
        },
      };
      const newMapping = getDefaultMapping(overrides);
      expect(newMapping[existingFact]?.utrCode).to.equal('custom-utr');
      expect(newMapping[existingFact]?.valueListCode).to.equal('custom-value');
    });

    it('adds new mapping items when overrides are provided', () => {
      const overrides = {
        'esrs:NewFact': { factName: 'esrs:NewFact' as EsrsFact, utrCode: 'new-utr' },
      };
      const newMapping = getDefaultMapping(overrides);
      expect(newMapping).to.have.property('esrs:NewFact');
      expect(newMapping['esrs:NewFact']?.utrCode).to.equal('new-utr');
    });
  });

  describe('buildIxbrlNodesFromMapping', () => {
    let tracker: XbrlTracker;
    let editor: any; // Type HeadlessEditor is not exported

    beforeEach(() => {
      tracker = new XbrlTracker();
      editor = createHeadlessEditor({
        onError: (error: Error) => {
          console.error(error);
        },
        nodes: [IXBRLNode, ListItemNode, ListNode, HeadingNode, ParagraphNode],
      });
    });

    it('should build IXBRL nodes for number type', () => {
      editor.update(() => {
        const nodes = buildIxbrlNodesFromMapping({
          mapping: { [factOne]: { factName: factOne, utrCode: 'utr1' } },
          utrCodeToUtrvMap: utrCodeToUtrvMap,
          tracker,
          majorSection: 'BP',
        });
        expect(nodes).to.have.lengthOf(1);
        expect(nodes[0].factName).to.equal(factOne);
        const xbrlNode = nodes[0].ixbrlNode;
        expect(xbrlNode.getName()).to.equal(factOne);
        expect(xbrlNode.getFirstChild()?.getTextContent()).to.equal('123');

        const xbrl = xbrlNode.getXbrl();
        expect(xbrl.tag).to.equal('ix:nonFraction');
        expect(xbrl.format).to.equal('ixt4:num-dot-decimal');
        if (xbrl.tag === 'ix:nonFraction') {
          expect(xbrl.decimals).to.equal(2);
          expect(xbrl.scale).to.equal(2);
          expect(xbrl.unitRef).to.not.be.empty;
        }
      });
    });

    it('should build IXBRL nodes for text type', () => {
      editor.update(() => {
        const nodes = buildIxbrlNodesFromMapping({
          mapping: { [factTwo]: { factName: factTwo, utrCode: 'utr2' } },
          utrCodeToUtrvMap: utrCodeToUtrvMap,
          tracker,
          majorSection: 'E2',
        });
        expect(nodes).to.have.lengthOf(1);
        expect(nodes[0].factName).to.equal(factTwo);

        const xbrlNode = nodes[0].ixbrlNode;
        expect(xbrlNode.getName()).to.equal(factTwo);
        expect(xbrlNode.getFirstChild()?.getTextContent()).to.equal('abc');

        const xbrl = xbrlNode.getXbrl();
        expect(xbrlNode.getXbrl().tag).to.equal('ix:nonNumeric');
        expect(xbrl.format).to.be.undefined;
      });
    });

    it('should handle missing UTRV data', () => {
      editor.update(() => {
        const utrCodeToUtrvMapWithoutFactOne = new Map(utrCodeToUtrvMap);
        utrCodeToUtrvMapWithoutFactOne.delete('utr1');

        const nodes = buildIxbrlNodesFromMapping({
          mapping: { [factOne]: { factName: factOne, utrCode: 'utr1' } },
          utrCodeToUtrvMap: utrCodeToUtrvMapWithoutFactOne,
          tracker,
          majorSection: 'BP',
        });
        expect(nodes).to.have.lengthOf(1);
        const xbrlNode = nodes[0].ixbrlNode;
        expect(xbrlNode.getName()).to.equal(factOne);
        expect(xbrlNode.getFirstChild()?.getTextContent()).to.equal('-');

        const xbrl = xbrlNode.getXbrl();
        expect(xbrl.tag).to.equal('ix:nonFraction');
        expect(xbrl.format).to.be.undefined;
        if (xbrl.tag === 'ix:nonFraction') {
          expect(xbrl.decimals).to.be.undefined;
          expect(xbrl.scale).to.be.undefined;
          expect(xbrl.unitRef).to.be.empty;
        }
      });
    });
  });

  describe('getContextualIxbrlNodes', () => {
    const tracker = new XbrlTracker();
    const editor = createHeadlessEditor(editorConfig);

    it('should generate contextual IXBRL nodes', () => {
      editor.update(() => {
        const nodes = getContextualIxbrlNodes({
          mapping: { [factOne]: { factName: factOne, utrCode: 'utr1' } },
          utrCodeToUtrvMap: utrCodeToUtrvMap,
          tracker,
          majorSection: 'BP',
        });
        expect(nodes).to.have.lengthOf(1);
        const paragraph = nodes[0] as any;
        expect(paragraph.getType()).to.equal('paragraph');
        expect(paragraph.children[0].getTextContent()).to.equal('Basis for preparation of sustainability statement');
        expect(paragraph.children[2].getTextContent()).to.equal('Value:  ');
        expect(paragraph.children[3].getType()).to.equal('ixbrlnode');
        expect(paragraph.children[3].children[0].getTextContent()).to.equal('123');
      });
    });
  });
});
