/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { expect } from 'chai';
import { createSandbox } from 'sinon';
import { testLogger } from '../../../factories/logger';
import { surveyOne } from '../../../fixtures/survey';
import { userOne } from '../../../fixtures/userFixtures';
import { createInitiative } from '../../../fixtures/initiativeFixtures';

import UniversalTrackerValue from '../../../../server/models/universalTrackerValue';
import setup from '../../../setup';
import { esrs2_E3_4 } from '../../../fixtures/utr/utrTableFixtures';
import {
  CsrdLexicalStateGenerator,
  getCsrdLexicalStateGenerator,
} from '../../../../server/service/reporting/esrs/CsrdLexicalStateGenerator';
import { getCsrdReportGenerator } from '../../../../server/service/reporting/esrs/CsrdReportGenerator';
import { CsrdReportMapping } from '../../../../server/service/reporting/esrs/CsrdReportMapping';

describe('CsrdLexicalStateGenerator', () => {
  const sandbox = createSandbox();
  const csrdGenerator = new CsrdLexicalStateGenerator(testLogger, getCsrdReportGenerator(), new CsrdReportMapping());

  const initiative = createInitiative({
    name: 'Alpha Bravo Ltd.',
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should create instance', () => {
    const instance = getCsrdLexicalStateGenerator();
    expect(instance).to.be.instanceOf(CsrdLexicalStateGenerator);
  });

  it('process report and return status', async () => {
    const mockUtrv = esrs2_E3_4;
    setup.wrapStub(sandbox, UniversalTrackerValue, 'aggregate', () => [mockUtrv]);

    const state = await csrdGenerator.generateTemplateLexicalState({
      survey: surveyOne,
      initiative: initiative,
      mapping: {},
      utrCodeToUtrvMap: new Map([[mockUtrv.universalTracker.code, mockUtrv]]),
      user: userOne,
      preview: true,
    });

    const root = state.root;
    expect(root.children).to.not.be.empty;

    const h2s = root.children.filter((n) => n.type === 'heading' && 'tag' in n && n.tag === 'h2');
    expect(h2s).to.not.be.empty;

    const firstH2 = h2s[0];
    expect((firstH2 as any).children?.[0]?.text).to.equal('📑 Table of Contents');
  });
});
