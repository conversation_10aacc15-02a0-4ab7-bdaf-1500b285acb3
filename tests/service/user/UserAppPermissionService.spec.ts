/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { expect } from 'chai';
import { createSandbox } from 'sinon';
import { UserRepository } from '../../../server/repository/UserRepository';
import { UserRoles } from '../../../server/service/user/userPermissions';
import { initiativeOneSimple, initiativeTwo } from '../../fixtures/initiativeFixtures';
import { userOne } from '../../fixtures/userFixtures';
import { UpdateResult } from 'mongoose';
import { getCustomerManager } from '../../../server/service/payment/CustomerManager';
import { organizationOne } from '../../fixtures/organization';
import Initiative, { PERMISSION_GROUPS } from '../../../server/models/initiative';
import { createMongooseModel } from '../../setup';
import { AppCode, ProductAppType } from '../../../server/service/app/AppConfig';
import { createSubWithItem } from '../../fixtures/subscriptions';
import { ProductCodes } from '../../../server/models/customer';
import Organization from '../../../server/models/organization';
import { OrganizationPermission } from '../../../server/models/assurancePermission';
import { UserAppPermissionService } from '../../../server/service/user/UserAppPermissionService';

describe('User App Permission Service', () => {
  const customerManager = getCustomerManager();

  const sandbox = createSandbox();
  const service = new UserAppPermissionService(customerManager);

  beforeEach(() => {
    sandbox.stub(UserRepository, 'addUserPermission').callsFake(async (userIds) => {
      return {
        acknowledged: true,
        modifiedCount: userIds.length,
        matchedCount: userIds.length,
        upsertedCount: 0,
        upsertedId: null,
      } satisfies UpdateResult;
    });
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('getAvailableApps', () => {
    const mockUser = {
      ...userOne,
      permissions: [{ initiativeId: initiativeOneSimple._id, permissions: [UserRoles.Contributor] }],
      organizationId: organizationOne._id,
    };

    it('returns PortfolioTracker when initiative permissionGroup is in PortfolioTrackerIds', async () => {
      sandbox.stub(Initiative, 'find').returns(
        createMongooseModel([
          {
            ...initiativeOneSimple,
            permissionGroup: PERMISSION_GROUPS.PORTFOLIO_TRACKER,
          },
        ])
      );
      const result = await service.getAvailableApps(mockUser);
      expect(result).to.deep.equal([ProductAppType.PortfolioTracker]);
    });

    it('returns SGXESGenome when initiative matches SGXESGenome criteria', async () => {
      sandbox.stub(Initiative, 'find').returns(
        createMongooseModel([
          {
            ...initiativeOneSimple,
            permissionGroup: PERMISSION_GROUPS.COMPANY_TRACKER_LIGHT,
            appConfigCode: AppCode.SGXESGenome,
          },
        ])
      );
      const result = await service.getAvailableApps(mockUser);
      expect(result).to.deep.equal([ProductAppType.SGXESGenome]);
    });

    it('returns CompanyTracker and MaterialityTracker when eligible', async () => {
      sandbox.stub(Initiative, 'find').returns(
        createMongooseModel([
          {
            ...initiativeOneSimple,
            permissionGroup: PERMISSION_GROUPS.COMPANY_TRACKER_ENTERPRISE_OLD1,
          },
        ])
      );
      const sub = createSubWithItem([{ productCode: ProductCodes.MaterialityTrackerLarge }], { status: 'active' });
      sandbox
        .stub(customerManager, 'getSubscriptionsByIds')
        .resolves([{ subscriptions: [sub], initiativeId: initiativeOneSimple._id }]);

      const result = await service.getAvailableApps(mockUser);
      expect(result).to.deep.equal([ProductAppType.CompanyTracker, ProductAppType.MaterialityTracker]);
    });

    it('returns CompanyTracker when permissionGroup is undefined as it is a free CT', async () => {
      sandbox.stub(Initiative, 'find').returns(createMongooseModel([initiativeOneSimple]));

      const result = await service.getAvailableApps(mockUser);
      expect(result).to.deep.equal([ProductAppType.CompanyTracker]);
    });

    it('returns AssuranceTracker when organization is found', async () => {
      sandbox.stub(Initiative, 'find').returns(createMongooseModel([]));
      sandbox.stub(Organization, 'findOne').returns(createMongooseModel(organizationOne));

      const result = await service.getAvailableApps(mockUser);
      expect(result).to.deep.equal([ProductAppType.AssuranceTracker]);
    });

    it('returns CompanyTracker, Portfolio Tracker, and AssuranceTracker when a CT, PT and AT permission are found', async () => {
      const user = {
        ...mockUser,
        permissions: [
          { initiativeId: initiativeOneSimple._id, permissions: [UserRoles.Contributor] },
          { initiativeId: initiativeTwo._id, permissions: [UserRoles.Manager] },
        ],
      };
      sandbox.stub(Initiative, 'find').returns(
        createMongooseModel([
          initiativeOneSimple,
          {
            ...initiativeTwo,
            permissionGroup: PERMISSION_GROUPS.PORTFOLIO_TRACKER,
          },
        ])
      );
      sandbox.stub(Organization, 'findOne').returns(
        createMongooseModel([
          {
            ...organizationOne,
            permissions: [{ userId: userOne._id, permissions: [OrganizationPermission.Assurer] }],
          },
        ])
      );

      const result = await service.getAvailableApps(user);
      expect(result).to.deep.equal([
        ProductAppType.PortfolioTracker,
        ProductAppType.CompanyTracker,
        ProductAppType.AssuranceTracker,
      ]);
    });
  });
});
