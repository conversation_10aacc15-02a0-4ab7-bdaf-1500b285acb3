import { expect } from 'chai';
import sinon from 'sinon';
import { ObjectId } from 'bson';
import { InitiativeSettingsService } from '../../../server/service/initiative/InitiativeSettingsService';
import DocumentModel from '../../../server/models/document';
import InitiativeSettings, { DisplayDocument } from '../../../server/models/initiativeSettings';
import { createMongooseModel } from '../../setup';
import { initiativeOneSimpleId } from '../../fixtures/initiativeFixtures';
import { DocumentMediaType } from '../../../server/types/document';

describe('InitiativeSettingsService', () => {
  let initiativeSettingsService: InitiativeSettingsService;
  let findOneAndUpdateStub: sinon.SinonStub;
  let findOneStub: sinon.SinonStub;
  let findStub: sinon.SinonStub;

  beforeEach(() => {
    initiativeSettingsService = new InitiativeSettingsService(DocumentModel, InitiativeSettings);
    findOneStub = sinon.stub(InitiativeSettings, 'findOne');
    findOneAndUpdateStub = sinon.stub(InitiativeSettings, 'findOneAndUpdate');
    findStub = sinon.stub(DocumentModel, 'find');
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getDisplayDocumentsMap', () => {
    it('should return a map of display documents categorized by media type', async () => {
      const photoDoc = { _id: new ObjectId(), metadata: { mimetype: 'image/jpeg' } };
      const videoDoc = { _id: new ObjectId(), metadata: { mimetype: 'video/mp4' } };
      const fileDoc = { _id: new ObjectId(), metadata: { mimetype: 'application/pdf' } };
      const settings = { documents: [photoDoc, videoDoc, fileDoc] };

      findOneStub.returns(createMongooseModel(settings));

      const result = await initiativeSettingsService.getDisplayDocumentsMap(initiativeOneSimpleId);

      expect(findOneStub.calledOnceWith({ initiativeId: initiativeOneSimpleId }, { displayDocuments: 1 })).to.be.true;
      expect(result[DocumentMediaType.Image].map((doc) => doc._id)).to.deep.equal([photoDoc._id]);
      expect(result[DocumentMediaType.Video].map((doc) => doc._id)).to.deep.equal([videoDoc._id]);
      expect(result[DocumentMediaType.File].map((doc) => doc._id)).to.deep.equal([fileDoc._id]);
    });

    it('should return empty map if no settings found', async () => {
      const initiativeId = new ObjectId();
      findOneStub.returns(createMongooseModel(null));

      const result = await initiativeSettingsService.getDisplayDocumentsMap(initiativeId);

      expect(result).to.deep.equal({
        [DocumentMediaType.Image]: [],
        [DocumentMediaType.Video]: [],
        [DocumentMediaType.File]: [],
      });
    });
  });

  describe('handleUpdateDisplayDocuments', () => {
    it('should update display documents with valid document ids', async () => {
      const added = new Date();
      const initiativeId = new ObjectId();
      const docId1 = new ObjectId();
      const docId2 = new ObjectId(); // This one is not valid
      const docId3 = new ObjectId(); // This one is removed
      const data = { addedIds: [docId1, docId2], removedIds: [docId3] };
      const validDocuments = [{ _id: docId1 }, { _id: docId3 }];
      const updatedSettings = { displayDocuments: [{ documentId: docId1, added }] };

      findStub.returns(createMongooseModel(validDocuments));
      findOneAndUpdateStub.returns(createMongooseModel(updatedSettings));

      const result = await initiativeSettingsService.handleUpdateDisplayDocuments(initiativeId, data);

      expect(findStub.calledOnceWith({ _id: { $in: data.addedIds }, ownerId: initiativeId }, { _id: 1 })).to.be.true;
      expect(
        findOneAndUpdateStub.calledOnceWith(
          { initiativeId },
          sinon.match((pipeline) => {
            const step = pipeline[0];
            const setUnion = step?.$set?.displayDocuments?.$setUnion;

            // Check structure of pipeline
            return (
              Array.isArray(pipeline) &&
              step?.$set &&
              Array.isArray(setUnion) &&
              setUnion.length === 2 &&
              setUnion[0]?.$filter?.input?.$ifNull &&
              (setUnion[1] as DisplayDocument[]).every(
                (doc, i) => doc.documentId === validDocuments[i]._id && 'added' in doc
              )
            );
          }),
          { upsert: true, new: true }
        )
      ).to.be.true;
      expect(result).to.deep.equal(updatedSettings.displayDocuments);
    });
  });
});
