/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { expect } from 'chai';
import { createSandbox } from 'sinon';
import { getExcel } from '../../../server/service/file/Excel';
import { getDataImporter } from '../../../server/service/survey/DataImporter';
import { FileImporter } from '../../../server/service/survey/FileImporter';
import { FileParserType } from '../../../server/service/survey/transfer/parserTypes';
import { testLogger } from '../../factories/logger';
import { surveyOne } from '../../fixtures/survey';
import { userOne } from '../../fixtures/userFixtures';
import { EMISSION_METRICS_MAPPING } from '../../../server/service/survey/constants';

describe('SGXImportSheet', function () {
  const sandbox = createSandbox();
  const excel = getExcel();
  const filePath = __dirname + '/SGX IMPORT SPREADSHEET TEMPLATE V2.xlsx';
  const questionSheetName = 'Questions';
  const options = {
    type: FileParserType.Xlsx,
    filepath: filePath,
    tab: { name: questionSheetName },
    metricsMapping: EMISSION_METRICS_MAPPING,
  };

  afterEach(() => {
    sandbox.restore();
  });

  it(`should find ${questionSheetName} sheet and load data`, async function () {
    const data = await excel.loadFile(filePath, options);
    const [firstRow, secondRow] = data;

    expect(firstRow).to.deep.equal({ 'Metric (Locked)': 'ENVIRONMENT - EMISSIONS' });
    expect(secondRow).to.deep.equal({
      'Sorting column': 1,
      'Metric Code (Locked)': 'sgx-core-1a',
      Number: 1,
      'Metric (Locked)': 'SGX Core 1a) GHG (CO2) Absolute emissions – total',
      'Input Type': 'number',
      'Value 1': 7,
      Unit: 'Metric Tonnes (mt)',
      'Reporting/NA/NR': 'Reporting',
      Verified: 'Yes',
    });
  });

  it('should populate mapping metrics if import sheet data has answered values', async () => {
    const dataImporter = getDataImporter();
    const fileImporter = new FileImporter(dataImporter, testLogger);
    const extendImportDataSpy = sandbox.spy(dataImporter, 'extendImportData');

    await fileImporter.processFile(surveyOne, userOne, options);

    const expectedExtendedImportData = [
      // rows from import sheet
      { 'Metric (Locked)': 'ENVIRONMENT - EMISSIONS' },
      {
        'Sorting column': 1,
        'Metric Code (Locked)': 'sgx-core-1a',
        Number: 1,
        'Metric (Locked)': 'SGX Core 1a) GHG (CO2) Absolute emissions – total',
        'Input Type': 'number',
        'Value 1': 7,
        Unit: 'Metric Tonnes (mt)',
        'Reporting/NA/NR': 'Reporting',
        Verified: 'Yes',
      },
      {
        'Sorting column': 2,
        'Metric Code (Locked)': 'gri/2020/305-1/a',
        Number: 2,
        'Metric (Locked)': 'SGX Core 1b) GHG (CO2) Absolute emissions – scope 1',
        'Input Type': 'number',
        'Value 1': 1,
        Unit: 'Metric Tonnes (mt)',
        'Reporting/NA/NR': 'Reporting',
        Verified: 'Yes',
      },
      {
        'Sorting column': 3,
        'Metric Code (Locked)': 'gri/2020/305-2/a',
        Number: 3,
        'Metric (Locked)': 'SGX Core 1c) GHG (CO2) Absolute emissions – scope 2 (location-based)',
        'Input Type': 'number',
        'Value 1': 2,
        Unit: 'Metric Tonnes (mt)',
        'Reporting/NA/NR': 'Reporting',
        Verified: 'Yes',
      },
      {
        'Sorting column': 4,
        'Metric Code (Locked)': 'gri/2020/305-2/b',
        Number: 4,
        'Metric (Locked)': 'SGX Core 1d) GHG (CO2) Absolute emissions – scope 2 (market-based)',
        'Input Type': 'number',
        Unit: 'Metric Tonnes (mt)',
        'Reporting/NA/NR': 'Reporting',
        Verified: 'Yes',
      },
      {
        'Sorting column': 5,
        'Metric Code (Locked)': 'gri/2020/305-3/a',
        Number: 5,
        'Metric (Locked)': 'SGX Core 1e) GHG (CO2) Absolute emissions – scope 3',
        'Input Type': 'number',
        Unit: 'Metric Tonnes (mt)',
        'Reporting/NA/NR': 'Reporting',
        Verified: 'Yes',
      },
      {
        'Metric (Locked)': 'ENVIRONMENT - EMISSIONS INTENSITY',
        'Sub-Metric (Locked)':
          'Emissions intensity is calculted by dividing total emissions in row 1, by revenue, headcount, floor area or somehting else',
      },
      {
        'Sorting column': 6,
        'Metric Code (Locked)': 'organizational-metric/revenue',
        Number: 6,
        'Metric (Locked)': 'If you use revenue to calculate intensity, enter your revenue in $SGD in column H',
        'Input Type': 'number',
        'Value 1': 1000,
      },
      {
        'Sorting column': 7,
        'Metric Code (Locked)': 'organizational-metric/floorarea',
        Number: 7,
        'Metric (Locked)':
          'If you use floor area to calculate intensity, enter the number and select the unit unit you use',
        'Sub-Metric (Locked)': 'What is the floor area value? And select the unit too',
        'Input Type': 'number',
        'Value 1': 3000,
        Unit: 'Square Kilometers (km2)',
      },
      {
        'Metric Code (Locked)': 'organizational-metric/headcount',
        Number: 8,
        'Metric (Locked)':
          'If you use headcount/number of staff to calculate intensity, enter the total number of staff in column H',
        'Input Type': 'number',
        'Value 1': 2000,
      },
      {
        'Sorting column': 8,
        'Metric Code (Locked)': 'organizational-metric/other',
        Number: 9,
        'Metric (Locked)':
          'If you use something else to calculate intensity, enter the figure in column H and type what the unit is in column I',
        'Sub-Metric (Locked)':
          "What is the 'Other' organisation unit or metric that you are using to calculate emissions intensity? (as the denomiator)",
        'Input Type': 'number',
        'Value 1': 4000,
      },
      // rows populated from mapping metrics
      // sgx-custom-56
      {
        'Metric Code (Locked)': 'sgx-custom-56',
        columnCode: 'sgx56-rev',
        unit: undefined,
        notApplicableType: undefined,
        note: undefined,
        'Value 1': 1000,
      },
      {
        'Metric Code (Locked)': 'sgx-custom-56',
        columnCode: 'sgx56-total-emissions',
        Unit: 'Metric Tonnes (mt)',
        'Reporting/NA/NR': 'Reporting',
        note: undefined,
        'Value 1': 7,
        'Value 2': 7,
        'Value 3': 7,
        'Value 4': 7,
      },
      {
        'Metric Code (Locked)': 'sgx-custom-56',
        columnCode: 'sgx56-denominator',
        unit: undefined,
        notApplicableType: undefined,
        note: undefined,
        'Value 1': 'sgx56-denominator1',
        'Value 2': 'sgx56-denominator2',
        Unit: 'Square Kilometers (km2)',
        'Value 3': 'sgx56-denominator3',
        'Value 4': 'sgx56-denominator4',
      },
      {
        'Metric Code (Locked)': 'sgx-custom-56',
        columnCode: 'sgx56-headcount',
        unit: undefined,
        notApplicableType: undefined,
        note: undefined,
        'Value 2': 2000,
      },
      {
        'Metric Code (Locked)': 'sgx-custom-56',
        columnCode: 'sgx56-floorarea',
        Unit: 'Square Kilometers (km2)',
        notApplicableType: undefined,
        note: undefined,
        'Value 3': 3000,
      },
      {
        'Metric Code (Locked)': 'sgx-custom-56',
        columnCode: 'sgx56-other-org-metric-amount',
        unit: undefined,
        notApplicableType: undefined,
        note: undefined,
        'Value 4': 4000,
      },
      // sgx-custom-57
      {
        'Metric Code (Locked)': 'sgx-custom-57',
        columnCode: 'sgx57-rev',
        unit: undefined,
        notApplicableType: undefined,
        note: undefined,
        'Value 1': 1000,
      },
      {
        'Metric Code (Locked)': 'sgx-custom-57',
        columnCode: 'sgx57-scope1-emissions',
        Unit: 'Metric Tonnes (mt)',
        'Reporting/NA/NR': 'Reporting',
        note: undefined,
        'Value 1': 1,
        'Value 2': 1,
        'Value 3': 1,
        'Value 4': 1,
      },
      {
        'Metric Code (Locked)': 'sgx-custom-57',
        columnCode: 'sgx57-denominator',
        unit: undefined,
        notApplicableType: undefined,
        note: undefined,
        'Value 1': 'sgx56-denominator1',
        'Value 2': 'sgx56-denominator2',
        Unit: 'Square Kilometers (km2)',
        'Value 3': 'sgx56-denominator3',
        'Value 4': 'sgx56-denominator4',
      },
      {
        'Metric Code (Locked)': 'sgx-custom-57',
        columnCode: 'sgx57-headcount',
        unit: undefined,
        notApplicableType: undefined,
        note: undefined,
        'Value 2': 2000,
      },
      {
        'Metric Code (Locked)': 'sgx-custom-57',
        columnCode: 'sgx57-floorarea',
        Unit: 'Square Kilometers (km2)',
        notApplicableType: undefined,
        note: undefined,
        'Value 3': 3000,
      },
      {
        'Metric Code (Locked)': 'sgx-custom-57',
        columnCode: 'sgx57-other-org-metric-amount',
        unit: undefined,
        notApplicableType: undefined,
        note: undefined,
        'Value 4': 4000,
      },
      // sgx-custom-58 - retrieve data from alternate row to use as a fallback when main row value is missing
      {
        'Metric Code (Locked)': 'sgx-custom-58',
        columnCode: 'sgx58-revenue',
        unit: undefined,
        notApplicableType: undefined,
        note: undefined,
        'Value 1': 1000,
      },
      {
        'Metric Code (Locked)': 'sgx-custom-58',
        columnCode: 'sgx58-scope2-emissions',
        Unit: 'Metric Tonnes (mt)',
        'Reporting/NA/NR': 'Reporting',
        note: undefined,
        'Value 1': 2,
        'Value 2': 2,
        'Value 3': 2,
        'Value 4': 2,
      },
      {
        'Metric Code (Locked)': 'sgx-custom-58',
        columnCode: 'sgx58-denominator',
        unit: undefined,
        notApplicableType: undefined,
        note: undefined,
        'Value 1': 'sgx56-denominator1',
        'Value 2': 'sgx56-denominator2',
        Unit: 'Square Kilometers (km2)',
        'Value 3': 'sgx56-denominator3',
        'Value 4': 'sgx56-denominator4',
      },
      {
        'Metric Code (Locked)': 'sgx-custom-58',
        columnCode: 'sgx58-headcount',
        unit: undefined,
        notApplicableType: undefined,
        note: undefined,
        'Value 2': 2000,
      },
      {
        'Metric Code (Locked)': 'sgx-custom-58',
        columnCode: 'sgx58-floorarea',
        Unit: 'Square Kilometers (km2)',
        notApplicableType: undefined,
        note: undefined,
        'Value 3': 3000,
      },
      {
        'Metric Code (Locked)': 'sgx-custom-58',
        columnCode: 'sgx58-other-org-metric-amount',
        unit: undefined,
        notApplicableType: undefined,
        note: undefined,
        'Value 4': 4000,
      },
      // sgx-custom-60 is skipped due to incomplete required columns.
    ];

    expect(extendImportDataSpy.returnValues[0]).to.deep.equal(expectedExtendedImportData);
  });
});
