import { expect } from 'chai';
import sinon from 'sinon';
import { SurveyAutoAnswerService } from '../../../server/service/ai/survey-auto-answer/SurveyAutoAnswerService';
import { ObjectId } from 'bson';
import UniversalTrackerValue from '../../../server/models/universalTrackerValue';
import { ActionList, DataPeriods } from '../../../server/service/utr/constants';
import UniversalTracker from '../../../server/models/universalTracker';
import { UtrValueType } from '../../../server/models/public/universalTrackerType';
import { ValueDataSourceType } from '../../../server/models/public/universalTrackerValueType';
import { createMongooseModel } from '../../../tests/setup';
import { initiativeOneSimple } from '../../../tests/fixtures/initiativeFixtures';
import { userIdOne } from '../../../tests/fixtures/userFixtures';
import { wwgLogger } from '../../../server/service/wwgLogger';
import { getBackgroundJobService } from '../../../server/service/background-process/BackgroundJobService';
import { getUtrvAssistantInputManager } from '../../../server/service/ai/utrv-assistant/UtrvAssistantInputManager';
import { getPredictedDataResolver } from '../../../server/service/ai/PredictedDataResolver';
import { getAiService } from '../../../server/service/ai/service';
import { universalTrackerOne } from '../../../tests/fixtures/universalTrackerFixtures';
import { createUtrv } from '../../../tests/fixtures/compositeUTRVFixtures';
import { getChatGPT, OPENAI_MODEL } from '../../../server/service/ai/models/ChatGPT';
import { AIUtrvSuggestion } from '../../../server/service/ai/types';
import { UtrvPromptInput } from '../../../server/service/ai/utrv-assistant/types';
import { getAppSettingsService } from '../../../server/service/app-settings/AppSettingsService';

const mockAIResponse: AIUtrvSuggestion = {
  questionExplanation: '',
  bestPractice: [],
  keyInfo: [],
  suggestedEvidence: {
    primaryDocumentation: [],
    supportingDocumentation: [],
  },
  whereToFind: {
    externalSource: [],
    internalSource: [],
  },
};

const mockPromptInput: UtrvPromptInput = {
  title: 'test',
  period: DataPeriods.Monthly,
  effectiveDate: '2019-02-05T12:06:21.187Z',
  type: 'test',
  previousUtrvs: [],
  valueType: UtrValueType.Number,
};

const createAssistantResponse = (predictedAnswer: { answer: string } | undefined) => ({
  content: {
    ...mockAIResponse,
    predictedAnswer,
  },
  modelVersion: OPENAI_MODEL,
  promptInput: mockPromptInput,
});

describe('SurveyAutoAnswerService', () => {
  const assistantInputManager = getUtrvAssistantInputManager();
  const dataResolver = getPredictedDataResolver();
  const aiService = getAiService();
  const appSettingsService = getAppSettingsService();
  const aiModel = getChatGPT();

  const service: SurveyAutoAnswerService = new SurveyAutoAnswerService(
    wwgLogger,
    getBackgroundJobService(),
    dataResolver,
    aiService,
    appSettingsService,
    aiModel,
  );
  let sandbox: sinon.SinonSandbox;
  const mockJobId = new ObjectId();
  const mockUtrvId = new ObjectId();
  const baseUtrv = createUtrv(
    mockUtrvId,
    universalTrackerOne._id,
    undefined,
    { status: ActionList.Created },
    initiativeOneSimple._id
  );

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    sandbox.stub(aiService, 'getFurtherNotesDraft').resolves({ content: '' });
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should create instance', () => {
    expect(service).to.be.instanceOf(SurveyAutoAnswerService);
  });

  it('should return an error if the utrv is not found', async () => {
    sandbox.stub(UniversalTrackerValue, 'findOne').returns(createMongooseModel(null));

    const result = await service.processAnswerUtrv({
      initiative: initiativeOneSimple,
      utrvId: mockUtrvId,
      userId: userIdOne,
      jobId: mockJobId,
    });

    expect(result.isSuccess).to.be.false;
    expect(result.errorMessage).to.eqls(`Utrv not matched: ${mockUtrvId}`);
  });

  it('should apply the predicted answer successfully', async () => {
    const mockUtr = { ...universalTrackerOne, valueType: UtrValueType.Text };
    const mockUtrv = {
      ...baseUtrv,
      universalTracker: mockUtr,
      save: sandbox.stub().resolves(undefined),
    };

    sandbox.stub(UniversalTrackerValue, 'findOne').returns(createMongooseModel(mockUtrv));
    sandbox.stub(assistantInputManager, 'prepareUtrvPromptInput').resolves();
    sandbox.stub(aiService, 'getUtrvAssistantResponse').resolves(createAssistantResponse({ answer: 'Predicted value' }));
    sandbox.stub(UniversalTracker, 'findById').returns(createMongooseModel(mockUtr));
    sandbox.stub(dataResolver, 'getValueDataPredictedAnswer').returns({
      value: 65,
      valueData: {},
    });

    const result = await service.processAnswerUtrv({
      initiative: initiativeOneSimple,
      utrvId: mockUtrvId,
      userId: userIdOne,
      jobId: mockJobId,
    });

    expect(result.isSuccess).to.be.true;
    expect(result.value).to.equal(65);
    expect(result.valueData?.source).to.deep.equal({
      type: ValueDataSourceType.AIAutoAnswer,
      data: {
        model: OPENAI_MODEL,
        backgroundJobId: mockJobId,
      },
    });
    expect(mockUtrv.save.calledOnce).to.be.true;
  });

  it('should return an error if AI did not provide a predicted answer', async () => {
    const mockUtr = { ...universalTrackerOne, valueType: UtrValueType.Text };
    const mockUtrv = {
      universalTracker: mockUtr,
      save: sandbox.stub().resolves(undefined),
    };

    sandbox.stub(UniversalTrackerValue, 'findOne').returns(createMongooseModel(mockUtrv));
    sandbox.stub(assistantInputManager, 'prepareUtrvPromptInput').resolves();
    sandbox.stub(aiService, 'getUtrvAssistantResponse').resolves(createAssistantResponse(undefined));

    const result = await service.processAnswerUtrv({
      initiative: initiativeOneSimple,
      utrvId: mockUtrvId,
      userId: userIdOne,
      jobId: mockJobId,
    });

    expect(result.isSuccess).to.be.false;
    expect(result.errorMessage).to.include('Not found any predicted answer from AI');
  });

  it('should return an error if not found utr', async () => {
    const mockUtr = { ...universalTrackerOne, valueType: UtrValueType.Text };
    const mockUtrv = {
      ...baseUtrv,
      universalTracker: mockUtr,
      save: sandbox.stub().resolves(undefined),
    };

    sandbox.stub(UniversalTrackerValue, 'findOne').returns(createMongooseModel(mockUtrv));
    sandbox.stub(assistantInputManager, 'prepareUtrvPromptInput').resolves();
    sandbox.stub(aiService, 'getUtrvAssistantResponse').resolves(createAssistantResponse({ answer: 'Predicted value' }));

    sandbox.stub(UniversalTracker, 'findById').returns(createMongooseModel(undefined));

    const result = await service.processAnswerUtrv({
      initiative: initiativeOneSimple,
      utrvId: mockUtrvId,
      userId: userIdOne,
      jobId: mockJobId,
    });

    expect(result.isSuccess).to.be.false;
    expect(result.errorMessage).to.include('Not found a UTR of this utrv');
  });

  it('should return an error if utr is not a supported type', async () => {
    const mockUtr = { ...universalTrackerOne, valueType: UtrValueType.Date };
    const mockUtrv = {
      ...baseUtrv,
      universalTracker: mockUtr,
      save: sandbox.stub().resolves(undefined),
    };

    sandbox.stub(UniversalTrackerValue, 'findOne').returns(createMongooseModel(mockUtrv));
    sandbox.stub(assistantInputManager, 'prepareUtrvPromptInput').resolves();
    sandbox.stub(aiService, 'getUtrvAssistantResponse').resolves(createAssistantResponse({ answer: 'Predicted value' }));

    sandbox.stub(UniversalTracker, 'findById').returns(createMongooseModel(mockUtr));

    const result = await service.processAnswerUtrv({
      initiative: initiativeOneSimple,
      utrvId: mockUtrvId,
      userId: userIdOne,
      jobId: mockJobId,
    });

    expect(result.isSuccess).to.be.false;
    expect(result.errorMessage).to.include('Not yet support this metric type');
  });

  it('should return an error if not have data to update', async () => {
    const mockUtr = { ...universalTrackerOne, valueType: UtrValueType.Text };
    const mockUtrv = {
      ...baseUtrv,
      universalTracker: mockUtr,
      save: sandbox.stub().resolves(undefined),
    };

    sandbox.stub(UniversalTrackerValue, 'findOne').returns(createMongooseModel(mockUtrv));
    sandbox.stub(assistantInputManager, 'prepareUtrvPromptInput').resolves();
    sandbox.stub(aiService, 'getUtrvAssistantResponse').resolves(createAssistantResponse({ answer: 'Predicted value' }));
    sandbox.stub(UniversalTracker, 'findById').returns(createMongooseModel(mockUtr));
    sandbox.stub(dataResolver, 'getValueDataPredictedAnswer').returns({
      value: undefined,
      valueData: undefined,
    });

    const result = await service.processAnswerUtrv({
      initiative: initiativeOneSimple,
      utrvId: mockUtrvId,
      userId: userIdOne,
      jobId: mockJobId,
    });

    expect(result.isSuccess).to.be.false;
    expect(result.errorMessage).to.include('Not have actual data to update');
  });

  it('should use documents to answer if provided', async () => {
    const mockUtr = { ...universalTrackerOne, valueType: UtrValueType.Text };
    const mockUtrv = {
      ...baseUtrv,
      universalTracker: mockUtr,
      save: sandbox.stub().resolves(undefined),
    };

    sandbox.stub(UniversalTrackerValue, 'findOne').returns(createMongooseModel(mockUtrv));
    const getDocumentUtrvAssistantResponseStub = sandbox
      .stub(aiService, 'getDocumentUtrvAssistantResponse')
      .resolves(createAssistantResponse({ answer: 'Predicted value' }));
    sandbox.stub(UniversalTracker, 'findById').returns(createMongooseModel(mockUtr));
    sandbox.stub(dataResolver, 'getValueDataPredictedAnswer').returns({
      value: undefined,
      valueData: { data: 'Predicted value' },
    });

    const result = await service.processAnswerUtrv({
      initiative: initiativeOneSimple,
      utrvId: mockUtrvId,
      userId: userIdOne,
      jobId: mockJobId,
      assistantId: 'assistantId',
      relatedDocumentIds: ['documentId'],
    });

    expect(result.isSuccess).to.be.true;
    expect(result.valueData?.data).to.equal('Predicted value');
    expect(result.valueData?.source).to.deep.equal({
      type: ValueDataSourceType.AIAutoAnswer,
      data: {
        model: OPENAI_MODEL,
        backgroundJobId: mockJobId,
      },
    });
    expect(getDocumentUtrvAssistantResponseStub.calledOnce).to.be.true;
  });
});
