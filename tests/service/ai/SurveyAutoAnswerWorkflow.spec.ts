import { expect } from 'chai';
import {
  getSurveyAutoAnswerWorkflow,
  SurveyAutoAnswerWorkflow,
} from '../../../server/service/ai/survey-auto-answer/SurveyAutoAnswerWorkflow';
import sinon from 'sinon';
import { ObjectId } from 'bson';
import { getSurveyAutoAnswerService } from '../../../server/service/ai/survey-auto-answer/SurveyAutoAnswerService';
import { getNotificationService } from '../../../server/service/notification/NotificationService';
import { getRootInitiativeService } from '../../../server/service/organization/RootInitiativeService';
import { getAIDocumentLibraryScanService } from '../../../server/service/ai/document-utr-mapping/AIDocumentUtrMappingService';
import { getAIDocumentService } from '../../../server/service/ai/AIDocumentService';
import <PERSON><PERSON><PERSON>, { JobStatus, JobType, TaskStatus, TaskType } from '../../../server/models/backgroundJob';
import { getBackgroundJobService } from '../../../server/service/background-process/BackgroundJobService';
import Initiative from '../../../server/models/initiative';
import User from '../../../server/models/user';
import { LEVEL } from '../../../server/service/event/Events';
import {
  ProcessedAnswerResult,
  ProcessedContext,
  TaskAIAutoAnswerCleanup,
  TaskAIAutoAnswerComplete,
  TaskAIAutoAnswerProcess,
} from '../../../server/service/ai/survey-auto-answer/types';
import { testLogger } from '../../factories/logger';
import { initiativeOneSimpleId } from '../../fixtures/initiativeFixtures';
import { createJobModel, createTask, initiativeId, surveyId, userId } from '../../fixtures/backgroundJobFixtures';
import { utrValueOneId, utrValueThreeId, utrValueTwoId } from '../../fixtures/universalTrackerValueFixtures';
import ContextError from '../../../server/error/ContextError';
import { createMongooseModel } from '../../setup';

describe('SurveyAutoAnswerWorkflow', () => {
  const sandbox = sinon.createSandbox();
  const surveyAutoAnswerService = getSurveyAutoAnswerService();
  const notificationService = getNotificationService();
  const rootInitiativeService = getRootInitiativeService();
  const documentUtrMappingService = getAIDocumentLibraryScanService();
  const aiDocumentService = getAIDocumentService();
  const backgroundJobService = getBackgroundJobService();

  const mockJobId = new ObjectId();

  const workflow = new SurveyAutoAnswerWorkflow(
    testLogger,
    surveyAutoAnswerService,
    notificationService,
    rootInitiativeService,
    documentUtrMappingService,
    aiDocumentService
  );

  it('should create instance', () => {
    const instance = getSurveyAutoAnswerWorkflow();
    expect(instance).to.be.instanceOf(SurveyAutoAnswerWorkflow);
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('createOrRetry', () => {
    const workflowCreateInput = {
      initiativeId,
      surveyId,
      userId,
      useDocumentLibrary: false,
      isOverwriteMetric: false,
    };

    it('should create a new job if no existing job is found', async () => {
      const idempotencyKey = 'idempotencyKey';
      const findOneStub = sandbox.stub(BackgroundJob, 'findOne').resolves(null);
      sandbox.stub(workflow, 'getIdempotencyKey').returns(idempotencyKey);
      const createJobStub = sandbox.stub(surveyAutoAnswerService, 'createJob').resolves({
        jobId: 'newJobId',
        status: JobStatus.Pending,
      });

      const result = await workflow.createOrRetry(workflowCreateInput);

      expect(result.jobId).to.equal('newJobId');
      expect(result.status).to.equal(JobStatus.Pending);
      expect(createJobStub.calledOnceWith({ ...workflowCreateInput, idempotencyKey })).to.be.true;
      expect(findOneStub.calledOnce).to.be.true;
    });

    it('should re-run an existing pending/processing job and return its details', async () => {
      const existingJob = createJobModel({
        _id: mockJobId,
        status: JobStatus.Processing,
        type: JobType.AIAutoAnswerSurvey,
        initiativeId: initiativeOneSimpleId,
      });
      sandbox.stub(BackgroundJob, 'findOne').resolves(existingJob);
      const runFromJobStub = sandbox.stub(backgroundJobService, 'runFromJob').resolves();
      const createJobStub = sandbox.stub(surveyAutoAnswerService, 'createJob');
      const result = await workflow.createOrRetry(workflowCreateInput);

      expect(result.jobId).to.equal(existingJob._id.toString());
      expect(result.status).to.equal(existingJob.status);
      expect(runFromJobStub.calledOnceWith(existingJob)).to.be.true;
      expect(createJobStub.notCalled).to.be.true;
    });
  });

  describe('processSetupTask', () => {
    const mockUtrCode1 = 'UTR001';
    const mockUtrCode2 = 'UTR002';

    let mockJob: any;

    beforeEach(() => {
      mockJob = createJobModel({
        _id: mockJobId,
        initiativeId,
        userId,
        tasks: [
          createTask({
            type: TaskType.AIAutoAnswerSetup,
            status: TaskStatus.Pending,
            data: {
              utrvs: [
                { _id: utrValueOneId, utrCode: mockUtrCode1 },
                { _id: utrValueTwoId, utrCode: mockUtrCode2 },
              ],
              surveyId,
              useDocumentLibrary: false,
              isOverwriteMetric: false,
            },
          }),
        ],
        logs: [],
      });
    });

    it('should add AIAutoAnswerProcess tasks and AIAutoAnswerComplete task when useDocumentLibrary is false', async () => {
      const { job } = await workflow.processTask(mockJob, mockJob.tasks[0]);

      expect(job.tasks.length).to.equal(4); // Setup + 2 Process + 1 Complete
      expect(job.tasks[0].type).to.equal(TaskType.AIAutoAnswerSetup);
      expect(job.tasks[0].status).to.equal(TaskStatus.Completed);
      expect(job.tasks[1].type).to.equal(TaskType.AIAutoAnswerProcess);
      expect((job.tasks[1] as TaskAIAutoAnswerProcess).data.utrvId).to.deep.equal(utrValueOneId);
      expect(job.tasks[2].type).to.equal(TaskType.AIAutoAnswerProcess);
      expect((job.tasks[2] as TaskAIAutoAnswerProcess).data.utrvId).to.deep.equal(utrValueTwoId);
      expect(job.tasks[3].type).to.equal(TaskType.AIAutoAnswerComplete);
    });

    it('should add AIAutoAnswerPrepareDocuments task when useDocumentLibrary is true', async () => {
      mockJob.tasks[0].data.useDocumentLibrary = true;

      const getMappingsStub = sandbox.stub(documentUtrMappingService, 'getRelevantDocumentUtrMappings').resolves([]);

      const { job } = await workflow.processTask(mockJob, mockJob.tasks[0]);

      expect(job.tasks.length).to.equal(2); // Setup + 1 PrepareDocuments
      expect(job.tasks[0].status).to.equal(TaskStatus.Completed);
      expect(job.tasks[1].type).to.equal(TaskType.AIAutoAnswerPrepareDocuments);
      expect(
        getMappingsStub.calledOnceWith({
          utrCodes: [mockUtrCode1, mockUtrCode2],
          initiativeId: mockJob.initiativeId,
        })
      ).to.be.true;
    });

    it('should handle no UTRVs gracefully', async () => {
      mockJob.tasks[0].data.utrvs = [];
      const { job } = await workflow.processTask(mockJob, mockJob.tasks[0]);

      expect(job.tasks.length).to.equal(1); // setup task
      expect(job.tasks[0].status).to.equal(TaskStatus.Completed);
    });
  });

  describe('prepareDocumentsTask', () => {
    const mockDocumentId1 = new ObjectId();
    const mockDocumentId2 = new ObjectId();
    const mockUploadedFileId1 = 'file123';
    const mockUploadedFileId2 = 'file456';
    const mockAssistantId = 'asst_mock';

    let mockJob: any;

    beforeEach(() => {
      mockJob = createJobModel({
        _id: mockJobId,
        tasks: [
          createTask({
            type: TaskType.AIAutoAnswerPrepareDocuments,
            status: TaskStatus.Pending,
            data: {
              mappings: [
                { documentId: mockDocumentId1, utrs: [{ code: 'UTR001', score: 0.8 }], path: 'path1', metadata: {} },
                { documentId: mockDocumentId2, utrs: [{ code: 'UTR002', score: 0.9 }], path: 'path2', metadata: {} },
              ],
              utrvs: [
                { _id: utrValueOneId, utrCode: 'UTR001' },
                { _id: utrValueTwoId, utrCode: 'UTR002' },
              ],
              surveyId,
              isOverwriteMetric: false,
            },
          }),
        ],
        logs: [],
      });
    });

    it('should upload documents, create process tasks with related document IDs and assistant ID, and add cleanup/complete tasks', async () => {
      const uploadDocumentStub = sandbox.stub(aiDocumentService, 'uploadDocument');
      uploadDocumentStub
        .withArgs(sandbox.match({ _id: mockDocumentId1, path: 'path1' }))
        .resolves({ documentId: mockDocumentId1, uploadedFileId: mockUploadedFileId1 });
      uploadDocumentStub
        .withArgs(sandbox.match({ _id: mockDocumentId2, path: 'path2' }))
        .resolves({ documentId: mockDocumentId2, uploadedFileId: mockUploadedFileId2 });

      const getOrCreateAssistantIdStub = sandbox
        .stub(surveyAutoAnswerService, 'getOrCreateAutoAnswerAssistantId')
        .resolves(mockAssistantId);

      const { job } = await workflow.processTask(mockJob, mockJob.tasks[0]);

      expect(uploadDocumentStub.calledTwice).to.be.true;
      expect(getOrCreateAssistantIdStub.calledOnce).to.be.true;

      expect(job.tasks.length).to.equal(5); // PrepareDocs + 2 Process + Cleanup + Complete
      expect(job.tasks[0].status).to.equal(TaskStatus.Completed);

      const processTask1 = job.tasks[1] as TaskAIAutoAnswerProcess;
      expect(processTask1.type).to.equal(TaskType.AIAutoAnswerProcess);
      expect(processTask1.data.utrvId).to.deep.equal(utrValueOneId);
      expect(processTask1.data.relatedDocumentIds).to.deep.equal([mockUploadedFileId1]);
      expect(processTask1.data.assistantId).to.equal(mockAssistantId);

      const processTask2 = job.tasks[2] as TaskAIAutoAnswerProcess;
      expect(processTask2.type).to.equal(TaskType.AIAutoAnswerProcess);
      expect(processTask2.data.utrvId).to.deep.equal(utrValueTwoId);
      expect(processTask2.data.relatedDocumentIds).to.deep.equal([mockUploadedFileId2]);
      expect(processTask2.data.assistantId).to.equal(mockAssistantId);

      const cleanupTask = job.tasks[3] as TaskAIAutoAnswerCleanup;
      expect(cleanupTask.type).to.equal(TaskType.AIAutoAnswerCleanup);
      expect(cleanupTask.data.documentIdMap).to.deep.equal({
        [mockDocumentId1.toString()]: mockUploadedFileId1,
        [mockDocumentId2.toString()]: mockUploadedFileId2,
      });
      expect(job.tasks[4].type).to.equal(TaskType.AIAutoAnswerComplete);
    });
  });

  describe('processAnswerTask', () => {
    let mockJob: any;

    beforeEach(() => {
      mockJob = createJobModel({
        _id: mockJobId,
        initiativeId: initiativeOneSimpleId,
        userId,
        tasks: [
          createTask({
            type: TaskType.AIAutoAnswerProcess,
            status: TaskStatus.Pending,
            data: {
              utrvId: utrValueOneId,
              isOverwriteMetric: false,
            },
          }),
        ],
        logs: [],
      });
      sandbox.stub(Initiative, 'findById').returns(createMongooseModel({ _id: initiativeOneSimpleId }));
    });

    it('should process answer successfully and update task data', async () => {
      const processAnswerUtrvStub = sandbox.stub(surveyAutoAnswerService, 'processAnswerUtrv').resolves({
        isSuccess: true,
        value: 100,
      } as ProcessedAnswerResult);

      const { job } = await workflow.processTask(mockJob, mockJob.tasks[0]);
      const task = job.tasks[0] as TaskAIAutoAnswerProcess<ProcessedContext>;

      expect(
        processAnswerUtrvStub.calledOnceWith(
          sinon.match({
            utrvId: utrValueOneId,
            userId,
            jobId: mockJobId,
            isOverwriteMetric: false,
          })
        )
      ).to.be.true;
      expect(task.status).to.equal(TaskStatus.Completed);
      expect(task.data.isSuccess).to.be.true;
      expect(task.data.errorMessage).to.be.undefined;
    });

    it('should process answer with an error and update task data', async () => {
      const processAnswerUtrvStub = sandbox.stub(surveyAutoAnswerService, 'processAnswerUtrv').resolves({
        isSuccess: false,
        errorMessage: 'AI failed',
      } as ProcessedAnswerResult);

      const { job } = await workflow.processTask(mockJob, mockJob.tasks[0]);
      const task = job.tasks[0] as TaskAIAutoAnswerProcess<ProcessedContext>;

      expect(
        processAnswerUtrvStub.calledOnceWith(
          sinon.match({
            utrvId: utrValueOneId,
            userId,
            jobId: mockJobId,
            isOverwriteMetric: false,
          })
        )
      ).to.be.true;
      expect(task.status).to.equal(TaskStatus.Completed);
      expect(task.data.isSuccess).to.be.false;
      expect(task.data.errorMessage).to.equal('AI failed');
    });
  });

  describe('completeTaskHandler', () => {
    let mockJob: any;

    beforeEach(() => {
      mockJob = createJobModel({
        _id: mockJobId,
        initiativeId: initiativeOneSimpleId,
        userId,
        tasks: [
          createTask({
            type: TaskType.AIAutoAnswerSetup,
            data: { surveyId },
          }),
          createTask({
            type: TaskType.AIAutoAnswerProcess,
            data: { utrvId: utrValueOneId, isSuccess: true },
          }),
          createTask({
            type: TaskType.AIAutoAnswerProcess,
            data: { utrvId: utrValueTwoId, isSuccess: false, errorMessage: 'Error 1' },
          }),
          createTask({
            type: TaskType.AIAutoAnswerProcess,
            data: { utrvId: utrValueThreeId, isSuccess: true },
          }),
          createTask({
            type: TaskType.AIAutoAnswerComplete,
            status: TaskStatus.Pending,
            data: { completedUtrvs: [], errorUtrvs: [] },
          }),
        ],
        attributes: [],
        logs: [],
      });
      sandbox.stub(User, 'findById').returns(createMongooseModel({ _id: userId }));
      sandbox.stub(Initiative, 'findById').returns(createMongooseModel({ _id: initiativeOneSimpleId }));
      sandbox
        .stub(rootInitiativeService, 'getOrganization')
        .resolves({ _id: new ObjectId(), appConfigCode: 'testOrg' });
    });

    it('should aggregate completed and error UTRVs and send notification', async () => {
      const createNotificationStub = sandbox.stub(notificationService, 'createNotification').resolves();

      const { job } = await workflow.processTask(mockJob, mockJob.tasks[4]);
      const task = job.tasks[4] as TaskAIAutoAnswerComplete;

      expect(task.status).to.equal(TaskStatus.Completed);
      expect(task.data.completedUtrvs).to.deep.include.members([utrValueOneId, utrValueThreeId]);
      expect(task.data.errorUtrvs).to.deep.include.members([utrValueTwoId]);
      expect(job.logs.length).to.equal(3); // Start task + complete task + notification
      expect(createNotificationStub.calledOnce).to.be.true;
    });

    it('should log an error if notification sending fails', async () => {
      const createNotificationStub = sandbox
        .stub(notificationService, 'createNotification')
        .rejects(new ContextError('Notification error'));

      const { job } = await workflow.processTask(mockJob, mockJob.tasks[4]);

      expect(job.tasks[4].status).to.equal(TaskStatus.Completed);
      expect(job.logs.length).to.equal(3); // Start task + complete task + error log for notification
      expect(job.logs[2].severity).to.equal(LEVEL.ERROR);
      expect(job.logs[2].message).to.include('Failed to AI automatic answer for survey notification');
      expect(createNotificationStub.calledOnce).to.be.true;
    });
  });

  describe('cleanupTaskHandler', () => {
    const mockDocumentId1 = new ObjectId();
    const mockDocumentId2 = new ObjectId();
    const mockUploadedFileId1 = 'file123';
    const mockUploadedFileId2 = 'file456';

    let mockJob: any;

    beforeEach(() => {
      mockJob = createJobModel({
        _id: mockJobId,
        tasks: [
          createTask({
            type: TaskType.AIAutoAnswerCleanup,
            status: TaskStatus.Pending,
            data: {
              documentIdMap: {
                [mockDocumentId1.toString()]: mockUploadedFileId1,
                [mockDocumentId2.toString()]: mockUploadedFileId2,
              },
            },
          }),
        ],
        logs: [],
      });
    });

    it('should call cleanupDocument for each entry in documentIdMap', async () => {
      const cleanupDocumentStub = sandbox.stub(aiDocumentService, 'cleanupDocument').resolves();

      const { job } = await workflow.processTask(mockJob, mockJob.tasks[0]);

      expect(cleanupDocumentStub.calledTwice).to.be.true;
      expect(
        cleanupDocumentStub.firstCall.calledWith({
          documentId: mockDocumentId1.toString(),
          fileId: mockUploadedFileId1,
        })
      ).to.be.true;
      expect(
        cleanupDocumentStub.secondCall.calledWith({
          documentId: mockDocumentId2.toString(),
          fileId: mockUploadedFileId2,
        })
      ).to.be.true;
      expect(job.tasks[0].status).to.equal(TaskStatus.Completed);
    });

    it('should handle empty documentIdMap', async () => {
      mockJob.tasks[0].data.documentIdMap = {};
      const cleanupDocumentStub = sandbox.stub(aiDocumentService, 'cleanupDocument').resolves();

      const { job } = await workflow.processTask(mockJob, mockJob.tasks[0]);

      expect(cleanupDocumentStub.notCalled).to.be.true;
      expect(job.tasks[0].status).to.equal(TaskStatus.Completed);
    });
  });
});
