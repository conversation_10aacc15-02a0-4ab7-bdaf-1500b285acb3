import { expect } from 'chai';
import { getPredictedDataResolver, PredictedDataResolver } from '../../../server/service/ai/PredictedDataResolver';
import { createUtrFromCode } from '../../fixtures/universalTrackerFixtures';
import { ColumnType, UtrValueType } from '../../../server/models/public/universalTrackerType';
import { UniversalTrackerValueListPlain } from '../../../server/models/universalTracker';
import { ObjectId } from 'bson';
import { AIUtrvSuggestion } from '../../../server/service/ai/types';
import { ValueData } from '../../../server/models/public/universalTrackerValueType';

describe('PredictedDataResolver', () => {
  const resolver = getPredictedDataResolver();
  const baseUtr = createUtrFromCode('utr-base', {
    valueType: UtrValueType.Number,
  });

  it('should create instance', () => {
    expect(resolver).to.be.instanceOf(PredictedDataResolver);
  });

  it('should handle Number value type correctly', () => {
    const dataProvider = [
      { predictedAnswer: undefined, output: undefined },
      { predictedAnswer: '42', output: 42 },
      { predictedAnswer: '0', output: 0 },
      { predictedAnswer: 0, output: 0 },
      { predictedAnswer: 'not valid number', output: undefined },
      { predictedAnswer: { a: 'not string or number' }, output: undefined },
    ];

    dataProvider.forEach(({ predictedAnswer, output }) => {
      const result = resolver.getValueDataPredictedAnswer({
        utr: { ...baseUtr, valueType: UtrValueType.Number },
        predictedAnswer,
      });

      expect(result).to.deep.equal({
        value: output,
        valueData: undefined,
      });
    });
  });

  it('should handle Percentage value type', () => {
    const result = resolver.getValueDataPredictedAnswer({
      utr: { ...baseUtr, valueType: UtrValueType.Percentage },
      predictedAnswer: 75,
    });

    expect(result).deep.equal({
      value: 75,
      valueData: undefined,
    });
  });

  it('should handle Text value type', () => {
    const dataProvider = [
      { predictedAnswer: undefined, output: undefined },
      { predictedAnswer: 'hello world', output: 'hello world' },
      { predictedAnswer: '123', output: '123' },
      { predictedAnswer: 123, output: '123' },
      { predictedAnswer: { a: 'not string or number' }, output: undefined },
    ];

    dataProvider.forEach(({ predictedAnswer, output }) => {
      const result = resolver.getValueDataPredictedAnswer({
        utr: { ...baseUtr, valueType: UtrValueType.Text },
        predictedAnswer,
      });

      expect(result).to.deep.equal({
        value: undefined,
        valueData: {
          data: output,
        },
      });
    });
  });

  it('should handle NumericValueList', () => {
    const utr: UniversalTrackerValueListPlain = {
      ...baseUtr,
      valueType: UtrValueType.NumericValueList,
      valueListOptions: {
        _id: new ObjectId(),
        name: 'list name',
        code: 'list-code',
        options: [
          { code: 'A', name: 'Option A' },
          { code: 'B', name: 'Option B' },
        ],
      },
    };

    const dataProvider: Array<{
      predictedAnswer: AIUtrvSuggestion['predictedAnswer'];
      output: { value: number | undefined; valueData: ValueData | undefined };
    }> = [
      {
        predictedAnswer: undefined,
        output: {
          value: undefined,
          valueData: undefined,
        },
      },
      {
        predictedAnswer: 123,
        output: {
          value: undefined,
          valueData: undefined,
        },
      },
      {
        predictedAnswer: 'not an predicted object value',
        output: {
          value: undefined,
          valueData: undefined,
        },
      },
      {
        predictedAnswer: { A: '10', B: 5 },
        output: {
          value: 15,
          valueData: {
            data: { A: 10, B: 5 },
          },
        },
      },
      {
        predictedAnswer: { A: '0', B: '10' },
        output: {
          value: 10,
          valueData: {
            data: { A: 0, B: 10 },
          },
        },
      },
      {
        predictedAnswer: { notFoundOption: '15', B: '10' },
        output: {
          value: 10,
          valueData: {
            data: { B: 10 },
          },
        },
      },
      {
        predictedAnswer: { A: 'not a valid number', B: '10' },
        output: {
          value: 10,
          valueData: {
            data: { B: 10 },
          },
        },
      },
      {
        predictedAnswer: { A: '', B: '' },
        output: {
          value: undefined,
          valueData: undefined,
        },
      },
    ];

    dataProvider.forEach(({ predictedAnswer, output }) => {
      const result = resolver.getValueDataPredictedAnswer({
        utr,
        predictedAnswer,
      });
      expect(result).deep.equal(output);
    });
  });

  it('should handle TextValueList', () => {
    const utr = {
      ...baseUtr,
      valueType: UtrValueType.TextValueList,
      valueListOptions: {
        _id: new ObjectId(),
        name: 'list name',
        code: 'list-code',
        options: [
          { code: 'X', name: 'Option X' },
          { code: 'Y', name: 'Option Y' },
        ],
      },
    };

    const dataProvider: Array<{
      predictedAnswer: AIUtrvSuggestion['predictedAnswer'];
      output: ValueData | undefined;
    }> = [
      {
        predictedAnswer: undefined,
        output: undefined,
      },
      {
        predictedAnswer: 123,
        output: undefined,
      },
      {
        predictedAnswer: 'not an object predicted value',
        output: undefined,
      },
      {
        predictedAnswer: { notFoundColumn: 'Yes', Y: 'No' },
        output: {
          data: { Y: 'No' },
        },
      },
      {
        predictedAnswer: { X: 3, Y: 5 },
        output: {
          data: { X: '3', Y: '5' },
        },
      },
      {
        predictedAnswer: { X: 'Yes', Y: 'No' },
        output: {
          data: { X: 'Yes', Y: 'No' },
        },
      },
      {
        predictedAnswer: { notFoundCol1: 'Yes', notFoundCol2: 'No' },
        output: undefined,
      },
    ];

    dataProvider.forEach(({ predictedAnswer, output }) => {
      const result = resolver.getValueDataPredictedAnswer({
        utr,
        predictedAnswer,
      });

      expect(result).to.deep.equal({
        value: undefined,
        valueData: output,
      });
    });
  });

  it('should handle Table value type when columns are text and number', () => {
    const utr = {
      ...baseUtr,
      valueType: UtrValueType.Table,
      valueValidation: {
        table: {
          columns: [
            { code: 'col1', name: 'col 1', type: ColumnType.Text },
            { code: 'col2', name: 'col 2', type: ColumnType.Number },
          ],
        },
      },
      tableColumnValueListOptions: [],
    };

    const dataProvider: Array<{
      predictedAnswer: AIUtrvSuggestion['predictedAnswer'];
      output: ValueData | undefined;
    }> = [
      {
        predictedAnswer: 123,
        output: undefined,
      },
      {
        predictedAnswer: 'string',
        output: undefined,
      },
      {
        predictedAnswer: { col1: [], col2: '100' },
        output: {
          table: [[{ code: 'col2', value: 100, unit: undefined, numberScale: undefined }]],
        },
      },
      {
        predictedAnswer: { col1: 200, col2: '' },
        output: {
          table: [
            [
              { code: 'col1', value: '200', unit: undefined, numberScale: undefined },
              { code: 'col2', value: undefined, unit: undefined, numberScale: undefined },
            ],
          ],
        },
      },
      {
        predictedAnswer: { col1: 200, col2: 'not a valid number' },
        output: {
          table: [
            [
              { code: 'col1', value: '200', unit: undefined, numberScale: undefined },
              { code: 'col2', value: undefined, unit: undefined, numberScale: undefined },
            ],
          ],
        },
      },
      {
        predictedAnswer: { col1: 'Name', col2: '100' },
        output: {
          table: [
            [
              { code: 'col1', value: 'Name', unit: undefined, numberScale: undefined },
              { code: 'col2', value: 100, unit: undefined, numberScale: undefined },
            ],
          ],
        },
      },
      {
        predictedAnswer: { col1: ['Name'], col2: ['100'] },
        output: undefined,
      },
      {
        predictedAnswer: { notFoundCol1: ['Name'], notFoundCol2: ['100'] },
        output: undefined,
      },
    ];

    dataProvider.forEach(({ predictedAnswer, output }) => {
      const result = resolver.getValueDataPredictedAnswer({
        utr,
        predictedAnswer,
      });

      expect(result).to.deep.equal({
        value: undefined,
        valueData: output,
      });
    });
  });

  it('should handle Table value type when columns have list columns', () => {
    const list1Id = new ObjectId();
    const list2Id = new ObjectId();
    const utr = {
      ...baseUtr,
      valueType: UtrValueType.Table,
      valueValidation: {
        table: {
          columns: [
            { code: 'col1', name: 'col 1', type: ColumnType.Text },
            {
              code: 'col2',
              name: 'col 2',
              type: ColumnType.ValueList,
              listId: list1Id,
              validation: { required: true },
            },
            { code: 'col3', name: 'col 3', type: ColumnType.ValueList, listId: list2Id },
          ],
        },
      },
      tableColumnValueListOptions: [
        {
          _id: list1Id,
          name: 'list 1 name',
          code: 'list-1-code',
          options: [
            { code: 'list-1-option-1', name: 'List 1 - Option 1' },
            { code: 'list-1-option-2', name: 'List 1 - Option 2' },
          ],
        },
        {
          _id: list2Id,
          name: 'list 2 name',
          code: 'list-2-code',
          options: [
            { code: 'list-2-option-1', name: 'List 2 - Option 1' },
            { code: 'list-2-option-2', name: 'List 2 - Option 2' },
          ],
        },
      ],
    };

    const dataProvider: Array<{
      describe: string;
      predictedAnswer: AIUtrvSuggestion['predictedAnswer'];
      output: ValueData | undefined;
    }> = [
      {
        describe: 'should return only 2 valid list options',
        predictedAnswer: { col1: [], col2: 'list-1-option-1', col3: 'list-2-option-2' },
        output: {
          table: [
            [
              {
                code: 'col2',
                value: 'list-1-option-1',
                unit: undefined,
                numberScale: undefined,
              },
              {
                code: 'col3',
                value: 'list-2-option-2',
                unit: undefined,
                numberScale: undefined,
              },
            ],
          ],
        },
      },
      {
        describe: 'should fallback to first option when column is required',
        predictedAnswer: { col1: 'valid text', col2: 'invalid-option-code', col3: 'invalid-option-code' },
        output: {
          table: [
            [
              {
                code: 'col1',
                value: 'valid text',
                unit: undefined,
                numberScale: undefined,
              },
              {
                code: 'col2',
                value: 'list-1-option-1',
                unit: undefined,
                numberScale: undefined,
              },
            ],
          ],
        },
      },
    ];

    dataProvider.forEach(({ predictedAnswer, output }) => {
      const result = resolver.getValueDataPredictedAnswer({
        utr,
        predictedAnswer,
      });

      expect(result).to.deep.equal({
        value: undefined,
        valueData: output,
      });
    });
  });

  it('should fallback undefined if list options is empty', () => {
    const listId = new ObjectId();
    const utr = {
      ...baseUtr,
      valueType: UtrValueType.Table,
      valueValidation: {
        table: {
          columns: [{ code: 'col1', name: 'col 1', type: ColumnType.ValueList, listId: listId }],
        },
      },
      tableColumnValueListOptions: [{ _id: listId, name: 'list name', code: 'list-code', options: [] }],
    };

    const result = resolver.getValueDataPredictedAnswer({
      utr,
      predictedAnswer: { col1: 'someValue' },
    });

    expect(result).to.deep.equal({
      value: undefined,
      valueData: undefined,
    });
  });

  it('should return undefined for unsupported value type', () => {
    const utr = { ...baseUtr, valueType: UtrValueType.Date };
    const result = resolver.getValueDataPredictedAnswer({
      utr,
      predictedAnswer: '05/05/2025',
    });

    expect(result).to.deep.equal({
      value: undefined,
      valueData: undefined,
    });
  });

  it('should handle missing valueListOptions for numericValueList', () => {
    const utr = { ...baseUtr, valueType: UtrValueType.NumericValueList, valueListOptions: undefined };
    const result = resolver.getValueDataPredictedAnswer({
      utr,
      predictedAnswer: { unknown: 100 },
    });

    expect(result).to.deep.equal({
      value: undefined,
      valueData: undefined,
    });
  });

  it('should handle missing valueListOptions for textValueList', () => {
    const utr = { ...baseUtr, valueType: UtrValueType.TextValueList, valueListOptions: undefined };
    const result = resolver.getValueDataPredictedAnswer({
      utr,
      predictedAnswer: { unknown: 'abc' },
    });

    expect(result).to.deep.equal({
      value: undefined,
      valueData: undefined,
    });
  });
});
