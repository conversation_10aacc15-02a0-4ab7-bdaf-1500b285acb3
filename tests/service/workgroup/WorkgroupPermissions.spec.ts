import * as sinon from 'sinon';
import { ObjectId } from 'bson';
import { expect } from 'chai';
import { WorkgroupPermissions } from '../../../server/service/workgroup/WorkgroupPermissions';
import { createMongooseModel } from '../../setup';
import { SurveyPermissionType } from '../../../server/models/survey';
import { DataScopeAccess } from '../../../server/models/dataShare';
import { SurveyUserRoles } from '../../../server/types/roles';
import { Permission, Workgroup } from '../../../server/models/workgroup';
import { getSurveyWorkgroupService } from '../../../server/service/workgroup/SurveyWorkgroupService';
import UniversalTracker from '../../../server/models/universalTracker';
import { ToGetWorkgroupsSurvey } from '../../../server/types/workgroup';
import { Blueprints } from '../../../server/survey/blueprints';
import { createScopeUtrv } from '../../fixtures/universalTrackerValueFixtures';
import { createSurveyWorkgroup, createToGetWorkgroupsSurvey } from '../../fixtures/workgroupFixtures';
import { createScopeFilterUtr } from '../../fixtures/universalTrackerFixtures';
import { createSurveyPermission } from '../../fixtures/survey';

describe('WorkgroupPermissions', () => {
  const workgroupModel = Workgroup;
  const surveyWorkgroupService = getSurveyWorkgroupService();
  const utrModel = UniversalTracker;
  const instance = new WorkgroupPermissions(workgroupModel, surveyWorkgroupService, utrModel);
  const sandbox = sinon.createSandbox();

  afterEach(() => {
    sandbox.restore();
  });

  describe('checkHasSurveyUserRoles', () => {
    const modelId1 = new ObjectId();
    const modelId2 = new ObjectId();

    const testCases = [
      {
        description: 'should return false if survey has no permissions',
        permissions: [],
        roles: [SurveyUserRoles.Verifier, SurveyUserRoles.Stakeholder],
        expectedCallWith: [],
        output: false,
      },
      {
        description: 'should return false if survey has no workgroup permissions',
        permissions: [
          {
            type: 'survey' as SurveyPermissionType,
            modelId: modelId1,
            access: DataScopeAccess.Full,
            roles: [SurveyUserRoles.Stakeholder],
          },
        ],
        roles: [SurveyUserRoles.Verifier, SurveyUserRoles.Stakeholder],
        expectedCallWith: [],
        output: false,
      },
      {
        description: 'should return false if survey has no workgroup permissions for the roles',
        permissions: [
          {
            type: SurveyPermissionType.Workgroup,
            modelId: modelId1,
            access: DataScopeAccess.Full,
            roles: [SurveyUserRoles.Admin],
          },
        ],
        roles: [SurveyUserRoles.Verifier, SurveyUserRoles.Stakeholder],
        expectedCallWith: [],
        output: false,
      },
      {
        description: 'should return true if survey has workgroup permissions for the roles',
        permissions: [
          {
            type: SurveyPermissionType.Workgroup,
            modelId: modelId1,
            access: DataScopeAccess.Full,
            roles: [SurveyUserRoles.Verifier],
          },
          {
            type: SurveyPermissionType.Workgroup,
            modelId: modelId2,
            access: DataScopeAccess.Full,
            roles: [SurveyUserRoles.Stakeholder],
          },
        ],
        roles: [SurveyUserRoles.Verifier, SurveyUserRoles.Stakeholder],
        expectedCallWith: [modelId1, modelId2],
        output: true,
      },
      {
        description: 'should return true if survey has workgroup permissions for the Admin role and access is partial',
        permissions: [
          {
            type: SurveyPermissionType.Workgroup,
            modelId: modelId1,
            access: DataScopeAccess.Partial,
            roles: [SurveyUserRoles.Admin],
          },
        ],
        roles: [SurveyUserRoles.Admin],
        expectedCallWith: [modelId1],
        output: true,
      },
    ];

    testCases.forEach(({ description, permissions, roles, expectedCallWith, output }) => {
      it(description, async () => {
        const survey = {
          permissions,
        };
        const userId = new ObjectId();

        const findOneStub = sandbox
          .stub(workgroupModel, 'exists')
          .returns(createMongooseModel({ _id: new ObjectId() }));

        const result = await instance.checkHasSurveyUserRoles({
          survey,
          userId,
          roles,
        });

        if (expectedCallWith.length) {
          expect(
            findOneStub.calledOnceWith({
              _id: { $in: expectedCallWith },
              'users._id': userId,
            })
          ).to.be.true;
        }
        expect(result).to.equal(output);
      });
    });
  });

  describe('checkHasUtrvsUserRoles', () => {
    const utrvId1 = new ObjectId();
    const utrvId2 = new ObjectId();
    const userId = new ObjectId();
    const workgroupId = new ObjectId();
    const utrId1 = new ObjectId();
    const utrId2 = new ObjectId();

    const utrv1 = createScopeUtrv({ _id: utrvId1, universalTrackerId: utrId1 });
    const utrv2 = createScopeUtrv({ _id: utrvId2, universalTrackerId: utrId2 });

    const survey: ToGetWorkgroupsSurvey = {
      permissions: [
        {
          type: SurveyPermissionType.Workgroup,
          modelId: workgroupId,
          access: DataScopeAccess.Partial,
          roles: [SurveyUserRoles.Stakeholder],
        },
      ],
      _id: new ObjectId(),
      initiativeId: new ObjectId(),
      sourceName: Blueprints.Gri2020,
      visibleUtrvs: [utrvId1, utrvId2],
    };

    it('should return false if no utrvs are provided', async () => {
      const result = await instance.checkHasUtrvsUserRoles({
        utrvs: [],
        userId,
        roles: [SurveyUserRoles.Stakeholder],
        survey: createToGetWorkgroupsSurvey({
          permissions: [
            {
              type: SurveyPermissionType.Workgroup,
              modelId: workgroupId,
              access: DataScopeAccess.Partial,
              roles: [SurveyUserRoles.Stakeholder],
            },
          ],
        }),
      });
      expect(result).to.be.false;
    });

    it('should return false if survey has no permissions', async () => {
      const result = await instance.checkHasUtrvsUserRoles({
        utrvs: [utrv1],
        userId,
        roles: [SurveyUserRoles.Stakeholder],
        survey: { ...survey, permissions: [] },
      });
      expect(result).to.be.false;
    });

    it('should fetch UTRs to map to UTRVs if not present', async () => {
      const findStub = sandbox
        .stub(utrModel, 'find')
        .returns(createMongooseModel([createScopeFilterUtr({ _id: utrId1 })]));
      sandbox.stub(surveyWorkgroupService, 'getWorkgroups').resolves([]);

      await instance.checkHasUtrvsUserRoles({
        utrvs: [{ ...utrv1, universalTracker: undefined }],
        userId,
        roles: [SurveyUserRoles.Stakeholder],
        survey: survey,
      });

      expect(findStub.calledOnce).to.be.true;
      expect(findStub.firstCall.args.at(0)).to.deep.include({ _id: { $in: [utrId1] } });
    });

    it('should return false if no survey workgroups are found', async () => {
      sandbox.stub(surveyWorkgroupService, 'getWorkgroups').resolves([]);
      const result = await instance.checkHasUtrvsUserRoles({
        utrvs: [utrv1],
        userId,
        roles: [SurveyUserRoles.Stakeholder],
        survey,
      });
      expect(result).to.be.false;
    });

    it('should return false if user is not in any workgroup', async () => {
      sandbox.stub(surveyWorkgroupService, 'getWorkgroups').resolves([
        createSurveyWorkgroup({
          users: [{ _id: new ObjectId(), permissions: [Permission.User] }],
        }),
      ]);
      const result = await instance.checkHasUtrvsUserRoles({
        utrvs: [utrv1],
        userId,
        roles: [SurveyUserRoles.Stakeholder],
        survey,
      });
      expect(result).to.be.false;
    });

    it('should return false if user does not have access to all UTRVs', async () => {
      sandbox.stub(surveyWorkgroupService, 'getWorkgroups').resolves([
        createSurveyWorkgroup({
          utrvIds: [utrvId1],
          users: [{ _id: userId, permissions: [Permission.User] }],
          permission: createSurveyPermission({
            access: DataScopeAccess.Partial,
            roles: [SurveyUserRoles.Stakeholder],
          }),
        }),
      ]);
      const result = await instance.checkHasUtrvsUserRoles({
        utrvs: [utrv1, utrv2],
        userId,
        roles: [SurveyUserRoles.Stakeholder],
        survey,
      });
      expect(result).to.be.false;
    });

    it('should return true if user has access to all UTRVs', async () => {
      sandbox.stub(surveyWorkgroupService, 'getWorkgroups').resolves([
        createSurveyWorkgroup({
          utrvIds: [utrvId1, utrvId2],
          users: [{ _id: userId, permissions: [Permission.User] }],
          permission: createSurveyPermission({
            access: DataScopeAccess.Partial,
            roles: [SurveyUserRoles.Stakeholder],
          }),
        }),
      ]);
      const result = await instance.checkHasUtrvsUserRoles({
        utrvs: [utrv1, utrv2],
        userId,
        roles: [SurveyUserRoles.Stakeholder],
        survey: survey,
      });
      expect(result).to.be.true;
    });
  });
});
