/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import '../../../setup';
import axios from 'axios';
import { expect } from 'chai';
import { createSandbox } from 'sinon';
import { getGreenlyApi, GreenlyA<PERSON> } from '../../../../server/service/integration/greenly/GreenlyApi';
import { createGreenlyFixtures, getMockGreenlyCompany } from '../../../fixtures/greenlyFixtures';
import ContextError from '../../../../server/error/ContextError';
import { GreenlyCreateData, GreenlyUserRole } from '../../../../server/service/integration/greenly/greenlyTypes';
import { testLogger } from '../../../factories/logger';
import { userOne } from '../../../fixtures/userFixtures';

const { greenlyCompanyListItem, createEmissionData } = createGreenlyFixtures();

describe('GreenlyApi', () => {
  const sandbox = createSandbox();
  const httpClient = axios.create();
  const api = new GreenlyApi(httpClient, testLogger);

  afterEach(() => {
    sandbox.restore();
  });

  it('should create instance', () => {
    expect(getGreenlyApi()).to.be.instanceOf(GreenlyApi);
  });

  describe('listCompanies', () => {
    it('should return companies list', async () => {
      const mockResponse = {
        data: {
          companies: [greenlyCompanyListItem]
        }
      };
      sandbox.stub(httpClient, 'get').resolves(mockResponse);

      const result = await api.listCompanies();
      expect(result).to.deep.equal([greenlyCompanyListItem]);
    });

    it('should handle API error', async () => {
      const error = new ContextError('API Error');
      sandbox.stub(httpClient, 'get').rejects(error);

      await expect(api.listCompanies()).to.be.rejectedWith(error);
    });
  });

  describe('getCompany', () => {
    it('should return company by id', async () => {
      const mockResponse = {
        data: {
          companies: [greenlyCompanyListItem]
        }
      };
      sandbox.stub(httpClient, 'get').resolves(mockResponse);

      const result = await api.getCompany(greenlyCompanyListItem.id);
      expect(result).to.deep.equal(greenlyCompanyListItem);
    });

    it('should throw error when company not found', async () => {
      const mockResponse = {
        data: {
          companies: []
        }
      };
      sandbox.stub(httpClient, 'get').resolves(mockResponse);
      await expect(api.getCompany('non-existent-id')).to.be.rejectedWith(ContextError);
    });
  });

  describe('createConnection', () => {
    const baseRootInitiative = {
      initiativeId: 'test-initiative',
      name: 'Test Company',
      logo: 'logo.png',
      description: 'Test Description',
      industryLevel3: 'Test Industry',
      language: 'en',
      year: '2024',
      validityStatus: 'DEMO',
      type: 'standalone',
      countryCode: 'GB',
      parentInitiativeId: undefined,
    } satisfies GreenlyCreateData['rootInitiative'];

    const baseCreateData: Omit<GreenlyCreateData, 'rootInitiative'> = {
      user: { ...userOne, jobTitle: 'Manager', firstName: 'John', surname: 'Doe' },
      additionalContext: [
        {
          name: 'Metric 1',
          value: '100',
          unit: 'kg',
          code: 'metric-1'
        }
      ],
      initiativeTree: [],
    };

    const fakeCompanyId = 'fake-company-id';
    const fakeUserId = 'fake-user-id';

    function setupStubs({ userExists = false, companyExists = false, companyOverride = {} } = {}) {
      sandbox.restore();
      sandbox.stub(httpClient, 'get').callsFake((url) => {
        if (typeof url === 'string' && url.includes('/users/check-email')) {
          if (userExists) {
            return Promise.resolve({ status: 200 });
          }
          return Promise.resolve({ status: 404 });
        }
        // /users/me for listCompanies
        return Promise.resolve({
          data: {
            companies: companyExists ? [getMockGreenlyCompany(companyOverride)] : []
          }
        });
      });
      sandbox.stub(httpClient, 'post').callsFake((url, data) => {
        if (typeof url === 'string' && url.includes('/users/invite')) {
          return Promise.resolve({
            data: {
              partialErrors: [],
              idsInvited: [fakeUserId],
              emailsInvited: [baseCreateData.user.email],
            }
          });
        }
        if (typeof url === 'string' && url.includes('/users')) {
          return Promise.resolve({
            data: [{ id: fakeUserId, email: baseCreateData.user.email }]
          });
        }
        if (typeof url === 'string' && url.includes('/companies')) {
          if (!companyExists) {
            const [first] = Array.isArray(data) ? data : [];
            return Promise.resolve({
              data: first ? [getMockGreenlyCompany({ name: first.name, identifier: first.identifier })] : []
            });
          }
          return Promise.resolve({ data: [] });
        }
        return Promise.resolve({ data: [] });
      });
    }

    it('should create root company', async () => {
      setupStubs({ userExists: false, companyExists: false });
      const rootInitiative = { ...baseRootInitiative };
      const createData: GreenlyCreateData = {
        rootInitiative,
        ...baseCreateData,
        initiativeTree: [rootInitiative],
      };
      const result = await api.createConnection(createData);
      expect(result.greenlyCompany.id).to.equal(fakeCompanyId);
      expect(result.greenlyCompany.identifier).to.deep.equal({ type: 'operatorId', value: rootInitiative.initiativeId });
      expect(result.childrenCompanies).to.deep.equal([]);
    });

    it('should invite user to company if user already exists', async () => {
      const rootInitiative = { ...baseRootInitiative, initiativeId: 'existing-initiative', name: 'Existing Company', industryLevel3: 'Existing Industry', description: 'Existing Description' };
      setupStubs({
        userExists: true,
        companyExists: true,
        companyOverride: { identifier: { type: 'operatorId', value: rootInitiative.initiativeId } }
      });
      const createData: GreenlyCreateData = {
        rootInitiative,
        ...baseCreateData,
        initiativeTree: [rootInitiative],
      };
      const result = await api.createConnection(createData);
      expect(result.greenlyCompany.id).to.equal(fakeCompanyId);
      expect(result.greenlyCompany.identifier).to.deep.equal({ type: 'operatorId', value: rootInitiative.initiativeId });
      expect(result.childrenCompanies).to.deep.equal([]);
    });
  });

  describe('inviteUser', () => {
    const testUser = {
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      jobTitle: 'Developer',
      role: GreenlyUserRole.ProjectManager,
      companyIds: ['test-company-id'],
    };
    const testCompanyId = 'test-company-id';

    it('should successfully invite a user', async () => {
      const mockResponse = {
        data: {
          partialErrors: [],
          idsInvited: ['user-123'],
          emailsInvited: [testUser.email],
        }
      };
      sandbox.stub(httpClient, 'post').resolves(mockResponse);

      const result = await api['inviteUser'](testCompanyId, testUser);
      expect(result).to.deep.equal(['user-123']);
    });

    it('should throw error with single partial error', async () => {
      const mockResponse = {
        data: {
          partialErrors: [{
            errorDetail: 'User already exists',
            errorLabel: 'USER_EXISTS'
          }],
          idsInvited: [],
          emailsInvited: [],
        }
      };
      sandbox.stub(httpClient, 'post').resolves(mockResponse);

      await expect(api['inviteUser'](testCompanyId, testUser))
        .to.be.rejectedWith(ContextError)
        .and.eventually.have.property('message')
        .that.include('Failed to invite user to company');
    });

    it('should log warning and include all errors when multiple partial errors occur', async () => {
      const partialErrors = [
        { errorDetail: 'User already exists', errorLabel: 'USER_EXISTS' },
        { errorDetail: 'Invalid email domain', errorLabel: 'INVALID_DOMAIN' },
        { errorDetail: 'Company limit reached', errorLabel: 'LIMIT_REACHED' }
      ];

      const mockResponse = {
        data: {
          partialErrors,
          idsInvited: [],
          emailsInvited: [],
        }
      };
      sandbox.stub(httpClient, 'post').resolves(mockResponse);
      const promise = api.inviteUser(testCompanyId, testUser);

      await expect(promise).to.be.rejectedWith(ContextError)
        .and.eventually.have.property('message')
        .that.include('Failed to invite user to company');

      // Get the actual error to verify context properties
      const error = await promise.catch(e => e);
      expect(error.context.partialErrors).to.deep.equal(partialErrors);
      expect(error.context.companyId).to.equal(testCompanyId);
    });

    it('should log success message when invitation succeeds', async () => {
      const mockResponse = {
        data: {
          partialErrors: [],
          idsInvited: ['user-123'],
          emailsInvited: [testUser.email],
        }
      };
      sandbox.stub(httpClient, 'post').resolves(mockResponse);
      const infoSpy = sandbox.spy(testLogger, 'info');

      await api.inviteUser(testCompanyId, testUser);

      expect(infoSpy.calledOnce).to.be.true;
      expect(infoSpy.firstCall.args[0]).to.include('success');
      expect(infoSpy.firstCall.args[0]).to.not.include('failed');
    });

    it('should log partial success message when invitation has errors', async () => {
      const mockResponse = {
        data: {
          partialErrors: [{
            errorDetail: 'Some error',
            errorLabel: 'ERROR'
          }],
          idsInvited: [],
          emailsInvited: ['<EMAIL>'],
        }
      };
      sandbox.stub(httpClient, 'post').resolves(mockResponse);
      const infoSpy = sandbox.spy(testLogger, 'info');

      await expect(api.inviteUser(testCompanyId, testUser)).to.be.rejectedWith(ContextError);

      expect(infoSpy.calledOnce).to.be.true;
      expect(infoSpy.firstCall.args[0]).to.include('success (with errors)');
    });
  });

  describe('getEmissionsDataTotal', () => {
    it('should return emissions data', async () => {
      const mockResponse = {
        data: createEmissionData()
      };
      sandbox.stub(httpClient, 'get').resolves(mockResponse);

      const result = await api.getEmissionsDataTotal('company-123', 2024);
      expect(result).to.deep.equal(mockResponse.data);
    });

    it('should handle API error with context', async () => {
      const error = {
        message: 'API Error',
        response: {
          data: { error: 'Invalid request' }
        }
      };
      sandbox.stub(httpClient, 'get').rejects(error);
      await expect(api.getEmissionsDataTotal('company-123', 2024)).to.be.rejectedWith(ContextError);
    });
  });
});
