import * as sinon from 'sinon';
import { ObjectId } from 'bson';
import { expect } from 'chai';
import { AggregationPreviewManager } from '../../../../server/service/utr/aggregation/AggregationPreviewManager';
import { testLogger } from '../../../factories/logger';
import UserError from '../../../../server/error/UserError';
import UniversalTrackerActionManager from '../../../../server/service/utr/UniversalTrackerActionManager';
import { SurveyRepository } from '../../../../server/repository/SurveyRepository';
import { createUtrvModel } from '../../../factories/universalTrackerValue';
import { createUtr } from '../../../fixtures/universalTrackerFixtures';
import { createSurvey } from '../../../fixtures/survey';
import { initiativeOneSimple } from '../../../fixtures/initiativeFixtures';
import { getSurveyAggregator } from '../../../../server/service/survey/SurveyAggregator';
import { UtrvFilter } from '../../../../server/types/universalTrackerValue';
import { SourceItemType, SurveyWithInitiative } from '../../../../server/models/survey';
import { UtrValueType, ValueAggregation } from '../../../../server/models/public/universalTrackerType';
import { ActionList, DataPeriods } from '../../../../server/service/utr/constants';
import { SourceTypes } from '../../../../server/models/public/universalTrackerValueType';
import { UniversalTrackerValueAggregated } from '../../../../server/models/universalTrackerValue';
import { UniversalTrackerPlain } from '../../../../server/models/universalTracker';
import { blueprintDefaultUnitConfig } from '../../../../server/service/units/unitTypes';

describe('AggregationPreviewManager', () => {
  const sandbox = sinon.createSandbox();

  // Create real manager with real dependencies
  const surveyAggregator = getSurveyAggregator();
  const aggregationPreviewManager = new AggregationPreviewManager(testLogger, surveyAggregator);

  // Helper functions for test data
  const createTestSurvey = (overrides = {}) => {
    const survey = createSurvey({
      sourceItems: [{ type: SourceItemType.Survey, sourceId: new ObjectId() }],
      initiative: initiativeOneSimple,
      filters: { utrv: UtrvFilter.Verified },
      ...overrides,
    });
    // Ensure initiative is set for SurveyWithInitiative type
    if (!survey.initiative) {
      survey.initiative = initiativeOneSimple;
    }
    // Cast to SurveyWithInitiative since we guarantee initiative is set
    return survey as SurveyWithInitiative;
  };

  const createTestSourceItems = (count = 2) => {
    const items = [];
    for (let i = 0; i < count; i++) {
      items.push({
        utrvId: new ObjectId(),
        historyId: new ObjectId(),
        latestHistoryId: new ObjectId(),
        latestStatus: i === 0 ? ActionList.Verified : ActionList.Updated,
      });
    }
    return items;
  };

  const createTestAggregatedUtrv = (utr: UniversalTrackerPlain, overrides: Partial<UniversalTrackerValueAggregated> = {}): UniversalTrackerValueAggregated => ({
    _id: utr._id,
    initiativeId: initiativeOneSimple._id,
    universalTrackerId: utr._id,
    universalTracker: utr,
    effectiveDate: new Date('2024-02-01'),
    valueData: { data: 99 },
    value: 99,
    valueAggregation: ValueAggregation.ValueSumAggregator,
    sourceItems: [],
    history: [],
    lastUpdated: new Date(),
    evidenceRequired: false,
    verificationRequired: false,
    status: ActionList.Verified,
    instructions: '',
    sourceType: SourceTypes.Aggregated,
    valueValidation: {},
    type: 'actual',
    valueType: UtrValueType.Number,
    period: DataPeriods.Yearly,
    note: undefined,
    ...overrides,
  } as UniversalTrackerValueAggregated);

  const surveysForAggregation = [{
    _id: new ObjectId(),
    initiativeId: new ObjectId(),
    effectiveDate: new Date('2024-01-01'),
    period: DataPeriods.Yearly,
    unitConfig: blueprintDefaultUnitConfig,
  }];

  afterEach(() => {
    sandbox.restore();
  });

  it('should create instance', () => {
    expect(aggregationPreviewManager).be.instanceOf(AggregationPreviewManager);
  });

  describe('previewAggregation', () => {
    it('should throw error if no surveyId in compositeData', async () => {
      const utr = createUtr(new ObjectId(), 'UTR1');
      const originalUtrv = createUtrvModel({ compositeData: { fragmentUtrvs: [] } });

      await expect(aggregationPreviewManager.previewAggregation({ utr, originalUtrv }))
        .to.be.rejectedWith(UserError, 'Failed to refresh data');
    });

    it('should throw error if no survey found', async () => {
      const utr = createUtr(new ObjectId(), 'UTR1');
      const surveyId = new ObjectId();
      const originalUtrv = createUtrvModel({ compositeData: { surveyId, fragmentUtrvs: [] } });

      sandbox.stub(SurveyRepository, 'findByIdWithInitiative').resolves(null);

      await expect(aggregationPreviewManager.previewAggregation({ utr, originalUtrv }))
        .to.be.rejectedWith(UserError, 'Failed to refresh data');
    });

    it('should throw error if no surveys are returned', async () => {
      const utr = createUtr(new ObjectId(), 'UTR1');
      const surveyId = new ObjectId();
      const originalUtrv = createUtrvModel({ compositeData: { surveyId, fragmentUtrvs: [] } });
      const survey = createTestSurvey();

      sandbox.stub(SurveyRepository, 'findByIdWithInitiative').resolves(survey);
      sandbox.stub(surveyAggregator, 'getSurveysForAggregation').resolves([]);

      await expect(aggregationPreviewManager.previewAggregation({ utr, originalUtrv }))
        .to.be.rejectedWith(UserError, 'Failed to refresh data');
    });

    it('should throw error if multiple aggregatedUtrv are returned', async () => {
      const utr = createUtr(new ObjectId(), 'UTR1');
      const surveyId = new ObjectId();
      const originalUtrv = createUtrvModel({ compositeData: { surveyId, fragmentUtrvs: [] } });
      const survey = createTestSurvey();

      sandbox.stub(SurveyRepository, 'findByIdWithInitiative').resolves(survey);
      sandbox.stub(surveyAggregator, 'getSurveysForAggregation').resolves(surveysForAggregation);
      sandbox.stub(surveyAggregator, 'convertToUtrvStatuses').returns([ActionList.Verified]);
      sandbox.stub(surveyAggregator, 'getAggregatedData').resolves([createTestAggregatedUtrv(utr), createTestAggregatedUtrv(utr)]); // Multiple results

      await expect(aggregationPreviewManager.previewAggregation({ utr, originalUtrv }))
        .to.be.rejectedWith(UserError, 'Failed to refresh data');
    });

    it('should return current and next value for valid input', async () => {
      const utr = createUtr(new ObjectId(), 'UTR1', {
        valueType: UtrValueType.Number,
        valueAggregation: ValueAggregation.ValueSumAggregator
      });
      const surveyId = new ObjectId();
      const originalUtrv = createUtrvModel({
        compositeData: { surveyId, fragmentUtrvs: [] },
        effectiveDate: new Date('2024-01-01'),
        status: ActionList.Created,
        valueData: { data: 42 },
        value: 42,
        note: 'original note',
        unit: 'kg',
        numberScale: '1',
        valueAggregation: ValueAggregation.ValueSumAggregator,
      });

      const survey = createTestSurvey();
      const aggregatedUtrv = createTestAggregatedUtrv(utr);

      sandbox.stub(SurveyRepository, 'findByIdWithInitiative').resolves(survey);
      sandbox.stub(surveyAggregator, 'getSurveysForAggregation').resolves(surveysForAggregation);
      sandbox.stub(surveyAggregator, 'convertToUtrvStatuses').returns([ActionList.Verified]);
      sandbox.stub(surveyAggregator, 'getAggregatedData').resolves([aggregatedUtrv]);

      const result = await aggregationPreviewManager.previewAggregation({ utr, originalUtrv });

      expect(result.current.value).to.equal(42);
      expect(result.next.value).to.equal(99);
      expect(result.current.valueData).to.deep.equal({ data: 42 });
      expect(result.next.valueData).to.deep.equal({ data: 99 });
      expect(result.current.note).to.equal('original note');
      expect(result.next.note).to.be.undefined; // No note in aggregated data
      expect(result.next.valueAggregation).to.equal(ValueAggregation.ValueSumAggregator);
      expect(result.next.sourceItems).to.deep.equal([]);
    });

    it('should include sourceItems in preview result', async () => {
      const utr = createUtr(new ObjectId(), 'UTR1', {
        valueType: UtrValueType.Number,
        valueAggregation: ValueAggregation.ValueSumAggregator
      });
      const surveyId = new ObjectId();
      const originalUtrv = createUtrvModel({
        compositeData: { surveyId, fragmentUtrvs: [] },
        sourceItems: [{ utrvId: new ObjectId(), historyId: new ObjectId(), latestHistoryId: new ObjectId(), latestStatus: ActionList.Verified }],
      });

      const survey = createTestSurvey();
      const testSourceItems = createTestSourceItems(1);
      const aggregatedUtrv = createTestAggregatedUtrv(utr, {
        value: 50,
        valueData: { data: 50 },
        sourceItems: testSourceItems,
      });

      sandbox.stub(SurveyRepository, 'findByIdWithInitiative').resolves(survey);
      sandbox.stub(surveyAggregator, 'getSurveysForAggregation').resolves(surveysForAggregation);
      sandbox.stub(surveyAggregator, 'convertToUtrvStatuses').returns([ActionList.Verified]);
      sandbox.stub(surveyAggregator, 'getAggregatedData').resolves([aggregatedUtrv]);

      const result = await aggregationPreviewManager.previewAggregation({ utr, originalUtrv });

      // Verify sourceItems are included in both current and next
      expect(result.current.sourceItems).to.be.an('array');
      expect(result.next.sourceItems).to.deep.equal(testSourceItems);
      expect(result.next.sourceItems).to.have.lengthOf(1);
    });
  });

  describe('updateAggregation', () => {
    it('should update utrv with aggregated values and save', async () => {
      const utr = createUtr(new ObjectId(), 'UTR1', {
        valueType: UtrValueType.Number,
        valueAggregation: ValueAggregation.ValueSumAggregator
      });
      const surveyId = new ObjectId();
      const userId = new ObjectId();
      const originalUtrv = createUtrvModel({
        compositeData: { surveyId, fragmentUtrvs: [] },
        effectiveDate: new Date('2024-01-01'),
        status: ActionList.Created,
        valueData: { data: 42 },
        value: 42,
        note: 'original note',
        unit: 'kg',
        numberScale: '1',
        valueAggregation: ValueAggregation.ValueSumAggregator,
      });

      // Factory already provides save method

      const survey = createTestSurvey();
      const aggregatedUtrv = createTestAggregatedUtrv(utr);

      sandbox.stub(SurveyRepository, 'findByIdWithInitiative').resolves(survey);
      sandbox.stub(surveyAggregator, 'getSurveysForAggregation').resolves(surveysForAggregation);
      sandbox.stub(surveyAggregator, 'convertToUtrvStatuses').returns([ActionList.Verified]);
      sandbox.stub(surveyAggregator, 'getAggregatedData').resolves([aggregatedUtrv]);

      const hydrateUpdateStub = sandbox.stub(UniversalTrackerActionManager, 'hydrateUpdate');
      const hydrateVerifyStub = sandbox.stub(UniversalTrackerActionManager, 'hydrateVerify');

      const result = await aggregationPreviewManager.updateAggregation({ utr, originalUtrv, userId });

      // Verify the hydrate methods were called with correct parameters
      expect(hydrateUpdateStub.calledOnce).to.be.true;
      expect(hydrateUpdateStub.calledWith({
        utrv: originalUtrv,
        userId,
        value: 99,
        note: undefined, // No note in test data
        valueData: { data: 99 },
      })).to.be.true;

      expect(hydrateVerifyStub.calledOnce).to.be.true;
      expect(hydrateVerifyStub.calledWith(originalUtrv, userId)).to.be.true;

      // Factory provides save method, no need to verify

      // Verify the valueAggregation was updated
      expect(originalUtrv.valueAggregation).to.equal(ValueAggregation.ValueSumAggregator);

      // Verify the result
      expect(result.current.value).to.equal(42);
      expect(result.next.value).to.equal(99);
    });

    it('should update sourceItems and valueType from aggregated result', async () => {
      const utr = createUtr(new ObjectId(), 'UTR1', {
        valueType: UtrValueType.Number,
        valueAggregation: ValueAggregation.ValueSumAggregator
      });
      const surveyId = new ObjectId();
      const userId = new ObjectId();
      const originalUtrv = createUtrvModel({
        compositeData: { surveyId, fragmentUtrvs: [] },
        valueType: UtrValueType.Number,
        sourceItems: [], // Start with empty sourceItems
      });

      // Factory already provides save method

      const survey = createTestSurvey();
      const testSourceItems = createTestSourceItems(2);
      const aggregatedUtrv = createTestAggregatedUtrv(utr, {
        sourceItems: testSourceItems,
        valueType: UtrValueType.Percentage, // Different from original
        note: 'Aggregated note',
      });

      sandbox.stub(SurveyRepository, 'findByIdWithInitiative').resolves(survey);
      sandbox.stub(surveyAggregator, 'getSurveysForAggregation').resolves(surveysForAggregation);
      sandbox.stub(surveyAggregator, 'convertToUtrvStatuses').returns([ActionList.Verified]);
      sandbox.stub(surveyAggregator, 'getAggregatedData').resolves([aggregatedUtrv]);

      sandbox.stub(UniversalTrackerActionManager, 'hydrateUpdate');
      sandbox.stub(UniversalTrackerActionManager, 'hydrateVerify');

      const result = await aggregationPreviewManager.updateAggregation({ utr, originalUtrv, userId });

      // Verify sourceItems was updated
      expect(originalUtrv.sourceItems).to.deep.equal(testSourceItems);
      expect(originalUtrv.sourceItems).to.have.lengthOf(2);

      // Verify valueType was updated
      expect(originalUtrv.valueType).to.equal(UtrValueType.Percentage);

      // Verify the result includes sourceItems
      expect(result.next.sourceItems).to.deep.equal(testSourceItems);
      expect(result.next.note).to.equal('Aggregated note');
    });

    it('should handle errors during aggregation', async () => {
      const utr = createUtr(new ObjectId(), 'UTR1');
      const surveyId = new ObjectId();
      const userId = new ObjectId();
      const originalUtrv = createUtrvModel({ compositeData: { surveyId, fragmentUtrvs: [] } });
      // Factory already provides save method

      sandbox.stub(SurveyRepository, 'findByIdWithInitiative').rejects(new UserError('Database error'));

      await expect(aggregationPreviewManager.updateAggregation({ utr, originalUtrv, userId }))
        .to.be.rejectedWith(UserError, 'Database error');
    });
  });
});
