import * as sinon from 'sinon';
import { expect } from 'chai';
import { ObjectId } from 'bson';
import { UtrvPermissions } from '../../../server/service/utr/UtrvPermissions';
import { SurveyRepository } from '../../../server/repository/SurveyRepository';
import { getWorkgroupPermissions } from '../../../server/service/workgroup/WorkgroupPermissions';
import { InitiativePermissions } from '../../../server/service/initiative/InitiativePermissions';
import { SurveyPermissions } from '../../../server/service/survey/SurveyPermissions';
import UniversalTrackerValue from '../../../server/models/universalTrackerValue';
import UniversalTrackerValueAssurance from '../../../server/models/universalTrackerValueAssurance';
import { createTestUser } from '../../fixtures/userFixtures';
import { createSurvey } from '../../fixtures/survey';
import { createUtrv } from '../../fixtures/compositeUTRVFixtures';


describe('UtrvPermissions', () => {
  const sandbox = sinon.createSandbox();

  let surveyRepoStub: sinon.SinonStub;
  let workgroupPermissionsStub: sinon.SinonStub;
  let initiativePermissionsCanAccessStub: sinon.SinonStub;
  let initiativePermissionsCanContributeStub: sinon.SinonStub;
  let initiativePermissionsCanVerifyStub: sinon.SinonStub;
  let surveyPermissionsCanAccessStub: sinon.SinonStub;
  let surveyPermissionsCanContributeStub: sinon.SinonStub;
  let surveyPermissionsCanVerifyStub: sinon.SinonStub;
  let assuranceAggregateStub: sinon.SinonStub;
  let utrvFindStub: sinon.SinonStub;

  beforeEach(() => {
    surveyRepoStub = sandbox.stub(SurveyRepository, 'findById');
    workgroupPermissionsStub = sandbox.stub(getWorkgroupPermissions(), 'checkHasUtrvsUserRoles');
    initiativePermissionsCanAccessStub = sandbox.stub(InitiativePermissions, 'canAccess');
    initiativePermissionsCanContributeStub = sandbox.stub(InitiativePermissions, 'canContribute');
    initiativePermissionsCanVerifyStub = sandbox.stub(InitiativePermissions, 'canVerify');
    surveyPermissionsCanAccessStub = sandbox.stub(SurveyPermissions, 'canAccess');
    surveyPermissionsCanContributeStub = sandbox.stub(SurveyPermissions, 'canContribute');
    surveyPermissionsCanVerifyStub = sandbox.stub(SurveyPermissions, 'canVerify');
    assuranceAggregateStub = sandbox.stub(UniversalTrackerValueAssurance, 'aggregate');
    utrvFindStub = sandbox.stub(UniversalTrackerValue, 'find');
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('canAccess', () => {
    it('should return true if user is a stakeholder', async () => {
      const user = createTestUser();
      const utrv = createUtrv(new ObjectId(), new ObjectId(), undefined, {
        stakeholders: { stakeholder: [user._id], verifier: [], escalation: [] },
      });

      const result = await UtrvPermissions.canAccess(utrv, user);
      expect(result).to.be.true;
    });

    it('should return true if user is a verifier', async () => {
      const user = createTestUser();
      const utrv = createUtrv(new ObjectId(), new ObjectId(), undefined, {
        stakeholders: { stakeholder: [], verifier: [user._id], escalation: [] },
      });

      const result = await UtrvPermissions.canAccess(utrv, user);
      expect(result).to.be.true;
    });

    it('should return true if user has access through a workgroup', async () => {
      const user = createTestUser();
      const survey = createSurvey();
      const utrv = createUtrv(new ObjectId(), new ObjectId(), undefined, {
        compositeData: { surveyId: survey._id, fragmentUtrvs: [] },
      });

      surveyRepoStub.resolves(survey);
      workgroupPermissionsStub.resolves(true);

      const result = await UtrvPermissions.canAccess(utrv, user);
      expect(result).to.be.true;
      sinon.assert.calledWith(workgroupPermissionsStub, {
        utrvs: [utrv],
        userId: user._id,
        roles: UtrvPermissions.canAccessRoles,
        survey,
      });
    });

    it('should return true if user has access through survey permissions', async () => {
      const user = createTestUser();
      const survey = createSurvey();
      const utrv = createUtrv(new ObjectId(), new ObjectId(), undefined, {
        compositeData: { surveyId: survey._id, fragmentUtrvs: [] },
      });

      surveyRepoStub.resolves(survey);
      workgroupPermissionsStub.resolves(false);
      surveyPermissionsCanAccessStub.resolves(true);

      const result = await UtrvPermissions.canAccess(utrv, user);
      expect(result).to.be.true;
      sinon.assert.calledWith(surveyPermissionsCanAccessStub, survey, user);
    });

    it('should return true if user has access through initiative permissions', async () => {
      const user = createTestUser();
      const utrv = createUtrv(new ObjectId(), new ObjectId(), undefined, {});

      initiativePermissionsCanAccessStub.resolves(true);

      const result = await UtrvPermissions.canAccess(utrv, user);
      expect(result).to.be.true;
      sinon.assert.calledWith(initiativePermissionsCanAccessStub, user, utrv.initiativeId.toString());
    });

    it('should return false if user has no access', async () => {
      const user = createTestUser();
      const utrv = createUtrv(new ObjectId(), new ObjectId(), undefined, {});

      initiativePermissionsCanAccessStub.resolves(false);

      const result = await UtrvPermissions.canAccess(utrv, user);
      expect(result).to.be.false;
    });
  });

  describe('canContribute', () => {
    it('should return true if user is a stakeholder', async () => {
      const user = createTestUser();
      const utrv = createUtrv(new ObjectId(), new ObjectId(), undefined, {
        stakeholders: { stakeholder: [user._id], verifier: [], escalation: [] },
      });

      const result = await UtrvPermissions.canContribute(utrv, user);
      expect(result).to.be.true;
    });

    it('should return true if user has access through a workgroup', async () => {
      const user = createTestUser();
      const survey = createSurvey();
      const utrv = createUtrv(new ObjectId(), new ObjectId(), undefined, {
        compositeData: { surveyId: survey._id, fragmentUtrvs: [] },
      });

      surveyRepoStub.resolves(survey);
      workgroupPermissionsStub.resolves(true);

      const result = await UtrvPermissions.canContribute(utrv, user);
      expect(result).to.be.true;
      sinon.assert.calledWith(workgroupPermissionsStub, {
        utrvs: [utrv],
        userId: user._id,
        roles: UtrvPermissions.canContributeRoles,
        survey,
      });
    });

    it('should return true if user has access through survey permissions', async () => {
      const user = createTestUser();
      const survey = createSurvey();
      const utrv = createUtrv(new ObjectId(), new ObjectId(), undefined, {
        compositeData: { surveyId: survey._id, fragmentUtrvs: [] },
      });

      surveyRepoStub.resolves(survey);
      workgroupPermissionsStub.resolves(false);
      surveyPermissionsCanContributeStub.resolves(true);

      const result = await UtrvPermissions.canContribute(utrv, user);
      expect(result).to.be.true;
      sinon.assert.calledWith(surveyPermissionsCanContributeStub, survey, user);
    });

    it('should return true if user has access through initiative permissions', async () => {
      const user = createTestUser();
      const utrv = createUtrv(new ObjectId(), new ObjectId(), undefined, {});

      initiativePermissionsCanContributeStub.resolves(true);

      const result = await UtrvPermissions.canContribute(utrv, user);
      expect(result).to.be.true;
      sinon.assert.calledWith(initiativePermissionsCanContributeStub, user, utrv.initiativeId.toString());
    });
  });

  describe('canVerify', () => {
    it('should return true if user is a verifier', async () => {
      const user = createTestUser();
      const utrv = createUtrv(new ObjectId(), new ObjectId(), undefined, {
        stakeholders: { stakeholder: [], verifier: [user._id], escalation: [] },
      });

      const result = await UtrvPermissions.canVerify(utrv, user);
      expect(result).to.be.true;
    });

    it('should return true if user has access through a workgroup', async () => {
      const user = createTestUser();
      const survey = createSurvey();
      const utrv = createUtrv(new ObjectId(), new ObjectId(), undefined, {
        compositeData: { surveyId: survey._id, fragmentUtrvs: [] },
      });

      surveyRepoStub.resolves(survey);
      workgroupPermissionsStub.resolves(true);

      const result = await UtrvPermissions.canVerify(utrv, user);
      expect(result).to.be.true;
      sinon.assert.calledWith(workgroupPermissionsStub, {
        utrvs: [utrv],
        userId: user._id,
        roles: UtrvPermissions.canVerifyRoles,
        survey,
      });
    });

    it('should return true if user has access through survey permissions', async () => {
      const user = createTestUser();
      const survey = createSurvey();
      const utrv = createUtrv(new ObjectId(), new ObjectId(), undefined, {
        compositeData: { surveyId: survey._id, fragmentUtrvs: [] },
      });

      surveyRepoStub.resolves(survey);
      workgroupPermissionsStub.resolves(false);
      surveyPermissionsCanVerifyStub.resolves(true);

      const result = await UtrvPermissions.canVerify(utrv, user);
      expect(result).to.be.true;
      sinon.assert.calledWith(surveyPermissionsCanVerifyStub, survey, user);
    });

    it('should return true if user has access through initiative permissions', async () => {
      const user = createTestUser();
      const utrv = createUtrv(new ObjectId(), new ObjectId(), undefined, {});

      initiativePermissionsCanVerifyStub.resolves(true);

      const result = await UtrvPermissions.canVerify(utrv, user);
      expect(result).to.be.true;
      sinon.assert.calledWith(initiativePermissionsCanVerifyStub, user, utrv.initiativeId.toString());
    });
  });

  describe('canAccessComments', () => {
    it('should return true if user can access UTRV', async () => {
      const user = createTestUser();
      const utrv = createUtrv(new ObjectId(), new ObjectId(), undefined, {});
      sandbox.stub(UtrvPermissions, 'canAccess').resolves(true);

      const result = await UtrvPermissions.canAccessComments(utrv, user);
      expect(result).to.be.true;
    });

    it('should return true if user can assure UTRV', async () => {
      const user = createTestUser();
      const utrv = createUtrv(new ObjectId(), new ObjectId(), undefined, {});
      sandbox.stub(UtrvPermissions, 'canAccess').resolves(false);
      sandbox.stub(UtrvPermissions, 'canAssure').resolves(true);

      const result = await UtrvPermissions.canAccessComments(utrv, user);
      expect(result).to.be.true;
    });
  });

  describe('canAssure', () => {
    it('should return true if user organization is in assurance portfolio', async () => {
      const user = createTestUser({ organizationId: new ObjectId() });
      const utrv = createUtrv(new ObjectId(), new ObjectId(), undefined, {});
      assuranceAggregateStub.resolves([{ organizationId: user.organizationId }]);

      const result = await UtrvPermissions.canAssure(utrv, user);
      expect(result).to.be.true;
    });

    it('should return false if user organization is not in assurance portfolio', async () => {
      const user = createTestUser();
      const utrv = createUtrv(new ObjectId(), new ObjectId(), undefined, {});
      assuranceAggregateStub.resolves([{ organizationId: new ObjectId() }]);

      const result = await UtrvPermissions.canAssure(utrv, user);
      expect(result).to.be.false;
    });
  });

  describe('hasAssurerAccessToUtrData', () => {
    it('should return false if user has no organizationId', async () => {
      const user = createTestUser({ organizationId: undefined });
      const result = await UtrvPermissions.hasAssurerAccessToUtrData(user, new ObjectId(), new ObjectId());
      expect(result).to.be.false;
    });

    it('should return false if no utrvs have assurance status', async () => {
      const user = createTestUser({ organizationId: new ObjectId() });
      utrvFindStub.resolves([{ assuranceStatus: undefined }]);
      const result = await UtrvPermissions.hasAssurerAccessToUtrData(user, new ObjectId(), new ObjectId());
      expect(result).to.be.false;
    });

    it('should return true if user organization is in assurance portfolio', async () => {
      const user = createTestUser({ organizationId: new ObjectId() });
      utrvFindStub.resolves([{ _id: new ObjectId(), assuranceStatus: 'status' }]);
      assuranceAggregateStub.resolves([{ organizationId: user.organizationId }]);
      const result = await UtrvPermissions.hasAssurerAccessToUtrData(user, new ObjectId(), new ObjectId());
      expect(result).to.be.true;
    });

    it('should return false if user organization is not in assurance portfolio', async () => {
      const user = createTestUser({ organizationId: new ObjectId() });
      utrvFindStub.resolves([{ _id: new ObjectId(), assuranceStatus: 'status' }]);
      assuranceAggregateStub.resolves([{ organizationId: new ObjectId() }]);
      const result = await UtrvPermissions.hasAssurerAccessToUtrData(user, new ObjectId(), new ObjectId());
      expect(result).to.be.false;
    });
  });

  describe('canContributeBulk', () => {
    it('should return true if user is a stakeholder for all utrvs', async () => {
      const user = createTestUser();
      const survey = createSurvey();
      const utrvs = [
        createUtrv(new ObjectId(), new ObjectId(), undefined, {
          stakeholders: { stakeholder: [user._id], verifier: [], escalation: [] },
        }),
        createUtrv(new ObjectId(), new ObjectId(), undefined, {
          stakeholders: { stakeholder: [user._id], verifier: [], escalation: [] },
        }),
      ];
      const result = await UtrvPermissions.canContributeBulk(utrvs, user, survey);
      expect(result).to.be.true;
    });

    it('should return true if user has workgroup permission', async () => {
      const user = createTestUser();
      const survey = createSurvey();
      const utrvs = [createUtrv(new ObjectId(), new ObjectId())];
      workgroupPermissionsStub.resolves(true);
      const result = await UtrvPermissions.canContributeBulk(utrvs, user, survey);
      expect(result).to.be.true;
    });

    it('should return true if user has survey permission', async () => {
      const user = createTestUser();
      const survey = createSurvey();
      const utrvs = [createUtrv(new ObjectId(), new ObjectId())];
      workgroupPermissionsStub.resolves(false);
      surveyPermissionsCanContributeStub.resolves(true);
      const result = await UtrvPermissions.canContributeBulk(utrvs, user, survey);
      expect(result).to.be.true;
    });

    it('should return false if user does not have permission', async () => {
      const user = createTestUser();
      const survey = createSurvey();
      const utrvs = [createUtrv(new ObjectId(), new ObjectId())];
      workgroupPermissionsStub.resolves(false);
      surveyPermissionsCanContributeStub.resolves(false);
      const result = await UtrvPermissions.canContributeBulk(utrvs, user, survey);
      expect(result).to.be.false;
    });
  });

  describe('canVerifyBulk', () => {
    it('should return true if user is a verifier for all utrvs', async () => {
      const user = createTestUser();
      const survey = createSurvey();
      const utrvs = [
        createUtrv(new ObjectId(), new ObjectId(), undefined, {
          stakeholders: { stakeholder: [], verifier: [user._id], escalation: [] },
        }),
        createUtrv(new ObjectId(), new ObjectId(), undefined, {
          stakeholders: { stakeholder: [], verifier: [user._id], escalation: [] },
        }),
      ];
      const result = await UtrvPermissions.canVerifyBulk(utrvs, user, survey);
      expect(result).to.be.true;
    });

    it('should return true if user has workgroup permission', async () => {
      const user = createTestUser();
      const survey = createSurvey();
      const utrvs = [createUtrv(new ObjectId(), new ObjectId())];
      workgroupPermissionsStub.resolves(true);
      const result = await UtrvPermissions.canVerifyBulk(utrvs, user, survey);
      expect(result).to.be.true;
    });

    it('should return true if user has survey permission', async () => {
      const user = createTestUser();
      const survey = createSurvey();
      const utrvs = [createUtrv(new ObjectId(), new ObjectId())];
      workgroupPermissionsStub.resolves(false);
      surveyPermissionsCanVerifyStub.resolves(true);
      const result = await UtrvPermissions.canVerifyBulk(utrvs, user, survey);
      expect(result).to.be.true;
    });

    it('should return false if user does not have permission', async () => {
      const user = createTestUser();
      const survey = createSurvey();
      const utrvs = [createUtrv(new ObjectId(), new ObjectId())];
      workgroupPermissionsStub.resolves(false);
      surveyPermissionsCanVerifyStub.resolves(false);
      const result = await UtrvPermissions.canVerifyBulk(utrvs, user, survey);
      expect(result).to.be.false;
    });
  });
});
