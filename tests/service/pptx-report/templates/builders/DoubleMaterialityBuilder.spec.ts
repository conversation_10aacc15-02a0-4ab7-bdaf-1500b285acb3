import { ObjectId } from 'bson';
import { DoubleMaterialityBuilder } from '../../../../../server/service/pptx-report/templates/builders/materiality-tracker/DoubleMaterialityBuilder';
import {
  AssessmentData,
  AssessmentResult,
  MaterialityAssessmentScope,
} from '../../../../../server/service/materiality-assessment/types';
import { ESGCategory, MaterialPillar, MaterialityBoundary } from '../../../../../server/models/materialTopics';
import { expect } from 'chai';
import { topicLengthMap } from '../../../../../server/service/materiality-assessment/constants';
import { roundTo } from '../../../../../server/util/number';

describe('DoubleMaterialityBuilder', () => {
  const initiativeId = new ObjectId();
  const surveyId = new ObjectId();
  const scopeSize = MaterialityAssessmentScope.Micro;

  const mockData = Array.from({ length: 25 }, (_, i) => ({
    name: `Topic ${i + 1}`,
    code: `code-${i + 1}`,
    score: Math.random() * 100,
    relativeScore: Math.random() * 100,
    categories: {
      esg: [ESGCategory.Environmental, ESGCategory.Social, ESGCategory.Governance],
      sdg: ['1', '1.1', '1.2', '2', '2.1', '2.2', '2.3'],
      materialPillar: [
        MaterialPillar.People,
        MaterialPillar.Partnership,
        MaterialPillar.Planet,
        MaterialPillar.Prosperity,
        MaterialPillar.Principle,
      ],
      boundary: [
        MaterialityBoundary.Leadership,
        MaterialityBoundary.ResearchAndDevelopment,
        MaterialityBoundary.SupplyChain,
        MaterialityBoundary.ProductAndServices,
        MaterialityBoundary.Distribution,
        MaterialityBoundary.Communities,
        MaterialityBoundary.Experiences,
      ],
    },
    utrMapping: [{ code: `utr-${i + 1}`, score: Math.random() * 100 }],
  }));
  const assessmentResult: AssessmentResult<AssessmentData> = {
    financial: mockData,
    nonFinancial: mockData,
  };

  describe('getRelevantTopTopics', () => {
    it('should return empty array when doubleAssessmentData is empty', () => {
      const builder = new DoubleMaterialityBuilder(
        initiativeId,
        {
          financial: [],
          nonFinancial: [],
        },
        surveyId,
        MaterialityAssessmentScope.Micro,
        false
      );
      expect(builder.getRelevantTopTopics()).to.eqls([]);
    });

    it('should return array with mapped elements', () => {
      const builder = new DoubleMaterialityBuilder(
        initiativeId,
        assessmentResult,
        surveyId,
        scopeSize,
        false
      );
      const result = builder.getRelevantTopTopics();
      const expectedResult = mockData
        .toSorted((a, b) => b.relativeScore - a.relativeScore)
        .map((topic, index) => ({
          ...topic,
          priority: index + 1,
        }))
        .slice(0, topicLengthMap[scopeSize])
        .map((topic) => ({
          name: topic.name,
          category: topic.categories?.esg?.[0],
          financialRelativeScore: topic.relativeScore,
          nonFinancialRelativeScore: topic.relativeScore,
          relativeScore: roundTo(topic.relativeScore),
          pillars: topic.categories?.materialPillar,
          priority: topic.priority,
        }));
      expect(result.length).to.eq(topicLengthMap[scopeSize]);
      expect(result).to.eqls(expectedResult);
    });
  });
});
