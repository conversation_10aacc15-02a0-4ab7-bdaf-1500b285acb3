import { expect } from 'chai';
import { AssessmentData } from '../../../../../server/service/materiality-assessment/types';
import { TableBuilder } from '../../../../../server/service/pptx-report/templates/builders/materiality-tracker/TableBuilder';
import { ESGCategory, MaterialPillar, MaterialityBoundary } from '../../../../../server/models/materialTopics';
import { DEFAULT_TABLE_CELL_VALUE } from '../../../../../server/service/pptx-report/constants';

const generateBoundariesByPillarsRow = (pillar: string) => [
  pillar,
  DEFAULT_TABLE_CELL_VALUE,
  DEFAULT_TABLE_CELL_VALUE,
  DEFAULT_TABLE_CELL_VALUE,
  DEFAULT_TABLE_CELL_VALUE,
  DEFAULT_TABLE_CELL_VALUE,
  DEFAULT_TABLE_CELL_VALUE,
  DEFAULT_TABLE_CELL_VALUE,
  DEFAULT_TABLE_CELL_VALUE,
];

const generateTopTopicsByBoundariesRow = () => [
  DEFAULT_TABLE_CELL_VALUE,
  DEFAULT_TABLE_CELL_VALUE,
  DEFAULT_TABLE_CELL_VALUE,
  DEFAULT_TABLE_CELL_VALUE,
  DEFAULT_TABLE_CELL_VALUE,
  DEFAULT_TABLE_CELL_VALUE,
  DEFAULT_TABLE_CELL_VALUE,
];

const generateAppendixDescriptionRow = (pillar: string) => [pillar, DEFAULT_TABLE_CELL_VALUE, DEFAULT_TABLE_CELL_VALUE];

describe('TableBuilder', () => {
  let pptxMTTableBuilder: TableBuilder;
  let hydratedTopics: AssessmentData[];
  const fixedTopics: AssessmentData[] = [
    {
      name: 'Topic 1',
      code: 'code-1',
      score: 99,
      relativeScore: 99,
      categories: {
        esg: [],
        sdg: ['1', '2'],
        materialPillar: [
          MaterialPillar.People,
          MaterialPillar.Partnership,
          MaterialPillar.Planet,
          MaterialPillar.Prosperity,
        ],
        boundary: [
          MaterialityBoundary.Leadership,
          MaterialityBoundary.ResearchAndDevelopment,
          MaterialityBoundary.SupplyChain,
          MaterialityBoundary.ProductAndServices,
          MaterialityBoundary.Distribution,
          MaterialityBoundary.Communities,
          MaterialityBoundary.Experiences,
        ],
      },
      utrMapping: [{ code: 'utr-1', score: Math.random() * 100 }],
      description: 'Topic 1 description',
      action: 'Topic 1 action',
    },
    {
      name: 'Topic 2',
      code: 'code-2',
      score: 80,
      relativeScore: 80,
      categories: {
        esg: [],
        sdg: ['1', '2', '3'],
        materialPillar: [MaterialPillar.People, MaterialPillar.Partnership, MaterialPillar.Principle],
        boundary: [
          MaterialityBoundary.Leadership,
          MaterialityBoundary.ResearchAndDevelopment,
          MaterialityBoundary.SupplyChain,
          MaterialityBoundary.ProductAndServices,
        ],
      },
      utrMapping: [{ code: `utr-2`, score: Math.random() * 100 }],
      description: 'Topic 2 description',
      action: 'Topic 2 action',
    },
  ];

  beforeEach(() => {
    hydratedTopics = Array.from({ length: 40 }, (_, i) => ({
      name: `Topic ${i + 1}`,
      code: `code-${i + 1}`,
      score: Math.random() * 100,
      relativeScore: Math.random() * 100,
      categories: {
        esg: i % 2 === 0 ? [ESGCategory.Environmental, ESGCategory.Social, ESGCategory.Governance] : [],
        sdg: i % 3 === 0 ? ['1', '1.1', '1.2', '2', '2.1', '2.2', '2.3'] : [],
        materialPillar:
          i % 4 === 0
            ? [
                MaterialPillar.People,
                MaterialPillar.Partnership,
                MaterialPillar.Planet,
                MaterialPillar.Prosperity,
                MaterialPillar.Principle,
              ]
            : [],
        boundary:
          i % 5 === 0
            ? [
                MaterialityBoundary.Leadership,
                MaterialityBoundary.ResearchAndDevelopment,
                MaterialityBoundary.SupplyChain,
                MaterialityBoundary.ProductAndServices,
                MaterialityBoundary.Distribution,
                MaterialityBoundary.Communities,
                MaterialityBoundary.Experiences,
              ]
            : [],
      },
      utrMapping: i % 6 === 0 ? [{ code: `utr-${i + 1}`, score: Math.random() * 100 }] : [],
    }));

    pptxMTTableBuilder = new TableBuilder(hydratedTopics);
  });

  describe('maxDecimals', () => {
    it('should set applyMaxDecimals and return the instance', () => {
      const result = pptxMTTableBuilder.maxDecimals(2);
      expect(result).to.be.an.instanceof(TableBuilder);
      expect(pptxMTTableBuilder.getApplyMaxDecimals()).to.equal(2);
    });
  });

  describe('applyDecimals', () => {
    it('should apply max decimals when applyMaxDecimals is set', () => {
      pptxMTTableBuilder.maxDecimals(2);
      const result = pptxMTTableBuilder.applyDecimals(1.2345);
      expect(result).to.equal(1.23);
    });

    it('should not apply max decimals when applyMaxDecimals is undefined', () => {
      pptxMTTableBuilder.maxDecimals(undefined);
      const result = pptxMTTableBuilder.applyDecimals(1.2345);
      expect(result).to.equal(1.2345);
    });
  });

  describe('toNum', () => {
    it('should convert valid number string to number', () => {
      const result = pptxMTTableBuilder.toNum('123');
      expect(result).to.equal(123);
    });

    it('should return 0 for invalid number string', () => {
      const result = pptxMTTableBuilder.toNum('abc');
      expect(result).to.equal(0);
    });

    it('should return the number itself', () => {
      const result = pptxMTTableBuilder.toNum(123);
      expect(result).to.equal(123);
    });
  });

  describe('getScoresTableRowCount', () => {
    it('should return the number of top 40 scored topics', () => {
      const result = pptxMTTableBuilder.getScoresTableRowCount();
      expect(result).to.be.a('number');
      expect(result).to.equal(40);
    });
  });

  describe('getScoresTable', () => {
    it('should return a table with topic names/codes and their scores', async () => {
      const result = await pptxMTTableBuilder.getScoresTable();
      const expectedResult = hydratedTopics
        .slice()
        .sort((a, b) => b.score - a.score)
        .slice(0, 40)
        .map((row) => ({
          values: [row.name || row.code, pptxMTTableBuilder.applyDecimals(pptxMTTableBuilder.toNum(row.score))],
        }));

      expect(result).to.be.an('array');
      expect(result).to.deep.equal(expectedResult);
    });
  });

  describe('getSummaryFindingsTable', () => {
    it('should return data for the summary findings table with empty hydrated topics', async () => {
      const builder = new TableBuilder([]);
      const result = await builder.getSummaryFindingsTable();
      expect(result).to.deep.equal([
        { values: ['People\n', '', '', ''] },
        { values: ['Partnership\n', '', '', ''] },
        { values: ['Planet\n', '', '', ''] },
        { values: ['Prosperity\n', '', '', ''] },
        { values: ['Principle\n', '', '', ''] },
      ]);
    });

    it('should return table with data', async () => {
      const builder = new TableBuilder(fixedTopics);
      const result = await builder.getSummaryFindingsTable();
      expect(result).to.deep.equal([
        {
          values: ['People\n', '1. Topic 1\n2. Topic 2', 'utr-1, utr-2', '1, 2, 3'],
        },
        {
          values: ['Partnership\n', '1. Topic 1\n2. Topic 2', 'utr-1, utr-2', '1, 2, 3'],
        },
        {
          values: ['Planet\n', '1. Topic 1', 'utr-1', '1, 2'],
        },
        {
          values: ['Prosperity\n', '1. Topic 1', 'utr-1', '1, 2'],
        },
        {
          values: ['Principle\n', '1. Topic 2', 'utr-2', '1, 2, 3'],
        },
      ]);
    });
  });

  describe('getBoundariesByPillarsTable', () => {
    it('should return data for the boundaries by pillars table with empty hydrated topics', async () => {
      const builder = new TableBuilder([]);
      const result = await builder.getBoundariesByPillarsTable();
      expect(result).to.deep.equal([
        {
          values: generateBoundariesByPillarsRow('People'),
        },
        {
          values: generateBoundariesByPillarsRow('People'),
        },
        {
          values: generateBoundariesByPillarsRow('People'),
        },
        {
          values: generateBoundariesByPillarsRow('Partnership'),
        },
        {
          values: generateBoundariesByPillarsRow('Partnership'),
        },
        {
          values: generateBoundariesByPillarsRow('Partnership'),
        },
        {
          values: generateBoundariesByPillarsRow('Planet'),
        },
        {
          values: generateBoundariesByPillarsRow('Planet'),
        },
        {
          values: generateBoundariesByPillarsRow('Planet'),
        },
        {
          values: generateBoundariesByPillarsRow('Prosperity'),
        },
        {
          values: generateBoundariesByPillarsRow('Prosperity'),
        },
        {
          values: generateBoundariesByPillarsRow('Prosperity'),
        },
        {
          values: generateBoundariesByPillarsRow('Principle'),
        },
        {
          values: generateBoundariesByPillarsRow('Principle'),
        },
        {
          values: generateBoundariesByPillarsRow('Principle'),
        },
      ]);
    });
    it('should return table with data', async () => {
      const builder = new TableBuilder(fixedTopics);
      const result = await builder.getBoundariesByPillarsTable();
      expect(result).to.deep.equal([
        {
          values: ['People', 'Topic 1', '✓', '✓', '✓', '✓', '✓', '✓', '✓'],
        },
        {
          values: [
            'People',
            'Topic 2',
            '✓',
            '✓',
            '✓',
            '✓',
            DEFAULT_TABLE_CELL_VALUE,
            DEFAULT_TABLE_CELL_VALUE,
            DEFAULT_TABLE_CELL_VALUE,
          ],
        },
        {
          values: generateBoundariesByPillarsRow('People'),
        },
        {
          values: ['Partnership', 'Topic 1', '✓', '✓', '✓', '✓', '✓', '✓', '✓'],
        },
        {
          values: [
            'Partnership',
            'Topic 2',
            '✓',
            '✓',
            '✓',
            '✓',
            DEFAULT_TABLE_CELL_VALUE,
            DEFAULT_TABLE_CELL_VALUE,
            DEFAULT_TABLE_CELL_VALUE,
          ],
        },
        {
          values: generateBoundariesByPillarsRow('Partnership'),
        },
        {
          values: ['Planet', 'Topic 1', '✓', '✓', '✓', '✓', '✓', '✓', '✓'],
        },
        {
          values: generateBoundariesByPillarsRow('Planet'),
        },
        {
          values: generateBoundariesByPillarsRow('Planet'),
        },
        {
          values: ['Prosperity', 'Topic 1', '✓', '✓', '✓', '✓', '✓', '✓', '✓'],
        },
        {
          values: generateBoundariesByPillarsRow('Prosperity'),
        },
        {
          values: generateBoundariesByPillarsRow('Prosperity'),
        },
        {
          values: [
            'Principle',
            'Topic 2',
            '✓',
            '✓',
            '✓',
            '✓',
            DEFAULT_TABLE_CELL_VALUE,
            DEFAULT_TABLE_CELL_VALUE,
            DEFAULT_TABLE_CELL_VALUE,
          ],
        },
        {
          values: generateBoundariesByPillarsRow('Principle'),
        },
        {
          values: generateBoundariesByPillarsRow('Principle'),
        },
      ]);
    });
  });

  describe('getTopTopicsByBoundariesTable', () => {
    it('should return data for the top topics by boundaries table with empty hydrated topics', async () => {
      const builder = new TableBuilder([]);
      const result = await builder.getTopTopicsByBoundariesTable();
      expect(result).to.deep.equal([
        {
          values: generateTopTopicsByBoundariesRow(),
        },
        {
          values: generateTopTopicsByBoundariesRow(),
        },
        {
          values: generateTopTopicsByBoundariesRow(),
        },
        {
          values: generateTopTopicsByBoundariesRow(),
        },
        {
          values: generateTopTopicsByBoundariesRow(),
        },
        {
          values: generateTopTopicsByBoundariesRow(),
        },
        {
          values: generateTopTopicsByBoundariesRow(),
        },
        {
          values: generateTopTopicsByBoundariesRow(),
        },
        {
          values: generateTopTopicsByBoundariesRow(),
        },
        {
          values: generateTopTopicsByBoundariesRow(),
        },
      ]);
    });
    it('should return table with data', async () => {
      const builder = new TableBuilder(fixedTopics);
      const result = await builder.getTopTopicsByBoundariesTable();
      expect(result).to.deep.equal([
        {
          values: ['Topic 1', 'Topic 1', 'Topic 1', 'Topic 1', 'Topic 1', 'Topic 1', 'Topic 1'],
        },
        {
          values: [
            'Topic 2',
            'Topic 2',
            'Topic 2',
            'Topic 2',
            DEFAULT_TABLE_CELL_VALUE,
            DEFAULT_TABLE_CELL_VALUE,
            DEFAULT_TABLE_CELL_VALUE,
          ],
        },
        {
          values: generateTopTopicsByBoundariesRow(),
        },
        {
          values: generateTopTopicsByBoundariesRow(),
        },
        {
          values: generateTopTopicsByBoundariesRow(),
        },
        {
          values: generateTopTopicsByBoundariesRow(),
        },
        {
          values: generateTopTopicsByBoundariesRow(),
        },
        {
          values: generateTopTopicsByBoundariesRow(),
        },
        {
          values: generateTopTopicsByBoundariesRow(),
        },
        {
          values: generateTopTopicsByBoundariesRow(),
        },
      ]);
    });
  });

  describe('getAppendixDescriptionTable', () => {
    it('should return data for the boundaries by pillars table with empty hydrated topics', async () => {
      const builder = new TableBuilder([]);
      const result = await builder.getAppendixDescriptionTable();
      expect(result).to.deep.equal([
        {
          values: generateAppendixDescriptionRow('People'),
        },
        {
          values: generateAppendixDescriptionRow('People'),
        },
        {
          values: generateAppendixDescriptionRow('People'),
        },
        {
          values: generateAppendixDescriptionRow('People'),
        },
        {
          values: generateAppendixDescriptionRow('Partnership'),
        },
        {
          values: generateAppendixDescriptionRow('Partnership'),
        },
        {
          values: generateAppendixDescriptionRow('Partnership'),
        },
        {
          values: generateAppendixDescriptionRow('Partnership'),
        },
        {
          values: generateAppendixDescriptionRow('Planet'),
        },
        {
          values: generateAppendixDescriptionRow('Planet'),
        },
        {
          values: generateAppendixDescriptionRow('Planet'),
        },
        {
          values: generateAppendixDescriptionRow('Planet'),
        },
        {
          values: generateAppendixDescriptionRow('Prosperity'),
        },
        {
          values: generateAppendixDescriptionRow('Prosperity'),
        },
        {
          values: generateAppendixDescriptionRow('Prosperity'),
        },
        {
          values: generateAppendixDescriptionRow('Prosperity'),
        },
        {
          values: generateAppendixDescriptionRow('Principle'),
        },
        {
          values: generateAppendixDescriptionRow('Principle'),
        },
        {
          values: generateAppendixDescriptionRow('Principle'),
        },
        {
          values: generateAppendixDescriptionRow('Principle'),
        },
      ]);
    });
    it('should return table with data', async () => {
      const builder = new TableBuilder(fixedTopics);
      const result = await builder.getAppendixDescriptionTable();
      expect(result).to.deep.equal([
        {
          values: ['People', 'Topic 1', 'Topic 1 description'],
        },
        {
          values: ['People', 'Topic 2', 'Topic 2 description'],
        },
        {
          values: generateAppendixDescriptionRow('People'),
        },
        {
          values: generateAppendixDescriptionRow('People'),
        },
        {
          values: ['Partnership', 'Topic 1', 'Topic 1 description'],
        },
        {
          values: ['Partnership', 'Topic 2', 'Topic 2 description'],
        },
        {
          values: generateAppendixDescriptionRow('Partnership'),
        },
        {
          values: generateAppendixDescriptionRow('Partnership'),
        },
        {
          values: ['Planet', 'Topic 1', 'Topic 1 description'],
        },
        {
          values: generateAppendixDescriptionRow('Planet'),
        },
        {
          values: generateAppendixDescriptionRow('Planet'),
        },
        {
          values: generateAppendixDescriptionRow('Planet'),
        },
        {
          values: ['Prosperity', 'Topic 1', 'Topic 1 description'],
        },
        {
          values: generateAppendixDescriptionRow('Prosperity'),
        },
        {
          values: generateAppendixDescriptionRow('Prosperity'),
        },
        {
          values: generateAppendixDescriptionRow('Prosperity'),
        },
        {
          values: ['Principle', 'Topic 2', 'Topic 2 description'],
        },
        {
          values: generateAppendixDescriptionRow('Principle'),
        },
        {
          values: generateAppendixDescriptionRow('Principle'),
        },
        {
          values: generateAppendixDescriptionRow('Principle'),
        },
      ]);
    });
  });

  describe('getAppendixActionTable', () => {
    it('should return data for the boundaries by pillars table with empty hydrated topics', async () => {
      const builder = new TableBuilder([]);
      const result = await builder.getAppendixActionTable();
      expect(result).to.deep.equal([
        {
          values: generateAppendixDescriptionRow('People'),
        },
        {
          values: generateAppendixDescriptionRow('People'),
        },
        {
          values: generateAppendixDescriptionRow('People'),
        },
        {
          values: generateAppendixDescriptionRow('People'),
        },
        {
          values: generateAppendixDescriptionRow('Partnership'),
        },
        {
          values: generateAppendixDescriptionRow('Partnership'),
        },
        {
          values: generateAppendixDescriptionRow('Partnership'),
        },
        {
          values: generateAppendixDescriptionRow('Partnership'),
        },
        {
          values: generateAppendixDescriptionRow('Planet'),
        },
        {
          values: generateAppendixDescriptionRow('Planet'),
        },
        {
          values: generateAppendixDescriptionRow('Planet'),
        },
        {
          values: generateAppendixDescriptionRow('Planet'),
        },
        {
          values: generateAppendixDescriptionRow('Prosperity'),
        },
        {
          values: generateAppendixDescriptionRow('Prosperity'),
        },
        {
          values: generateAppendixDescriptionRow('Prosperity'),
        },
        {
          values: generateAppendixDescriptionRow('Prosperity'),
        },
        {
          values: generateAppendixDescriptionRow('Principle'),
        },
        {
          values: generateAppendixDescriptionRow('Principle'),
        },
        {
          values: generateAppendixDescriptionRow('Principle'),
        },
        {
          values: generateAppendixDescriptionRow('Principle'),
        },
      ]);
    });
    it('should return table with data', async () => {
      const builder = new TableBuilder(fixedTopics);
      const result = await builder.getAppendixActionTable();
      expect(result).to.deep.equal([
        {
          values: ['People', 'Topic 1', 'Topic 1 action'],
        },
        {
          values: ['People', 'Topic 2', 'Topic 2 action'],
        },
        {
          values: generateAppendixDescriptionRow('People'),
        },
        {
          values: generateAppendixDescriptionRow('People'),
        },
        {
          values: ['Partnership', 'Topic 1', 'Topic 1 action'],
        },
        {
          values: ['Partnership', 'Topic 2', 'Topic 2 action'],
        },
        {
          values: generateAppendixDescriptionRow('Partnership'),
        },
        {
          values: generateAppendixDescriptionRow('Partnership'),
        },
        {
          values: ['Planet', 'Topic 1', 'Topic 1 action'],
        },
        {
          values: generateAppendixDescriptionRow('Planet'),
        },
        {
          values: generateAppendixDescriptionRow('Planet'),
        },
        {
          values: generateAppendixDescriptionRow('Planet'),
        },
        {
          values: ['Prosperity', 'Topic 1', 'Topic 1 action'],
        },
        {
          values: generateAppendixDescriptionRow('Prosperity'),
        },
        {
          values: generateAppendixDescriptionRow('Prosperity'),
        },
        {
          values: generateAppendixDescriptionRow('Prosperity'),
        },
        {
          values: ['Principle', 'Topic 2', 'Topic 2 action'],
        },
        {
          values: generateAppendixDescriptionRow('Principle'),
        },
        {
          values: generateAppendixDescriptionRow('Principle'),
        },
        {
          values: generateAppendixDescriptionRow('Principle'),
        },
      ]);
    });
  });

  describe('getDefinitionsTable', () => {
    it('should return empty for the appendix table with empty hydrated topics', async () => {
      const builder = new TableBuilder([]);
      const result = await builder.getDefinitionsTable({ startOffset: 0, endOffset: 5 });
      expect(result).to.deep.equal([]);
    });

    it('should return table with data', async () => {
      const builder = new TableBuilder(fixedTopics);
      const testResult1 = await builder.getDefinitionsTable({ startOffset: 0, endOffset: 1 });
      const expectedTopic1 = fixedTopics[0];
      const expectedTopic2 = fixedTopics[1];
      expect(testResult1).to.deep.equal([
        { values: [expectedTopic1.name, `Score: ${expectedTopic1.relativeScore} - ${expectedTopic1.description}`] },
      ]);

      const testResult2 = await builder.getDefinitionsTable({ startOffset: 0, endOffset: 4 });
      expect(testResult2).to.deep.equal([
        { values: [expectedTopic1.name, `Score: ${expectedTopic1.relativeScore} - ${expectedTopic1.description}`] },
        { values: [expectedTopic2.name, `Score: ${expectedTopic2.relativeScore} - ${expectedTopic2.description}`] },
      ]);
    });
  });
});
