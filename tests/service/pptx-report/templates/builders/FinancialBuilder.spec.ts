import sinon from 'sinon';
import { ObjectId } from 'bson';
import { FinancialBuilder } from '../../../../../server/service/pptx-report/templates/builders/materiality-tracker/FinancialBuilder';
import { AssessmentData, MaterialityAssessmentScope } from '../../../../../server/service/materiality-assessment/types';
import { ESGCategory, MaterialPillar, MaterialityBoundary } from '../../../../../server/models/materialTopics';
import Initiative from '../../../../../server/models/initiative';
import { expect } from 'chai';
import { Query } from 'mongoose';
import { TableBuilder } from '../../../../../server/service/pptx-report/templates/builders/materiality-tracker/TableBuilder';
import UniversalTracker, { UniversalTrackerPlain } from '../../../../../server/models/universalTracker';
import ValueList from '../../../../../server/models/valueList';
import UniversalTrackerValue from '../../../../../server/models/universalTrackerValue';
import { createUtr } from '../../../../fixtures/universalTrackerFixtures';
import { ValueValidationType } from '../../../../../server/models/public/universalTrackerType';
import { createMongooseModel } from '../../../../setup';
import { initiativeOneSimple } from '../../../../fixtures/initiativeFixtures';
import { capitalize, truncate } from '../../../../../server/util/string';

describe('FinancialBuilder', () => {
  let financialBuilder: FinancialBuilder;
  let financialAssessmentData: AssessmentData[];
  const initiativeId = new ObjectId();
  const surveyId = new ObjectId();
  const initiativeProjection = {
    _id: 1,
    industry: 1,
    name: 1,
    profile: 1,
  };
  const mockInitiative = { _id: initiativeId, industry: 'Tech', name: 'Company', profile: 'Profile' };

  beforeEach(() => {
    financialAssessmentData = Array.from({ length: 25 }, (_, i) => ({
      name: `Topic ${i + 1}`,
      code: `code-${i + 1}`,
      score: Math.random() * 100,
      categories: {
        esg: i % 2 === 0 ? [ESGCategory.Environmental, ESGCategory.Social, ESGCategory.Governance] : [],
        sdg: i % 3 === 0 ? ['1', '1.1', '1.2', '2', '2.1', '2.2', '2.3'] : [],
        materialPillar:
          i % 4 === 0
            ? [
                MaterialPillar.People,
                MaterialPillar.Partnership,
                MaterialPillar.Planet,
                MaterialPillar.Prosperity,
                MaterialPillar.Principle,
              ]
            : [],
        boundary:
          i % 5 === 0
            ? [
                MaterialityBoundary.Leadership,
                MaterialityBoundary.ResearchAndDevelopment,
                MaterialityBoundary.SupplyChain,
                MaterialityBoundary.ProductAndServices,
                MaterialityBoundary.Distribution,
                MaterialityBoundary.Communities,
                MaterialityBoundary.Experiences,
              ]
            : [],
      },
      utrMapping: i % 6 === 0 ? [{ code: `utr-${i + 1}`, score: Math.random() * 100 }] : [],
    }));
    financialBuilder = new FinancialBuilder(
      initiativeId,
      financialAssessmentData,
      surveyId,
      MaterialityAssessmentScope.Micro,
      Math.random() * 100
    );
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getInitiative', () => {
    it('should fetch and return the initiative if not already set', async () => {
      const initiativeFindStub = sinon
        .stub(Initiative, 'findById')
        .returns({ lean: () => mockInitiative } as unknown as Query<any, any>);

      const result = await financialBuilder.getInitiative();

      expect(result).to.deep.equal(mockInitiative);
      expect(initiativeFindStub.calledOnceWith(initiativeId, initiativeProjection)).to.be.true;
    });

    it('should return the initiative if already set', async () => {
      const initiativeFindStub = sinon
        .stub(Initiative, 'findById')
        .returns({ lean: () => mockInitiative } as unknown as Query<any, any>);

      await financialBuilder.getInitiative();
      const result = await financialBuilder.getInitiative();

      expect(result).to.deep.equal(mockInitiative);
      expect(initiativeFindStub.calledOnceWith(initiativeId, initiativeProjection)).to.be.true;
    });

    it('should throw an error if initiative is not found', async () => {
      sinon.stub(Initiative, 'findById').returns({ lean: () => null } as unknown as Query<any, any>);
      try {
        await financialBuilder.getInitiative();
      } catch (error) {
        expect(error.message).to.equal('Could not load initiative to generate PPTX report');
      }
    });
  });

  describe('getCompanyName', () => {
    it('should return the company name from the initiative', async () => {
      sinon.stub(Initiative, 'findById').returns({ lean: () => mockInitiative } as unknown as Query<any, any>);

      const result = await financialBuilder.getCompanyName();
      expect(result).to.equal('Company');
    });
  });

  describe('getTableBuilder', () => {
    it('should create and return a new PPTXMTTableBuilder instance if not already set', async () => {
      const result = await financialBuilder.getTableBuilder();
      expect(result).to.be.an.instanceof(TableBuilder);
    });

    it('should return the existing PPTXMTTableBuilder instance if already set', async () => {
      const tableBuilder1 = await financialBuilder.getTableBuilder();
      const tableBuilder2 = await financialBuilder.getTableBuilder();
      expect(tableBuilder1 === tableBuilder2).to.be.true;
    });
  });

  describe('getSectorName', () => {
    let templateBuilder: FinancialBuilder;
    let utr: UniversalTrackerPlain;

    beforeEach(() => {
      templateBuilder = new FinancialBuilder(
        initiativeId,
        financialAssessmentData,
        surveyId,
        MaterialityAssessmentScope.Micro,
        Math.random() * 100
      );
      utr = createUtr(new ObjectId(), 'materiality-2024/sector', {
        valueValidation: { valueList: { type: ValueValidationType.List, listId: new ObjectId() } },
      });
    });
    afterEach(() => {
      sinon.restore();
    });
    it('should return null if UniversalTracker not found', async () => {
      const findOneStub = sinon.stub(UniversalTracker, 'findOne').returns(createMongooseModel(null));
      const result = await templateBuilder.getSectorName();
      expect(result).to.be.null;
      expect(findOneStub.calledOnce).to.be.true;
    });

    it('should return null if ValueList not found', async () => {
      const findOneStub = sinon.stub(UniversalTracker, 'findOne').returns(createMongooseModel(utr));
      const valueListFindOneStub = sinon.stub(ValueList, 'findOne').returns(createMongooseModel(null));
      const result = await templateBuilder.getSectorName();
      expect(result).to.be.null;
      expect(findOneStub.calledOnce).to.be.true;
      expect(valueListFindOneStub.calledOnce).to.be.true;
    });

    it('should return null if UniversalTrackerValue not found', async () => {
      const findOneStub = sinon.stub(UniversalTracker, 'findOne').returns(createMongooseModel(utr));
      const valueListFindOneStub = sinon
        .stub(ValueList, 'findOne')
        .returns(createMongooseModel({ code: 'materiality-2024/sector' }));
      const universalTrackerValueFindOneStub = sinon
        .stub(UniversalTrackerValue, 'findOne')
        .returns(createMongooseModel(null));
      const result = await templateBuilder.getSectorName();
      expect(result).to.be.null;
      expect(findOneStub.calledOnce).to.be.true;
      expect(valueListFindOneStub.calledOnce).to.be.true;
      expect(universalTrackerValueFindOneStub.calledOnce).to.be.true;
    });

    it('should return null if ValueList options not found', async () => {
      const findOneStub = sinon.stub(UniversalTracker, 'findOne').returns(createMongooseModel(utr));
      const valueListFindOneStub = sinon
        .stub(ValueList, 'findOne')
        .returns(createMongooseModel({ code: 'materiality-2024/sector', options: [] }));
      const universalTrackerValueFindOneStub = sinon
        .stub(UniversalTrackerValue, 'findOne')
        .returns(createMongooseModel({ valueData: { data: 'option-code' } }));
      const result = await templateBuilder.getSectorName();
      expect(result).to.be.null;
      expect(findOneStub.calledOnce).to.be.true;
      expect(valueListFindOneStub.calledOnce).to.be.true;
      expect(universalTrackerValueFindOneStub.calledOnce).to.be.true;
    });

    it('should return sector name if all data found', async () => {
      const findOneStub = sinon.stub(UniversalTracker, 'findOne').returns(createMongooseModel(utr));
      const valueListFindOneStub = sinon.stub(ValueList, 'findOne').returns(
        createMongooseModel({
          code: 'materiality-2024/sector',
          options: [{ code: 'option-code', name: 'sector-name' }],
        })
      );
      const universalTrackerValueFindOneStub = sinon
        .stub(UniversalTrackerValue, 'findOne')
        .returns(createMongooseModel({ valueData: { data: 'option-code' } }));

      const result = await templateBuilder.getSectorName();
      expect(result).to.equal('sector-name');
      expect(findOneStub.calledOnce).to.be.true;
      expect(valueListFindOneStub.calledOnce).to.be.true;
      expect(universalTrackerValueFindOneStub.calledOnce).to.be.true;
    });
  });

  describe('getPillarsTextReplacement', () => {
    it('should return N/A if financial assessmet data is empty', async () => {
      const emptyAssessmentData: AssessmentData[] = [];
      const builder = new FinancialBuilder(
        initiativeId,
        emptyAssessmentData,
        surveyId,
        MaterialityAssessmentScope.Micro,
        Math.random() * 100
      );

      const result = builder.getPillarsTextReplacement(initiativeOneSimple.name);
      expect(result.length).equal(Object.keys(MaterialPillar).length);
      Array.from({ length: result.length }, (_, i) => i).forEach(async (index) => {
        expect(await result[index][1].text({ slideNum: 1 })).to.equal(
          `Based on your assessment results, no highly relevant topics have been categorised under the ${capitalize(
            result[index][0]
          )} pillar for ${initiativeOneSimple.name}.`
        );
      });
    });

    it('should return correct pillars text replacements', async () => {
      const result = financialBuilder.getPillarsTextReplacement(initiativeOneSimple.name);
      Object.values(MaterialPillar).forEach(async (pillar, index) => {
        const filteredPillars = financialAssessmentData.filter((data) =>
          data.categories?.materialPillar?.includes(MaterialPillar.People)
        );
        const displayText = filteredPillars
          .map((topic, index) => `${index + 1}. ${truncate(topic.name || '', 50, true)}`)
          .join('\n');
        expect(result[index][0]).to.equal(pillar);
        const test = await result[index][1].text({ slideNum: 1 });
        expect(test).to.equal(displayText);
      });
    });
  });
});
