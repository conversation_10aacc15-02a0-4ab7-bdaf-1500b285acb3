/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { expect } from 'chai';
import sinon from 'sinon';
import axios from 'axios';
import fs from 'fs/promises';
import Automizer, { ISlide } from 'pptx-automizer';
import JSZip from 'jszip';
import { PPTXTemplate } from '../../../server/service/pptx-report/PPTXTemplate';
import {
  PPTXTemplateConfig,
  PPTXTemplateImageReplacement,
} from '../../../server/service/pptx-report/templates/PPTXTemplateInterface';
import { PPTXTemplateName, TemplateContext } from '../../../server/service/pptx-report/types';
import { ObjectId } from 'bson';

describe('PPTXTemplate', () => {
  const sandbox = sinon.createSandbox();

  let mockPresentation: sinon.SinonStubbedInstance<Automizer>;
  let mockConfig: PPTXTemplateConfig;
  let mockContext: TemplateContext;
  let pptxTemplate: PPTXTemplate;

  // Mock data
  const mockImageBuffer = Buffer.from('mock-image-data');
  const testImageUrl = 'https://example.com/bucket/image.jpg?md5hash123';
  const testImageUrlWithSlashes = 'https://example.com/bucket/image.jpg?md5/hash/with/slashes';
  const mockSlide = {
    modifyElement: sandbox.stub(),
    removeElement: sandbox.stub(),
    modify: sandbox.stub(),
  } as unknown as ISlide;

  // Typed stub references
  let axiosGetStub: sinon.SinonStub;
  let fsWriteFileStub: sinon.SinonStub;

  beforeEach(() => {
    // Stub file system operations and assign to typed references
    sandbox.stub(fs, 'mkdir').resolves();
    sandbox.stub(fs, 'rm').resolves();
    fsWriteFileStub = sandbox.stub(fs, 'writeFile').resolves();

    // Stub axios
    axiosGetStub = sandbox.stub(axios, 'get').resolves({
      data: mockImageBuffer,
    });

    // Create mock presentation
    mockPresentation = sandbox.createStubInstance(Automizer);
    mockPresentation.loadRoot.returns(mockPresentation);
    mockPresentation.load.returns(mockPresentation);
    mockPresentation.loadMedia.resolves();
    mockPresentation.addMaster.returns(mockPresentation);
    mockPresentation.modify.returns(mockPresentation);
    mockPresentation.getJSZip.resolves({} as JSZip);
    mockPresentation.addSlide.callsFake((themeName, slideId, callback) => {
      if (callback) {
        callback(mockSlide);
      }
      return mockPresentation;
    });

    // Create mock context
    mockContext = {
      initiativeId: new ObjectId(),
      surveyId: new ObjectId(),
      userId: new ObjectId(),
      templateName: PPTXTemplateName.CT,
    };

    // Create basic mock config
    mockConfig = {
      templateFilename: 'test-template.pptx',
      slides: [],
    };

    pptxTemplate = new PPTXTemplate(mockConfig, mockPresentation, mockContext);
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('generate', () => {
    describe('successful image URL processing', () => {
      it('should successfully process image URL and extract filename correctly', async () => {
        const mockImageReplacement: PPTXTemplateImageReplacement = {
          imageUrl: () => Promise.resolve(testImageUrl),
        };

        mockConfig.slides = [
          {
            slideId: 1,
            imageReplacements: [['TEST_IMAGE', mockImageReplacement]],
          },
        ];

        await pptxTemplate.generate();

        // Verify axios was called with correct URL
        expect(axiosGetStub.calledOnceWith(testImageUrl, { responseType: 'arraybuffer' })).to.be.true;

        // Verify file was written with correct filename
        const writeFileCall = fsWriteFileStub.getCall(0);
        expect(Buffer.isBuffer(writeFileCall.args[1])).to.be.true;
        expect(writeFileCall.args[0]).to.include('image.jpg');

        // Verify loadMedia was called with the filename
        expect(mockPresentation.loadMedia.calledOnce).to.be.true;
        const loadMediaCall = mockPresentation.loadMedia.getCall(0);
        expect(loadMediaCall.args[0]).to.include('image.jpg');
      });

      it('should handle image URL with slashes in md5 hash correctly', async () => {
        const mockImageReplacement: PPTXTemplateImageReplacement = {
          imageUrl: () => Promise.resolve(testImageUrlWithSlashes),
        };

        mockConfig.slides = [
          {
            slideId: 1,
            imageReplacements: [['TEST_IMAGE', mockImageReplacement]],
          },
        ];

        pptxTemplate = new PPTXTemplate(mockConfig, mockPresentation, mockContext);

        await pptxTemplate.generate();

        // Verify filename extraction works correctly with slashes in hash
        const writeFileCall = fsWriteFileStub.getCall(0);
        expect(writeFileCall.args[0]).to.include('image.jpg');

        // Verify loadMedia was called with correct filename
        const loadMediaCall = mockPresentation.loadMedia.getCall(0);
        expect(loadMediaCall.args[0]).to.include('image.jpg');
      });
    });
  });
});
