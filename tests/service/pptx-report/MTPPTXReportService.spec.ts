import { ObjectId } from 'bson';
import { expect } from 'chai';
import ContextError from '../../../server/error/ContextError';
import { TaskType } from '../../../server/models/backgroundJob';
import { SurveyModelPlain } from '../../../server/models/survey';
import { getBackgroundJobService } from '../../../server/service/background-process/BackgroundJobService';
import { SupportedJobPlain } from '../../../server/service/materiality-assessment/background-job/types';
import { AssessmentResultType } from '../../../server/service/materiality-assessment/types';
import { MTPPTXReportService } from '../../../server/service/pptx-report/MTPPTXReportService';
import {
  MTPPTXTemplateScheme,
  PPTXTemplateName,
  TemplateContextInput,
} from '../../../server/service/pptx-report/types';
import { wwgLogger } from '../../../server/service/wwgLogger';
import { AssessmentType } from '../../../server/types/materiality-assessment';
import { initiativeOneSimple } from '../../fixtures/initiativeFixtures';
import { surveyOne } from '../../fixtures/survey';
import { userOne } from '../../fixtures/userFixtures';

describe('MTPPTXReportService', () => {
  const backgroundJobService = getBackgroundJobService();
  const service = new MTPPTXReportService(wwgLogger, backgroundJobService);

  const userId = userOne._id;
  const initiativeId = initiativeOneSimple._id;
  const assessmentJob = { _id: new ObjectId(), tasks: [{ data: {} }] } as SupportedJobPlain;

  const getParams = (survey: SurveyModelPlain) => ({
    userId,
    initiativeId,
    survey,
    assessmentJob,
  });

  describe('getMTReportJobContext', () => {
    it('should return context for Financial Materiality', () => {
      const surveyFinancial = { ...surveyOne, assessmentType: AssessmentType.FinancialMateriality };

      const expectedContext: TemplateContextInput = {
        templateName: PPTXTemplateName.MT,
        templateScheme: MTPPTXTemplateScheme.FinancialReport,
        type: TaskType.GenerateMaterialityTrackerFinancialReport,
        userId,
        initiativeId,
        surveyId: surveyFinancial._id,
        financialAssessmentDataMin: [],
        scoreJobId: assessmentJob._id,
        metadata: {
          effectiveDate: surveyFinancial.effectiveDate,
        },
      };

      const context = service.getMTReportJobContext(getParams(surveyFinancial));
      expect(context).to.deep.equal(expectedContext);
    });

    it('should return context for Double Materiality', () => {
      const surveyDouble = { ...surveyOne, assessmentType: AssessmentType.DoubleMateriality };

      const expectedContext: TemplateContextInput = {
        templateName: PPTXTemplateName.MT,
        templateScheme: MTPPTXTemplateScheme.DoubleMateriality,
        type: TaskType.GenerateMaterialityTrackerDoubleMaterialityReport,
        userId,
        initiativeId,
        surveyId: surveyDouble._id,
        assessmentDataMin: {
          [AssessmentResultType.Financial]: [],
          [AssessmentResultType.Impact]: [],
        },
        scoreJobId: assessmentJob._id,
        metadata: {
          effectiveDate: surveyDouble.effectiveDate,
          hasCustomOrder: false,
        },
      };
      const context = service.getMTReportJobContext(getParams(surveyDouble));
      expect(context).to.deep.equal(expectedContext);
    });

    it('should throw ContextError for unsupported assessment type', () => {
      const surveyUnsupported = { ...surveyOne, assessmentType: 'unsupported' } as unknown as SurveyModelPlain;
      expect(() => service.getMTReportJobContext(getParams(surveyUnsupported))).to.throw(
        ContextError,
        'Unsupported assessment type'
      );
    });
  });
});
