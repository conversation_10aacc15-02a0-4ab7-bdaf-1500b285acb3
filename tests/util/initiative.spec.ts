import { expect } from 'chai';
import { getAddressText } from '../../server/util/initiative';
import type { InitiativePlain } from '../../server/models/initiative';

describe('initiative utils', () => {
  describe('getAddressText', () => {
    it('should return a full address string when all parts are present', () => {
      const address: InitiativePlain['address'] = {
        line1: '123 Main St',
        line2: 'Apt 4B',
        city: 'Anytown',
        postcode: '12345',
      };
      const result = getAddressText(address);
      expect(result).to.equal('123 Main St, Apt 4B, Anytown, 12345');
    });

    it('should handle missing optional parts', () => {
      const address: InitiativePlain['address'] = {
        line1: '123 Main St',
        city: 'Anytown',
        postcode: '12345',
      };
      const result = getAddressText(address);
      expect(result).to.equal('123 Main St, Anytown, 12345');
    });

    it('should handle only required parts', () => {
      const address: <PERSON><PERSON><PERSON>['address'] = {
        line1: '123 Main St',
        postcode: '',
      };
      const result = getAddressText(address);
      expect(result).to.equal('123 Main St');
    });

    it('should return an empty indicator if address is undefined', () => {
      const result = getAddressText(undefined);
      expect(result).to.equal('-');
    });

    it('should return an empty indicator if address is null', () => {
      const result = getAddressText(undefined);
      expect(result).to.equal('-');
    });

    it('should filter out empty or whitespace-only parts', () => {
      const address: InitiativePlain['address'] = {
        line1: '123 Main St',
        line2: '  ',
        city: 'Anytown',
        postcode: '',
      };
      const result = getAddressText(address);
      expect(result).to.equal('123 Main St, Anytown');
    });
  });
});
