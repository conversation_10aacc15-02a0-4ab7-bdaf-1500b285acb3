/* eslint-disable no-undef */

db['assurance-portfolios'].aggregate([
  {
    $match: {
      permissions: { $exists: false },
    },
  },
  {
    $lookup: {
      from: 'users',
      let: { assuranceOrgId: '$organizationId' },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [
                { $ne: ["$organizationId", null] },
                { $eq: ["$organizationId", "$$assuranceOrgId"] },
                { $eq: ["$active", true] },
              ]
            }
          }
        },
      ],
      as: 'users'
    }
  },
  {
    $project: {
      _id: 1,
      stakeholders: 1,
      permissions: 1,
      orgUserIds: "$users._id",
    }
  }
]).forEach(function (row) {

  // Manually build up a map
  const userMap = {}
  row.orgUserIds.forEach(userId => {
    // Need to store a string, for later conversion back to objectId
    const id = userId.str
    if (!userMap[id]) {
      userMap[id] = ['user']
    }
  })

  // Override default user map values
  const userIds = row.stakeholders ? row.stakeholders.stakeholder : []
  userIds.forEach((userId, index) => {
      userMap[userId.str] = [index === 0 ? 'admin' : 'user']
  });
  const permissions = []

  for (const userId in userMap) {
    permissions.push({ userId: ObjectId(userId), permissions: userMap[userId]})
  }

  db['assurance-portfolios'].updateOne(
    { _id: row._id },
    {
      $set: {
        permissions: permissions
      },
    }
  );
});
