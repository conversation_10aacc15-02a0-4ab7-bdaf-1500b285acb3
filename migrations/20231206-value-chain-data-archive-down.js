/* eslint-disable no-undef */

db['vc-initiatives'].aggregate([
  {
    $match: {
      tags: {
        $in: [
          "external-csr",
          "operations",
          "products-services",
        ],
      },
    },
  },
  {
    $project: {
      _id: 1,
    }
  },
]).forEach(function (row) {
  // For each initiative, migrate utrv first universal-tracker-values
  db['vc-universal-tracker-values'].aggregate(
    {
      $match: { initiativeId: row._id },
    },
    {
      $merge: { into: 'universal-tracker-values' },
    },
  );

  db['vc-initiatives'].aggregate(
    {
      $match: { _id: row._id },
    },
    {
      $merge: { into: 'initiatives' },
    },
  );

  // Delete it
  db['vc-universal-tracker-values'].deleteMany({ initiativeId: row._id, });
  db['vc-initiatives'].deleteOne({ _id: row._id, });
});

