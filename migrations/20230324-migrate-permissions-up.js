/* eslint-disable no-undef */

let total = {
  matches: 0,
  changed: 0
}
db['users'].find({ 'permissions.permissions': 'manager' }).forEach(function (row) {
  let changed = false;
    total.matches += 1;
    const permissions = row.permissions.map(function (p) {
    if (p.permissions.includes('contributor') || !p.permissions.includes('manager')) {
      return p; // Nothing to do, skip
    }
    changed = true;
    return {
      initiativeId: p.initiativeId,
      permissions: [...new Set([...p.permissions, 'contributor'])],
      permissionAdded: 'contributor',
    }
  });
  if (changed) {
    total.changed += 1;
    printjson({ _id: row._id, status: 'changed', before: row.permissions, after: permissions });
    db['users'].updateOne({ _id: row._id }, { $set: { permissions: permissions } });
  } else {
    printjson({ _id: row._id, status: 'unchanged' });
  }
});
printjson({ status: 'completed', total });

