/* eslint-disable no-undef */

db['data-share'].aggregate([
  {
    $match: {
      title: 'Data Share SGX',
      'dataScope.survey': { $exists: true },
      'dataScope.survey.views': { $exists: false }
    },
  },
]).forEach(function (row) {
  db['data-share'].updateOne(
    { _id: row._id },
    {
      $set: {
        'dataScope.survey.views': ['insights', 'downloads', 'survey']
      },
    }
  );
});
