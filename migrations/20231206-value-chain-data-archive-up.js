/* eslint-disable no-undef */
db['initiatives'].aggregate([
  {
    $match: {
      tags: {
        $in: [
          "external-csr",
          "operations",
          "products-services",
        ],
      },
    },
  },
  {
    $project: {
      _id: 1,
    }
  },
]).forEach(function (row) {
  // For each initiative, migrate utrv first universal-tracker-values
  db['universal-tracker-values'].aggregate(
    {
      $match: { initiativeId: row._id },
    },
    {
      $merge: { into: 'vc-universal-tracker-values' },
    },
  );

  db['initiatives'].aggregate(
    {
      $match: { _id: row._id },
    },
    {
      $merge: { into: 'vc-initiatives' },
    },
  );

  // Delete it
  db['universal-tracker-values'].deleteMany({ initiativeId: row._id, });
  db['initiatives'].deleteOne({ _id: row._id, });
});
