# Cursor IDE Instructions - G17Eco API

This file provides guidance for Cursor IDE when working with this codebase.

## Quick Reference Links
- [Architecture Overview](../core/architecture.md) - System architecture and domain concepts
- [Core Commands](../core/commands.md) - Development and testing commands
- [Testing Framework](../testing/framework.md) - Testing stack and organization
- [Service Testing Patterns](../patterns/service-testing.md) - Testing patterns and templates

## Cursor-Specific Guidelines

### Code Generation
- Use existing patterns from the codebase
- Follow TypeScript strict mode conventions
- Maintain consistency with existing service architectures

### Test Generation
- Reference [Service Testing Patterns](../patterns/service-testing.md) for templates
- Use the established Mocha + Chai + Sinon stack
- Follow existing fixture and mock patterns

### Refactoring
- Preserve existing API contracts
- Maintain test coverage during refactoring
- Follow the modular architecture patterns

### AI Features
- Use Cursor's autocomplete with existing patterns
- Lev<PERSON>ge Cursor's chat for architecture questions
- Reference the modular docs for context

## Development Workflow
1. Use `npm start` for development server
2. Run `npm test` before commits
3. Check `npm run test-coverage` for coverage reports
4. Use TypeScript compilation for type checking