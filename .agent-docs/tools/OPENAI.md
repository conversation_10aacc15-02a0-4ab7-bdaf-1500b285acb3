# OpenAI/ChatGPT Instructions - G17Eco API

When working with the G17Eco API codebase, please reference these documentation modules:

## Core Documentation
- [Architecture Overview](../core/architecture.md) - System layers and domain concepts
- [Core Commands](../core/commands.md) - Development, testing, and deployment commands

## Testing Documentation
- [Testing Framework](../testing/framework.md) - Mocha/Chai/Sinon setup and conventions
- [Service Testing Patterns](../patterns/service-testing.md) - Testing templates and patterns
- [Error Handling Patterns](../patterns/error-handling.md) - Error testing and anti-patterns
- [Test Data Management](../patterns/test-data.md) - Fixtures, factories, and integration tests

## Key Principles
1. **Modular Architecture**: Follow the established service/repository/model layers
2. **TypeScript First**: Use strict TypeScript with proper typing
3. **Test Coverage**: Maintain 90%+ coverage for services
4. **Domain-Driven**: Organize code by business domains (user, survey, initiative, etc.)

## Common Tasks
- **Service Creation**: Follow patterns in `server/service/` with corresponding tests
- **API Endpoints**: Use existing route patterns in `server/routes/`
- **Database Operations**: Use repository layer for data access
- **Testing**: Use the established Mocha + Chai + Sinon patterns

## Development Commands
```bash
npm start              # Development server
npm test              # Run tests
npm run test-coverage # Coverage report
npm run build-ts      # Build TypeScript
```