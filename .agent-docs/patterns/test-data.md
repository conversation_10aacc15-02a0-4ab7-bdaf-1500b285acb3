# Test Data Management - G17Eco API

## Test Data Management

### 1. Fixtures Organization
```typescript
// Domain-specific fixtures
export const userFixtures = {
  userOne: { _id: new ObjectId(), email: '<EMAIL>' },
  userTwo: { _id: new ObjectId(), email: '<EMAIL>' }
};

// Shared test utilities
export const cloneObject = (obj: any) => JSON.parse(JSON.stringify(obj));
```

### 2. Factory Pattern
```typescript
// Object factories for dynamic test data
export const createUserModel = (overrides = {}) => ({
  _id: new ObjectId(),
  email: '<EMAIL>',
  active: true,
  ...overrides
});
```

### 3. Mock Services
```typescript
// Comprehensive mock implementations
export class MockMailer {
  sendEmail = sinon.stub().resolves();
  sendBulkEmail = sinon.stub().resolves();
}
```

## Performance and Integration Testing

### 1. Database Integration Tests
```typescript
// Using MongoDB Memory Server for integration tests
import setup from '../../setup';

describe('Integration Test', () => {
  before(async () => {
    await setup.connectToDatabase();
  });
  
  after(async () => {
    await setup.disconnectFromDatabase();
  });
});
```

### 2. Timeout Handling
```typescript
// Tests with custom timeouts for slow operations
it('should handle slow operation', async function() {
  this.timeout(10000); // 10 second timeout
  const result = await service.slowMethod();
  expect(result).to.exist;
});
```