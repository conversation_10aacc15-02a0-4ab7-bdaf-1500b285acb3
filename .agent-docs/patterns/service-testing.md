# Service Testing Patterns - G17Eco API

## Service Test Structure Template

```typescript
import { expect } from 'chai';
import sinon from 'sinon';
import { ServiceClass } from '../../../server/service/domain/ServiceClass';
import { RelatedRepository } from '../../../server/repository/RelatedRepository';
import { testFixtures } from '../../fixtures/testFixtures';

describe('ServiceClass', () => {
  const sandbox = sinon.createSandbox();
  const service = new ServiceClass(/* dependencies */);

  afterEach(() => {
    sandbox.restore();
  });

  describe('methodName', () => {
    it('should handle success case correctly', async () => {
      // Arrange
      const stub = sandbox.stub(RelatedRepository, 'method').resolves(testData);
      
      // Act
      const result = await service.methodName(input);
      
      // Assert
      expect(stub.calledOnceWith(expectedArgs)).to.be.true;
      expect(result).to.deep.equal(expectedResult);
    });

    it('should handle error case appropriately', async () => {
      // Arrange
      sandbox.stub(RelatedRepository, 'method').rejects(new Error('Test error'));
      
      // Act & Assert
      await expect(service.methodName(input)).to.be.rejectedWith('Test error');
    });
  });
});
```

## Core Testing Patterns

### 1. Dependency Injection Testing
```typescript
// Service with dependencies
const emailService = createUserEmailService();
const userEventService = createUserEventService();
const manager = new UserManager(emailService, userEventService, repository);

// Test with mocked dependencies
sandbox.stub(emailService, 'sendEmail').resolves();
```

### 2. Repository Stubbing Pattern
```typescript
// Using setup.wrapStub for Mongoose models
const findStub = setup.wrapStub(sandbox, UserRepository, 'findById', testUser);

// Direct stubbing for simple cases
const repoStub = sandbox.stub(repository, 'find').resolves([testData]);
```

### 3. Async Testing with Promises
```typescript
// Successful async operations
it('should process async operation', async () => {
  const result = await service.asyncMethod(input);
  expect(result).to.equal(expectedValue);
});

// Error handling
it('should handle async errors', async () => {
  sandbox.stub(dependency, 'method').rejects(new Error('Async error'));
  await expect(service.asyncMethod(input)).to.be.rejectedWith('Async error');
});
```

### 4. Parameterized Testing
```typescript
const testCases = [
  {
    describe: 'should handle case A',
    input: { type: 'A' },
    expected: { result: 'A processed' }
  },
  {
    describe: 'should handle case B', 
    input: { type: 'B' },
    expected: { result: 'B processed' }
  }
];

testCases.forEach(({ describe, input, expected }) => {
  it(describe, async () => {
    const result = await service.process(input);
    expect(result).to.deep.equal(expected);
  });
});
```

### 5. Complex Object Matching
```typescript
// Using deep equality for complex objects
expect(result).to.deep.equal(expectedComplexObject);

// Partial matching for large objects
expect(result).to.include({
  id: expectedId,
  status: expectedStatus
});

// Array membership testing
expect(result.items).to.have.members(expectedItems);
```

## Stub and Mock Management

### 1. Sinon Sandbox Pattern
```typescript
describe('ServiceTest', () => {
  const sandbox = sinon.createSandbox();
  
  afterEach(() => {
    sandbox.restore(); // Clean up all stubs
  });
  
  // Use sandbox.stub() for all stubs
});
```

### 2. Stub Verification
```typescript
// Verify call count and arguments
expect(stub.calledOnce).to.be.true;
expect(stub.calledWith(expectedArg)).to.be.true;
expect(stub.calledOnceWith(expectedArg)).to.be.true;

// Access call arguments
expect(stub.args[0][0]).to.equal(firstCallFirstArg);
```

### 3. Complex Stub Behaviors
```typescript
// Conditional responses
stub.callsFake((condition) => {
  if (condition === 'success') return Promise.resolve(successData);
  return Promise.reject(new Error('Failure'));
});

// Sequential responses
stub.onFirstCall().resolves(firstResult)
    .onSecondCall().resolves(secondResult);
```