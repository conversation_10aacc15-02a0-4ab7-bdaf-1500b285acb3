# Architecture Overview - G17Eco API

## Core Architecture Layers

This is a **Node.js/Express API** for the G17Eco sustainability platform, built with TypeScript and MongoDB. The architecture follows a modular pattern with clear separation of concerns.

**Models Layer** (`server/models/`)
- Mongoose schemas for MongoDB collections
- Core entities: Initiative, Survey, UniversalTracker, Organization, User
- Shared schemas in `models/common/` for reusable components
- Public types in `models/public/` for API responses

**Routes Layer** (`server/routes/`)
- RESTful API endpoints organized by feature
- Admin routes in `routes/admin/` for internal operations
- Public API routes in `routes/public/` with v1 versioning
- Manager routes for portfolio/organization management
- Validation schemas in `routes/validation-schemas/`

**Service Layer** (`server/service/`)
- Business logic organized by domain (survey, initiative, user, etc.)
- Background processing services for async jobs
- External integrations (Stripe, email, AI services)
- Utility services (file handling, notifications, reporting)

**Repository Layer** (`server/repository/`)
- Data access layer with aggregation pipelines
- MongoDB query optimization and projections
- Centralized database operations

**Middleware Layer** (`server/middleware/`)
- Authentication and authorization
- Request validation and context setup
- Audit logging and error handling

## Key Domain Concepts

**Initiatives** - Core sustainability projects/companies being tracked
**Universal Trackers (UTR)** - Configurable metrics for tracking ESG data
**Surveys** - Data collection templates with delegation and workflow
**Workgroups** - User collaboration and data sharing groups
**Assurance** - Third-party verification and audit trails
**Materiality Assessment** - ESG impact scoring and analysis

## Database Architecture

**Primary Database**: MongoDB with Mongoose ODM
**Caching**: Redis for session and API response caching
**Ledger**: AWS QLDB for immutable audit trails
**Files**: Cloud storage (Google Cloud/Azure) with local filesystem fallback

## External Integrations

- **Stripe** - Payment processing and subscription management
- **Okta** - Authentication and user management
- **AWS SES/SQS** - Email delivery and message queuing
- **Google Cloud/Azure** - File storage and compute services
- **AI Services** - OpenAI GPT and Anthropic Claude for data analysis