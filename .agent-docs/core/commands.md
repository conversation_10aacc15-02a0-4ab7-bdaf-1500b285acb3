# Core Commands - G17Eco API

## Development Commands
- `npm start` - Start development server with TypeScript watch mode and auto-restart
- `npm run ts` - Run TypeScript server directly with ts-node-dev
- `npm run ts-debug` - Run with Node.js inspector for debugging
- `npm run build-ts` - Build TypeScript to dist/ directory
- `npm run serve` - Run built JavaScript from dist/

## Testing Commands
- `npm test` - Run all unit tests with Mocha
- `npm run test-coverage` - Generate test coverage report (HTML + text)
- `npm run test-codecov` - Generate coverage for Codecov
- `npm run test-integration` - Run integration tests against local server

## Background Processing Commands
- `npm run background-jobs` - Run background job processor
- `npm run materiality-assessment` - Generate materiality assessment data

## Development Workflow

1. Local development uses `npm start` which runs TypeScript compilation + nodemon
2. Database migrations are in `migrations/` directory (MongoDB shell scripts)
3. Background jobs can be run locally or queued to cloud services
4. Integration tests require local MongoDB and Redis instances
5. Docker Compose setup available for debugging with attached debugger

## Environment Setup

Required environment variables are documented in README.md. Key categories:
- Database connections (MongoDB, Redis, PostgreSQL for migrations)
- Authentication (Okta, JWT secrets)
- External services (Stripe, AWS, email providers)
- Feature flags and regional settings