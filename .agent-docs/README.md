# Modular AI Agent Documentation - G17Eco API

This directory contains modular documentation designed for reuse across different AI tools and assistants.

## Structure

```
.agent-docs/
├── core/                    # Core system information
│   ├── architecture.md      # System architecture and layers
│   └── commands.md         # Development and testing commands
├── testing/                # Testing documentation
│   └── framework.md        # Testing stack and organization
├── patterns/               # Code and testing patterns
│   ├── service-testing.md  # Service testing templates
│   ├── error-handling.md   # Error handling patterns
│   └── test-data.md        # Test data management
├── tools/                  # AI tool-specific instructions
│   ├── CLAUDE.md          # Claude Code instructions
│   ├── CURSOR.md          # Cursor IDE instructions
│   ├── OPENAI.md          # OpenAI/ChatGPT instructions
│   └── GEMINI.md          # Google Gemini instructions
└── README.md              # This file
```

## Usage by AI Tool

### Claude Code
Reference: `.agent-docs/tools/CLAUDE.md`
- Primary instruction file for Claude Code sessions
- Includes all necessary architectural and testing context

### Cursor IDE
Reference: `.agent-docs/tools/CURSOR.md`
Configuration: `.cursorrules`
- Cursor-specific guidelines for code generation and refactoring
- Integrates with Cursor's AI features

### OpenAI/ChatGPT
Reference: `.agent-docs/tools/OPENAI.md`
- Comprehensive context for ChatGPT interactions
- Includes links to all relevant documentation modules

### Google Gemini
Reference: `.agent-docs/tools/GEMINI.md`
- Structured context for Gemini AI interactions
- Technology stack and development workflow information

## Benefits of This Structure

1. **Reduced Duplication**: Core information is written once and referenced
2. **Tool-Specific Optimization**: Each AI tool gets tailored instructions
3. **Easy Maintenance**: Updates to core info automatically apply everywhere
4. **Consistent Context**: All AI tools work with the same foundational knowledge
5. **Modular Loading**: Tools can load only the context they need

## Updating Documentation

When updating system information:

1. **Core Changes**: Update files in `core/` directory
2. **Testing Changes**: Update files in `testing/` and `patterns/` directories
3. **Tool-Specific**: Update individual tool files in `tools/` directory
4. **New Patterns**: Add new pattern files and reference them in tool files

## Integration with Main Documentation

The main `CLAUDE.md` file now references this modular structure, reducing its size while maintaining full functionality.

## Configuration Files

- `.cursorrules` - Cursor IDE configuration
- `.aidigestrc` - AI digest configuration for documentation priority