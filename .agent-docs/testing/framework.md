# Testing Framework - G17Eco API

## Testing Stack

- **Test Framework**: Mocha
- **Assertion Library**: <PERSON><PERSON> (with chai-as-promised for async assertions)
- **Mocking**: Sinon.js
- **Coverage**: NYC (Istanbul) with TypeScript support
- **Test Runner**: `npm test` (5-second timeout)
- **Environment**: NODE_ENV=test, UTC timezone

## File Organization & Naming

### Directory Structure
```
tests/
├── service/                    # Service layer tests
│   ├── [domain]/              # Domain-specific services
│   │   └── ServiceName.spec.ts
│   └── [category]/            # Cross-cutting concerns
│       └── ServiceName.spec.ts
├── fixtures/                  # Test data and factories
├── mocks/                     # Mock implementations
├── factories/                 # Test object factories
└── setup/                     # Test configuration
```

### Naming Conventions
- **Test Files**: `ServiceName.spec.ts` (matches service file name)
- **Test Suites**: Use service class name as main describe block
- **Test Cases**: Use descriptive "should..." statements
- **Fixtures**: `[domain]Fixtures.ts` or `[entity]Fixtures.ts`

## Coverage Requirements

### Coverage Targets
- **Services**: 90%+ line coverage required
- **Critical Business Logic**: 100% branch coverage
- **Error Paths**: All error conditions must be tested

### Coverage Exclusions
```javascript
// nyc.config.js exclusions
"!server/routes",     // Routes tested separately
"!server/db",         // Database config
"!server/analytics",  // External analytics
"!server/scripts"     // Utility scripts
```

### Testing Priority
1. **Critical Path Functions**: User management, payments, data processing
2. **Business Logic**: Calculations, validations, transformations  
3. **Integration Points**: External API calls, database operations
4. **Edge Cases**: Error conditions, boundary values