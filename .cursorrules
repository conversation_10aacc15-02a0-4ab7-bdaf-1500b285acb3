# Cursor Rules for G17Eco API

## Documentation Reference
Always reference the modular documentation in `.agent-docs/tools/CURSOR.md` for comprehensive guidelines.

## Code Standards
- Use TypeScript strict mode
- Follow existing architectural patterns
- Maintain 90%+ test coverage for services
- Use established service/repository/model layers

## Testing
- Use Mocha + Chai + Sinon stack
- Follow patterns in `.agent-docs/patterns/service-testing.md`
- Create fixtures in `tests/fixtures/`
- Use sandbox pattern for mocking

## File Organization
- Services: `server/service/[domain]/`
- Tests: `tests/service/[domain]/`
- Models: `server/models/`
- Routes: `server/routes/`

## Development Commands
- `npm start` - Development server
- `npm test` - Run tests
- `npm run test-coverage` - Coverage report

## AI Assistant Guidelines
- Reference modular docs before code generation
- Follow existing patterns and conventions
- Maintain API compatibility during refactoring
- Use domain-driven design principles